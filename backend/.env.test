# 测试环境配置
NODE_ENV="test"
DATABASE_URL="mysql://root:123456@localhost:3306/ops_management_test"
REDIS_URL="redis://localhost:6379/1"

# JWT配置
JWT_SECRET="test-jwt-secret"
JWT_EXPIRES_IN="1h"
REFRESH_TOKEN_SECRET="test-refresh-token-secret"
REFRESH_TOKEN_EXPIRES_IN="7d"

# 应用配置
PORT=3002
FRONTEND_URL="http://localhost:3000"

# 文件上传配置
UPLOAD_DIR="test-uploads"
MAX_FILE_SIZE="10485760"

# 加密配置
CRYPTO_SECRET="test-crypto-secret-key-32-chars-l"

# 日志配置
LOG_LEVEL="error"
LOG_DIR="test-logs"

# 限流配置
RATE_LIMIT_WINDOW_MS="900000"
RATE_LIMIT_MAX="1000"

# 禁用邮件和短信服务在测试环境
SMTP_HOST=""
ALI_SMS_ACCESS_KEY_ID=""