const fs = require('fs');
const path = require('path');

const ROUTES_DIR = path.join(__dirname, '..', 'src', 'routes');

function stripSwaggerComments(content) {
  // Remove all /** ... */ blocks that contain @swagger
  return content.replace(/\/\*\*[\s\S]*?@swagger[\s\S]*?\*\//g, '').replace(/\n{3,}/g, '\n\n');
}

function processFile(filePath) {
  const original = fs.readFileSync(filePath, 'utf8');
  const stripped = stripSwaggerComments(original);
  if (stripped !== original) {
    fs.writeFileSync(filePath, stripped, 'utf8');
    return true;
  }
  return false;
}

function walk(dir) {
  const entries = fs.readdirSync(dir, { withFileTypes: true });
  let changed = 0;
  for (const entry of entries) {
    const full = path.join(dir, entry.name);
    if (entry.isDirectory()) {
      changed += walk(full);
    } else if (/\.ts$/.test(entry.name)) {
      if (processFile(full)) changed += 1;
    }
  }
  return changed;
}

(function main() {
  if (!fs.existsSync(ROUTES_DIR)) {
    console.error('Routes directory not found:', ROUTES_DIR);
    process.exit(1);
  }
  const changed = walk(ROUTES_DIR);
  console.log(`Stripped @swagger comments in ${changed} files.`);
})();
