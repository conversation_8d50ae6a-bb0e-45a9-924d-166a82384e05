import request from 'supertest';
import { Express } from 'express';
import { PrismaClient } from '@prisma/client';
import { createTestApp } from '../helpers/app';
import { createTestDataSet, cleanupDatabase, getTestToken } from '../helpers/database';

describe('User Management API Tests', () => {
  let app: Express;
  let prisma: PrismaClient;
  let testData: any;
  let userToken: string;
  let adminToken: string;

  beforeAll(async () => {
    app = await createTestApp();
    prisma = new PrismaClient();
    await prisma.$connect();
  });

  afterAll(async () => {
    await cleanupDatabase(prisma);
    await prisma.$disconnect();
  });

  beforeEach(async () => {
    await cleanupDatabase(prisma);
    testData = await createTestDataSet(prisma);
    userToken = await getTestToken(prisma, testData.user.id);
    adminToken = await getTestToken(prisma, testData.admin.id);
  });

  describe('GET /api/v1/users', () => {
    it('管理员应该能够获取用户列表', async () => {
      const response = await request(app)
        .get('/api/v1/users')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body.data).toHaveProperty('users');
      expect(response.body.data).toHaveProperty('pagination');
      expect(Array.isArray(response.body.data.users)).toBe(true);
      expect(response.body.data.users.length).toBeGreaterThan(0);
      
      // 检查用户数据不包含密码
      response.body.data.users.forEach((user: any) => {
        expect(user).not.toHaveProperty('password');
        expect(user).toHaveProperty('id');
        expect(user).toHaveProperty('username');
        expect(user).toHaveProperty('email');
      });
    });

    it('普通用户不应该能够获取用户列表', async () => {
      const response = await request(app)
        .get('/api/v1/users')
        .set('Authorization', `Bearer ${userToken}`)
        .expect(403);

      expect(response.body).toHaveProperty('success', false);
      expect(response.body.message).toContain('权限不足');
    });

    it('应该支持分页查询', async () => {
      const response = await request(app)
        .get('/api/v1/users?page=1&limit=10')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.data.pagination).toHaveProperty('page', 1);
      expect(response.body.data.pagination).toHaveProperty('limit', 10);
      expect(response.body.data.pagination).toHaveProperty('total');
      expect(response.body.data.pagination).toHaveProperty('totalPages');
    });

    it('应该支持搜索用户', async () => {
      const response = await request(app)
        .get('/api/v1/users?search=test')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.data.users).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            username: expect.stringContaining('test')
          })
        ])
      );
    });

    it('应该支持按状态筛选用户', async () => {
      const response = await request(app)
        .get('/api/v1/users?status=ACTIVE')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      response.body.data.users.forEach((user: any) => {
        expect(user.status).toBe('ACTIVE');
      });
    });
  });

  describe('GET /api/v1/users/:id', () => {
    it('管理员应该能够获取用户详情', async () => {
      const response = await request(app)
        .get(`/api/v1/users/${testData.user.id}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body.data).toHaveProperty('id', testData.user.id);
      expect(response.body.data).toHaveProperty('username');
      expect(response.body.data).toHaveProperty('email');
      expect(response.body.data).not.toHaveProperty('password');
      expect(response.body.data).toHaveProperty('roles');
    });

    it('用户应该能够获取自己的详情', async () => {
      const response = await request(app)
        .get(`/api/v1/users/${testData.user.id}`)
        .set('Authorization', `Bearer ${userToken}`)
        .expect(200);

      expect(response.body.data).toHaveProperty('id', testData.user.id);
    });

    it('用户不应该能够获取其他用户的详情', async () => {
      const response = await request(app)
        .get(`/api/v1/users/${testData.admin.id}`)
        .set('Authorization', `Bearer ${userToken}`)
        .expect(403);

      expect(response.body).toHaveProperty('success', false);
    });

    it('应该处理不存在的用户ID', async () => {
      const response = await request(app)
        .get('/api/v1/users/nonexistent-id')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(404);

      expect(response.body).toHaveProperty('success', false);
      expect(response.body.message).toContain('用户不存在');
    });
  });

  describe('POST /api/v1/users', () => {
    const newUserData = {
      username: 'newuser',
      email: '<EMAIL>',
      name: '新用户',
      password: 'newpassword123',
      department: '技术部',
      position: '工程师'
    };

    it('管理员应该能够创建新用户', async () => {
      const response = await request(app)
        .post('/api/v1/users')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(newUserData)
        .expect(201);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body.data).toHaveProperty('id');
      expect(response.body.data).toHaveProperty('username', newUserData.username);
      expect(response.body.data).toHaveProperty('email', newUserData.email);
      expect(response.body.data).not.toHaveProperty('password');

      // 验证密码已加密存储
      const createdUser = await prisma.user.findUnique({
        where: { id: response.body.data.id }
      });
      expect(createdUser?.password).not.toBe(newUserData.password);
      expect(createdUser?.password).toMatch(/^\$2[ayb]\$/); // bcrypt hash pattern
    });

    it('普通用户不应该能够创建用户', async () => {
      const response = await request(app)
        .post('/api/v1/users')
        .set('Authorization', `Bearer ${userToken}`)
        .send(newUserData)
        .expect(403);

      expect(response.body).toHaveProperty('success', false);
    });

    it('应该验证用户名唯一性', async () => {
      // 先创建一个用户
      await request(app)
        .post('/api/v1/users')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(newUserData);

      // 尝试创建相同用户名的用户
      const response = await request(app)
        .post('/api/v1/users')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(newUserData)
        .expect(400);

      expect(response.body).toHaveProperty('success', false);
      expect(response.body.message).toContain('用户名已存在');
    });

    it('应该验证邮箱唯一性', async () => {
      const duplicateEmailData = {
        ...newUserData,
        username: 'different_username',
        email: testData.user.email
      };

      const response = await request(app)
        .post('/api/v1/users')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(duplicateEmailData)
        .expect(400);

      expect(response.body).toHaveProperty('success', false);
      expect(response.body.message).toContain('邮箱已存在');
    });

    it('应该验证必填字段', async () => {
      const incompleteData = {
        username: 'incomplete'
        // 缺少 email, name, password
      };

      const response = await request(app)
        .post('/api/v1/users')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(incompleteData)
        .expect(400);

      expect(response.body).toHaveProperty('success', false);
      expect(response.body.message).toContain('验证失败');
    });

    it('应该验证密码强度', async () => {
      const weakPasswordData = {
        ...newUserData,
        password: '123' // 太弱的密码
      };

      const response = await request(app)
        .post('/api/v1/users')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(weakPasswordData)
        .expect(400);

      expect(response.body).toHaveProperty('success', false);
      expect(response.body.message).toContain('密码');
    });
  });

  describe('PUT /api/v1/users/:id', () => {
    const updateData = {
      name: '更新的名称',
      department: '更新的部门',
      position: '更新的职位'
    };

    it('管理员应该能够更新用户信息', async () => {
      const response = await request(app)
        .put(`/api/v1/users/${testData.user.id}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send(updateData)
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body.data).toHaveProperty('name', updateData.name);
      expect(response.body.data).toHaveProperty('department', updateData.department);
    });

    it('用户应该能够更新自己的信息', async () => {
      const response = await request(app)
        .put(`/api/v1/users/${testData.user.id}`)
        .set('Authorization', `Bearer ${userToken}`)
        .send(updateData)
        .expect(200);

      expect(response.body.data).toHaveProperty('name', updateData.name);
    });

    it('用户不应该能够更新其他用户的信息', async () => {
      const response = await request(app)
        .put(`/api/v1/users/${testData.admin.id}`)
        .set('Authorization', `Bearer ${userToken}`)
        .send(updateData)
        .expect(403);

      expect(response.body).toHaveProperty('success', false);
    });

    it('不应该允许更新用户名', async () => {
      const response = await request(app)
        .put(`/api/v1/users/${testData.user.id}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send({ username: 'newusername' })
        .expect(400);

      expect(response.body).toHaveProperty('success', false);
      expect(response.body.message).toContain('不允许修改用户名');
    });
  });

  describe('DELETE /api/v1/users/:id', () => {
    it('管理员应该能够删除用户', async () => {
      const response = await request(app)
        .delete(`/api/v1/users/${testData.user.id}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('success', true);

      // 验证用户已被删除
      const deletedUser = await prisma.user.findUnique({
        where: { id: testData.user.id }
      });
      expect(deletedUser).toBeNull();
    });

    it('普通用户不应该能够删除用户', async () => {
      const response = await request(app)
        .delete(`/api/v1/users/${testData.admin.id}`)
        .set('Authorization', `Bearer ${userToken}`)
        .expect(403);

      expect(response.body).toHaveProperty('success', false);
    });

    it('不应该允许删除自己的账户', async () => {
      const response = await request(app)
        .delete(`/api/v1/users/${testData.admin.id}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(400);

      expect(response.body).toHaveProperty('success', false);
      expect(response.body.message).toContain('不能删除自己的账户');
    });
  });

  describe('POST /api/v1/users/:id/status', () => {
    it('管理员应该能够切换用户状态', async () => {
      const response = await request(app)
        .post(`/api/v1/users/${testData.user.id}/status`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send({ status: 'INACTIVE' })
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body.data).toHaveProperty('status', 'INACTIVE');
    });

    it('不应该允许禁用自己的账户', async () => {
      const response = await request(app)
        .post(`/api/v1/users/${testData.admin.id}/status`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send({ status: 'INACTIVE' })
        .expect(400);

      expect(response.body).toHaveProperty('success', false);
      expect(response.body.message).toContain('不能禁用自己的账户');
    });
  });

  describe('POST /api/v1/users/:id/reset-password', () => {
    it('管理员应该能够重置用户密码', async () => {
      const response = await request(app)
        .post(`/api/v1/users/${testData.user.id}/reset-password`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send({ newPassword: 'newpassword123' })
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body.message).toContain('密码重置成功');
    });

    it('用户应该能够修改自己的密码', async () => {
      const response = await request(app)
        .post(`/api/v1/users/${testData.user.id}/reset-password`)
        .set('Authorization', `Bearer ${userToken}`)
        .send({
          currentPassword: testData.user.plainPassword,
          newPassword: 'newpassword123'
        })
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
    });

    it('用户修改密码时应该验证当前密码', async () => {
      const response = await request(app)
        .post(`/api/v1/users/${testData.user.id}/reset-password`)
        .set('Authorization', `Bearer ${userToken}`)
        .send({
          currentPassword: 'wrongpassword',
          newPassword: 'newpassword123'
        })
        .expect(400);

      expect(response.body).toHaveProperty('success', false);
      expect(response.body.message).toContain('当前密码不正确');
    });
  });

  describe('权限测试', () => {
    it('未认证的请求应该被拒绝', async () => {
      const response = await request(app)
        .get('/api/v1/users')
        .expect(401);

      expect(response.body).toHaveProperty('success', false);
      expect(response.body.message).toContain('访问令牌不存在');
    });

    it('过期的令牌应该被拒绝', async () => {
      const jwt = require('jsonwebtoken');
      const expiredToken = jwt.sign(
        { userId: testData.user.id },
        process.env.JWT_SECRET || 'test-secret',
        { expiresIn: '-1h' } // 已过期
      );

      const response = await request(app)
        .get('/api/v1/users')
        .set('Authorization', `Bearer ${expiredToken}`)
        .expect(401);

      expect(response.body).toHaveProperty('success', false);
      expect(response.body.message).toContain('令牌已过期');
    });
  });
});