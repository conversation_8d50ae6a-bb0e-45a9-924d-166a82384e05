import request from 'supertest';
import { Express } from 'express';
import { PrismaClient } from '@prisma/client';
import { createTestApp } from '../helpers/app';
import { createTestUser, cleanupDatabase } from '../helpers/database';

describe('Authentication API Tests', () => {
  let app: Express;
  let prisma: PrismaClient;
  let testUser: any;

  beforeAll(async () => {
    app = await createTestApp();
    prisma = new PrismaClient();
    await prisma.$connect();
  });

  afterAll(async () => {
    await cleanupDatabase(prisma);
    await prisma.$disconnect();
  });

  beforeEach(async () => {
    await cleanupDatabase(prisma);
    testUser = await createTestUser(prisma);
  });

  describe('POST /api/v1/auth/login', () => {
    it('应该能够使用有效凭据登录', async () => {
      const response = await request(app)
        .post('/api/v1/auth/login')
        .send({
          username: testUser.username,
          password: 'testpassword'
        })
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data).toHaveProperty('user');
      expect(response.body.data).toHaveProperty('accessToken');
      expect(response.body.data.user).toHaveProperty('id');
      expect(response.body.data.user).toHaveProperty('username');
      expect(response.body.data.user).not.toHaveProperty('password');
      
      // 检查是否设置了Cookie
      expect(response.headers['set-cookie']).toBeDefined();
    });

    it('应该拒绝无效用户名', async () => {
      const response = await request(app)
        .post('/api/v1/auth/login')
        .send({
          username: 'invalid_user',
          password: 'testpassword'
        })
        .expect(401);

      expect(response.body).toHaveProperty('success', false);
      expect(response.body).toHaveProperty('message');
    });

    it('应该拒绝错误密码', async () => {
      const response = await request(app)
        .post('/api/v1/auth/login')
        .send({
          username: testUser.username,
          password: 'wrong_password'
        })
        .expect(401);

      expect(response.body).toHaveProperty('success', false);
      expect(response.body).toHaveProperty('message');
    });

    it('应该拒绝禁用的用户', async () => {
      // 先禁用用户
      await prisma.user.update({
        where: { id: testUser.id },
        data: { status: 'INACTIVE' }
      });

      const response = await request(app)
        .post('/api/v1/auth/login')
        .send({
          username: testUser.username,
          password: 'testpassword'
        })
        .expect(401);

      expect(response.body).toHaveProperty('success', false);
      expect(response.body.message).toContain('用户已被禁用');
    });

    it('应该验证请求数据格式', async () => {
      const response = await request(app)
        .post('/api/v1/auth/login')
        .send({
          username: '',
          password: ''
        })
        .expect(400);

      expect(response.body).toHaveProperty('success', false);
      expect(response.body).toHaveProperty('message');
    });
  });

  describe('POST /api/v1/auth/refresh', () => {
    let accessToken: string;
    let refreshToken: string;

    beforeEach(async () => {
      // 先登录获取token
      const loginResponse = await request(app)
        .post('/api/v1/auth/login')
        .send({
          username: testUser.username,
          password: 'testpassword'
        });
      
      accessToken = loginResponse.body.data.accessToken;
      refreshToken = loginResponse.body.data.refreshToken;
    });

    it('应该能够使用有效的刷新令牌获取新的访问令牌', async () => {
      const response = await request(app)
        .post('/api/v1/auth/refresh')
        .send({
          refreshToken
        })
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body.data).toHaveProperty('accessToken');
      expect(response.body.data.accessToken).not.toBe(accessToken);
    });

    it('应该拒绝无效的刷新令牌', async () => {
      const response = await request(app)
        .post('/api/v1/auth/refresh')
        .send({
          refreshToken: 'invalid_token'
        })
        .expect(401);

      expect(response.body).toHaveProperty('success', false);
      expect(response.body).toHaveProperty('message');
    });
  });

  describe('POST /api/v1/auth/logout', () => {
    let accessToken: string;

    beforeEach(async () => {
      const loginResponse = await request(app)
        .post('/api/v1/auth/login')
        .send({
          username: testUser.username,
          password: 'testpassword'
        });
      
      accessToken = loginResponse.body.data.accessToken;
    });

    it('应该能够成功登出', async () => {
      const response = await request(app)
        .post('/api/v1/auth/logout')
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('message');
    });

    it('应该清除Cookie', async () => {
      const response = await request(app)
        .post('/api/v1/auth/logout')
        .set('Authorization', `Bearer ${accessToken}`);

      expect(response.headers['set-cookie']).toBeDefined();
      // 检查Cookie是否被清除（设置为空值或过期）
      const setCookieHeader = response.headers['set-cookie'][0];
      expect(setCookieHeader).toMatch(/=;.*expires/);
    });
  });

  describe('GET /api/v1/auth/me', () => {
    let accessToken: string;

    beforeEach(async () => {
      const loginResponse = await request(app)
        .post('/api/v1/auth/login')
        .send({
          username: testUser.username,
          password: 'testpassword'
        });
      
      accessToken = loginResponse.body.data.accessToken;
    });

    it('应该返回当前用户信息', async () => {
      const response = await request(app)
        .get('/api/v1/auth/me')
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body.data).toHaveProperty('id');
      expect(response.body.data).toHaveProperty('username');
      expect(response.body.data).toHaveProperty('email');
      expect(response.body.data).not.toHaveProperty('password');
    });

    it('应该拒绝无效的访问令牌', async () => {
      const response = await request(app)
        .get('/api/v1/auth/me')
        .set('Authorization', 'Bearer invalid_token')
        .expect(401);

      expect(response.body).toHaveProperty('success', false);
      expect(response.body).toHaveProperty('message');
    });

    it('应该拒绝缺少访问令牌的请求', async () => {
      const response = await request(app)
        .get('/api/v1/auth/me')
        .expect(401);

      expect(response.body).toHaveProperty('success', false);
      expect(response.body.message).toContain('访问令牌不存在');
    });
  });

  describe('安全性测试', () => {
    it('应该防止SQL注入攻击', async () => {
      const response = await request(app)
        .post('/api/v1/auth/login')
        .send({
          username: "admin' OR '1'='1",
          password: "password' OR '1'='1"
        })
        .expect(401);

      expect(response.body).toHaveProperty('success', false);
    });

    it('应该限制登录尝试次数', async () => {
      // 模拟多次失败登录
      const promises = Array(6).fill(null).map(() => 
        request(app)
          .post('/api/v1/auth/login')
          .send({
            username: testUser.username,
            password: 'wrong_password'
          })
      );

      await Promise.all(promises);

      // 第7次尝试应该被限制
      const response = await request(app)
        .post('/api/v1/auth/login')
        .send({
          username: testUser.username,
          password: 'wrong_password'
        })
        .expect(429);

      expect(response.body).toHaveProperty('success', false);
      expect(response.body.message).toContain('请求频率');
    });

    it('JWT令牌应该包含正确的声明', async () => {
      const response = await request(app)
        .post('/api/v1/auth/login')
        .send({
          username: testUser.username,
          password: 'testpassword'
        });

      const token = response.body.data.accessToken;
      
      // 解码JWT令牌（不验证签名，仅检查载荷）
      const payload = JSON.parse(Buffer.from(token.split('.')[1], 'base64').toString());
      
      expect(payload).toHaveProperty('userId');
      expect(payload).toHaveProperty('username');
      expect(payload).toHaveProperty('iat');
      expect(payload).toHaveProperty('exp');
      expect(payload.exp).toBeGreaterThan(payload.iat);
    });
  });
});