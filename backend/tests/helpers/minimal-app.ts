import express, { Express } from 'express';
import cors from 'cors';
import cookieParser from 'cookie-parser';
import helmet from 'helmet';
import rateLimit from 'express-rate-limit';

// 创建最小化测试应用实例（避免复杂的权限系统依赖）
export const createMinimalTestApp = async (): Promise<Express> => {
  const app = express();

  // 基本中间件
  app.use(helmet({
    contentSecurityPolicy: false,
    crossOriginEmbedderPolicy: false
  }));
  
  app.use(cors({
    origin: process.env['FRONTEND_URL'] || 'http://localhost:3000',
    credentials: true,
    optionsSuccessStatus: 200
  }));

  app.use(express.json({ limit: '10mb' }));
  app.use(express.urlencoded({ extended: true, limit: '10mb' }));
  app.use(cookieParser());

  // 速率限制 (测试环境下更宽松)
  const limiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 1000,
    message: {
      success: false,
      message: '请求过于频繁，请稍后再试',
      code: 'RATE_LIMIT_EXCEEDED'
    },
    standardHeaders: true,
    legacyHeaders: false,
  });
  app.use('/api/', limiter);

  // 健康检查端点
  app.get('/api/health', (_req, res) => {
    res.json({ 
      success: true, 
      message: 'Service is healthy',
      timestamp: new Date().toISOString(),
      environment: process.env['NODE_ENV'] || 'development'
    });
  });

  // 模拟认证路由
  app.post('/api/v1/auth/login', async (req, res) => {
    const { username, password } = req.body;

    if (!username || !password) {
      return res.status(400).json({
        success: false,
        message: '用户名和密码不能为空'
      });
    }

    try {
      const prisma = globalThis.prisma;
      const user = await prisma.user.findUnique({
        where: { username }
      });

      if (!user) {
        return res.status(401).json({
          success: false,
          message: '用户名或密码错误'
        });
      }

      if (user.status !== 'ACTIVE') {
        return res.status(401).json({
          success: false,
          message: '账户已被禁用'
        });
      }

      // 简化的密码验证（实际应该使用bcrypt）
      const bcrypt = require('bcrypt');
      const isValidPassword = await bcrypt.compare(password, user.password);

      if (!isValidPassword) {
        return res.status(401).json({
          success: false,
          message: '用户名或密码错误'
        });
      }

      // 生成JWT令牌
      const jwt = require('jsonwebtoken');
      const accessToken = jwt.sign(
        { 
          userId: user.id, 
          username: user.username,
          email: user.email 
        },
        process.env['JWT_SECRET'] || 'test-jwt-secret',
        { expiresIn: '1h' }
      );

      const refreshToken = jwt.sign(
        { userId: user.id },
        process.env['REFRESH_TOKEN_SECRET'] || 'test-refresh-token-secret',
        { expiresIn: '7d' }
      );

      return res.json({
        success: true,
        data: {
          access_token: accessToken,
          refresh_token: refreshToken,
          user: {
            id: user.id,
            username: user.username,
            email: user.email
            // 不返回密码
          }
        }
      });

    } catch (error) {
      console.error('Login error:', error);
      return res.status(500).json({
        success: false,
        message: '登录失败'
      });
    }
  });

  // 模拟注册路由
  app.post('/api/v1/auth/register', async (req, res) => {
    const { username, email, password } = req.body;

    // 基本验证
    if (!username || !email || !password) {
      return res.status(400).json({
        success: false,
        message: '用户名、邮箱和密码不能为空'
      });
    }

    // 邮箱格式验证
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return res.status(400).json({
        success: false,
        message: '邮箱格式不正确'
      });
    }

    // 密码强度验证
    if (password.length < 8) {
      return res.status(400).json({
        success: false,
        message: '密码长度至少8位'
      });
    }

    // 用户名长度验证
    if (username.length > 100) {
      return res.status(400).json({
        success: false,
        message: '用户名过长'
      });
    }

    // XSS检查
    const xssPattern = /<script|javascript:|on\w+=/i;
    if (xssPattern.test(username) || xssPattern.test(email)) {
      return res.status(400).json({
        success: false,
        message: '输入包含不安全字符'
      });
    }

    try {
      const prisma = globalThis.prisma;
      
      // 检查用户是否已存在
      const existingUser = await prisma.user.findFirst({
        where: {
          OR: [
            { username },
            { email }
          ]
        }
      });

      if (existingUser) {
        return res.status(409).json({
          success: false,
          message: '用户名或邮箱已存在'
        });
      }

      // 哈希密码
      const bcrypt = require('bcrypt');
      const hashedPassword = await bcrypt.hash(password, 12);

      // 创建用户
      const user = await prisma.user.create({
        data: {
          username,
          email,
          password: hashedPassword,
          status: 'ACTIVE'
        }
      });

      return res.status(201).json({
        success: true,
        message: '用户注册成功',
        data: {
          user: {
            id: user.id,
            username: user.username,
            email: user.email
          }
        }
      });

    } catch (error) {
      console.error('Registration error:', error);
      return res.status(500).json({
        success: false,
        message: '注册失败'
      });
    }
  });

  // 模拟用户资料路由（需要认证）
  app.get('/api/v1/users/profile', async (req, res) => {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1];

    if (!token) {
      return res.status(401).json({
        success: false,
        message: '访问令牌不存在'
      });
    }

    try {
      const jwt = require('jsonwebtoken');
      const decoded = jwt.verify(token, process.env['JWT_SECRET'] || 'test-jwt-secret');
      
      const prisma = globalThis.prisma;
      const user = await prisma.user.findUnique({
        where: { id: decoded.userId }
      });

      if (!user) {
        return res.status(401).json({
          success: false,
          message: '用户不存在'
        });
      }

      return res.json({
        success: true,
        data: {
          user: {
            id: user.id,
            username: user.username,
            email: user.email
          }
        }
      });

    } catch (error) {
      return res.status(401).json({
        success: false,
        message: '访问令牌无效'
      });
    }
  });

  // 模拟登出路由
  app.post('/api/v1/auth/logout', (_req, res) => {
    res.json({
      success: true,
      message: '登出成功'
    });
  });

  // 模拟令牌刷新路由
  app.post('/api/v1/auth/refresh', async (req, res) => {
    const { refresh_token } = req.body;

    if (!refresh_token) {
      return res.status(401).json({
        success: false,
        message: '刷新令牌不存在'
      });
    }

    try {
      const jwt = require('jsonwebtoken');
      const decoded = jwt.verify(refresh_token, process.env['REFRESH_TOKEN_SECRET'] || 'test-refresh-token-secret');
      
      const prisma = globalThis.prisma;
      const user = await prisma.user.findUnique({
        where: { id: decoded.userId }
      });

      if (!user) {
        return res.status(401).json({
          success: false,
          message: '用户不存在'
        });
      }

      // 生成新的访问令牌
      const accessToken = jwt.sign(
        { 
          userId: user.id, 
          username: user.username,
          email: user.email 
        },
        process.env['JWT_SECRET'] || 'test-jwt-secret',
        { expiresIn: '1h' }
      );

      return res.json({
        success: true,
        data: {
          access_token: accessToken
        }
      });

    } catch (error) {
      return res.status(401).json({
        success: false,
        message: '刷新令牌无效'
      });
    }
  });

  // 404 处理
  app.use('*', (_req, res) => {
    res.status(404).json({
      success: false,
      message: 'Route not found'
    });
  });

  return app;
};