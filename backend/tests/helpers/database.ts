import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcryptjs';

// 创建测试用户
export const createTestUser = async (prisma: PrismaClient) => {
  const hashedPassword = await bcrypt.hash('testpassword', 10);
  
  const user = await prisma.user.create({
    data: {
      username: 'testuser',
      email: '<EMAIL>',
      name: '测试用户',
      password: hashedPassword,
      status: 'ACTIVE',
      createdBy: '系统'
    }
  });

  return {
    ...user,
    plainPassword: 'testpassword' // 保存明文密码供测试使用
  };
};

// 创建管理员用户
export const createAdminUser = async (prisma: PrismaClient) => {
  const hashedPassword = await bcrypt.hash('adminpassword', 10);
  
  const user = await prisma.user.create({
    data: {
      username: 'admin',
      email: '<EMAIL>',
      name: '管理员',
      password: hashedPassword,
      status: 'ACTIVE',
      createdBy: '系统'
    }
  });

  // 创建管理员角色
  const adminRole = await prisma.role.create({
    data: {
      name: 'admin',
      displayName: '系统管理员',
      description: '系统管理员角色',
      permissions: ['admin:all'],
      isSystem: true,
      createdBy: '系统'
    }
  });

  // 分配角色给用户
  await prisma.userRole.create({
    data: {
      userId: user.id,
      roleId: adminRole.id,
      createdBy: '系统'
    }
  });

  return {
    ...user,
    plainPassword: 'adminpassword',
    role: adminRole
  };
};

// 创建测试客户
export const createTestCustomer = async (prisma: PrismaClient) => {
  return await prisma.customer.create({
    data: {
      name: '测试客户公司',
      type: 'ENTERPRISE',
      industry: 'IT',
      level: 'GOLD',
      status: 'ACTIVE',
      description: '测试用客户',
      createdBy: '系统'
    }
  });
};

// 创建测试项目档案
export const createTestArchive = async (prisma: PrismaClient, customerId: string) => {
  return await prisma.projectArchive.create({
    data: {
      name: '测试项目',
      description: '测试项目描述',
      customerId,
      techStack: ['React', 'Node.js'],
      status: 'ACTIVE',
      version: '1.0.0',
      createdBy: '系统'
    }
  });
};

// 创建测试服务工单
export const createTestService = async (prisma: PrismaClient, customerId: string, archiveId: string) => {
  return await prisma.service.create({
    data: {
      title: '测试服务工单',
      description: '测试服务工单描述',
      customerId,
      archiveId,
      category: 'MAINTENANCE',
      priority: 'MEDIUM',
      status: 'PENDING',
      estimatedHours: 8,
      contactName: '测试联系人',
      contactEmail: '<EMAIL>',
      contactPhone: '13800138000',
      createdBy: '系统'
    }
  });
};

// 清理数据库
export const cleanupDatabase = async (prisma: PrismaClient) => {
  // 按外键依赖顺序删除
  await prisma.operationLog.deleteMany();
  await prisma.serviceComment.deleteMany();
  await prisma.serviceAttachment.deleteMany();
  await prisma.serviceWorkLog.deleteMany();
  await prisma.service.deleteMany();
  await prisma.projectConfiguration.deleteMany();
  await prisma.projectArchive.deleteMany();
  await prisma.customerContact.deleteMany();
  await prisma.customer.deleteMany();
  await prisma.userRole.deleteMany();
  await prisma.role.deleteMany();
  await prisma.user.deleteMany();
  await prisma.notificationTemplate.deleteMany();
  await prisma.notification.deleteMany();
  await prisma.systemConfig.deleteMany();
  await prisma.permissionTemplate.deleteMany();
  await prisma.alertRule.deleteMany();
};

// 创建完整的测试数据集
export const createTestDataSet = async (prisma: PrismaClient) => {
  const user = await createTestUser(prisma);
  const admin = await createAdminUser(prisma);
  const customer = await createTestCustomer(prisma);
  const archive = await createTestArchive(prisma, customer.id);
  const service = await createTestService(prisma, customer.id, archive.id);

  return {
    user,
    admin,
    customer,
    archive,
    service
  };
};

// 获取测试JWT令牌
export const getTestToken = async (prisma: PrismaClient, userId: string) => {
  const jwt = require('jsonwebtoken');
  const user = await prisma.user.findUnique({
    where: { id: userId },
    include: {
      roles: {
        include: {
          role: true
        }
      }
    }
  });

  if (!user) {
    throw new Error('User not found');
  }

  const payload = {
    userId: user.id,
    username: user.username,
    email: user.email,
    roles: user.roles.map(ur => ur.role.name)
  };

  return jwt.sign(payload, process.env.JWT_SECRET || 'test-secret', { expiresIn: '1h' });
};