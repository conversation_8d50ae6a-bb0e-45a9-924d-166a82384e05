import express, { Express } from 'express';
import cors from 'cors';
import cookieParser from 'cookie-parser';
import helmet from 'helmet';
import morgan from 'morgan';
import rateLimit from 'express-rate-limit';
import routes from '../../src/routes';
import { errorHandler } from '../../src/middleware/error.middleware';

// 创建测试应用实例
export const createTestApp = async (): Promise<Express> => {
  const app = express();

  // 基本中间件
  app.use(helmet({
    contentSecurityPolicy: false,
    crossOriginEmbedderPolicy: false
  }));
  
  app.use(cors({
    origin: process.env['FRONTEND_URL'] || 'http://localhost:3000',
    credentials: true,
    optionsSuccessStatus: 200
  }));

  app.use(express.json({ limit: '10mb' }));
  app.use(express.urlencoded({ extended: true, limit: '10mb' }));
  app.use(cookieParser());

  // 测试环境下关闭日志
  if (process.env['NODE_ENV'] === 'test') {
    app.use(morgan('dev', {
      skip: () => true
    }));
  }

  // 速率限制 (测试环境下更宽松)
  const limiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: process.env['NODE_ENV'] === 'test' ? 1000 : 100,
    message: {
      success: false,
      message: '请求过于频繁，请稍后再试',
      code: 'RATE_LIMIT_EXCEEDED'
    },
    standardHeaders: true,
    legacyHeaders: false,
  });
  app.use('/api/', limiter);

  // 注册路由
  app.use('/api/v1', routes);

  // 健康检查端点 (不需要认证)
  app.get('/api/health', (_req, res) => {
    res.json({ 
      success: true, 
      message: 'Service is healthy',
      timestamp: new Date().toISOString(),
      environment: process.env['NODE_ENV'] || 'development'
    });
  });

  // 错误处理中间件
  app.use(errorHandler);

  // 404 处理
  app.use('*', (_req, res) => {
    res.status(404).json({
      success: false,
      message: 'Route not found'
    });
  });

  return app;
};