import { PrismaClient } from '@prisma/client';
import { createClient } from 'redis';
import dotenv from 'dotenv';

// Load test environment variables
dotenv.config({ path: '.env.test' });

// Global test setup
let prisma: PrismaClient;
let redisClient: any;

// Declare global types
declare global {
  var prisma: PrismaClient;
  var redisClient: any;
}

beforeAll(async () => {
  // Initialize database connection for tests
  prisma = new PrismaClient({
    datasources: {
      db: {
        url: process.env['DATABASE_URL'] || 'mysql://root:password@localhost:3306/ops_management_test'
      }
    }
  });

  // Initialize Redis client for tests
  try {
    redisClient = createClient({
      url: process.env['REDIS_URL'] || 'redis://localhost:6379'
    });
    await redisClient.connect();
  } catch (error) {
    console.warn('Redis connection failed in tests:', error);
  }

  // Set global variables after initialization
  globalThis.prisma = prisma;
  globalThis.redisClient = redisClient;
});

afterAll(async () => {
  // Cleanup connections
  if (prisma) {
    await prisma.$disconnect();
  }
  if (redisClient && redisClient.isReady) {
    await redisClient.quit();
  }
});

// Mock external services for testing
jest.mock('../src/services/email.service', () => ({
  sendEmail: jest.fn().mockResolvedValue({ success: true })
}));

jest.mock('../src/services/sms.service', () => ({
  sendSms: jest.fn().mockResolvedValue({ success: true })
}));