/**
 * 安全性验证测试 - 独立运行，不依赖数据库
 * 主要测试安全工具函数和配置
 */

import bcrypt from 'bcrypt';
import jwt from 'jsonwebtoken';

describe('安全性验证测试套件', () => {
  
  describe('密码加密安全性', () => {
    test('bcrypt哈希应该是安全的', async () => {
      const plainPassword = 'TestPassword123!';
      const saltRounds = 12;
      
      const hashedPassword = await bcrypt.hash(plainPassword, saltRounds);
      
      // 验证哈希结果
      expect(hashedPassword).not.toBe(plainPassword);
      expect(hashedPassword.length).toBeGreaterThan(50);
      expect(hashedPassword.startsWith('$2b$12$')).toBe(true); // bcrypt格式
      
      // 验证密码验证
      const isValid = await bcrypt.compare(plainPassword, hashedPassword);
      expect(isValid).toBe(true);
      
      const isInvalid = await bcrypt.compare('wrongPassword', hashedPassword);
      expect(isInvalid).toBe(false);
    });

    test('相同密码应该产生不同的哈希', async () => {
      const password = 'SamePassword123';
      const hash1 = await bcrypt.hash(password, 12);
      const hash2 = await bcrypt.hash(password, 12);
      
      // 由于盐值不同，哈希应该不同
      expect(hash1).not.toBe(hash2);
      
      // 但都应该能验证原密码
      expect(await bcrypt.compare(password, hash1)).toBe(true);
      expect(await bcrypt.compare(password, hash2)).toBe(true);
    });
  });

  describe('JWT令牌安全性', () => {
    const secretKey = 'test-secret-key-for-testing';
    
    test('JWT令牌生成和验证', () => {
      const payload = {
        userId: '123',
        username: 'testuser',
        email: '<EMAIL>'
      };
      
      // 生成令牌
      const token = jwt.sign(payload, secretKey, { expiresIn: '1h' });
      
      expect(typeof token).toBe('string');
      expect(token.split('.')).toHaveLength(3); // JWT格式: header.payload.signature
      
      // 验证令牌
      const decoded = jwt.verify(token, secretKey) as any;
      expect(decoded.userId).toBe(payload.userId);
      expect(decoded.username).toBe(payload.username);
      expect(decoded.email).toBe(payload.email);
    });

    test('过期令牌应该被拒绝', () => {
      const payload = { userId: '123' };
      
      // 生成已过期的令牌
      const expiredToken = jwt.sign(payload, secretKey, { expiresIn: '-1h' });
      
      expect(() => {
        jwt.verify(expiredToken, secretKey);
      }).toThrow('jwt expired');
    });

    test('错误密钥应该被拒绝', () => {
      const payload = { userId: '123' };
      const token = jwt.sign(payload, secretKey);
      
      expect(() => {
        jwt.verify(token, 'wrong-secret-key');
      }).toThrow('invalid signature');
    });

    test('格式错误的令牌应该被拒绝', () => {
      const malformedTokens = [
        'invalid.jwt.token',
        'not-a-jwt-at-all',
        'header.payload', // 缺少签名
        '', // 空令牌
      ];
      
      malformedTokens.forEach(token => {
        expect(() => {
          jwt.verify(token, secretKey);
        }).toThrow();
      });
    });
  });

  describe('输入验证安全性', () => {
    test('邮箱格式验证', () => {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      
      // 有效邮箱
      const validEmails = [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>'
      ];
      
      validEmails.forEach(email => {
        expect(emailRegex.test(email)).toBe(true);
      });
      
      // 无效邮箱
      const invalidEmails = [
        'invalid-email',
        'test@',
        '@domain.com',
        '<EMAIL>',
        'test@domain',
        ''
      ];
      
      invalidEmails.forEach(email => {
        expect(emailRegex.test(email)).toBe(false);
      });
    });

    test('XSS检测', () => {
      const xssPattern = /<script|javascript:|on\w+=/i;
      
      const xssPayloads = [
        '<script>alert("XSS")</script>',
        '<img src=x onerror=alert("XSS")>',
        'javascript:alert("XSS")',
        '<svg onload=alert("XSS")>',
        '<div onclick="malicious()">',
      ];
      
      xssPayloads.forEach(payload => {
        expect(xssPattern.test(payload)).toBe(true);
      });
      
      const safeInputs = [
        'normal text',
        '<EMAIL>',
        'Safe content without scripts',
        '<p>Safe HTML content</p>',
      ];
      
      safeInputs.forEach(input => {
        expect(xssPattern.test(input)).toBe(false);
      });
    });

    test('SQL注入检测', () => {
      const sqlInjectionPayloads = [
        "'; DROP TABLE users; --",
        "admin'; DELETE FROM users; --",
        "' OR 1=1; --",
        "' UNION SELECT * FROM users; --",
        "'; INSERT INTO users VALUES ('hacker', 'password'); --"
      ];
      
      // 简单的SQL注入检测 - 检查常见的SQL注入模式
      sqlInjectionPayloads.forEach(payload => {
        const hasSqlPatterns = 
          payload.includes("'") || 
          payload.includes(';') || 
          payload.includes('--') ||
          payload.includes('DROP') ||
          payload.includes('DELETE') ||
          payload.includes('UNION');
        
        expect(hasSqlPatterns).toBe(true);
      });
    });

    test('路径遍历检测', () => {
      const pathTraversalPattern = /\.\.[\/\\]/;
      
      const traversalPayloads = [
        '../../../etc/passwd',
        '..\\..\\windows\\system32',
        '....//....//etc/passwd',
        '../config/database.json'
      ];
      
      traversalPayloads.forEach(payload => {
        expect(pathTraversalPattern.test(payload)).toBe(true);
      });
      
      const safeFilenames = [
        'document.pdf',
        'image.jpg',
        'data/report.xlsx',
        'uploads/file.txt'
      ];
      
      safeFilenames.forEach(filename => {
        expect(pathTraversalPattern.test(filename)).toBe(false);
      });
    });
  });

  describe('安全配置验证', () => {
    test('环境变量安全设置', () => {
      const testEnvVars = {
        JWT_SECRET: 'test-jwt-secret-key-should-be-long',
        REFRESH_TOKEN_SECRET: 'test-refresh-token-secret-key',
        CRYPTO_SECRET: 'test-crypto-secret-key-32-chars-l',
        NODE_ENV: 'test'
      };
      
      // JWT密钥长度检查
      expect(testEnvVars.JWT_SECRET.length).toBeGreaterThan(16);
      expect(testEnvVars.REFRESH_TOKEN_SECRET.length).toBeGreaterThan(16);
      
      // 加密密钥长度检查（AES-256需要32字节）
      expect(testEnvVars.CRYPTO_SECRET.length).toBe(32);
      
      // 环境设置检查
      expect(['development', 'test', 'production']).toContain(testEnvVars.NODE_ENV);
    });

    test('CORS配置安全性', () => {
      const corsConfig = {
        origin: 'http://localhost:3000',
        credentials: true,
        optionsSuccessStatus: 200
      };
      
      // 确认没有使用通配符 '*' 与 credentials: true 组合
      expect(corsConfig.origin).not.toBe('*');
      expect(corsConfig.credentials).toBe(true);
      
      // 验证源URL格式
      const originUrl = new URL(corsConfig.origin);
      expect(originUrl.protocol).toMatch(/^https?:$/);
      expect(originUrl.hostname).toBeTruthy();
    });

    test('速率限制配置', () => {
      const rateLimitConfig = {
        windowMs: 15 * 60 * 1000, // 15分钟
        max: 100, // 最多100次请求
        standardHeaders: true,
        legacyHeaders: false
      };
      
      // 窗口时间应该合理（不少于5分钟，不超过1小时）
      expect(rateLimitConfig.windowMs).toBeGreaterThan(5 * 60 * 1000);
      expect(rateLimitConfig.windowMs).toBeLessThanOrEqual(60 * 60 * 1000);
      
      // 请求限制应该合理
      expect(rateLimitConfig.max).toBeGreaterThan(0);
      expect(rateLimitConfig.max).toBeLessThan(10000);
      
      // 现代头设置
      expect(rateLimitConfig.standardHeaders).toBe(true);
      expect(rateLimitConfig.legacyHeaders).toBe(false);
    });
  });

  describe('加密工具函数测试', () => {
    test('AES加密解密功能', () => {
      const crypto = require('crypto');
      const secretKey = 'test-crypto-secret-key-32-chars-l'; // 32字节密钥
      const algorithm = 'aes-256-gcm';
      
      // 模拟加密函数
      function encrypt(text: string, key: string): { encrypted: string; iv: string; tag: string } {
        const iv = crypto.randomBytes(16);
        const cipher = crypto.createCipher(algorithm, key);
        
        let encrypted = cipher.update(text, 'utf8', 'hex');
        encrypted += cipher.final('hex');
        
        return {
          encrypted,
          iv: iv.toString('hex'),
          tag: cipher.getAuthTag ? cipher.getAuthTag().toString('hex') : ''
        };
      }
      
      // 模拟解密函数
      function decrypt(encryptedData: { encrypted: string; iv: string; tag: string }, key: string): string {
        try {
          const decipher = crypto.createDecipher(algorithm, key);
          
          let decrypted = decipher.update(encryptedData.encrypted, 'hex', 'utf8');
          decrypted += decipher.final('utf8');
          
          return decrypted;
        } catch (error) {
          return '';
        }
      }
      
      const originalText = 'sensitive configuration data';
      const encrypted = encrypt(originalText, secretKey);
      
      // 验证加密结果
      expect(encrypted.encrypted).not.toBe(originalText);
      expect(encrypted.encrypted.length).toBeGreaterThan(0);
      expect(encrypted.iv.length).toBe(32); // 16字节的hex表示
      
      // 验证解密结果
      const decrypted = decrypt(encrypted, secretKey);
      expect(decrypted).toBe(originalText);
    });
  });

  describe('安全头验证', () => {
    test('Helmet安全头配置', () => {
      const helmetConfig = {
        contentSecurityPolicy: false, // 测试环境禁用
        crossOriginEmbedderPolicy: false, // 测试环境禁用
        xssFilter: true,
        noSniff: true,
        frameguard: { action: 'deny' }
      };
      
      // XSS保护
      expect(helmetConfig.xssFilter).toBe(true);
      
      // MIME类型嗅探保护
      expect(helmetConfig.noSniff).toBe(true);
      
      // 点击劫持保护
      expect(helmetConfig.frameguard.action).toBe('deny');
    });
  });

  describe('密码策略验证', () => {
    test('密码强度检查', () => {
      function checkPasswordStrength(password: string): { strong: boolean; reasons: string[] } {
        const reasons: string[] = [];
        
        if (password.length < 8) {
          reasons.push('密码长度至少8位');
        }
        
        if (!/[a-z]/.test(password)) {
          reasons.push('需要包含小写字母');
        }
        
        if (!/[A-Z]/.test(password)) {
          reasons.push('需要包含大写字母');
        }
        
        if (!/[0-9]/.test(password)) {
          reasons.push('需要包含数字');
        }
        
        if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
          reasons.push('需要包含特殊字符');
        }
        
        const commonPasswords = ['password', '123456', 'qwerty', 'admin', 'root'];
        if (commonPasswords.includes(password.toLowerCase())) {
          reasons.push('不能使用常见密码');
        }
        
        return {
          strong: reasons.length === 0,
          reasons
        };
      }
      
      // 弱密码测试
      const weakPasswords = [
        '123',
        'password',
        '12345678',
        'abcdefgh',
        'PASSWORD'
      ];
      
      weakPasswords.forEach(password => {
        const result = checkPasswordStrength(password);
        expect(result.strong).toBe(false);
        expect(result.reasons.length).toBeGreaterThan(0);
      });
      
      // 强密码测试
      const strongPasswords = [
        'MySecureP@ssw0rd!',
        'C0mplex!Password123',
        'Str0ng#Passw0rd$2025'
      ];
      
      strongPasswords.forEach(password => {
        const result = checkPasswordStrength(password);
        expect(result.strong).toBe(true);
        expect(result.reasons.length).toBe(0);
      });
    });
  });
});