import request from 'supertest';
import jwt from 'jsonwebtoken';
import bcrypt from 'bcrypt';
import { createMinimalTestApp } from '../helpers/minimal-app';
import { PrismaClient } from '@prisma/client';

// 声明全局类型
declare global {
  var prisma: PrismaClient;
}

describe('身份认证安全测试', () => {
  let app: any;
  let prisma: PrismaClient;
  let testUser: any;

  beforeAll(async () => {
    app = await createMinimalTestApp();
    prisma = globalThis.prisma;
    
    // 创建测试用户
    const hashedPassword = await bcrypt.hash('testPassword123', 12);
    testUser = await prisma.user.create({
      data: {
        username: 'security_test_user',
        email: '<EMAIL>',
        password: hashedPassword,
        status: 'ACTIVE'
      }
    });
  });

  afterAll(async () => {
    // 清理测试数据
    if (testUser) {
      await prisma.user.delete({ where: { id: testUser.id } }).catch(() => {});
    }
  });

  describe('JWT令牌安全性', () => {
    test('应该拒绝无令牌的请求', async () => {
      const response = await request(app)
        .get('/api/v1/users/profile')
        .expect(401);

      expect(response.body).toHaveProperty('success', false);
      expect(response.body.message).toMatch(/(访问令牌不存在|Unauthorized)/i);
    });

    test('应该拒绝无效令牌', async () => {
      const response = await request(app)
        .get('/api/v1/users/profile')
        .set('Authorization', 'Bearer invalid_token')
        .expect(401);

      expect(response.body).toHaveProperty('success', false);
    });

    test('应该拒绝过期令牌', async () => {
      const expiredToken = jwt.sign(
        { userId: testUser.id, username: testUser.username },
        process.env['JWT_SECRET'] || 'test-jwt-secret',
        { expiresIn: '-1h' } // 过期令牌
      );

      const response = await request(app)
        .get('/api/v1/users/profile')
        .set('Authorization', `Bearer ${expiredToken}`)
        .expect(401);

      expect(response.body).toHaveProperty('success', false);
    });

    test('应该拒绝错误密钥签名的令牌', async () => {
      const fakeToken = jwt.sign(
        { userId: testUser.id, username: testUser.username },
        'wrong-secret-key',
        { expiresIn: '1h' }
      );

      const response = await request(app)
        .get('/api/v1/users/profile')
        .set('Authorization', `Bearer ${fakeToken}`)
        .expect(401);

      expect(response.body).toHaveProperty('success', false);
    });

    test('应该拒绝格式错误的令牌', async () => {
      const malformedTokens = [
        'malformed.jwt.token',
        'Bearer',
        'Bearer ',
        'not-a-jwt-token',
        ''
      ];

      for (const token of malformedTokens) {
        const response = await request(app)
          .get('/api/v1/users/profile')
          .set('Authorization', token)
          .expect(401);

        expect(response.body).toHaveProperty('success', false);
      }
    });
  });

  describe('登录安全性', () => {
    test('应该要求用户名和密码', async () => {
      // 缺少用户名
      const response1 = await request(app)
        .post('/api/v1/auth/login')
        .send({ password: 'testPassword123' })
        .expect(400);

      expect(response1.body).toHaveProperty('success', false);

      // 缺少密码
      const response2 = await request(app)
        .post('/api/v1/auth/login')
        .send({ username: 'testuser' })
        .expect(400);

      expect(response2.body).toHaveProperty('success', false);
    });

    test('应该拒绝错误的凭据', async () => {
      const response = await request(app)
        .post('/api/v1/auth/login')
        .send({
          username: testUser.username,
          password: 'wrongPassword'
        })
        .expect(401);

      expect(response.body).toHaveProperty('success', false);
      expect(response.body.message).toMatch(/(用户名或密码错误|Invalid credentials)/i);
    });

    test('应该拒绝不存在的用户', async () => {
      const response = await request(app)
        .post('/api/v1/auth/login')
        .send({
          username: 'nonexistent_user',
          password: 'anyPassword'
        })
        .expect(401);

      expect(response.body).toHaveProperty('success', false);
    });

    test('应该拒绝被禁用的用户', async () => {
      // 创建被禁用的用户
      const disabledUser = await prisma.user.create({
        data: {
          username: 'disabled_user',
          email: '<EMAIL>',
          password: await bcrypt.hash('password123', 12),
          status: 'INACTIVE'
        }
      });

      const response = await request(app)
        .post('/api/v1/auth/login')
        .send({
          username: disabledUser.username,
          password: 'password123'
        })
        .expect(401);

      expect(response.body).toHaveProperty('success', false);
      expect(response.body.message).toMatch(/(账户已被禁用|Account disabled)/i);

      // 清理
      await prisma.user.delete({ where: { id: disabledUser.id } }).catch(() => {});
    });
  });

  describe('密码安全性', () => {
    test('密码应该正确哈希存储', async () => {
      const plainPassword = 'TestPassword123!';
      const hashedPassword = await bcrypt.hash(plainPassword, 12);
      
      // 验证密码未以明文存储
      expect(hashedPassword).not.toBe(plainPassword);
      expect(hashedPassword.length).toBeGreaterThan(50);
      
      // 验证可以正确验证
      const isValid = await bcrypt.compare(plainPassword, hashedPassword);
      expect(isValid).toBe(true);
      
      const isInvalid = await bcrypt.compare('wrongpassword', hashedPassword);
      expect(isInvalid).toBe(false);
    });

    test('应该要求强密码（注册时）', async () => {
      const weakPasswords = [
        '123',           // 太短
        'password',      // 常见密码
        '********',      // 纯数字
        'abcdefgh',      // 纯字母
        'PASSWORD',      // 纯大写
      ];
      
      for (const weakPassword of weakPasswords) {
        const response = await request(app)
          .post('/api/v1/auth/register')
          .send({
            username: `user_${Date.now()}_${Math.random()}`,
            email: `test_${Date.now()}_${Math.random()}@example.com`,
            password: weakPassword
          });

        // 应该拒绝弱密码（400或409状态码都可以接受）
        expect([400, 409]).toContain(response.status);
        expect(response.body).toHaveProperty('success', false);
      }
    });
  });

  describe('输入验证安全性', () => {
    test('应该防范SQL注入', async () => {
      const sqlInjectionPayloads = [
        "'; DROP TABLE users; --",
        "admin'; DELETE FROM users; --",
        "' OR 1=1; --",
        "' UNION SELECT * FROM users; --"
      ];
      
      for (const payload of sqlInjectionPayloads) {
        const response = await request(app)
          .post('/api/v1/auth/login')
          .send({
            username: payload,
            password: 'password'
          });

        // 应该返回400或401，而不是500（内部错误）
        expect([400, 401]).toContain(response.status);
        expect(response.body).toHaveProperty('success', false);
      }
      
      // 确认数据库表仍然存在且未被破坏
      const usersCount = await prisma.user.count();
      expect(usersCount).toBeGreaterThan(0);
    });

    test('应该防范XSS攻击', async () => {
      const xssPayloads = [
        '<script>alert("XSS")</script>',
        '<img src=x onerror=alert("XSS")>',
        'javascript:alert("XSS")',
        '<svg onload=alert("XSS")>'
      ];
      
      for (const payload of xssPayloads) {
        const response = await request(app)
          .post('/api/v1/auth/register')
          .send({
            username: payload,
            email: '<EMAIL>',
            password: 'Password123!'
          });

        expect([400, 409]).toContain(response.status);
        expect(response.body).toHaveProperty('success', false);
      }
    });

    test('应该验证输入长度', async () => {
      const longString = 'a'.repeat(1000);
      
      const response = await request(app)
        .post('/api/v1/auth/register')
        .send({
          username: longString,
          email: '<EMAIL>',
          password: 'Password123!'
        })
        .expect(400);

      expect(response.body).toHaveProperty('success', false);
    });

    test('应该验证邮箱格式', async () => {
      const invalidEmails = [
        'invalid-email',
        'test@',
        '@domain.com',
        '<EMAIL>',
        'test@domain',
        ''
      ];
      
      for (const invalidEmail of invalidEmails) {
        const response = await request(app)
          .post('/api/v1/auth/register')
          .send({
            username: `user_${Date.now()}_${Math.random()}`,
            email: invalidEmail,
            password: 'Password123!'
          })
          .expect(400);

        expect(response.body).toHaveProperty('success', false);
      }
    });
  });

  describe('会话管理安全性', () => {
    test('登出后令牌应该失效', async () => {
      // 先登录获取令牌
      const loginResponse = await request(app)
        .post('/api/v1/auth/login')
        .send({
          username: testUser.username,
          password: 'testPassword123'
        })
        .expect(200);

      const token = loginResponse.body.data?.access_token || loginResponse.body.accessToken;
      expect(token).toBeDefined();
      
      // 验证令牌有效
      const profileResponse = await request(app)
        .get('/api/v1/users/profile')
        .set('Authorization', `Bearer ${token}`)
        .expect(200);
      
      expect(profileResponse.body).toHaveProperty('success', true);
      
      // 登出
      await request(app)
        .post('/api/v1/auth/logout')
        .set('Authorization', `Bearer ${token}`)
        .expect(200);
      
      // 验证令牌已失效
      const response = await request(app)
        .get('/api/v1/users/profile')
        .set('Authorization', `Bearer ${token}`)
        .expect(401);

      expect(response.body).toHaveProperty('success', false);
    });

    test('令牌刷新应该正常工作', async () => {
      // 登录获取令牌
      const loginResponse = await request(app)
        .post('/api/v1/auth/login')
        .send({
          username: testUser.username,
          password: 'testPassword123'
        })
        .expect(200);

      const refreshToken = loginResponse.body.data?.refresh_token || loginResponse.body.refreshToken;
      
      if (refreshToken) {
        // 使用刷新令牌获取新的访问令牌
        const refreshResponse = await request(app)
          .post('/api/v1/auth/refresh')
          .send({ refresh_token: refreshToken })
          .expect(200);

        expect(refreshResponse.body).toHaveProperty('success', true);
        expect(refreshResponse.body.data).toHaveProperty('access_token');
      }
    });
  });

  describe('响应安全性', () => {
    test('响应不应包含敏感信息', async () => {
      const response = await request(app)
        .post('/api/v1/auth/login')
        .send({
          username: testUser.username,
          password: 'testPassword123'
        })
        .expect(200);

      const responseBody = JSON.stringify(response.body);
      
      // 响应不应包含密码或哈希
      expect(responseBody).not.toContain('testPassword123');
      expect(responseBody).not.toContain('$2b$'); // bcrypt哈希前缀
      
      // 用户信息不应包含敏感字段
      if (response.body.data?.user) {
        expect(response.body.data.user.password).toBeUndefined();
      }
    });

    test('错误响应不应泄露系统信息', async () => {
      const response = await request(app)
        .get('/api/v1/nonexistent-endpoint')
        .expect(404);

      const responseBody = JSON.stringify(response.body);
      
      // 不应包含文件路径
      expect(responseBody).not.toMatch(/\/.*\.(ts|js)/);
      expect(responseBody).not.toMatch(/Error:.*at/);
      expect(responseBody).not.toContain('prisma');
      expect(responseBody).not.toContain('mysql');
      expect(responseBody).not.toContain('stack trace');
    });
  });

  describe('请求头安全性', () => {
    test('应该设置安全HTTP头', async () => {
      const response = await request(app)
        .get('/api/health')
        .expect(200);

      // 检查常见安全头
      const headers = response.headers;
      
      // 至少应该有一些基本的安全头
      expect(
        headers['x-content-type-options'] ||
        headers['x-frame-options'] ||  
        headers['x-xss-protection']
      ).toBeDefined();
    });

    test('应该正确处理CORS', async () => {
      const response = await request(app)
        .options('/api/v1/users')
        .expect(200);

      expect(response.headers['access-control-allow-origin']).toBeDefined();
    });
  });
});