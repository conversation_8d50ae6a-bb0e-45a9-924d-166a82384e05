import request from 'supertest';
import jwt from 'jsonwebtoken';
import bcrypt from 'bcrypt';
import { createTestApp } from '../helpers/app';
import { PrismaClient } from '@prisma/client';

// 声明全局类型
declare global {
  var prisma: PrismaClient;
}

describe('安全性测试套件', () => {
  let app: any;
  let prisma: PrismaClient;
  let testUser: any;
  let authToken: string;

  beforeAll(async () => {
    app = await createTestApp();
    prisma = globalThis.prisma;
    
    // 创建测试用户
    const hashedPassword = await bcrypt.hash('testPassword123', 12);
    testUser = await prisma.user.create({
      data: {
        username: 'security_test_user',
        email: '<EMAIL>',
        password: hashedPassword,
        status: 'ACTIVE'
      }
    });

    // 生成测试JWT令牌
    authToken = jwt.sign(
      { userId: testUser.id, username: testUser.username, email: testUser.email },
      process.env['JWT_SECRET'] || 'test-jwt-secret',
      { expiresIn: '1h' }
    );
  });

  afterAll(async () => {
    // 清理测试数据
    if (testUser) {
      await prisma.user.delete({ where: { id: testUser.id } }).catch(() => {});
    }
  });

  describe('身份认证安全测试', () => {
    test('应该拒绝无令牌的请求', async () => {
      const response = await request(app)
        .get('/api/v1/users')
        .expect(401);

      expect(response.body).toHaveProperty('success', false);
      expect(response.body.message).toContain('访问令牌不存在');
    });

    test('应该拒绝无效令牌的请求', async () => {
      const response = await request(app)
        .get('/api/v1/users')
        .set('Authorization', 'Bearer invalid_token')
        .expect(401);

      expect(response.body).toHaveProperty('success', false);
    });

    test('应该拒绝过期令牌的请求', async () => {
      const expiredToken = jwt.sign(
        { userId: testUser.id, username: testUser.username },
        process.env['JWT_SECRET'] || 'test-jwt-secret',
        { expiresIn: '-1h' } // 过期令牌
      );

      const response = await request(app)
        .get('/api/v1/users')
        .set('Authorization', `Bearer ${expiredToken}`)
        .expect(401);

      expect(response.body).toHaveProperty('success', false);
    });

    test('应该拒绝错误密钥签名的令牌', async () => {
      const fakeToken = jwt.sign(
        { userId: testUser.id, username: testUser.username },
        'wrong-secret-key',
        { expiresIn: '1h' }
      );

      const response = await request(app)
        .get('/api/v1/users')
        .set('Authorization', `Bearer ${fakeToken}`)
        .expect(401);

      expect(response.body).toHaveProperty('success', false);
    });
  });

  describe('授权安全测试', () => {
    test('应该拒绝无权限用户访问管理接口', async () => {
      // 创建无特殊权限的用户
      const normalUser = await prisma.user.create({
        data: {
          username: 'normal_user',
          email: '<EMAIL>',
          password: await bcrypt.hash('password123', 12),
          status: 'ACTIVE'
        }
      });

      const normalToken = jwt.sign(
        { userId: normalUser.id, username: normalUser.username },
        process.env['JWT_SECRET'] || 'test-jwt-secret',
        { expiresIn: '1h' }
      );

      const response = await request(app)
        .get('/api/v1/users')
        .set('Authorization', `Bearer ${normalToken}`)
        .expect(403);

      expect(response.body).toHaveProperty('success', false);
      expect(response.body.message).toContain('权限不足');

      // 清理
      await prisma.user.delete({ where: { id: normalUser.id } }).catch(() => {});
    });

    test('应该检查资源所有权', async () => {
      // 尝试访问其他用户的资源
      const response = await request(app)
        .get(`/api/v1/users/${testUser.id + 1}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(403);

      expect(response.body).toHaveProperty('success', false);
    });
  });

  describe('输入验证安全测试', () => {
    test('应该防范SQL注入攻击', async () => {
      const maliciousInput = "'; DROP TABLE users; --";
      
      const response = await request(app)
        .post('/api/v1/auth/login')
        .send({
          username: maliciousInput,
          password: 'password'
        })
        .expect(400);

      expect(response.body).toHaveProperty('success', false);
      
      // 确认数据库表仍然存在
      const usersCount = await prisma.user.count();
      expect(usersCount).toBeGreaterThan(0);
    });

    test('应该防范XSS攻击', async () => {
      const xssPayload = '<script>alert("XSS")</script>';
      
      const response = await request(app)
        .post('/api/v1/auth/register')
        .send({
          username: xssPayload,
          email: '<EMAIL>',
          password: 'Password123!'
        })
        .expect(400);

      expect(response.body).toHaveProperty('success', false);
    });

    test('应该验证输入长度限制', async () => {
      const longString = 'a'.repeat(1000);
      
      const response = await request(app)
        .post('/api/v1/auth/register')
        .send({
          username: longString,
          email: '<EMAIL>',
          password: 'Password123!'
        })
        .expect(400);

      expect(response.body).toHaveProperty('success', false);
    });

    test('应该验证邮箱格式', async () => {
      const response = await request(app)
        .post('/api/v1/auth/register')
        .send({
          username: 'testuser',
          email: 'invalid-email-format',
          password: 'Password123!'
        })
        .expect(400);

      expect(response.body).toHaveProperty('success', false);
      expect(response.body.message).toContain('邮箱');
    });
  });

  describe('密码安全测试', () => {
    test('应该要求强密码', async () => {
      const weakPasswords = ['123', 'password', '12345678', 'qwerty'];
      
      for (const weakPassword of weakPasswords) {
        const response = await request(app)
          .post('/api/v1/auth/register')
          .send({
            username: `user_${Date.now()}`,
            email: `test_${Date.now()}@example.com`,
            password: weakPassword
          })
          .expect(400);

        expect(response.body).toHaveProperty('success', false);
        expect(response.body.message).toContain('密码');
      }
    });

    test('应该正确哈希密码', async () => {
      const plainPassword = 'TestPassword123!';
      const hashedPassword = await bcrypt.hash(plainPassword, 12);
      
      // 验证密码未以明文存储
      expect(hashedPassword).not.toBe(plainPassword);
      expect(hashedPassword.length).toBeGreaterThan(50);
      
      // 验证可以正确验证
      const isValid = await bcrypt.compare(plainPassword, hashedPassword);
      expect(isValid).toBe(true);
      
      const isInvalid = await bcrypt.compare('wrongpassword', hashedPassword);
      expect(isInvalid).toBe(false);
    });
  });

  describe('会话管理安全测试', () => {
    test('应该正确处理登出', async () => {
      // 先登录获取令牌
      const loginResponse = await request(app)
        .post('/api/v1/auth/login')
        .send({
          username: testUser.username,
          password: 'testPassword123'
        });

      const token = loginResponse.body.data.access_token;
      
      // 登出
      await request(app)
        .post('/api/v1/auth/logout')
        .set('Authorization', `Bearer ${token}`)
        .expect(200);
      
      // 验证令牌已失效
      const response = await request(app)
        .get('/api/v1/users/profile')
        .set('Authorization', `Bearer ${token}`)
        .expect(401);

      expect(response.body).toHaveProperty('success', false);
    });

    test('应该限制并发会话', async () => {
      // 连续多次登录
      const loginPromises = [];
      for (let i = 0; i < 5; i++) {
        loginPromises.push(
          request(app)
            .post('/api/v1/auth/login')
            .send({
              username: testUser.username,
              password: 'testPassword123'
            })
        );
      }
      
      const responses = await Promise.all(loginPromises);
      
      // 至少应有部分请求成功
      const successCount = responses.filter(r => r.status === 200).length;
      expect(successCount).toBeGreaterThan(0);
      expect(successCount).toBeLessThanOrEqual(5);
    });
  });

  describe('速率限制安全测试', () => {
    test('应该限制登录尝试次数', async () => {
      const attempts = [];
      
      // 连续失败登录尝试
      for (let i = 0; i < 10; i++) {
        attempts.push(
          request(app)
            .post('/api/v1/auth/login')
            .send({
              username: testUser.username,
              password: 'wrongpassword'
            })
        );
      }
      
      const responses = await Promise.all(attempts);
      
      // 应该有部分请求被拒绝（429状态码）
      const rateLimitedCount = responses.filter(r => r.status === 429).length;
      expect(rateLimitedCount).toBeGreaterThan(0);
    }, 30000);

    test('应该限制API请求频率', async () => {
      const requests = [];
      
      // 快速发送大量请求
      for (let i = 0; i < 50; i++) {
        requests.push(
          request(app)
            .get('/api/health')
        );
      }
      
      const responses = await Promise.all(requests);
      
      // 大部分请求应该成功，但可能有部分被限制
      const successCount = responses.filter(r => r.status === 200).length;
      const rateLimitedCount = responses.filter(r => r.status === 429).length;
      
      expect(successCount).toBeGreaterThan(0);
      console.log(`成功请求: ${successCount}, 被限制请求: ${rateLimitedCount}`);
    }, 30000);
  });

  describe('敏感信息保护测试', () => {
    test('响应中不应包含敏感信息', async () => {
      const response = await request(app)
        .post('/api/v1/auth/login')
        .send({
          username: testUser.username,
          password: 'testPassword123'
        })
        .expect(200);

      // 响应不应包含密码
      expect(JSON.stringify(response.body)).not.toContain('testPassword123');
      expect(JSON.stringify(response.body)).not.toContain('password');
      
      // 用户信息不应包含敏感字段
      if (response.body.data.user) {
        expect(response.body.data.user.password).toBeUndefined();
      }
    });

    test('错误信息不应泄露系统信息', async () => {
      const response = await request(app)
        .get('/api/v1/nonexistent')
        .expect(404);

      // 错误信息不应包含文件路径、内部错误等
      const errorMessage = JSON.stringify(response.body);
      expect(errorMessage).not.toMatch(/\/.*\.ts/); // 不包含TypeScript文件路径
      expect(errorMessage).not.toMatch(/Error:.*at/); // 不包含堆栈跟踪
      expect(errorMessage).not.toContain('prisma'); // 不包含数据库信息
    });
  });

  describe('CORS和安全头测试', () => {
    test('应该设置适当的CORS头', async () => {
      const response = await request(app)
        .options('/api/v1/users')
        .expect(200);

      expect(response.headers['access-control-allow-origin']).toBeDefined();
      expect(response.headers['access-control-allow-methods']).toBeDefined();
    });

    test('应该设置安全相关的HTTP头', async () => {
      const response = await request(app)
        .get('/api/health')
        .expect(200);

      // 检查安全头
      expect(response.headers['x-content-type-options']).toBe('nosniff');
      expect(response.headers['x-frame-options']).toBeDefined();
      expect(response.headers['x-xss-protection']).toBeDefined();
    });
  });
});