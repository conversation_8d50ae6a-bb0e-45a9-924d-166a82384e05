/**
 * 安全性检查脚本 - 独立运行，不依赖Jest或数据库
 * 使用Node.js直接运行
 */

const bcrypt = require('bcrypt');
const jwt = require('jsonwebtoken');
const crypto = require('crypto');

// 颜色输出
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
};

// 测试结果统计
let totalTests = 0;
let passedTests = 0;
let failedTests = 0;

function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`);
}

function test(name, testFn) {
  totalTests++;
  try {
    testFn();
    log(`✅ ${name}`, colors.green);
    passedTests++;
  } catch (error) {
    log(`❌ ${name}: ${error.message}`, colors.red);
    failedTests++;
  }
}

function expect(actual) {
  return {
    toBe(expected) {
      if (actual !== expected) {
        throw new Error(`Expected ${expected}, but got ${actual}`);
      }
    },
    not: {
      toBe(expected) {
        if (actual === expected) {
          throw new Error(`Expected not to be ${expected}, but got ${actual}`);
        }
      }
    },
    toBeGreaterThan(expected) {
      if (actual <= expected) {
        throw new Error(`Expected ${actual} to be greater than ${expected}`);
      }
    },
    toBeLessThan(expected) {
      if (actual >= expected) {
        throw new Error(`Expected ${actual} to be less than ${expected}`);
      }
    },
    toBeLessThanOrEqual(expected) {
      if (actual > expected) {
        throw new Error(`Expected ${actual} to be less than or equal to ${expected}`);
      }
    },
    toContain(expected) {
      if (!actual.includes(expected)) {
        throw new Error(`Expected ${actual} to contain ${expected}`);
      }
    },
    toMatch(regex) {
      if (!regex.test(actual)) {
        throw new Error(`Expected ${actual} to match ${regex}`);
      }
    },
    toHaveLength(expected) {
      if (actual.length !== expected) {
        throw new Error(`Expected length ${expected}, but got ${actual.length}`);
      }
    },
    toThrow() {
      if (typeof actual !== 'function') {
        throw new Error('Expected a function that throws');
      }
      try {
        actual();
        throw new Error('Expected function to throw, but it did not');
      } catch (e) {
        // Expected behavior
      }
    }
  };
}

async function runSecurityTests() {
  log('\n🛡️  运维服务管理系统 - 安全性验证测试', colors.cyan + colors.bright);
  log('=========================================', colors.cyan);

  // 1. 密码加密安全性测试
  log('\n📝 密码加密安全性测试', colors.yellow);

  test('bcrypt哈希应该是安全的', async () => {
    const plainPassword = 'TestPassword123!';
    const saltRounds = 12;
    
    const hashedPassword = await bcrypt.hash(plainPassword, saltRounds);
    
    expect(hashedPassword).not.toBe(plainPassword);
    expect(hashedPassword.length).toBeGreaterThan(50);
    expect(hashedPassword.startsWith('$2b$12$')).toBe(true);
    
    const isValid = await bcrypt.compare(plainPassword, hashedPassword);
    expect(isValid).toBe(true);
    
    const isInvalid = await bcrypt.compare('wrongPassword', hashedPassword);
    expect(isInvalid).toBe(false);
  });

  test('相同密码应该产生不同的哈希', async () => {
    const password = 'SamePassword123';
    const hash1 = await bcrypt.hash(password, 12);
    const hash2 = await bcrypt.hash(password, 12);
    
    expect(hash1).not.toBe(hash2);
    
    const valid1 = await bcrypt.compare(password, hash1);
    const valid2 = await bcrypt.compare(password, hash2);
    expect(valid1).toBe(true);
    expect(valid2).toBe(true);
  });

  // 2. JWT令牌安全性测试
  log('\n🔑 JWT令牌安全性测试', colors.yellow);

  test('JWT令牌生成和验证', () => {
    const secretKey = 'test-secret-key-for-testing';
    const payload = {
      userId: '123',
      username: 'testuser',
      email: '<EMAIL>'
    };
    
    const token = jwt.sign(payload, secretKey, { expiresIn: '1h' });
    
    expect(typeof token).toBe('string');
    expect(token.split('.')).toHaveLength(3);
    
    const decoded = jwt.verify(token, secretKey);
    expect(decoded.userId).toBe(payload.userId);
    expect(decoded.username).toBe(payload.username);
    expect(decoded.email).toBe(payload.email);
  });

  test('过期令牌应该被拒绝', () => {
    const secretKey = 'test-secret-key-for-testing';
    const payload = { userId: '123' };
    
    const expiredToken = jwt.sign(payload, secretKey, { expiresIn: '-1h' });
    
    expect(() => jwt.verify(expiredToken, secretKey)).toThrow();
  });

  test('错误密钥应该被拒绝', () => {
    const secretKey = 'test-secret-key-for-testing';
    const payload = { userId: '123' };
    const token = jwt.sign(payload, secretKey);
    
    expect(() => jwt.verify(token, 'wrong-secret-key')).toThrow();
  });

  // 3. 输入验证安全性测试
  log('\n🔍 输入验证安全性测试', colors.yellow);

  test('邮箱格式验证', () => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    
    const validEmails = [
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>'
    ];
    
    validEmails.forEach(email => {
      expect(emailRegex.test(email)).toBe(true);
    });
    
    const invalidEmails = [
      'invalid-email',
      'test@',
      '@domain.com',
      'test@domain',
      ''
    ];
    
    invalidEmails.forEach(email => {
      expect(emailRegex.test(email)).toBe(false);
    });
  });

  test('XSS检测', () => {
    const xssPattern = /<script|javascript:|on\w+=/i;
    
    const xssPayloads = [
      '<script>alert("XSS")</script>',
      '<img src=x onerror=alert("XSS")>',
      'javascript:alert("XSS")',
      '<svg onload=alert("XSS")>',
      '<div onclick="malicious()">',
    ];
    
    xssPayloads.forEach(payload => {
      expect(xssPattern.test(payload)).toBe(true);
    });
    
    const safeInputs = [
      'normal text',
      '<EMAIL>',
      'Safe content without scripts',
    ];
    
    safeInputs.forEach(input => {
      expect(xssPattern.test(input)).toBe(false);
    });
  });

  test('SQL注入检测', () => {
    const sqlInjectionPayloads = [
      "'; DROP TABLE users; --",
      "admin'; DELETE FROM users; --",
      "' OR 1=1; --",
      "' UNION SELECT * FROM users; --",
    ];
    
    sqlInjectionPayloads.forEach(payload => {
      const hasSqlPatterns = 
        payload.includes("'") || 
        payload.includes(';') || 
        payload.includes('--') ||
        payload.includes('DROP') ||
        payload.includes('DELETE') ||
        payload.includes('UNION');
      
      expect(hasSqlPatterns).toBe(true);
    });
  });

  test('路径遍历检测', () => {
    const pathTraversalPattern = /\.\.[\/\\]/;
    
    const traversalPayloads = [
      '../../../etc/passwd',
      '..\\..\\windows\\system32',
      '....//....//etc/passwd',
      '../config/database.json'
    ];
    
    traversalPayloads.forEach(payload => {
      expect(pathTraversalPattern.test(payload)).toBe(true);
    });
    
    const safeFilenames = [
      'document.pdf',
      'image.jpg',
      'data/report.xlsx',
      'uploads/file.txt'
    ];
    
    safeFilenames.forEach(filename => {
      expect(pathTraversalPattern.test(filename)).toBe(false);
    });
  });

  // 4. 安全配置验证
  log('\n⚙️  安全配置验证', colors.yellow);

  test('环境变量安全设置', () => {
    const testEnvVars = {
      JWT_SECRET: 'test-jwt-secret-key-should-be-long',
      REFRESH_TOKEN_SECRET: 'test-refresh-token-secret-key',
      CRYPTO_SECRET: 'test-crypto-secret-key-32-chars-',
      NODE_ENV: 'test'
    };
    
    expect(testEnvVars.JWT_SECRET.length).toBeGreaterThan(16);
    expect(testEnvVars.REFRESH_TOKEN_SECRET.length).toBeGreaterThan(16);
    expect(testEnvVars.CRYPTO_SECRET.length).toBe(32);
    expect(['development', 'test', 'production'].includes(testEnvVars.NODE_ENV)).toBe(true);
  });

  test('CORS配置安全性', () => {
    const corsConfig = {
      origin: 'http://localhost:3000',
      credentials: true,
      optionsSuccessStatus: 200
    };
    
    expect(corsConfig.origin).not.toBe('*');
    expect(corsConfig.credentials).toBe(true);
    
    const originUrl = new URL(corsConfig.origin);
    expect(originUrl.protocol).toMatch(/^https?:$/);
    expect(originUrl.hostname).toBe('localhost');
  });

  test('速率限制配置', () => {
    const rateLimitConfig = {
      windowMs: 15 * 60 * 1000, // 15分钟
      max: 100, // 最多100次请求
      standardHeaders: true,
      legacyHeaders: false
    };
    
    expect(rateLimitConfig.windowMs).toBeGreaterThan(5 * 60 * 1000);
    expect(rateLimitConfig.windowMs).toBeLessThanOrEqual(60 * 60 * 1000);
    expect(rateLimitConfig.max).toBeGreaterThan(0);
    expect(rateLimitConfig.max).toBeLessThan(10000);
    expect(rateLimitConfig.standardHeaders).toBe(true);
    expect(rateLimitConfig.legacyHeaders).toBe(false);
  });

  // 5. 加密工具函数测试
  log('\n🔐 加密工具函数测试', colors.yellow);

  test('AES加密解密功能', () => {
    const secretKey = 'test-crypto-secret-key-32-chars-'; // 32字节密钥
    
    function encrypt(text, key) {
      const algorithm = 'aes-256-cbc';
      const iv = crypto.randomBytes(16);
      const cipher = crypto.createCipher(algorithm, key);
      
      let encrypted = cipher.update(text, 'utf8', 'hex');
      encrypted += cipher.final('hex');
      
      return {
        encrypted,
        iv: iv.toString('hex')
      };
    }
    
    function decrypt(encryptedData, key) {
      const algorithm = 'aes-256-cbc';
      const decipher = crypto.createDecipher(algorithm, key);
      
      let decrypted = decipher.update(encryptedData.encrypted, 'hex', 'utf8');
      decrypted += decipher.final('utf8');
      
      return decrypted;
    }
    
    const originalText = 'sensitive configuration data';
    const encrypted = encrypt(originalText, secretKey);
    
    expect(encrypted.encrypted).not.toBe(originalText);
    expect(encrypted.encrypted.length).toBeGreaterThan(0);
    expect(encrypted.iv.length).toBe(32);
    
    const decrypted = decrypt(encrypted, secretKey);
    expect(decrypted).toBe(originalText);
  });

  // 6. 密码策略验证
  log('\n🔒 密码策略验证', colors.yellow);

  test('密码强度检查', () => {
    function checkPasswordStrength(password) {
      const reasons = [];
      
      if (password.length < 8) {
        reasons.push('密码长度至少8位');
      }
      
      if (!/[a-z]/.test(password)) {
        reasons.push('需要包含小写字母');
      }
      
      if (!/[A-Z]/.test(password)) {
        reasons.push('需要包含大写字母');
      }
      
      if (!/[0-9]/.test(password)) {
        reasons.push('需要包含数字');
      }
      
      if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
        reasons.push('需要包含特殊字符');
      }
      
      const commonPasswords = ['password', '123456', 'qwerty', 'admin', 'root'];
      if (commonPasswords.includes(password.toLowerCase())) {
        reasons.push('不能使用常见密码');
      }
      
      return {
        strong: reasons.length === 0,
        reasons
      };
    }
    
    const weakPasswords = [
      '123',
      'password',
      '12345678',
      'abcdefgh',
      'PASSWORD'
    ];
    
    weakPasswords.forEach(password => {
      const result = checkPasswordStrength(password);
      expect(result.strong).toBe(false);
      expect(result.reasons.length).toBeGreaterThan(0);
    });
    
    const strongPasswords = [
      'MySecureP@ssw0rd!',
      'C0mplex!Password123',
      'Str0ng#Passw0rd$2025'
    ];
    
    strongPasswords.forEach(password => {
      const result = checkPasswordStrength(password);
      expect(result.strong).toBe(true);
      expect(result.reasons.length).toBe(0);
    });
  });

  // 测试结果总结
  log(`\\n📊 测试结果总结`, colors.cyan + colors.bright);
  log('====================', colors.cyan);
  log(`总测试数: ${totalTests}`, colors.blue);
  log(`通过: ${passedTests}`, colors.green);
  log(`失败: ${failedTests}`, colors.red);
  log(`成功率: ${((passedTests / totalTests) * 100).toFixed(1)}%`, 
    failedTests === 0 ? colors.green : colors.yellow);

  if (failedTests === 0) {
    log(`\\n🎉 所有安全测试通过！系统安全性良好。`, colors.green + colors.bright);
  } else {
    log(`\\n⚠️  发现 ${failedTests} 个安全问题，请及时修复。`, colors.red + colors.bright);
  }

  return failedTests === 0;
}

// 运行所有测试
runSecurityTests()
  .then(success => {
    process.exit(success ? 0 : 1);
  })
  .catch(error => {
    log(`\\n💥 测试运行出错: ${error.message}`, colors.red);
    console.error(error);
    process.exit(1);
  });