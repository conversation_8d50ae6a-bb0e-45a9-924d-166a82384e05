import { PrismaClient } from '@prisma/client';
import request from 'supertest';
import { createTestApp } from '../helpers/app';

// 声明全局类型
declare global {
  var prisma: PrismaClient;
  var redisClient: any;
}

describe('系统启动和基础连接测试', () => {
  let prisma: PrismaClient;
  let redisClient: any;
  let app: any;

  beforeAll(async () => {
    // 使用全局的数据库连接
    prisma = globalThis.prisma;
    redisClient = globalThis.redisClient;
    
    // 创建测试应用实例
    app = await createTestApp();
  });

  afterAll(async () => {
    // 清理资源
  });

  describe('数据库连接测试', () => {
    it('应该能够连接到MySQL数据库', async () => {
      expect(prisma).toBeDefined();
      
      // 尝试执行简单查询来验证连接
      const result = await prisma.$queryRaw`SELECT 1 as test`;
      expect(result).toBeDefined();
    });

    it('应该能够查询用户表', async () => {
      const users = await prisma.user.findMany({
        take: 1
      });
      // 这个测试不会失败，即使没有数据
      expect(Array.isArray(users)).toBe(true);
    });
  });

  describe('Redis连接测试', () => {
    it('应该能够连接到Redis', async () => {
      if (redisClient && redisClient.isReady) {
        // 测试基础Redis操作
        await redisClient.set('test_key', 'test_value');
        const value = await redisClient.get('test_key');
        expect(value).toBe('test_value');
        
        // 清理测试数据
        await redisClient.del('test_key');
      } else {
        console.warn('Redis 在测试环境中未可用');
        expect(true).toBe(true); // 跳过测试
      }
    });
  });

  describe('API服务启动测试', () => {
    it('应该响应健康检查请求', async () => {
      if (!app) {
        console.warn('应用实例未可用，跳过API测试');
        return;
      }

      const response = await request(app)
        .get('/api/health')
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('timestamp');
    });

    it('应该返回404对未定义的路由', async () => {
      if (!app) {
        return;
      }

      await request(app)
        .get('/non-existent-route')
        .expect(404);
    });
  });

  describe('环境配置测试', () => {
    it('应该正确加载测试环境变量', () => {
      expect(process.env['NODE_ENV']).toBe('test');
      expect(process.env['DATABASE_URL']).toContain('ops_management_test');
      expect(process.env['JWT_SECRET']).toBe('test-jwt-secret');
    });

    it('应该设置正确的端口配置', () => {
      expect(process.env['PORT']).toBe('3002');
    });
  });
});