# PDF导出功能故障排除指南

## 🔍 问题诊断

### 常见错误："访问令牌无效"

#### 可能原因：
1. **Token获取方式不一致** - 前端使用了错误的token获取方式
2. **Token已过期** - JWT token超过了有效期
3. **Token格式错误** - Authorization头格式不正确
4. **中间件认证失败** - 后端认证中间件配置问题

#### 诊断步骤：

##### 1. 检查浏览器控制台
```javascript
// 在浏览器控制台中执行，检查token存储
console.log('localStorage token:', localStorage.getItem('token'))
console.log('auth-storage:', localStorage.getItem('auth-storage'))

// 检查AuthStore
console.log('AuthStore:', window.__AUTH_STORE__?.getState?.())
```

##### 2. 检查网络请求
在浏览器开发者工具的Network标签页中：
- 查看请求头是否包含正确的Authorization头
- 检查响应状态码（401, 403, 500等）
- 查看响应内容获取具体错误信息

##### 3. 查看后端日志
在后端控制台中查看：
```
🔄 PDF导出请求: { serviceId: 'xxx', authHeader: 'Present/Missing' }
👤 当前用户信息: { hasUser: true/false, userId: 'xxx' }
```

## 🛠️ 修复方案

### 方案1：安装Chrome浏览器（Puppeteer超时问题）
```bash
# 在后端目录执行，安装Chrome供Puppeteer使用
cd backend
npx puppeteer browsers install chrome
```

### 方案2：检查Token有效性
```bash
# 在后端目录执行，查看JWT配置
cd backend
echo $JWT_SECRET
echo $JWT_EXPIRES_IN
```

### 方案3：手动测试API
```bash
# 使用curl测试API端点
curl -X GET \
  'http://localhost:3000/api/v1/services/{工单ID}/export/pdf' \
  -H 'Authorization: Bearer {your_token}' \
  -H 'Accept: application/pdf' \
  --output test.pdf
```

### 方案4：重新登录
如果token确实过期了：
1. 退出当前账户
2. 重新登录获取新token
3. 再次尝试导出PDF

### 方案5：检查权限
确保当前用户有正确的权限：
- `service:read` - 读取工单权限
- 是工单的创建人、负责人或管理员

## 🔧 代码调试

### 前端调试代码
在`ServiceDetailSideSheet.tsx`中添加更多调试信息：

```typescript
const handleExportPDF = async () => {
  // 添加详细的调试日志
  console.log('=== PDF导出调试信息 ===')
  console.log('Service ID:', service?.id)
  console.log('Ticket Number:', service?.ticketNumber)
  
  // 检查所有可能的token存储位置
  console.log('localStorage token:', localStorage.getItem('token'))
  console.log('localStorage auth-storage:', localStorage.getItem('auth-storage'))
  console.log('AuthStore:', (window as any).__AUTH_STORE__?.getState?.())
  
  // 尝试解析auth-storage
  try {
    const authStorage = localStorage.getItem('auth-storage')
    if (authStorage) {
      const parsed = JSON.parse(authStorage)
      console.log('Parsed auth-storage:', parsed)
      console.log('Token from auth-storage:', parsed.state?.token)
    }
  } catch (error) {
    console.error('Failed to parse auth-storage:', error)
  }
  
  // 继续原有的导出逻辑...
}
```

### 后端调试代码
在`service.controller.ts`的`exportServiceReportPDF`函数中：

```typescript
export const exportServiceReportPDF = async (req: Request, res: Response) => {
  console.log('=== PDF导出后端调试 ===')
  console.log('Headers:', req.headers)
  console.log('Authorization header:', req.headers.authorization)
  console.log('User from middleware:', (req as any).user)
  console.log('Service ID:', req.params.id)
  
  // 继续原有逻辑...
}
```

## 📋 检查清单

在报告问题前，请确认：

- [ ] 其他API调用是否正常工作？
- [ ] 用户是否已正确登录？
- [ ] 浏览器控制台是否有错误信息？
- [ ] 网络请求的状态码是什么？
- [ ] 后端服务是否正在运行？
- [ ] 是否有权限访问该工单？

## 🚀 快速修复

### 临时解决方案
如果问题紧急，可以尝试：

1. **刷新页面**重新获取token
2. **清除浏览器缓存**和localStorage
3. **重新登录**获取新的认证信息
4. **使用其他浏览器**测试

### 长期解决方案
1. 检查JWT配置的过期时间
2. 实现token自动刷新机制
3. 添加更完善的错误处理
4. 统一前端的认证状态管理

## 📞 获取帮助

如果以上方案都无法解决问题，请提供：

1. **浏览器控制台的完整错误信息**
2. **网络请求的详细信息**（状态码、请求头、响应）
3. **后端服务日志**
4. **用户权限和角色信息**
5. **重现问题的具体步骤**

联系方式：<EMAIL>
