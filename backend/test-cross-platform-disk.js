const { execSync } = require('child_process');

function testCrossPlatformDisk() {
  console.log('🔍 测试跨平台磁盘统计...\n');

  try {
    // 1. 检测操作系统
    const platform = process.platform;
    console.log('📱 操作系统:', platform);
    
    // 2. 获取df命令结果
    console.log('\n📊 df / 命令结果:');
    const dfOutput = execSync('df /', { encoding: 'utf8' });
    console.log(dfOutput);
    
    // 3. 解析df结果
    const lines = dfOutput.trim().split('\n');
    const rootLine = lines[lines.length - 1];
    
    if (rootLine) {
      const parts = rootLine.split(/\s+/);
      console.log('\n🔍 解析结果:');
      console.log('原始行:', rootLine);
      console.log('分割后:', parts);
      
      if (parts.length >= 5) {
        const totalBlocks = parseInt(parts[1]);
        const usedBlocks = parseInt(parts[2]);
        const availableBlocks = parseInt(parts[3]);
        const usageStr = parts[4].replace('%', '');
        
        const totalSize = totalBlocks * 1024;
        const totalUsed = usedBlocks * 1024;
        const totalAvailable = availableBlocks * 1024;
        const usagePercent = parseInt(usageStr);
        
        console.log('\n📈 计算结果:');
        console.log('总块数:', totalBlocks, '(1KB块)');
        console.log('已用块数:', usedBlocks);
        console.log('可用块数:', availableBlocks);
        console.log('使用率:', usageStr + '%');
        
        console.log('\n💾 转换为字节:');
        console.log('总大小:', (totalSize / 1024 / 1024 / 1024).toFixed(2) + 'GB');
        console.log('已用:', (totalUsed / 1024 / 1024 / 1024).toFixed(2) + 'GB');
        console.log('可用:', (totalAvailable / 1024 / 1024 / 1024).toFixed(2) + 'GB');
        console.log('使用率:', usagePercent + '%');
        
        // 4. Mac系统特殊处理
        if (platform === 'darwin') {
          console.log('\n🍎 Mac系统特殊处理:');
          try {
            const diskutilOutput = execSync('diskutil list', { encoding: 'utf8' });
            const apfsContainerMatch = diskutilOutput.match(/APFS Container Scheme.*\+(\d+\.?\d*)\s*GB/);
            
            if (apfsContainerMatch) {
              const containerSizeGB = parseFloat(apfsContainerMatch[1]);
              const containerSizeBytes = containerSizeGB * 1024 * 1024 * 1024;
              
              console.log('APFS容器大小:', containerSizeGB + 'GB');
              console.log('APFS容器字节:', containerSizeBytes);
              console.log('当前统计大小:', (totalSize / 1024 / 1024 / 1024).toFixed(2) + 'GB');
              
              if (containerSizeBytes > totalSize * 1.5) {
                console.log('✅ 检测到APFS容器，大小明显大于当前统计');
                console.log('建议使用APFS容器大小进行统计');
                
                const newTotalSize = containerSizeBytes;
                const newTotalAvailable = newTotalSize - totalUsed;
                const newUsagePercent = Math.round((totalUsed / newTotalSize) * 100);
                
                console.log('\n🔄 调整后的结果:');
                console.log('总大小:', (newTotalSize / 1024 / 1024 / 1024).toFixed(2) + 'GB');
                console.log('已用:', (totalUsed / 1024 / 1024 / 1024).toFixed(2) + 'GB');
                console.log('可用:', (newTotalAvailable / 1024 / 1024 / 1024).toFixed(2) + 'GB');
                console.log('使用率:', newUsagePercent + '%');
              } else {
                console.log('ℹ️ APFS容器大小与当前统计相近，使用当前统计');
              }
            } else {
              console.log('ℹ️ 未检测到APFS容器信息');
            }
          } catch (apfsError) {
            console.log('⚠️ APFS信息获取失败:', apfsError.message);
          }
        }
        
        // 5. 数据一致性验证
        console.log('\n✅ 数据一致性验证:');
        const expectedTotal = totalUsed + totalAvailable;
        const consistency = Math.abs(totalSize - expectedTotal) < (1024 * 1024 * 1024); // 1GB误差
        console.log('数据一致性:', consistency ? '✅ 通过' : '❌ 失败');
        console.log('总大小 vs (已用+可用):', (totalSize / 1024 / 1024 / 1024).toFixed(2) + 'GB vs ' + (expectedTotal / 1024 / 1024 / 1024).toFixed(2) + 'GB');
        
      } else {
        console.log('❌ df输出格式不符合预期');
      }
    } else {
      console.log('❌ 未找到根目录信息');
    }
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  }
}

// 运行测试
testCrossPlatformDisk();
console.log('\n🏁 测试完成');
