import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

/**
 * 修复现有工单的首次响应时间
 * 这个工具函数用于修复历史数据中缺失的 firstResponseAt 字段
 */
export async function fixExistingServiceResponseTimes(): Promise<{
  total: number
  fixed: number
  errors: number
}> {
  console.log('🔧 开始修复现有工单的首次响应时间...')
  
  let total = 0
  let fixed = 0
  let errors = 0

  try {
    // 查找所有没有首次响应时间但应该有的工单
    const servicesNeedingFix = await prisma.service.findMany({
      where: {
        firstResponseAt: null,
        OR: [
          // 有非内部评论的工单
          {
            comments: {
              some: {
                isInternal: false
              }
            }
          },
          // 状态不是 PENDING 的工单
          {
            status: {
              not: 'PENDING'
            }
          },
          // 已分配给用户的工单
          {
            assignedTo: {
              not: null
            }
          },
          // 有工作日志的工单
          {
            workLogs: {
              some: {}
            }
          }
        ]
      },
      include: {
        comments: {
          where: {
            isInternal: false
          },
          orderBy: {
            createdAt: 'asc'
          },
          take: 1
        },
        workLogs: {
          orderBy: {
            createdAt: 'asc'
          },
          take: 1
        }
      },
      orderBy: {
        createdAt: 'asc'
      }
    })

    total = servicesNeedingFix.length
    console.log(`📊 找到 ${total} 个需要修复的工单`)

    for (const service of servicesNeedingFix) {
      try {
        let firstResponseTime: Date | null = null

        // 确定首次响应时间的优先级：
        // 1. 首个非内部评论时间
        // 2. 状态变更时间（如果不是 PENDING）
        // 3. 工单分配时间（updatedAt，如果有 assignedTo）
        // 4. 首个工作日志时间

        // 1. 检查首个非内部评论
        if (service.comments.length > 0) {
          firstResponseTime = service.comments[0]?.createdAt || null
        }

        // 2. 如果状态不是 PENDING，使用更新时间
        if (!firstResponseTime && service.status !== 'PENDING') {
          firstResponseTime = service.updatedAt
        }

        // 3. 如果有分配用户且没有其他响应时间，使用更新时间
        if (!firstResponseTime && service.assignedTo) {
          firstResponseTime = service.updatedAt
        }

        // 4. 检查首个工作日志
        if (!firstResponseTime && service.workLogs.length > 0) {
          firstResponseTime = service.workLogs[0]?.createdAt || null
        }

        // 如果找到了响应时间，更新工单
        if (firstResponseTime) {
          const responseTimeMinutes = Math.floor(
            (firstResponseTime.getTime() - service.createdAt.getTime()) / (1000 * 60)
          )

          await prisma.service.update({
            where: { id: service.id },
            data: {
              firstResponseAt: firstResponseTime,
              actualResponseTime: responseTimeMinutes
            }
          })

          console.log(`✅ 修复工单 ${service.ticketNumber}: 首次响应时间 ${firstResponseTime.toISOString()}, 响应时长 ${responseTimeMinutes} 分钟`)
          fixed++
        } else {
          console.log(`⚠️  工单 ${service.ticketNumber}: 无法确定首次响应时间`)
        }

      } catch (error) {
        console.error(`❌ 修复工单 ${service.ticketNumber} 失败:`, error)
        errors++
      }
    }

    console.log(`🎉 修复完成: 总计 ${total} 个工单，成功修复 ${fixed} 个，失败 ${errors} 个`)

    return { total, fixed, errors }

  } catch (error) {
    console.error('❌ 修复过程中发生错误:', error)
    throw error
  }
}

/**
 * 检查 SLA 响应时间统计
 */
export async function checkSlaResponseTimeStats(): Promise<{
  totalServices: number
  servicesWithResponseTime: number
  servicesWithoutResponseTime: number
  averageResponseTime: number | null
}> {
  try {
    const totalServices = await prisma.service.count()

    const servicesWithResponseTime = await prisma.service.count({
      where: {
        firstResponseAt: { not: null }
      }
    })

    const servicesWithoutResponseTime = totalServices - servicesWithResponseTime

    const avgResult = await prisma.service.aggregate({
      where: {
        actualResponseTime: { not: null }
      },
      _avg: {
        actualResponseTime: true
      }
    })

    return {
      totalServices,
      servicesWithResponseTime,
      servicesWithoutResponseTime,
      averageResponseTime: avgResult._avg.actualResponseTime
    }
  } catch (error) {
    console.error('检查 SLA 统计时出错:', error)
    throw error
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  fixExistingServiceResponseTimes()
    .then(result => {
      console.log('修复结果:', result)
      return checkSlaResponseTimeStats()
    })
    .then(stats => {
      console.log('当前统计:', stats)
      process.exit(0)
    })
    .catch(error => {
      console.error('执行失败:', error)
      process.exit(1)
    })
}
