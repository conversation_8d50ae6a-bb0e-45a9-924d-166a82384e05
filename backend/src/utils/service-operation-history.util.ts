import { prisma } from '@/config/database.config'
import { ServiceOperationType } from '@prisma/client'

interface CreateOperationHistoryInput {
  serviceId: string
  type: ServiceOperationType
  description: string
  fromValue?: string | null
  toValue?: string | null
  note?: string | null
  userId: string
}

/**
 * 创建服务操作历史记录
 */
export async function createServiceOperationHistory(input: CreateOperationHistoryInput) {
  try {
    const history = await prisma.serviceOperationHistory.create({
      data: {
        serviceId: input.serviceId,
        type: input.type,
        description: input.description,
        fromValue: input.fromValue || null,
        toValue: input.toValue || null,
        note: input.note || null,
        userId: input.userId
      }
    })
    
    console.log('服务操作历史记录已创建:', {
      historyId: history.id,
      serviceId: input.serviceId,
      type: input.type,
      description: input.description
    })
    
    return history
  } catch (error) {
    console.error('创建服务操作历史记录失败:', error)
    throw error
  }
}

/**
 * 获取服务操作历史
 */
export async function getServiceOperationHistory(serviceId: string) {
  try {
    const history = await prisma.serviceOperationHistory.findMany({
      where: { serviceId },
      include: {
        user: {
          select: {
            id: true,
            username: true,
            fullName: true
          }
        }
      },
      orderBy: { createdAt: 'desc' }
    })
    
    return history
  } catch (error) {
    console.error('获取服务操作历史失败:', error)
    throw error
  }
}

/**
 * 状态变更历史记录
 */
export async function recordStatusChange(
  serviceId: string,
  fromStatus: string,
  toStatus: string,
  userId: string,
  note?: string | null
) {
  return createServiceOperationHistory({
    serviceId,
    type: 'STATUS_CHANGE',
    description: '状态变更',
    fromValue: fromStatus,
    toValue: toStatus,
    note: note || null,
    userId
  })
}

/**
 * 工单转交历史记录
 */
export async function recordTransfer(
  serviceId: string,
  fromUserId: string | null,
  toUserId: string,
  operatorUserId: string,
  note?: string | null
) {
  // 获取用户信息用于显示
  const [fromUser, toUser] = await Promise.all([
    fromUserId ? prisma.user.findUnique({
      where: { id: fromUserId },
      select: { username: true, fullName: true }
    }) : null,
    prisma.user.findUnique({
      where: { id: toUserId },
      select: { username: true, fullName: true }
    })
  ])
  
  const fromDisplayName = fromUser ? (fromUser.fullName || fromUser.username) : '未分配'
  const toDisplayName = toUser ? (toUser.fullName || toUser.username) : '未知用户'
  
  return createServiceOperationHistory({
    serviceId,
    type: 'TRANSFER',
    description: '工单转交',
    fromValue: fromDisplayName,
    toValue: toDisplayName,
    note: note || null,
    userId: operatorUserId
  })
}

/**
 * 工单创建历史记录
 */
export async function recordServiceCreation(serviceId: string, userId: string) {
  return createServiceOperationHistory({
    serviceId,
    type: 'CREATE',
    description: '创建工单',
    note: '工单已创建',
    userId
  })
}

/**
 * 工单更新历史记录
 */
export async function recordServiceUpdate(
  serviceId: string,
  userId: string,
  updateDescription: string,
  note?: string | null
) {
  return createServiceOperationHistory({
    serviceId,
    type: 'UPDATE',
    description: updateDescription,
    note: note || null,
    userId
  })
}

/**
 * 工作日志添加历史记录
 */
export async function recordWorkLogAdded(serviceId: string, userId: string, workHours: number) {
  return createServiceOperationHistory({
    serviceId,
    type: 'WORK_LOG',
    description: `添加工作日志 (${workHours}小时)`,
    userId
  })
}

/**
 * 评论添加历史记录
 */
export async function recordCommentAdded(serviceId: string, userId: string, isInternal: boolean) {
  return createServiceOperationHistory({
    serviceId,
    type: 'COMMENT',
    description: `添加${isInternal ? '内部' : ''}评论`,
    userId
  })
}

/**
 * 附件上传历史记录
 */
export async function recordAttachmentUploaded(serviceId: string, userId: string, filename: string) {
  return createServiceOperationHistory({
    serviceId,
    type: 'ATTACHMENT',
    description: `上传附件: ${filename}`,
    userId
  })
}