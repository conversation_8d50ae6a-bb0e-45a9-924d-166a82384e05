/**
 * 权限工具函数
 * 
 * 提供权限检查、权限验证、权限装饰器等功能
 */

import { Request } from 'express'
import { AuthenticatedRequest } from '@/middleware/auth.middleware'
import { ADMIN_ALL, getAllPermissions, isValidPermission } from '@/constants/permissions'

// ==================== 权限检查函数 ====================

/**
 * 检查用户是否拥有指定权限
 * @param userPermissions 用户权限列表
 * @param requiredPermissions 需要的权限列表
 * @returns 是否拥有权限
 */
export function hasPermission(
  userPermissions: string[],
  requiredPermissions: string | string[]
): boolean {
  // 如果没有用户权限，返回false
  if (!userPermissions || userPermissions.length === 0) {
    return false
  }

  // 如果拥有超级管理员权限，返回true
  if (userPermissions.includes(ADMIN_ALL)) {
    return true
  }

  // 将需要的权限转为数组
  const permissions = Array.isArray(requiredPermissions) ? requiredPermissions : [requiredPermissions]

  // 检查是否拥有所有需要的权限
  return permissions.every(permission => userPermissions.includes(permission))
}

/**
 * 检查用户是否拥有任一权限
 * @param userPermissions 用户权限列表
 * @param requiredPermissions 需要的权限列表（满足其中一个即可）
 * @returns 是否拥有权限
 */
export function hasAnyPermission(
  userPermissions: string[],
  requiredPermissions: string[]
): boolean {
  // 如果没有用户权限，返回false
  if (!userPermissions || userPermissions.length === 0) {
    return false
  }

  // 如果拥有超级管理员权限，返回true
  if (userPermissions.includes(ADMIN_ALL)) {
    return true
  }

  // 检查是否拥有任一权限
  return requiredPermissions.some(permission => userPermissions.includes(permission))
}

/**
 * 检查用户是否为超级管理员
 * @param userPermissions 用户权限列表
 * @returns 是否为超级管理员
 */
export function isSuperAdmin(userPermissions: string[]): boolean {
  return userPermissions?.includes(ADMIN_ALL) || false
}

/**
 * 检查用户是否拥有模块权限
 * @param userPermissions 用户权限列表
 * @param module 模块名称（如 'user', 'customer' 等）
 * @param action 操作类型（如 'read', 'write' 等）
 * @returns 是否拥有权限
 */
export function hasModulePermission(
  userPermissions: string[],
  module: string,
  action: string
): boolean {
  const permission = `${module}:${action}`
  return hasPermission(userPermissions, permission)
}

// ==================== 请求权限检查函数 ====================

/**
 * 从请求中获取用户权限
 * @param req Express请求对象
 * @returns 用户权限列表
 */
export function getUserPermissions(req: Request): string[] {
  const authReq = req as AuthenticatedRequest
  return authReq.user?.permissions || []
}

/**
 * 检查请求用户是否拥有权限
 * @param req Express请求对象
 * @param requiredPermissions 需要的权限
 * @returns 是否拥有权限
 */
export function checkRequestPermission(
  req: Request,
  requiredPermissions: string | string[]
): boolean {
  const userPermissions = getUserPermissions(req)
  return hasPermission(userPermissions, requiredPermissions)
}

/**
 * 检查请求用户是否拥有任一权限
 * @param req Express请求对象
 * @param requiredPermissions 需要的权限列表
 * @returns 是否拥有权限
 */
export function checkRequestAnyPermission(
  req: Request,
  requiredPermissions: string[]
): boolean {
  const userPermissions = getUserPermissions(req)
  return hasAnyPermission(userPermissions, requiredPermissions)
}

/**
 * 检查请求用户是否为资源拥有者或管理员
 * @param req Express请求对象
 * @param resourceUserId 资源拥有者ID
 * @param adminPermissions 管理员权限列表
 * @returns 是否有权限访问
 */
export function checkOwnershipOrAdmin(
  req: Request,
  resourceUserId: string,
  adminPermissions: string[] = [ADMIN_ALL]
): boolean {
  const authReq = req as AuthenticatedRequest
  const currentUserId = authReq.user?.id
  const userPermissions = getUserPermissions(req)

  // 检查是否为资源拥有者
  if (currentUserId === resourceUserId) {
    return true
  }

  // 检查是否拥有管理员权限
  return hasAnyPermission(userPermissions, adminPermissions)
}

/**
 * 检查用户是否可以访问指定客户的资源
 * @param req Express请求对象
 * @param customerId 客户ID
 * @param viewAllPermission 查看所有资源的权限
 * @returns 是否有权限访问
 */
export function checkCustomerAccess(
  req: Request,
  _customerId: string,
  viewAllPermission: string
): boolean {
  const userPermissions = getUserPermissions(req)

  // 超级管理员或拥有查看所有权限
  if (isSuperAdmin(userPermissions) || hasPermission(userPermissions, viewAllPermission)) {
    return true
  }

  // TODO: 这里可以添加基于部门、团队等的访问控制逻辑
  // 目前简化为只要有基本读取权限就可以访问
  return true
}

// ==================== 权限验证装饰器 ====================
// 注意：装饰器功能已移至 middleware/permission.middleware.ts 中的中间件形式

// ==================== 权限验证辅助函数 ====================

/**
 * 验证权限字符串格式
 * @param permission 权限字符串
 * @returns 是否为有效格式
 */
export function validatePermissionFormat(permission: string): boolean {
  // 权限格式：模块名:操作类型[:子操作]
  const pattern = /^[a-z_]+:[a-z_]+(?::[a-z_]+)*$/
  return pattern.test(permission)
}

/**
 * 验证权限数组
 * @param permissions 权限数组
 * @returns 验证结果
 */
export function validatePermissions(permissions: string[]): {
  valid: boolean
  invalidPermissions: string[]
  unknownPermissions: string[]
} {
  const invalidPermissions: string[] = []
  const unknownPermissions: string[] = []

  for (const permission of permissions) {
    // 检查格式是否有效
    if (!validatePermissionFormat(permission)) {
      invalidPermissions.push(permission)
      continue
    }

    // 检查权限是否在系统中定义（跳过超级管理员权限）
    if (permission !== ADMIN_ALL && !isValidPermission(permission)) {
      unknownPermissions.push(permission)
    }
  }

  return {
    valid: invalidPermissions.length === 0 && unknownPermissions.length === 0,
    invalidPermissions,
    unknownPermissions
  }
}

/**
 * 过滤有效权限
 * @param permissions 权限数组
 * @returns 有效权限数组
 */
export function filterValidPermissions(permissions: string[]): string[] {
  return permissions.filter(permission => {
    return validatePermissionFormat(permission) && 
           (permission === ADMIN_ALL || isValidPermission(permission))
  })
}

/**
 * 合并权限数组并去重
 * @param permissionArrays 权限数组列表
 * @returns 合并后的权限数组
 */
export function mergePermissions(...permissionArrays: string[][]): string[] {
  const allPermissions = permissionArrays.flat()
  return [...new Set(allPermissions)]
}

/**
 * 权限差集计算
 * @param allPermissions 所有权限
 * @param userPermissions 用户权限
 * @returns 用户缺少的权限
 */
export function getMissingPermissions(allPermissions: string[], userPermissions: string[]): string[] {
  // 如果用户有超级管理员权限，则不缺少任何权限
  if (userPermissions.includes(ADMIN_ALL)) {
    return []
  }

  return allPermissions.filter(permission => !userPermissions.includes(permission))
}

/**
 * 权限交集计算
 * @param permissions1 权限数组1
 * @param permissions2 权限数组2
 * @returns 交集权限
 */
export function getPermissionIntersection(permissions1: string[], permissions2: string[]): string[] {
  return permissions1.filter(permission => permissions2.includes(permission))
}

// ==================== 权限层次结构处理 ====================

/**
 * 检查权限是否隐含其他权限
 * @param permission 权限
 * @returns 隐含的权限列表
 */
export function getImpliedPermissions(permission: string): string[] {
  // 超级管理员权限隐含所有权限
  if (permission === ADMIN_ALL) {
    return getAllPermissions()
  }

  // 模块管理权限隐含该模块的所有操作权限
  if (permission.endsWith(':manage')) {
    const module = permission.split(':')[0]
    return getAllPermissions().filter(p => p.startsWith(`${module}:`))
  }

  // 写权限隐含读权限
  if (permission.endsWith(':write')) {
    const readPermission = permission.replace(':write', ':read')
    if (isValidPermission(readPermission)) {
      return [readPermission]
    }
  }

  return []
}

/**
 * 展开权限（包括隐含权限）
 * @param permissions 原始权限数组
 * @returns 展开后的权限数组
 */
export function expandPermissions(permissions: string[]): string[] {
  const expandedPermissions = new Set<string>()

  for (const permission of permissions) {
    expandedPermissions.add(permission)
    
    // 添加隐含权限
    const impliedPermissions = getImpliedPermissions(permission)
    impliedPermissions.forEach(implied => expandedPermissions.add(implied))
  }

  return Array.from(expandedPermissions)
}

// ==================== 权限匹配函数 ====================

/**
 * 权限模式匹配（支持通配符）
 * @param pattern 权限模式（如 'user:*', 'service:*:read'）
 * @param permission 要匹配的权限
 * @returns 是否匹配
 */
export function matchPermissionPattern(pattern: string, permission: string): boolean {
  // 完全匹配
  if (pattern === permission) {
    return true
  }

  // 通配符匹配
  const patternParts = pattern.split(':')
  const permissionParts = permission.split(':')

  // 长度必须一致（除非最后一个是通配符）
  if (patternParts.length !== permissionParts.length) {
    return false
  }

  // 逐个比较
  for (let i = 0; i < patternParts.length; i++) {
    if (patternParts[i] === '*') {
      continue // 通配符匹配任意值
    }
    if (patternParts[i] !== permissionParts[i]) {
      return false
    }
  }

  return true
}

/**
 * 检查用户是否匹配权限模式
 * @param userPermissions 用户权限
 * @param patterns 权限模式列表
 * @returns 是否匹配
 */
export function hasPermissionPattern(userPermissions: string[], patterns: string[]): boolean {
  // 超级管理员权限
  if (userPermissions.includes(ADMIN_ALL)) {
    return true
  }

  // 检查是否匹配任一模式
  return patterns.some(pattern => 
    userPermissions.some(permission => 
      matchPermissionPattern(pattern, permission)
    )
  )
}

// ==================== 导出所有工具函数 ====================

export default {
  // 权限检查
  hasPermission,
  hasAnyPermission,
  isSuperAdmin,
  hasModulePermission,
  
  // 请求权限检查
  getUserPermissions,
  checkRequestPermission,
  checkRequestAnyPermission,
  checkOwnershipOrAdmin,
  checkCustomerAccess,
  
  // 权限验证
  validatePermissionFormat,
  validatePermissions,
  filterValidPermissions,
  
  // 权限操作
  mergePermissions,
  getMissingPermissions,
  getPermissionIntersection,
  
  // 权限层次
  getImpliedPermissions,
  expandPermissions,
  
  // 权限匹配
  matchPermissionPattern,
  hasPermissionPattern,
}