import jwt from 'jsonwebtoken'
import { CacheService } from '@/config/redis.config'

export interface TokenPayload {
  userId: string
  username: string
  email: string
  role: string
  permissions: string[]
}

export interface TokenPair {
  accessToken: string
  refreshToken: string
  expiresIn: number
}

// 生成访问令牌
export function generateAccessToken(payload: TokenPayload): string {
  const secret = process.env['JWT_SECRET']!
  const options = {
    expiresIn: (process.env['JWT_EXPIRES_IN'] || '7d') as any,
    issuer: 'ops-management-system',
    audience: 'ops-client'
  }
  return jwt.sign(payload, secret, options)
}

// 生成刷新令牌
export function generateRefreshToken(payload: TokenPayload): string {
  const secret = process.env['REFRESH_TOKEN_SECRET']!
  const options = {
    expiresIn: (process.env['REFRESH_TOKEN_EXPIRES_IN'] || '7d') as any,
    issuer: 'ops-management-system',
    audience: 'ops-client'
  }
  return jwt.sign(payload, secret, options)
}

// 生成令牌对
export async function generateTokenPair(payload: TokenPayload): Promise<TokenPair> {
  const accessToken = generateAccessToken(payload)
  const refreshToken = generateRefreshToken(payload)
  
  // 计算过期时间（秒）
  const expiresIn = jwt.decode(accessToken) as any
  const expirationTime = expiresIn.exp - Math.floor(Date.now() / 1000)
  // console.log(`expirationTime: ${expirationTime}`)
  // 将刷新令牌存储到 Redis，设置过期时间
  const refreshTokenExpiry = 7 * 24 * 60 * 60 // 7天（秒）
  await CacheService.set(
    `refresh_token:${payload.userId}`,
    refreshToken,
    refreshTokenExpiry
  )
  
  return {
      accessToken,
      refreshToken,
      expiresIn: expirationTime,
  }
}

// 验证访问令牌
export function verifyAccessToken(token: string): TokenPayload {
  try {
    const decoded = jwt.verify(token, process.env['JWT_SECRET']!) as any
    return {
      userId: decoded.userId,
      username: decoded.username,
      email: decoded.email,
      role: decoded.role,
      permissions: decoded.permissions
    }
  } catch (error) {
    throw new Error('访问令牌无效或已过期')
  }
}

// 验证刷新令牌
export function verifyRefreshToken(token: string): TokenPayload {
  try {
    const decoded = jwt.verify(token, process.env['REFRESH_TOKEN_SECRET']!) as any
    return {
      userId: decoded.userId,
      username: decoded.username,
      email: decoded.email,
      role: decoded.role,
      permissions: decoded.permissions
    }
  } catch (error) {
    throw new Error('刷新令牌无效或已过期')
  }
}

// 刷新访问令牌
export async function refreshAccessToken(refreshToken: string): Promise<TokenPair> {
  // 验证刷新令牌
  const payload = verifyRefreshToken(refreshToken)
  
  // 检查刷新令牌是否存在于 Redis 中
  const storedRefreshToken = await CacheService.get<string>(`refresh_token:${payload.userId}`)
  
  if (!storedRefreshToken || storedRefreshToken !== refreshToken) {
    throw new Error('刷新令牌无效或已被撤销')
  }
  
  // 生成新的令牌对
  return generateTokenPair(payload)
}

// 撤销刷新令牌
export async function revokeRefreshToken(userId: string): Promise<void> {
  await CacheService.del(`refresh_token:${userId}`)
}

// 撤销所有用户的刷新令牌
export async function revokeAllUserRefreshTokens(userId: string): Promise<void> {
  await CacheService.delPattern(`refresh_token:${userId}*`)
}

// 将访问令牌加入黑名单
export async function blacklistAccessToken(token: string): Promise<void> {
  try {
    const decoded = jwt.decode(token) as any
    if (decoded && decoded.exp) {
      const expirationTime = decoded.exp - Math.floor(Date.now() / 1000)
      if (expirationTime > 0) {
        await CacheService.set(`blacklist:${token}`, true, expirationTime)
      }
    }
  } catch (error) {
    console.error('Error blacklisting token:', error)
  }
}

// 检查令牌是否在黑名单中
export async function isTokenBlacklisted(token: string): Promise<boolean> {
  return CacheService.exists(`blacklist:${token}`)
}

// 获取令牌剩余有效时间
export function getTokenRemainingTime(token: string): number {
  try {
    const decoded = jwt.decode(token) as any
    if (decoded && decoded.exp) {
      return decoded.exp - Math.floor(Date.now() / 1000)
    }
    return 0
  } catch (error) {
    return 0
  }
}

// 解码令牌（不验证签名）
export function decodeToken(token: string): any {
  return jwt.decode(token)
}