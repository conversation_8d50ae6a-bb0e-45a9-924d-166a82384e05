import { prisma } from '@/config/database.config'
import { Service, SlaTemplate } from '@prisma/client'
import { emailService } from '@/services/email.service'
import { smsService } from '@/services/sms.service'

// SLA状态枚举
export enum SlaStatus {
  WITHIN_SLA = 'WITHIN_SLA',         // 在SLA范围内
  APPROACHING_BREACH = 'APPROACHING_BREACH', // 接近违约
  RESPONSE_BREACH = 'RESPONSE_BREACH',       // 响应时间违约
  RESOLUTION_BREACH = 'RESOLUTION_BREACH',   // 解决时间违约
  FULL_BREACH = 'FULL_BREACH'               // 完全违约
}

// SLA计算结果接口
export interface SlaCalculation {
  service: Service
  slaTemplate: SlaTemplate | null
  status: SlaStatus
  responseTimeStatus: {
    elapsed: number // 已用时间（分钟）
    remaining: number // 剩余时间（分钟）
    percentage: number // 完成百分比
    isBreached: boolean
  }
  resolutionTimeStatus: {
    elapsed: number // 已用时间（小时）
    remaining: number // 剩余时间（小时）
    percentage: number // 完成百分比
    isBreached: boolean
  }
  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
}

/**
 * 计算服务工单的SLA状态
 */
export async function calculateServiceSlaStatus(serviceId: string): Promise<SlaCalculation | null> {
  try {
    // 获取服务工单和关联的SLA模板
    const service = await prisma.service.findUnique({
      where: { id: serviceId },
      include: {
        slaTemplate: true
      }
    })

    if (!service || !service.slaTemplate) {
      return null
    }

    const now = new Date()
    const createdAt = service.createdAt
    const firstResponseAt = service.firstResponseAt
    const resolvedAt = service.status === 'RESOLVED' || service.status === 'CLOSED' ? service.updatedAt : null

    // 计算响应时间状态
    const responseElapsedMinutes = firstResponseAt 
      ? Math.floor((firstResponseAt.getTime() - createdAt.getTime()) / (1000 * 60))
      : Math.floor((now.getTime() - createdAt.getTime()) / (1000 * 60))

    const responseRemainingMinutes = service.slaTemplate.responseTime - responseElapsedMinutes
    const responsePercentage = (responseElapsedMinutes / service.slaTemplate.responseTime) * 100
    const responseBreached = responseElapsedMinutes > service.slaTemplate.responseTime

    // 计算解决时间状态
    const resolutionElapsedHours = resolvedAt
      ? Math.floor((resolvedAt.getTime() - createdAt.getTime()) / (1000 * 60 * 60))
      : Math.floor((now.getTime() - createdAt.getTime()) / (1000 * 60 * 60))

    const resolutionRemainingHours = service.slaTemplate.resolutionTime - resolutionElapsedHours
    const resolutionPercentage = (resolutionElapsedHours / service.slaTemplate.resolutionTime) * 100
    const resolutionBreached = resolutionElapsedHours > service.slaTemplate.resolutionTime

    // 确定整体SLA状态
    let status: SlaStatus
    let riskLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'

    if (responseBreached && resolutionBreached) {
      status = SlaStatus.FULL_BREACH
      riskLevel = 'CRITICAL'
    } else if (responseBreached) {
      status = SlaStatus.RESPONSE_BREACH
      riskLevel = 'CRITICAL'
    } else if (resolutionBreached) {
      status = SlaStatus.RESOLUTION_BREACH
      riskLevel = 'CRITICAL'
    } else if (responsePercentage > 80 || resolutionPercentage > 80) {
      status = SlaStatus.APPROACHING_BREACH
      riskLevel = 'HIGH'
    } else if (responsePercentage > 60 || resolutionPercentage > 60) {
      status = SlaStatus.WITHIN_SLA
      riskLevel = 'MEDIUM'
    } else {
      status = SlaStatus.WITHIN_SLA
      riskLevel = 'LOW'
    }

    return {
      service,
      slaTemplate: service.slaTemplate,
      status,
      responseTimeStatus: {
        elapsed: responseElapsedMinutes,
        remaining: responseRemainingMinutes,
        percentage: Math.round(responsePercentage * 100) / 100,
        isBreached: responseBreached
      },
      resolutionTimeStatus: {
        elapsed: resolutionElapsedHours,
        remaining: resolutionRemainingHours,
        percentage: Math.round(resolutionPercentage * 100) / 100,
        isBreached: resolutionBreached
      },
      riskLevel
    }
  } catch (error) {
    console.error('计算SLA状态时出错:', error)
    return null
  }
}

/**
 * 批量计算多个服务的SLA状态
 */
export async function calculateBatchSlaStatus(serviceIds: string[]): Promise<SlaCalculation[]> {
  const results: SlaCalculation[] = []
  
  for (const serviceId of serviceIds) {
    const calculation = await calculateServiceSlaStatus(serviceId)
    if (calculation) {
      results.push(calculation)
    }
  }
  
  return results
}

/**
 * 获取所有活跃服务的SLA状态
 */
export async function getAllActiveSlaStatus(): Promise<SlaCalculation[]> {
  try {
    // 获取所有活跃的服务工单（未关闭且有SLA模板）
    const activeServices = await prisma.service.findMany({
      where: {
        status: {
          in: ['PENDING', 'IN_PROGRESS', 'WAITING_CUSTOMER']
        },
        slaTemplateId: {
          not: null
        }
      },
      select: { id: true }
    })

    const serviceIds = activeServices.map(s => s.id)
    return await calculateBatchSlaStatus(serviceIds)
  } catch (error) {
    console.error('获取活跃SLA状态时出错:', error)
    return []
  }
}

/**
 * 检查即将违约的服务
 */
export async function checkApproachingBreaches(): Promise<SlaCalculation[]> {
  const allStatuses = await getAllActiveSlaStatus()
  
  return allStatuses.filter(calc => 
    calc.status === SlaStatus.APPROACHING_BREACH ||
    calc.riskLevel === 'HIGH' ||
    calc.riskLevel === 'CRITICAL'
  )
}

/**
 * 发送SLA违约警报
 */
export async function sendSlaAlert(calculation: SlaCalculation): Promise<void> {
  try {
    // 获取服务的负责人和相关联系人
    const serviceWithDetails = await prisma.service.findUnique({
      where: { id: calculation.service.id },
      include: {
        archive: {
          include: {
            customer: {
              include: {
                contacts: true
              }
            }
          }
        },
        assignedUser: {
          select: {
            email: true,
            phone: true,
            fullName: true
          }
        }
      }
    })

    if (!serviceWithDetails) return

    // 构建警报消息
    let alertType = 'SLA监控通知'
    let urgencyLevel = 'normal'

    switch (calculation.status) {
      case SlaStatus.APPROACHING_BREACH:
        alertType = 'SLA即将违约警告'
        urgencyLevel = 'high'
        break
      case SlaStatus.RESPONSE_BREACH:
        alertType = 'SLA响应时间违约告警'
        urgencyLevel = 'critical'
        break
      case SlaStatus.RESOLUTION_BREACH:
        alertType = 'SLA解决时间违约告警'
        urgencyLevel = 'critical'
        break
      case SlaStatus.FULL_BREACH:
        alertType = 'SLA严重违约告警'
        urgencyLevel = 'critical'
        break
    }

    const message = `
工单号: ${calculation.service.ticketNumber}
工单标题: ${calculation.service.title}
客户: ${serviceWithDetails.archive.customer.name}
SLA状态: ${alertType}

响应时间状态:
- 已用时间: ${calculation.responseTimeStatus.elapsed}分钟
- 剩余时间: ${calculation.responseTimeStatus.remaining}分钟
- 完成度: ${calculation.responseTimeStatus.percentage}%
- 是否违约: ${calculation.responseTimeStatus.isBreached ? '是' : '否'}

解决时间状态:
- 已用时间: ${calculation.resolutionTimeStatus.elapsed}小时  
- 剩余时间: ${calculation.resolutionTimeStatus.remaining}小时
- 完成度: ${calculation.resolutionTimeStatus.percentage}%
- 是否违约: ${calculation.resolutionTimeStatus.isBreached ? '是' : '否'}

风险等级: ${calculation.riskLevel}
`.trim()

    // 发送给分配的工程师
    if (serviceWithDetails.assignedUser) {
      if (serviceWithDetails.assignedUser.email) {
        await emailService.sendEmail({
          to: serviceWithDetails.assignedUser.email,
          subject: `【${urgencyLevel === 'critical' ? '紧急' : '重要'}】${alertType}`,
          html: message.replace(/\n/g, '<br>')
        })
      }

      if (serviceWithDetails.assignedUser.phone) {
        await smsService.sendSms({
          phoneNumber: serviceWithDetails.assignedUser.phone,
          message: `【SLA告警】工单${calculation.service.ticketNumber}${alertType}，请及时处理。详情请查看邮件或系统。`
        })
      }
    }

    // 记录警报到数据库
    await prisma.notification.create({
      data: {
        type: 'SYSTEM',
        subject: alertType,
        content: message,
        recipient: serviceWithDetails.assignedUser?.email || '<EMAIL>',
        status: 'SENT'
      }
    })

  } catch (error) {
    console.error('发送SLA警报时出错:', error)
  }
}

/**
 * 批量检查并发送SLA警报
 */
export async function processSlaAlerts(): Promise<void> {
  try {
    console.log('🔍 开始SLA状态检查...')
    
    const approachingBreaches = await checkApproachingBreaches()
    
    if (approachingBreaches.length === 0) {
      console.log('✅ 没有发现即将违约的SLA')
      return
    }

    console.log(`⚠️  发现 ${approachingBreaches.length} 个需要警报的SLA状态`)

    // 检查最近是否已发送过相同警报（避免重复发送）
    for (const calculation of approachingBreaches) {
      const recentAlert = await prisma.notification.findFirst({
        where: {
          type: 'SYSTEM',
          subject: {
            contains: 'SLA'
          },
          content: {
            contains: calculation.service.ticketNumber
          },
          createdAt: {
            gte: new Date(Date.now() - 30 * 60 * 1000) // 30分钟内
          }
        }
      })

      if (!recentAlert || calculation.status === SlaStatus.RESPONSE_BREACH || calculation.status === SlaStatus.FULL_BREACH) {
        // 如果没有最近的警报，或者是严重违约，则发送警报
        await sendSlaAlert(calculation)
        console.log(`📨 已发送SLA警报: 工单 ${calculation.service.ticketNumber}`)
      }
    }

    console.log('✅ SLA警报处理完成')
  } catch (error) {
    console.error('处理SLA警报时出错:', error)
  }
}

/**
 * 获取SLA性能统计
 */
export async function getSlaPerformanceStats(): Promise<{
  totalActiveServices: number
  withinSla: number
  approachingBreach: number
  breached: number
  averageResponseCompliance: number
  averageResolutionCompliance: number
  riskDistribution: Record<string, number>
}> {
  try {
    const allStatuses = await getAllActiveSlaStatus()
    
    const stats = {
      totalActiveServices: allStatuses.length,
      withinSla: 0,
      approachingBreach: 0,
      breached: 0,
      averageResponseCompliance: 0,
      averageResolutionCompliance: 0,
      riskDistribution: {
        LOW: 0,
        MEDIUM: 0,
        HIGH: 0,
        CRITICAL: 0
      }
    }

    let totalResponseCompliance = 0
    let totalResolutionCompliance = 0

    for (const calc of allStatuses) {
      // 统计SLA状态分布
      switch (calc.status) {
        case SlaStatus.WITHIN_SLA:
          stats.withinSla++
          break
        case SlaStatus.APPROACHING_BREACH:
          stats.approachingBreach++
          break
        case SlaStatus.RESPONSE_BREACH:
        case SlaStatus.RESOLUTION_BREACH:
        case SlaStatus.FULL_BREACH:
          stats.breached++
          break
      }

      // 统计风险等级分布
      stats.riskDistribution[calc.riskLevel]++

      // 计算合规性
      const responseCompliance = Math.min(100, (1 - calc.responseTimeStatus.percentage / 100) * 100)
      const resolutionCompliance = Math.min(100, (1 - calc.resolutionTimeStatus.percentage / 100) * 100)
      
      totalResponseCompliance += responseCompliance
      totalResolutionCompliance += resolutionCompliance
    }

    if (allStatuses.length > 0) {
      stats.averageResponseCompliance = Math.round((totalResponseCompliance / allStatuses.length) * 100) / 100
      stats.averageResolutionCompliance = Math.round((totalResolutionCompliance / allStatuses.length) * 100) / 100
    }

    return stats
  } catch (error) {
    console.error('获取SLA性能统计时出错:', error)
    return {
      totalActiveServices: 0,
      withinSla: 0,
      approachingBreach: 0,
      breached: 0,
      averageResponseCompliance: 0,
      averageResolutionCompliance: 0,
      riskDistribution: { LOW: 0, MEDIUM: 0, HIGH: 0, CRITICAL: 0 }
    }
  }
}