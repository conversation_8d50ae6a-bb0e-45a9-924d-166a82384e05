/**
 * 自定义错误类
 */
export class AppError extends Error {
  public readonly statusCode: number
  public readonly isOperational: boolean
  public readonly errorCode: string | undefined
  public readonly details: any

  constructor(
    message: string,
    statusCode: number = 500,
    isOperational: boolean = true,
    errorCode?: string,
    details?: any
  ) {
    super(message)
    this.statusCode = statusCode
    this.isOperational = isOperational
    this.errorCode = errorCode
    this.details = details

    Error.captureStackTrace(this, this.constructor)
  }
}

/**
 * 业务逻辑错误
 */
export class BusinessError extends AppError {
  constructor(message: string, errorCode?: string, details?: any) {
    super(message, 400, true, errorCode, details)
  }
}

/**
 * 认证错误
 */
export class AuthenticationError extends AppError {
  constructor(message: string = '认证失败') {
    super(message, 401, true, 'AUTH_FAILED')
  }
}

/**
 * 授权错误
 */
export class AuthorizationError extends AppError {
  constructor(message: string = '权限不足') {
    super(message, 403, true, 'INSUFFICIENT_PERMISSIONS')
  }
}

/**
 * 资源未找到错误
 */
export class NotFoundError extends AppError {
  constructor(resource: string = '资源') {
    super(`${resource}不存在`, 404, true, 'RESOURCE_NOT_FOUND')
  }
}

/**
 * 冲突错误
 */
export class ConflictError extends AppError {
  constructor(message: string) {
    super(message, 409, true, 'RESOURCE_CONFLICT')
  }
}

/**
 * 验证错误
 */
export class ValidationError extends AppError {
  constructor(message: string, details?: any) {
    super(message, 422, true, 'VALIDATION_ERROR', details)
  }
}

/**
 * 限流错误
 */
export class RateLimitError extends AppError {
  constructor(message: string = '请求过于频繁，请稍后再试') {
    super(message, 429, true, 'RATE_LIMIT_EXCEEDED')
  }
}

/**
 * 外部服务错误
 */
export class ExternalServiceError extends AppError {
  constructor(service: string, message?: string) {
    super(
      message || `${service}服务暂时不可用`,
      503,
      true,
      'EXTERNAL_SERVICE_ERROR'
    )
  }
}

/**
 * 错误工厂类
 */
export class ErrorFactory {
  /**
   * 创建业务错误
   */
  static business(message: string, errorCode?: string, details?: any): BusinessError {
    return new BusinessError(message, errorCode, details)
  }

  /**
   * 创建认证错误
   */
  static authentication(message?: string): AuthenticationError {
    return new AuthenticationError(message)
  }

  /**
   * 创建授权错误
   */
  static authorization(message?: string): AuthorizationError {
    return new AuthorizationError(message)
  }

  /**
   * 创建未找到错误
   */
  static notFound(resource?: string): NotFoundError {
    return new NotFoundError(resource)
  }

  /**
   * 创建冲突错误
   */
  static conflict(message: string): ConflictError {
    return new ConflictError(message)
  }

  /**
   * 创建验证错误
   */
  static validation(message: string, details?: any): ValidationError {
    return new ValidationError(message, details)
  }

  /**
   * 创建限流错误
   */
  static rateLimit(message?: string): RateLimitError {
    return new RateLimitError(message)
  }

  /**
   * 创建外部服务错误
   */
  static externalService(service: string, message?: string): ExternalServiceError {
    return new ExternalServiceError(service, message)
  }
}

/**
 * 错误响应格式化
 */
export class ErrorResponse {
  static format(error: AppError | Error, includeStack: boolean = false) {
    if (error instanceof AppError) {
      return {
        success: false,
        error: {
          message: error.message,
          code: error.errorCode,
          statusCode: error.statusCode,
          details: error.details,
          ...(includeStack && { stack: error.stack })
        }
      }
    }

    // 非自定义错误
    return {
      success: false,
      error: {
        message: '服务器内部错误',
        code: 'INTERNAL_SERVER_ERROR',
        statusCode: 500,
        ...(includeStack && { stack: error.stack })
      }
    }
  }
}

/**
 * 异步错误捕获包装器
 */
export function asyncHandler<T extends any[], R>(
  fn: (...args: T) => Promise<R>
) {
  return (...args: T): Promise<R> => {
    return Promise.resolve(fn(...args)).catch(args[args.length - 1])
  }
}

/**
 * 常用的错误消息
 */
export const ErrorMessages = {
  // 通用
  INTERNAL_ERROR: '服务器内部错误',
  INVALID_REQUEST: '无效的请求',
  UNAUTHORIZED: '未授权访问',
  FORBIDDEN: '权限不足',
  NOT_FOUND: '资源不存在',
  CONFLICT: '资源冲突',
  
  // 认证相关
  INVALID_CREDENTIALS: '用户名或密码错误',
  TOKEN_EXPIRED: '令牌已过期',
  TOKEN_INVALID: '无效的令牌',
  ACCOUNT_LOCKED: '账户已被锁定',
  
  // 业务相关
  CUSTOMER_NOT_FOUND: '客户不存在',
  SERVICE_NOT_FOUND: '服务工单不存在',
  ARCHIVE_NOT_FOUND: '项目档案不存在',
  CONFIG_NOT_FOUND: '配置不存在',
  SLA_NOT_FOUND: 'SLA模板不存在',
  
  // 操作相关
  OPERATION_NOT_ALLOWED: '不允许的操作',
  STATUS_TRANSITION_INVALID: '无效的状态转换',
  INSUFFICIENT_PERMISSIONS: '权限不足',
  RESOURCE_IN_USE: '资源正在使用中，无法删除',
  
  // 文件相关
  FILE_TOO_LARGE: '文件过大',
  INVALID_FILE_TYPE: '不支持的文件类型',
  FILE_UPLOAD_FAILED: '文件上传失败',
  FILE_NOT_FOUND: '文件不存在',
  
  // 验证相关
  VALIDATION_FAILED: '数据验证失败',
  REQUIRED_FIELD_MISSING: '必填字段缺失',
  INVALID_FORMAT: '格式不正确',
  VALUE_OUT_OF_RANGE: '值超出允许范围'
} as const

/**
 * 错误分类
 */
export const ErrorCategories = {
  AUTHENTICATION: 'authentication',
  AUTHORIZATION: 'authorization',
  VALIDATION: 'validation',
  BUSINESS: 'business',
  SYSTEM: 'system',
  EXTERNAL: 'external'
} as const

/**
 * 错误严重级别
 */
export const ErrorSeverity = {
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high',
  CRITICAL: 'critical'
} as const