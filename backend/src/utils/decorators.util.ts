/**
 * 装饰器工具函数
 * 
 * 提供权限验证、日志记录等装饰器
 */

import { Request, Response, NextFunction } from 'express'
import { AuthenticatedRequest } from '@/middleware/auth.middleware'
import { hasPermission, hasAnyPermission, getUserPermissions, isSuperAdmin } from '@/utils/permission.util'
import { ForbiddenError, UnauthorizedError } from '@/middleware/error.middleware'

// ==================== 权限验证装饰器 ====================

/**
 * 权限验证装饰器
 * 检查用户是否拥有指定的所有权限
 */
export function RequirePermissions(...permissions: string[]) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value

    descriptor.value = async function (req: Request, res: Response, next: NextFunction) {
      try {
        const authReq = req as AuthenticatedRequest

        if (!authReq.user) {
          throw new UnauthorizedError('用户未认证')
        }

        const userPermissions = getUserPermissions(req)
        
        if (!hasPermission(userPermissions, permissions)) {
          throw new ForbiddenError(`需要权限: ${permissions.join(', ')}`)
        }

        return await method.apply(this, arguments)
      } catch (error) {
        next(error)
      }
    }

    return descriptor
  }
}

/**
 * 任一权限验证装饰器
 * 检查用户是否拥有指定权限中的任意一个
 */
export function RequireAnyPermission(...permissions: string[]) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value

    descriptor.value = async function (req: Request, res: Response, next: NextFunction) {
      try {
        const authReq = req as AuthenticatedRequest

        if (!authReq.user) {
          throw new UnauthorizedError('用户未认证')
        }

        const userPermissions = getUserPermissions(req)
        
        if (!hasAnyPermission(userPermissions, permissions)) {
          throw new ForbiddenError(`需要以下权限之一: ${permissions.join(', ')}`)
        }

        return await method.apply(this, arguments)
      } catch (error) {
        next(error)
      }
    }

    return descriptor
  }
}

/**
 * 超级管理员权限装饰器
 * 只有超级管理员才能访问
 */
export function RequireSuperAdmin() {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value

    descriptor.value = async function (req: Request, res: Response, next: NextFunction) {
      try {
        const authReq = req as AuthenticatedRequest

        if (!authReq.user) {
          throw new UnauthorizedError('用户未认证')
        }

        const userPermissions = getUserPermissions(req)
        
        if (!isSuperAdmin(userPermissions)) {
          throw new ForbiddenError('需要超级管理员权限')
        }

        return await method.apply(this, arguments)
      } catch (error) {
        next(error)
      }
    }

    return descriptor
  }
}

/**
 * 资源所有者权限装饰器
 * 只有资源所有者或管理员才能访问
 */
export function RequireOwnership(
  getUserId: (req: Request) => string,
  adminPermissions: string[] = ['admin:all']
) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value

    descriptor.value = async function (req: Request, res: Response, next: NextFunction) {
      try {
        const authReq = req as AuthenticatedRequest

        if (!authReq.user) {
          throw new UnauthorizedError('用户未认证')
        }

        const currentUserId = authReq.user.id
        const targetUserId = getUserId(req)
        const userPermissions = getUserPermissions(req)

        // 检查是否为资源拥有者
        if (currentUserId === targetUserId) {
          return await method.apply(this, arguments)
        }

        // 检查是否拥有管理员权限
        if (hasAnyPermission(userPermissions, adminPermissions)) {
          return await method.apply(this, arguments)
        }

        throw new ForbiddenError('只能访问自己的资源或需要管理员权限')
      } catch (error) {
        next(error)
      }
    }

    return descriptor
  }
}

/**
 * 条件权限装饰器
 * 根据条件动态检查权限
 */
export function RequireConditionalPermission(
  condition: (req: AuthenticatedRequest) => boolean | Promise<boolean>,
  permissions: string[]
) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value

    descriptor.value = async function (req: Request, res: Response, next: NextFunction) {
      try {
        const authReq = req as AuthenticatedRequest

        if (!authReq.user) {
          throw new UnauthorizedError('用户未认证')
        }

        const shouldCheck = await Promise.resolve(condition(authReq))
        
        if (shouldCheck) {
          const userPermissions = getUserPermissions(req)
          
          if (!hasPermission(userPermissions, permissions)) {
            throw new ForbiddenError(`需要权限: ${permissions.join(', ')}`)
          }
        }

        return await method.apply(this, arguments)
      } catch (error) {
        next(error)
      }
    }

    return descriptor
  }
}

/**
 * 角色验证装饰器
 * 检查用户是否拥有指定角色
 */
export function RequireRoles(...roles: string[]) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value

    descriptor.value = async function (req: Request, res: Response, next: NextFunction) {
      try {
        const authReq = req as AuthenticatedRequest

        if (!authReq.user) {
          throw new UnauthorizedError('用户未认证')
        }

        const userRoles = authReq.user.roles || []
        const hasRole = roles.some(role => 
          userRoles.includes(role) || userRoles.includes('admin')
        )

        if (!hasRole) {
          throw new ForbiddenError(`需要角色: ${roles.join(', ')}`)
        }

        return await method.apply(this, arguments)
      } catch (error) {
        next(error)
      }
    }

    return descriptor
  }
}

// ==================== 日志记录装饰器 ====================

/**
 * API访问日志装饰器
 * 记录API的访问情况
 */
export function LogApiAccess(action: string, resource?: string) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value

    descriptor.value = async function (req: Request, res: Response, next: NextFunction) {
      const startTime = Date.now()
      const authReq = req as AuthenticatedRequest
      
      try {
        console.log(`🔍 API访问: ${action} - 用户: ${authReq.user?.username || '未认证'} - 资源: ${resource || '未指定'}`)
        
        const result = await method.apply(this, arguments)
        
        const endTime = Date.now()
        const duration = endTime - startTime
        
        console.log(`✅ API完成: ${action} - 耗时: ${duration}ms`)
        
        return result
      } catch (error) {
        const endTime = Date.now()
        const duration = endTime - startTime
        
        console.error(`❌ API失败: ${action} - 耗时: ${duration}ms - 错误: ${error}`)
        
        throw error
      }
    }

    return descriptor
  }
}

/**
 * 性能监控装饰器
 * 监控方法的执行性能
 */
export function MonitorPerformance(threshold: number = 1000) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value

    descriptor.value = async function (...args: any[]) {
      const startTime = Date.now()
      const methodName = `${target.constructor.name}.${propertyName}`
      
      try {
        const result = await method.apply(this, args)
        
        const endTime = Date.now()
        const duration = endTime - startTime
        
        if (duration > threshold) {
          console.warn(`⚠️ 性能警告: ${methodName} 执行耗时 ${duration}ms (阈值: ${threshold}ms)`)
        }
        
        return result
      } catch (error) {
        const endTime = Date.now()
        const duration = endTime - startTime
        
        console.error(`❌ 方法执行失败: ${methodName} - 耗时: ${duration}ms`)
        
        throw error
      }
    }

    return descriptor
  }
}

// ==================== 缓存装饰器 ====================

/**
 * 缓存装饰器
 * 缓存方法的返回结果
 */
export function Cacheable(ttl: number = 300, keyGenerator?: (args: any[]) => string) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value
    const cache = new Map<string, { data: any; expiry: number }>()

    descriptor.value = async function (...args: any[]) {
      const cacheKey = keyGenerator ? keyGenerator(args) : JSON.stringify(args)
      const now = Date.now()
      
      // 检查缓存
      const cached = cache.get(cacheKey)
      if (cached && now < cached.expiry) {
        console.log(`🎯 缓存命中: ${target.constructor.name}.${propertyName}`)
        return cached.data
      }
      
      // 执行方法
      const result = await method.apply(this, args)
      
      // 存储到缓存
      cache.set(cacheKey, {
        data: result,
        expiry: now + (ttl * 1000)
      })
      
      console.log(`💾 缓存存储: ${target.constructor.name}.${propertyName}`)
      
      return result
    }

    return descriptor
  }
}

// ==================== 参数验证装饰器 ====================

/**
 * 参数验证装饰器
 * 验证请求参数
 */
export function ValidateParams(validator: (req: Request) => void) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value

    descriptor.value = async function (req: Request, res: Response, next: NextFunction) {
      try {
        validator(req)
        return await method.apply(this, arguments)
      } catch (error) {
        next(error)
      }
    }

    return descriptor
  }
}

/**
 * 限流装饰器
 * 限制方法的调用频率
 */
export function RateLimit(windowMs: number, maxRequests: number) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value
    const requestCounts = new Map<string, { count: number; resetTime: number }>()

    descriptor.value = async function (req: Request, res: Response, next: NextFunction) {
      try {
        const authReq = req as AuthenticatedRequest
        const userId = authReq.user?.id || req.ip
        const now = Date.now()
        
        let userRecord = requestCounts.get(userId)
        
        if (!userRecord || now > userRecord.resetTime) {
          userRecord = { count: 0, resetTime: now + windowMs }
          requestCounts.set(userId, userRecord)
        }
        
        if (userRecord.count >= maxRequests) {
          return res.status(429).json({
            success: false,
            message: '请求过于频繁，请稍后再试',
            error: 'RATE_LIMIT_EXCEEDED'
          })
        }
        
        userRecord.count++
        
        return await method.apply(this, arguments)
      } catch (error) {
        next(error)
      }
    }

    return descriptor
  }
}

// ==================== 导出所有装饰器 ====================

export default {
  // 权限验证
  RequirePermissions,
  RequireAnyPermission,
  RequireSuperAdmin,
  RequireOwnership,
  RequireConditionalPermission,
  RequireRoles,
  
  // 日志记录
  LogApiAccess,
  MonitorPerformance,
  
  // 缓存
  Cacheable,
  
  // 参数验证
  ValidateParams,
  RateLimit
}