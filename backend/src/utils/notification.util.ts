import nodemailer from 'nodemailer'
// import SMSClient from '@alicloud/sms-sdk' // 需要安装: npm install @alicloud/sms-sdk

export interface EmailConfig {
  to: string
  subject: string
  content: string
  isHtml?: boolean
  from?: string
}

export interface SmsConfig {
  phone: string
  message: string
  templateCode?: string
  templateParams?: Record<string, any>
}

export interface NotificationResult {
  success: boolean
  message: string
  details?: any
}

export class NotificationUtil {
  /**
   * 发送邮件
   */
  static async sendEmail(
    emailConfig: EmailConfig,
    systemEmailConfig: Record<string, any>
  ): Promise<NotificationResult> {
    try {
      // 检查邮件功能是否启用
      if (!systemEmailConfig.EMAIL_ENABLED) {
        return {
          success: false,
          message: '邮件发送功能未启用'
        }
      }

      // 验证必需的配置
      const requiredFields = ['SMTP_HOST', 'SMTP_PORT', 'SMTP_USER', 'SMTP_PASS', 'SENDER_EMAIL']
      for (const field of requiredFields) {
        if (!systemEmailConfig[field]) {
          return {
            success: false,
            message: `邮件配置不完整，缺少 ${field}`
          }
        }
      }

      // 创建邮件传输器
      const transporter = nodemailer.createTransporter({
        host: systemEmailConfig.SMTP_HOST,
        port: systemEmailConfig.SMTP_PORT,
        secure: systemEmailConfig.SMTP_SECURE, // true for 465, false for other ports
        auth: {
          user: systemEmailConfig.SMTP_USER,
          pass: systemEmailConfig.SMTP_PASS
        },
        // 允许自签名证书
        tls: {
          rejectUnauthorized: false
        }
      })

      // 验证SMTP连接
      await transporter.verify()

      // 发送邮件
      const info = await transporter.sendMail({
        from: emailConfig.from || `"${systemEmailConfig.SENDER_NAME}" <${systemEmailConfig.SENDER_EMAIL}>`,
        to: emailConfig.to,
        subject: emailConfig.subject,
        [emailConfig.isHtml ? 'html' : 'text']: emailConfig.content
      })

      return {
        success: true,
        message: '邮件发送成功',
        details: {
          messageId: info.messageId,
          accepted: info.accepted,
          rejected: info.rejected
        }
      }
    } catch (error) {
      console.error('Send email error:', error)
      
      let errorMessage = '邮件发送失败'
      if (error instanceof Error) {
        if (error.message.includes('Invalid login')) {
          errorMessage = 'SMTP认证失败，请检查用户名和密码'
        } else if (error.message.includes('ENOTFOUND') || error.message.includes('ECONNREFUSED')) {
          errorMessage = '无法连接到SMTP服务器，请检查主机地址和端口'
        } else if (error.message.includes('self signed certificate')) {
          errorMessage = '证书验证失败，请检查SSL配置'
        } else {
          errorMessage = `邮件发送失败: ${error.message}`
        }
      }

      return {
        success: false,
        message: errorMessage,
        details: error instanceof Error ? error.message : error
      }
    }
  }

  /**
   * 发送短信
   */
  static async sendSms(
    smsConfig: SmsConfig,
    systemSmsConfig: Record<string, any>
  ): Promise<NotificationResult> {
    try {
      // 检查短信功能是否启用
      if (!systemSmsConfig.SMS_ENABLED) {
        return {
          success: false,
          message: '短信发送功能未启用'
        }
      }

      // 验证必需的配置
      const requiredFields = ['ALI_SMS_ACCESS_KEY_ID', 'ALI_SMS_ACCESS_KEY_SECRET', 'ALI_SMS_SIGN_NAME']
      for (const field of requiredFields) {
        if (!systemSmsConfig[field]) {
          return {
            success: false,
            message: `短信配置不完整，缺少 ${field}`
          }
        }
      }

      // TODO: 实现阿里云短信发送逻辑
      // 需要安装依赖: npm install @alicloud/sms-sdk
      // 当前为模拟实现
      console.log('发送短信配置:', {
        phone: smsConfig.phone,
        message: smsConfig.message,
        templateCode: smsConfig.templateCode,
        signName: systemSmsConfig.ALI_SMS_SIGN_NAME
      })
      
      // 模拟发送结果
      const result = {
        Code: 'OK',
        Message: '发送成功',
        RequestId: 'mock-request-id',
        BizId: 'mock-biz-id'
      }

      if (result.Code === 'OK') {
        return {
          success: true,
          message: '短信发送成功',
          details: {
            requestId: result.RequestId,
            bizId: result.BizId
          }
        }
      } else {
        return {
          success: false,
          message: `短信发送失败: ${result.Message || result.Code}`,
          details: result
        }
      }
    } catch (error) {
      console.error('Send SMS error:', error)
      
      let errorMessage = '短信发送失败'
      if (error instanceof Error) {
        if (error.message.includes('InvalidAccessKeyId')) {
          errorMessage = '阿里云AccessKey验证失败'
        } else if (error.message.includes('SignatureDoesNotMatch')) {
          errorMessage = '阿里云签名验证失败，请检查AccessKeySecret'
        } else if (error.message.includes('InvalidTemplate')) {
          errorMessage = '短信模板不存在或未审核通过'
        } else if (error.message.includes('InvalidSignName')) {
          errorMessage = '短信签名不存在或未审核通过'
        } else {
          errorMessage = `短信发送失败: ${error.message}`
        }
      }

      return {
        success: false,
        message: errorMessage,
        details: error instanceof Error ? error.message : error
      }
    }
  }

  /**
   * 发送系统通知邮件（使用系统模板）
   */
  static async sendSystemEmail(
    to: string,
    templateType: string,
    variables: Record<string, any>,
    systemConfig: Record<string, any>
  ): Promise<NotificationResult> {
    // 这里可以根据模板类型生成不同的邮件内容
    const templates: Record<string, { subject: string; content: string }> = {
      'service_created': {
        subject: '新服务工单通知 - {{ticketNumber}}',
        content: `
        <h2>新服务工单创建通知</h2>
        <p>尊敬的用户，您有一个新的服务工单需要处理：</p>
        <ul>
          <li>工单号：{{ticketNumber}}</li>
          <li>标题：{{title}}</li>
          <li>优先级：{{priority}}</li>
          <li>创建时间：{{createdAt}}</li>
        </ul>
        <p>请及时登录系统处理。</p>
        `
      },
      'service_assigned': {
        subject: '服务工单分配通知 - {{ticketNumber}}',
        content: `
        <h2>服务工单分配通知</h2>
        <p>您被分配了一个新的服务工单：</p>
        <ul>
          <li>工单号：{{ticketNumber}}</li>
          <li>标题：{{title}}</li>
          <li>优先级：{{priority}}</li>
          <li>客户：{{customerName}}</li>
        </ul>
        <p>请及时处理并更新工单状态。</p>
        `
      },
      'config_test': {
        subject: '系统配置测试邮件',
        content: `
        <h2>系统配置测试</h2>
        <p>这是一封系统配置测试邮件，如果您收到此邮件，说明邮件配置正常。</p>
        <p>发送时间：{{timestamp}}</p>
        `
      }
    }

    const template = templates[templateType]
    if (!template) {
      return {
        success: false,
        message: `未找到邮件模板: ${templateType}`
      }
    }

    // 替换模板变量
    let subject = template.subject
    let content = template.content

    for (const [key, value] of Object.entries(variables)) {
      const regex = new RegExp(`{{${key}}}`, 'g')
      subject = subject.replace(regex, String(value))
      content = content.replace(regex, String(value))
    }

    return this.sendEmail({
      to,
      subject,
      content,
      isHtml: true
    }, systemConfig)
  }

  /**
   * 发送系统通知短信
   */
  static async sendSystemSms(
    phone: string,
    templateType: string,
    variables: Record<string, any>,
    systemConfig: Record<string, any>
  ): Promise<NotificationResult> {
    // 短信模板映射
    const templateMapping: Record<string, string> = {
      'service_created': 'SMS_SERVICE_CREATED',
      'service_assigned': 'SMS_SERVICE_ASSIGNED',
      'config_test': 'SMS_CONFIG_TEST'
    }

    const templateCode = templateMapping[templateType]
    if (!templateCode) {
      return {
        success: false,
        message: `未找到短信模板: ${templateType}`
      }
    }

    return this.sendSms({
      phone,
      message: '', // 阿里云短信使用模板，不需要直接的消息内容
      templateCode,
      templateParams: variables
    }, systemConfig)
  }
}