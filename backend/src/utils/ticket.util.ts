/**
 * 工单工具函数
 */

import { prisma } from '@/config/database.config'

/**
 * 生成工单号
 * 格式: TK202412080001
 */
export async function generateTicketNumber(): Promise<string> {
  const now = new Date()
  const year = now.getFullYear()
  const month = String(now.getMonth() + 1).padStart(2, '0')
  const day = String(now.getDate()).padStart(2, '0')
  const datePrefix = `TK${year}${month}${day}`

  let ticketNumber: string
  let attempts = 0
  const maxAttempts = 100

  do {
    const randomSuffix = String(Math.floor(Math.random() * 10000)).padStart(4, '0')
    ticketNumber = `${datePrefix}${randomSuffix}`
    
    // 检查工单号是否已存在
    const existing = await prisma.service.findUnique({
      where: { ticketNumber }
    })

    if (!existing) {
      break
    }

    attempts++
    if (attempts >= maxAttempts) {
      throw new Error('无法生成唯一的工单号')
    }
  } while (true)

  return ticketNumber
}

/**
 * 验证工单号格式
 */
export function validateTicketNumber(ticketNumber: string): boolean {
  // 格式: TK202412080001 (TK + 8位日期 + 4位随机数)
  const pattern = /^TK\d{8}\d{4}$/
  return pattern.test(ticketNumber)
}

/**
 * 解析工单号中的日期
 */
export function parseTicketDate(ticketNumber: string): Date | null {
  if (!validateTicketNumber(ticketNumber)) {
    return null
  }

  const dateStr = ticketNumber.substring(2, 10) // 移除TK前缀，获取日期部分
  const year = parseInt(dateStr.substring(0, 4))
  const month = parseInt(dateStr.substring(4, 6)) - 1 // JavaScript月份从0开始
  const day = parseInt(dateStr.substring(6, 8))

  return new Date(year, month, day)
}