import { prisma } from '@/config/database.config'

export interface UserProfileData {
  userId: string
  loginPatterns: {
    preferredHours: number[]
    weekdayActivity: number[]
    avgSessionDuration: number
    loginFrequency: number
  }
  operationPatterns: {
    topOperations: Array<{ action: string, count: number }>
    operationFrequency: number
    peakOperationHours: number[]
  }
  featureUsage: {
    topFeatures: Array<{ feature: string, usage: number }>
    diversityScore: number
    specialization: string[]
  }
  riskIndicators: {
    unusualTimeActivity: number
    operationSpikes: number
    locationAnomalies: number
    behaviorChanges: number
  }
}

export interface PredictiveInsight {
  type: 'risk' | 'churn' | 'engagement' | 'productivity'
  userId: string
  prediction: string
  confidence: number
  reasoning: string[]
  recommendations: string[]
  validUntil: Date
}

export class AIAnalyticsUtil {
  
  /**
   * 生成用户画像
   */
  static async generateUserProfile(userId: string, days: number = 90): Promise<UserProfileData> {
    const endDate = new Date()
    const startDate = new Date(endDate.getTime() - days * 24 * 60 * 60 * 1000)

    // 获取用户会话数据
    const sessions = await prisma.userSession.findMany({
      where: {
        userId,
        loginTime: {
          gte: startDate,
          lte: endDate
        }
      },
      orderBy: { loginTime: 'asc' }
    })

    // 获取操作日志
    const operations = await prisma.operationLog.findMany({
      where: {
        userId,
        createdAt: {
          gte: startDate,
          lte: endDate
        }
      },
      orderBy: { createdAt: 'asc' }
    })

    // 获取功能使用数据
    const featureUsage = await prisma.userFeatureUsage.findMany({
      where: {
        userId,
        date: {
          gte: startDate,
          lte: endDate
        }
      }
    })

    // 分析登录模式
    const loginPatterns = this.analyzeLoginPatterns(sessions)
    
    // 分析操作模式
    const operationPatterns = this.analyzeOperationPatterns(operations)
    
    // 分析功能使用
    const featureUsageAnalysis = this.analyzeFeatureUsage(featureUsage)
    
    // 计算风险指标
    const riskIndicators = await this.calculateRiskIndicators(userId, sessions, operations, days)

    return {
      userId,
      loginPatterns,
      operationPatterns,
      featureUsage: featureUsageAnalysis,
      riskIndicators
    }
  }

  /**
   * 预测用户行为
   */
  static async predictUserBehavior(userId: string): Promise<PredictiveInsight[]> {
    const profile = await this.generateUserProfile(userId)
    const insights: PredictiveInsight[] = []

    // 风险预测
    const riskInsight = this.predictRisk(profile)
    if (riskInsight) insights.push(riskInsight)

    // 流失预测
    const churnInsight = this.predictChurn(profile)
    if (churnInsight) insights.push(churnInsight)

    // 参与度预测
    const engagementInsight = this.predictEngagement(profile)
    if (engagementInsight) insights.push(engagementInsight)

    // 生产力预测
    const productivityInsight = this.predictProductivity(profile)
    if (productivityInsight) insights.push(productivityInsight)

    return insights
  }

  /**
   * 检测异常行为模式
   */
  static async detectAnomalousBehavior(userId: string): Promise<Array<{
    type: string
    description: string
    severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
    confidence: number
    evidence: any
    suggestions: string[]
  }>> {
    const profile = await this.generateUserProfile(userId)
    const anomalies: Array<any> = []

    // 检测登录时间异常
    const timeAnomalies = this.detectTimeAnomalies(profile)
    anomalies.push(...timeAnomalies)

    // 检测操作频率异常
    const operationAnomalies = this.detectOperationAnomalies(profile)
    anomalies.push(...operationAnomalies)

    // 检测功能使用异常
    const featureAnomalies = this.detectFeatureAnomalies(profile)
    anomalies.push(...featureAnomalies)

    // 检测综合行为异常
    const behaviorAnomalies = this.detectBehaviorAnomalies(profile)
    anomalies.push(...behaviorAnomalies)

    return anomalies
  }

  /**
   * 生成智能推荐
   */
  static async generateRecommendations(userId: string): Promise<Array<{
    category: string
    title: string
    description: string
    priority: 'LOW' | 'MEDIUM' | 'HIGH'
    actionType: 'TRAINING' | 'RESTRICTION' | 'OPTIMIZATION' | 'ALERT'
    details: any
  }>> {
    const profile = await this.generateUserProfile(userId)
    const insights = await this.predictUserBehavior(userId)
    const anomalies = await this.detectAnomalousBehavior(userId)

    const recommendations: Array<any> = []

    // 基于用户画像的推荐
    recommendations.push(...this.generateProfileBasedRecommendations(profile))

    // 基于预测的推荐
    recommendations.push(...this.generateInsightBasedRecommendations(insights))

    // 基于异常的推荐
    recommendations.push(...this.generateAnomalyBasedRecommendations(anomalies))

    return recommendations.sort((a, b) => {
      const priorityOrder = { HIGH: 3, MEDIUM: 2, LOW: 1 }
      return priorityOrder[b.priority] - priorityOrder[a.priority]
    })
  }

  /**
   * 计算用户相似度
   */
  static async calculateUserSimilarity(userId1: string, userId2: string): Promise<{
    similarityScore: number
    commonPatterns: string[]
    differences: string[]
    recommendations: string[]
  }> {
    const [profile1, profile2] = await Promise.all([
      this.generateUserProfile(userId1),
      this.generateUserProfile(userId2)
    ])

    // 计算登录模式相似度
    const loginSimilarity = this.calculateLoginPatternSimilarity(
      profile1.loginPatterns, 
      profile2.loginPatterns
    )

    // 计算操作模式相似度
    const operationSimilarity = this.calculateOperationPatternSimilarity(
      profile1.operationPatterns,
      profile2.operationPatterns
    )

    // 计算功能使用相似度
    const featureSimilarity = this.calculateFeatureUsageSimilarity(
      profile1.featureUsage,
      profile2.featureUsage
    )

    // 综合相似度
    const similarityScore = (loginSimilarity + operationSimilarity + featureSimilarity) / 3

    // 找出共同模式
    const commonPatterns = this.findCommonPatterns(profile1, profile2)
    
    // 找出差异
    const differences = this.findDifferences(profile1, profile2)

    // 生成推荐
    const recommendations = this.generateSimilarityRecommendations(
      profile1, profile2, commonPatterns, differences
    )

    return {
      similarityScore,
      commonPatterns,
      differences,
      recommendations
    }
  }

  // ==================== 私有分析方法 ====================

  /**
   * 分析登录模式
   */
  private static analyzeLoginPatterns(sessions: any[]): any {
    if (sessions.length === 0) {
      return {
        preferredHours: [],
        weekdayActivity: [0, 0, 0, 0, 0, 0, 0],
        avgSessionDuration: 0,
        loginFrequency: 0
      }
    }

    // 分析登录时间分布
    const hourCounts: { [hour: number]: number } = {}
    const weekdayCounts = [0, 0, 0, 0, 0, 0, 0] // 周日到周六
    let totalDuration = 0
    let durationCount = 0

    sessions.forEach(session => {
      const loginTime = new Date(session.loginTime)
      const hour = loginTime.getHours()
      const weekday = loginTime.getDay()

      hourCounts[hour] = (hourCounts[hour] || 0) + 1
      weekdayCounts[weekday]++

      if (session.durationMinutes) {
        totalDuration += session.durationMinutes
        durationCount++
      }
    })

    // 找出偏好时间（登录次数最多的3个小时）
    const preferredHours = Object.entries(hourCounts)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 3)
      .map(([hour]) => parseInt(hour))

    // 计算平均会话时长
    const avgSessionDuration = durationCount > 0 ? totalDuration / durationCount : 0

    // 计算登录频率（每天平均登录次数）
    const daySpan = (sessions[sessions.length - 1].loginTime.getTime() - sessions[0].loginTime.getTime()) 
                   / (1000 * 60 * 60 * 24)
    const loginFrequency = sessions.length / Math.max(daySpan, 1)

    return {
      preferredHours,
      weekdayActivity: weekdayCounts,
      avgSessionDuration,
      loginFrequency
    }
  }

  /**
   * 分析操作模式
   */
  private static analyzeOperationPatterns(operations: any[]): any {
    if (operations.length === 0) {
      return {
        topOperations: [],
        operationFrequency: 0,
        peakOperationHours: []
      }
    }

    // 统计操作类型
    const operationCounts: { [action: string]: number } = {}
    const hourCounts: { [hour: number]: number } = {}

    operations.forEach(op => {
      operationCounts[op.action] = (operationCounts[op.action] || 0) + 1
      
      const hour = new Date(op.createdAt).getHours()
      hourCounts[hour] = (hourCounts[hour] || 0) + 1
    })

    // 获取最常用的操作
    const topOperations = Object.entries(operationCounts)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 10)
      .map(([action, count]) => ({ action, count }))

    // 计算操作频率
    const daySpan = (operations[operations.length - 1].createdAt.getTime() - operations[0].createdAt.getTime())
                   / (1000 * 60 * 60 * 24)
    const operationFrequency = operations.length / Math.max(daySpan, 1)

    // 找出操作高峰时间
    const peakOperationHours = Object.entries(hourCounts)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 3)
      .map(([hour]) => parseInt(hour))

    return {
      topOperations,
      operationFrequency,
      peakOperationHours
    }
  }

  /**
   * 分析功能使用
   */
  private static analyzeFeatureUsage(featureUsage: any[]): any {
    if (featureUsage.length === 0) {
      return {
        topFeatures: [],
        diversityScore: 0,
        specialization: []
      }
    }

    // 统计功能使用次数
    const featureCounts: { [feature: string]: number } = {}
    let totalUsage = 0

    featureUsage.forEach(usage => {
      featureCounts[usage.featureName] = (featureCounts[usage.featureName] || 0) + usage.usageCount
      totalUsage += usage.usageCount
    })

    // 获取最常用功能
    const topFeatures = Object.entries(featureCounts)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 10)
      .map(([feature, usage]) => ({ feature, usage }))

    // 计算多样性分数（使用不同功能的数量 / 总功能数）
    const uniqueFeatures = Object.keys(featureCounts).length
    const diversityScore = uniqueFeatures / Math.max(topFeatures.length, 1)

    // 找出专业化领域（使用频率最高的功能类别）
    const specialization = topFeatures
      .filter(f => f.usage / totalUsage > 0.1) // 使用率超过10%的功能
      .map(f => f.feature)

    return {
      topFeatures,
      diversityScore,
      specialization
    }
  }

  /**
   * 计算风险指标
   */
  private static async calculateRiskIndicators(
    userId: string,
    sessions: any[],
    operations: any[],
    days: number
  ): Promise<any> {
    // 异常时间活动（工作时间外的活动）
    const unusualTimeActivity = sessions.filter(session => {
      const hour = new Date(session.loginTime).getHours()
      return hour < 8 || hour > 18 // 非工作时间
    }).length / Math.max(sessions.length, 1)

    // 操作突增（与平均值相比的异常增长）
    const operationSpikes = await this.calculateOperationSpikes(userId, operations, days)

    // 位置异常（不同IP地址登录）
    const uniqueIPs = new Set(sessions.map(s => s.ipAddress).filter(Boolean))
    const locationAnomalies = uniqueIPs.size > 3 ? 1 : 0

    // 行为变化（与历史模式的偏差）
    const behaviorChanges = await this.calculateBehaviorChanges(userId, days)

    return {
      unusualTimeActivity,
      operationSpikes,
      locationAnomalies,
      behaviorChanges
    }
  }

  /**
   * 预测风险
   */
  private static predictRisk(profile: UserProfileData): PredictiveInsight | null {
    const riskFactors = []
    let riskScore = 0

    // 分析风险因素
    if (profile.riskIndicators.unusualTimeActivity > 0.3) {
      riskFactors.push('频繁在非工作时间活动')
      riskScore += 30
    }

    if (profile.riskIndicators.operationSpikes > 0.5) {
      riskFactors.push('操作频率异常增长')
      riskScore += 25
    }

    if (profile.riskIndicators.locationAnomalies > 0) {
      riskFactors.push('多地点登录异常')
      riskScore += 20
    }

    if (profile.riskIndicators.behaviorChanges > 0.4) {
      riskFactors.push('行为模式显著变化')
      riskScore += 25
    }

    if (riskScore < 30) return null

    const severity = riskScore >= 70 ? 'HIGH' : riskScore >= 50 ? 'MEDIUM' : 'LOW'
    
    return {
      type: 'risk',
      userId: profile.userId,
      prediction: `用户存在${severity === 'HIGH' ? '高' : severity === 'MEDIUM' ? '中等' : '低'}风险`,
      confidence: Math.min(riskScore / 100, 0.95),
      reasoning: riskFactors,
      recommendations: [
        '加强账户监控',
        '要求多因子认证',
        '限制敏感操作权限',
        '进行安全培训'
      ],
      validUntil: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7天有效
    }
  }

  /**
   * 预测用户流失
   */
  private static predictChurn(profile: UserProfileData): PredictiveInsight | null {
    let churnScore = 0
    const churnFactors = []

    // 登录频率下降
    if (profile.loginPatterns.loginFrequency < 0.5) {
      churnFactors.push('登录频率过低')
      churnScore += 40
    }

    // 会话时长过短
    if (profile.loginPatterns.avgSessionDuration < 30) {
      churnFactors.push('平均会话时长过短')
      churnScore += 25
    }

    // 功能使用多样性低
    if (profile.featureUsage.diversityScore < 0.3) {
      churnFactors.push('功能使用单一')
      churnScore += 20
    }

    // 操作频率低
    if (profile.operationPatterns.operationFrequency < 5) {
      churnFactors.push('操作活跃度低')
      churnScore += 15
    }

    if (churnScore < 40) return null

    return {
      type: 'churn',
      userId: profile.userId,
      prediction: `用户有${churnScore >= 70 ? '高' : '中等'}流失风险`,
      confidence: Math.min(churnScore / 100, 0.9),
      reasoning: churnFactors,
      recommendations: [
        '发送产品使用指南',
        '安排培训课程',
        '了解使用障碍',
        '提供个性化支持'
      ],
      validUntil: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000)
    }
  }

  // 其他预测方法的简化实现...
  private static predictEngagement(profile: UserProfileData): PredictiveInsight | null {
    // 实现参与度预测逻辑
    return null
  }

  private static predictProductivity(profile: UserProfileData): PredictiveInsight | null {
    // 实现生产力预测逻辑
    return null
  }

  private static detectTimeAnomalies(profile: UserProfileData): any[] {
    // 实现时间异常检测
    return []
  }

  private static detectOperationAnomalies(profile: UserProfileData): any[] {
    // 实现操作异常检测
    return []
  }

  private static detectFeatureAnomalies(profile: UserProfileData): any[] {
    // 实现功能异常检测
    return []
  }

  private static detectBehaviorAnomalies(profile: UserProfileData): any[] {
    // 实现行为异常检测
    return []
  }

  private static generateProfileBasedRecommendations(profile: UserProfileData): any[] {
    // 基于画像生成推荐
    return []
  }

  private static generateInsightBasedRecommendations(insights: PredictiveInsight[]): any[] {
    // 基于预测生成推荐
    return []
  }

  private static generateAnomalyBasedRecommendations(anomalies: any[]): any[] {
    // 基于异常生成推荐
    return []
  }

  // 相似度计算方法...
  private static calculateLoginPatternSimilarity(pattern1: any, pattern2: any): number {
    // 实现登录模式相似度计算
    return 0.5
  }

  private static calculateOperationPatternSimilarity(pattern1: any, pattern2: any): number {
    // 实现操作模式相似度计算
    return 0.5
  }

  private static calculateFeatureUsageSimilarity(usage1: any, usage2: any): number {
    // 实现功能使用相似度计算
    return 0.5
  }

  private static findCommonPatterns(profile1: UserProfileData, profile2: UserProfileData): string[] {
    // 找出共同模式
    return []
  }

  private static findDifferences(profile1: UserProfileData, profile2: UserProfileData): string[] {
    // 找出差异
    return []
  }

  private static generateSimilarityRecommendations(
    profile1: UserProfileData, 
    profile2: UserProfileData, 
    commonPatterns: string[], 
    differences: string[]
  ): string[] {
    // 生成相似度推荐
    return []
  }

  private static async calculateOperationSpikes(userId: string, operations: any[], days: number): Promise<number> {
    // 计算操作突增
    return 0
  }

  private static async calculateBehaviorChanges(userId: string, days: number): Promise<number> {
    // 计算行为变化
    return 0
  }
}