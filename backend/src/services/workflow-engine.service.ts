import { prisma } from '@/config/database.config';
import { EventEmitter } from 'events';
import type { 
  WorkflowDefinition, 
  WorkflowExecution, 
  WorkflowExecutionStep,
  WorkflowExecutionStatus,
  WorkflowStepStatus,
  WorkflowTriggerType,
  WorkflowStepType,
  WorkflowPriority
} from '@prisma/client';

// 工作流触发器接口
export interface WorkflowTrigger {
  type: WorkflowTriggerType;
  config: any;
  data?: any;
}

// 工作流步骤接口
export interface WorkflowStep {
  index: number;
  name: string;
  type: WorkflowStepType;
  config: any;
  condition?: any;
  timeout?: number;
  retry?: {
    maxAttempts: number;
    delay: number;
  };
}

// 工作流配置接口
export interface WorkflowConfig {
  trigger: WorkflowTrigger;
  steps: WorkflowStep[];
  variables?: Record<string, any>;
  settings?: {
    timeout?: number;
    maxRetries?: number;
    errorHandling?: 'stop' | 'continue' | 'rollback';
    notifications?: string[];
  };
}

// 执行上下文接口
export interface WorkflowExecutionContext {
  executionId: string;
  workflowId: string;
  variables: Record<string, any>;
  triggerData?: any;
  userId?: string;
}

// 步骤执行结果接口
export interface StepExecutionResult {
  success: boolean;
  data?: any;
  error?: string;
  logs?: string[];
  nextStep?: number; // 允许动态跳转
}

// 工作流引擎类
export class WorkflowEngine extends EventEmitter {
  private static instance: WorkflowEngine;
  private runningExecutions = new Map<string, Promise<void>>();
  private stepExecutors = new Map<WorkflowStepType, (step: WorkflowStep, context: WorkflowExecutionContext) => Promise<StepExecutionResult>>();

  constructor() {
    super();
    this.registerDefaultExecutors();
  }

  static getInstance(): WorkflowEngine {
    if (!WorkflowEngine.instance) {
      WorkflowEngine.instance = new WorkflowEngine();
    }
    return WorkflowEngine.instance;
  }

  /**
   * 注册默认的步骤执行器
   */
  private registerDefaultExecutors() {
    // ACTION步骤执行器
    this.stepExecutors.set('ACTION', async (step, context) => {
      const { config } = step;
      
      try {
        switch (config.actionType) {
          case 'assign_service':
            return await this.executeAssignService(config, context);
          case 'update_status':
            return await this.executeUpdateStatus(config, context);
          case 'send_notification':
            return await this.executeSendNotification(config, context);
          case 'create_service':
            return await this.executeCreateService(config, context);
          default:
            return { success: false, error: `Unknown action type: ${config.actionType}` };
        }
      } catch (error: any) {
        return { success: false, error: error.message };
      }
    });

    // CONDITION步骤执行器
    this.stepExecutors.set('CONDITION', async (step, context) => {
      try {
        const result = this.evaluateCondition(step.config.condition, context);
        return {
          success: true,
          data: { conditionMet: result },
          nextStep: result ? step.config.trueStep : step.config.falseStep
        };
      } catch (error: any) {
        return { success: false, error: error.message };
      }
    });

    // APPROVAL步骤执行器
    this.stepExecutors.set('APPROVAL', async (step, context) => {
      try {
        // 创建审批任务
        const approvalResult = await this.createApprovalTask(step.config, context);
        return { success: true, data: approvalResult };
      } catch (error: any) {
        return { success: false, error: error.message };
      }
    });

    // NOTIFICATION步骤执行器  
    this.stepExecutors.set('NOTIFICATION', async (step, context) => {
      try {
        const result = await this.executeSendNotification(step.config, context);
        return result;
      } catch (error: any) {
        return { success: false, error: error.message };
      }
    });

    // DELAY步骤执行器
    this.stepExecutors.set('DELAY', async (step, context) => {
      const delayMs = step.config.delay || 1000;
      await new Promise(resolve => setTimeout(resolve, delayMs));
      return { success: true, data: { delayed: delayMs } };
    });

    // HTTP_REQUEST步骤执行器
    this.stepExecutors.set('HTTP_REQUEST', async (step, context) => {
      try {
        const response = await this.executeHttpRequest(step.config, context);
        return { success: true, data: response };
      } catch (error: any) {
        return { success: false, error: error.message };
      }
    });

    // DATABASE_OPERATION步骤执行器
    this.stepExecutors.set('DATABASE_OPERATION', async (step, context) => {
      try {
        const result = await this.executeDatabaseOperation(step.config, context);
        return { success: true, data: result };
      } catch (error: any) {
        return { success: false, error: error.message };
      }
    });
  }

  /**
   * 注册自定义步骤执行器
   */
  registerExecutor(stepType: WorkflowStepType, executor: (step: WorkflowStep, context: WorkflowExecutionContext) => Promise<StepExecutionResult>) {
    this.stepExecutors.set(stepType, executor);
  }

  /**
   * 触发工作流执行
   */
  async triggerWorkflow(
    workflowId: string,
    triggerType: WorkflowTriggerType,
    triggerData?: any,
    triggeredBy?: string
  ): Promise<string> {
    // 获取工作流定义
    const workflow = await prisma.workflowDefinition.findUnique({
      where: { id: workflowId }
    });

    if (!workflow || !workflow.isActive) {
      throw new Error('工作流不存在或已禁用');
    }

    // 解析配置
    const config = workflow.stepsConfig as WorkflowConfig;
    const executionId = `exec_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // 创建执行记录
    const execution = await prisma.workflowExecution.create({
      data: {
        workflowId,
        executionId,
        status: 'PENDING',
        priority: workflow.priority,
        triggerType,
        triggerData: triggerData || {},
        triggeredBy,
        currentStep: 0,
        totalSteps: config.steps.length,
        variables: config.variables || {},
        scheduledAt: new Date(),
        timeoutAt: config.settings?.timeout ? new Date(Date.now() + config.settings.timeout * 1000) : null
      }
    });

    // 异步执行工作流
    this.executeWorkflow(execution);

    return executionId;
  }

  /**
   * 执行工作流
   */
  private async executeWorkflow(execution: WorkflowExecution) {
    const executionPromise = this.doExecuteWorkflow(execution);
    this.runningExecutions.set(execution.executionId, executionPromise);

    try {
      await executionPromise;
    } finally {
      this.runningExecutions.delete(execution.executionId);
    }
  }

  /**
   * 实际执行工作流
   */
  private async doExecuteWorkflow(execution: WorkflowExecution) {
    let currentExecution = execution;
    
    try {
      // 更新状态为运行中
      currentExecution = await prisma.workflowExecution.update({
        where: { id: execution.id },
        data: {
          status: 'RUNNING',
          startedAt: new Date()
        }
      });

      const workflow = await prisma.workflowDefinition.findUnique({
        where: { id: execution.workflowId }
      });

      if (!workflow) {
        throw new Error('工作流定义不存在');
      }

      const config = workflow.stepsConfig as WorkflowConfig;
      const context: WorkflowExecutionContext = {
        executionId: execution.executionId,
        workflowId: execution.workflowId,
        variables: execution.variables as Record<string, any> || {},
        triggerData: execution.triggerData,
        userId: execution.triggeredBy || undefined
      };

      this.emit('execution:started', { executionId: execution.executionId, workflowId: execution.workflowId });

      // 执行步骤
      let currentStepIndex = execution.currentStep;
      const maxSteps = config.steps.length;

      while (currentStepIndex < maxSteps) {
        const step = config.steps[currentStepIndex];
        
        // 检查超时
        if (currentExecution.timeoutAt && new Date() > currentExecution.timeoutAt) {
          throw new Error('工作流执行超时');
        }

        // 执行步骤
        const stepResult = await this.executeStep(step, context, currentExecution);
        
        if (!stepResult.success) {
          // 处理失败
          const errorHandling = config.settings?.errorHandling || 'stop';
          if (errorHandling === 'stop') {
            throw new Error(`步骤执行失败: ${stepResult.error}`);
          } else if (errorHandling === 'continue') {
            console.warn(`步骤执行失败但继续执行: ${stepResult.error}`);
          }
        }

        // 更新变量
        if (stepResult.data) {
          context.variables = { ...context.variables, ...stepResult.data };
        }

        // 确定下一步
        currentStepIndex = stepResult.nextStep !== undefined ? stepResult.nextStep : currentStepIndex + 1;
        
        // 更新执行状态
        currentExecution = await prisma.workflowExecution.update({
          where: { id: currentExecution.id },
          data: {
            currentStep: currentStepIndex,
            variables: context.variables
          }
        });
      }

      // 完成执行
      await prisma.workflowExecution.update({
        where: { id: currentExecution.id },
        data: {
          status: 'COMPLETED',
          completedAt: new Date(),
          executionTime: (new Date().getTime() - (currentExecution.startedAt?.getTime() || Date.now())) / 1000
        }
      });

      // 更新工作流统计
      await prisma.workflowDefinition.update({
        where: { id: workflow.id },
        data: {
          executionCount: { increment: 1 },
          successCount: { increment: 1 },
          lastExecutedAt: new Date()
        }
      });

      this.emit('execution:completed', { 
        executionId: execution.executionId, 
        workflowId: execution.workflowId,
        success: true
      });

    } catch (error: any) {
      console.error(`工作流执行失败 [${execution.executionId}]:`, error);

      // 更新执行状态为失败
      await prisma.workflowExecution.update({
        where: { id: currentExecution.id },
        data: {
          status: 'FAILED',
          completedAt: new Date(),
          error: error.message,
          executionTime: (new Date().getTime() - (currentExecution.startedAt?.getTime() || Date.now())) / 1000
        }
      });

      // 更新工作流统计
      const workflow = await prisma.workflowDefinition.findUnique({
        where: { id: execution.workflowId }
      });

      if (workflow) {
        await prisma.workflowDefinition.update({
          where: { id: workflow.id },
          data: {
            executionCount: { increment: 1 },
            failureCount: { increment: 1 },
            lastExecutedAt: new Date()
          }
        });
      }

      this.emit('execution:failed', { 
        executionId: execution.executionId, 
        workflowId: execution.workflowId,
        error: error.message
      });
    }
  }

  /**
   * 执行单个步骤
   */
  private async executeStep(
    step: WorkflowStep, 
    context: WorkflowExecutionContext,
    execution: WorkflowExecution
  ): Promise<StepExecutionResult> {
    // 创建步骤执行记录
    const stepExecution = await prisma.workflowExecutionStep.create({
      data: {
        executionId: execution.id,
        stepIndex: step.index,
        stepName: step.name,
        stepType: step.type,
        status: 'RUNNING',
        config: step.config,
        startedAt: new Date()
      }
    });

    try {
      this.emit('step:started', { 
        executionId: context.executionId,
        stepIndex: step.index,
        stepName: step.name
      });

      // 获取步骤执行器
      const executor = this.stepExecutors.get(step.type);
      if (!executor) {
        throw new Error(`不支持的步骤类型: ${step.type}`);
      }

      // 执行步骤
      let result = await executor(step, context);
      let retryCount = 0;
      const maxRetries = step.retry?.maxAttempts || 0;

      // 重试逻辑
      while (!result.success && retryCount < maxRetries) {
        retryCount++;
        await new Promise(resolve => setTimeout(resolve, step.retry?.delay || 1000));
        result = await executor(step, context);
      }

      // 更新步骤状态
      await prisma.workflowExecutionStep.update({
        where: { id: stepExecution.id },
        data: {
          status: result.success ? 'COMPLETED' : 'FAILED',
          completedAt: new Date(),
          output: result.data,
          error: result.error,
          retryCount,
          executionTime: (new Date().getTime() - stepExecution.startedAt!.getTime()) / 1000
        }
      });

      this.emit('step:completed', {
        executionId: context.executionId,
        stepIndex: step.index,
        stepName: step.name,
        success: result.success,
        retryCount
      });

      return result;

    } catch (error: any) {
      // 更新步骤状态为失败
      await prisma.workflowExecutionStep.update({
        where: { id: stepExecution.id },
        data: {
          status: 'FAILED',
          completedAt: new Date(),
          error: error.message,
          executionTime: (new Date().getTime() - stepExecution.startedAt!.getTime()) / 1000
        }
      });

      this.emit('step:failed', {
        executionId: context.executionId,
        stepIndex: step.index,
        stepName: step.name,
        error: error.message
      });

      return { success: false, error: error.message };
    }
  }

  /**
   * 暂停工作流执行
   */
  async pauseExecution(executionId: string): Promise<void> {
    await prisma.workflowExecution.update({
      where: { executionId },
      data: { status: 'PAUSED' }
    });

    this.emit('execution:paused', { executionId });
  }

  /**
   * 恢复工作流执行
   */
  async resumeExecution(executionId: string): Promise<void> {
    const execution = await prisma.workflowExecution.findUnique({
      where: { executionId }
    });

    if (!execution || execution.status !== 'PAUSED') {
      throw new Error('执行不存在或状态不正确');
    }

    await prisma.workflowExecution.update({
      where: { executionId },
      data: { status: 'RUNNING' }
    });

    // 重新开始执行
    this.executeWorkflow(execution);
    this.emit('execution:resumed', { executionId });
  }

  /**
   * 取消工作流执行
   */
  async cancelExecution(executionId: string): Promise<void> {
    await prisma.workflowExecution.update({
      where: { executionId },
      data: { 
        status: 'CANCELLED',
        completedAt: new Date()
      }
    });

    this.emit('execution:cancelled', { executionId });
  }

  /**
   * 获取正在运行的执行
   */
  getRunningExecutions(): string[] {
    return Array.from(this.runningExecutions.keys());
  }

  /**
   * 计算条件
   */
  private evaluateCondition(condition: any, context: WorkflowExecutionContext): boolean {
    try {
      // 简单的条件评估，可以扩展支持更复杂的表达式
      if (condition.type === 'compare') {
        const leftValue = this.resolveValue(condition.left, context);
        const rightValue = this.resolveValue(condition.right, context);
        
        switch (condition.operator) {
          case '==': return leftValue == rightValue;
          case '===': return leftValue === rightValue;
          case '!=': return leftValue != rightValue;
          case '!==': return leftValue !== rightValue;
          case '>': return leftValue > rightValue;
          case '>=': return leftValue >= rightValue;
          case '<': return leftValue < rightValue;
          case '<=': return leftValue <= rightValue;
          default: return false;
        }
      }
      
      return false;
    } catch {
      return false;
    }
  }

  /**
   * 解析值（支持变量引用）
   */
  private resolveValue(value: any, context: WorkflowExecutionContext): any {
    if (typeof value === 'string' && value.startsWith('${') && value.endsWith('}')) {
      const varName = value.slice(2, -1);
      return context.variables[varName];
    }
    return value;
  }

  // ========== 具体的步骤执行器实现 ==========

  /**
   * 分配服务工单
   */
  private async executeAssignService(config: any, context: WorkflowExecutionContext): Promise<StepExecutionResult> {
    const { serviceId, assignedUserId } = config;
    
    const resolvedServiceId = this.resolveValue(serviceId, context);
    const resolvedUserId = this.resolveValue(assignedUserId, context);

    await prisma.service.update({
      where: { id: resolvedServiceId },
      data: { 
        assignedUserId: resolvedUserId,
        status: 'IN_PROGRESS',
        updatedAt: new Date()
      }
    });

    return { 
      success: true, 
      data: { serviceId: resolvedServiceId, assignedUserId: resolvedUserId },
      logs: [`服务工单 ${resolvedServiceId} 已分配给用户 ${resolvedUserId}`]
    };
  }

  /**
   * 更新状态
   */
  private async executeUpdateStatus(config: any, context: WorkflowExecutionContext): Promise<StepExecutionResult> {
    const { entityType, entityId, status } = config;
    
    const resolvedEntityId = this.resolveValue(entityId, context);
    const resolvedStatus = this.resolveValue(status, context);

    if (entityType === 'service') {
      await prisma.service.update({
        where: { id: resolvedEntityId },
        data: { 
          status: resolvedStatus,
          updatedAt: new Date()
        }
      });
    }

    return { 
      success: true, 
      data: { entityType, entityId: resolvedEntityId, status: resolvedStatus },
      logs: [`${entityType} ${resolvedEntityId} 状态更新为 ${resolvedStatus}`]
    };
  }

  /**
   * 发送通知
   */
  private async executeSendNotification(config: any, context: WorkflowExecutionContext): Promise<StepExecutionResult> {
    // 这里应该集成实际的通知服务
    const { recipients, template, data } = config;
    
    const resolvedRecipients = this.resolveValue(recipients, context);
    const resolvedData = this.resolveValue(data, context);

    // 模拟发送通知
    console.log(`发送通知到: ${resolvedRecipients}, 模板: ${template}, 数据:`, resolvedData);

    return { 
      success: true, 
      data: { recipients: resolvedRecipients, template, notificationData: resolvedData },
      logs: [`通知已发送给: ${resolvedRecipients}`]
    };
  }

  /**
   * 创建服务工单
   */
  private async executeCreateService(config: any, context: WorkflowExecutionContext): Promise<StepExecutionResult> {
    const serviceData = {
      ...config.serviceData,
      ticketNumber: `AUTO_${Date.now()}`,
      status: 'OPEN',
      source: 'WORKFLOW'
    };

    // 解析变量
    Object.keys(serviceData).forEach(key => {
      serviceData[key] = this.resolveValue(serviceData[key], context);
    });

    const service = await prisma.service.create({
      data: serviceData
    });

    return { 
      success: true, 
      data: { serviceId: service.id, ticketNumber: service.ticketNumber },
      logs: [`创建服务工单: ${service.ticketNumber}`]
    };
  }

  /**
   * 创建审批任务
   */
  private async createApprovalTask(config: any, context: WorkflowExecutionContext): Promise<any> {
    // 这里应该集成审批系统
    const { approvers, title, description } = config;
    
    console.log(`创建审批任务: ${title}, 审批人: ${approvers}`);
    
    return {
      approvalId: `approval_${Date.now()}`,
      status: 'pending',
      approvers
    };
  }

  /**
   * 执行HTTP请求
   */
  private async executeHttpRequest(config: any, context: WorkflowExecutionContext): Promise<any> {
    const { method, url, headers, data } = config;
    
    const resolvedUrl = this.resolveValue(url, context);
    const resolvedData = this.resolveValue(data, context);

    // 模拟HTTP请求
    console.log(`HTTP ${method} ${resolvedUrl}`, resolvedData);
    
    return {
      status: 200,
      data: { message: 'Success' }
    };
  }

  /**
   * 执行数据库操作
   */
  private async executeDatabaseOperation(config: any, context: WorkflowExecutionContext): Promise<any> {
    const { operation, table, data, where } = config;
    
    // 这里应该实现安全的数据库操作
    console.log(`数据库操作: ${operation} on ${table}`, { data, where });
    
    return {
      affected: 1,
      operation
    };
  }
}

// 导出单例实例
export const workflowEngine = WorkflowEngine.getInstance();