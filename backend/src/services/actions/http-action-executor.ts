/**
 * HTTP请求动作执行器
 * 负责执行HTTP请求操作
 */

import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { WorkflowStepType } from '@prisma/client';
import { BaseActionExecutor } from './base-action-executor';
import type {
  WorkflowStep,
  ExecutionContext,
  ActionExecutionResult,
  ValidationResult,
  RollbackResult,
  HttpActionConfig,
  ErrorType,
  ErrorSeverity
} from '@/types/action-executor.types';

/**
 * HTTP动作执行器
 */
export class HttpActionExecutor extends BaseActionExecutor {
  public readonly type = WorkflowStepType.HTTP_REQUEST;
  public readonly name = 'HTTP请求执行器';
  public readonly version = '1.0.0';

  private httpClient: AxiosInstance;
  private defaultTimeout = 30000; // 30秒

  constructor() {
    super();
    this.setupHttpClient();
  }

  /**
   * 设置HTTP客户端
   */
  private setupHttpClient(): void {
    this.httpClient = axios.create({
      timeout: this.defaultTimeout,
      maxRedirects: 5,
      validateStatus: (status) => status < 500, // 不自动抛出4xx错误
    });

    // 请求拦截器
    this.httpClient.interceptors.request.use(
      (config) => {
        console.log(`🌐 HTTP请求: ${config.method?.toUpperCase()} ${config.url}`);
        return config;
      },
      (error) => {
        console.error('❌ HTTP请求拦截器错误:', error);
        return Promise.reject(error);
      }
    );

    // 响应拦截器
    this.httpClient.interceptors.response.use(
      (response) => {
        console.log(`✅ HTTP响应: ${response.status} ${response.config.url}`);
        return response;
      },
      (error) => {
        console.error('❌ HTTP响应拦截器错误:', error.message);
        return Promise.reject(error);
      }
    );
  }

  /**
   * 初始化执行器
   */
  protected async doInitialize(): Promise<void> {
    // 测试HTTP客户端
    try {
      await this.httpClient.get('https://httpbin.org/status/200', {
        timeout: 5000,
        validateStatus: (status) => status === 200
      });
      console.log('✅ HTTP客户端测试成功');
    } catch (error) {
      console.warn('⚠️ HTTP客户端测试失败，但继续初始化:', error);
    }
  }

  /**
   * 健康检查
   */
  protected async doHealthCheck(): Promise<boolean> {
    try {
      await this.httpClient.get('https://httpbin.org/status/200', {
        timeout: 3000,
        validateStatus: (status) => status === 200
      });
      return true;
    } catch {
      return false;
    }
  }

  /**
   * 验证步骤配置
   */
  protected async doValidate(step: WorkflowStep): Promise<ValidationResult> {
    const config = step.config as HttpActionConfig;
    
    // 使用基础验证方法
    const baseValidation = this.validateConfig(
      config,
      ['method', 'url'], // 必需字段
      ['headers', 'data', 'timeout', 'authentication', 'retryPolicy', 'validateResponse'] // 可选字段
    );

    if (!baseValidation.valid) {
      return baseValidation;
    }

    const errors: string[] = [];
    const warnings: string[] = [];

    // 验证HTTP方法
    const validMethods = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'];
    if (!validMethods.includes(config.method.toUpperCase())) {
      errors.push(`不支持的HTTP方法: ${config.method}`);
    }

    // 验证URL格式
    try {
      new URL(config.url);
    } catch {
      errors.push(`无效的URL格式: ${config.url}`);
    }

    // 验证超时设置
    if (config.timeout && (config.timeout < 1000 || config.timeout > 300000)) {
      warnings.push('建议超时时间设置在1秒到5分钟之间');
    }

    // 验证认证配置
    if (config.authentication) {
      const auth = config.authentication;
      if (!['basic', 'bearer', 'api-key'].includes(auth.type)) {
        errors.push(`不支持的认证类型: ${auth.type}`);
      }

      if (!auth.credentials) {
        errors.push('认证凭据不能为空');
      }
    }

    return {
      valid: errors.length === 0,
      errors: [...baseValidation.errors, ...errors],
      warnings: [...baseValidation.warnings, ...warnings]
    };
  }

  /**
   * 执行HTTP请求
   */
  protected async doExecute(step: WorkflowStep, context: ExecutionContext): Promise<ActionExecutionResult> {
    const config = step.config as HttpActionConfig;
    const logs: ExecutionLog[] = [];

    try {
      // 解析配置中的变量
      const resolvedConfig = this.resolveValue(config, context) as HttpActionConfig;

      // 构建axios请求配置
      const axiosConfig = await this.buildAxiosConfig(resolvedConfig, context);

      this.addLog(logs, 'INFO', `发送HTTP请求: ${resolvedConfig.method} ${resolvedConfig.url}`, context, {
        method: resolvedConfig.method,
        url: resolvedConfig.url,
        headers: this.sanitizeHeaders(axiosConfig.headers || {})
      });

      // 执行HTTP请求
      const response = await this.executeHttpRequest(axiosConfig, context);

      // 验证响应
      if (resolvedConfig.validateResponse) {
        const validationResult = this.validateResponse(response, resolvedConfig);
        if (!validationResult.isValid) {
          const error = this.createError(
            ErrorType.DATA_ERROR,
            'RESPONSE_VALIDATION_FAILED',
            validationResult.message,
            { statusCode: response.status, responseData: response.data }
          );
          return this.createFailureResult(error, logs, true);
        }
      }

      // 处理成功响应
      this.addLog(logs, 'INFO', `HTTP请求成功: ${response.status} ${response.statusText}`, context, {
        statusCode: response.status,
        responseSize: JSON.stringify(response.data).length
      });

      const responseData = {
        statusCode: response.status,
        statusText: response.statusText,
        headers: response.headers,
        data: response.data,
        requestUrl: response.config?.url,
        requestMethod: response.config?.method?.toUpperCase()
      };

      return this.createSuccessResult(responseData, logs);

    } catch (error: any) {
      return await this.handleHttpError(error, logs, context, config);
    }
  }

  /**
   * 构建Axios请求配置
   */
  private async buildAxiosConfig(config: HttpActionConfig, context: ExecutionContext): Promise<AxiosRequestConfig> {
    const axiosConfig: AxiosRequestConfig = {
      method: config.method.toLowerCase() as any,
      url: config.url,
      timeout: config.timeout || this.defaultTimeout,
      headers: {
        'User-Agent': 'OpsManagement-Workflow/1.0',
        'X-Execution-ID': context.executionId,
        ...config.headers
      }
    };

    // 添加请求体数据
    if (config.data && ['POST', 'PUT', 'PATCH'].includes(config.method.toUpperCase())) {
      axiosConfig.data = config.data;
      
      // 自动设置Content-Type
      if (!axiosConfig.headers!['Content-Type']) {
        if (typeof config.data === 'object') {
          axiosConfig.headers!['Content-Type'] = 'application/json';
        } else {
          axiosConfig.headers!['Content-Type'] = 'text/plain';
        }
      }
    }

    // 添加认证信息
    if (config.authentication) {
      await this.addAuthentication(axiosConfig, config.authentication, context);
    }

    // 添加重试配置
    if (config.retryPolicy) {
      // Axios doesn't have built-in retry, we handle it at the executor level
      axiosConfig.timeout = config.retryPolicy.delay || axiosConfig.timeout;
    }

    return axiosConfig;
  }

  /**
   * 添加认证信息
   */
  private async addAuthentication(
    axiosConfig: AxiosRequestConfig,
    authentication: HttpActionConfig['authentication'],
    context: ExecutionContext
  ): Promise<void> {
    if (!authentication) return;

    const credentials = this.resolveValue(authentication.credentials, context);

    switch (authentication.type) {
      case 'basic':
        if (credentials.username && credentials.password) {
          axiosConfig.auth = {
            username: credentials.username,
            password: credentials.password
          };
        }
        break;

      case 'bearer':
        if (credentials.token) {
          axiosConfig.headers!['Authorization'] = `Bearer ${credentials.token}`;
        }
        break;

      case 'api-key':
        if (credentials.key) {
          const headerName = credentials.headerName || 'X-API-Key';
          axiosConfig.headers![headerName] = credentials.key;
        }
        break;
    }
  }

  /**
   * 执行HTTP请求
   */
  private async executeHttpRequest(
    axiosConfig: AxiosRequestConfig,
    context: ExecutionContext
  ): Promise<AxiosResponse> {
    // 检查中止信号
    if (this.checkAbortSignal(context)) {
      throw new Error('请求已被中止');
    }

    // 添加中止控制
    axiosConfig.signal = context.abortController.signal;

    return await this.httpClient.request(axiosConfig);
  }

  /**
   * 验证响应
   */
  private validateResponse(response: AxiosResponse, config: HttpActionConfig): { isValid: boolean; message: string } {
    // 检查状态码
    if (response.status >= 400) {
      return {
        isValid: false,
        message: `HTTP请求失败，状态码: ${response.status}`
      };
    }

    // 检查响应内容类型
    const contentType = response.headers['content-type'];
    if (contentType && contentType.includes('application/json')) {
      try {
        // 尝试解析JSON
        if (typeof response.data === 'string') {
          JSON.parse(response.data);
        }
      } catch {
        return {
          isValid: false,
          message: '响应数据不是有效的JSON格式'
        };
      }
    }

    return { isValid: true, message: '响应验证通过' };
  }

  /**
   * 处理HTTP错误
   */
  private async handleHttpError(
    error: any,
    logs: ExecutionLog[],
    context: ExecutionContext,
    config: HttpActionConfig
  ): Promise<ActionExecutionResult> {
    let errorType = ErrorType.NETWORK_ERROR;
    let errorCode = 'HTTP_REQUEST_FAILED';
    let errorMessage = error.message || 'HTTP请求失败';
    let shouldRetry = false;
    let severity = ErrorSeverity.MEDIUM;

    // 分析错误类型
    if (error.code === 'ECONNABORTED' || error.message?.includes('timeout')) {
      errorType = ErrorType.TIMEOUT_ERROR;
      errorCode = 'HTTP_REQUEST_TIMEOUT';
      errorMessage = 'HTTP请求超时';
      shouldRetry = true;
    } else if (error.code === 'ENOTFOUND' || error.code === 'ECONNREFUSED') {
      errorType = ErrorType.NETWORK_ERROR;
      errorCode = 'NETWORK_ERROR';
      errorMessage = `网络连接失败: ${error.code}`;
      shouldRetry = true;
    } else if (error.response) {
      // HTTP错误响应
      const status = error.response.status;
      errorMessage = `HTTP请求失败: ${status} ${error.response.statusText}`;
      
      if (status >= 400 && status < 500) {
        errorType = ErrorType.AUTHENTICATION_ERROR;
        errorCode = 'HTTP_CLIENT_ERROR';
        shouldRetry = false; // 客户端错误通常不需要重试
        severity = ErrorSeverity.HIGH;
      } else if (status >= 500) {
        errorType = ErrorType.SYSTEM_ERROR;
        errorCode = 'HTTP_SERVER_ERROR';
        shouldRetry = true; // 服务器错误可以重试
        severity = ErrorSeverity.HIGH;
      }
    }

    this.addLog(logs, 'ERROR', errorMessage, context, {
      errorCode,
      errorType,
      requestUrl: config.url,
      requestMethod: config.method,
      statusCode: error.response?.status,
      responseData: error.response?.data
    });

    const executionError = this.createError(
      errorType,
      errorCode,
      errorMessage,
      {
        originalError: error.message,
        statusCode: error.response?.status,
        responseData: error.response?.data,
        requestConfig: {
          method: config.method,
          url: config.url,
          headers: this.sanitizeHeaders(config.headers || {})
        }
      },
      shouldRetry,
      severity
    );

    return this.createFailureResult(executionError, logs, shouldRetry);
  }

  /**
   * 回滚HTTP请求（通常不支持）
   */
  protected async doRollback(step: WorkflowStep, context: ExecutionContext): Promise<RollbackResult> {
    const logs: ExecutionLog[] = [];
    
    // HTTP请求通常不支持回滚，但可以发送反向操作
    const config = step.config as HttpActionConfig;
    
    // 检查是否有回滚配置
    if (config.rollbackRequest) {
      try {
        this.addLog(logs, 'INFO', '执行HTTP回滚请求', context);
        
        const rollbackConfig = this.resolveValue(config.rollbackRequest, context) as HttpActionConfig;
        const axiosConfig = await this.buildAxiosConfig(rollbackConfig, context);
        
        const response = await this.executeHttpRequest(axiosConfig, context);
        
        this.addLog(logs, 'INFO', `HTTP回滚请求成功: ${response.status}`, context);
        
        return {
          success: true,
          logs,
          restoredState: {
            rollbackResponse: {
              statusCode: response.status,
              data: response.data
            }
          }
        };
        
      } catch (error: any) {
        const rollbackError = this.createError(
          ErrorType.EXECUTION_ERROR,
          'HTTP_ROLLBACK_FAILED',
          `HTTP回滚失败: ${error.message}`
        );
        
        this.addLog(logs, 'ERROR', `HTTP回滚失败: ${error.message}`, context);
        
        return {
          success: false,
          error: rollbackError,
          logs
        };
      }
    }

    // 默认不支持回滚
    this.addLog(logs, 'WARN', 'HTTP请求不支持自动回滚', context);
    
    return {
      success: true,
      logs
    };
  }

  /**
   * 清理敏感的头部信息（用于日志）
   */
  private sanitizeHeaders(headers: Record<string, any>): Record<string, any> {
    const sanitized = { ...headers };
    const sensitiveHeaders = ['authorization', 'x-api-key', 'cookie'];
    
    for (const key of Object.keys(sanitized)) {
      if (sensitiveHeaders.includes(key.toLowerCase())) {
        sanitized[key] = '[REDACTED]';
      }
    }
    
    return sanitized;
  }
}

// 声明类型扩展
declare module '@/types/action-executor.types' {
  interface HttpActionConfig {
    rollbackRequest?: HttpActionConfig;
  }
}