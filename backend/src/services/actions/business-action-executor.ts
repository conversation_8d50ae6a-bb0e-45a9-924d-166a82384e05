/**
 * 业务动作执行器
 * 负责执行特定于业务领域的操作
 */

import { WorkflowStepType } from '@prisma/client';
import { BaseActionExecutor } from './base-action-executor';
import { prisma } from '@/config/database.config';
import type {
  WorkflowStep,
  ExecutionContext,
  ActionExecutionResult,
  ValidationResult,
  RollbackResult,
  BusinessActionConfig,
  ErrorType,
  ErrorSeverity
} from '@/types/action-executor.types';

/**
 * 业务动作执行器
 */
export class BusinessActionExecutor extends BaseActionExecutor {
  public readonly type = WorkflowStepType.BUSINESS_LOGIC;
  public readonly name = '业务逻辑执行器';
  public readonly version = '1.0.0';

  private businessHandlers = new Map<string, (config: any, context: ExecutionContext) => Promise<any>>();

  constructor() {
    super();
    this.initializeBusinessHandlers();
  }

  /**
   * 初始化执行器
   */
  protected async doInitialize(): Promise<void> {
    try {
      // 测试数据库连接（业务逻辑通常需要访问数据库）
      await prisma.$queryRaw`SELECT 1 as test`;
      console.log('✅ 业务动作执行器初始化完成');
    } catch (error) {
      console.error('❌ 业务动作执行器初始化失败:', error);
      throw error;
    }
  }

  /**
   * 健康检查
   */
  protected async doHealthCheck(): Promise<boolean> {
    try {
      // 检查必要的服务和资源
      await prisma.$queryRaw`SELECT 1 as health_check`;
      return true;
    } catch {
      return false;
    }
  }

  /**
   * 验证步骤配置
   */
  protected async doValidate(step: WorkflowStep): Promise<ValidationResult> {
    const config = step.config as BusinessActionConfig;
    
    // 使用基础验证方法
    const baseValidation = this.validateConfig(
      config,
      ['action'], // 必需字段
      ['parameters', 'timeout', 'rollbackAction', 'validation'] // 可选字段
    );

    if (!baseValidation.valid) {
      return baseValidation;
    }

    const errors: string[] = [];
    const warnings: string[] = [];

    // 验证业务动作类型
    const validActions = [
      'create_service_ticket',
      'update_service_status',
      'close_service_ticket',
      'assign_service_engineer',
      'escalate_service',
      'calculate_sla_compliance',
      'generate_report',
      'update_customer_info',
      'create_project_archive',
      'update_configuration',
      'backup_data',
      'cleanup_resources',
      'send_notification',
      'custom_script'
    ];

    if (!validActions.includes(config.action)) {
      errors.push(`不支持的业务动作: ${config.action}`);
    }

    // 验证参数配置
    if (config.parameters && typeof config.parameters !== 'object') {
      errors.push('参数配置必须是对象类型');
    }

    // 验证特定动作的必需参数
    const actionValidation = this.validateActionSpecificParams(config.action, config.parameters || {});
    errors.push(...actionValidation.errors);
    warnings.push(...actionValidation.warnings);

    // 验证超时设置
    if (config.timeout && (config.timeout < 1000 || config.timeout > 600000)) {
      warnings.push('建议业务动作超时时间设置在1秒到10分钟之间');
    }

    return {
      valid: errors.length === 0,
      errors: [...baseValidation.errors, ...errors],
      warnings: [...baseValidation.warnings, ...warnings]
    };
  }

  /**
   * 执行业务动作
   */
  protected async doExecute(step: WorkflowStep, context: ExecutionContext): Promise<ActionExecutionResult> {
    const config = step.config as BusinessActionConfig;
    const logs: ExecutionLog[] = [];

    try {
      // 解析配置中的变量
      const resolvedConfig = this.resolveValue(config, context) as BusinessActionConfig;

      this.addLog(logs, 'INFO', `执行业务动作: ${resolvedConfig.action}`, context, {
        action: resolvedConfig.action,
        hasParameters: !!resolvedConfig.parameters
      });

      // 获取业务动作处理器
      const handler = this.businessHandlers.get(resolvedConfig.action);
      if (!handler) {
        throw new Error(`未找到业务动作处理器: ${resolvedConfig.action}`);
      }

      // 执行业务逻辑
      const result = await this.executeWithTimeout(
        () => handler(resolvedConfig.parameters || {}, context),
        resolvedConfig.timeout || 30000
      );

      // 记录成功日志
      this.addLog(logs, 'INFO', `业务动作执行完成: ${resolvedConfig.action}`, context, {
        action: resolvedConfig.action,
        resultType: typeof result,
        resultSize: JSON.stringify(result).length
      });

      const responseData = {
        action: resolvedConfig.action,
        success: true,
        result: result,
        executedAt: new Date().toISOString()
      };

      return this.createSuccessResult(responseData, logs);

    } catch (error: any) {
      return await this.handleBusinessError(error, logs, context, config);
    }
  }

  /**
   * 初始化业务动作处理器
   */
  private initializeBusinessHandlers(): void {
    // 服务工单相关操作
    this.businessHandlers.set('create_service_ticket', this.createServiceTicket.bind(this));
    this.businessHandlers.set('update_service_status', this.updateServiceStatus.bind(this));
    this.businessHandlers.set('close_service_ticket', this.closeServiceTicket.bind(this));
    this.businessHandlers.set('assign_service_engineer', this.assignServiceEngineer.bind(this));
    this.businessHandlers.set('escalate_service', this.escalateService.bind(this));

    // SLA和报告相关操作
    this.businessHandlers.set('calculate_sla_compliance', this.calculateSlaCompliance.bind(this));
    this.businessHandlers.set('generate_report', this.generateReport.bind(this));

    // 客户和项目相关操作
    this.businessHandlers.set('update_customer_info', this.updateCustomerInfo.bind(this));
    this.businessHandlers.set('create_project_archive', this.createProjectArchive.bind(this));
    this.businessHandlers.set('update_configuration', this.updateConfiguration.bind(this));

    // 系统维护相关操作
    this.businessHandlers.set('backup_data', this.backupData.bind(this));
    this.businessHandlers.set('cleanup_resources', this.cleanupResources.bind(this));

    // 通用操作
    this.businessHandlers.set('send_notification', this.sendNotification.bind(this));
    this.businessHandlers.set('custom_script', this.executeCustomScript.bind(this));
  }

  /**
   * 创建服务工单
   */
  private async createServiceTicket(params: any, context: ExecutionContext): Promise<any> {
    context.logger.info('创建服务工单', params);

    const service = await prisma.service.create({
      data: {
        ticketNumber: params.ticketNumber || `TICKET-${Date.now()}`,
        title: params.title,
        description: params.description,
        category: params.category || 'SUPPORT',
        priority: params.priority || 'MEDIUM',
        status: 'PENDING',
        customerId: params.customerId,
        projectArchiveId: params.projectArchiveId,
        assignedToId: params.assignedToId,
        createdById: params.createdById || context.metadata.executionId, // 使用执行ID作为创建者
        requestedCompletionAt: params.requestedCompletionAt ? new Date(params.requestedCompletionAt) : undefined
      }
    });

    return {
      serviceId: service.id,
      ticketNumber: service.ticketNumber,
      status: service.status,
      createdAt: service.createdAt
    };
  }

  /**
   * 更新服务状态
   */
  private async updateServiceStatus(params: any, context: ExecutionContext): Promise<any> {
    context.logger.info('更新服务状态', params);

    const service = await prisma.service.update({
      where: { id: params.serviceId },
      data: {
        status: params.status,
        updatedAt: new Date()
      }
    });

    // 如果状态变为已完成，记录完成时间
    if (params.status === 'COMPLETED') {
      await prisma.service.update({
        where: { id: params.serviceId },
        data: { completedAt: new Date() }
      });
    }

    return {
      serviceId: service.id,
      status: service.status,
      updatedAt: service.updatedAt
    };
  }

  /**
   * 关闭服务工单
   */
  private async closeServiceTicket(params: any, context: ExecutionContext): Promise<any> {
    context.logger.info('关闭服务工单', params);

    const service = await prisma.service.update({
      where: { id: params.serviceId },
      data: {
        status: 'CLOSED',
        completedAt: new Date(),
        updatedAt: new Date()
      }
    });

    return {
      serviceId: service.id,
      status: service.status,
      closedAt: service.completedAt
    };
  }

  /**
   * 分配服务工程师
   */
  private async assignServiceEngineer(params: any, context: ExecutionContext): Promise<any> {
    context.logger.info('分配服务工程师', params);

    const service = await prisma.service.update({
      where: { id: params.serviceId },
      data: {
        assignedToId: params.engineerId,
        status: 'IN_PROGRESS',
        updatedAt: new Date()
      }
    });

    return {
      serviceId: service.id,
      assignedToId: service.assignedToId,
      status: service.status
    };
  }

  /**
   * 服务升级
   */
  private async escalateService(params: any, context: ExecutionContext): Promise<any> {
    context.logger.info('服务升级', params);

    const service = await prisma.service.update({
      where: { id: params.serviceId },
      data: {
        priority: params.newPriority || 'HIGH',
        assignedToId: params.escalateToId,
        updatedAt: new Date()
      }
    });

    // 创建升级记录日志
    await prisma.serviceWorkLog.create({
      data: {
        serviceId: params.serviceId,
        workType: 'ESCALATION',
        description: `服务升级到 ${params.newPriority} 优先级，分配给工程师 ${params.escalateToId}`,
        hoursWorked: 0,
        createdById: params.escalatedById || context.metadata.executionId
      }
    });

    return {
      serviceId: service.id,
      priority: service.priority,
      assignedToId: service.assignedToId
    };
  }

  /**
   * 计算SLA合规性
   */
  private async calculateSlaCompliance(params: any, context: ExecutionContext): Promise<any> {
    context.logger.info('计算SLA合规性', params);

    const services = await prisma.service.findMany({
      where: {
        customerId: params.customerId,
        createdAt: {
          gte: new Date(params.startDate),
          lte: new Date(params.endDate)
        }
      },
      include: {
        customer: true
      }
    });

    let compliantCount = 0;
    let totalCount = services.length;

    for (const service of services) {
      // 简单的SLA合规性计算逻辑
      const responseTime = service.completedAt 
        ? (service.completedAt.getTime() - service.createdAt.getTime()) / (1000 * 60 * 60) // 小时
        : null;

      const slaHours = this.getSlaHours(service.priority);
      
      if (responseTime && responseTime <= slaHours) {
        compliantCount++;
      }
    }

    const complianceRate = totalCount > 0 ? (compliantCount / totalCount) * 100 : 0;

    return {
      customerId: params.customerId,
      period: { startDate: params.startDate, endDate: params.endDate },
      totalServices: totalCount,
      compliantServices: compliantCount,
      complianceRate: Math.round(complianceRate * 100) / 100,
      calculatedAt: new Date().toISOString()
    };
  }

  /**
   * 生成报告
   */
  private async generateReport(params: any, context: ExecutionContext): Promise<any> {
    context.logger.info('生成报告', params);

    // 根据报告类型生成不同的报告
    switch (params.reportType) {
      case 'service_summary':
        return await this.generateServiceSummaryReport(params, context);
      case 'sla_compliance':
        return await this.generateSlaComplianceReport(params, context);
      case 'customer_satisfaction':
        return await this.generateCustomerSatisfactionReport(params, context);
      default:
        throw new Error(`不支持的报告类型: ${params.reportType}`);
    }
  }

  /**
   * 更新客户信息
   */
  private async updateCustomerInfo(params: any, context: ExecutionContext): Promise<any> {
    context.logger.info('更新客户信息', params);

    const customer = await prisma.customer.update({
      where: { id: params.customerId },
      data: {
        name: params.name,
        contactEmail: params.contactEmail,
        contactPhone: params.contactPhone,
        address: params.address,
        level: params.level,
        updatedAt: new Date()
      }
    });

    return {
      customerId: customer.id,
      name: customer.name,
      updatedAt: customer.updatedAt
    };
  }

  /**
   * 创建项目档案
   */
  private async createProjectArchive(params: any, context: ExecutionContext): Promise<any> {
    context.logger.info('创建项目档案', params);

    const archive = await prisma.projectArchive.create({
      data: {
        name: params.name,
        description: params.description,
        techStack: params.techStack || [],
        version: params.version || '1.0.0',
        status: params.status || 'ACTIVE',
        customerId: params.customerId,
        deploymentDate: params.deploymentDate ? new Date(params.deploymentDate) : undefined,
        notes: params.notes
      }
    });

    return {
      archiveId: archive.id,
      name: archive.name,
      status: archive.status,
      createdAt: archive.createdAt
    };
  }

  /**
   * 更新配置
   */
  private async updateConfiguration(params: any, context: ExecutionContext): Promise<any> {
    context.logger.info('更新配置', params);

    // 这里应该实现具体的配置更新逻辑
    // 例如：更新项目配置、系统配置等
    
    return {
      configType: params.configType,
      configId: params.configId,
      updatedFields: Object.keys(params.updates || {}),
      updatedAt: new Date().toISOString()
    };
  }

  /**
   * 数据备份
   */
  private async backupData(params: any, context: ExecutionContext): Promise<any> {
    context.logger.info('执行数据备份', params);

    // 模拟数据备份过程
    await this.sleep(2000);

    return {
      backupType: params.backupType || 'full',
      backupPath: params.backupPath || `/backup/${Date.now()}`,
      dataSize: params.estimatedSize || '100MB',
      backedUpAt: new Date().toISOString()
    };
  }

  /**
   * 清理资源
   */
  private async cleanupResources(params: any, context: ExecutionContext): Promise<any> {
    context.logger.info('清理资源', params);

    // 模拟资源清理过程
    await this.sleep(1000);

    const cleanedResources = [
      'temporary_files',
      'old_logs',
      'unused_cache'
    ];

    return {
      resourceTypes: params.resourceTypes || cleanedResources,
      freedSpace: '500MB',
      cleanedAt: new Date().toISOString()
    };
  }

  /**
   * 发送通知
   */
  private async sendNotification(params: any, context: ExecutionContext): Promise<any> {
    context.logger.info('发送通知', params);

    // 这里应该调用实际的通知服务
    // 为了演示，我们只是记录日志
    
    return {
      notificationType: params.type || 'email',
      recipients: params.recipients || [],
      message: params.message,
      sentAt: new Date().toISOString()
    };
  }

  /**
   * 执行自定义脚本
   */
  private async executeCustomScript(params: any, context: ExecutionContext): Promise<any> {
    context.logger.info('执行自定义脚本', params);

    // 安全注意：在实际项目中，执行自定义脚本需要严格的安全控制
    // 这里只是一个示例实现
    
    if (!params.scriptType || !params.scriptContent) {
      throw new Error('自定义脚本缺少必要参数');
    }

    // 模拟脚本执行
    await this.sleep(1500);

    return {
      scriptType: params.scriptType,
      executionResult: 'success',
      output: params.expectedOutput || 'Script executed successfully',
      executedAt: new Date().toISOString()
    };
  }

  /**
   * 验证特定动作的参数
   */
  private validateActionSpecificParams(action: string, parameters: any): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    switch (action) {
      case 'create_service_ticket':
        if (!parameters.title) errors.push('创建服务工单需要标题');
        if (!parameters.customerId) errors.push('创建服务工单需要客户ID');
        break;

      case 'update_service_status':
        if (!parameters.serviceId) errors.push('更新服务状态需要服务ID');
        if (!parameters.status) errors.push('更新服务状态需要指定状态');
        break;

      case 'assign_service_engineer':
        if (!parameters.serviceId) errors.push('分配工程师需要服务ID');
        if (!parameters.engineerId) errors.push('分配工程师需要工程师ID');
        break;

      case 'calculate_sla_compliance':
        if (!parameters.startDate || !parameters.endDate) {
          errors.push('SLA合规性计算需要时间范围');
        }
        break;

      case 'generate_report':
        if (!parameters.reportType) errors.push('生成报告需要指定报告类型');
        break;

      case 'custom_script':
        if (!parameters.scriptType || !parameters.scriptContent) {
          errors.push('自定义脚本需要脚本类型和内容');
        }
        warnings.push('执行自定义脚本存在安全风险，请谨慎使用');
        break;
    }

    return { valid: errors.length === 0, errors, warnings };
  }

  /**
   * 生成服务汇总报告
   */
  private async generateServiceSummaryReport(params: any, context: ExecutionContext): Promise<any> {
    const services = await prisma.service.findMany({
      where: {
        createdAt: {
          gte: new Date(params.startDate),
          lte: new Date(params.endDate)
        }
      }
    });

    const summary = {
      totalServices: services.length,
      byStatus: services.reduce((acc, service) => {
        acc[service.status] = (acc[service.status] || 0) + 1;
        return acc;
      }, {} as Record<string, number>),
      byPriority: services.reduce((acc, service) => {
        acc[service.priority] = (acc[service.priority] || 0) + 1;
        return acc;
      }, {} as Record<string, number>)
    };

    return summary;
  }

  /**
   * 生成SLA合规性报告
   */
  private async generateSlaComplianceReport(params: any, context: ExecutionContext): Promise<any> {
    return await this.calculateSlaCompliance(params, context);
  }

  /**
   * 生成客户满意度报告
   */
  private async generateCustomerSatisfactionReport(params: any, context: ExecutionContext): Promise<any> {
    // 这里应该查询客户反馈数据
    // 为了演示，返回模拟数据
    return {
      averageRating: 4.2,
      totalResponses: 150,
      satisfactionRate: 85.3,
      period: { startDate: params.startDate, endDate: params.endDate }
    };
  }

  /**
   * 获取SLA小时数
   */
  private getSlaHours(priority: string): number {
    switch (priority) {
      case 'URGENT': return 4;
      case 'HIGH': return 8;
      case 'MEDIUM': return 24;
      case 'LOW': return 72;
      default: return 24;
    }
  }

  /**
   * 带超时的执行
   */
  private async executeWithTimeout<T>(fn: () => Promise<T>, timeout: number): Promise<T> {
    return new Promise<T>((resolve, reject) => {
      const timer = setTimeout(() => {
        reject(new Error('业务动作执行超时'));
      }, timeout);

      fn()
        .then(resolve)
        .catch(reject)
        .finally(() => clearTimeout(timer));
    });
  }

  /**
   * 处理业务错误
   */
  private async handleBusinessError(
    error: any,
    logs: ExecutionLog[],
    context: ExecutionContext,
    config: BusinessActionConfig
  ): Promise<ActionExecutionResult> {
    let errorType = ErrorType.EXECUTION_ERROR;
    let errorCode = 'BUSINESS_ACTION_FAILED';
    let errorMessage = error.message || '业务动作执行失败';
    let shouldRetry = false;
    let severity = ErrorSeverity.MEDIUM;

    // 分析错误类型
    if (error.message?.includes('timeout') || error.message?.includes('超时')) {
      errorType = ErrorType.TIMEOUT_ERROR;
      errorCode = 'BUSINESS_ACTION_TIMEOUT';
      errorMessage = '业务动作执行超时';
      shouldRetry = true;
    } else if (error.message?.includes('not found') || error.message?.includes('未找到')) {
      errorType = ErrorType.DATA_ERROR;
      errorCode = 'BUSINESS_DATA_NOT_FOUND';
      errorMessage = '业务数据不存在';
      shouldRetry = false;
    } else if (error.message?.includes('permission') || error.message?.includes('权限')) {
      errorType = ErrorType.AUTHENTICATION_ERROR;
      errorCode = 'BUSINESS_PERMISSION_DENIED';
      errorMessage = '业务操作权限不足';
      shouldRetry = false;
      severity = ErrorSeverity.HIGH;
    } else if (error.message?.includes('validation') || error.message?.includes('验证')) {
      errorType = ErrorType.VALIDATION_ERROR;
      errorCode = 'BUSINESS_VALIDATION_FAILED';
      errorMessage = '业务数据验证失败';
      shouldRetry = false;
    }

    this.addLog(logs, 'ERROR', errorMessage, context, {
      errorCode,
      errorType,
      action: config.action,
      originalError: error.message
    });

    const executionError = this.createError(
      errorType,
      errorCode,
      errorMessage,
      {
        originalError: error.message,
        action: config.action,
        parameters: config.parameters
      },
      shouldRetry,
      severity
    );

    return this.createFailureResult(executionError, logs, shouldRetry);
  }

  /**
   * 回滚业务动作
   */
  protected async doRollback(step: WorkflowStep, context: ExecutionContext): Promise<RollbackResult> {
    const config = step.config as BusinessActionConfig;
    const logs: ExecutionLog[] = [];
    
    // 检查是否有回滚动作配置
    if (!config.rollbackAction) {
      this.addLog(logs, 'WARN', `业务动作 ${config.action} 未配置回滚操作`, context);
      
      return {
        success: true,
        logs
      };
    }

    try {
      this.addLog(logs, 'INFO', `执行业务动作回滚: ${config.rollbackAction.action}`, context);
      
      const rollbackHandler = this.businessHandlers.get(config.rollbackAction.action);
      if (!rollbackHandler) {
        throw new Error(`未找到回滚动作处理器: ${config.rollbackAction.action}`);
      }

      const rollbackResult = await rollbackHandler(config.rollbackAction.parameters || {}, context);
      
      this.addLog(logs, 'INFO', `业务动作回滚成功: ${config.rollbackAction.action}`, context);
      
      return {
        success: true,
        logs,
        restoredState: {
          rollbackAction: config.rollbackAction.action,
          rollbackResult: rollbackResult
        }
      };
      
    } catch (error: any) {
      const rollbackError = this.createError(
        ErrorType.EXECUTION_ERROR,
        'BUSINESS_ROLLBACK_FAILED',
        `业务动作回滚失败: ${error.message}`
      );
      
      this.addLog(logs, 'ERROR', `业务动作回滚失败: ${error.message}`, context);
      
      return {
        success: false,
        error: rollbackError,
        logs
      };
    }
  }
}

// 声明类型扩展
declare module '@/types/action-executor.types' {
  interface BusinessActionConfig {
    action: string;
    parameters?: Record<string, any>;
    timeout?: number;
    rollbackAction?: {
      action: string;
      parameters?: Record<string, any>;
    };
    validation?: {
      rules: string[];
      strict?: boolean;
    };
  }
}