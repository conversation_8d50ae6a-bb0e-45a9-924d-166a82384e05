/**
 * 条件动作执行器
 * 负责执行条件判断和分支逻辑
 */

import { WorkflowStepType } from '@prisma/client';
import { BaseActionExecutor } from './base-action-executor';
import type {
  WorkflowStep,
  ExecutionContext,
  ActionExecutionResult,
  ValidationResult,
  RollbackResult,
  ConditionalActionConfig,
  ErrorType,
  ErrorSeverity
} from '@/types/action-executor.types';

/**
 * 条件表达式类型
 */
type ConditionExpression = {
  operator: 'equals' | 'not_equals' | 'greater_than' | 'less_than' | 'greater_equal' | 'less_equal' | 'contains' | 'not_contains' | 'exists' | 'not_exists' | 'in' | 'not_in' | 'regex' | 'and' | 'or' | 'not';
  left?: any;
  right?: any;
  conditions?: ConditionExpression[];
};

/**
 * 分支动作配置
 */
type BranchAction = {
  type: 'continue' | 'skip' | 'fail' | 'retry' | 'custom';
  data?: any;
  message?: string;
};

/**
 * 条件动作执行器
 */
export class ConditionalActionExecutor extends BaseActionExecutor {
  public readonly type = WorkflowStepType.CONDITIONAL;
  public readonly name = '条件判断执行器';
  public readonly version = '1.0.0';

  constructor() {
    super();
  }

  /**
   * 初始化执行器
   */
  protected async doInitialize(): Promise<void> {
    console.log('✅ 条件判断执行器初始化完成');
  }

  /**
   * 健康检查
   */
  protected async doHealthCheck(): Promise<boolean> {
    return true;
  }

  /**
   * 验证步骤配置
   */
  protected async doValidate(step: WorkflowStep): Promise<ValidationResult> {
    const config = step.config as ConditionalActionConfig;
    
    // 使用基础验证方法
    const baseValidation = this.validateConfig(
      config,
      ['condition'], // 必需字段
      ['trueAction', 'falseAction', 'timeout', 'defaultResult'] // 可选字段
    );

    if (!baseValidation.valid) {
      return baseValidation;
    }

    const errors: string[] = [];
    const warnings: string[] = [];

    // 验证条件表达式
    try {
      this.validateConditionExpression(config.condition);
    } catch (error: any) {
      errors.push(`条件表达式验证失败: ${error.message}`);
    }

    // 验证分支动作
    if (config.trueAction) {
      const trueActionValidation = this.validateBranchAction(config.trueAction, 'trueAction');
      errors.push(...trueActionValidation.errors);
      warnings.push(...trueActionValidation.warnings);
    }

    if (config.falseAction) {
      const falseActionValidation = this.validateBranchAction(config.falseAction, 'falseAction');
      errors.push(...falseActionValidation.errors);
      warnings.push(...falseActionValidation.warnings);
    }

    // 验证超时设置
    if (config.timeout && (config.timeout < 100 || config.timeout > 60000)) {
      warnings.push('建议条件判断超时时间设置在100ms到1分钟之间');
    }

    // 如果没有配置任何分支动作，给出警告
    if (!config.trueAction && !config.falseAction) {
      warnings.push('未配置任何分支动作，条件判断结果将仅返回布尔值');
    }

    return {
      valid: errors.length === 0,
      errors: [...baseValidation.errors, ...errors],
      warnings: [...baseValidation.warnings, ...warnings]
    };
  }

  /**
   * 执行条件判断
   */
  protected async doExecute(step: WorkflowStep, context: ExecutionContext): Promise<ActionExecutionResult> {
    const config = step.config as ConditionalActionConfig;
    const logs: ExecutionLog[] = [];

    try {
      // 解析配置中的变量
      const resolvedConfig = this.resolveValue(config, context) as ConditionalActionConfig;

      this.addLog(logs, 'INFO', `开始执行条件判断`, context, {
        conditionType: resolvedConfig.condition.operator,
        hasVariables: this.hasVariables(JSON.stringify(resolvedConfig.condition))
      });

      // 评估条件表达式
      const conditionResult = await this.evaluateCondition(resolvedConfig.condition, context);

      this.addLog(logs, 'INFO', `条件判断结果: ${conditionResult}`, context, {
        result: conditionResult,
        condition: this.sanitizeCondition(resolvedConfig.condition)
      });

      // 执行相应的分支动作
      const branchResult = await this.executeBranchAction(
        conditionResult,
        resolvedConfig,
        context,
        logs
      );

      const responseData = {
        conditionResult: conditionResult,
        branchExecuted: conditionResult ? 'true' : 'false',
        branchResult: branchResult,
        evaluatedAt: new Date().toISOString()
      };

      return this.createSuccessResult(responseData, logs);

    } catch (error: any) {
      return await this.handleConditionalError(error, logs, context, config);
    }
  }

  /**
   * 评估条件表达式
   */
  private async evaluateCondition(condition: ConditionExpression, context: ExecutionContext): Promise<boolean> {
    // 检查中止信号
    if (this.checkAbortSignal(context)) {
      throw new Error('条件评估已被中止');
    }

    const operator = condition.operator;

    try {
      switch (operator) {
        case 'equals':
          return this.resolveValue(condition.left, context) === this.resolveValue(condition.right, context);

        case 'not_equals':
          return this.resolveValue(condition.left, context) !== this.resolveValue(condition.right, context);

        case 'greater_than':
          return Number(this.resolveValue(condition.left, context)) > Number(this.resolveValue(condition.right, context));

        case 'less_than':
          return Number(this.resolveValue(condition.left, context)) < Number(this.resolveValue(condition.right, context));

        case 'greater_equal':
          return Number(this.resolveValue(condition.left, context)) >= Number(this.resolveValue(condition.right, context));

        case 'less_equal':
          return Number(this.resolveValue(condition.left, context)) <= Number(this.resolveValue(condition.right, context));

        case 'contains':
          const leftValue = String(this.resolveValue(condition.left, context));
          const rightValue = String(this.resolveValue(condition.right, context));
          return leftValue.includes(rightValue);

        case 'not_contains':
          const leftVal = String(this.resolveValue(condition.left, context));
          const rightVal = String(this.resolveValue(condition.right, context));
          return !leftVal.includes(rightVal);

        case 'exists':
          const existsValue = this.resolveValue(condition.left, context);
          return existsValue !== undefined && existsValue !== null && existsValue !== '';

        case 'not_exists':
          const notExistsValue = this.resolveValue(condition.left, context);
          return notExistsValue === undefined || notExistsValue === null || notExistsValue === '';

        case 'in':
          const inValue = this.resolveValue(condition.left, context);
          const inArray = this.resolveValue(condition.right, context);
          return Array.isArray(inArray) && inArray.includes(inValue);

        case 'not_in':
          const notInValue = this.resolveValue(condition.left, context);
          const notInArray = this.resolveValue(condition.right, context);
          return Array.isArray(notInArray) && !notInArray.includes(notInValue);

        case 'regex':
          const regexValue = String(this.resolveValue(condition.left, context));
          const regexPattern = String(this.resolveValue(condition.right, context));
          const regex = new RegExp(regexPattern);
          return regex.test(regexValue);

        case 'and':
          if (!condition.conditions || !Array.isArray(condition.conditions)) {
            throw new Error('AND操作符需要conditions数组');
          }
          for (const subCondition of condition.conditions) {
            if (!(await this.evaluateCondition(subCondition, context))) {
              return false;
            }
          }
          return true;

        case 'or':
          if (!condition.conditions || !Array.isArray(condition.conditions)) {
            throw new Error('OR操作符需要conditions数组');
          }
          for (const subCondition of condition.conditions) {
            if (await this.evaluateCondition(subCondition, context)) {
              return true;
            }
          }
          return false;

        case 'not':
          if (!condition.conditions || condition.conditions.length !== 1) {
            throw new Error('NOT操作符需要恰好一个子条件');
          }
          return !(await this.evaluateCondition(condition.conditions[0], context));

        default:
          throw new Error(`不支持的条件操作符: ${operator}`);
      }
    } catch (error: any) {
      throw new Error(`条件评估失败: ${error.message}`);
    }
  }

  /**
   * 执行分支动作
   */
  private async executeBranchAction(
    conditionResult: boolean,
    config: ConditionalActionConfig,
    context: ExecutionContext,
    logs: ExecutionLog[]
  ): Promise<any> {
    const branchAction = conditionResult ? config.trueAction : config.falseAction;

    if (!branchAction) {
      this.addLog(logs, 'INFO', `没有配置${conditionResult ? 'true' : 'false'}分支动作`, context);
      return { action: 'none', message: `条件结果为${conditionResult}，无配置动作` };
    }

    this.addLog(logs, 'INFO', `执行${conditionResult ? 'true' : 'false'}分支动作: ${branchAction.type}`, context);

    switch (branchAction.type) {
      case 'continue':
        return {
          action: 'continue',
          message: branchAction.message || '继续执行',
          data: branchAction.data
        };

      case 'skip':
        return {
          action: 'skip',
          message: branchAction.message || '跳过后续步骤',
          data: branchAction.data
        };

      case 'fail':
        throw new Error(branchAction.message || '条件分支要求失败');

      case 'retry':
        return {
          action: 'retry',
          message: branchAction.message || '重试当前步骤',
          data: branchAction.data
        };

      case 'custom':
        return {
          action: 'custom',
          message: branchAction.message || '执行自定义动作',
          data: branchAction.data
        };

      default:
        throw new Error(`不支持的分支动作类型: ${branchAction.type}`);
    }
  }

  /**
   * 验证条件表达式
   */
  private validateConditionExpression(condition: ConditionExpression): void {
    if (!condition || typeof condition !== 'object') {
      throw new Error('条件表达式必须是对象');
    }

    if (!condition.operator) {
      throw new Error('条件表达式缺少operator字段');
    }

    const validOperators = [
      'equals', 'not_equals', 'greater_than', 'less_than', 'greater_equal', 'less_equal',
      'contains', 'not_contains', 'exists', 'not_exists', 'in', 'not_in', 'regex',
      'and', 'or', 'not'
    ];

    if (!validOperators.includes(condition.operator)) {
      throw new Error(`无效的条件操作符: ${condition.operator}`);
    }

    // 验证复合条件
    if (['and', 'or', 'not'].includes(condition.operator)) {
      if (!condition.conditions || !Array.isArray(condition.conditions)) {
        throw new Error(`${condition.operator}操作符需要conditions数组`);
      }

      if (condition.operator === 'not' && condition.conditions.length !== 1) {
        throw new Error('NOT操作符需要恰好一个子条件');
      }

      // 递归验证子条件
      for (const subCondition of condition.conditions) {
        this.validateConditionExpression(subCondition);
      }
    } else if (condition.operator === 'exists' || condition.operator === 'not_exists') {
      // 存在性检查只需要left参数
      if (condition.left === undefined) {
        throw new Error(`${condition.operator}操作符需要left参数`);
      }
    } else {
      // 其他操作符需要left和right参数
      if (condition.left === undefined || condition.right === undefined) {
        throw new Error(`${condition.operator}操作符需要left和right参数`);
      }
    }
  }

  /**
   * 验证分支动作
   */
  private validateBranchAction(branchAction: BranchAction, branchName: string): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    if (!branchAction || typeof branchAction !== 'object') {
      errors.push(`${branchName}必须是对象`);
      return { valid: false, errors, warnings };
    }

    if (!branchAction.type) {
      errors.push(`${branchName}缺少type字段`);
    } else {
      const validTypes = ['continue', 'skip', 'fail', 'retry', 'custom'];
      if (!validTypes.includes(branchAction.type)) {
        errors.push(`${branchName}的type值无效: ${branchAction.type}`);
      }
    }

    if (branchAction.type === 'fail' && !branchAction.message) {
      warnings.push(`${branchName}为fail类型时建议设置错误消息`);
    }

    return { valid: errors.length === 0, errors, warnings };
  }

  /**
   * 检查是否包含变量
   */
  private hasVariables(str: string): boolean {
    return /\$\{[^}]+\}/.test(str) || /\{\{[^}]+\}\}/.test(str);
  }

  /**
   * 清理条件表达式（用于日志记录）
   */
  private sanitizeCondition(condition: ConditionExpression): any {
    // 创建条件的副本，隐藏敏感信息
    const sanitized: any = { ...condition };
    
    if (typeof sanitized.left === 'string' && sanitized.left.length > 50) {
      sanitized.left = sanitized.left.substring(0, 50) + '...';
    }
    
    if (typeof sanitized.right === 'string' && sanitized.right.length > 50) {
      sanitized.right = sanitized.right.substring(0, 50) + '...';
    }

    if (sanitized.conditions) {
      sanitized.conditions = sanitized.conditions.map((cond: any) => this.sanitizeCondition(cond));
    }

    return sanitized;
  }

  /**
   * 处理条件判断错误
   */
  private async handleConditionalError(
    error: any,
    logs: ExecutionLog[],
    context: ExecutionContext,
    config: ConditionalActionConfig
  ): Promise<ActionExecutionResult> {
    let errorType = ErrorType.EXECUTION_ERROR;
    let errorCode = 'CONDITIONAL_EXECUTION_FAILED';
    let errorMessage = error.message || '条件判断执行失败';
    let shouldRetry = false;
    let severity = ErrorSeverity.MEDIUM;

    // 分析错误类型
    if (error.message?.includes('timeout') || error.message?.includes('超时')) {
      errorType = ErrorType.TIMEOUT_ERROR;
      errorCode = 'CONDITIONAL_TIMEOUT';
      errorMessage = '条件判断超时';
      shouldRetry = true;
    } else if (error.message?.includes('验证') || error.message?.includes('validation')) {
      errorType = ErrorType.VALIDATION_ERROR;
      errorCode = 'CONDITIONAL_VALIDATION_FAILED';
      errorMessage = '条件表达式验证失败';
      shouldRetry = false;
      severity = ErrorSeverity.HIGH;
    } else if (error.message?.includes('条件评估')) {
      errorType = ErrorType.DATA_ERROR;
      errorCode = 'CONDITIONAL_EVALUATION_FAILED';
      errorMessage = '条件评估失败';
      shouldRetry = false;
    } else if (error.message?.includes('中止')) {
      errorType = ErrorType.EXECUTION_ERROR;
      errorCode = 'CONDITIONAL_ABORTED';
      errorMessage = '条件判断被中止';
      shouldRetry = false;
    }

    this.addLog(logs, 'ERROR', errorMessage, context, {
      errorCode,
      errorType,
      originalError: error.message,
      conditionOperator: config.condition?.operator
    });

    const executionError = this.createError(
      errorType,
      errorCode,
      errorMessage,
      {
        originalError: error.message,
        condition: this.sanitizeCondition(config.condition),
        stack: error.stack
      },
      shouldRetry,
      severity
    );

    return this.createFailureResult(executionError, logs, shouldRetry);
  }

  /**
   * 回滚条件判断（通常不需要）
   */
  protected async doRollback(step: WorkflowStep, context: ExecutionContext): Promise<RollbackResult> {
    const logs: ExecutionLog[] = [];
    
    // 条件判断通常不需要回滚，因为它只是评估条件并返回结果
    this.addLog(logs, 'INFO', '条件判断操作无需回滚', context);
    
    return {
      success: true,
      logs,
      restoredState: {
        message: '条件判断操作无状态变更，无需回滚'
      }
    };
  }
}

// 声明类型扩展
declare module '@/types/action-executor.types' {
  interface ConditionalActionConfig {
    condition: {
      operator: 'equals' | 'not_equals' | 'greater_than' | 'less_than' | 'greater_equal' | 'less_equal' | 'contains' | 'not_contains' | 'exists' | 'not_exists' | 'in' | 'not_in' | 'regex' | 'and' | 'or' | 'not';
      left?: any;
      right?: any;
      conditions?: Array<any>;
    };
    trueAction?: {
      type: 'continue' | 'skip' | 'fail' | 'retry' | 'custom';
      data?: any;
      message?: string;
    };
    falseAction?: {
      type: 'continue' | 'skip' | 'fail' | 'retry' | 'custom';
      data?: any;
      message?: string;
    };
    timeout?: number;
    defaultResult?: boolean;
  }
}