/**
 * 数据库动作执行器
 * 负责执行数据库查询和操作
 */

import { WorkflowStepType } from '@prisma/client';
import { BaseActionExecutor } from './base-action-executor';
import { prisma } from '@/config/database.config';
import type {
  WorkflowStep,
  ExecutionContext,
  ActionExecutionResult,
  ValidationResult,
  RollbackResult,
  DatabaseActionConfig,
  ErrorType,
  ErrorSeverity
} from '@/types/action-executor.types';

/**
 * 数据库动作执行器
 */
export class DatabaseActionExecutor extends BaseActionExecutor {
  public readonly type = WorkflowStepType.DATABASE_QUERY;
  public readonly name = '数据库查询执行器';
  public readonly version = '1.0.0';

  private connectionPool = new Map<string, any>();

  constructor() {
    super();
  }

  /**
   * 初始化执行器
   */
  protected async doInitialize(): Promise<void> {
    try {
      // 测试数据库连接
      await prisma.$queryRaw`SELECT 1 as test`;
      console.log('✅ 数据库连接测试成功');
    } catch (error) {
      console.error('❌ 数据库连接测试失败:', error);
      throw error;
    }
  }

  /**
   * 健康检查
   */
  protected async doHealthCheck(): Promise<boolean> {
    try {
      await prisma.$queryRaw`SELECT 1 as health_check`;
      return true;
    } catch {
      return false;
    }
  }

  /**
   * 验证步骤配置
   */
  protected async doValidate(step: WorkflowStep): Promise<ValidationResult> {
    const config = step.config as DatabaseActionConfig;
    
    // 使用基础验证方法
    const baseValidation = this.validateConfig(
      config,
      ['operation', 'query'], // 必需字段
      ['parameters', 'timeout', 'transactional', 'rollbackQuery'] // 可选字段
    );

    if (!baseValidation.valid) {
      return baseValidation;
    }

    const errors: string[] = [];
    const warnings: string[] = [];

    // 验证操作类型
    const validOperations = ['SELECT', 'INSERT', 'UPDATE', 'DELETE', 'EXECUTE'];
    if (!validOperations.includes(config.operation.toUpperCase())) {
      errors.push(`不支持的数据库操作: ${config.operation}`);
    }

    // 验证SQL查询
    if (!config.query || typeof config.query !== 'string') {
      errors.push('SQL查询语句不能为空');
    } else {
      // 基础SQL注入检查
      const dangerousPatterns = [
        /;\s*(drop|delete|truncate|alter)\s+/i,
        /union\s+select/i,
        /\/\*[\s\S]*?\*\//,
        /--\s*$/m
      ];

      for (const pattern of dangerousPatterns) {
        if (pattern.test(config.query)) {
          errors.push('检测到潜在的SQL注入风险');
          break;
        }
      }

      // 检查参数化查询
      const parameterCount = (config.query.match(/\$/g) || []).length;
      const providedParams = config.parameters ? Object.keys(config.parameters).length : 0;
      
      if (parameterCount > 0 && parameterCount !== providedParams) {
        warnings.push(`参数数量不匹配: 查询需要${parameterCount}个参数，提供了${providedParams}个`);
      }
    }

    // 验证超时设置
    if (config.timeout && (config.timeout < 1000 || config.timeout > 300000)) {
      warnings.push('建议数据库查询超时时间设置在1秒到5分钟之间');
    }

    return {
      valid: errors.length === 0,
      errors: [...baseValidation.errors, ...errors],
      warnings: [...baseValidation.warnings, ...warnings]
    };
  }

  /**
   * 执行数据库查询
   */
  protected async doExecute(step: WorkflowStep, context: ExecutionContext): Promise<ActionExecutionResult> {
    const config = step.config as DatabaseActionConfig;
    const logs: ExecutionLog[] = [];

    try {
      // 解析配置中的变量
      const resolvedConfig = this.resolveValue(config, context) as DatabaseActionConfig;

      this.addLog(logs, 'INFO', `执行数据库操作: ${resolvedConfig.operation}`, context, {
        operation: resolvedConfig.operation,
        hasParameters: !!resolvedConfig.parameters
      });

      // 构建查询参数
      const queryParams = this.buildQueryParameters(resolvedConfig, context);

      // 执行数据库操作
      const result = await this.executeQuery(resolvedConfig, queryParams, context);

      // 记录成功日志
      this.addLog(logs, 'INFO', `数据库操作完成`, context, {
        operation: resolvedConfig.operation,
        affectedRows: result.count || result.length || 0
      });

      const responseData = {
        operation: resolvedConfig.operation,
        success: true,
        data: result,
        affectedRows: result.count || (Array.isArray(result) ? result.length : 1),
        executedQuery: this.sanitizeQuery(resolvedConfig.query, queryParams)
      };

      return this.createSuccessResult(responseData, logs);

    } catch (error: any) {
      return await this.handleDatabaseError(error, logs, context, config);
    }
  }

  /**
   * 构建查询参数
   */
  private buildQueryParameters(config: DatabaseActionConfig, context: ExecutionContext): any[] {
    if (!config.parameters) {
      return [];
    }

    const parameters: any[] = [];
    const resolvedParams = this.resolveValue(config.parameters, context);

    // 将参数对象转换为数组（按$1, $2, $3...的顺序）
    for (let i = 1; i <= Object.keys(resolvedParams).length; i++) {
      const paramKey = `$${i}`;
      if (resolvedParams[paramKey] !== undefined) {
        parameters.push(resolvedParams[paramKey]);
      }
    }

    return parameters;
  }

  /**
   * 执行数据库查询
   */
  private async executeQuery(
    config: DatabaseActionConfig,
    parameters: any[],
    context: ExecutionContext
  ): Promise<any> {
    // 检查中止信号
    if (this.checkAbortSignal(context)) {
      throw new Error('查询已被中止');
    }

    const timeout = config.timeout || 30000;
    const operation = config.operation.toUpperCase();

    // 设置查询超时
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('数据库查询超时')), timeout);
    });

    try {
      let queryPromise: Promise<any>;

      // 根据操作类型执行不同的查询
      switch (operation) {
        case 'SELECT':
          queryPromise = prisma.$queryRawUnsafe(config.query, ...parameters);
          break;

        case 'INSERT':
        case 'UPDATE':
        case 'DELETE':
          queryPromise = prisma.$executeRawUnsafe(config.query, ...parameters);
          break;

        case 'EXECUTE':
          // 执行存储过程或函数
          queryPromise = prisma.$queryRawUnsafe(config.query, ...parameters);
          break;

        default:
          throw new Error(`不支持的数据库操作: ${operation}`);
      }

      // 如果需要事务处理
      if (config.transactional) {
        return await prisma.$transaction(async (tx) => {
          return await Promise.race([queryPromise, timeoutPromise]);
        });
      } else {
        return await Promise.race([queryPromise, timeoutPromise]);
      }

    } catch (error: any) {
      // 重新抛出错误，让上层处理
      throw error;
    }
  }

  /**
   * 处理数据库错误
   */
  private async handleDatabaseError(
    error: any,
    logs: ExecutionLog[],
    context: ExecutionContext,
    config: DatabaseActionConfig
  ): Promise<ActionExecutionResult> {
    let errorType = ErrorType.SYSTEM_ERROR;
    let errorCode = 'DATABASE_OPERATION_FAILED';
    let errorMessage = error.message || '数据库操作失败';
    let shouldRetry = false;
    let severity = ErrorSeverity.MEDIUM;

    // 分析数据库错误类型
    if (error.message?.includes('timeout') || error.message?.includes('超时')) {
      errorType = ErrorType.TIMEOUT_ERROR;
      errorCode = 'DATABASE_QUERY_TIMEOUT';
      errorMessage = '数据库查询超时';
      shouldRetry = true;
      severity = ErrorSeverity.MEDIUM;
    } else if (error.code === 'P2002') {
      // Prisma 唯一约束违反
      errorType = ErrorType.DATA_ERROR;
      errorCode = 'UNIQUE_CONSTRAINT_VIOLATION';
      errorMessage = '违反唯一约束条件';
      shouldRetry = false;
      severity = ErrorSeverity.LOW;
    } else if (error.code === 'P2003') {
      // Prisma 外键约束违反
      errorType = ErrorType.DATA_ERROR;
      errorCode = 'FOREIGN_KEY_CONSTRAINT_VIOLATION';
      errorMessage = '违反外键约束条件';
      shouldRetry = false;
      severity = ErrorSeverity.MEDIUM;
    } else if (error.code === 'P2025') {
      // Prisma 记录不存在
      errorType = ErrorType.DATA_ERROR;
      errorCode = 'RECORD_NOT_FOUND';
      errorMessage = '未找到相关记录';
      shouldRetry = false;
      severity = ErrorSeverity.LOW;
    } else if (error.message?.includes('syntax error') || error.message?.includes('语法错误')) {
      errorType = ErrorType.VALIDATION_ERROR;
      errorCode = 'SQL_SYNTAX_ERROR';
      errorMessage = 'SQL语法错误';
      shouldRetry = false;
      severity = ErrorSeverity.HIGH;
    } else if (error.message?.includes('permission') || error.message?.includes('权限')) {
      errorType = ErrorType.AUTHENTICATION_ERROR;
      errorCode = 'DATABASE_PERMISSION_DENIED';
      errorMessage = '数据库权限不足';
      shouldRetry = false;
      severity = ErrorSeverity.HIGH;
    } else if (error.message?.includes('connection') || error.message?.includes('连接')) {
      errorType = ErrorType.NETWORK_ERROR;
      errorCode = 'DATABASE_CONNECTION_FAILED';
      errorMessage = '数据库连接失败';
      shouldRetry = true;
      severity = ErrorSeverity.HIGH;
    }

    this.addLog(logs, 'ERROR', errorMessage, context, {
      errorCode,
      errorType,
      operation: config.operation,
      originalError: error.message,
      sqlState: error.code
    });

    const executionError = this.createError(
      errorType,
      errorCode,
      errorMessage,
      {
        originalError: error.message,
        sqlState: error.code,
        operation: config.operation,
        query: this.sanitizeQuery(config.query)
      },
      shouldRetry,
      severity
    );

    return this.createFailureResult(executionError, logs, shouldRetry);
  }

  /**
   * 回滚数据库操作
   */
  protected async doRollback(step: WorkflowStep, context: ExecutionContext): Promise<RollbackResult> {
    const config = step.config as DatabaseActionConfig;
    const logs: ExecutionLog[] = [];
    
    // 检查是否有回滚查询
    if (!config.rollbackQuery) {
      this.addLog(logs, 'WARN', '未配置回滚查询，无法执行回滚', context);
      
      return {
        success: true,
        logs
      };
    }

    try {
      this.addLog(logs, 'INFO', '执行数据库回滚查询', context);
      
      const rollbackConfig = {
        ...config,
        query: config.rollbackQuery,
        operation: 'EXECUTE'
      } as DatabaseActionConfig;

      const resolvedConfig = this.resolveValue(rollbackConfig, context) as DatabaseActionConfig;
      const queryParams = this.buildQueryParameters(resolvedConfig, context);
      
      const result = await this.executeQuery(resolvedConfig, queryParams, context);
      
      this.addLog(logs, 'INFO', '数据库回滚成功', context, {
        affectedRows: result.count || 0
      });
      
      return {
        success: true,
        logs,
        restoredState: {
          rollbackResult: {
            affectedRows: result.count || 0,
            executedQuery: this.sanitizeQuery(resolvedConfig.query, queryParams)
          }
        }
      };
      
    } catch (error: any) {
      const rollbackError = this.createError(
        ErrorType.EXECUTION_ERROR,
        'DATABASE_ROLLBACK_FAILED',
        `数据库回滚失败: ${error.message}`
      );
      
      this.addLog(logs, 'ERROR', `数据库回滚失败: ${error.message}`, context);
      
      return {
        success: false,
        error: rollbackError,
        logs
      };
    }
  }

  /**
   * 清理查询字符串（用于日志记录）
   */
  private sanitizeQuery(query: string, parameters?: any[]): string {
    if (!parameters || parameters.length === 0) {
      return query;
    }

    let sanitizedQuery = query;
    parameters.forEach((param, index) => {
      const placeholder = `$${index + 1}`;
      const sanitizedParam = typeof param === 'string' ? `'${param.substring(0, 50)}${param.length > 50 ? '...' : ''}'` : String(param);
      sanitizedQuery = sanitizedQuery.replace(new RegExp(`\\${placeholder}\\b`, 'g'), sanitizedParam);
    });

    return sanitizedQuery;
  }

  /**
   * 清理资源
   */
  protected async doCleanup(): Promise<void> {
    try {
      // 清理连接池
      for (const [key, connection] of this.connectionPool) {
        try {
          // 这里应该关闭具体的连接
          console.log(`关闭数据库连接: ${key}`);
        } catch (error) {
          console.error(`关闭数据库连接失败 ${key}:`, error);
        }
      }
      this.connectionPool.clear();
      
      console.log('✅ 数据库执行器清理完成');
    } catch (error) {
      console.error('❌ 数据库执行器清理失败:', error);
    }
  }
}

// 声明类型扩展
declare module '@/types/action-executor.types' {
  interface DatabaseActionConfig {
    operation: 'SELECT' | 'INSERT' | 'UPDATE' | 'DELETE' | 'EXECUTE';
    query: string;
    parameters?: Record<string, any>;
    timeout?: number;
    transactional?: boolean;
    rollbackQuery?: string;
  }
}