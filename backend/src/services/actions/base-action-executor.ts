/**
 * 基础动作执行器抽象类
 * 为所有具体的动作执行器提供通用功能和模板
 */

import { EventEmitter } from 'events';
import type { WorkflowStepType } from '@prisma/client';
import type {
  IActionExecutor,
  WorkflowStep,
  ExecutionContext,
  ActionExecutionResult,
  ValidationResult,
  RollbackResult,
  ExecutionError,
  ExecutionLog,
  ErrorType,
  ErrorSeverity
} from '@/types/action-executor.types';

/**
 * 基础动作执行器抽象类
 */
export abstract class BaseActionExecutor extends EventEmitter implements IActionExecutor {
  public abstract readonly type: WorkflowStepType;
  public abstract readonly name: string;
  public readonly version: string = '1.0.0';

  protected isInitialized = false;

  constructor() {
    super();
  }

  /**
   * 初始化执行器（可选实现）
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    try {
      await this.doInitialize();
      this.isInitialized = true;
      
      console.log(`✅ 执行器 ${this.name} 初始化完成`);
      this.emit('initialized');
      
    } catch (error) {
      console.error(`❌ 执行器 ${this.name} 初始化失败:`, error);
      this.emit('initialization:failed', { error });
      throw error;
    }
  }

  /**
   * 清理资源（可选实现）
   */
  async cleanup(): Promise<void> {
    try {
      await this.doCleanup();
      this.isInitialized = false;
      
      console.log(`🧹 执行器 ${this.name} 清理完成`);
      this.emit('cleanup:completed');
      
    } catch (error) {
      console.error(`❌ 执行器 ${this.name} 清理失败:`, error);
      this.emit('cleanup:failed', { error });
    }
  }

  /**
   * 健康检查（可选实现）
   */
  async healthCheck(): Promise<boolean> {
    try {
      if (!this.isInitialized) {
        return false;
      }
      
      return await this.doHealthCheck();
      
    } catch (error) {
      console.error(`❌ 执行器 ${this.name} 健康检查失败:`, error);
      return false;
    }
  }

  /**
   * 验证步骤配置
   */
  async validate(step: WorkflowStep): Promise<ValidationResult> {
    const errors: string[] = [];
    const warnings: string[] = [];

    try {
      // 基础验证
      if (!step.name) {
        errors.push('步骤名称不能为空');
      }

      if (!step.config) {
        errors.push('步骤配置不能为空');
      }

      if (step.type !== this.type) {
        errors.push(`步骤类型不匹配，期望: ${this.type}, 实际: ${step.type}`);
      }

      // 调用具体实现的验证
      const specificValidation = await this.doValidate(step);
      errors.push(...specificValidation.errors);
      warnings.push(...specificValidation.warnings);

    } catch (error: any) {
      errors.push(`验证过程异常: ${error.message}`);
    }

    return {
      valid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * 执行步骤
   */
  async execute(step: WorkflowStep, context: ExecutionContext): Promise<ActionExecutionResult> {
    const startTime = Date.now();
    const logs: ExecutionLog[] = [];

    try {
      // 检查初始化状态
      if (!this.isInitialized) {
        const error = this.createError(
          ErrorType.SYSTEM_ERROR,
          'EXECUTOR_NOT_INITIALIZED',
          `执行器 ${this.name} 未初始化`
        );
        return { success: false, error, logs };
      }

      // 检查中止信号
      if (context.abortController.signal.aborted) {
        const error = this.createError(
          ErrorType.EXECUTION_ERROR,
          'EXECUTION_ABORTED',
          '执行已被中止'
        );
        return { success: false, error, logs };
      }

      // 记录开始日志
      this.addLog(logs, 'INFO', `开始执行 ${this.name} 步骤: ${step.name}`, context);

      // 发出开始事件
      this.emit('execution:started', {
        executorType: this.type,
        stepName: step.name,
        executionId: context.executionId
      });

      // 执行前验证
      const validation = await this.validate(step);
      if (!validation.valid) {
        const error = this.createError(
          ErrorType.VALIDATION_ERROR,
          'VALIDATION_FAILED',
          `步骤验证失败: ${validation.errors.join(', ')}`
        );
        this.addLog(logs, 'ERROR', `验证失败: ${validation.errors.join(', ')}`, context);
        return { success: false, error, logs };
      }

      // 记录警告
      if (validation.warnings.length > 0) {
        this.addLog(logs, 'WARN', `验证警告: ${validation.warnings.join(', ')}`, context);
      }

      // 调用具体实现的执行逻辑
      const result = await this.doExecute(step, context);

      // 计算执行时间
      const executionTime = (Date.now() - startTime) / 1000;
      result.executionTime = executionTime;

      // 合并日志
      result.logs = [...logs, ...result.logs];

      // 记录完成日志
      this.addLog(
        result.logs,
        result.success ? 'INFO' : 'ERROR',
        `步骤执行${result.success ? '成功' : '失败'}: ${step.name} (${executionTime}s)`,
        context
      );

      // 发出完成事件
      this.emit('execution:completed', {
        executorType: this.type,
        stepName: step.name,
        executionId: context.executionId,
        success: result.success,
        executionTime
      });

      return result;

    } catch (error: any) {
      const executionTime = (Date.now() - startTime) / 1000;
      
      const executionError = this.createError(
        ErrorType.EXECUTION_ERROR,
        'EXECUTION_EXCEPTION',
        error.message || '执行过程中发生异常',
        { originalError: error, stack: error.stack }
      );

      this.addLog(logs, 'ERROR', `执行异常: ${error.message}`, context);

      // 发出失败事件
      this.emit('execution:failed', {
        executorType: this.type,
        stepName: step.name,
        executionId: context.executionId,
        error: executionError,
        executionTime
      });

      return {
        success: false,
        error: executionError,
        logs,
        executionTime
      };
    }
  }

  /**
   * 回滚步骤（可选实现）
   */
  async rollback?(step: WorkflowStep, context: ExecutionContext): Promise<RollbackResult> {
    const logs: ExecutionLog[] = [];

    try {
      this.addLog(logs, 'INFO', `开始回滚 ${this.name} 步骤: ${step.name}`, context);

      // 发出回滚开始事件
      this.emit('rollback:started', {
        executorType: this.type,
        stepName: step.name,
        executionId: context.executionId
      });

      // 调用具体实现的回滚逻辑
      const result = await this.doRollback(step, context);

      // 合并日志
      result.logs = [...logs, ...result.logs];

      // 记录回滚完成日志
      this.addLog(
        result.logs,
        result.success ? 'INFO' : 'ERROR',
        `步骤回滚${result.success ? '成功' : '失败'}: ${step.name}`,
        context
      );

      // 发出回滚完成事件
      this.emit('rollback:completed', {
        executorType: this.type,
        stepName: step.name,
        executionId: context.executionId,
        success: result.success
      });

      return result;

    } catch (error: any) {
      const rollbackError = this.createError(
        ErrorType.EXECUTION_ERROR,
        'ROLLBACK_EXCEPTION',
        error.message || '回滚过程中发生异常'
      );

      this.addLog(logs, 'ERROR', `回滚异常: ${error.message}`, context);

      // 发出回滚失败事件
      this.emit('rollback:failed', {
        executorType: this.type,
        stepName: step.name,
        executionId: context.executionId,
        error: rollbackError
      });

      return {
        success: false,
        error: rollbackError,
        logs
      };
    }
  }

  // ========== 抽象方法，子类必须实现 ==========

  /**
   * 具体的初始化逻辑
   */
  protected async doInitialize(): Promise<void> {
    // 默认空实现
  }

  /**
   * 具体的清理逻辑
   */
  protected async doCleanup(): Promise<void> {
    // 默认空实现
  }

  /**
   * 具体的健康检查逻辑
   */
  protected async doHealthCheck(): Promise<boolean> {
    return true; // 默认返回健康
  }

  /**
   * 具体的验证逻辑
   */
  protected abstract doValidate(step: WorkflowStep): Promise<ValidationResult>;

  /**
   * 具体的执行逻辑
   */
  protected abstract doExecute(step: WorkflowStep, context: ExecutionContext): Promise<ActionExecutionResult>;

  /**
   * 具体的回滚逻辑（可选实现）
   */
  protected async doRollback(step: WorkflowStep, context: ExecutionContext): Promise<RollbackResult> {
    // 默认不支持回滚
    return {
      success: true,
      logs: [{
        level: 'WARN',
        message: `执行器 ${this.name} 不支持回滚操作`,
        timestamp: new Date(),
        source: this.name
      }]
    };
  }

  // ========== 辅助方法 ==========

  /**
   * 创建执行错误
   */
  protected createError(
    type: ErrorType,
    code: string,
    message: string,
    details?: any,
    recoverable: boolean = true,
    severity: ErrorSeverity = ErrorSeverity.MEDIUM
  ): ExecutionError {
    return {
      type,
      code,
      message,
      details,
      recoverable,
      severity,
      timestamp: new Date()
    };
  }

  /**
   * 添加执行日志
   */
  protected addLog(
    logs: ExecutionLog[],
    level: ExecutionLog['level'],
    message: string,
    context: ExecutionContext,
    metadata?: any
  ): void {
    const log: ExecutionLog = {
      level,
      message,
      timestamp: new Date(),
      metadata,
      source: this.name
    };

    logs.push(log);

    // 同时记录到上下文日志器
    switch (level) {
      case 'DEBUG':
        context.logger.debug(message, metadata);
        break;
      case 'INFO':
        context.logger.info(message, metadata);
        break;
      case 'WARN':
        context.logger.warn(message, metadata);
        break;
      case 'ERROR':
        context.logger.error(message, undefined, metadata);
        break;
    }
  }

  /**
   * 解析变量值
   */
  protected resolveValue(value: any, context: ExecutionContext): any {
    if (typeof value === 'string') {
      // 支持变量替换: ${variableName}
      const variablePattern = /\$\{([^}]+)\}/g;
      return value.replace(variablePattern, (match, variableName) => {
        return context.variables[variableName] || match;
      });
    }

    if (typeof value === 'object' && value !== null) {
      if (Array.isArray(value)) {
        return value.map(item => this.resolveValue(item, context));
      } else {
        const resolved: any = {};
        for (const [key, val] of Object.entries(value)) {
          resolved[key] = this.resolveValue(val, context);
        }
        return resolved;
      }
    }

    return value;
  }

  /**
   * 验证配置字段
   */
  protected validateConfig(
    config: any,
    requiredFields: string[],
    optionalFields: string[] = []
  ): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    // 检查必需字段
    for (const field of requiredFields) {
      if (config[field] === undefined || config[field] === null) {
        errors.push(`缺少必需字段: ${field}`);
      }
    }

    // 检查未知字段
    const allValidFields = [...requiredFields, ...optionalFields];
    for (const field of Object.keys(config)) {
      if (!allValidFields.includes(field)) {
        warnings.push(`未知配置字段: ${field}`);
      }
    }

    return {
      valid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * 休眠工具方法
   */
  protected sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 安全的JSON解析
   */
  protected safeJSONParse(str: string, defaultValue: any = {}): any {
    try {
      return JSON.parse(str);
    } catch {
      return defaultValue;
    }
  }

  /**
   * 安全的JSON字符串化
   */
  protected safeJSONStringify(obj: any, defaultValue: string = '{}'): string {
    try {
      return JSON.stringify(obj);
    } catch {
      return defaultValue;
    }
  }

  /**
   * 检查中止信号
   */
  protected checkAbortSignal(context: ExecutionContext): boolean {
    return context.abortController.signal.aborted;
  }

  /**
   * 创建成功结果
   */
  protected createSuccessResult(data?: any, logs: ExecutionLog[] = []): ActionExecutionResult {
    return {
      success: true,
      data,
      logs
    };
  }

  /**
   * 创建失败结果
   */
  protected createFailureResult(
    error: ExecutionError,
    logs: ExecutionLog[] = [],
    shouldRetry: boolean = false
  ): ActionExecutionResult {
    return {
      success: false,
      error,
      logs,
      shouldRetry
    };
  }
}