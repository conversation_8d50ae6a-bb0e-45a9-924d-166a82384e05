/**
 * 通知动作执行器
 * 负责执行各种类型的通知发送操作
 */

import { WorkflowStepType } from '@prisma/client';
import { BaseActionExecutor } from './base-action-executor';
import type {
  WorkflowStep,
  ExecutionContext,
  ActionExecutionResult,
  ValidationResult,
  RollbackResult,
  NotificationActionConfig,
  ErrorType,
  ErrorSeverity
} from '@/types/action-executor.types';

/**
 * 通知渠道配置接口
 */
interface NotificationChannel {
  type: 'email' | 'sms' | 'webhook' | 'slack' | 'teams' | 'dingtalk';
  config: any;
}

/**
 * 通知动作执行器
 */
export class NotificationActionExecutor extends BaseActionExecutor {
  public readonly type = WorkflowStepType.NOTIFICATION;
  public readonly name = '通知发送执行器';
  public readonly version = '1.0.0';

  private channelHandlers = new Map<string, (config: any, message: any, context: ExecutionContext) => Promise<any>>();

  constructor() {
    super();
    this.initializeChannelHandlers();
  }

  /**
   * 初始化执行器
   */
  protected async doInitialize(): Promise<void> {
    try {
      // 测试各个通知渠道的连接
      await this.testNotificationChannels();
      console.log('✅ 通知渠道测试完成');
    } catch (error) {
      console.warn('⚠️ 部分通知渠道测试失败，但继续初始化:', error);
    }
  }

  /**
   * 健康检查
   */
  protected async doHealthCheck(): Promise<boolean> {
    try {
      // 检查基本的通知服务可用性
      return await this.checkNotificationServices();
    } catch {
      return false;
    }
  }

  /**
   * 验证步骤配置
   */
  protected async doValidate(step: WorkflowStep): Promise<ValidationResult> {
    const config = step.config as NotificationActionConfig;
    
    // 使用基础验证方法
    const baseValidation = this.validateConfig(
      config,
      ['type', 'message'], // 必需字段
      ['recipients', 'subject', 'template', 'priority', 'retryPolicy', 'channels'] // 可选字段
    );

    if (!baseValidation.valid) {
      return baseValidation;
    }

    const errors: string[] = [];
    const warnings: string[] = [];

    // 验证通知类型
    const validTypes = ['email', 'sms', 'webhook', 'slack', 'teams', 'dingtalk', 'multi'];
    if (!validTypes.includes(config.type)) {
      errors.push(`不支持的通知类型: ${config.type}`);
    }

    // 验证消息内容
    if (!config.message || (typeof config.message !== 'string' && typeof config.message !== 'object')) {
      errors.push('消息内容不能为空');
    }

    // 验证收件人配置
    if (config.recipients) {
      if (!Array.isArray(config.recipients) && typeof config.recipients !== 'string') {
        errors.push('收件人配置格式错误');
      } else if (Array.isArray(config.recipients) && config.recipients.length === 0) {
        warnings.push('收件人列表为空');
      }
    }

    // 验证多渠道配置
    if (config.type === 'multi' && config.channels) {
      if (!Array.isArray(config.channels) || config.channels.length === 0) {
        errors.push('多渠道通知需要配置至少一个通知渠道');
      } else {
        for (const channel of config.channels) {
          if (!channel.type || !validTypes.includes(channel.type)) {
            errors.push(`通知渠道类型无效: ${channel.type}`);
          }
        }
      }
    }

    // 验证邮件特定配置
    if (config.type === 'email') {
      if (!config.subject && !config.template) {
        warnings.push('邮件通知建议设置主题或使用模板');
      }
    }

    // 验证优先级设置
    if (config.priority && !['low', 'normal', 'high', 'urgent'].includes(config.priority)) {
      warnings.push('优先级设置无效，将使用默认优先级');
    }

    return {
      valid: errors.length === 0,
      errors: [...baseValidation.errors, ...errors],
      warnings: [...baseValidation.warnings, ...warnings]
    };
  }

  /**
   * 执行通知发送
   */
  protected async doExecute(step: WorkflowStep, context: ExecutionContext): Promise<ActionExecutionResult> {
    const config = step.config as NotificationActionConfig;
    const logs: ExecutionLog[] = [];

    try {
      // 解析配置中的变量
      const resolvedConfig = this.resolveValue(config, context) as NotificationActionConfig;

      this.addLog(logs, 'INFO', `发送通知: ${resolvedConfig.type}`, context, {
        type: resolvedConfig.type,
        hasTemplate: !!resolvedConfig.template,
        recipientCount: this.getRecipientCount(resolvedConfig.recipients)
      });

      // 构建通知消息
      const message = await this.buildNotificationMessage(resolvedConfig, context);

      // 发送通知
      const results = await this.sendNotification(resolvedConfig, message, context);

      // 记录发送结果
      this.addLog(logs, 'INFO', `通知发送完成`, context, {
        type: resolvedConfig.type,
        successCount: results.filter(r => r.success).length,
        totalCount: results.length
      });

      const responseData = {
        type: resolvedConfig.type,
        success: true,
        results: results,
        successCount: results.filter(r => r.success).length,
        failureCount: results.filter(r => !r.success).length,
        sentAt: new Date().toISOString()
      };

      return this.createSuccessResult(responseData, logs);

    } catch (error: any) {
      return await this.handleNotificationError(error, logs, context, config);
    }
  }

  /**
   * 初始化通知渠道处理器
   */
  private initializeChannelHandlers(): void {
    // 邮件处理器
    this.channelHandlers.set('email', async (config, message, context) => {
      return await this.sendEmailNotification(config, message, context);
    });

    // 短信处理器
    this.channelHandlers.set('sms', async (config, message, context) => {
      return await this.sendSmsNotification(config, message, context);
    });

    // Webhook处理器
    this.channelHandlers.set('webhook', async (config, message, context) => {
      return await this.sendWebhookNotification(config, message, context);
    });

    // Slack处理器
    this.channelHandlers.set('slack', async (config, message, context) => {
      return await this.sendSlackNotification(config, message, context);
    });

    // Teams处理器
    this.channelHandlers.set('teams', async (config, message, context) => {
      return await this.sendTeamsNotification(config, message, context);
    });

    // 钉钉处理器
    this.channelHandlers.set('dingtalk', async (config, message, context) => {
      return await this.sendDingtalkNotification(config, message, context);
    });
  }

  /**
   * 构建通知消息
   */
  private async buildNotificationMessage(config: NotificationActionConfig, context: ExecutionContext): Promise<any> {
    let message = config.message;

    // 如果使用模板，则渲染模板
    if (config.template) {
      message = await this.renderTemplate(config.template, context.variables, context);
    }

    // 如果消息是字符串，转换为对象格式
    if (typeof message === 'string') {
      message = {
        content: message,
        subject: config.subject || '工作流通知',
        priority: config.priority || 'normal'
      };
    }

    return message;
  }

  /**
   * 发送通知
   */
  private async sendNotification(
    config: NotificationActionConfig,
    message: any,
    context: ExecutionContext
  ): Promise<any[]> {
    const results: any[] = [];

    if (config.type === 'multi' && config.channels) {
      // 多渠道发送
      for (const channel of config.channels) {
        try {
          const handler = this.channelHandlers.get(channel.type);
          if (handler) {
            const result = await handler(channel.config || {}, message, context);
            results.push({
              channel: channel.type,
              success: true,
              result: result
            });
          } else {
            results.push({
              channel: channel.type,
              success: false,
              error: `不支持的通知渠道: ${channel.type}`
            });
          }
        } catch (error: any) {
          results.push({
            channel: channel.type,
            success: false,
            error: error.message
          });
        }
      }
    } else {
      // 单渠道发送
      const handler = this.channelHandlers.get(config.type);
      if (handler) {
        try {
          const result = await handler(config, message, context);
          results.push({
            channel: config.type,
            success: true,
            result: result
          });
        } catch (error: any) {
          results.push({
            channel: config.type,
            success: false,
            error: error.message
          });
        }
      } else {
        results.push({
          channel: config.type,
          success: false,
          error: `不支持的通知类型: ${config.type}`
        });
      }
    }

    return results;
  }

  /**
   * 发送邮件通知
   */
  private async sendEmailNotification(config: any, message: any, context: ExecutionContext): Promise<any> {
    // 这里应该集成实际的邮件发送服务
    // 例如: nodemailer, SendGrid, AWS SES等
    
    context.logger.info('模拟发送邮件通知', {
      to: config.recipients || message.recipients,
      subject: message.subject,
      content: message.content?.substring(0, 100)
    });

    // 模拟发送延迟
    await this.sleep(1000);

    return {
      messageId: `email_${Date.now()}`,
      recipients: config.recipients || message.recipients,
      sentAt: new Date().toISOString()
    };
  }

  /**
   * 发送短信通知
   */
  private async sendSmsNotification(config: any, message: any, context: ExecutionContext): Promise<any> {
    // 这里应该集成实际的短信发送服务
    // 例如: 阿里云短信, 腾讯云短信等
    
    context.logger.info('模拟发送短信通知', {
      to: config.recipients || message.recipients,
      content: message.content?.substring(0, 50)
    });

    // 模拟发送延迟
    await this.sleep(500);

    return {
      messageId: `sms_${Date.now()}`,
      recipients: config.recipients || message.recipients,
      sentAt: new Date().toISOString()
    };
  }

  /**
   * 发送Webhook通知
   */
  private async sendWebhookNotification(config: any, message: any, context: ExecutionContext): Promise<any> {
    // 这里应该发送HTTP请求到指定的Webhook URL
    
    context.logger.info('模拟发送Webhook通知', {
      url: config.url,
      method: config.method || 'POST',
      contentType: 'application/json'
    });

    // 模拟发送延迟
    await this.sleep(800);

    return {
      messageId: `webhook_${Date.now()}`,
      url: config.url,
      statusCode: 200,
      sentAt: new Date().toISOString()
    };
  }

  /**
   * 发送Slack通知
   */
  private async sendSlackNotification(config: any, message: any, context: ExecutionContext): Promise<any> {
    // 这里应该集成Slack API
    
    context.logger.info('模拟发送Slack通知', {
      channel: config.channel,
      content: message.content?.substring(0, 100)
    });

    // 模拟发送延迟
    await this.sleep(600);

    return {
      messageId: `slack_${Date.now()}`,
      channel: config.channel,
      sentAt: new Date().toISOString()
    };
  }

  /**
   * 发送Teams通知
   */
  private async sendTeamsNotification(config: any, message: any, context: ExecutionContext): Promise<any> {
    // 这里应该集成Microsoft Teams API
    
    context.logger.info('模拟发送Teams通知', {
      webhook: config.webhook,
      content: message.content?.substring(0, 100)
    });

    // 模拟发送延迟
    await this.sleep(700);

    return {
      messageId: `teams_${Date.now()}`,
      webhook: config.webhook,
      sentAt: new Date().toISOString()
    };
  }

  /**
   * 发送钉钉通知
   */
  private async sendDingtalkNotification(config: any, message: any, context: ExecutionContext): Promise<any> {
    // 这里应该集成钉钉机器人API
    
    context.logger.info('模拟发送钉钉通知', {
      webhook: config.webhook,
      content: message.content?.substring(0, 100)
    });

    // 模拟发送延迟
    await this.sleep(600);

    return {
      messageId: `dingtalk_${Date.now()}`,
      webhook: config.webhook,
      sentAt: new Date().toISOString()
    };
  }

  /**
   * 渲染消息模板
   */
  private async renderTemplate(template: string, variables: Record<string, any>, context: ExecutionContext): Promise<string> {
    // 简单的模板渲染实现
    // 实际项目中可以使用更完善的模板引擎，如Handlebars、Mustache等
    
    let rendered = template;
    
    // 替换变量 {{variableName}}
    const variablePattern = /\{\{([^}]+)\}\}/g;
    rendered = rendered.replace(variablePattern, (match, variableName) => {
      const trimmedName = variableName.trim();
      return variables[trimmedName] !== undefined ? String(variables[trimmedName]) : match;
    });

    // 替换上下文变量 ${contextProperty}
    const contextPattern = /\$\{([^}]+)\}/g;
    rendered = rendered.replace(contextPattern, (match, propertyName) => {
      const trimmedName = propertyName.trim();
      switch (trimmedName) {
        case 'executionId':
          return context.executionId;
        case 'workflowId':
          return context.workflowId;
        case 'stepIndex':
          return String(context.stepIndex);
        case 'timestamp':
          return new Date().toISOString();
        default:
          return match;
      }
    });

    return rendered;
  }

  /**
   * 获取收件人数量
   */
  private getRecipientCount(recipients: any): number {
    if (!recipients) return 0;
    if (typeof recipients === 'string') return 1;
    if (Array.isArray(recipients)) return recipients.length;
    return 0;
  }

  /**
   * 测试通知渠道
   */
  private async testNotificationChannels(): Promise<void> {
    // 这里可以测试各种通知渠道的连接
    // 例如：测试SMTP连接、API密钥等
    console.log('测试通知渠道连接...');
  }

  /**
   * 检查通知服务可用性
   */
  private async checkNotificationServices(): Promise<boolean> {
    // 这里可以检查各种通知服务的健康状态
    return true;
  }

  /**
   * 处理通知错误
   */
  private async handleNotificationError(
    error: any,
    logs: ExecutionLog[],
    context: ExecutionContext,
    config: NotificationActionConfig
  ): Promise<ActionExecutionResult> {
    let errorType = ErrorType.SYSTEM_ERROR;
    let errorCode = 'NOTIFICATION_FAILED';
    let errorMessage = error.message || '通知发送失败';
    let shouldRetry = false;
    let severity = ErrorSeverity.MEDIUM;

    // 分析错误类型
    if (error.message?.includes('timeout') || error.message?.includes('超时')) {
      errorType = ErrorType.TIMEOUT_ERROR;
      errorCode = 'NOTIFICATION_TIMEOUT';
      errorMessage = '通知发送超时';
      shouldRetry = true;
    } else if (error.message?.includes('network') || error.message?.includes('连接')) {
      errorType = ErrorType.NETWORK_ERROR;
      errorCode = 'NOTIFICATION_NETWORK_ERROR';
      errorMessage = '网络连接失败';
      shouldRetry = true;
    } else if (error.message?.includes('authentication') || error.message?.includes('认证')) {
      errorType = ErrorType.AUTHENTICATION_ERROR;
      errorCode = 'NOTIFICATION_AUTH_FAILED';
      errorMessage = '通知服务认证失败';
      shouldRetry = false;
      severity = ErrorSeverity.HIGH;
    } else if (error.message?.includes('quota') || error.message?.includes('limit')) {
      errorType = ErrorType.SYSTEM_ERROR;
      errorCode = 'NOTIFICATION_QUOTA_EXCEEDED';
      errorMessage = '通知服务配额超限';
      shouldRetry = true;
      severity = ErrorSeverity.HIGH;
    }

    this.addLog(logs, 'ERROR', errorMessage, context, {
      errorCode,
      errorType,
      notificationType: config.type,
      originalError: error.message
    });

    const executionError = this.createError(
      errorType,
      errorCode,
      errorMessage,
      {
        originalError: error.message,
        notificationType: config.type,
        recipients: config.recipients
      },
      shouldRetry,
      severity
    );

    return this.createFailureResult(executionError, logs, shouldRetry);
  }

  /**
   * 回滚通知（通常不支持）
   */
  protected async doRollback(step: WorkflowStep, context: ExecutionContext): Promise<RollbackResult> {
    const logs: ExecutionLog[] = [];
    
    // 通知通常不支持回滚，但可以发送撤回通知
    const config = step.config as NotificationActionConfig;
    
    this.addLog(logs, 'INFO', '通知操作不支持直接回滚', context);
    
    // 如果配置了回滚通知，可以发送撤回通知
    if (config.rollbackNotification) {
      try {
        this.addLog(logs, 'INFO', '发送回滚通知', context);
        
        const rollbackMessage = await this.buildNotificationMessage(config.rollbackNotification as any, context);
        const results = await this.sendNotification(config.rollbackNotification as any, rollbackMessage, context);
        
        this.addLog(logs, 'INFO', '回滚通知发送完成', context);
        
        return {
          success: true,
          logs,
          restoredState: {
            rollbackNotification: {
              sent: true,
              results: results
            }
          }
        };
        
      } catch (error: any) {
        const rollbackError = this.createError(
          ErrorType.EXECUTION_ERROR,
          'NOTIFICATION_ROLLBACK_FAILED',
          `回滚通知发送失败: ${error.message}`
        );
        
        this.addLog(logs, 'ERROR', `回滚通知发送失败: ${error.message}`, context);
        
        return {
          success: false,
          error: rollbackError,
          logs
        };
      }
    }

    return {
      success: true,
      logs
    };
  }
}

// 声明类型扩展
declare module '@/types/action-executor.types' {
  interface NotificationActionConfig {
    type: 'email' | 'sms' | 'webhook' | 'slack' | 'teams' | 'dingtalk' | 'multi';
    message: string | object;
    recipients?: string | string[];
    subject?: string;
    template?: string;
    priority?: 'low' | 'normal' | 'high' | 'urgent';
    channels?: Array<{
      type: string;
      config?: any;
    }>;
    retryPolicy?: {
      maxAttempts: number;
      delay: number;
      backoffMultiplier?: number;
    };
    rollbackNotification?: NotificationActionConfig;
  }
}