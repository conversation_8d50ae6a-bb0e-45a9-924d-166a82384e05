/**
 * 回滚协调器
 * 负责协调复杂的多步骤回滚操作和分布式事务回滚
 */

import { EventEmitter } from 'events';
import type {
  WorkflowStep,
  ExecutionContext,
  ActionExecutionResult,
  RollbackResult,
  ExecutionError,
  ExecutionLog,
  ErrorType,
  ErrorSeverity
} from '@/types/action-executor.types';
import { actionExecutorManager } from './action-executor-manager.service';
import { executionContextManager } from './execution-context-manager.service';
import { executionStateTracker } from './execution-state-tracker.service';

/**
 * 回滚策略
 */
export enum RollbackStrategy {
  SEQUENTIAL = 'SEQUENTIAL',           // 顺序回滚
  PARALLEL = 'PARALLEL',               // 并行回滚
  DEPENDENCY_AWARE = 'DEPENDENCY_AWARE', // 依赖感知回滚
  COMPENSATION_BASED = 'COMPENSATION_BASED' // 补偿模式回滚
}

/**
 * 回滚会话状态
 */
export enum RollbackSessionStatus {
  PENDING = 'PENDING',
  RUNNING = 'RUNNING',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
  PARTIALLY_COMPLETED = 'PARTIALLY_COMPLETED'
}

/**
 * 回滚会话
 */
export interface RollbackSession {
  sessionId: string;
  executionId: string;
  workflowId: string;
  strategy: RollbackStrategy;
  status: RollbackSessionStatus;
  stepsToRollback: WorkflowStep[];
  rollbackResults: Map<number, RollbackResult>;
  dependencies: Map<number, number[]>; // stepIndex -> dependentStepIndexes
  startTime: Date;
  endTime?: Date;
  duration?: number;
  logs: ExecutionLog[];
  metadata: Record<string, any>;
}

/**
 * 补偿动作定义
 */
export interface CompensationAction {
  stepIndex: number;
  compensationStep: WorkflowStep;
  condition?: (originalResult: ActionExecutionResult) => boolean;
  priority: number;
  timeout?: number;
}

/**
 * 回滚协调器
 */
export class RollbackCoordinator extends EventEmitter {
  private static instance: RollbackCoordinator;
  private rollbackSessions = new Map<string, RollbackSession>();
  private compensationActions = new Map<string, CompensationAction[]>(); // workflowId -> compensations
  private isInitialized = false;

  constructor() {
    super();
  }

  /**
   * 获取单例实例
   */
  static getInstance(): RollbackCoordinator {
    if (!RollbackCoordinator.instance) {
      RollbackCoordinator.instance = new RollbackCoordinator();
    }
    return RollbackCoordinator.instance;
  }

  /**
   * 初始化回滚协调器
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      console.log('🚀 初始化RollbackCoordinator...');

      this.setupEventListeners();
      this.isInitialized = true;

      console.log('✅ RollbackCoordinator初始化完成');
      this.emit('initialized');

    } catch (error) {
      console.error('❌ RollbackCoordinator初始化失败:', error);
      throw error;
    }
  }

  /**
   * 执行工作流回滚
   */
  async executeWorkflowRollback(
    executionId: string,
    workflowId: string,
    failedStepIndex: number,
    executedSteps: WorkflowStep[],
    strategy: RollbackStrategy = RollbackStrategy.SEQUENTIAL
  ): Promise<RollbackSession> {
    const sessionId = this.generateSessionId();
    
    try {
      console.log(`🔄 开始工作流回滚: ${workflowId}, 会话: ${sessionId}`);

      // 创建回滚会话
      const session = this.createRollbackSession(
        sessionId,
        executionId,
        workflowId,
        failedStepIndex,
        executedSteps,
        strategy
      );

      this.rollbackSessions.set(sessionId, session);

      this.addSessionLog(session, 'INFO', `创建回滚会话: 策略=${strategy}, 步骤数=${session.stepsToRollback.length}`);

      this.emit('rollback:session:created', {
        sessionId,
        executionId,
        workflowId,
        strategy,
        stepsCount: session.stepsToRollback.length
      });

      // 执行回滚
      await this.executeRollbackStrategy(session);

      return session;

    } catch (error: any) {
      console.error(`❌ 工作流回滚失败: ${sessionId}`, error);
      
      const session = this.rollbackSessions.get(sessionId);
      if (session) {
        session.status = RollbackSessionStatus.FAILED;
        session.endTime = new Date();
        this.addSessionLog(session, 'ERROR', `回滚失败: ${error.message}`);
      }

      throw error;
    }
  }

  /**
   * 注册补偿动作
   */
  registerCompensationActions(
    workflowId: string,
    compensations: CompensationAction[]
  ): void {
    this.compensationActions.set(workflowId, compensations);
    
    console.log(`📝 注册补偿动作: ${workflowId}, 动作数: ${compensations.length}`);
    this.emit('compensation:registered', { workflowId, count: compensations.length });
  }

  /**
   * 获取回滚会话
   */
  getRollbackSession(sessionId: string): RollbackSession | undefined {
    return this.rollbackSessions.get(sessionId);
  }

  /**
   * 列出回滚会话
   */
  listRollbackSessions(filter?: {
    status?: RollbackSessionStatus;
    workflowId?: string;
    executionId?: string;
  }): RollbackSession[] {
    let sessions = Array.from(this.rollbackSessions.values());

    if (filter) {
      if (filter.status) {
        sessions = sessions.filter(s => s.status === filter.status);
      }
      if (filter.workflowId) {
        sessions = sessions.filter(s => s.workflowId === filter.workflowId);
      }
      if (filter.executionId) {
        sessions = sessions.filter(s => s.executionId === filter.executionId);
      }
    }

    return sessions.sort((a, b) => b.startTime.getTime() - a.startTime.getTime());
  }

  /**
   * 获取回滚统计信息
   */
  getRollbackStats(): {
    totalSessions: number;
    completedSessions: number;
    failedSessions: number;
    partiallyCompletedSessions: number;
    averageDuration: number;
    successRate: number;
  } {
    const sessions = Array.from(this.rollbackSessions.values());
    const total = sessions.length;

    const statusCounts = sessions.reduce((acc, session) => {
      acc[session.status] = (acc[session.status] || 0) + 1;
      return acc;
    }, {} as Record<RollbackSessionStatus, number>);

    const completedSessions = sessions.filter(s => s.status === RollbackSessionStatus.COMPLETED);
    const averageDuration = completedSessions.length > 0
      ? completedSessions.reduce((sum, s) => sum + (s.duration || 0), 0) / completedSessions.length
      : 0;

    const successfulSessions = statusCounts[RollbackSessionStatus.COMPLETED] || 0;
    const partiallySuccessfulSessions = statusCounts[RollbackSessionStatus.PARTIALLY_COMPLETED] || 0;
    const successRate = total > 0
      ? ((successfulSessions + partiallySuccessfulSessions * 0.5) / total) * 100
      : 0;

    return {
      totalSessions: total,
      completedSessions: successfulSessions,
      failedSessions: statusCounts[RollbackSessionStatus.FAILED] || 0,
      partiallyCompletedSessions: partiallySuccessfulSessions,
      averageDuration: Math.round(averageDuration),
      successRate: Math.round(successRate * 100) / 100
    };
  }

  // ========== 私有方法 ==========

  /**
   * 创建回滚会话
   */
  private createRollbackSession(
    sessionId: string,
    executionId: string,
    workflowId: string,
    failedStepIndex: number,
    executedSteps: WorkflowStep[],
    strategy: RollbackStrategy
  ): RollbackSession {
    // 获取需要回滚的步骤（失败步骤之前的已执行步骤）
    const stepsToRollback = executedSteps
      .filter(step => step.index < failedStepIndex)
      .sort((a, b) => b.index - a.index); // 按相反顺序

    // 分析步骤依赖关系
    const dependencies = this.analyzeDependencies(stepsToRollback);

    return {
      sessionId,
      executionId,
      workflowId,
      strategy,
      status: RollbackSessionStatus.PENDING,
      stepsToRollback,
      rollbackResults: new Map(),
      dependencies,
      startTime: new Date(),
      logs: [],
      metadata: { failedStepIndex }
    };
  }

  /**
   * 分析步骤依赖关系
   */
  private analyzeDependencies(steps: WorkflowStep[]): Map<number, number[]> {
    const dependencies = new Map<number, number[]>();

    // 简化的依赖分析：基于步骤顺序和配置
    for (const step of steps) {
      const dependents: number[] = [];

      // 检查其他步骤是否依赖于当前步骤
      for (const otherStep of steps) {
        if (otherStep.index > step.index) {
          // 简化逻辑：后续步骤可能依赖前面的步骤
          if (this.hasDependency(step, otherStep)) {
            dependents.push(otherStep.index);
          }
        }
      }

      dependencies.set(step.index, dependents);
    }

    return dependencies;
  }

  /**
   * 检查步骤间是否存在依赖关系
   */
  private hasDependency(step: WorkflowStep, dependentStep: WorkflowStep): boolean {
    // 简化的依赖检查逻辑
    // 在实际实现中，这里应该检查步骤配置、变量依赖等
    
    // 例如：检查 dependentStep 的配置中是否引用了 step 的输出
    const stepConfig = JSON.stringify(dependentStep.config);
    const stepReference = `\${step_${step.index}_`;
    
    return stepConfig.includes(stepReference);
  }

  /**
   * 执行回滚策略
   */
  private async executeRollbackStrategy(session: RollbackSession): Promise<void> {
    session.status = RollbackSessionStatus.RUNNING;
    
    this.addSessionLog(session, 'INFO', `开始执行回滚策略: ${session.strategy}`);

    switch (session.strategy) {
      case RollbackStrategy.SEQUENTIAL:
        await this.executeSequentialRollback(session);
        break;
      case RollbackStrategy.PARALLEL:
        await this.executeParallelRollback(session);
        break;
      case RollbackStrategy.DEPENDENCY_AWARE:
        await this.executeDependencyAwareRollback(session);
        break;
      case RollbackStrategy.COMPENSATION_BASED:
        await this.executeCompensationBasedRollback(session);
        break;
      default:
        throw new Error(`不支持的回滚策略: ${session.strategy}`);
    }

    // 完成回滚会话
    this.completeRollbackSession(session);
  }

  /**
   * 执行顺序回滚
   */
  private async executeSequentialRollback(session: RollbackSession): Promise<void> {
    this.addSessionLog(session, 'INFO', '执行顺序回滚');

    for (const step of session.stepsToRollback) {
      try {
        const result = await this.rollbackSingleStep(session, step);
        session.rollbackResults.set(step.index, result);

        if (!result.success) {
          this.addSessionLog(session, 'ERROR', `步骤 ${step.index} 回滚失败: ${result.error?.message}`);
          // 继续回滚其他步骤，不因单个失败而停止
        }
      } catch (error: any) {
        this.addSessionLog(session, 'ERROR', `步骤 ${step.index} 回滚异常: ${error.message}`);
      }
    }
  }

  /**
   * 执行并行回滚
   */
  private async executeParallelRollback(session: RollbackSession): Promise<void> {
    this.addSessionLog(session, 'INFO', '执行并行回滚');

    const rollbackPromises = session.stepsToRollback.map(async (step) => {
      try {
        const result = await this.rollbackSingleStep(session, step);
        session.rollbackResults.set(step.index, result);
        return { step, result };
      } catch (error: any) {
        const failureResult: RollbackResult = {
          success: false,
          error: {
            type: ErrorType.EXECUTION_ERROR,
            code: 'ROLLBACK_EXCEPTION',
            message: `回滚步骤 ${step.index} 异常: ${error.message}`,
            recoverable: false,
            severity: ErrorSeverity.HIGH,
            timestamp: new Date()
          },
          logs: [{
            level: 'ERROR',
            message: `回滚异常: ${error.message}`,
            timestamp: new Date(),
            source: 'RollbackCoordinator'
          }]
        };
        session.rollbackResults.set(step.index, failureResult);
        return { step, result: failureResult };
      }
    });

    await Promise.all(rollbackPromises);
  }

  /**
   * 执行依赖感知回滚
   */
  private async executeDependencyAwareRollback(session: RollbackSession): Promise<void> {
    this.addSessionLog(session, 'INFO', '执行依赖感知回滚');

    const processedSteps = new Set<number>();
    const pendingSteps = new Set(session.stepsToRollback.map(s => s.index));

    while (pendingSteps.size > 0) {
      const readySteps: WorkflowStep[] = [];

      // 找到可以回滚的步骤（没有未回滚的依赖步骤）
      for (const step of session.stepsToRollback) {
        if (pendingSteps.has(step.index)) {
          const dependents = session.dependencies.get(step.index) || [];
          const hasUnprocessedDependents = dependents.some(depIndex => pendingSteps.has(depIndex));
          
          if (!hasUnprocessedDependents) {
            readySteps.push(step);
          }
        }
      }

      if (readySteps.length === 0) {
        // 如果没有可处理的步骤，可能存在循环依赖，强制处理剩余步骤
        this.addSessionLog(session, 'WARN', '检测到可能的循环依赖，强制处理剩余步骤');
        readySteps.push(...session.stepsToRollback.filter(s => pendingSteps.has(s.index)));
      }

      // 并行处理准备好的步骤
      const rollbackPromises = readySteps.map(async (step) => {
        try {
          const result = await this.rollbackSingleStep(session, step);
          session.rollbackResults.set(step.index, result);
          processedSteps.add(step.index);
          pendingSteps.delete(step.index);
          return result;
        } catch (error: any) {
          this.addSessionLog(session, 'ERROR', `步骤 ${step.index} 回滚异常: ${error.message}`);
          processedSteps.add(step.index);
          pendingSteps.delete(step.index);
        }
      });

      await Promise.all(rollbackPromises);
    }
  }

  /**
   * 执行补偿模式回滚
   */
  private async executeCompensationBasedRollback(session: RollbackSession): Promise<void> {
    this.addSessionLog(session, 'INFO', '执行补偿模式回滚');

    const compensations = this.compensationActions.get(session.workflowId) || [];
    
    if (compensations.length === 0) {
      this.addSessionLog(session, 'WARN', '未找到补偿动作，回退到顺序回滚');
      await this.executeSequentialRollback(session);
      return;
    }

    // 按优先级排序补偿动作
    const sortedCompensations = compensations
      .filter(comp => session.stepsToRollback.some(step => step.index === comp.stepIndex))
      .sort((a, b) => b.priority - a.priority);

    for (const compensation of sortedCompensations) {
      try {
        this.addSessionLog(session, 'INFO', `执行补偿动作: 步骤 ${compensation.stepIndex}`);
        
        const result = await this.executeCompensationAction(session, compensation);
        session.rollbackResults.set(compensation.stepIndex, result);

      } catch (error: any) {
        this.addSessionLog(session, 'ERROR', `补偿动作失败: 步骤 ${compensation.stepIndex}, 错误: ${error.message}`);
      }
    }
  }

  /**
   * 回滚单个步骤
   */
  private async rollbackSingleStep(
    session: RollbackSession,
    step: WorkflowStep
  ): Promise<RollbackResult> {
    this.addSessionLog(session, 'INFO', `开始回滚步骤 ${step.index}: ${step.name}`);

    try {
      // 获取执行上下文
      const context = executionContextManager.getContext(session.executionId, step.index);
      if (!context) {
        this.addSessionLog(session, 'WARN', `无法获取步骤 ${step.index} 的上下文`);
        return this.createFailureResult('CONTEXT_NOT_FOUND', '执行上下文不存在');
      }

      // 执行回滚
      const result = await actionExecutorManager.rollbackStep(step, context);
      
      if (result.success) {
        this.addSessionLog(session, 'INFO', `步骤 ${step.index} 回滚成功`);
      } else {
        this.addSessionLog(session, 'ERROR', `步骤 ${step.index} 回滚失败: ${result.error?.message}`);
      }

      return result;

    } catch (error: any) {
      this.addSessionLog(session, 'ERROR', `步骤 ${step.index} 回滚异常: ${error.message}`);
      return this.createFailureResult('ROLLBACK_EXCEPTION', `回滚异常: ${error.message}`);
    }
  }

  /**
   * 执行补偿动作
   */
  private async executeCompensationAction(
    session: RollbackSession,
    compensation: CompensationAction
  ): Promise<RollbackResult> {
    try {
      // 获取原始执行结果
      const originalState = await executionStateTracker.getExecutionState(
        session.executionId,
        compensation.stepIndex
      );

      // 检查补偿条件
      if (compensation.condition && originalState?.result) {
        if (!compensation.condition(originalState.result)) {
          this.addSessionLog(session, 'INFO', `补偿动作条件不满足，跳过步骤 ${compensation.stepIndex}`);
          return {
            success: true,
            logs: [{
              level: 'INFO',
              message: '补偿条件不满足，已跳过',
              timestamp: new Date(),
              source: 'RollbackCoordinator'
            }]
          };
        }
      }

      // 获取执行上下文
      const context = executionContextManager.getContext(session.executionId, compensation.stepIndex);
      if (!context) {
        return this.createFailureResult('CONTEXT_NOT_FOUND', '执行上下文不存在');
      }

      // 执行补偿步骤
      const result = await actionExecutorManager.executeStep(compensation.compensationStep, context);
      
      return {
        success: result.success,
        error: result.error,
        logs: result.logs,
        restoredState: result.data
      };

    } catch (error: any) {
      return this.createFailureResult('COMPENSATION_EXCEPTION', `补偿异常: ${error.message}`);
    }
  }

  /**
   * 完成回滚会话
   */
  private completeRollbackSession(session: RollbackSession): void {
    session.endTime = new Date();
    session.duration = session.endTime.getTime() - session.startTime.getTime();

    const results = Array.from(session.rollbackResults.values());
    const successCount = results.filter(r => r.success).length;
    const totalCount = results.length;

    if (successCount === totalCount) {
      session.status = RollbackSessionStatus.COMPLETED;
      this.addSessionLog(session, 'INFO', `回滚完成: ${successCount}/${totalCount} 步骤成功`);
    } else if (successCount > 0) {
      session.status = RollbackSessionStatus.PARTIALLY_COMPLETED;
      this.addSessionLog(session, 'WARN', `回滚部分完成: ${successCount}/${totalCount} 步骤成功`);
    } else {
      session.status = RollbackSessionStatus.FAILED;
      this.addSessionLog(session, 'ERROR', `回滚失败: 所有步骤都失败`);
    }

    this.emit('rollback:session:completed', {
      sessionId: session.sessionId,
      status: session.status,
      successCount,
      totalCount,
      duration: session.duration
    });

    console.log(`🔄 回滚会话完成: ${session.sessionId}, 状态: ${session.status}`);
  }

  /**
   * 创建失败结果
   */
  private createFailureResult(code: string, message: string): RollbackResult {
    return {
      success: false,
      error: {
        type: ErrorType.EXECUTION_ERROR,
        code,
        message,
        recoverable: false,
        severity: ErrorSeverity.HIGH,
        timestamp: new Date()
      },
      logs: [{
        level: 'ERROR',
        message,
        timestamp: new Date(),
        source: 'RollbackCoordinator'
      }]
    };
  }

  /**
   * 添加会话日志
   */
  private addSessionLog(
    session: RollbackSession,
    level: ExecutionLog['level'],
    message: string,
    metadata?: any
  ): void {
    const log: ExecutionLog = {
      level,
      message,
      timestamp: new Date(),
      metadata,
      source: 'RollbackCoordinator'
    };
    
    session.logs.push(log);
    
    // 记录到控制台
    switch (level) {
      case 'INFO':
        console.log(`[${session.sessionId}] ${message}`, metadata);
        break;
      case 'WARN':
        console.warn(`[${session.sessionId}] ${message}`, metadata);
        break;
      case 'ERROR':
        console.error(`[${session.sessionId}] ${message}`, metadata);
        break;
    }
  }

  /**
   * 生成会话ID
   */
  private generateSessionId(): string {
    return `rollback_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    // 监听工作流执行失败事件
    actionExecutorManager.on('workflow:failed', async (event) => {
      console.log(`⚠️ 检测到工作流失败，自动触发回滚: ${event.workflowId}`);
      // 这里可以自动触发回滚
    });

    // 定期清理过期会话
    setInterval(() => {
      this.cleanupExpiredSessions();
    }, 30 * 60 * 1000); // 30分钟清理一次
  }

  /**
   * 清理过期会话
   */
  private cleanupExpiredSessions(): void {
    const now = Date.now();
    const maxAge = 24 * 60 * 60 * 1000; // 24小时

    for (const [sessionId, session] of this.rollbackSessions) {
      if (session.status === RollbackSessionStatus.COMPLETED || 
          session.status === RollbackSessionStatus.FAILED ||
          session.status === RollbackSessionStatus.PARTIALLY_COMPLETED) {
        
        const age = now - session.startTime.getTime();
        if (age > maxAge) {
          this.rollbackSessions.delete(sessionId);
          console.log(`🧹 清理过期回滚会话: ${sessionId}`);
        }
      }
    }
  }

  /**
   * 全局清理
   */
  async cleanup(): Promise<void> {
    console.log('🧹 开始清理RollbackCoordinator...');
    
    this.rollbackSessions.clear();
    this.compensationActions.clear();
    this.isInitialized = false;
    
    console.log('✅ RollbackCoordinator清理完成');
    this.emit('cleanup:completed');
  }
}

// 导出单例实例
export const rollbackCoordinator = RollbackCoordinator.getInstance();