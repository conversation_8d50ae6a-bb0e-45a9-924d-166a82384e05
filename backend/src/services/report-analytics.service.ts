import { prisma } from '@/config/database.config'
import { CacheService } from '@/config/redis.config'
import { ServiceStatus, ServiceCategory, Priority, AlertSeverity } from '@prisma/client'
import * as XLSX from 'xlsx'
import * as fs from 'fs'
import * as path from 'path'

export interface AnalyticsTimeRange {
  start: Date
  end: Date
}

export interface OperationEfficiencyMetrics {
  totalServices: number
  completedServices: number
  avgResponseTimeMinutes: number
  avgResolutionTimeHours: number
  slaComplianceRate: number
  engineerWorkload: Array<{
    userId: string
    userName: string
    assignedCount: number
    completedCount: number
    avgResolutionTime: number
    totalWorkHours: number
    efficiency: number
  }>
  categoryDistribution: Record<ServiceCategory, number>
  priorityDistribution: Record<Priority, number>
  statusDistribution: Record<ServiceStatus, number>
}

export interface CustomerSatisfactionMetrics {
  totalFeedbacks: number
  averageRating: number
  ratingDistribution: Record<number, number>
  customerSatisfactionTrend: Array<{
    date: string
    rating: number
    count: number
  }>
  topCustomers: Array<{
    customerId: string
    customerName: string
    totalServices: number
    avgRating: number
    lastServiceDate: Date
  }>
  satisfactionByCategory: Record<ServiceCategory, {
    avgRating: number
    count: number
  }>
  complaintsAnalysis: {
    totalComplaints: number
    resolvedComplaints: number
    avgResolutionTime: number
    commonIssues: Array<{
      issue: string
      count: number
      percentage: number
    }>
  }
}

export interface SLAComplianceReport {
  totalServices: number
  slaViolations: number
  complianceRate: number
  responseTimeCompliance: {
    total: number
    compliant: number
    rate: number
    avgResponseTime: number
  }
  resolutionTimeCompliance: {
    total: number
    compliant: number
    rate: number
    avgResolutionTime: number
  }
  violationsByPriority: Record<Priority, {
    total: number
    violations: number
    rate: number
  }>
  violationsByCategory: Record<ServiceCategory, {
    total: number
    violations: number
    rate: number
  }>
  monthlyTrend: Array<{
    month: string
    total: number
    violations: number
    rate: number
  }>
  costImpact: {
    totalViolationCost: number
    avgCostPerViolation: number
    potentialSavings: number
  }
}

export interface ServiceTrendAnalysis {
  timeRange: AnalyticsTimeRange
  totalServices: number
  serviceVolumeTrend: Array<{
    date: string
    count: number
    cumulative: number
  }>
  categoryTrends: Record<ServiceCategory, Array<{
    date: string
    count: number
  }>>
  resolutionTimeTrend: Array<{
    date: string
    avgHours: number
    minHours: number
    maxHours: number
  }>
  backlogAnalysis: {
    currentBacklog: number
    avgBacklogSize: number
    backlogTrend: Array<{
      date: string
      size: number
    }>
    oldestTicket: {
      id: string
      ticketNumber: string
      daysOld: number
    }
  }
  seasonalPatterns: {
    hourlyDistribution: Record<number, number>
    weeklyDistribution: Record<number, number>
    monthlyDistribution: Record<number, number>
  }
}

export interface CustomReportConfig {
  id?: string
  name: string
  description?: string
  timeRange: {
    type: 'custom' | 'last7days' | 'last30days' | 'lastQuarter' | 'lastYear'
    start?: Date
    end?: Date
  }
  metrics: Array<{
    type: 'service_count' | 'avg_response_time' | 'sla_compliance' | 'satisfaction' | 'workload'
    groupBy?: 'day' | 'week' | 'month' | 'quarter' | 'year' | 'category' | 'priority' | 'engineer'
    filters?: {
      categories?: ServiceCategory[]
      priorities?: Priority[]
      engineers?: string[]
      customers?: string[]
    }
  }>
  visualizations: Array<{
    type: 'line' | 'bar' | 'pie' | 'table' | 'number'
    title: string
    metricIndex: number
    config?: Record<string, any>
  }>
  exportFormats: ('pdf' | 'excel' | 'csv')[]
  schedule?: {
    enabled: boolean
    frequency: 'daily' | 'weekly' | 'monthly'
    recipients: string[]
  }
  createdBy: string
  createdAt?: Date
  lastGenerated?: Date
}

/**
 * 报告和数据分析引擎服务
 * 提供运维效率、客户满意度、SLA合规性等数据分析功能
 */
export class ReportAnalyticsService {
  private static instance: ReportAnalyticsService
  private readonly CACHE_TTL = 1800 // 30分钟缓存

  private constructor() {
    this.initializeService()
  }

  public static getInstance(): ReportAnalyticsService {
    if (!ReportAnalyticsService.instance) {
      ReportAnalyticsService.instance = new ReportAnalyticsService()
    }
    return ReportAnalyticsService.instance
  }

  private async initializeService() {
    console.log('📊 报告分析引擎初始化中...')
    // 预热常用查询缓存
    this.warmupCache()
    console.log('✅ 报告分析引擎初始化完成')
  }

  /**
   * 获取运维效率分析数据
   */
  async getOperationEfficiencyMetrics(timeRange: AnalyticsTimeRange): Promise<OperationEfficiencyMetrics> {
    const cacheKey = `analytics:efficiency:${timeRange.start.getTime()}-${timeRange.end.getTime()}`
    
    try {
      // 尝试从缓存获取
      const cached = await CacheService.get(cacheKey)
      if (cached) {
        return JSON.parse(cached as string)
      }

      console.log('📊 计算运维效率指标...')

      const [
        serviceStats,
        engineerWorkload,
        categoryDistribution,
        priorityDistribution,
        statusDistribution
      ] = await Promise.all([
        this.calculateServiceStats(timeRange),
        this.calculateEngineerWorkload(timeRange),
        this.getServiceDistribution(timeRange, 'category'),
        this.getServiceDistribution(timeRange, 'priority'),
        this.getServiceDistribution(timeRange, 'status')
      ])

      const result: OperationEfficiencyMetrics = {
        totalServices: serviceStats.total,
        completedServices: serviceStats.completed,
        avgResponseTimeMinutes: serviceStats.avgResponseTime,
        avgResolutionTimeHours: serviceStats.avgResolutionTime,
        slaComplianceRate: serviceStats.slaCompliance,
        engineerWorkload: engineerWorkload as any,
        categoryDistribution: categoryDistribution as Record<ServiceCategory, number>,
        priorityDistribution: priorityDistribution as Record<Priority, number>,
        statusDistribution: statusDistribution as Record<ServiceStatus, number>
      }

      // 缓存结果
      await CacheService.setex(cacheKey, this.CACHE_TTL, JSON.stringify(result))
      
      console.log('✅ 运维效率指标计算完成')
      return result

    } catch (error) {
      console.error('计算运维效率指标失败:', error)
      throw error
    }
  }

  /**
   * 获取客户满意度分析数据
   */
  async getCustomerSatisfactionMetrics(timeRange: AnalyticsTimeRange): Promise<CustomerSatisfactionMetrics> {
    const cacheKey = `analytics:satisfaction:${timeRange.start.getTime()}-${timeRange.end.getTime()}`
    
    try {
      const cached = await CacheService.get(cacheKey)
      if (cached) {
        return JSON.parse(cached as string)
      }

      console.log('😊 计算客户满意度指标...')

      const [
        satisfactionStats,
        satisfactionTrend,
        topCustomers,
        categoryRatings,
        complaintsData
      ] = await Promise.all([
        this.calculateSatisfactionStats(timeRange),
        this.getSatisfactionTrend(timeRange),
        this.getTopCustomers(timeRange),
        this.getSatisfactionByCategory(timeRange),
        this.getComplaintsAnalysis(timeRange)
      ])

      const result: CustomerSatisfactionMetrics = {
        totalFeedbacks: satisfactionStats.total,
        averageRating: satisfactionStats.avgRating,
        ratingDistribution: satisfactionStats.distribution,
        customerSatisfactionTrend: satisfactionTrend,
        topCustomers,
        satisfactionByCategory: categoryRatings,
        complaintsAnalysis: complaintsData
      }

      await CacheService.setex(cacheKey, this.CACHE_TTL, JSON.stringify(result))
      
      console.log('✅ 客户满意度指标计算完成')
      return result

    } catch (error) {
      console.error('计算客户满意度指标失败:', error)
      throw error
    }
  }

  /**
   * 获取SLA合规性报告
   */
  async getSLAComplianceReport(timeRange: AnalyticsTimeRange): Promise<SLAComplianceReport> {
    const cacheKey = `analytics:sla:${timeRange.start.getTime()}-${timeRange.end.getTime()}`
    
    try {
      const cached = await CacheService.get(cacheKey)
      if (cached) {
        return JSON.parse(cached as string)
      }

      console.log('📋 计算SLA合规性指标...')

      const [
        complianceStats,
        responseCompliance,
        resolutionCompliance,
        violationsByPriority,
        violationsByCategory,
        monthlyTrend,
        costImpact
      ] = await Promise.all([
        this.calculateSLAComplianceStats(timeRange),
        this.calculateResponseTimeCompliance(timeRange),
        this.calculateResolutionTimeCompliance(timeRange),
        this.getSLAViolationsByPriority(timeRange),
        this.getSLAViolationsByCategory(timeRange),
        this.getSLAComplianceTrend(timeRange),
        this.calculateSLACostImpact(timeRange)
      ])

      const result: SLAComplianceReport = {
        totalServices: complianceStats.total,
        slaViolations: complianceStats.violations,
        complianceRate: complianceStats.rate,
        responseTimeCompliance: responseCompliance,
        resolutionTimeCompliance: resolutionCompliance,
        violationsByPriority,
        violationsByCategory,
        monthlyTrend,
        costImpact
      }

      await CacheService.setex(cacheKey, this.CACHE_TTL, JSON.stringify(result))
      
      console.log('✅ SLA合规性指标计算完成')
      return result

    } catch (error) {
      console.error('计算SLA合规性指标失败:', error)
      throw error
    }
  }

  /**
   * 获取服务趋势分析
   */
  async getServiceTrendAnalysis(timeRange: AnalyticsTimeRange): Promise<ServiceTrendAnalysis> {
    const cacheKey = `analytics:trends:${timeRange.start.getTime()}-${timeRange.end.getTime()}`
    
    try {
      const cached = await CacheService.get(cacheKey)
      if (cached) {
        return JSON.parse(cached as string)
      }

      console.log('📈 分析服务趋势...')

      const [
        totalServices,
        volumeTrend,
        categoryTrends,
        resolutionTrend,
        backlogAnalysis,
        seasonalPatterns
      ] = await Promise.all([
        this.getTotalServices(timeRange),
        this.getServiceVolumeTrend(timeRange),
        this.getCategoryTrends(timeRange),
        this.getResolutionTimeTrend(timeRange),
        this.getBacklogAnalysis(timeRange),
        this.getSeasonalPatterns(timeRange)
      ])

      const result: ServiceTrendAnalysis = {
        timeRange,
        totalServices,
        serviceVolumeTrend: volumeTrend,
        categoryTrends,
        resolutionTimeTrend: resolutionTrend,
        backlogAnalysis: backlogAnalysis as any,
        seasonalPatterns
      }

      await CacheService.setex(cacheKey, this.CACHE_TTL, JSON.stringify(result))
      
      console.log('✅ 服务趋势分析完成')
      return result

    } catch (error) {
      console.error('服务趋势分析失败:', error)
      throw error
    }
  }

  /**
   * 生成自定义报表
   */
  async generateCustomReport(config: CustomReportConfig): Promise<{
    data: any
    metadata: {
      generatedAt: Date
      recordCount: number
      executionTime: number
    }
  }> {
    const startTime = Date.now()
    console.log(`📊 生成自定义报表: ${config.name}`)

    try {
      // 确定时间范围
      const timeRange = this.resolveTimeRange(config.timeRange)
      
      // 收集指标数据
      const metricsData = await Promise.all(
        config.metrics.map(metric => this.collectMetricData(metric, timeRange))
      )

      // 组合数据
      const reportData = {
        config,
        timeRange,
        metrics: metricsData,
        summary: this.calculateReportSummary(metricsData)
      }

      const executionTime = Date.now() - startTime
      
      // 记录报表生成历史
      await this.recordReportGeneration(config, executionTime)

      console.log(`✅ 自定义报表生成完成: ${config.name} (${executionTime}ms)`)

      return {
        data: reportData,
        metadata: {
          generatedAt: new Date(),
          recordCount: this.calculateRecordCount(metricsData),
          executionTime
        }
      }

    } catch (error) {
      console.error(`生成自定义报表失败: ${config.name}`, error)
      throw error
    }
  }

  /**
   * 导出Excel报表
   */
  async exportToExcel(data: any, fileName: string): Promise<string> {
    try {
      console.log(`📄 导出Excel报表: ${fileName}`)

      const workbook = XLSX.utils.book_new()
      
      // 创建主要数据表
      if (data.metrics) {
        for (let i = 0; i < data.metrics.length; i++) {
          const metric = data.metrics[i]
          const worksheet = XLSX.utils.json_to_sheet(metric.data || [])
          XLSX.utils.book_append_sheet(workbook, worksheet, `指标${i + 1}`)
        }
      }

      // 添加汇总表
      if (data.summary) {
        const summaryData = Object.entries(data.summary).map(([key, value]) => ({
          指标: key,
          数值: value
        }))
        const summarySheet = XLSX.utils.json_to_sheet(summaryData)
        XLSX.utils.book_append_sheet(workbook, summarySheet, '汇总')
      }

      // 确保目录存在
      const exportDir = path.join(process.cwd(), 'uploads', 'exports')
      await fs.promises.mkdir(exportDir, { recursive: true })

      // 生成文件路径
      const filePath = path.join(exportDir, `${fileName}.xlsx`)
      
      // 写入文件
      XLSX.writeFile(workbook, filePath)

      console.log(`✅ Excel报表导出完成: ${filePath}`)
      return filePath

    } catch (error) {
      console.error(`Excel导出失败: ${fileName}`, error)
      throw error
    }
  }

  /**
   * 导出CSV数据
   */
  async exportToCSV(data: any[], fileName: string): Promise<string> {
    try {
      console.log(`📄 导出CSV数据: ${fileName}`)

      const worksheet = XLSX.utils.json_to_sheet(data)
      const csv = XLSX.utils.sheet_to_csv(worksheet)

      const exportDir = path.join(process.cwd(), 'uploads', 'exports')
      await fs.promises.mkdir(exportDir, { recursive: true })

      const filePath = path.join(exportDir, `${fileName}.csv`)
      await fs.promises.writeFile(filePath, csv, 'utf8')

      console.log(`✅ CSV数据导出完成: ${filePath}`)
      return filePath

    } catch (error) {
      console.error(`CSV导出失败: ${fileName}`, error)
      throw error
    }
  }

  // ========== 私有计算方法 ==========

  private async calculateServiceStats(timeRange: AnalyticsTimeRange) {
    const services = await prisma.service.findMany({
      where: {
        createdAt: {
          gte: timeRange.start,
          lte: timeRange.end
        }
      },
      select: {
        id: true,
        status: true,
        actualResponseTime: true,
        actualResolutionTime: true,
        createdAt: true,
        firstResponseAt: true,
        endTime: true,
        priority: true
      }
    })

    const total = services.length
    const completed = services.filter(s => s.status === 'RESOLVED' || s.status === 'CLOSED').length

    // 计算平均响应时间
    const responseTimes = services
      .filter(s => s.actualResponseTime !== null)
      .map(s => s.actualResponseTime!)
    const avgResponseTime = responseTimes.length > 0 
      ? responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length 
      : 0

    // 计算平均解决时间
    const resolutionTimes = services
      .filter(s => s.actualResolutionTime !== null)
      .map(s => s.actualResolutionTime!)
    const avgResolutionTime = resolutionTimes.length > 0
      ? resolutionTimes.reduce((a, b) => a + b, 0) / resolutionTimes.length
      : 0

    // 计算SLA合规率（简化实现）
    const slaCompliantServices = services.filter(s => {
      if (!s.actualResponseTime || !s.actualResolutionTime) return false
      const responseThreshold = this.getResponseTimeThreshold(s.priority)
      const resolutionThreshold = this.getResolutionTimeThreshold(s.priority)
      return s.actualResponseTime <= responseThreshold && s.actualResolutionTime <= resolutionThreshold
    }).length

    const slaCompliance = total > 0 ? (slaCompliantServices / total) * 100 : 0

    return {
      total,
      completed,
      avgResponseTime,
      avgResolutionTime,
      slaCompliance
    }
  }

  private async calculateEngineerWorkload(timeRange: AnalyticsTimeRange) {
    const workloadData = await prisma.user.findMany({
      where: {
        userRoles: {
          some: {
            role: {
              name: { in: ['engineer', 'senior_engineer', 'lead_engineer'] }
            }
          }
        }
      },
      select: {
        id: true,
        fullName: true,
        assignedServices: {
          where: {
            createdAt: {
              gte: timeRange.start,
              lte: timeRange.end
            }
          },
          select: {
            id: true,
            status: true,
            actualResolutionTime: true,
            createdAt: true,
            endTime: true
          }
        },
        workLogs: {
          where: {
            workDate: {
              gte: timeRange.start,
              lte: timeRange.end
            }
          },
          select: {
            workHours: true
          }
        }
      }
    })

    return workloadData.map(engineer => {
      const assigned = engineer.assignedServices
      const completed = assigned.filter(s => s.status === 'RESOLVED' || s.status === 'CLOSED')
      const totalWorkHours = engineer.workLogs.reduce((sum, log) => sum + log.workHours, 0)
      
      const avgResolutionTime = completed.length > 0
        ? completed
            .filter(s => s.actualResolutionTime)
            .reduce((sum, s) => sum + (s.actualResolutionTime || 0), 0) / completed.length
        : 0

      const efficiency = assigned.length > 0 ? (completed.length / assigned.length) * 100 : 0

      return {
        userId: engineer.id,
        userName: engineer.fullName,
        assignedCount: assigned.length,
        completedCount: completed.length,
        avgResolutionTime,
        totalWorkHours,
        efficiency
      }
    })
  }

  private async getServiceDistribution(timeRange: AnalyticsTimeRange, field: 'category' | 'priority' | 'status') {
    const result = await prisma.service.groupBy({
      by: [field],
      where: {
        createdAt: {
          gte: timeRange.start,
          lte: timeRange.end
        }
      },
      _count: {
        id: true
      }
    })

    const distribution: Record<string, number> = {}
    result.forEach(item => {
      distribution[item[field] as string] = item._count.id
    })

    return distribution
  }

  private async calculateSatisfactionStats(timeRange: AnalyticsTimeRange) {
    const services = await prisma.service.findMany({
      where: {
        createdAt: {
          gte: timeRange.start,
          lte: timeRange.end
        },
        satisfaction: {
          not: null
        }
      },
      select: {
        satisfaction: true
      }
    })

    if (services.length === 0) {
      return {
        total: 0,
        avgRating: 0,
        distribution: {}
      }
    }

    const ratings = services.map(s => s.satisfaction!).filter(r => r >= 1 && r <= 5)
    const avgRating = ratings.reduce((sum, rating) => sum + rating, 0) / ratings.length

    const distribution: Record<number, number> = {}
    for (let i = 1; i <= 5; i++) {
      distribution[i] = ratings.filter(r => r === i).length
    }

    return {
      total: ratings.length,
      avgRating: Math.round(avgRating * 10) / 10,
      distribution
    }
  }

  private async getSatisfactionTrend(timeRange: AnalyticsTimeRange) {
    const query = `
      SELECT 
        DATE(created_at) as date,
        AVG(satisfaction) as rating,
        COUNT(*) as count
      FROM services 
      WHERE created_at >= ? AND created_at <= ? AND satisfaction IS NOT NULL
      GROUP BY DATE(created_at)
      ORDER BY date ASC
    `

    const result = await prisma.$queryRawUnsafe(query, timeRange.start, timeRange.end) as any[]
    
    return result.map(row => ({
      date: row.date,
      rating: Math.round(row.rating * 10) / 10,
      count: Number(row.count)
    }))
  }

  private getResponseTimeThreshold(priority: Priority): number {
    const thresholds = {
      'URGENT': 15,
      'HIGH': 60,
      'MEDIUM': 240,
      'LOW': 480
    }
    return thresholds[priority] || 240
  }

  private getResolutionTimeThreshold(priority: Priority): number {
    const thresholds = {
      'URGENT': 4,
      'HIGH': 24,
      'MEDIUM': 72,
      'LOW': 168
    }
    return thresholds[priority] || 72
  }

  private resolveTimeRange(config: CustomReportConfig['timeRange']): AnalyticsTimeRange {
    const now = new Date()
    
    switch (config.type) {
      case 'last7days':
        return {
          start: new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000),
          end: now
        }
      case 'last30days':
        return {
          start: new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000),
          end: now
        }
      case 'lastQuarter':
        return {
          start: new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000),
          end: now
        }
      case 'lastYear':
        return {
          start: new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000),
          end: now
        }
      case 'custom':
        return {
          start: config.start || new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000),
          end: config.end || now
        }
      default:
        return {
          start: new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000),
          end: now
        }
    }
  }

  private async warmupCache() {
    try {
      // 预热最近30天的数据
      const timeRange = {
        start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
        end: new Date()
      }
      
      // 异步预热，不阻塞初始化
      setTimeout(async () => {
        try {
          await Promise.all([
            this.getOperationEfficiencyMetrics(timeRange),
            this.getCustomerSatisfactionMetrics(timeRange),
            this.getSLAComplianceReport(timeRange)
          ])
          console.log('🔥 缓存预热完成')
        } catch (error) {
          console.warn('缓存预热失败:', error)
        }
      }, 5000)
    } catch (error) {
      console.warn('缓存预热启动失败:', error)
    }
  }

  // 其他私有方法的简化实现...
  private async getTotalServices(timeRange: AnalyticsTimeRange): Promise<number> {
    return await prisma.service.count({
      where: {
        createdAt: {
          gte: timeRange.start,
          lte: timeRange.end
        }
      }
    })
  }

  private async getServiceVolumeTrend(timeRange: AnalyticsTimeRange) {
    const query = `
      SELECT 
        DATE(created_at) as date,
        COUNT(*) as count
      FROM services 
      WHERE created_at >= ? AND created_at <= ?
      GROUP BY DATE(created_at)
      ORDER BY date ASC
    `

    const result = await prisma.$queryRawUnsafe(query, timeRange.start, timeRange.end) as any[]
    
    let cumulative = 0
    return result.map(row => {
      cumulative += Number(row.count)
      return {
        date: row.date,
        count: Number(row.count),
        cumulative
      }
    })
  }

  private async getCategoryTrends(timeRange: AnalyticsTimeRange) {
    const result = await prisma.service.groupBy({
      by: ['category', 'createdAt'],
      where: {
        createdAt: {
          gte: timeRange.start,
          lte: timeRange.end
        }
      },
      _count: {
        id: true
      }
    })

    const trends: Record<ServiceCategory, Array<{date: string, count: number}>> = {} as any
    
    // 处理数据分组
    result.forEach(item => {
      const category = item.category
      const date = item.createdAt.toISOString().split('T')[0]
      
      if (!trends[category]) {
        trends[category] = []
      }
      
      trends[category].push({
        date: date as string,
        count: item._count.id
      })
    })

    return trends
  }

  private async getBacklogAnalysis(timeRange: AnalyticsTimeRange) {
    const currentBacklog = await prisma.service.count({
      where: {
        status: {
          in: ['PENDING', 'IN_PROGRESS', 'WAITING_CUSTOMER']
        }
      }
    })

    const oldestTicket = await prisma.service.findFirst({
      where: {
        status: {
          in: ['PENDING', 'IN_PROGRESS', 'WAITING_CUSTOMER']
        }
      },
      orderBy: {
        createdAt: 'asc'
      },
      select: {
        id: true,
        ticketNumber: true,
        createdAt: true
      }
    })

    const daysOld = oldestTicket 
      ? Math.floor((Date.now() - oldestTicket.createdAt.getTime()) / (24 * 60 * 60 * 1000))
      : 0

    return {
      currentBacklog,
      avgBacklogSize: currentBacklog, // 简化实现
      backlogTrend: [], // 简化实现
      oldestTicket: oldestTicket ? {
        id: oldestTicket.id,
        ticketNumber: oldestTicket.ticketNumber,
        daysOld
      } : null
    }
  }

  private async getSeasonalPatterns(timeRange: AnalyticsTimeRange) {
    // 简化实现，返回模拟数据
    return {
      hourlyDistribution: {},
      weeklyDistribution: {},
      monthlyDistribution: {}
    }
  }

  private async collectMetricData(metric: any, timeRange: AnalyticsTimeRange): Promise<any> {
    // 根据指标类型收集相应数据
    switch (metric.type) {
      case 'service_count':
        return await this.getServiceCountMetric(timeRange, metric)
      case 'avg_response_time':
        return await this.getResponseTimeMetric(timeRange, metric)
      case 'sla_compliance':
        return await this.getSLAComplianceMetric(timeRange, metric)
      case 'satisfaction':
        return await this.getSatisfactionMetric(timeRange, metric)
      case 'workload':
        return await this.getWorkloadMetric(timeRange, metric)
      default:
        return { data: [], total: 0 }
    }
  }

  private calculateReportSummary(metricsData: any[]): Record<string, any> {
    // 计算报表汇总信息
    return {
      totalMetrics: metricsData.length,
      generatedAt: new Date().toISOString(),
      dataPoints: metricsData.reduce((sum, metric) => sum + (metric.data?.length || 0), 0)
    }
  }

  private calculateRecordCount(metricsData: any[]): number {
    return metricsData.reduce((sum, metric) => sum + (metric.data?.length || 0), 0)
  }

  private async recordReportGeneration(config: CustomReportConfig, executionTime: number) {
    // 记录报表生成历史
    await prisma.operationLog.create({
      data: {
        operation: 'GENERATE_REPORT',
        entityType: 'REPORT',
        entityId: config.id || 'CUSTOM',
        description: `生成自定义报表: ${config.name}`,
        metadata: JSON.stringify({
          reportName: config.name,
          executionTime,
          metricsCount: config.metrics.length
        })
      }
    })
  }

  // 简化实现的指标收集方法
  private async getServiceCountMetric(timeRange: AnalyticsTimeRange, metric: any) {
    return { data: [], total: 0 }
  }

  private async getResponseTimeMetric(timeRange: AnalyticsTimeRange, metric: any) {
    return { data: [], average: 0 }
  }

  private async getSLAComplianceMetric(timeRange: AnalyticsTimeRange, metric: any) {
    return { data: [], complianceRate: 0 }
  }

  private async getSatisfactionMetric(timeRange: AnalyticsTimeRange, metric: any) {
    return { data: [], averageRating: 0 }
  }

  private async getWorkloadMetric(timeRange: AnalyticsTimeRange, metric: any) {
    return { data: [], totalHours: 0 }
  }

  private async calculateSLAComplianceStats(timeRange: AnalyticsTimeRange) {
    return { total: 0, violations: 0, rate: 0 }
  }

  private async calculateResponseTimeCompliance(timeRange: AnalyticsTimeRange) {
    return { total: 0, compliant: 0, rate: 0, avgResponseTime: 0 }
  }

  private async calculateResolutionTimeCompliance(timeRange: AnalyticsTimeRange) {
    return { total: 0, compliant: 0, rate: 0, avgResolutionTime: 0 }
  }

  private async getSLAViolationsByPriority(timeRange: AnalyticsTimeRange) {
    return {} as Record<Priority, { total: number, violations: number, rate: number }>
  }

  private async getSLAViolationsByCategory(timeRange: AnalyticsTimeRange) {
    return {} as Record<ServiceCategory, { total: number, violations: number, rate: number }>
  }

  private async getSLAComplianceTrend(timeRange: AnalyticsTimeRange) {
    return []
  }

  private async calculateSLACostImpact(timeRange: AnalyticsTimeRange) {
    return { totalViolationCost: 0, avgCostPerViolation: 0, potentialSavings: 0 }
  }

  private async getTopCustomers(timeRange: AnalyticsTimeRange) {
    return []
  }

  private async getSatisfactionByCategory(timeRange: AnalyticsTimeRange) {
    return {} as Record<ServiceCategory, { avgRating: number, count: number }>
  }

  private async getComplaintsAnalysis(timeRange: AnalyticsTimeRange) {
    return {
      totalComplaints: 0,
      resolvedComplaints: 0,
      avgResolutionTime: 0,
      commonIssues: []
    }
  }

  private async getResolutionTimeTrend(timeRange: AnalyticsTimeRange) {
    return []
  }
}