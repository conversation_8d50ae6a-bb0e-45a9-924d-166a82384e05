/**
 * 执行上下文管理器
 * 负责管理工作流执行过程中的上下文、变量和资源
 */

import { EventEmitter } from 'events';
import type {
  ExecutionContext,
  ContextMetadata,
  ResourcePool,
  ContextLogger,
  ExecutionLog,
  ResourceConfig,
  ResourceUsage
} from '@/types/action-executor.types';

/**
 * 上下文日志记录器实现
 */
class ExecutionContextLogger implements ContextLogger {
  private logs: ExecutionLog[] = [];

  constructor(
    private executionId: string,
    private stepIndex: number
  ) {}

  debug(message: string, metadata?: any): void {
    this.addLog('DEBUG', message, metadata);
  }

  info(message: string, metadata?: any): void {
    this.addLog('INFO', message, metadata);
  }

  warn(message: string, metadata?: any): void {
    this.addLog('WARN', message, metadata);
  }

  error(message: string, error?: Error, metadata?: any): void {
    const logMetadata = {
      ...metadata,
      error: error ? {
        message: error.message,
        stack: error.stack,
        name: error.name
      } : undefined
    };
    this.addLog('ERROR', message, logMetadata);
  }

  private addLog(level: ExecutionLog['level'], message: string, metadata?: any): void {
    const log: ExecutionLog = {
      level,
      message,
      timestamp: new Date(),
      metadata,
      source: `execution:${this.executionId}:step:${this.stepIndex}`
    };

    this.logs.push(log);
    
    // 控制台输出
    const logMessage = `[${log.timestamp.toISOString()}] ${level} - ${message}`;
    switch (level) {
      case 'DEBUG':
        console.debug(logMessage, metadata);
        break;
      case 'INFO':
        console.info(logMessage, metadata);
        break;
      case 'WARN':
        console.warn(logMessage, metadata);
        break;
      case 'ERROR':
        console.error(logMessage, metadata);
        break;
    }
  }

  getLogs(): ExecutionLog[] {
    return [...this.logs];
  }

  clearLogs(): void {
    this.logs = [];
  }
}

/**
 * 资源池管理器
 */
class ResourcePoolManager {
  private httpClients = new Map<string, any>();
  private databaseConnections = new Map<string, any>();
  private cacheClients = new Map<string, any>();
  private messageQueues = new Map<string, any>();

  constructor(private config: ResourceConfig) {}

  getResourcePool(): ResourcePool {
    return {
      httpClients: this.httpClients,
      databaseConnections: this.databaseConnections,
      cacheClients: this.cacheClients,
      messageQueues: this.messageQueues
    };
  }

  async createHttpClient(identifier: string): Promise<any> {
    if (this.httpClients.has(identifier)) {
      return this.httpClients.get(identifier);
    }

    // 这里应该创建实际的HTTP客户端实例
    const client = {
      timeout: this.config.httpTimeout,
      maxConnections: this.config.maxHttpConnections,
      identifier,
      createdAt: new Date()
    };

    this.httpClients.set(identifier, client);
    return client;
  }

  async createDatabaseConnection(identifier: string): Promise<any> {
    if (this.databaseConnections.has(identifier)) {
      return this.databaseConnections.get(identifier);
    }

    // 这里应该创建实际的数据库连接
    const connection = {
      poolSize: this.config.databasePoolSize,
      identifier,
      createdAt: new Date()
    };

    this.databaseConnections.set(identifier, connection);
    return connection;
  }

  async createCacheClient(identifier: string): Promise<any> {
    if (this.cacheClients.has(identifier)) {
      return this.cacheClients.get(identifier);
    }

    // 这里应该创建实际的缓存客户端
    const client = {
      timeout: this.config.cacheTimeout,
      identifier,
      createdAt: new Date()
    };

    this.cacheClients.set(identifier, client);
    return client;
  }

  async cleanup(): Promise<void> {
    // 清理所有资源
    for (const [id, client] of this.httpClients) {
      try {
        // 这里应该调用实际的清理方法
        console.log(`清理HTTP客户端: ${id}`);
      } catch (error) {
        console.error(`清理HTTP客户端失败 ${id}:`, error);
      }
    }

    for (const [id, connection] of this.databaseConnections) {
      try {
        // 这里应该调用实际的连接关闭方法
        console.log(`关闭数据库连接: ${id}`);
      } catch (error) {
        console.error(`关闭数据库连接失败 ${id}:`, error);
      }
    }

    this.httpClients.clear();
    this.databaseConnections.clear();
    this.cacheClients.clear();
    this.messageQueues.clear();
  }

  getResourceUsage(): ResourceUsage {
    return {
      cpu: 0, // 这里应该获取实际的CPU使用率
      memory: process.memoryUsage().heapUsed / 1024 / 1024, // MB
      io: 0, // 这里应该获取实际的IO使用率
      network: 0, // 这里应该获取实际的网络使用率
      duration: 0
    };
  }
}

/**
 * 执行上下文管理器
 */
export class ExecutionContextManager extends EventEmitter {
  private static instance: ExecutionContextManager;
  private contexts = new Map<string, ExecutionContext>();
  private resourcePools = new Map<string, ResourcePoolManager>();
  private config: ResourceConfig;

  constructor() {
    super();
    this.config = {
      httpTimeout: 30000, // 30秒
      maxHttpConnections: 100,
      databasePoolSize: 10,
      cacheTimeout: 5000, // 5秒
      memoryLimit: 512, // MB
      cpuLimit: 80 // 百分比
    };
  }

  /**
   * 获取单例实例
   */
  static getInstance(): ExecutionContextManager {
    if (!ExecutionContextManager.instance) {
      ExecutionContextManager.instance = new ExecutionContextManager();
    }
    return ExecutionContextManager.instance;
  }

  /**
   * 创建执行上下文
   */
  async createContext(
    executionId: string,
    workflowId: string,
    stepIndex: number,
    variables: Record<string, any> = {}
  ): Promise<ExecutionContext> {
    const contextKey = `${executionId}:${stepIndex}`;

    // 检查是否已存在
    if (this.contexts.has(contextKey)) {
      return this.contexts.get(contextKey)!;
    }

    // 创建或获取资源池
    let resourcePoolManager = this.resourcePools.get(executionId);
    if (!resourcePoolManager) {
      resourcePoolManager = new ResourcePoolManager(this.config);
      this.resourcePools.set(executionId, resourcePoolManager);
    }

    // 创建上下文元数据
    const metadata: ContextMetadata = {
      executionId,
      workflowId,
      stepIndex,
      attemptNumber: 1,
      startTime: new Date(),
      timeout: 300, // 5分钟默认超时
    };

    // 创建日志记录器
    const logger = new ExecutionContextLogger(executionId, stepIndex);

    // 创建中断控制器
    const abortController = new AbortController();

    // 创建执行上下文
    const context: ExecutionContext = {
      executionId,
      workflowId,
      stepIndex,
      variables: { ...variables },
      metadata,
      resources: resourcePoolManager.getResourcePool(),
      logger,
      abortController
    };

    this.contexts.set(contextKey, context);

    // 设置超时
    if (metadata.timeout) {
      setTimeout(() => {
        if (this.contexts.has(contextKey)) {
          abortController.abort();
          this.emit('context:timeout', { executionId, stepIndex });
        }
      }, metadata.timeout * 1000);
    }

    this.emit('context:created', { executionId, stepIndex, workflowId });
    
    logger.info(`创建执行上下文: 工作流=${workflowId}, 步骤=${stepIndex}`, {
      executionId,
      workflowId,
      stepIndex
    });

    return context;
  }

  /**
   * 获取执行上下文
   */
  getContext(executionId: string, stepIndex: number): ExecutionContext | undefined {
    const contextKey = `${executionId}:${stepIndex}`;
    return this.contexts.get(contextKey);
  }

  /**
   * 更新上下文变量
   */
  updateContextVariables(
    executionId: string,
    stepIndex: number,
    variables: Record<string, any>
  ): boolean {
    const context = this.getContext(executionId, stepIndex);
    if (!context) {
      return false;
    }

    // 合并变量
    context.variables = { ...context.variables, ...variables };

    context.logger.debug('更新上下文变量', {
      newVariables: variables,
      allVariables: context.variables
    });

    this.emit('context:variables:updated', {
      executionId,
      stepIndex,
      variables: context.variables
    });

    return true;
  }

  /**
   * 获取上下文变量
   */
  getContextVariables(executionId: string, stepIndex: number): Record<string, any> | undefined {
    const context = this.getContext(executionId, stepIndex);
    return context?.variables;
  }

  /**
   * 增加重试计数
   */
  incrementAttempt(executionId: string, stepIndex: number): boolean {
    const context = this.getContext(executionId, stepIndex);
    if (!context) {
      return false;
    }

    context.metadata.attemptNumber++;

    context.logger.warn(`步骤重试: 第 ${context.metadata.attemptNumber} 次尝试`);

    this.emit('context:attempt:incremented', {
      executionId,
      stepIndex,
      attemptNumber: context.metadata.attemptNumber
    });

    return true;
  }

  /**
   * 获取资源使用情况
   */
  getResourceUsage(executionId: string): ResourceUsage | undefined {
    const resourcePool = this.resourcePools.get(executionId);
    return resourcePool?.getResourceUsage();
  }

  /**
   * 检查资源限制
   */
  checkResourceLimits(executionId: string): boolean {
    const usage = this.getResourceUsage(executionId);
    if (!usage) {
      return true;
    }

    // 检查内存限制
    if (usage.memory && usage.memory > this.config.memoryLimit) {
      this.emit('context:resource:limit:exceeded', {
        executionId,
        type: 'memory',
        current: usage.memory,
        limit: this.config.memoryLimit
      });
      return false;
    }

    // 检查CPU限制
    if (usage.cpu && usage.cpu > this.config.cpuLimit) {
      this.emit('context:resource:limit:exceeded', {
        executionId,
        type: 'cpu',
        current: usage.cpu,
        limit: this.config.cpuLimit
      });
      return false;
    }

    return true;
  }

  /**
   * 清理执行上下文
   */
  async cleanupContext(executionId: string, stepIndex?: number): Promise<void> {
    if (stepIndex !== undefined) {
      // 清理特定步骤的上下文
      const contextKey = `${executionId}:${stepIndex}`;
      const context = this.contexts.get(contextKey);
      
      if (context) {
        context.abortController.abort();
        this.contexts.delete(contextKey);
        
        context.logger.info('清理步骤上下文');
        this.emit('context:cleaned', { executionId, stepIndex });
      }
    } else {
      // 清理整个执行的上下文
      const contextKeys = Array.from(this.contexts.keys()).filter(key => 
        key.startsWith(`${executionId}:`)
      );

      for (const key of contextKeys) {
        const context = this.contexts.get(key);
        if (context) {
          context.abortController.abort();
          this.contexts.delete(key);
        }
      }

      // 清理资源池
      const resourcePool = this.resourcePools.get(executionId);
      if (resourcePool) {
        await resourcePool.cleanup();
        this.resourcePools.delete(executionId);
      }

      this.emit('context:execution:cleaned', { executionId });
    }
  }

  /**
   * 列出所有活跃的上下文
   */
  listActiveContexts(): Array<{
    executionId: string;
    stepIndex: number;
    workflowId: string;
    createdAt: Date;
    attemptNumber: number;
  }> {
    return Array.from(this.contexts.entries()).map(([key, context]) => ({
      executionId: context.executionId,
      stepIndex: context.stepIndex,
      workflowId: context.workflowId,
      createdAt: context.metadata.startTime,
      attemptNumber: context.metadata.attemptNumber
    }));
  }

  /**
   * 获取系统统计信息
   */
  getSystemStats(): any {
    const totalContexts = this.contexts.size;
    const totalResourcePools = this.resourcePools.size;
    
    const resourceUsage = Array.from(this.resourcePools.values())
      .map(pool => pool.getResourceUsage())
      .reduce(
        (acc, usage) => ({
          cpu: Math.max(acc.cpu || 0, usage.cpu || 0),
          memory: (acc.memory || 0) + (usage.memory || 0),
          io: Math.max(acc.io || 0, usage.io || 0),
          network: (acc.network || 0) + (usage.network || 0),
          duration: Math.max(acc.duration || 0, usage.duration || 0)
        }),
        { cpu: 0, memory: 0, io: 0, network: 0, duration: 0 }
      );

    return {
      totalContexts,
      totalResourcePools,
      resourceUsage,
      config: this.config,
      activeExecutions: Array.from(new Set(
        Array.from(this.contexts.keys()).map(key => key.split(':')[0])
      ))
    };
  }

  /**
   * 更新配置
   */
  updateConfig(config: Partial<ResourceConfig>): void {
    this.config = { ...this.config, ...config };
    this.emit('config:updated', this.config);
  }

  /**
   * 全局清理
   */
  async cleanup(): Promise<void> {
    console.log('🧹 开始清理 ExecutionContextManager...');

    // 中断所有上下文
    for (const [key, context] of this.contexts) {
      context.abortController.abort();
      context.logger.warn('系统关闭，强制中断执行上下文');
    }

    // 清理所有资源池
    const cleanupPromises = Array.from(this.resourcePools.values()).map(pool => 
      pool.cleanup()
    );
    await Promise.all(cleanupPromises);

    this.contexts.clear();
    this.resourcePools.clear();

    console.log('✅ ExecutionContextManager 清理完成');
    this.emit('cleanup:completed');
  }
}

// 导出单例实例
export const executionContextManager = ExecutionContextManager.getInstance();