import { v4 as uuidv4 } from 'uuid'
import {
  RealtimeMessage,
  RealtimeEventType,
  RealtimeChannelType,
  RealtimePriority,
  PushStrategy,
  SystemMetricsMessage,
  SystemStatusMessage,
  SystemAlertMessage,
  UserActivityMessage,
  OnlineUsersMessage,
  ServiceMessage,
  AuditLogMessage,
  ConfigChangeMessage,
  NotificationMessage
} from '@shared/types/realtime'
import { WebSocketService } from './websocket.service'
import { CacheService } from '@/config/redis.config'

export class RealtimeService {
  private static instance: RealtimeService
  private webSocketService: WebSocketService
  private messageHistory = new Map<string, RealtimeMessage[]>()
  private throttleMap = new Map<string, { lastSent: number; count: number }>()

  private constructor() {
    this.webSocketService = WebSocketService.getInstance()
  }

  static getInstance(): RealtimeService {
    if (!RealtimeService.instance) {
      RealtimeService.instance = new RealtimeService()
    }
    return RealtimeService.instance
  }

  /**
   * 推送系统监控数据
   */
  async pushSystemMetrics(metrics: {
    cpu: number
    memory: number
    disk: number
    network: number
  }): Promise<number> {
    const message: SystemMetricsMessage = {
      id: uuidv4(),
      type: RealtimeEventType.SYSTEM_METRICS_UPDATE,
      channel: RealtimeChannelType.SYSTEM_MONITOR,
      priority: RealtimePriority.NORMAL,
      timestamp: new Date().toISOString(),
      data: {
        ...metrics,
        timestamp: new Date().toISOString()
      }
    }

    const strategy: PushStrategy = {
      channels: [RealtimeChannelType.SYSTEM_MONITOR, RealtimeChannelType.ADMIN],
      priority: RealtimePriority.NORMAL,
      throttle: {
        enabled: true,
        interval: 5000, // 5秒
        maxPerInterval: 1
      },
      filter: {
        permissions: ['system:monitor']
      }
    }

    if (this.shouldThrottle('system_metrics', strategy)) {
      return 0
    }

    const sentCount = this.webSocketService.broadcastToChannel(
      RealtimeChannelType.SYSTEM_MONITOR, 
      message, 
      strategy
    )

    // 缓存最新的监控数据
    await CacheService.set('realtime:system_metrics', JSON.stringify(metrics), 300) // 5分钟缓存

    return sentCount
  }

  /**
   * 推送系统状态变更
   */
  async pushSystemStatusChange(status: {
    overall: 'healthy' | 'warning' | 'critical'
    services: Array<{
      name: string
      status: 'healthy' | 'warning' | 'critical' | 'down'
      responseTime: number
      message?: string
    }>
  }): Promise<number> {
    const message: SystemStatusMessage = {
      id: uuidv4(),
      type: RealtimeEventType.SYSTEM_STATUS_CHANGE,
      channel: RealtimeChannelType.SYSTEM_MONITOR,
      priority: status.overall === 'critical' ? RealtimePriority.CRITICAL : RealtimePriority.HIGH,
      timestamp: new Date().toISOString(),
      data: {
        ...status,
        timestamp: new Date().toISOString()
      }
    }

    const strategy: PushStrategy = {
      channels: [RealtimeChannelType.SYSTEM_MONITOR, RealtimeChannelType.ADMIN],
      priority: message.priority,
      filter: {
        permissions: ['system:monitor']
      }
    }

    const sentCount = this.webSocketService.broadcast(message, strategy)

    // 如果是严重状态，也推送给全局频道
    if (status.overall === 'critical') {
      const criticalMessage = {
        ...message,
        channel: RealtimeChannelType.GLOBAL,
        data: {
          ...message.data,
          message: '系统状态严重异常，请立即检查！'
        }
      }
      this.webSocketService.broadcast(criticalMessage)
    }

    return sentCount
  }

  /**
   * 推送系统告警
   */
  async pushSystemAlert(alert: {
    id: string
    title: string
    message: string
    severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
    component: string
    status: 'PENDING' | 'ACKNOWLEDGED' | 'RESOLVED'
    acknowledgedBy?: string
    resolvedBy?: string
  }): Promise<number> {
    const message: SystemAlertMessage = {
      id: uuidv4(),
      type: alert.status === 'PENDING' ? RealtimeEventType.SYSTEM_ALERT_NEW : RealtimeEventType.SYSTEM_ALERT_UPDATE,
      channel: RealtimeChannelType.SYSTEM_MONITOR,
      priority: this.mapAlertSeverityToPriority(alert.severity),
      timestamp: new Date().toISOString(),
      data: {
        ...alert,
        timestamp: new Date().toISOString()
      }
    }

    const strategy: PushStrategy = {
      channels: [RealtimeChannelType.SYSTEM_MONITOR, RealtimeChannelType.ADMIN],
      priority: message.priority,
      filter: {
        permissions: ['system:monitor']
      }
    }

    let sentCount = this.webSocketService.broadcast(message, strategy)

    // 严重告警推送给更多用户
    if (alert.severity === 'CRITICAL') {
      const criticalNotification = {
        ...message,
        channel: RealtimeChannelType.GLOBAL,
        type: RealtimeEventType.NOTIFICATION_NEW,
        data: {
          id: uuidv4(),
          type: 'error' as const,
          title: '严重系统告警',
          message: `${alert.component}: ${alert.title}`,
          persistent: true,
          timestamp: new Date().toISOString()
        }
      }
      sentCount += this.webSocketService.broadcast(criticalNotification)
    }

    return sentCount
  }

  /**
   * 推送用户活动
   */
  async pushUserActivity(activity: {
    userId: string
    username: string
    fullName: string
    action: string
    metadata?: any
  }): Promise<number> {
    const message: UserActivityMessage = {
      id: uuidv4(),
      type: RealtimeEventType.USER_ACTIVITY,
      channel: RealtimeChannelType.GLOBAL,
      priority: RealtimePriority.LOW,
      timestamp: new Date().toISOString(),
      data: {
        ...activity,
        timestamp: new Date().toISOString()
      }
    }

    const strategy: PushStrategy = {
      channels: [RealtimeChannelType.ADMIN],
      priority: RealtimePriority.LOW,
      filter: {
        permissions: ['user:view']
      }
    }

    return this.webSocketService.broadcastToChannel(RealtimeChannelType.ADMIN, message, strategy)
  }

  /**
   * 推送在线用户更新
   */
  async pushOnlineUsersUpdate(): Promise<number> {
    const onlineUsers = this.webSocketService.getOnlineUsers()

    const message: OnlineUsersMessage = {
      id: uuidv4(),
      type: RealtimeEventType.ONLINE_USERS_UPDATE,
      channel: RealtimeChannelType.GLOBAL,
      priority: RealtimePriority.LOW,
      timestamp: new Date().toISOString(),
      data: {
        count: onlineUsers.length,
        users: onlineUsers.map(user => ({
          userId: user.userId,
          username: user.username,
          fullName: user.username,
          lastActivity: user.lastActivity,
          status: 'active' as const
        }))
      }
    }

    return this.webSocketService.broadcast(message)
  }

  /**
   * 推送服务工单相关事件
   */
  async pushServiceEvent(
    eventType: RealtimeEventType.SERVICE_CREATED | RealtimeEventType.SERVICE_UPDATED | 
               RealtimeEventType.SERVICE_STATUS_CHANGED | RealtimeEventType.SERVICE_ASSIGNED,
    serviceData: {
      id: string
      ticketNumber: string
      title: string
      status: string
      priority: string
      assignedTo?: string
      customerName: string
      updatedBy: string
      changes?: Record<string, any>
    }
  ): Promise<number> {
    const message: ServiceMessage = {
      id: uuidv4(),
      type: eventType,
      channel: RealtimeChannelType.GLOBAL,
      priority: this.mapServicePriorityToRealtimePriority(serviceData.priority),
      timestamp: new Date().toISOString(),
      data: {
        ...serviceData,
        timestamp: new Date().toISOString()
      }
    }

    const strategy: PushStrategy = {
      channels: [RealtimeChannelType.GLOBAL],
      priority: message.priority
    }

    let sentCount = this.webSocketService.broadcast(message, strategy)

    // 如果有指定负责人，单独推送给他
    if (serviceData.assignedTo) {
      const personalMessage = {
        ...message,
        channel: RealtimeChannelType.USER,
        priority: RealtimePriority.HIGH
      }
      sentCount += this.webSocketService.sendToUser(serviceData.assignedTo, personalMessage)
    }

    return sentCount
  }

  /**
   * 推送审计日志
   */
  async pushAuditLog(logData: {
    id: string
    userId: string
    username: string
    fullName: string
    action: string
    resource: string
    resourceId?: string
    ip: string
    details?: any
  }): Promise<number> {
    const message: AuditLogMessage = {
      id: uuidv4(),
      type: RealtimeEventType.AUDIT_LOG_NEW,
      channel: RealtimeChannelType.ADMIN,
      priority: RealtimePriority.LOW,
      timestamp: new Date().toISOString(),
      data: {
        ...logData,
        timestamp: new Date().toISOString()
      }
    }

    const strategy: PushStrategy = {
      channels: [RealtimeChannelType.ADMIN],
      priority: RealtimePriority.LOW,
      filter: {
        permissions: ['audit:view']
      }
    }

    // 敏感操作的审计日志提高优先级
    const sensitiveActions = ['DELETE', 'CREATE', 'UPDATE']
    const sensitiveResources = ['USER', 'ROLE', 'SYSTEM_CONFIG']
    
    if (sensitiveActions.includes(logData.action) && sensitiveResources.includes(logData.resource)) {
      message.priority = RealtimePriority.NORMAL
      strategy.priority = RealtimePriority.NORMAL
    }

    return this.webSocketService.broadcastToChannel(RealtimeChannelType.ADMIN, message, strategy)
  }

  /**
   * 推送配置变更
   */
  async pushConfigChange(changeData: {
    category: string
    key: string
    oldValue?: any
    newValue: any
    changedBy: string
    changeReason?: string
  }): Promise<number> {
    const message: ConfigChangeMessage = {
      id: uuidv4(),
      type: RealtimeEventType.CONFIG_CHANGED,
      channel: RealtimeChannelType.ADMIN,
      priority: RealtimePriority.NORMAL,
      timestamp: new Date().toISOString(),
      data: {
        ...changeData,
        timestamp: new Date().toISOString()
      }
    }

    const strategy: PushStrategy = {
      channels: [RealtimeChannelType.ADMIN],
      priority: RealtimePriority.NORMAL,
      filter: {
        permissions: ['system:config']
      }
    }

    return this.webSocketService.broadcastToChannel(RealtimeChannelType.ADMIN, message, strategy)
  }

  /**
   * 推送通知
   */
  async pushNotification(
    notification: {
      id: string
      type: 'info' | 'warning' | 'error' | 'success'
      title: string
      message: string
      targetUserId?: string
      actionUrl?: string
      persistent: boolean
    },
    isUpdate = false
  ): Promise<number> {
    const message: NotificationMessage = {
      id: uuidv4(),
      type: isUpdate ? RealtimeEventType.NOTIFICATION_UPDATE : RealtimeEventType.NOTIFICATION_NEW,
      channel: notification.targetUserId ? RealtimeChannelType.USER : RealtimeChannelType.GLOBAL,
      priority: notification.type === 'error' ? RealtimePriority.HIGH : RealtimePriority.NORMAL,
      timestamp: new Date().toISOString(),
      data: {
        ...notification,
        timestamp: new Date().toISOString()
      }
    }

    if (notification.targetUserId) {
      return this.webSocketService.sendToUser(notification.targetUserId, message)
    } else {
      return this.webSocketService.broadcast(message)
    }
  }

  /**
   * 推送安全事件
   */
  async pushSecurityEvent(event: {
    type: 'login_failed' | 'suspicious_activity' | 'permission_denied' | 'account_locked'
    userId?: string
    ip: string
    userAgent?: string
    details: any
  }): Promise<number> {
    const message: RealtimeMessage = {
      id: uuidv4(),
      type: RealtimeEventType.SECURITY_EVENT,
      channel: RealtimeChannelType.ADMIN,
      priority: RealtimePriority.HIGH,
      timestamp: new Date().toISOString(),
      data: {
        ...event,
        timestamp: new Date().toISOString()
      }
    }

    const strategy: PushStrategy = {
      channels: [RealtimeChannelType.ADMIN],
      priority: RealtimePriority.HIGH,
      filter: {
        permissions: ['security:monitor']
      }
    }

    return this.webSocketService.broadcastToChannel(RealtimeChannelType.ADMIN, message, strategy)
  }

  /**
   * 获取实时统计
   */
  getRealtimeStats() {
    return this.webSocketService.getStats()
  }

  /**
   * 获取在线用户
   */
  getOnlineUsers() {
    return this.webSocketService.getOnlineUsers()
  }

  /**
   * 判断是否应该节流
   */
  private shouldThrottle(key: string, strategy?: PushStrategy): boolean {
    if (!strategy?.throttle?.enabled) {
      return false
    }

    const now = Date.now()
    const throttleInfo = this.throttleMap.get(key)

    if (!throttleInfo) {
      this.throttleMap.set(key, { lastSent: now, count: 1 })
      return false
    }

    const timeSinceLastSent = now - throttleInfo.lastSent

    if (timeSinceLastSent >= strategy.throttle.interval) {
      // 重置计数器
      this.throttleMap.set(key, { lastSent: now, count: 1 })
      return false
    }

    if (throttleInfo.count >= strategy.throttle.maxPerInterval) {
      return true // 达到限制，需要节流
    }

    // 更新计数器
    throttleInfo.count++
    return false
  }

  /**
   * 映射告警严重级别到实时推送优先级
   */
  private mapAlertSeverityToPriority(severity: string): RealtimePriority {
    switch (severity) {
      case 'CRITICAL':
        return RealtimePriority.CRITICAL
      case 'HIGH':
        return RealtimePriority.HIGH
      case 'MEDIUM':
        return RealtimePriority.NORMAL
      case 'LOW':
      default:
        return RealtimePriority.LOW
    }
  }

  /**
   * 映射服务优先级到实时推送优先级
   */
  private mapServicePriorityToRealtimePriority(priority: string): RealtimePriority {
    switch (priority?.toUpperCase()) {
      case 'URGENT':
        return RealtimePriority.CRITICAL
      case 'HIGH':
        return RealtimePriority.HIGH
      case 'MEDIUM':
        return RealtimePriority.NORMAL
      case 'LOW':
      default:
        return RealtimePriority.LOW
    }
  }

  /**
   * 清理资源
   */
  async cleanup(): Promise<void> {
    this.messageHistory.clear()
    this.throttleMap.clear()
  }
}