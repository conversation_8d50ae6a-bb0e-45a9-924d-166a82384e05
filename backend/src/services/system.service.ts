import { prisma } from '@/config/database.config'
import { CacheService } from '@/config/redis.config'
import { ErrorFactory } from '@/utils/errors.util'
import { encrypt, decrypt } from '@/utils/crypto.util'
import { NotificationUtil } from '@/utils/notification.util'
import * as os from 'os'
import * as fs from 'fs/promises'
import { execSync } from 'child_process'
import {
  SystemConfig,
  SystemConfigCategory,
  SystemConfigDataType,
  CreateSystemConfigInput,
  UpdateSystemConfigInput,
  SystemConfigFilter,
  ConfigTestResult,
  EmailTestConfig,
  SmsTestConfig,
  ConfigExport,
  ConfigImportOptions,
  ConfigImportResult,
  SystemConfigDefaults
} from '@/types/system.types'

export interface CreateAlertRuleInput {
  name: string
  description?: string
  metricType: 'CPU' | 'MEMORY' | 'DISK' | 'NETWORK' | 'SERVICE'
  condition: '>' | '<' | '>=' | '<=' | '='
  threshold: number
  duration: number
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
  enabled?: boolean
  notificationChannels?: ('EMAIL' | 'SMS' | 'WEBHOOK')[]
}

export interface SystemResourceUsage {
  cpu: {
    usage: number
    cores: number
    loadAverage: number[]
  }
  memory: {
    total: number
    used: number
    free: number
    usage: number
  }
  disk: {
    total: number
    used: number
    free: number
    usage: number
  }
  network: {
    interfaces: {
      name: string
      rx: number
      tx: number
    }[]
  }
}

export class SystemService {
  /**
   * 获取系统状态
   */
  static async getSystemStatus() {
    const cacheKey = 'system:status'
    
    // 尝试从缓存获取
    const cached = await CacheService.get(cacheKey)
    if (cached) {
      return JSON.parse(cached)
    }

    const status = {
      uptime: os.uptime(),
      nodeVersion: process.version,
      platform: os.platform(),
      arch: os.arch(),
      hostname: os.hostname(),
      timestamp: new Date().toISOString(),
      services: {
        database: await this.checkDatabaseHealth(),
        redis: await this.checkRedisHealth(),
        api: true // 如果能执行到这里说明API服务正常
      }
    }

    // 缓存1分钟
    await CacheService.setex(cacheKey, 60, JSON.stringify(status))
    
    return status
  }

  /**
   * 获取系统资源使用情况
   */
  static async getSystemResources(): Promise<SystemResourceUsage> {
    const cacheKey = 'system:resources'
    
    // 尝试从缓存获取
    const cached = await CacheService.get(cacheKey)
    if (cached) {
      return JSON.parse(cached)
    }

    const resources: SystemResourceUsage = {
      cpu: {
        usage: await this.getCpuUsage(),
        cores: os.cpus().length,
        loadAverage: os.loadavg()
      },
      memory: this.getMemoryUsage(),
      disk: await this.getDiskUsage(),
      network: await this.getNetworkUsage()
    }

    // 缓存30秒
    await CacheService.setex(cacheKey, 30, JSON.stringify(resources))
    
    return resources
  }

  /**
   * 获取健康检查
   */
  static async getHealthCheck() {
    const checks = {
      database: await this.checkDatabaseHealth(),
      redis: await this.checkRedisHealth(),
      diskSpace: await this.checkDiskSpace(),
      memory: this.checkMemoryUsage(),
      services: await this.checkCriticalServices()
    }

    const healthy = Object.values(checks).every(check => 
      typeof check === 'boolean' ? check : check.status === 'healthy'
    )

    return {
      status: healthy ? 'healthy' : 'unhealthy',
      timestamp: new Date().toISOString(),
      checks
    }
  }

  /**
   * 获取数据库状态
   */
  static async getDatabaseStatus() {
    try {
      const startTime = Date.now()
      
      // 测试数据库连接
      await prisma.$queryRaw`SELECT 1`
      
      const responseTime = Date.now() - startTime
      
      // 获取数据库统计信息
      const [userCount, serviceCount, logCount] = await Promise.all([
        prisma.user.count(),
        prisma.service.count(),
        prisma.operationLog.count()
      ])

      return {
        status: 'connected',
        responseTime,
        statistics: {
          totalUsers: userCount,
          totalServices: serviceCount,
          totalLogs: logCount
        },
        timestamp: new Date().toISOString()
      }
    } catch (error) {
      return {
        status: 'disconnected',
        error: error instanceof Error ? error.message : '未知错误',
        timestamp: new Date().toISOString()
      }
    }
  }

  /**
   * 获取Redis状态
   */
  static async getRedisStatus() {
    try {
      const startTime = Date.now()
      
      // 测试Redis连接
      await CacheService.ping()
      
      const responseTime = Date.now() - startTime

      return {
        status: 'connected',
        responseTime,
        timestamp: new Date().toISOString()
      }
    } catch (error) {
      return {
        status: 'disconnected',
        error: error instanceof Error ? error.message : '未知错误',
        timestamp: new Date().toISOString()
      }
    }
  }

  /**
   * 获取性能指标
   */
  static async getPerformanceMetrics(hours: number = 24) {
    // 这里应该从实际的监控系统或日志中获取历史数据
    // 为了演示，生成模拟数据
    const metrics = []
    const now = Date.now()
    const interval = (hours * 60 * 60 * 1000) / 100 // 100个数据点

    for (let i = 0; i < 100; i++) {
      const timestamp = new Date(now - (99 - i) * interval)
      metrics.push({
        timestamp: timestamp.toISOString(),
        cpu: Math.random() * 100,
        memory: Math.random() * 100,
        disk: Math.random() * 100,
        network: Math.random() * 1000000 // bytes/s
      })
    }

    return metrics
  }

  /**
   * 创建告警规则
   */
  static async createAlertRule(data: CreateAlertRuleInput) {
    // 检查规则名称唯一性
    const existingRule = await prisma.alertRule.findUnique({
      where: { name: data.name }
    })

    if (existingRule) {
      throw ErrorFactory.conflict('告警规则名称已存在')
    }

    // 映射条件
    const conditionMap: { [key: string]: any } = {
      '>': 'GT',
      '<': 'LT',
      '>=': 'GTE',
      '<=': 'LTE',
      '=': 'EQ'
    }

    const rule = await prisma.alertRule.create({
      data: {
        name: data.name,
        description: data.description,
        metricType: data.metricType,
        condition: conditionMap[data.condition] || 'GT',
        threshold: data.threshold,
        duration: data.duration,
        severity: data.severity,
        enabled: data.enabled ?? true,
        notificationChannels: data.notificationChannels || []
      }
    })

    return rule
  }

  /**
   * 获取告警规则列表
   */
  static async getAlertRules() {
    const rules = await prisma.alertRule.findMany({
      orderBy: { createdAt: 'desc' },
      include: {
        _count: {
          select: {
            alerts: true
          }
        }
      }
    })

    return rules.map(rule => ({
      ...rule,
      alertCount: rule._count.alerts,
      _count: undefined
    }))
  }

  /**
   * 更新告警规则
   */
  static async updateAlertRule(id: string, data: Partial<CreateAlertRuleInput>) {
    // 检查规则是否存在
    const existingRule = await prisma.alertRule.findUnique({
      where: { id }
    })

    if (!existingRule) {
      throw ErrorFactory.notFound('告警规则')
    }

    // 检查名称唯一性（如果更新了名称）
    if (data.name && data.name !== existingRule.name) {
      const duplicateName = await prisma.alertRule.findUnique({
        where: { name: data.name }
      })

      if (duplicateName) {
        throw ErrorFactory.conflict('告警规则名称已存在')
      }
    }

    // 映射条件
    const conditionMap: { [key: string]: any } = {
      '>': 'GT',
      '<': 'LT',
      '>=': 'GTE',
      '<=': 'LTE',
      '=': 'EQ'
    }

    const updateData: any = {}
    if (data.name !== undefined) updateData.name = data.name
    if (data.description !== undefined) updateData.description = data.description
    if (data.metricType !== undefined) updateData.metricType = data.metricType
    if (data.condition !== undefined) updateData.condition = conditionMap[data.condition] || 'GT'
    if (data.threshold !== undefined) updateData.threshold = data.threshold
    if (data.duration !== undefined) updateData.duration = data.duration
    if (data.severity !== undefined) updateData.severity = data.severity
    if (data.enabled !== undefined) updateData.enabled = data.enabled
    if (data.notificationChannels !== undefined) updateData.notificationChannels = data.notificationChannels

    const rule = await prisma.alertRule.update({
      where: { id },
      data: updateData
    })

    return rule
  }

  /**
   * 删除告警规则
   */
  static async deleteAlertRule(id: string) {
    // 检查规则是否存在
    const rule = await prisma.alertRule.findUnique({
      where: { id }
    })

    if (!rule) {
      throw ErrorFactory.notFound('告警规则')
    }

    // 删除规则（级联删除相关告警记录）
    await prisma.alertRule.delete({
      where: { id }
    })

    return true
  }

  /**
   * 获取告警历史
   */
  static async getAlerts(
    page: number = 1,
    limit: number = 20,
    severity?: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL',
    status?: 'PENDING' | 'ACKNOWLEDGED' | 'RESOLVED',
    dateFrom?: Date,
    dateTo?: Date
  ) {
    const skip = (page - 1) * limit
    
    const where: any = {}
    
    if (severity) {
      where.severity = severity
    }
    
    if (status) {
      where.status = status
    }
    
    if (dateFrom || dateTo) {
      where.triggeredAt = {}
      if (dateFrom) {
        where.triggeredAt.gte = dateFrom
      }
      if (dateTo) {
        where.triggeredAt.lte = dateTo
      }
    }

    const [alerts, total] = await Promise.all([
      prisma.alert.findMany({
        where,
        skip,
        take: limit,
        include: {
          rule: {
            select: {
              name: true
            }
          },
          acknowledgedByUser: {
            select: {
              username: true,
              fullName: true
            }
          },
          resolvedByUser: {
            select: {
              username: true,
              fullName: true
            }
          }
        },
        orderBy: { triggeredAt: 'desc' }
      }),
      prisma.alert.count({ where })
    ])

    return {
      alerts: alerts.map(alert => ({
        id: alert.id,
        ruleId: alert.ruleId,
        ruleName: alert.rule.name,
        severity: alert.severity,
        status: alert.status,
        message: alert.message,
        metricValue: alert.metricValue,
        triggeredAt: alert.triggeredAt,
        acknowledgedAt: alert.acknowledgedAt,
        acknowledgedBy: alert.acknowledgedByUser,
        resolvedAt: alert.resolvedAt,
        resolvedBy: alert.resolvedByUser
      })),
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    }
  }

  /**
   * 确认告警
   */
  static async acknowledgeAlert(id: string, userId: string) {
    const alert = await prisma.alert.findUnique({
      where: { id }
    })

    if (!alert) {
      throw ErrorFactory.notFound('告警记录')
    }

    if (alert.status !== 'PENDING') {
      throw ErrorFactory.business('只能确认未处理的告警')
    }

    const updatedAlert = await prisma.alert.update({
      where: { id },
      data: {
        status: 'ACKNOWLEDGED',
        acknowledgedBy: userId,
        acknowledgedAt: new Date()
      },
      include: {
        rule: {
          select: {
            name: true
          }
        }
      }
    })

    return updatedAlert
  }

  /**
   * 解决告警
   */
  static async resolveAlert(id: string, userId: string) {
    const alert = await prisma.alert.findUnique({
      where: { id }
    })

    if (!alert) {
      throw ErrorFactory.notFound('告警记录')
    }

    if (alert.status === 'RESOLVED') {
      throw ErrorFactory.business('告警已经被解决')
    }

    const updatedAlert = await prisma.alert.update({
      where: { id },
      data: {
        status: 'RESOLVED',
        resolvedBy: userId,
        resolvedAt: new Date()
      },
      include: {
        rule: {
          select: {
            name: true
          }
        }
      }
    })

    return updatedAlert
  }

  /**
   * 获取告警统计
   */
  static async getAlertStats() {
    const now = new Date()
    const last24Hours = new Date(now.getTime() - 24 * 60 * 60 * 1000)
    const last7Days = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)

    const [
      totalAlerts,
      pendingAlerts,
      acknowledgedAlerts,
      resolvedAlerts,
      criticalAlerts,
      highAlerts,
      mediumAlerts,
      lowAlerts,
      alertsLast24Hours,
      alertsLast7Days
    ] = await Promise.all([
      prisma.alert.count(),
      prisma.alert.count({ where: { status: 'PENDING' } }),
      prisma.alert.count({ where: { status: 'ACKNOWLEDGED' } }),
      prisma.alert.count({ where: { status: 'RESOLVED' } }),
      prisma.alert.count({ where: { severity: 'CRITICAL' } }),
      prisma.alert.count({ where: { severity: 'HIGH' } }),
      prisma.alert.count({ where: { severity: 'MEDIUM' } }),
      prisma.alert.count({ where: { severity: 'LOW' } }),
      prisma.alert.count({
        where: {
          triggeredAt: {
            gte: last24Hours
          }
        }
      }),
      prisma.alert.count({
        where: {
          triggeredAt: {
            gte: last7Days
          }
        }
      })
    ])

    return {
      totalAlerts,
      pendingAlerts,
      acknowledgedAlerts,
      resolvedAlerts,
      criticalAlerts,
      highAlerts,
      mediumAlerts,
      lowAlerts,
      last24Hours: alertsLast24Hours,
      last7Days: alertsLast7Days
    }
  }

  /**
   * ===== 系统配置管理 =====
   */

  /**
   * 获取系统配置列表
   */
  static async getSystemConfigs(filter?: SystemConfigFilter, includeEncrypted: boolean = false) {
    const cacheKey = `system:configs:${JSON.stringify(filter)}`
    
    // 尝试从缓存获取（不包含敏感配置）
    if (!includeEncrypted) {
      const cached = await CacheService.get(cacheKey)
      if (cached) {
        return JSON.parse(cached)
      }
    }

    const where: any = {}
    
    if (filter?.category) {
      where.category = filter.category
    }
    
    if (filter?.isPublic !== undefined) {
      where.isPublic = filter.isPublic
    }
    
    if (filter?.isSystem !== undefined) {
      where.isSystem = filter.isSystem
    }
    
    if (filter?.search) {
      where.OR = [
        { key: { contains: filter.search, mode: 'insensitive' } },
        { description: { contains: filter.search, mode: 'insensitive' } }
      ]
    }

    const configs = await prisma.systemConfig.findMany({
      where,
      include: {
        updatedByUser: {
          select: {
            username: true,
            fullName: true
          }
        }
      },
      orderBy: [
        { displayOrder: 'asc' },
        { key: 'asc' }
      ]
    })

    // 处理配置数据
    const processedConfigs = configs.map(config => {
      let value = config.value
      
      // 解密加密字段
      if (config.isEncrypted && includeEncrypted && typeof value === 'string') {
        try {
          value = decrypt(value)
        } catch (error) {
          console.error(`Failed to decrypt config ${config.key}:`, error)
        }
      }
      
      // 隐藏敏感信息（如果不包含加密内容）
      if (config.isEncrypted && !includeEncrypted) {
        value = '***'
      }
      
      return {
        ...config,
        value
      }
    })

    // 缓存非敏感配置
    if (!includeEncrypted) {
      await CacheService.setex(cacheKey, 300, JSON.stringify(processedConfigs))
    }
    
    return processedConfigs
  }

  /**
   * 获取单个系统配置
   */
  static async getSystemConfig(category: SystemConfigCategory, key: string, includeEncrypted: boolean = false) {
    const config = await prisma.systemConfig.findUnique({
      where: {
        category_key: {
          category,
          key
        }
      },
      include: {
        updatedByUser: {
          select: {
            username: true,
            fullName: true
          }
        }
      }
    })

    if (!config) {
      return null
    }

    let value = config.value
    
    // 解密加密字段
    if (config.isEncrypted && includeEncrypted && typeof value === 'string') {
      try {
        value = decrypt(value)
      } catch (error) {
        console.error(`Failed to decrypt config ${config.key}:`, error)
      }
    }
    
    // 隐藏敏感信息
    if (config.isEncrypted && !includeEncrypted) {
      value = '***'
    }
    
    return {
      ...config,
      value
    }
  }

  /**
   * 获取配置值（简化版本）
   */
  static async getConfigValue(category: SystemConfigCategory, key: string, defaultValue?: any) {
    const cacheKey = `system:config:${category}:${key}`
    
    // 尝试从缓存获取
    const cached = await CacheService.get(cacheKey)
    if (cached !== null) {
      return JSON.parse(cached)
    }

    const config = await this.getSystemConfig(category, key, true)
    
    if (!config) {
      return defaultValue
    }
    
    const value = config.value
    
    // 缓存配置值
    await CacheService.setex(cacheKey, 300, JSON.stringify(value))
    
    return value
  }

  /**
   * 创建系统配置
   */
  static async createSystemConfig(data: CreateSystemConfigInput, userId: string) {
    // 检查配置是否已存在
    const existingConfig = await prisma.systemConfig.findUnique({
      where: {
        category_key: {
          category: data.category,
          key: data.key
        }
      }
    })

    if (existingConfig) {
      throw ErrorFactory.conflict(`配置 ${data.category}.${data.key} 已存在`)
    }

    // 处理加密字段
    let value = data.value
    const isEncrypted = data.isEncrypted || data.dataType === SystemConfigDataType.PASSWORD
    
    if (isEncrypted && typeof value === 'string') {
      value = encrypt(value)
    }

    const config = await prisma.systemConfig.create({
      data: {
        category: data.category,
        key: data.key,
        value,
        description: data.description,
        dataType: data.dataType || SystemConfigDataType.STRING,
        isEncrypted,
        isSystem: data.isSystem || false,
        isPublic: data.isPublic || false,
        validationRule: data.validationRule,
        defaultValue: data.defaultValue,
        displayOrder: data.displayOrder,
        updatedBy: userId
      },
      include: {
        updatedByUser: {
          select: {
            username: true,
            fullName: true
          }
        }
      }
    })

    // 记录变更历史
    await prisma.systemConfigHistory.create({
      data: {
        configId: config.id,
        oldValue: null,
        newValue: data.value, // 保存原始值
        changeReason: '创建配置',
        changedBy: userId
      }
    })

    // 清除缓存
    await this.clearConfigCache()
    
    return config
  }

  /**
   * 更新系统配置
   */
  static async updateSystemConfig(
    category: SystemConfigCategory, 
    key: string, 
    data: UpdateSystemConfigInput, 
    userId: string
  ) {
    // 获取现有配置
    const existingConfig = await prisma.systemConfig.findUnique({
      where: {
        category_key: {
          category,
          key
        }
      }
    })

    if (!existingConfig) {
      throw ErrorFactory.notFound(`配置 ${category}.${key}`)
    }

    // 准备更新数据
    const updateData: any = {}
    
    if (data.value !== undefined) {
      let value = data.value
      
      // 处理加密字段
      if (existingConfig.isEncrypted && typeof value === 'string') {
        value = encrypt(value)
      }
      
      updateData.value = value
    }
    
    if (data.description !== undefined) updateData.description = data.description
    if (data.isPublic !== undefined) updateData.isPublic = data.isPublic
    if (data.validationRule !== undefined) updateData.validationRule = data.validationRule
    if (data.displayOrder !== undefined) updateData.displayOrder = data.displayOrder
    
    updateData.updatedBy = userId

    // 获取旧值用于历史记录
    let oldValue = existingConfig.value
    if (existingConfig.isEncrypted && typeof oldValue === 'string') {
      try {
        oldValue = decrypt(oldValue)
      } catch (error) {
        oldValue = '***'
      }
    }

    // 更新配置
    const updatedConfig = await prisma.systemConfig.update({
      where: {
        category_key: {
          category,
          key
        }
      },
      data: updateData,
      include: {
        updatedByUser: {
          select: {
            username: true,
            fullName: true
          }
        }
      }
    })

    // 记录变更历史
    if (data.value !== undefined) {
      try {
        console.log('Creating config history with userId:', userId)
        await prisma.systemConfigHistory.create({
          data: {
            configId: updatedConfig.id,
            oldValue,
            newValue: data.value, // 保存原始值
            changeReason: data.changeReason || '更新配置',
            changedBy: userId
          }
        })
      } catch (error) {
        console.error('Failed to create config history:', error)
        // 不让历史记录失败影响主功能
      }
    }

    // 清除缓存
    await this.clearConfigCache(category, key)
    
    return updatedConfig
  }

  /**
   * 批量更新系统配置
   */
  static async batchUpdateSystemConfigs(
    configs: Array<{ category: SystemConfigCategory; key: string; value: any; changeReason?: string }>,
    userId: string
  ) {
    const results = []
    
    for (const configData of configs) {
      try {
        const result = await this.updateSystemConfig(
          configData.category,
          configData.key,
          { value: configData.value, changeReason: configData.changeReason },
          userId
        )
        results.push({ success: true, config: result })
      } catch (error) {
        results.push({
          success: false,
          error: error instanceof Error ? error.message : '未知错误',
          category: configData.category,
          key: configData.key
        })
      }
    }
    
    return results
  }

  /**
   * 删除系统配置
   */
  static async deleteSystemConfig(category: SystemConfigCategory, key: string) {
    const config = await prisma.systemConfig.findUnique({
      where: {
        category_key: {
          category,
          key
        }
      }
    })

    if (!config) {
      throw ErrorFactory.notFound(`配置 ${category}.${key}`)
    }

    if (config.isSystem) {
      throw ErrorFactory.business('不能删除系统配置')
    }

    await prisma.systemConfig.delete({
      where: {
        category_key: {
          category,
          key
        }
      }
    })

    // 清除缓存
    await this.clearConfigCache(category, key)
    
    return true
  }

  /**
   * 重置配置为默认值
   */
  static async resetConfigToDefault(category: SystemConfigCategory, key: string, userId: string) {
    const config = await prisma.systemConfig.findUnique({
      where: {
        category_key: {
          category,
          key
        }
      }
    })

    if (!config) {
      throw ErrorFactory.notFound(`配置 ${category}.${key}`)
    }

    if (!config.defaultValue) {
      throw ErrorFactory.business('该配置没有设置默认值')
    }

    return this.updateSystemConfig(category, key, {
      value: config.defaultValue,
      changeReason: '重置为默认值'
    }, userId)
  }

  /**
   * 获取配置变更历史
   */
  static async getConfigHistory(
    category?: SystemConfigCategory,
    key?: string,
    page: number = 1,
    limit: number = 20
  ) {
    const skip = (page - 1) * limit
    
    const where: any = {}
    
    if (category && key) {
      const config = await prisma.systemConfig.findUnique({
        where: {
          category_key: {
            category,
            key
          }
        },
        select: { id: true }
      })
      
      if (config) {
        where.configId = config.id
      }
    }

    const [history, total] = await Promise.all([
      prisma.systemConfigHistory.findMany({
        where,
        skip,
        take: limit,
        include: {
          config: {
            select: {
              category: true,
              key: true,
              description: true
            }
          },
          changedByUser: {
            select: {
              username: true,
              fullName: true
            }
          }
        },
        orderBy: { createdAt: 'desc' }
      }),
      prisma.systemConfigHistory.count({ where })
    ])

    return {
      history,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    }
  }

  /**
   * 测试邮件配置
   */
  static async testEmailConfig(config: EmailTestConfig): Promise<ConfigTestResult> {
    try {
      // 获取邮件配置
      const emailConfigs = await this.getSystemConfigs({ category: SystemConfigCategory.EMAIL }, true)
      const emailConfigMap = emailConfigs.reduce((map: any, item) => {
        map[item.key] = item.value
        return map
      }, {})

      // 发送测试邮件
      const result = await NotificationUtil.sendEmail({
        to: config.recipient,
        subject: config.subject || '系统配置测试邮件',
        content: config.content || '这是一封系统配置测试邮件，如果您收到此邮件，说明邮件配置正常。',
        isHtml: false
      }, emailConfigMap)

      if (result.success) {
        return {
          success: true,
          message: '邮件发送成功',
          timestamp: new Date().toISOString()
        }
      } else {
        return {
          success: false,
          message: result.message || '邮件发送失败',
          timestamp: new Date().toISOString()
        }
      }
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : '邮件配置测试失败',
        timestamp: new Date().toISOString()
      }
    }
  }

  /**
   * 测试短信配置
   */
  static async testSmsConfig(config: SmsTestConfig): Promise<ConfigTestResult> {
    try {
      // 获取短信配置
      const smsConfigs = await this.getSystemConfigs({ category: SystemConfigCategory.SMS }, true)
      const smsConfigMap = smsConfigs.reduce((map: any, item) => {
        map[item.key] = item.value
        return map
      }, {})

      // 发送测试短信
      const result = await NotificationUtil.sendSms({
        phone: config.phone,
        message: config.message || '这是一条系统配置测试短信，如果您收到此短信，说明短信配置正常。'
      }, smsConfigMap)

      if (result.success) {
        return {
          success: true,
          message: '短信发送成功',
          timestamp: new Date().toISOString()
        }
      } else {
        return {
          success: false,
          message: result.message || '短信发送失败',
          timestamp: new Date().toISOString()
        }
      }
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : '短信配置测试失败',
        timestamp: new Date().toISOString()
      }
    }
  }

  /**
   * 导出系统配置
   */
  static async exportConfigs(categories?: SystemConfigCategory[]): Promise<ConfigExport> {
    const filter: SystemConfigFilter = {}
    if (categories && categories.length > 0) {
      // 需要分别获取每个分类的配置
      const allConfigs = []
      for (const category of categories) {
        const configs = await this.getSystemConfigs({ category }, true)
        allConfigs.push(...configs)
      }
      
      return {
        version: '1.0',
        timestamp: new Date().toISOString(),
        configs: allConfigs.map(config => ({
          category: config.category,
          key: config.key,
          value: config.value,
          description: config.description,
          dataType: config.dataType,
          isEncrypted: config.isEncrypted,
          isSystem: config.isSystem,
          isPublic: config.isPublic,
          validationRule: config.validationRule,
          defaultValue: config.defaultValue,
          displayOrder: config.displayOrder
        }))
      }
    }
    
    const configs = await this.getSystemConfigs(filter, true)
    
    return {
      version: '1.0',
      timestamp: new Date().toISOString(),
      configs: configs.map(config => ({
        category: config.category,
        key: config.key,
        value: config.value,
        description: config.description,
        dataType: config.dataType,
        isEncrypted: config.isEncrypted,
        isSystem: config.isSystem,
        isPublic: config.isPublic,
        validationRule: config.validationRule,
        defaultValue: config.defaultValue,
        displayOrder: config.displayOrder
      }))
    }
  }

  /**
   * 导入系统配置
   */
  static async importConfigs(
    exportData: ConfigExport,
    options: ConfigImportOptions,
    userId: string
  ): Promise<ConfigImportResult> {
    let imported = 0
    let skipped = 0
    const errors: string[] = []

    for (const configData of exportData.configs) {
      try {
        // 检查分类过滤
        if (options.categories && !options.categories.includes(configData.category)) {
          skipped++
          continue
        }
        
        // 检查是否跳过加密配置
        if (options.skipEncrypted && configData.isEncrypted) {
          skipped++
          continue
        }

        // 检查配置是否已存在
        const existingConfig = await prisma.systemConfig.findUnique({
          where: {
            category_key: {
              category: configData.category,
              key: configData.key
            }
          }
        })

        if (existingConfig) {
          if (options.overwriteExisting) {
            // 更新现有配置
            await this.updateSystemConfig(
              configData.category,
              configData.key,
              { 
                value: configData.value,
                changeReason: '配置导入'
              },
              userId
            )
            imported++
          } else {
            skipped++
          }
        } else {
          // 创建新配置
          await this.createSystemConfig({
            category: configData.category,
            key: configData.key,
            value: configData.value,
            description: configData.description,
            dataType: configData.dataType,
            isEncrypted: configData.isEncrypted,
            isSystem: configData.isSystem,
            isPublic: configData.isPublic,
            validationRule: configData.validationRule,
            defaultValue: configData.defaultValue,
            displayOrder: configData.displayOrder
          }, userId)
          imported++
        }
      } catch (error) {
        errors.push(`${configData.category}.${configData.key}: ${error instanceof Error ? error.message : '未知错误'}`)
      }
    }

    return {
      success: errors.length === 0,
      message: errors.length === 0 ? '配置导入成功' : `导入完成，但有 ${errors.length} 个错误`,
      imported,
      skipped,
      errors
    }
  }

  /**
   * 初始化默认系统配置
   */
  static async initializeDefaultConfigs(userId: string) {
    const defaultConfigs = this.getDefaultConfigs()
    const results = []
    
    for (const [category, configs] of Object.entries(defaultConfigs)) {
      for (const [key, value] of Object.entries(configs)) {
        try {
          // 检查配置是否已存在
          const existing = await prisma.systemConfig.findUnique({
            where: {
              category_key: {
                category: category as SystemConfigCategory,
                key
              }
            }
          })
          
          if (!existing) {
            const configMeta = this.getConfigMeta(category as SystemConfigCategory, key)
            await this.createSystemConfig({
              category: category as SystemConfigCategory,
              key,
              value,
              description: configMeta?.description,
              dataType: this.inferDataType(value),
              isSystem: true,
              isPublic: this.isPublicConfig(category as SystemConfigCategory, key),
              defaultValue: value
            }, userId)
            results.push({ success: true, category, key })
          }
        } catch (error) {
          results.push({
            success: false,
            category,
            key,
            error: error instanceof Error ? error.message : '未知错误'
          })
        }
      }
    }
    
    return results
  }

  /**
   * 清除配置缓存
   */
  private static async clearConfigCache(category?: SystemConfigCategory, key?: string) {
    if (category && key) {
      // 清除特定配置的缓存
      const cacheKey = `system:config:${category}:${key}`
      await CacheService.del(cacheKey)
    }
    
    // 清除配置列表缓存（使用通配符删除）
    const keys = await CacheService.keys('system:configs:*')
    if (keys.length > 0) {
      await CacheService.del(...keys)
    }
  }

  /**
   * 获取默认系统配置
   */
  private static getDefaultConfigs(): SystemConfigDefaults {
    return {
      [SystemConfigCategory.GENERAL]: {
        SITE_NAME: '运维服务管理系统',
        SITE_DESCRIPTION: '专业的运维服务管理平台',
        SITE_LOGO: '/images/logo.png',
        SITE_FAVICON: '/images/favicon.ico',
        DEFAULT_TIMEZONE: 'Asia/Shanghai',
        DEFAULT_LANGUAGE: 'zh-CN',
        COMPANY_NAME: '多协云科技有限公司',
        COMPANY_ADDRESS: '深圳市南山区科技园',
        COMPANY_PHONE: '************',
        COMPANY_EMAIL: '<EMAIL>'
      },
      
      [SystemConfigCategory.SECURITY]: {
        PASSWORD_MIN_LENGTH: 8,
        PASSWORD_REQUIRE_UPPERCASE: true,
        PASSWORD_REQUIRE_LOWERCASE: true,
        PASSWORD_REQUIRE_NUMBER: true,
        PASSWORD_REQUIRE_SPECIAL: false,
        PASSWORD_EXPIRY_DAYS: 90,
        MAX_LOGIN_ATTEMPTS: 5,
        LOCKOUT_DURATION: 1800, // 30 minutes in seconds
        SESSION_TIMEOUT: 7200, // 2 hours in seconds
        ENABLE_2FA: false,
        JWT_EXPIRY: 3600, // 1 hour in seconds
        REFRESH_TOKEN_EXPIRY: 604800 // 7 days in seconds
      },
      
      [SystemConfigCategory.EMAIL]: {
        SMTP_HOST: '',
        SMTP_PORT: 587,
        SMTP_SECURE: false,
        SMTP_USER: '',
        SMTP_PASS: '',
        SENDER_NAME: '运维服务管理系统',
        SENDER_EMAIL: '',
        EMAIL_ENABLED: false
      },
      
      [SystemConfigCategory.SMS]: {
        ALI_SMS_ACCESS_KEY_ID: '',
        ALI_SMS_ACCESS_KEY_SECRET: '',
        ALI_SMS_REGION: 'cn-hangzhou',
        ALI_SMS_SIGN_NAME: '',
        SMS_ENABLED: false
      },
      
      [SystemConfigCategory.NOTIFICATION]: {
        DEFAULT_EMAIL_TEMPLATE: 'default',
        DEFAULT_SMS_TEMPLATE: 'default',
        NOTIFICATION_RETRY_TIMES: 3,
        NOTIFICATION_RETRY_DELAY: 5000, // 5 seconds
        AUTO_NOTIFICATION_ENABLED: true,
        NOTIFICATION_QUEUE_SIZE: 1000
      },
      
      [SystemConfigCategory.STORAGE]: {
        UPLOAD_MAX_SIZE: 10 * 1024 * 1024, // 10MB
        UPLOAD_ALLOWED_TYPES: ['image/*', 'application/pdf', 'text/*', 'application/msword', 'application/vnd.openxmlformats-officedocument.*'],
        STORAGE_PATH: './uploads',
        ENABLE_CDN: false,
        CDN_DOMAIN: '',
        FILE_RETENTION_DAYS: 365
      },
      
      [SystemConfigCategory.BACKUP]: {
        AUTO_BACKUP_ENABLED: false,
        BACKUP_SCHEDULE: '0 2 * * *', // 每天凌晨2点
        BACKUP_RETENTION_DAYS: 30,
        BACKUP_STORAGE_PATH: './backups',
        BACKUP_COMPRESSION: true,
        BACKUP_ENCRYPTION: false
      },
      
      [SystemConfigCategory.SYSTEM]: {
        SYSTEM_MAINTENANCE_MODE: false,
        MAINTENANCE_MESSAGE: '系统正在维护中，请稍后再试',
        DEBUG_MODE: false,
        LOG_LEVEL: 'info',
        LOG_RETENTION_DAYS: 30,
        CACHE_TTL: 300, // 5 minutes
        API_RATE_LIMIT: 1000, // requests per hour
        HEALTH_CHECK_INTERVAL: 60 // seconds
      }
    }
  }

  /**
   * 推断数据类型
   */
  private static inferDataType(value: any): SystemConfigDataType {
    if (typeof value === 'boolean') {
      return SystemConfigDataType.BOOLEAN
    }
    if (typeof value === 'number') {
      return SystemConfigDataType.NUMBER
    }
    if (typeof value === 'object') {
      return SystemConfigDataType.JSON
    }
    if (typeof value === 'string') {
      if (value.includes('@')) {
        return SystemConfigDataType.EMAIL
      }
      if (value.startsWith('http')) {
        return SystemConfigDataType.URL
      }
      if (value.length > 100) {
        return SystemConfigDataType.TEXTAREA
      }
    }
    return SystemConfigDataType.STRING
  }

  /**
   * 判断是否为公开配置
   */
  private static isPublicConfig(category: SystemConfigCategory, key: string): boolean {
    const publicConfigs = {
      [SystemConfigCategory.GENERAL]: ['SITE_NAME', 'SITE_DESCRIPTION', 'SITE_LOGO', 'SITE_FAVICON', 'DEFAULT_TIMEZONE', 'DEFAULT_LANGUAGE'],
      [SystemConfigCategory.STORAGE]: ['UPLOAD_MAX_SIZE', 'UPLOAD_ALLOWED_TYPES'],
      [SystemConfigCategory.SYSTEM]: ['SYSTEM_MAINTENANCE_MODE', 'MAINTENANCE_MESSAGE']
    }
    
    return publicConfigs[category]?.includes(key) || false
  }

  /**
   * 获取配置元数据
   */
  private static getConfigMeta(category: SystemConfigCategory, key: string) {
    // 这里可以返回配置的描述信息，用于前端显示
    const metaData: Record<string, Record<string, any>> = {
      [SystemConfigCategory.GENERAL]: {
        SITE_NAME: { description: '网站名称' },
        SITE_DESCRIPTION: { description: '网站描述' },
        COMPANY_NAME: { description: '公司名称' }
      },
      [SystemConfigCategory.SECURITY]: {
        PASSWORD_MIN_LENGTH: { description: '密码最小长度' },
        MAX_LOGIN_ATTEMPTS: { description: '最大登录尝试次数' }
      }
    }
    
    return metaData[category]?.[key]
  }

  // ===== 私有辅助方法 =====

  /**
   * 检查数据库健康状态
   */
  private static async checkDatabaseHealth(): Promise<boolean> {
    try {
      await prisma.$queryRaw`SELECT 1`
      return true
    } catch {
      return false
    }
  }

  /**
   * 检查Redis健康状态
   */
  private static async checkRedisHealth(): Promise<boolean> {
    try {
      await CacheService.ping()
      return true
    } catch {
      return false
    }
  }

  /**
   * 获取CPU使用率
   */
  private static async getCpuUsage(): Promise<number> {
    const cpus = os.cpus()
    let totalIdle = 0
    let totalTick = 0

    cpus.forEach(cpu => {
      for (const type in cpu.times) {
        totalTick += cpu.times[type as keyof typeof cpu.times]
      }
      totalIdle += cpu.times.idle
    })

    return Math.round(100 - (100 * totalIdle / totalTick))
  }

  /**
   * 获取内存使用情况
   */
  private static getMemoryUsage() {
    const total = os.totalmem()
    const free = os.freemem()
    const used = total - free

    return {
      total: Math.round(total / 1024 / 1024 / 1024 * 100) / 100, // GB
      used: Math.round(used / 1024 / 1024 / 1024 * 100) / 100, // GB
      free: Math.round(free / 1024 / 1024 / 1024 * 100) / 100, // GB
      usage: Math.round((used / total) * 100)
    }
  }

  /**
   * 获取磁盘使用情况
   */
  private static async getDiskUsage() {
    try {
      // 在Linux/Mac系统上使用df命令
      if (process.platform !== 'win32') {
        const output = execSync('df -h /', { encoding: 'utf8' })
        const lines = output.split('\n')
        const diskInfo = lines[1].split(/\s+/)
        
        return {
          total: this.parseSize(diskInfo[1]),
          used: this.parseSize(diskInfo[2]),
          free: this.parseSize(diskInfo[3]),
          usage: parseInt(diskInfo[4].replace('%', ''))
        }
      }
    } catch (error) {
      console.error('Error getting disk usage:', error)
    }

    // 默认返回值
    return {
      total: 100,
      used: 50,
      free: 50,
      usage: 50
    }
  }

  /**
   * 获取网络使用情况
   */
  private static async getNetworkUsage() {
    const interfaces = os.networkInterfaces()
    const result = []

    for (const [name, addresses] of Object.entries(interfaces)) {
      if (addresses && addresses.length > 0) {
        result.push({
          name,
          rx: Math.random() * 1000000, // 模拟数据
          tx: Math.random() * 1000000  // 模拟数据
        })
      }
    }

    return { interfaces: result }
  }

  /**
   * 检查磁盘空间
   */
  private static async checkDiskSpace() {
    const diskUsage = await this.getDiskUsage()
    return {
      status: diskUsage.usage < 90 ? 'healthy' : 'warning',
      usage: diskUsage.usage,
      message: diskUsage.usage >= 90 ? '磁盘空间不足' : '磁盘空间正常'
    }
  }

  /**
   * 检查内存使用率
   */
  private static checkMemoryUsage() {
    const memory = this.getMemoryUsage()
    return {
      status: memory.usage < 90 ? 'healthy' : 'warning',
      usage: memory.usage,
      message: memory.usage >= 90 ? '内存使用率过高' : '内存使用率正常'
    }
  }

  /**
   * 检查关键服务
   */
  private static async checkCriticalServices() {
    return {
      status: 'healthy',
      services: {
        api: true,
        database: await this.checkDatabaseHealth(),
        redis: await this.checkRedisHealth()
      }
    }
  }

  /**
   * 解析磁盘大小
   */
  private static parseSize(sizeStr: string): number {
    const size = parseFloat(sizeStr)
    const unit = sizeStr.slice(-1).toUpperCase()
    
    switch (unit) {
      case 'K': return size / 1024 / 1024
      case 'M': return size / 1024
      case 'G': return size
      case 'T': return size * 1024
      default: return size / 1024 / 1024 / 1024
    }
  }
}