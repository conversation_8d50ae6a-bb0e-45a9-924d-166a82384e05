/**
 * 缓存服务
 * 提供内存缓存功能，支持TTL和模式匹配删除
 */

interface CacheItem {
  value: string
  expireAt: number
}

export class CacheService {
  private static cache = new Map<string, CacheItem>()
  private static cleanupInterval: NodeJS.Timeout | null = null

  /**
   * 初始化缓存服务
   */
  static init() {
    // 每5分钟清理一次过期缓存
    if (!this.cleanupInterval) {
      this.cleanupInterval = setInterval(() => {
        this.cleanup()
      }, 5 * 60 * 1000)
    }
  }

  /**
   * 设置缓存
   */
  static async set(key: string, value: string, ttlSeconds: number = 300): Promise<void> {
    const expireAt = Date.now() + (ttlSeconds * 1000)
    this.cache.set(key, { value, expireAt })
  }

  /**
   * 获取缓存
   */
  static async get(key: string): Promise<string | null> {
    const item = this.cache.get(key)
    
    if (!item) {
      return null
    }

    // 检查是否过期
    if (Date.now() > item.expireAt) {
      this.cache.delete(key)
      return null
    }

    return item.value
  }

  /**
   * 删除缓存
   */
  static async delete(key: string): Promise<void> {
    this.cache.delete(key)
  }

  /**
   * 根据模式删除缓存
   */
  static async deletePattern(pattern: string): Promise<void> {
    // 简单的通配符匹配，支持 * 结尾
    const regex = new RegExp('^' + pattern.replace(/\*/g, '.*') + '$')
    
    const keysToDelete: string[] = []
    for (const key of this.cache.keys()) {
      if (regex.test(key)) {
        keysToDelete.push(key)
      }
    }

    keysToDelete.forEach(key => this.cache.delete(key))
  }

  /**
   * 检查缓存是否存在
   */
  static async exists(key: string): Promise<boolean> {
    const item = this.cache.get(key)
    
    if (!item) {
      return false
    }

    // 检查是否过期
    if (Date.now() > item.expireAt) {
      this.cache.delete(key)
      return false
    }

    return true
  }

  /**
   * 获取缓存统计信息
   */
  static getStats(): {
    totalKeys: number
    expiredKeys: number
    memoryUsage: number
  } {
    let expiredKeys = 0
    const now = Date.now()

    for (const [key, item] of this.cache.entries()) {
      if (now > item.expireAt) {
        expiredKeys++
      }
    }

    return {
      totalKeys: this.cache.size,
      expiredKeys,
      memoryUsage: process.memoryUsage().heapUsed
    }
  }

  /**
   * 清理过期缓存
   */
  private static cleanup(): void {
    const now = Date.now()
    const keysToDelete: string[] = []

    for (const [key, item] of this.cache.entries()) {
      if (now > item.expireAt) {
        keysToDelete.push(key)
      }
    }

    keysToDelete.forEach(key => this.cache.delete(key))
    
    if (keysToDelete.length > 0) {
      console.log(`缓存清理完成，删除了 ${keysToDelete.length} 个过期项`)
    }
  }

  /**
   * 清空所有缓存
   */
  static async clear(): Promise<void> {
    this.cache.clear()
  }

  /**
   * 关闭缓存服务
   */
  static shutdown(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval)
      this.cleanupInterval = null
    }
    this.cache.clear()
  }
}

// 初始化缓存服务
CacheService.init()
