/**
 * 步骤执行引擎
 * 负责协调和控制工作流步骤的执行流程
 */

import { EventEmitter } from 'events';
import type {
  WorkflowStep,
  ExecutionContext,
  ActionExecutionResult,
  ExecutionError,
  ExecutionLog,
  ErrorType,
  ErrorSeverity,
  ExecutionStatus,
  ErrorHandlingStrategy,
  ErrorHandlingType,
  ValidationResult
} from '@/types/action-executor.types';
import { actionExecutorManager } from './action-executor-manager.service';
import { executionContextManager } from './execution-context-manager.service';
import { executionStateTracker } from './execution-state-tracker.service';

/**
 * 步骤执行引擎配置
 */
interface StepExecutionConfig {
  maxConcurrentSteps: number;
  defaultTimeout: number;
  enableResourceMonitoring: boolean;
  enablePerformanceTracking: boolean;
  retryBackoffMultiplier: number;
  maxRetryDelay: number;
}

/**
 * 执行队列项
 */
interface ExecutionQueueItem {
  executionId: string;
  workflowId: string;
  step: WorkflowStep;
  context: ExecutionContext;
  priority: number;
  createdAt: Date;
}

/**
 * 步骤执行引擎实现
 */
export class StepExecutionEngine extends EventEmitter {
  private static instance: StepExecutionEngine;
  private config: StepExecutionConfig;
  private executionQueue: ExecutionQueueItem[] = [];
  private runningExecutions = new Map<string, Promise<ActionExecutionResult>>();
  private isProcessing = false;

  constructor() {
    super();
    this.config = {
      maxConcurrentSteps: 10,
      defaultTimeout: 300, // 5分钟
      enableResourceMonitoring: true,
      enablePerformanceTracking: true,
      retryBackoffMultiplier: 2,
      maxRetryDelay: 60000 // 1分钟
    };
    this.setupEventListeners();
  }

  /**
   * 获取单例实例
   */
  static getInstance(): StepExecutionEngine {
    if (!StepExecutionEngine.instance) {
      StepExecutionEngine.instance = new StepExecutionEngine();
    }
    return StepExecutionEngine.instance;
  }

  /**
   * 初始化执行引擎
   */
  async initialize(): Promise<void> {
    try {
      console.log('🚀 初始化 StepExecutionEngine...');

      // 确保依赖组件已初始化
      await actionExecutorManager.initialize();

      // 启动执行队列处理
      this.startQueueProcessor();

      console.log('✅ StepExecutionEngine 初始化完成');
      this.emit('initialized');

    } catch (error) {
      console.error('❌ StepExecutionEngine 初始化失败:', error);
      this.emit('initialization:failed', { error });
      throw error;
    }
  }

  /**
   * 执行步骤 - 主要入口点
   */
  async executeStep(
    executionId: string,
    workflowId: string,
    step: WorkflowStep,
    variables: Record<string, any> = {},
    priority: number = 5
  ): Promise<ActionExecutionResult> {
    try {
      console.log(`🔄 开始执行步骤: ${step.name} (${step.type})`);

      // 创建执行上下文
      const context = await executionContextManager.createContext(
        executionId,
        workflowId,
        step.index,
        variables
      );

      // 开始状态跟踪
      await executionStateTracker.startExecution(executionId, workflowId, step.index);
      await executionStateTracker.updateStatus(executionId, step.index, ExecutionStatus.RUNNING);

      // 发出开始事件
      this.emit('step:execution:started', {
        executionId,
        workflowId,
        stepIndex: step.index,
        stepName: step.name,
        stepType: step.type
      });

      // 如果需要排队执行
      if (this.runningExecutions.size >= this.config.maxConcurrentSteps) {
        return await this.queueExecution(executionId, workflowId, step, context, priority);
      }

      // 直接执行
      return await this.doExecuteStep(step, context);

    } catch (error: any) {
      console.error(`❌ 步骤执行异常: ${step.name}`, error);
      
      const executionError: ExecutionError = {
        type: ErrorType.EXECUTION_ERROR,
        code: 'STEP_EXECUTION_FAILED',
        message: error.message || '步骤执行失败',
        recoverable: true,
        severity: ErrorSeverity.HIGH,
        timestamp: new Date()
      };

      await executionStateTracker.failExecution(executionId, step.index, executionError);

      return {
        success: false,
        error: executionError,
        logs: [{
          level: 'ERROR',
          message: `步骤执行异常: ${error.message}`,
          timestamp: new Date(),
          source: 'StepExecutionEngine'
        }]
      };
    }
  }

  /**
   * 批量执行步骤
   */
  async executeBatch(
    executionId: string,
    workflowId: string,
    steps: WorkflowStep[],
    variables: Record<string, any> = {}
  ): Promise<ActionExecutionResult[]> {
    try {
      console.log(`📦 开始批量执行 ${steps.length} 个步骤`);

      const results: ActionExecutionResult[] = [];
      
      // 按优先级和依赖关系排序
      const sortedSteps = this.sortStepsByDependency(steps);

      for (const step of sortedSteps) {
        const result = await this.executeStep(executionId, workflowId, step, variables);
        results.push(result);

        // 如果步骤失败且配置为停止执行
        if (!result.success && step.errorHandling?.type === ErrorHandlingType.FAIL) {
          console.log(`⛔ 步骤 ${step.name} 失败，停止批量执行`);
          break;
        }

        // 更新变量
        if (result.success && result.data) {
          variables = { ...variables, ...result.data };
        }
      }

      this.emit('batch:execution:completed', {
        executionId,
        workflowId,
        totalSteps: steps.length,
        completedSteps: results.length,
        successfulSteps: results.filter(r => r.success).length
      });

      return results;

    } catch (error: any) {
      console.error('❌ 批量执行失败:', error);
      throw error;
    }
  }

  /**
   * 暂停步骤执行
   */
  async pauseExecution(executionId: string, stepIndex: number): Promise<boolean> {
    try {
      const context = executionContextManager.getContext(executionId, stepIndex);
      if (context) {
        // 发送中止信号但不立即中断
        context.abortController.abort();
        await executionStateTracker.updateStatus(executionId, stepIndex, ExecutionStatus.PENDING);

        this.emit('step:execution:paused', { executionId, stepIndex });
        return true;
      }
      return false;
    } catch (error) {
      console.error('暂停执行失败:', error);
      return false;
    }
  }

  /**
   * 恢复步骤执行
   */
  async resumeExecution(executionId: string, stepIndex: number): Promise<boolean> {
    try {
      // 从队列中查找对应的执行项
      const queueItem = this.executionQueue.find(
        item => item.executionId === executionId && item.step.index === stepIndex
      );

      if (queueItem) {
        // 重新创建上下文
        const context = await executionContextManager.createContext(
          executionId,
          queueItem.workflowId,
          stepIndex,
          executionContextManager.getContextVariables(executionId, stepIndex) || {}
        );

        // 更新状态并重新执行
        await executionStateTracker.updateStatus(executionId, stepIndex, ExecutionStatus.RUNNING);
        
        this.doExecuteStep(queueItem.step, context).catch(error => {
          console.error('恢复执行失败:', error);
        });

        this.emit('step:execution:resumed', { executionId, stepIndex });
        return true;
      }

      return false;
    } catch (error) {
      console.error('恢复执行失败:', error);
      return false;
    }
  }

  /**
   * 取消步骤执行
   */
  async cancelExecution(executionId: string, stepIndex: number): Promise<boolean> {
    try {
      const executionKey = `${executionId}:${stepIndex}`;
      
      // 取消正在运行的执行
      if (this.runningExecutions.has(executionKey)) {
        const context = executionContextManager.getContext(executionId, stepIndex);
        if (context) {
          context.abortController.abort();
        }
        this.runningExecutions.delete(executionKey);
      }

      // 从队列中移除
      this.executionQueue = this.executionQueue.filter(
        item => !(item.executionId === executionId && item.step.index === stepIndex)
      );

      // 更新状态
      await executionStateTracker.updateStatus(executionId, stepIndex, ExecutionStatus.SKIPPED);

      // 清理上下文
      await executionContextManager.cleanupContext(executionId, stepIndex);

      this.emit('step:execution:cancelled', { executionId, stepIndex });
      return true;

    } catch (error) {
      console.error('取消执行失败:', error);
      return false;
    }
  }

  /**
   * 获取执行统计
   */
  getExecutionStats(): any {
    const runningCount = this.runningExecutions.size;
    const queuedCount = this.executionQueue.length;
    
    const queueStats = this.executionQueue.reduce(
      (acc, item) => {
        acc.totalItems++;
        acc.priorities[item.priority] = (acc.priorities[item.priority] || 0) + 1;
        
        const waitTime = Date.now() - item.createdAt.getTime();
        acc.avgWaitTime = (acc.avgWaitTime * (acc.totalItems - 1) + waitTime) / acc.totalItems;
        
        return acc;
      },
      { totalItems: 0, priorities: {} as Record<number, number>, avgWaitTime: 0 }
    );

    return {
      running: runningCount,
      queued: queuedCount,
      maxConcurrent: this.config.maxConcurrentSteps,
      utilizationRate: (runningCount / this.config.maxConcurrentSteps) * 100,
      queueStats,
      config: this.config
    };
  }

  /**
   * 更新配置
   */
  updateConfig(config: Partial<StepExecutionConfig>): void {
    this.config = { ...this.config, ...config };
    this.emit('config:updated', this.config);
    console.log('📝 StepExecutionEngine 配置已更新');
  }

  // ========== 私有方法 ==========

  /**
   * 实际执行步骤
   */
  private async doExecuteStep(step: WorkflowStep, context: ExecutionContext): Promise<ActionExecutionResult> {
    const executionKey = `${context.executionId}:${step.index}`;
    
    try {
      // 预执行验证
      const validation = await this.preExecutionValidation(step, context);
      if (!validation.valid) {
        const error: ExecutionError = {
          type: ErrorType.VALIDATION_ERROR,
          code: 'PRE_EXECUTION_VALIDATION_FAILED',
          message: `预执行验证失败: ${validation.errors.join(', ')}`,
          recoverable: false,
          severity: ErrorSeverity.MEDIUM,
          timestamp: new Date()
        };
        
        return { success: false, error, logs: [] };
      }

      // 设置超时
      const timeout = step.timeout || this.config.defaultTimeout;
      const timeoutPromise = new Promise<ActionExecutionResult>((_, reject) => {
        setTimeout(() => {
          reject(new Error(`步骤执行超时 (${timeout}s)`));
        }, timeout * 1000);
      });

      // 执行步骤的Promise
      const executionPromise = this.executeWithRetry(step, context);
      
      // 记录正在运行的执行
      this.runningExecutions.set(executionKey, executionPromise);

      try {
        // 等待执行完成或超时
        const result = await Promise.race([executionPromise, timeoutPromise]);
        
        // 执行后处理
        return await this.postExecutionProcessing(step, context, result);

      } catch (error: any) {
        if (error.message.includes('超时')) {
          const timeoutError: ExecutionError = {
            type: ErrorType.TIMEOUT_ERROR,
            code: 'STEP_EXECUTION_TIMEOUT',
            message: `步骤执行超时: ${step.name}`,
            recoverable: true,
            severity: ErrorSeverity.HIGH,
            timestamp: new Date()
          };
          
          return { success: false, error: timeoutError, logs: [] };
        }
        throw error;
      }

    } finally {
      // 清理运行状态
      this.runningExecutions.delete(executionKey);
    }
  }

  /**
   * 带重试的执行
   */
  private async executeWithRetry(step: WorkflowStep, context: ExecutionContext): Promise<ActionExecutionResult> {
    let result = await actionExecutorManager.executeStep(step, context);
    let retryCount = 0;
    const maxRetries = step.retry?.maxAttempts || 0;

    while (!result.success && result.shouldRetry && retryCount < maxRetries) {
      retryCount++;
      
      // 增加重试计数
      await executionStateTracker.incrementRetryCount(context.executionId, step.index);
      executionContextManager.incrementAttempt(context.executionId, step.index);

      // 计算重试延迟
      const delay = this.calculateRetryDelay(step.retry, retryCount);
      
      context.logger.warn(`第 ${retryCount} 次重试，延迟 ${delay}ms`);
      
      await this.sleep(delay);

      // 重新执行
      result = await actionExecutorManager.executeStep(step, context);
    }

    return result;
  }

  /**
   * 预执行验证
   */
  private async preExecutionValidation(step: WorkflowStep, context: ExecutionContext): Promise<ValidationResult> {
    const errors: string[] = [];
    const warnings: string[] = [];

    try {
      // 检查执行器是否存在
      const executor = actionExecutorManager.getExecutor(step.type);
      if (!executor) {
        errors.push(`执行器不存在: ${step.type}`);
      }

      // 检查资源限制
      const resourcesOk = executionContextManager.checkResourceLimits(context.executionId);
      if (!resourcesOk) {
        warnings.push('资源使用接近限制');
      }

      // 检查步骤配置
      if (executor) {
        const stepValidation = await executor.validate(step);
        errors.push(...stepValidation.errors);
        warnings.push(...stepValidation.warnings);
      }

      // 检查中止信号
      if (context.abortController.signal.aborted) {
        errors.push('执行已被中止');
      }

    } catch (error: any) {
      errors.push(`验证过程异常: ${error.message}`);
    }

    return {
      valid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * 执行后处理
   */
  private async postExecutionProcessing(
    step: WorkflowStep,
    context: ExecutionContext,
    result: ActionExecutionResult
  ): Promise<ActionExecutionResult> {
    try {
      // 更新执行状态
      if (result.success) {
        await executionStateTracker.completeExecution(context.executionId, step.index, result);
      } else {
        if (result.error) {
          await executionStateTracker.failExecution(context.executionId, step.index, result.error);
        }
      }

      // 处理执行结果数据
      if (result.success && result.data) {
        executionContextManager.updateContextVariables(
          context.executionId,
          step.index,
          result.data
        );
      }

      // 记录执行日志
      for (const log of result.logs) {
        await executionStateTracker.addLog(context.executionId, step.index, log);
      }

      // 资源使用监控
      if (this.config.enableResourceMonitoring) {
        const resourceUsage = executionContextManager.getResourceUsage(context.executionId);
        if (resourceUsage) {
          result.resourceUsage = resourceUsage;
        }
      }

      return result;

    } catch (error: any) {
      console.error('执行后处理失败:', error);
      // 即使后处理失败，也要返回原始结果
      return result;
    }
  }

  /**
   * 排队执行
   */
  private async queueExecution(
    executionId: string,
    workflowId: string,
    step: WorkflowStep,
    context: ExecutionContext,
    priority: number
  ): Promise<ActionExecutionResult> {
    return new Promise((resolve, reject) => {
      const queueItem: ExecutionQueueItem = {
        executionId,
        workflowId,
        step,
        context,
        priority,
        createdAt: new Date()
      };

      // 插入到队列中（按优先级排序）
      const insertIndex = this.executionQueue.findIndex(item => item.priority > priority);
      if (insertIndex === -1) {
        this.executionQueue.push(queueItem);
      } else {
        this.executionQueue.splice(insertIndex, 0, queueItem);
      }

      context.logger.info(`步骤已加入执行队列，位置: ${this.executionQueue.length}`);

      this.emit('step:queued', {
        executionId,
        stepIndex: step.index,
        queuePosition: this.executionQueue.length,
        priority
      });

      // 设置执行Promise（将在队列处理器中解决）
      const originalExecute = this.doExecuteStep.bind(this);
      this.doExecuteStep = async (queueStep: WorkflowStep, queueContext: ExecutionContext) => {
        try {
          const result = await originalExecute(queueStep, queueContext);
          if (queueStep.index === step.index && queueContext.executionId === executionId) {
            resolve(result);
          }
          return result;
        } catch (error) {
          if (queueStep.index === step.index && queueContext.executionId === executionId) {
            reject(error);
          }
          throw error;
        }
      };
    });
  }

  /**
   * 启动队列处理器
   */
  private startQueueProcessor(): void {
    if (this.isProcessing) return;

    this.isProcessing = true;

    const processQueue = async () => {
      while (this.executionQueue.length > 0 && 
             this.runningExecutions.size < this.config.maxConcurrentSteps) {
        
        const item = this.executionQueue.shift();
        if (!item) continue;

        // 检查执行是否已被取消
        if (item.context.abortController.signal.aborted) {
          continue;
        }

        this.emit('step:dequeued', {
          executionId: item.executionId,
          stepIndex: item.step.index
        });

        // 异步执行（不等待完成）
        this.doExecuteStep(item.step, item.context).catch(error => {
          console.error(`队列执行失败:`, error);
        });
      }

      // 继续处理队列
      if (this.isProcessing) {
        setTimeout(processQueue, 100); // 100ms间隔
      }
    };

    processQueue();
  }

  /**
   * 按依赖关系排序步骤
   */
  private sortStepsByDependency(steps: WorkflowStep[]): WorkflowStep[] {
    // 简单实现：按index排序
    // 实际实现可以根据步骤之间的依赖关系进行拓扑排序
    return [...steps].sort((a, b) => a.index - b.index);
  }

  /**
   * 计算重试延迟
   */
  private calculateRetryDelay(retryConfig: any, attempt: number): number {
    if (!retryConfig) return 1000;

    const baseDelay = retryConfig.delay || 1000;
    const multiplier = retryConfig.backoffMultiplier || this.config.retryBackoffMultiplier;
    const maxDelay = retryConfig.maxDelay || this.config.maxRetryDelay;

    const delay = baseDelay * Math.pow(multiplier, attempt - 1);
    return Math.min(delay, maxDelay);
  }

  /**
   * 休眠
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    // 监听资源限制超出
    executionContextManager.on('context:resource:limit:exceeded', (event) => {
      console.warn(`⚠️ 资源限制超出: ${event.type} (${event.current} > ${event.limit})`);
      this.emit('resource:limit:exceeded', event);
    });

    // 监听执行器状态变化
    actionExecutorManager.on('step:failed', (event) => {
      this.emit('step:execution:failed', event);
    });

    actionExecutorManager.on('step:completed', (event) => {
      this.emit('step:execution:completed', event);
    });

    // 全局错误处理
    this.on('error', (error) => {
      console.error('StepExecutionEngine 错误:', error);
    });
  }

  /**
   * 全局清理
   */
  async cleanup(): Promise<void> {
    console.log('🧹 开始清理 StepExecutionEngine...');

    this.isProcessing = false;
    
    // 取消所有正在运行的执行
    for (const [key, promise] of this.runningExecutions) {
      try {
        const [executionId, stepIndex] = key.split(':');
        const context = executionContextManager.getContext(executionId, parseInt(stepIndex));
        if (context) {
          context.abortController.abort();
        }
      } catch (error) {
        console.error(`取消执行失败 ${key}:`, error);
      }
    }

    // 清空队列和运行状态
    this.executionQueue = [];
    this.runningExecutions.clear();

    console.log('✅ StepExecutionEngine 清理完成');
    this.emit('cleanup:completed');
  }
}

// 导出单例实例
export const stepExecutionEngine = StepExecutionEngine.getInstance();