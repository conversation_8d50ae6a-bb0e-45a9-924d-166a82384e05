import nodemailer from 'nodemailer'
import { z } from 'zod'
import { PrismaClient } from '@prisma/client'
import { decrypt } from '../utils/crypto.util'

const prisma = new PrismaClient()

// 邮件配置验证
const emailConfigSchema = z.object({
  host: z.string(),
  port: z.number(),
  user: z.string().email(),
  pass: z.string(),
  fromEmail: z.string().email(),
  fromName: z.string()
})

// 邮件发送参数验证
const sendEmailSchema = z.object({
  to: z.union([z.string().email(), z.array(z.string().email())]),
  subject: z.string().min(1),
  html: z.string().optional(),
  text: z.string().optional(),
  cc: z.union([z.string().email(), z.array(z.string().email())]).optional(),
  bcc: z.union([z.string().email(), z.array(z.string().email())]).optional(),
  attachments: z.array(z.object({
    filename: z.string(),
    path: z.string().optional(),
    content: z.any().optional(),
    contentType: z.string().optional()
  })).optional()
})

export interface EmailOptions {
  to: string | string[]
  subject: string
  html?: string
  text?: string
  cc?: string | string[]
  bcc?: string | string[]
  attachments?: Array<{
    filename: string
    path?: string
    content?: any
    contentType?: string
  }>
}

interface EmailConfig {
  host: string
  port: number
  secure: boolean
  user: string
  pass: string
  fromEmail: string
  fromName: string
  enabled: boolean
}

class EnhancedEmailService {
  private transporter: nodemailer.Transporter | null = null
  private config: EmailConfig | null = null
  private lastConfigCheck: Date | null = null
  private configCheckInterval = 60000 // 1分钟检查一次配置更新

  constructor() {
    this.initializeTransporter()
  }

  // 从数据库获取邮件配置
  private async getEmailConfigFromDB(): Promise<EmailConfig | null> {
    try {
      const emailConfigs = await prisma.systemConfig.findMany({
        where: {
          category: 'EMAIL',
          key: {
            in: ['SMTP_HOST', 'SMTP_PORT', 'SMTP_SECURE', 'SMTP_USER', 'SMTP_PASS', 'SENDER_NAME', 'SENDER_EMAIL', 'EMAIL_ENABLED']
          }
        }
      })

      if (emailConfigs.length === 0) {
        console.log('📧 No email configuration found in database, trying environment variables')
        return this.getEmailConfigFromEnv()
      }

      const configMap: Record<string, any> = {}
      for (const config of emailConfigs) {
        try {
          let value = config.value
          
          // 处理加密的配置值
          if (config.isEncrypted && typeof value === 'string') {
            try {
              value = await decrypt(value)
            } catch (decryptError) {
              console.warn(`Failed to decrypt config ${config.key}:`, decryptError)
              // 如果解密失败，使用原值
            }
          }
          
          // 处理 JSON 值
          if (typeof value === 'object') {
            configMap[config.key] = value
          } else if (typeof value === 'string') {
            try {
              configMap[config.key] = JSON.parse(value)
            } catch {
              configMap[config.key] = value
            }
          } else {
            configMap[config.key] = value
          }
        } catch (error) {
          console.warn(`Failed to parse config ${config.key}:`, error)
          configMap[config.key] = config.value
        }
      }

      // 检查邮件功能是否启用
      if (!configMap['EMAIL_ENABLED']) {
        console.log('📧 Email service is disabled in system configuration')
        return null
      }

      // 检查必需的配置
      const requiredFields = ['SMTP_HOST', 'SMTP_USER', 'SMTP_PASS', 'SENDER_EMAIL']
      for (const field of requiredFields) {
        if (!configMap[field]) {
          console.log(`⚠️  Email configuration incomplete: missing ${field}`)
          return null
        }
      }

      return {
        host: configMap['SMTP_HOST'],
        port: parseInt(configMap['SMTP_PORT']) || 587,
        secure: configMap['SMTP_SECURE'] === true || configMap['SMTP_SECURE'] === 'true',
        user: configMap['SMTP_USER'],
        pass: configMap['SMTP_PASS'],
        fromEmail: configMap['SENDER_EMAIL'],
        fromName: configMap['SENDER_NAME'] || '运维服务管理系统',
        enabled: configMap['EMAIL_ENABLED'] === true || configMap['EMAIL_ENABLED'] === 'true'
      }
    } catch (error) {
      console.error('Failed to get email config from database:', error)
      // 如果数据库配置获取失败，尝试使用环境变量作为备用
      return this.getEmailConfigFromEnv()
    }
  }

  // 从环境变量获取邮件配置（备用方案）
  private getEmailConfigFromEnv(): EmailConfig | null {
    const config = {
      host: process.env['SMTP_HOST'] || '',
      port: parseInt(process.env['SMTP_PORT'] || '587'),
      secure: process.env['SMTP_SECURE'] === 'true',
      user: process.env['SMTP_USER'] || '',
      pass: process.env['SMTP_PASS'] || '',
      fromEmail: process.env['FROM_EMAIL'] || '',
      fromName: process.env['FROM_NAME'] || '运维服务管理系统',
      enabled: true
    }

    // 检查是否所有必需的配置都已设置
    const hasValidConfig = config.host && 
                         config.user && 
                         config.pass && 
                         config.fromEmail && 
                         config.user !== '<EMAIL>' && 
                         config.fromEmail !== '<EMAIL>'

    if (!hasValidConfig) {
      console.log('⚠️  Email service not configured in environment variables')
      return null
    }

    return config
  }

  // 初始化或更新传输器
  private async initializeTransporter() {
    try {
      const dbConfig = await this.getEmailConfigFromDB()
      if (!dbConfig || !dbConfig.enabled) {
        console.log('⚠️  Email service not configured or disabled - Email functionality will be disabled.')
        this.transporter = null
        this.config = null
        return
      }

      // 验证配置
      const validatedConfig = emailConfigSchema.parse({
        host: dbConfig.host,
        port: dbConfig.port,
        user: dbConfig.user,
        pass: dbConfig.pass,
        fromEmail: dbConfig.fromEmail,
        fromName: dbConfig.fromName
      })

      this.config = dbConfig

      // 创建传输器
      this.transporter = nodemailer.createTransport({
        host: validatedConfig.host,
        port: validatedConfig.port,
        secure: dbConfig.secure || validatedConfig.port === 465,
        auth: {
          user: validatedConfig.user,
          pass: validatedConfig.pass
        },
        tls: {
          rejectUnauthorized: false
        }
      })

      this.lastConfigCheck = new Date()
      console.log('✅ Enhanced email service initialized successfully from database configuration')
    } catch (error) {
      console.error('❌ Enhanced email service initialization failed:', error)
      this.transporter = null
      this.config = null
    }
  }

  // 检查配置是否需要更新
  private async checkConfigUpdate() {
    const now = new Date()
    if (!this.lastConfigCheck || (now.getTime() - this.lastConfigCheck.getTime()) > this.configCheckInterval) {
      await this.initializeTransporter()
    }
  }

  // 强制重新加载配置
  async reloadConfig() {
    console.log('🔄 Manually reloading email configuration...')
    await this.initializeTransporter()
  }

  // 发送邮件
  async sendEmail(options: EmailOptions): Promise<boolean> {
    try {
      // 检查配置更新
      await this.checkConfigUpdate()

      if (!this.transporter || !this.config) {
        console.log('⚠️  Email service not configured - email not sent')
        return false
      }

      // 验证参数
      const validatedOptions = sendEmailSchema.parse(options)

      const mailOptions = {
        from: `"${this.config.fromName}" <${this.config.fromEmail}>`,
        to: Array.isArray(validatedOptions.to) ? validatedOptions.to.join(', ') : validatedOptions.to,
        subject: validatedOptions.subject,
        html: validatedOptions.html,
        text: validatedOptions.text || undefined,
        cc: validatedOptions.cc ? (Array.isArray(validatedOptions.cc) ? validatedOptions.cc.join(', ') : validatedOptions.cc) : undefined,
        bcc: validatedOptions.bcc ? (Array.isArray(validatedOptions.bcc) ? validatedOptions.bcc.join(', ') : validatedOptions.bcc) : undefined,
        attachments: validatedOptions.attachments
      }

      const result = await this.transporter.sendMail(mailOptions)
      console.log('📧 Email sent successfully:', result.messageId)
      return true
    } catch (error) {
      console.error('❌ Email send failed:', error)
      return false
    }
  }

  // 发送测试邮件
  async sendTestEmail(testEmail: string, message?: string): Promise<{ success: boolean; message: string; details?: any }> {
    try {
      // 检查配置更新
      await this.checkConfigUpdate()

      if (!this.transporter || !this.config) {
        return {
          success: false,
          message: '邮件服务未配置，请检查数据库中的邮件配置'
        }
      }

      const testSubject = `运维系统邮件测试 - ${new Date().toLocaleString()}`
      const testContent = message || `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #333;">邮件配置测试</h2>
          <div style="background: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0;">
            <p>这是一封测试邮件，用于验证您的邮件配置是否正常工作。</p>
            <p><strong>测试时间：</strong>${new Date().toLocaleString()}</p>
            <p><strong>SMTP服务器：</strong>${this.config.host}:${this.config.port}</p>
            <p><strong>发送账户：</strong>${this.config.user}</p>
            <p><strong>配置来源：</strong>数据库配置</p>
          </div>
          <p style="color: #28a745;">✅ 如果您收到这封邮件，说明邮件配置正常！</p>
          <p style="color: #666; font-size: 12px;">此邮件由运维服务管理系统自动发送</p>
        </div>
      `

      const result = await this.sendEmail({
        to: testEmail,
        subject: testSubject,
        html: testContent
      })

      if (result) {
        // 记录到邮件日志
        await prisma.emailLog.create({
          data: {
            recipient: testEmail,
            subject: testSubject,
            content: testContent,
            status: 'SENT',
            sentAt: new Date(),
            metadata: {
              isTest: true,
              smtpHost: this.config.host,
              smtpPort: this.config.port,
              configSource: 'database'
            }
          }
        })

        return {
          success: true,
          message: `测试邮件已发送到 ${testEmail}，请检查邮箱（包括垃圾邮件文件夹）`,
          details: {
            recipient: testEmail,
            smtpHost: this.config.host,
            smtpPort: this.config.port,
            configSource: 'database',
            sentTime: new Date().toISOString()
          }
        }
      } else {
        return {
          success: false,
          message: '邮件发送失败，请检查邮件配置或网络连接'
        }
      }
    } catch (error: any) {
      console.error('Email test failed:', error)
      
      // 记录失败日志
      try {
        await prisma.emailLog.create({
          data: {
            recipient: testEmail,
            subject: `测试邮件 - ${new Date().toLocaleString()}`,
            content: 'Email test failed',
            status: 'FAILED',
            errorMessage: error.message || 'Unknown error',
            metadata: {
              isTest: true,
              configSource: 'database',
              error: error.toString()
            }
          }
        })
      } catch (logError) {
        console.error('Failed to log email error:', logError)
      }

      return {
        success: false,
        message: `邮件发送失败: ${error.message || '未知错误'}`,
        details: {
          error: error.message,
          smtpHost: this.config?.host,
          smtpPort: this.config?.port,
          configSource: 'database'
        }
      }
    }
  }

  // 使用数据库模板发送邮件
  async sendEmailWithTemplate(
    templateId: string,
    recipient: string,
    variables: Record<string, any> = {},
    options?: Partial<EmailOptions>
  ): Promise<{ success: boolean; message: string; logId?: string }> {
    try {
      // 检查配置更新
      await this.checkConfigUpdate()

      if (!this.transporter || !this.config) {
        return {
          success: false,
          message: '邮件服务未配置'
        }
      }

      // 从数据库获取模板
      const template = await prisma.emailTemplate.findUnique({
        where: { id: templateId }
      })

      if (!template) {
        return {
          success: false,
          message: '邮件模板不存在'
        }
      }

      if (!template.enabled) {
        return {
          success: false,
          message: '邮件模板已禁用'
        }
      }

      // 渲染模板
      let renderedSubject = template.subject
      let renderedContent = template.content

      // 简单的变量替换
      Object.keys(variables).forEach(key => {
        const placeholder = new RegExp(`\\{\\{${key}\\}\\}`, 'g')
        const value = variables[key] || ''
        renderedSubject = renderedSubject.replace(placeholder, value)
        renderedContent = renderedContent.replace(placeholder, value)
      })

      // 创建邮件日志记录
      const emailLog = await prisma.emailLog.create({
        data: {
          templateId: template.id,
          recipient,
          subject: renderedSubject,
          content: renderedContent,
          status: 'PENDING',
          metadata: {
            variables,
            templateName: template.name,
            templateType: template.type,
            configSource: 'database'
          }
        }
      })

      // 发送邮件
      const emailOptions: EmailOptions = {
        to: recipient,
        subject: renderedSubject,
        html: renderedContent,
        ...options
      }

      const result = await this.sendEmail(emailOptions)

      // 更新邮件日志状态
      if (result) {
        await prisma.emailLog.update({
          where: { id: emailLog.id },
          data: {
            status: 'SENT',
            sentAt: new Date()
          }
        })

        return {
          success: true,
          message: '邮件发送成功',
          logId: emailLog.id
        }
      } else {
        await prisma.emailLog.update({
          where: { id: emailLog.id },
          data: {
            status: 'FAILED',
            errorMessage: '邮件发送失败'
          }
        })

        return {
          success: false,
          message: '邮件发送失败'
        }
      }
    } catch (error: any) {
      console.error('Template email send failed:', error)
      return {
        success: false,
        message: `邮件发送失败: ${error.message || '未知错误'}`
      }
    }
  }

  // 测试邮件连接
  async testConnection(): Promise<boolean> {
    try {
      // 检查配置更新
      await this.checkConfigUpdate()

      if (!this.transporter) {
        return false
      }

      await this.transporter.verify()
      console.log('✅ Enhanced email connection test successful')
      return true
    } catch (error) {
      console.error('❌ Enhanced email connection test failed:', error)
      return false
    }
  }

  // 获取服务状态
  isConfigured(): boolean {
    return this.transporter !== null && this.config !== null
  }

  // 获取配置信息（隐藏敏感数据）
  getConfigInfo() {
    if (!this.config) {
      return null
    }

    return {
      host: this.config.host,
      port: this.config.port,
      user: this.config.user,
      fromEmail: this.config.fromEmail,
      fromName: this.config.fromName,
      secure: this.config.secure,
      enabled: this.config.enabled,
      configured: true,
      configSource: 'database'
    }
  }

  // 获取邮件发送统计
  async getEmailStats(startDate?: Date, endDate?: Date) {
    const where: any = {}
    
    if (startDate || endDate) {
      where.createdAt = {}
      if (startDate) where.createdAt.gte = startDate
      if (endDate) where.createdAt.lte = endDate
    }

    const [total, sent, failed, pending] = await Promise.all([
      prisma.emailLog.count({ where }),
      prisma.emailLog.count({ where: { ...where, status: 'SENT' } }),
      prisma.emailLog.count({ where: { ...where, status: 'FAILED' } }),
      prisma.emailLog.count({ where: { ...where, status: 'PENDING' } })
    ])

    return {
      total,
      sent,
      failed,
      pending,
      successRate: total > 0 ? (sent / total * 100).toFixed(2) : '0.00'
    }
  }
}

// 导出增强版邮件服务实例
export const enhancedEmailService = new EnhancedEmailService()
export default enhancedEmailService
