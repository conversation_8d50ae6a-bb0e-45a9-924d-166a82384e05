/**
 * 工作流协调器服务
 * 协调触发器和执行器系统，管理完整的工作流生命周期
 */

import { EventEmitter } from 'events';
import type { WorkflowDefinition, WorkflowExecution, WorkflowStepType } from '@prisma/client';
import type {
  WorkflowStep,
  ExecutionContext,
  ActionExecutionResult,
  ExecutionError
} from '@/types/action-executor.types';

// 导入各个组件
import { workflowEngine } from './workflow-engine.service';
import { triggerManager } from './trigger-manager.service';
import { stepExecutionEngine } from './step-execution-engine.service';
import { errorRecoveryManager } from './error-recovery-manager.service';
import { executionStateTracker } from './execution-state-tracker.service';
import { executionContextManager } from './execution-context-manager.service';
import { metricsCollector } from './metrics-collector.service';
import { logAggregator } from './log-aggregator.service';

/**
 * 工作流协调状态
 */
export enum WorkflowCoordinationStatus {
  IDLE = 'IDLE',
  STARTING = 'STARTING',
  RUNNING = 'RUNNING',
  PAUSED = 'PAUSED',
  STOPPING = 'STOPPING',
  STOPPED = 'STOPPED'
}

/**
 * 工作流执行会话
 */
export interface WorkflowExecutionSession {
  sessionId: string;
  executionId: string;
  workflowId: string;
  workflowDefinition: WorkflowDefinition;
  currentStep: number;
  totalSteps: number;
  status: WorkflowCoordinationStatus;
  startTime: Date;
  endTime?: Date;
  context: ExecutionContext;
  executionResults: Map<number, ActionExecutionResult>;
  errors: ExecutionError[];
}

/**
 * 触发器执行器集成配置
 */
export interface TriggerExecutorIntegrationConfig {
  triggerTypes: string[];
  executorTypes: WorkflowStepType[];
  enableAutoRecovery: boolean;
  enableMetricsCollection: boolean;
  enableLogging: boolean;
  maxConcurrentSessions: number;
}

/**
 * 工作流协调器
 */
export class WorkflowCoordinator extends EventEmitter {
  private static instance: WorkflowCoordinator;
  private executionSessions = new Map<string, WorkflowExecutionSession>();
  private status: WorkflowCoordinationStatus = WorkflowCoordinationStatus.IDLE;
  private config: TriggerExecutorIntegrationConfig;
  private isInitialized = false;

  constructor() {
    super();
    this.config = {
      triggerTypes: ['event', 'cron', 'webhook', 'conditional'],
      executorTypes: ['HTTP_REQUEST', 'DATABASE_QUERY', 'NOTIFICATION', 'BUSINESS_LOGIC', 'CONDITIONAL'],
      enableAutoRecovery: true,
      enableMetricsCollection: true,
      enableLogging: true,
      maxConcurrentSessions: 10
    };
  }

  /**
   * 获取单例实例
   */
  static getInstance(): WorkflowCoordinator {
    if (!WorkflowCoordinator.instance) {
      WorkflowCoordinator.instance = new WorkflowCoordinator();
    }
    return WorkflowCoordinator.instance;
  }

  /**
   * 初始化工作流协调器
   */
  async initialize(config?: Partial<TriggerExecutorIntegrationConfig>): Promise<void> {
    if (this.isInitialized) return;

    try {
      console.log('🚀 初始化WorkflowCoordinator...');

      // 更新配置
      if (config) {
        this.config = { ...this.config, ...config };
      }

      // 初始化依赖组件
      await this.initializeDependencies();

      // 设置组件间的集成
      this.setupComponentIntegration();

      // 设置事件监听
      this.setupEventListeners();

      this.status = WorkflowCoordinationStatus.RUNNING;
      this.isInitialized = true;

      console.log('✅ WorkflowCoordinator初始化完成');
      this.emit('coordinator:initialized', {
        config: this.config,
        status: this.status
      });

    } catch (error) {
      console.error('❌ WorkflowCoordinator初始化失败:', error);
      this.status = WorkflowCoordinationStatus.STOPPED;
      throw error;
    }
  }

  /**
   * 启动工作流执行
   */
  async startWorkflowExecution(
    workflowId: string,
    triggerContext?: Record<string, any>
  ): Promise<string> {
    try {
      // 检查并发限制
      if (this.executionSessions.size >= this.config.maxConcurrentSessions) {
        throw new Error(`达到最大并发执行限制: ${this.config.maxConcurrentSessions}`);
      }

      console.log(`🚀 启动工作流执行: ${workflowId}`);

      // 创建工作流执行
      const execution = await workflowEngine.startExecution(
        workflowId,
        triggerContext || {},
        'system' // 默认触发用户
      );

      // 获取工作流定义
      const workflowDefinition = await workflowEngine.getWorkflowDefinition(workflowId);
      if (!workflowDefinition) {
        throw new Error(`工作流定义不存在: ${workflowId}`);
      }

      // 解析工作流步骤
      const steps = this.parseWorkflowSteps(workflowDefinition);

      // 创建执行上下文
      const context = await executionContextManager.createContext(
        execution.id,
        workflowId,
        0,
        triggerContext || {}
      );

      // 创建执行会话
      const session: WorkflowExecutionSession = {
        sessionId: this.generateSessionId(),
        executionId: execution.id,
        workflowId,
        workflowDefinition,
        currentStep: 0,
        totalSteps: steps.length,
        status: WorkflowCoordinationStatus.STARTING,
        startTime: new Date(),
        context,
        executionResults: new Map(),
        errors: []
      };

      this.executionSessions.set(session.sessionId, session);

      this.emit('workflow:execution:started', {
        sessionId: session.sessionId,
        executionId: execution.id,
        workflowId,
        totalSteps: steps.length
      });

      // 异步执行工作流
      this.executeWorkflowAsync(session, steps);

      return session.sessionId;

    } catch (error: any) {
      console.error(`❌ 启动工作流执行失败: ${workflowId}`, error);
      throw error;
    }
  }

  /**
   * 暂停工作流执行
   */
  async pauseWorkflowExecution(sessionId: string): Promise<void> {
    const session = this.executionSessions.get(sessionId);
    if (!session) {
      throw new Error(`执行会话不存在: ${sessionId}`);
    }

    if (session.status !== WorkflowCoordinationStatus.RUNNING) {
      throw new Error(`工作流不在运行状态: ${session.status}`);
    }

    session.status = WorkflowCoordinationStatus.PAUSED;
    
    // 暂停执行上下文
    session.context.abortController.abort();

    this.emit('workflow:execution:paused', {
      sessionId,
      executionId: session.executionId,
      currentStep: session.currentStep
    });

    console.log(`⏸️ 暂停工作流执行: ${sessionId}`);
  }

  /**
   * 恢复工作流执行
   */
  async resumeWorkflowExecution(sessionId: string): Promise<void> {
    const session = this.executionSessions.get(sessionId);
    if (!session) {
      throw new Error(`执行会话不存在: ${sessionId}`);
    }

    if (session.status !== WorkflowCoordinationStatus.PAUSED) {
      throw new Error(`工作流不在暂停状态: ${session.status}`);
    }

    session.status = WorkflowCoordinationStatus.RUNNING;
    
    // 创建新的AbortController
    session.context.abortController = new AbortController();

    this.emit('workflow:execution:resumed', {
      sessionId,
      executionId: session.executionId,
      currentStep: session.currentStep
    });

    console.log(`▶️ 恢复工作流执行: ${sessionId}`);

    // 继续执行
    const steps = this.parseWorkflowSteps(session.workflowDefinition);
    this.continueWorkflowExecution(session, steps);
  }

  /**
   * 停止工作流执行
   */
  async stopWorkflowExecution(sessionId: string, reason: string = '用户停止'): Promise<void> {
    const session = this.executionSessions.get(sessionId);
    if (!session) {
      throw new Error(`执行会话不存在: ${sessionId}`);
    }

    session.status = WorkflowCoordinationStatus.STOPPING;
    session.endTime = new Date();
    
    // 停止执行上下文
    session.context.abortController.abort();

    // 记录停止原因
    const stopError: ExecutionError = {
      type: 'EXECUTION_ERROR',
      code: 'WORKFLOW_STOPPED',
      message: `工作流被停止: ${reason}`,
      recoverable: false,
      severity: 'MEDIUM',
      timestamp: new Date()
    };
    session.errors.push(stopError);

    // 执行回滚（如果需要）
    if (session.currentStep > 0) {
      await this.executeWorkflowRollback(session);
    }

    session.status = WorkflowCoordinationStatus.STOPPED;

    this.emit('workflow:execution:stopped', {
      sessionId,
      executionId: session.executionId,
      reason,
      completedSteps: session.currentStep,
      totalSteps: session.totalSteps
    });

    console.log(`⏹️ 停止工作流执行: ${sessionId} - ${reason}`);

    // 清理会话
    setTimeout(() => {
      this.cleanupSession(sessionId);
    }, 5000); // 5秒后清理
  }

  /**
   * 获取执行会话信息
   */
  getExecutionSession(sessionId: string): WorkflowExecutionSession | undefined {
    return this.executionSessions.get(sessionId);
  }

  /**
   * 列出所有执行会话
   */
  listExecutionSessions(): WorkflowExecutionSession[] {
    return Array.from(this.executionSessions.values());
  }

  /**
   * 获取协调器状态
   */
  getCoordinatorStatus(): {
    status: WorkflowCoordinationStatus;
    activeSessions: number;
    totalSessions: number;
    config: TriggerExecutorIntegrationConfig;
  } {
    return {
      status: this.status,
      activeSessions: Array.from(this.executionSessions.values())
        .filter(s => s.status === WorkflowCoordinationStatus.RUNNING).length,
      totalSessions: this.executionSessions.size,
      config: this.config
    };
  }

  /**
   * 获取执行统计
   */
  getExecutionStatistics(): {
    completedExecutions: number;
    failedExecutions: number;
    runningExecutions: number;
    averageExecutionTime: number;
    successRate: number;
  } {
    const sessions = Array.from(this.executionSessions.values());
    const completed = sessions.filter(s => s.status === WorkflowCoordinationStatus.STOPPED && s.errors.length === 0);
    const failed = sessions.filter(s => s.status === WorkflowCoordinationStatus.STOPPED && s.errors.length > 0);
    const running = sessions.filter(s => s.status === WorkflowCoordinationStatus.RUNNING);

    const completedWithTime = completed.filter(s => s.endTime);
    const averageExecutionTime = completedWithTime.length > 0
      ? completedWithTime.reduce((sum, s) => {
          return sum + (s.endTime!.getTime() - s.startTime.getTime());
        }, 0) / completedWithTime.length
      : 0;

    const successRate = (completed.length + failed.length) > 0
      ? (completed.length / (completed.length + failed.length)) * 100
      : 0;

    return {
      completedExecutions: completed.length,
      failedExecutions: failed.length,
      runningExecutions: running.length,
      averageExecutionTime: Math.round(averageExecutionTime),
      successRate: Math.round(successRate * 100) / 100
    };
  }

  // ========== 私有方法 ==========

  /**
   * 初始化依赖组件
   */
  private async initializeDependencies(): Promise<void> {
    console.log('🔧 初始化依赖组件...');

    // 初始化所有必要的服务
    const initPromises = [
      stepExecutionEngine.initialize(),
      errorRecoveryManager.initialize(),
      metricsCollector.initialize(),
      logAggregator.initialize()
    ];

    await Promise.all(initPromises);
    
    console.log('✅ 所有依赖组件初始化完成');
  }

  /**
   * 设置组件间集成
   */
  private setupComponentIntegration(): void {
    console.log('🔗 设置组件间集成...');

    // 触发器管理器集成
    triggerManager.on('trigger:fired', async (event) => {
      try {
        await this.startWorkflowExecution(event.workflowId, event.data);
      } catch (error) {
        console.error('触发器启动工作流失败:', error);
        this.emit('integration:error', { source: 'trigger', error });
      }
    });

    // 错误恢复管理器集成
    errorRecoveryManager.on('recovery:failed', (event) => {
      const session = Array.from(this.executionSessions.values())
        .find(s => s.executionId === event.executionId);
      
      if (session) {
        this.handleWorkflowError(session, {
          type: 'EXECUTION_ERROR',
          code: 'RECOVERY_FAILED',
          message: '错误恢复失败',
          recoverable: false,
          severity: 'HIGH',
          timestamp: new Date()
        });
      }
    });
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    console.log('👂 设置事件监听器...');

    // 监听步骤执行引擎事件
    stepExecutionEngine.on('step:completed', (event) => {
      const session = Array.from(this.executionSessions.values())
        .find(s => s.executionId === event.executionId);
      
      if (session) {
        this.handleStepCompleted(session, event.stepIndex, event.result);
      }
    });

    stepExecutionEngine.on('step:failed', (event) => {
      const session = Array.from(this.executionSessions.values())
        .find(s => s.executionId === event.executionId);
      
      if (session) {
        this.handleStepFailed(session, event.stepIndex, event.error);
      }
    });

    // 监听系统关闭事件
    process.on('SIGTERM', () => this.gracefulShutdown());
    process.on('SIGINT', () => this.gracefulShutdown());
  }

  /**
   * 异步执行工作流
   */
  private async executeWorkflowAsync(
    session: WorkflowExecutionSession,
    steps: WorkflowStep[]
  ): Promise<void> {
    try {
      session.status = WorkflowCoordinationStatus.RUNNING;
      
      await this.continueWorkflowExecution(session, steps);
      
    } catch (error: any) {
      console.error(`❌ 工作流执行异常: ${session.sessionId}`, error);
      await this.handleWorkflowError(session, {
        type: 'SYSTEM_ERROR',
        code: 'WORKFLOW_EXECUTION_ERROR',
        message: `工作流执行异常: ${error.message}`,
        recoverable: false,
        severity: 'CRITICAL',
        timestamp: new Date()
      });
    }
  }

  /**
   * 继续工作流执行
   */
  private async continueWorkflowExecution(
    session: WorkflowExecutionSession,
    steps: WorkflowStep[]
  ): Promise<void> {
    while (session.currentStep < steps.length && 
           session.status === WorkflowCoordinationStatus.RUNNING) {
      
      const step = steps[session.currentStep];
      
      try {
        console.log(`🔄 执行步骤 ${session.currentStep + 1}/${steps.length}: ${step.name}`);
        
        // 开始状态跟踪
        await executionStateTracker.startExecution(
          session.executionId,
          session.workflowId,
          step.index
        );

        // 执行步骤
        const result = await stepExecutionEngine.executeStep(
          session.executionId,
          session.workflowId,
          step,
          session.context.variables
        );

        // 记录结果
        session.executionResults.set(step.index, result);

        if (result.success) {
          await this.handleStepCompleted(session, step.index, result);
        } else {
          await this.handleStepFailed(session, step.index, result.error!);
          break; // 步骤失败时停止执行
        }

      } catch (error: any) {
        await this.handleStepError(session, step, error);
        break; // 发生异常时停止执行
      }
    }

    // 检查是否完成
    if (session.currentStep >= steps.length && session.status === WorkflowCoordinationStatus.RUNNING) {
      await this.completeWorkflowExecution(session);
    }
  }

  /**
   * 处理步骤完成
   */
  private async handleStepCompleted(
    session: WorkflowExecutionSession,
    stepIndex: number,
    result: ActionExecutionResult
  ): Promise<void> {
    console.log(`✅ 步骤 ${stepIndex} 执行成功`);

    // 更新上下文变量
    if (result.data) {
      session.context.variables[`step_${stepIndex}_result`] = result.data;
    }

    // 完成状态跟踪
    await executionStateTracker.completeExecution(session.executionId, stepIndex, result);

    // 移动到下一步
    session.currentStep++;

    this.emit('workflow:step:completed', {
      sessionId: session.sessionId,
      executionId: session.executionId,
      stepIndex,
      result
    });
  }

  /**
   * 处理步骤失败
   */
  private async handleStepFailed(
    session: WorkflowExecutionSession,
    stepIndex: number,
    error: ExecutionError
  ): Promise<void> {
    console.error(`❌ 步骤 ${stepIndex} 执行失败:`, error);

    session.errors.push(error);

    // 记录失败状态
    await executionStateTracker.failExecution(session.executionId, stepIndex, error);

    // 如果启用了自动恢复
    if (this.config.enableAutoRecovery) {
      try {
        // 这里可以触发错误恢复流程
        console.log('🔧 尝试自动恢复...');
        // await this.attemptAutoRecovery(session, stepIndex, error);
      } catch (recoveryError) {
        console.error('自动恢复失败:', recoveryError);
      }
    }

    this.emit('workflow:step:failed', {
      sessionId: session.sessionId,
      executionId: session.executionId,
      stepIndex,
      error
    });

    // 执行工作流失败处理
    await this.handleWorkflowError(session, error);
  }

  /**
   * 处理步骤异常
   */
  private async handleStepError(
    session: WorkflowExecutionSession,
    step: WorkflowStep,
    error: any
  ): Promise<void> {
    const executionError: ExecutionError = {
      type: 'EXECUTION_ERROR',
      code: 'STEP_EXECUTION_EXCEPTION',
      message: `步骤执行异常: ${error.message}`,
      details: { stepName: step.name, stepIndex: step.index },
      recoverable: false,
      severity: 'HIGH',
      timestamp: new Date()
    };

    await this.handleStepFailed(session, step.index, executionError);
  }

  /**
   * 处理工作流错误
   */
  private async handleWorkflowError(
    session: WorkflowExecutionSession,
    error: ExecutionError
  ): Promise<void> {
    session.status = WorkflowCoordinationStatus.STOPPING;
    session.endTime = new Date();
    session.errors.push(error);

    // 执行回滚
    await this.executeWorkflowRollback(session);

    session.status = WorkflowCoordinationStatus.STOPPED;

    this.emit('workflow:execution:failed', {
      sessionId: session.sessionId,
      executionId: session.executionId,
      error,
      completedSteps: session.currentStep,
      totalSteps: session.totalSteps
    });
  }

  /**
   * 完成工作流执行
   */
  private async completeWorkflowExecution(session: WorkflowExecutionSession): Promise<void> {
    session.status = WorkflowCoordinationStatus.STOPPED;
    session.endTime = new Date();

    const duration = session.endTime.getTime() - session.startTime.getTime();

    console.log(`🎉 工作流执行完成: ${session.sessionId} (耗时: ${duration}ms)`);

    this.emit('workflow:execution:completed', {
      sessionId: session.sessionId,
      executionId: session.executionId,
      completedSteps: session.currentStep,
      totalSteps: session.totalSteps,
      duration
    });

    // 延迟清理会话
    setTimeout(() => {
      this.cleanupSession(session.sessionId);
    }, 60000); // 1分钟后清理
  }

  /**
   * 执行工作流回滚
   */
  private async executeWorkflowRollback(session: WorkflowExecutionSession): Promise<void> {
    if (session.currentStep === 0) return;

    try {
      console.log(`🔄 开始工作流回滚: ${session.sessionId}`);

      const steps = this.parseWorkflowSteps(session.workflowDefinition);
      const executedSteps = steps.slice(0, session.currentStep);

      // 使用错误恢复管理器执行回滚
      await errorRecoveryManager.executeWorkflowRollback(
        session.executionId,
        session.workflowId,
        session.currentStep,
        executedSteps
      );

      console.log(`✅ 工作流回滚完成: ${session.sessionId}`);

    } catch (error) {
      console.error(`❌ 工作流回滚失败: ${session.sessionId}`, error);
    }
  }

  /**
   * 解析工作流步骤
   */
  private parseWorkflowSteps(workflowDefinition: WorkflowDefinition): WorkflowStep[] {
    try {
      const definition = JSON.parse(workflowDefinition.definition as string);
      return definition.steps || [];
    } catch (error) {
      console.error('解析工作流定义失败:', error);
      return [];
    }
  }

  /**
   * 生成会话ID
   */
  private generateSessionId(): string {
    return `wf_session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 清理会话
   */
  private cleanupSession(sessionId: string): void {
    const session = this.executionSessions.get(sessionId);
    if (session) {
      // 清理执行上下文
      executionContextManager.cleanupContext(session.executionId);
      
      // 移除会话
      this.executionSessions.delete(sessionId);
      
      console.log(`🧹 清理执行会话: ${sessionId}`);
      
      this.emit('workflow:session:cleaned', { sessionId });
    }
  }

  /**
   * 优雅关闭
   */
  private async gracefulShutdown(): Promise<void> {
    console.log('🛑 开始优雅关闭WorkflowCoordinator...');
    
    this.status = WorkflowCoordinationStatus.STOPPING;

    // 停止所有正在运行的会话
    const runningSessions = Array.from(this.executionSessions.values())
      .filter(s => s.status === WorkflowCoordinationStatus.RUNNING);

    for (const session of runningSessions) {
      await this.stopWorkflowExecution(session.sessionId, '系统关闭');
    }

    // 清理所有会话
    for (const sessionId of this.executionSessions.keys()) {
      this.cleanupSession(sessionId);
    }

    this.status = WorkflowCoordinationStatus.STOPPED;
    this.isInitialized = false;

    console.log('✅ WorkflowCoordinator优雅关闭完成');
    this.emit('coordinator:shutdown');
  }

  /**
   * 全局清理
   */
  async cleanup(): Promise<void> {
    await this.gracefulShutdown();
  }
}

// 导出单例实例
export const workflowCoordinator = WorkflowCoordinator.getInstance();