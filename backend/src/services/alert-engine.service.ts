import { prisma } from '@/config/database.config'
import { CacheService } from '@/config/redis.config'
import { RealtimeService } from './realtime.service'
import { EmailService } from './email.service'
import { SmsService } from './sms.service'
import { 
  AlertRule, 
  Alert, 
  AlertSeverity, 
  AlertStatus, 
  AlertCondition,
  AlertMetricType
} from '@prisma/client'
import { SystemMetrics } from '@/types/monitor.types'

export interface AlertRuleConfig {
  name: string
  description?: string
  metricType: AlertMetricType
  condition: AlertCondition
  threshold: number
  duration: number // 持续时间（秒）
  severity: AlertSeverity
  enabled: boolean
  notificationChannels: string[]
  cooldownPeriod?: number // 冷却期（秒）
  tags?: string[]
}

export interface AlertTriggerContext {
  metricValue: number
  metrics: SystemMetrics
  timestamp: Date
  previousAlerts?: Alert[]
}

export interface AlertNotificationChannel {
  type: 'email' | 'sms' | 'webhook' | 'realtime'
  config: Record<string, any>
  enabled: boolean
}

export interface AlertDeduplicationRule {
  timeWindow: number // 时间窗口（秒）
  similarityThreshold: number // 相似度阈值（0-1）
  maxAlertsPerWindow: number
}

export interface AlertEscalationRule {
  level: number
  delayMinutes: number
  channels: string[]
  conditions?: string[]
}

/**
 * 智能告警规则引擎服务
 * 提供告警规则管理、告警触发、告警降噪、优先级分级等功能
 */
export class AlertEngineService {
  private static instance: AlertEngineService
  private alertHistory: Map<string, Alert[]> = new Map()
  private cooldownTracker: Map<string, Date> = new Map()
  private readonly MAX_HISTORY_SIZE = 1000
  private readonly DEFAULT_COOLDOWN = 300 // 5分钟默认冷却期

  private constructor() {
    this.initializeService()
  }

  public static getInstance(): AlertEngineService {
    if (!AlertEngineService.instance) {
      AlertEngineService.instance = new AlertEngineService()
    }
    return AlertEngineService.instance
  }

  /**
   * 初始化服务
   */
  private async initializeService() {
    console.log('🚨 Alert Engine Service 初始化中...')
    
    // 清理过期的告警历史
    setInterval(() => {
      this.cleanupExpiredHistory()
    }, 300000) // 每5分钟清理一次
    
    // 检查未解决的告警升级
    setInterval(() => {
      this.processAlertEscalation()
    }, 60000) // 每分钟检查一次升级
    
    console.log('✅ Alert Engine Service 初始化完成')
  }

  /**
   * 创建告警规则
   */
  async createAlertRule(config: AlertRuleConfig, createdBy: string): Promise<AlertRule> {
    try {
      // 验证配置
      this.validateAlertRuleConfig(config)

      const rule = await prisma.alertRule.create({
        data: {
          name: config.name,
          description: config.description || null,
          metricType: config.metricType,
          condition: config.condition,
          threshold: config.threshold,
          duration: config.duration,
          severity: config.severity,
          enabled: config.enabled,
          notificationChannels: JSON.stringify(config.notificationChannels),
        }
      })

      // 缓存规则以提高性能
      await this.cacheAlertRule(rule)

      console.log(`📋 创建告警规则: ${rule.name} (${rule.id})`)
      return rule
    } catch (error) {
      console.error('创建告警规则失败:', error)
      throw new Error(`创建告警规则失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 更新告警规则
   */
  async updateAlertRule(ruleId: string, config: Partial<AlertRuleConfig>): Promise<AlertRule> {
    try {
      const rule = await prisma.alertRule.update({
        where: { id: ruleId },
        data: {
          ...config,
          notificationChannels: config.notificationChannels 
            ? JSON.stringify(config.notificationChannels)
            : undefined,
          updatedAt: new Date()
        }
      })

      // 更新缓存
      await this.cacheAlertRule(rule)

      console.log(`🔄 更新告警规则: ${rule.name}`)
      return rule
    } catch (error) {
      console.error('更新告警规则失败:', error)
      throw error
    }
  }

  /**
   * 删除告警规则
   */
  async deleteAlertRule(ruleId: string): Promise<void> {
    try {
      await prisma.alertRule.delete({
        where: { id: ruleId }
      })

      // 清除缓存
      await CacheService.del(`alert:rule:${ruleId}`)

      console.log(`🗑️ 删除告警规则: ${ruleId}`)
    } catch (error) {
      console.error('删除告警规则失败:', error)
      throw error
    }
  }

  /**
   * 获取所有告警规则
   */
  async getAllAlertRules(includeDisabled: boolean = false): Promise<AlertRule[]> {
    try {
      const where = includeDisabled ? {} : { enabled: true }
      return await prisma.alertRule.findMany({
        where,
        orderBy: [
          { severity: 'desc' },
          { name: 'asc' }
        ]
      })
    } catch (error) {
      console.error('获取告警规则失败:', error)
      return []
    }
  }

  /**
   * 评估指标并触发告警
   */
  async evaluateMetrics(metrics: SystemMetrics): Promise<Alert[]> {
    try {
      const rules = await this.getAllAlertRules()
      const triggeredAlerts: Alert[] = []

      for (const rule of rules) {
        const alert = await this.evaluateRule(rule, metrics)
        if (alert) {
          triggeredAlerts.push(alert)
        }
      }

      return triggeredAlerts
    } catch (error) {
      console.error('评估指标时发生错误:', error)
      return []
    }
  }

  /**
   * 评估单个规则
   */
  private async evaluateRule(rule: AlertRule, metrics: SystemMetrics): Promise<Alert | null> {
    try {
      // 获取指标值
      const metricValue = this.extractMetricValue(metrics, rule.metricType)
      if (metricValue === null) return null

      // 检查条件
      const conditionMet = this.checkCondition(metricValue, rule.condition, rule.threshold)
      if (!conditionMet) {
        // 如果条件不满足，检查是否需要自动恢复告警
        await this.checkAutoRecovery(rule.id, metricValue)
        return null
      }

      // 检查冷却期
      if (this.isInCooldown(rule.id)) {
        console.log(`⏰ 告警规则 ${rule.name} 在冷却期内`)
        return null
      }

      // 检查持续时间
      const durationMet = await this.checkDuration(rule, metricValue)
      if (!durationMet) return null

      // 应用告警降噪
      const shouldSuppress = await this.shouldSuppressAlert(rule, metricValue, metrics)
      if (shouldSuppress) {
        console.log(`🔇 告警被降噪抑制: ${rule.name}`)
        return null
      }

      // 创建告警
      const alert = await this.createAlert(rule, metricValue, {
        metricValue,
        metrics,
        timestamp: new Date()
      })

      // 发送通知
      await this.sendNotifications(alert, rule)

      // 设置冷却期
      this.setCooldown(rule.id)

      return alert
    } catch (error) {
      console.error(`评估规则 ${rule.name} 时发生错误:`, error)
      return null
    }
  }

  /**
   * 创建告警
   */
  private async createAlert(
    rule: AlertRule, 
    metricValue: number, 
    context: AlertTriggerContext
  ): Promise<Alert> {
    const message = this.generateAlertMessage(rule, metricValue, context)
    
    const alert = await prisma.alert.create({
      data: {
        ruleId: rule.id,
        severity: rule.severity,
        status: 'PENDING',
        message,
        metricValue,
        triggeredAt: context.timestamp
      }
    })

    // 添加到历史记录
    this.addToHistory(rule.id, alert)

    // 实时推送
    await this.pushRealtimeAlert(alert, rule)

    console.log(`🚨 触发告警: ${rule.name} - ${message}`)
    return alert
  }

  /**
   * 发送通知
   */
  private async sendNotifications(alert: Alert, rule: AlertRule): Promise<void> {
    try {
      const channels = JSON.parse(rule.notificationChannels as string) as string[]
      const emailService = new EmailService()
      const smsService = new SmsService()

      for (const channel of channels) {
        switch (channel) {
          case 'email':
            await this.sendEmailNotification(alert, rule, emailService)
            break
          case 'sms':
            await this.sendSmsNotification(alert, rule, smsService)
            break
          case 'webhook':
            await this.sendWebhookNotification(alert, rule)
            break
          case 'realtime':
            // 已在 createAlert 中处理
            break
          default:
            console.warn(`未知的通知渠道: ${channel}`)
        }
      }

      // 记录通知发送状态
      await this.recordNotificationStatus(alert.id, channels)
    } catch (error) {
      console.error('发送通知失败:', error)
    }
  }

  /**
   * 发送邮件通知
   */
  private async sendEmailNotification(
    alert: Alert, 
    rule: AlertRule, 
    emailService: EmailService
  ): Promise<void> {
    try {
      // 获取管理员邮箱列表
      const adminEmails = await this.getAdminEmails()
      
      for (const email of adminEmails) {
        await emailService.sendAlertEmail(email, {
          ruleName: rule.name,
          severity: alert.severity,
          message: alert.message,
          metricValue: alert.metricValue,
          threshold: rule.threshold,
          triggeredAt: alert.triggeredAt
        })
      }

      console.log(`📧 发送邮件告警通知: ${rule.name}`)
    } catch (error) {
      console.error('发送邮件通知失败:', error)
    }
  }

  /**
   * 发送短信通知
   */
  private async sendSmsNotification(
    alert: Alert, 
    rule: AlertRule, 
    smsService: SmsService
  ): Promise<void> {
    try {
      // 只对高优先级告警发送短信
      if (alert.severity !== 'HIGH' && alert.severity !== 'CRITICAL') return

      const adminPhones = await this.getAdminPhones()
      const smsContent = `【运维告警】${rule.name}: ${alert.message}`

      for (const phone of adminPhones) {
        await smsService.sendSms(phone, smsContent)
      }

      console.log(`📱 发送短信告警通知: ${rule.name}`)
    } catch (error) {
      console.error('发送短信通知失败:', error)
    }
  }

  /**
   * 发送Webhook通知
   */
  private async sendWebhookNotification(alert: Alert, rule: AlertRule): Promise<void> {
    try {
      const webhookUrls = await this.getWebhookUrls()
      
      const payload = {
        alertId: alert.id,
        ruleName: rule.name,
        severity: alert.severity,
        status: alert.status,
        message: alert.message,
        metricValue: alert.metricValue,
        threshold: rule.threshold,
        triggeredAt: alert.triggeredAt,
        timestamp: new Date().toISOString()
      }

      for (const url of webhookUrls) {
        try {
          const response = await fetch(url, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'User-Agent': 'OPS-Management-Alert-Engine'
            },
            body: JSON.stringify(payload)
          })

          if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`)
          }

          console.log(`🔗 Webhook通知发送成功: ${url}`)
        } catch (error) {
          console.error(`Webhook通知发送失败 ${url}:`, error)
        }
      }
    } catch (error) {
      console.error('发送Webhook通知失败:', error)
    }
  }

  /**
   * 告警降噪判断
   */
  private async shouldSuppressAlert(
    rule: AlertRule, 
    metricValue: number, 
    metrics: SystemMetrics
  ): Promise<boolean> {
    try {
      // 1. 检查重复告警（时间窗口内的相同告警）
      const recentAlerts = await this.getRecentAlerts(rule.id, 300) // 5分钟窗口
      if (recentAlerts.length > 0) {
        console.log(`🔇 降噪: 时间窗口内已存在相似告警`)
        return true
      }

      // 2. 检查告警频率（防止告警风暴）
      const hourlyAlerts = await this.getRecentAlerts(rule.id, 3600) // 1小时窗口
      if (hourlyAlerts.length >= 10) {
        console.log(`🔇 降噪: 1小时内告警次数过多 (${hourlyAlerts.length})`)
        return true
      }

      // 3. 智能降噪：检查指标波动情况
      const isFluctuation = await this.isMetricFluctuation(rule.metricType, metricValue)
      if (isFluctuation) {
        console.log(`🔇 降噪: 检测到指标波动，可能是临时异常`)
        return true
      }

      // 4. 业务时间降噪（非工作时间的低优先级告警）
      if (this.isNonBusinessHours() && rule.severity === 'LOW') {
        console.log(`🔇 降噪: 非工作时间的低优先级告警`)
        return true
      }

      return false
    } catch (error) {
      console.error('告警降噪判断失败:', error)
      return false
    }
  }

  /**
   * 智能优先级调整
   */
  async adjustAlertPriority(alert: Alert, context?: any): Promise<AlertSeverity> {
    try {
      let adjustedSeverity = alert.severity

      // 1. 基于系统整体状态调整优先级
      const systemHealth = await this.getSystemHealthScore()
      if (systemHealth < 60) {
        // 系统整体不健康时，提升告警优先级
        adjustedSeverity = this.escalateSeverity(adjustedSeverity)
      }

      // 2. 基于历史模式调整
      const historicalPattern = await this.getHistoricalAlertPattern(alert.ruleId)
      if (historicalPattern.isRecurrent) {
        // 频繁出现的告警降低优先级
        adjustedSeverity = this.degradeSeverity(adjustedSeverity)
      }

      // 3. 基于业务影响调整
      const businessImpact = await this.assessBusinessImpact(alert)
      if (businessImpact === 'HIGH') {
        adjustedSeverity = this.escalateSeverity(adjustedSeverity)
      }

      // 4. 基于用户活跃度调整
      const userActivity = await this.getCurrentUserActivity()
      if (userActivity.activeUsers > 50 && alert.severity === 'MEDIUM') {
        // 高用户活跃期间的中等告警提升优先级
        adjustedSeverity = 'HIGH'
      }

      if (adjustedSeverity !== alert.severity) {
        await this.updateAlertSeverity(alert.id, adjustedSeverity)
        console.log(`⚖️ 调整告警优先级: ${alert.severity} → ${adjustedSeverity}`)
      }

      return adjustedSeverity
    } catch (error) {
      console.error('调整告警优先级失败:', error)
      return alert.severity
    }
  }

  /**
   * 告警自动恢复检查
   */
  private async checkAutoRecovery(ruleId: string, currentValue: number): Promise<void> {
    try {
      const pendingAlerts = await prisma.alert.findMany({
        where: {
          ruleId,
          status: { in: ['PENDING', 'ACKNOWLEDGED'] }
        },
        include: { rule: true }
      })

      for (const alert of pendingAlerts) {
        const rule = alert.rule
        const recoveryCondition = this.getRecoveryCondition(rule.condition)
        const recoveryThreshold = this.getRecoveryThreshold(rule.threshold, rule.metricType)

        if (this.checkCondition(currentValue, recoveryCondition, recoveryThreshold)) {
          await this.resolveAlert(alert.id, 'AUTO_RECOVERED')
          console.log(`✅ 告警自动恢复: ${rule.name}`)
        }
      }
    } catch (error) {
      console.error('检查告警自动恢复失败:', error)
    }
  }

  /**
   * 手动解决告警
   */
  async resolveAlert(alertId: string, resolvedBy?: string, note?: string): Promise<void> {
    try {
      await prisma.alert.update({
        where: { id: alertId },
        data: {
          status: 'RESOLVED',
          resolvedAt: new Date(),
          resolvedBy,
          // 如果有备注，可以添加到详细信息中
        }
      })

      // 通知实时更新
      const realtimeService = RealtimeService.getInstance()
      await realtimeService.pushAlertResolved(alertId)

      console.log(`✅ 告警已解决: ${alertId}`)
    } catch (error) {
      console.error('解决告警失败:', error)
      throw error
    }
  }

  /**
   * 告警确认
   */
  async acknowledgeAlert(alertId: string, acknowledgedBy: string, note?: string): Promise<void> {
    try {
      await prisma.alert.update({
        where: { id: alertId },
        data: {
          status: 'ACKNOWLEDGED',
          acknowledgedAt: new Date(),
          acknowledgedBy
        }
      })

      console.log(`👀 告警已确认: ${alertId} by ${acknowledgedBy}`)
    } catch (error) {
      console.error('确认告警失败:', error)
      throw error
    }
  }

  /**
   * 获取告警统计
   */
  async getAlertStatistics(timeRange: { start: Date; end: Date }) {
    try {
      const { start, end } = timeRange

      const [
        totalAlerts,
        severityStats,
        statusStats,
        topRules,
        resolutionTime
      ] = await Promise.all([
        // 总告警数
        prisma.alert.count({
          where: {
            triggeredAt: { gte: start, lte: end }
          }
        }),
        // 按严重程度统计
        prisma.alert.groupBy({
          by: ['severity'],
          where: {
            triggeredAt: { gte: start, lte: end }
          },
          _count: { severity: true }
        }),
        // 按状态统计
        prisma.alert.groupBy({
          by: ['status'],
          where: {
            triggeredAt: { gte: start, lte: end }
          },
          _count: { status: true }
        }),
        // 触发最多的规则
        prisma.alert.groupBy({
          by: ['ruleId'],
          where: {
            triggeredAt: { gte: start, lte: end }
          },
          _count: { ruleId: true },
          orderBy: { _count: { ruleId: 'desc' } },
          take: 10
        }),
        // 平均解决时间
        this.calculateAverageResolutionTime(start, end)
      ])

      return {
        total: totalAlerts,
        bySeverity: Object.fromEntries(
          severityStats.map(s => [s.severity, s._count.severity])
        ),
        byStatus: Object.fromEntries(
          statusStats.map(s => [s.status, s._count.status])
        ),
        topRules: await this.enrichRuleStats(topRules),
        averageResolutionTime: resolutionTime,
        period: { start, end }
      }
    } catch (error) {
      console.error('获取告警统计失败:', error)
      throw error
    }
  }

  // ========== 私有辅助方法 ==========

  /**
   * 验证告警规则配置
   */
  private validateAlertRuleConfig(config: AlertRuleConfig): void {
    if (!config.name?.trim()) {
      throw new Error('告警规则名称不能为空')
    }
    if (config.threshold < 0 || config.threshold > 100) {
      throw new Error('阈值必须在0-100之间')
    }
    if (config.duration < 0) {
      throw new Error('持续时间不能为负数')
    }
    if (!config.notificationChannels?.length) {
      throw new Error('至少需要配置一个通知渠道')
    }
  }

  /**
   * 缓存告警规则
   */
  private async cacheAlertRule(rule: AlertRule): Promise<void> {
    const cacheKey = `alert:rule:${rule.id}`
    await CacheService.setex(cacheKey, 3600, JSON.stringify(rule))
  }

  /**
   * 提取指标值
   */
  private extractMetricValue(metrics: SystemMetrics, metricType: AlertMetricType): number | null {
    switch (metricType) {
      case 'CPU':
        return metrics.cpu.usage
      case 'MEMORY':
        return metrics.memory.usage
      case 'DISK':
        return metrics.disk.usage
      case 'NETWORK':
        return metrics.network.totalRxSpeed + metrics.network.totalTxSpeed
      default:
        return null
    }
  }

  /**
   * 检查条件
   */
  private checkCondition(value: number, condition: AlertCondition, threshold: number): boolean {
    switch (condition) {
      case 'GT': return value > threshold
      case 'LT': return value < threshold
      case 'GTE': return value >= threshold
      case 'LTE': return value <= threshold
      case 'EQ': return value === threshold
      case 'NEQ': return value !== threshold
      default: return false
    }
  }

  /**
   * 检查持续时间
   */
  private async checkDuration(rule: AlertRule, metricValue: number): Promise<boolean> {
    // 简化实现：检查缓存中的连续触发记录
    const cacheKey = `alert:duration:${rule.id}`
    const records = await CacheService.get(cacheKey)
    
    const now = Date.now()
    let triggerRecords: number[] = records ? JSON.parse(records) : []
    
    // 添加当前触发时间
    triggerRecords.push(now)
    
    // 清理过期记录
    const cutoffTime = now - rule.duration * 1000
    triggerRecords = triggerRecords.filter(time => time > cutoffTime)
    
    // 更新缓存
    await CacheService.setex(cacheKey, rule.duration + 60, JSON.stringify(triggerRecords))
    
    // 检查是否满足持续时间要求
    return triggerRecords.length >= Math.max(1, Math.floor(rule.duration / 30)) // 每30秒一个数据点
  }

  /**
   * 检查冷却期
   */
  private isInCooldown(ruleId: string): boolean {
    const cooldownUntil = this.cooldownTracker.get(ruleId)
    if (!cooldownUntil) return false
    
    return Date.now() < cooldownUntil.getTime()
  }

  /**
   * 设置冷却期
   */
  private setCooldown(ruleId: string, duration?: number): void {
    const cooldownDuration = duration || this.DEFAULT_COOLDOWN
    const cooldownUntil = new Date(Date.now() + cooldownDuration * 1000)
    this.cooldownTracker.set(ruleId, cooldownUntil)
  }

  /**
   * 生成告警消息
   */
  private generateAlertMessage(
    rule: AlertRule, 
    metricValue: number, 
    context: AlertTriggerContext
  ): string {
    const metricName = this.getMetricDisplayName(rule.metricType)
    const conditionText = this.getConditionDisplayText(rule.condition)
    const unit = this.getMetricUnit(rule.metricType)
    
    return `${metricName}${conditionText}${rule.threshold}${unit}，当前值: ${metricValue}${unit}`
  }

  /**
   * 添加到历史记录
   */
  private addToHistory(ruleId: string, alert: Alert): void {
    if (!this.alertHistory.has(ruleId)) {
      this.alertHistory.set(ruleId, [])
    }
    
    const history = this.alertHistory.get(ruleId)!
    history.push(alert)
    
    // 保持历史记录在合理范围内
    if (history.length > this.MAX_HISTORY_SIZE) {
      history.shift()
    }
  }

  /**
   * 实时推送告警
   */
  private async pushRealtimeAlert(alert: Alert, rule: AlertRule): Promise<void> {
    try {
      const realtimeService = RealtimeService.getInstance()
      await realtimeService.pushSystemAlert({
        id: alert.id,
        title: rule.name,
        message: alert.message,
        severity: alert.severity,
        component: rule.metricType,
        status: alert.status
      })
    } catch (error) {
      console.error('推送实时告警失败:', error)
    }
  }

  private getMetricDisplayName(type: AlertMetricType): string {
    const names: Record<AlertMetricType, string> = {
      'CPU': 'CPU使用率',
      'MEMORY': '内存使用率',
      'DISK': '磁盘使用率',
      'NETWORK': '网络流量',
      'SERVICE': '服务状态',
      'DATABASE': '数据库',
      'REDIS': 'Redis缓存'
    }
    return names[type] || type
  }

  private getConditionDisplayText(condition: AlertCondition): string {
    const texts: Record<AlertCondition, string> = {
      'GT': '超过',
      'LT': '低于',
      'GTE': '达到或超过',
      'LTE': '低于或等于',
      'EQ': '等于',
      'NEQ': '不等于'
    }
    return texts[condition] || condition
  }

  private getMetricUnit(type: AlertMetricType): string {
    const units: Record<AlertMetricType, string> = {
      'CPU': '%',
      'MEMORY': '%',
      'DISK': '%',
      'NETWORK': 'Mbps',
      'SERVICE': '',
      'DATABASE': 'ms',
      'REDIS': 'ms'
    }
    return units[type] || ''
  }

  // 其他辅助方法的简化实现
  private async getRecentAlerts(ruleId: string, seconds: number): Promise<Alert[]> {
    const cutoffTime = new Date(Date.now() - seconds * 1000)
    return await prisma.alert.findMany({
      where: {
        ruleId,
        triggeredAt: { gte: cutoffTime }
      },
      orderBy: { triggeredAt: 'desc' }
    })
  }

  private async isMetricFluctuation(metricType: AlertMetricType, currentValue: number): Promise<boolean> {
    // 简化实现：检查最近几个数据点的方差
    return false // 实际应该基于历史数据计算
  }

  private isNonBusinessHours(): boolean {
    const now = new Date()
    const hour = now.getHours()
    const day = now.getDay()
    
    // 简化：工作时间为周一到周五 9:00-18:00
    return day === 0 || day === 6 || hour < 9 || hour >= 18
  }

  private async getSystemHealthScore(): Promise<number> {
    // 简化实现
    return 85
  }

  private async getAdminEmails(): Promise<string[]> {
    const admins = await prisma.user.findMany({
      where: {
        userRoles: {
          some: {
            role: {
              name: { in: ['admin', 'system_admin'] }
            }
          }
        }
      },
      select: { email: true }
    })
    return admins.map(admin => admin.email)
  }

  private async getAdminPhones(): Promise<string[]> {
    const admins = await prisma.user.findMany({
      where: {
        userRoles: {
          some: {
            role: {
              name: { in: ['admin', 'system_admin'] }
            }
          }
        },
        phone: { not: null }
      },
      select: { phone: true }
    })
    return admins.filter(admin => admin.phone).map(admin => admin.phone!)
  }

  private async getWebhookUrls(): Promise<string[]> {
    // 从系统配置获取webhook URLs
    return []
  }

  private async recordNotificationStatus(alertId: string, channels: string[]): Promise<void> {
    await prisma.alert.update({
      where: { id: alertId },
      data: {
        notificationsSent: JSON.stringify({
          channels,
          sentAt: new Date().toISOString()
        })
      }
    })
  }

  private escalateSeverity(severity: AlertSeverity): AlertSeverity {
    const escalationMap: Record<AlertSeverity, AlertSeverity> = {
      'LOW': 'MEDIUM',
      'MEDIUM': 'HIGH',
      'HIGH': 'CRITICAL',
      'CRITICAL': 'CRITICAL'
    }
    return escalationMap[severity]
  }

  private degradeSeverity(severity: AlertSeverity): AlertSeverity {
    const degradationMap: Record<AlertSeverity, AlertSeverity> = {
      'CRITICAL': 'HIGH',
      'HIGH': 'MEDIUM',
      'MEDIUM': 'LOW',
      'LOW': 'LOW'
    }
    return degradationMap[severity]
  }

  private async updateAlertSeverity(alertId: string, severity: AlertSeverity): Promise<void> {
    await prisma.alert.update({
      where: { id: alertId },
      data: { severity }
    })
  }

  private getRecoveryCondition(condition: AlertCondition): AlertCondition {
    const recoveryMap: Record<AlertCondition, AlertCondition> = {
      'GT': 'LTE',
      'GTE': 'LT',
      'LT': 'GTE',
      'LTE': 'GT',
      'EQ': 'NEQ',
      'NEQ': 'EQ'
    }
    return recoveryMap[condition]
  }

  private getRecoveryThreshold(threshold: number, metricType: AlertMetricType): number {
    // 添加一定的回滞，避免告警抖动
    const hysteresis = metricType === 'CPU' ? 5 : 3
    return threshold - hysteresis
  }

  private async calculateAverageResolutionTime(start: Date, end: Date): Promise<number> {
    const resolvedAlerts = await prisma.alert.findMany({
      where: {
        triggeredAt: { gte: start, lte: end },
        status: 'RESOLVED',
        resolvedAt: { not: null }
      },
      select: {
        triggeredAt: true,
        resolvedAt: true
      }
    })

    if (resolvedAlerts.length === 0) return 0

    const totalResolutionTime = resolvedAlerts.reduce((sum, alert) => {
      const resolutionTime = alert.resolvedAt!.getTime() - alert.triggeredAt.getTime()
      return sum + resolutionTime
    }, 0)

    return Math.round(totalResolutionTime / resolvedAlerts.length / 1000 / 60) // 转换为分钟
  }

  private async enrichRuleStats(topRules: any[]): Promise<any[]> {
    const enrichedRules = []
    for (const ruleStat of topRules) {
      const rule = await prisma.alertRule.findUnique({
        where: { id: ruleStat.ruleId },
        select: { name: true, metricType: true, severity: true }
      })
      enrichedRules.push({
        ...ruleStat,
        rule
      })
    }
    return enrichedRules
  }

  private cleanupExpiredHistory(): void {
    const cutoffTime = Date.now() - 24 * 60 * 60 * 1000 // 24小时前
    
    for (const [ruleId, alerts] of this.alertHistory.entries()) {
      const validAlerts = alerts.filter(alert => 
        alert.triggeredAt.getTime() > cutoffTime
      )
      
      if (validAlerts.length === 0) {
        this.alertHistory.delete(ruleId)
      } else {
        this.alertHistory.set(ruleId, validAlerts)
      }
    }

    // 清理过期的冷却期记录
    for (const [ruleId, cooldownUntil] of this.cooldownTracker.entries()) {
      if (Date.now() > cooldownUntil.getTime()) {
        this.cooldownTracker.delete(ruleId)
      }
    }
  }

  private async processAlertEscalation(): Promise<void> {
    // 简化实现：检查未解决的高优先级告警
    try {
      const unacknowledgedAlerts = await prisma.alert.findMany({
        where: {
          status: 'PENDING',
          severity: { in: ['HIGH', 'CRITICAL'] },
          triggeredAt: {
            lt: new Date(Date.now() - 30 * 60 * 1000) // 30分钟前
          }
        },
        include: { rule: true }
      })

      for (const alert of unacknowledgedAlerts) {
        console.log(`⬆️ 告警升级: ${alert.rule.name} 已超过30分钟未处理`)
        // 这里可以实现升级逻辑，如发送给更高级管理员
      }
    } catch (error) {
      console.error('处理告警升级失败:', error)
    }
  }

  private async getHistoricalAlertPattern(ruleId: string): Promise<{ isRecurrent: boolean }> {
    // 简化实现
    return { isRecurrent: false }
  }

  private async assessBusinessImpact(alert: Alert): Promise<'LOW' | 'MEDIUM' | 'HIGH'> {
    // 简化实现
    return 'MEDIUM'
  }

  private async getCurrentUserActivity(): Promise<{ activeUsers: number }> {
    // 简化实现
    return { activeUsers: 25 }
  }

  /**
   * 创建默认告警规则
   */
  async createDefaultAlertRules(): Promise<void> {
    try {
      const defaultRules = [
        {
          name: 'CPU使用率过高',
          description: '系统CPU使用率超过80%时触发告警',
          metricType: 'CPU',
          condition: 'GT',
          threshold: 80,
          duration: 300, // 5分钟
          severity: 'HIGH',
          enabled: true,
          notificationChannels: ['email', 'realtime']
        },
        {
          name: '内存使用率过高',
          description: '系统内存使用率超过85%时触发告警',
          metricType: 'MEMORY',
          condition: 'GT',
          threshold: 85,
          duration: 180, // 3分钟
          severity: 'HIGH',
          enabled: true,
          notificationChannels: ['email', 'realtime']
        },
        {
          name: '磁盘使用率过高',
          description: '磁盘使用率超过90%时触发告警',
          metricType: 'DISK',
          condition: 'GT',
          threshold: 90,
          duration: 600, // 10分钟
          severity: 'CRITICAL',
          enabled: true,
          notificationChannels: ['email', 'sms', 'realtime']
        },
        {
          name: '网络流量异常',
          description: '网络流量异常时触发告警',
          metricType: 'NETWORK',
          condition: 'GT',
          threshold: 1000, // 1000 Mbps
          duration: 120, // 2分钟
          severity: 'MEDIUM',
          enabled: true,
          notificationChannels: ['email', 'realtime']
        }
      ]

      for (const ruleConfig of defaultRules) {
        // 检查是否已存在
        const existingRule = await prisma.alertRule.findFirst({
          where: { name: ruleConfig.name }
        })

        if (!existingRule) {
          await this.createAlertRule(ruleConfig as any, 'SYSTEM')
          console.log(`✅ 创建默认告警规则: ${ruleConfig.name}`)
        }
      }

    } catch (error) {
      console.error('创建默认告警规则失败:', error)
    }
  }
}