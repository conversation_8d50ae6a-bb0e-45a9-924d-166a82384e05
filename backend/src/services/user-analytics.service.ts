import { prisma } from '@/config/database.config'
import { CacheService } from '@/config/redis.config'
import { AuditService } from './audit.service'
import { RealtimeService } from './realtime.service'

export interface UserSessionInfo {
  id: string
  userId: string
  sessionToken: string
  ipAddress?: string
  userAgent?: string
  location?: any
  deviceInfo?: any
  isActive: boolean
  lastActivity: Date
  loginTime: Date
  logoutTime?: Date
  durationMinutes?: number
  activityScore?: number
}

export interface ActivitySummary {
  totalUsers: number
  activeUsers: number
  onlineUsers: number
  todayLogins: number
  avgSessionDuration: number
  peakActivityTime: string
  topActiveUsers: Array<{
    user: any
    activityScore: number
    loginCount: number
    totalDuration: number
  }>
}

export interface UserBehaviorAnalysis {
  userId: string
  activityLevel: 'HIGH' | 'MEDIUM' | 'LOW'
  preferredHours: number[]
  avgSessionDuration: number
  frequentFeatures: string[]
  riskScore: number
  patterns: any[]
  anomalies: any[]
}

export class UserAnalyticsService {
  private static readonly CACHE_TTL = 300 // 5分钟缓存
  private static readonly ONLINE_THRESHOLD = 30 // 30分钟内算在线
  private static readonly HIGH_ACTIVITY_SCORE = 80
  private static readonly MEDIUM_ACTIVITY_SCORE = 40

  /**
   * 创建用户会话
   */
  static async createSession(
    userId: string,
    sessionToken: string,
    ipAddress?: string,
    userAgent?: string
  ): Promise<UserSessionInfo> {
    console.log('=== CREATE SESSION CALLED ===', { userId, hasToken: !!sessionToken, ipAddress, userAgent: userAgent?.substring(0, 50) })
    try {
      // 解析设备和位置信息
      const deviceInfo = this.parseDeviceInfo(userAgent)
      const location = await this.parseLocationInfo(ipAddress)

      console.log('Creating session for user:', userId, 'with token:', sessionToken?.substring(0, 20) + '...')
      
      const session = await prisma.userSession.create({
        data: {
          userId,
          sessionToken,
          ipAddress,
          userAgent,
          deviceInfo,
          location,
          loginTime: new Date(),
          lastActivity: new Date(),
          isActive: true
        }
      })

      console.log('Session created successfully:', session.id)
      
      // 更新用户最后登录时间
      await prisma.user.update({
        where: { id: userId },
        data: { lastLoginAt: new Date() }
      })

      // 记录登录活动统计
      try {
        await this.updateDailyStats(userId, 'LOGIN')
      } catch (error) {
        console.error('Failed to update daily stats:', error)
      }
      
      // 缓存在线用户信息
      try {
        await CacheService.setex(
          `online_user:${userId}`,
          this.ONLINE_THRESHOLD * 60,
          JSON.stringify({
            userId,
            sessionToken,
            loginTime: session.loginTime,
            lastActivity: session.lastActivity
          })
        )
      } catch (error) {
        console.error('Failed to cache online user:', error)
      }

      return session
    } catch (error) {
      console.error('Error creating session:', error)
      throw error
    }
  }

  /**
   * 更新会话活动
   */
  static async updateSessionActivity(
    sessionToken: string,
    activityData?: {
      featureName?: string
      pageView?: boolean
      operationType?: string
    }
  ): Promise<void> {
    const session = await prisma.userSession.findUnique({
      where: { sessionToken }
    })

    if (!session || !session.isActive) return

    const now = new Date()
    const lastActivity = new Date(session.lastActivity)
    const timeDiff = now.getTime() - lastActivity.getTime()
    const minutesDiff = Math.floor(timeDiff / (1000 * 60))

    // 更新会话活动时间
    await prisma.userSession.update({
      where: { sessionToken },
      data: {
        lastActivity: now,
        durationMinutes: session.durationMinutes 
          ? session.durationMinutes + minutesDiff 
          : minutesDiff
      }
    })

    // 更新Redis缓存
    await CacheService.setex(
      `online_user:${session.userId}`,
      this.ONLINE_THRESHOLD * 60,
      JSON.stringify({
        userId: session.userId,
        sessionToken,
        loginTime: session.loginTime,
        lastActivity: now
      })
    )

    // 记录特定活动
    if (activityData) {
      await this.recordActivity(session.userId, activityData)
    }
  }

  /**
   * 结束用户会话
   */
  static async endSession(sessionToken: string): Promise<void> {
    const session = await prisma.userSession.findUnique({
      where: { sessionToken }
    })

    if (!session) return

    const now = new Date()
    const durationMinutes = Math.floor(
      (now.getTime() - session.loginTime.getTime()) / (1000 * 60)
    )

    // 计算活动分数
    const activityScore = this.calculateActivityScore(
      durationMinutes,
      session.userId
    )

    await prisma.userSession.update({
      where: { sessionToken },
      data: {
        isActive: false,
        logoutTime: now,
        durationMinutes,
        activityScore: await activityScore
      }
    })

    // 从Redis中移除在线用户
    await CacheService.del(`online_user:${session.userId}`)

    // 更新每日统计
    await this.updateDailyStats(session.userId, 'LOGOUT', {
      sessionDuration: durationMinutes,
      activityScore: await activityScore
    })
  }

  /**
   * 获取在线用户列表
   */
  static async getOnlineUsers(): Promise<any[]> {
    const cacheKey = 'online_users_list'
    const cached = await CacheService.get(cacheKey)
    
    if (cached) {
      return JSON.parse(cached)
    }

    const onlineThreshold = new Date(Date.now() - this.ONLINE_THRESHOLD * 60 * 1000)
    
    const onlineUsers = await prisma.userSession.findMany({
      where: {
        isActive: true,
        lastActivity: {
          gte: onlineThreshold
        }
      },
      include: {
        user: {
          select: {
            id: true,
            username: true,
            fullName: true,
            avatar: true,
            department: true
          }
        }
      },
      orderBy: {
        lastActivity: 'desc'
      }
    })

    const result = onlineUsers.map(session => ({
      ...session.user,
      sessionId: session.id,
      lastActivity: session.lastActivity,
      loginTime: session.loginTime,
      ipAddress: session.ipAddress,
      location: session.location
    }))

    // 缓存结果
    await CacheService.setex(cacheKey, 60, JSON.stringify(result))

    return result
  }

  /**
   * 获取用户活动概览
   */
  static async getActivitySummary(
    startDate?: Date,
    endDate?: Date
  ): Promise<ActivitySummary> {
    const cacheKey = `activity_summary:${startDate?.toISOString()}:${endDate?.toISOString()}`
    const cached = await CacheService.get(cacheKey)
    
    if (cached) {
      return JSON.parse(cached)
    }

    if (!startDate) startDate = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
    if (!endDate) endDate = new Date()

    const dateFilter = {
      gte: startDate,
      lte: endDate
    }

    // 总用户数
    const totalUsers = await prisma.user.count({
      where: { status: 'ACTIVE' }
    })

    // 活跃用户数（期间有活动的用户）
    const activeUsers = await prisma.userActivityStats.groupBy({
      by: ['userId'],
      where: {
        date: {
          gte: startDate,
          lte: endDate
        },
        activityScore: { gt: 0 }
      }
    })

    // 在线用户数
    const onlineUsers = await this.getOnlineUsers()

    // 今日登录数
    const today = new Date()
    today.setHours(0, 0, 0, 0)
    const todayLogins = await prisma.userSession.count({
      where: {
        loginTime: { gte: today }
      }
    })

    // 平均会话时长
    const avgDurationResult = await prisma.userSession.aggregate({
      where: {
        loginTime: dateFilter,
        durationMinutes: { not: null }
      },
      _avg: {
        durationMinutes: true
      }
    })

    // 高活跃用户
    const topActiveUsers = await prisma.userActivityStats.findMany({
      where: {
        date: dateFilter,
        activityScore: { gt: this.MEDIUM_ACTIVITY_SCORE }
      },
      include: {
        user: {
          select: {
            id: true,
            username: true,
            fullName: true,
            avatar: true,
            department: true
          }
        }
      },
      orderBy: {
        activityScore: 'desc'
      },
      take: 10
    })

    // 获取高峰活动时间
    const peakTime = await this.getPeakActivityTime(startDate, endDate)

    const summary: ActivitySummary = {
      totalUsers,
      activeUsers: activeUsers.length,
      onlineUsers: onlineUsers.length,
      todayLogins,
      avgSessionDuration: Number(avgDurationResult._avg.durationMinutes || 0),
      peakActivityTime: peakTime,
      topActiveUsers: topActiveUsers.map(stat => ({
        user: stat.user,
        activityScore: stat.activityScore,
        loginCount: stat.loginCount,
        totalDuration: stat.totalDurationMinutes
      }))
    }

    // 缓存结果
    await CacheService.setex(cacheKey, this.CACHE_TTL, JSON.stringify(summary))

    return summary
  }

  /**
   * 获取用户行为分析
   */
  static async getUserBehaviorAnalysis(
    userId: string,
    days: number = 30
  ): Promise<UserBehaviorAnalysis> {
    const startDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000)
    const endDate = new Date()

    // 获取用户活动统计
    const stats = await prisma.userActivityStats.findMany({
      where: {
        userId,
        date: {
          gte: startDate,
          lte: endDate
        }
      },
      orderBy: { date: 'desc' }
    })

    if (stats.length === 0) {
      return {
        userId,
        activityLevel: 'LOW',
        preferredHours: [],
        avgSessionDuration: 0,
        frequentFeatures: [],
        riskScore: 0,
        patterns: [],
        anomalies: []
      }
    }

    // 计算活动等级
    const avgScore = stats.reduce((sum, s) => sum + s.activityScore, 0) / stats.length
    const activityLevel = avgScore >= this.HIGH_ACTIVITY_SCORE ? 'HIGH' :
                         avgScore >= this.MEDIUM_ACTIVITY_SCORE ? 'MEDIUM' : 'LOW'

    // 获取偏好时间
    const preferredHours = this.calculatePreferredHours(stats)

    // 平均会话时长
    const avgSessionDuration = stats.reduce((sum, s) => sum + s.totalDurationMinutes, 0) / 
                              stats.reduce((sum, s) => sum + s.sessionCount, 0) || 0

    // 常用功能
    const featureUsage = await prisma.userFeatureUsage.findMany({
      where: {
        userId,
        date: {
          gte: startDate,
          lte: endDate
        }
      },
      orderBy: {
        usageCount: 'desc'
      },
      take: 10
    })

    const frequentFeatures = featureUsage.map(f => f.featureName)

    // 获取行为模式
    const patterns = await prisma.userBehaviorPattern.findMany({
      where: { userId }
    })

    // 获取异常记录
    const anomalies = await prisma.userActivityAnomaly.findMany({
      where: {
        userId,
        detectedAt: {
          gte: startDate,
          lte: endDate
        }
      },
      orderBy: { detectedAt: 'desc' }
    })

    // 计算风险分数
    const riskScore = this.calculateRiskScore(anomalies, patterns, stats)

    return {
      userId,
      activityLevel,
      preferredHours,
      avgSessionDuration,
      frequentFeatures,
      riskScore,
      patterns,
      anomalies
    }
  }

  /**
   * 检测用户行为异常
   */
  static async detectAnomalies(userId: string): Promise<void> {
    const user = await prisma.user.findUnique({ where: { id: userId } })
    if (!user) return

    // 获取用户历史行为模式
    const patterns = await prisma.userBehaviorPattern.findMany({
      where: { userId, status: 'ACTIVE' }
    })

    // 获取最近活动数据
    const recentActivity = await this.getRecentUserActivity(userId, 7)
    
    // 检测各种异常类型
    await Promise.all([
      this.detectUnusualTimeActivity(userId, patterns, recentActivity),
      this.detectExcessiveOperations(userId, recentActivity),
      this.detectAbnormalLocation(userId, recentActivity),
      this.detectSuspiciousBehavior(userId, recentActivity)
    ])
  }

  /**
   * 更新用户行为模式
   */
  static async updateBehaviorPatterns(userId: string): Promise<void> {
    const days = 30
    const startDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000)
    
    // 获取用户活动数据
    const sessions = await prisma.userSession.findMany({
      where: {
        userId,
        loginTime: { gte: startDate }
      }
    })

    if (sessions.length === 0) return

    // 分析登录时间模式
    const loginPattern = this.analyzeLoginPattern(sessions)
    await this.upsertBehaviorPattern(userId, 'LOGIN_TIME', loginPattern)

    // 分析活动时长模式
    const durationPattern = this.analyzeDurationPattern(sessions)
    await this.upsertBehaviorPattern(userId, 'ACTIVITY_DURATION', durationPattern)

    // 分析功能使用模式
    const featurePattern = await this.analyzeFeatureUsagePattern(userId, startDate)
    await this.upsertBehaviorPattern(userId, 'FEATURE_USAGE', featurePattern)

    // 分析操作频率模式
    const operationPattern = await this.analyzeOperationFrequencyPattern(userId, startDate)
    await this.upsertBehaviorPattern(userId, 'OPERATION_FREQUENCY', operationPattern)
  }

  /**
   * 获取用户活动热力图数据
   */
  static async getActivityHeatmap(
    userId?: string,
    days: number = 30
  ): Promise<Array<{ date: string, hour: number, value: number }>> {
    const startDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000)
    
    const whereCondition: any = {
      createdAt: { gte: startDate }
    }
    
    if (userId) {
      whereCondition.userId = userId
    }

    // 使用原始SQL查询获取热力图数据
    const result = await prisma.$queryRaw`
      SELECT 
        DATE(created_at) as date,
        HOUR(created_at) as hour,
        COUNT(*) as value
      FROM operation_logs 
      WHERE created_at >= ${startDate}
        ${userId ? prisma.$queryRaw`AND user_id = ${userId}` : prisma.$queryRaw``}
      GROUP BY DATE(created_at), HOUR(created_at)
      ORDER BY date ASC, hour ASC
    ` as Array<{ date: string, hour: number, value: bigint }>

    return result.map(item => ({
      date: item.date,
      hour: Number(item.hour),
      value: Number(item.value)
    }))
  }

  /**
   * 导出用户活动报告
   */
  static async exportActivityReport(
    startDate: Date,
    endDate: Date,
    userIds?: string[]
  ): Promise<any> {
    const whereCondition: any = {
      date: {
        gte: startDate,
        lte: endDate
      }
    }

    if (userIds) {
      whereCondition.userId = { in: userIds }
    }

    const stats = await prisma.userActivityStats.findMany({
      where: whereCondition,
      include: {
        user: {
          select: {
            id: true,
            username: true,
            fullName: true,
            email: true,
            department: true
          }
        }
      },
      orderBy: [
        { date: 'desc' },
        { activityScore: 'desc' }
      ]
    })

    return {
      period: { startDate, endDate },
      totalRecords: stats.length,
      data: stats.map(stat => ({
        date: stat.date,
        user: stat.user,
        loginCount: stat.loginCount,
        sessionCount: stat.sessionCount,
        totalDuration: stat.totalDurationMinutes,
        operationCount: stat.operationCount,
        activityScore: stat.activityScore,
        featuresUsed: stat.featuresUsed
      }))
    }
  }

  // ==================== 私有辅助方法 ====================

  /**
   * 记录用户活动
   */
  private static async recordActivity(
    userId: string,
    activityData: {
      featureName?: string
      pageView?: boolean
      operationType?: string
    }
  ): Promise<void> {
    const today = new Date()
    today.setHours(0, 0, 0, 0)

    // 更新功能使用统计
    if (activityData.featureName) {
      await prisma.userFeatureUsage.upsert({
        where: {
          userId_featureName_date: {
            userId,
            featureName: activityData.featureName,
            date: today
          }
        },
        update: {
          usageCount: { increment: 1 },
          lastUsed: new Date()
        },
        create: {
          userId,
          featureName: activityData.featureName,
          date: today,
          usageCount: 1,
          lastUsed: new Date()
        }
      })
    }

    // 更新日活动统计
    if (activityData.pageView) {
      await this.updateDailyStats(userId, 'PAGE_VIEW')
    }

    if (activityData.operationType) {
      await this.updateDailyStats(userId, 'OPERATION')
    }

    // 实时推送用户活动（仅重要操作）
    if (activityData.operationType) {
      try {
        const user = await prisma.user.findUnique({
          where: { id: userId },
          select: { username: true, fullName: true }
        })
        
        if (user) {
          const realtimeService = RealtimeService.getInstance()
          await realtimeService.pushUserActivity({
            userId,
            username: user.username,
            fullName: user.fullName || user.username,
            action: activityData.operationType
          })
        }
      } catch (error) {
        console.error('Failed to push user activity realtime:', error)
        // 不抛出错误，避免影响主要业务流程
      }
    }
  }

  /**
   * 更新每日统计数据
   */
  private static async updateDailyStats(
    userId: string,
    type: 'LOGIN' | 'LOGOUT' | 'PAGE_VIEW' | 'OPERATION',
    data?: any
  ): Promise<void> {
    const today = new Date()
    today.setHours(0, 0, 0, 0)

    const updateData: any = {}
    
    switch (type) {
      case 'LOGIN':
        updateData.loginCount = { increment: 1 }
        updateData.sessionCount = { increment: 1 }
        break
      case 'LOGOUT':
        if (data?.sessionDuration) {
          updateData.totalDurationMinutes = { increment: data.sessionDuration }
        }
        if (data?.activityScore) {
          updateData.activityScore = { increment: data.activityScore }
        }
        break
      case 'PAGE_VIEW':
        updateData.pageViews = { increment: 1 }
        break
      case 'OPERATION':
        updateData.operationCount = { increment: 1 }
        break
    }

    updateData.lastActivity = new Date()

    await prisma.userActivityStats.upsert({
      where: {
        userId_date: {
          userId,
          date: today
        }
      },
      update: updateData,
      create: {
        userId,
        date: today,
        loginCount: type === 'LOGIN' ? 1 : 0,
        sessionCount: type === 'LOGIN' ? 1 : 0,
        totalDurationMinutes: data?.sessionDuration || 0,
        operationCount: type === 'OPERATION' ? 1 : 0,
        pageViews: type === 'PAGE_VIEW' ? 1 : 0,
        activityScore: data?.activityScore || 0,
        lastActivity: new Date()
      }
    })
  }

  /**
   * 计算活动分数
   */
  private static async calculateActivityScore(
    sessionDuration: number,
    userId: string
  ): Promise<number> {
    // 基础分数基于会话时长
    let score = Math.min(sessionDuration * 0.5, 50) // 最高50分

    // 获取今日操作次数
    const today = new Date()
    today.setHours(0, 0, 0, 0)
    
    const todayLogs = await prisma.operationLog.count({
      where: {
        userId,
        createdAt: { gte: today }
      }
    })

    // 操作次数加分
    score += Math.min(todayLogs * 2, 30) // 最高30分

    // 功能多样性加分
    const featureCount = await prisma.userFeatureUsage.count({
      where: {
        userId,
        date: today,
        usageCount: { gt: 0 }
      }
    })

    score += Math.min(featureCount * 3, 20) // 最高20分

    return Math.min(score, 100)
  }

  /**
   * 解析设备信息
   */
  private static parseDeviceInfo(userAgent?: string): any {
    if (!userAgent) return null

    // 简单的设备信息解析
    const isMobile = /Mobile|Android|iPhone|iPad/.test(userAgent)
    const browser = userAgent.includes('Chrome') ? 'Chrome' :
                   userAgent.includes('Firefox') ? 'Firefox' :
                   userAgent.includes('Safari') ? 'Safari' : 'Unknown'

    return {
      isMobile,
      browser,
      userAgent
    }
  }

  /**
   * 解析位置信息
   */
  private static async parseLocationInfo(ipAddress?: string): Promise<any> {
    if (!ipAddress || ipAddress === '127.0.0.1' || ipAddress.startsWith('192.168.')) {
      return { type: 'local', ip: ipAddress }
    }

    // 在实际应用中，这里可以调用IP地理位置API
    // 这里返回模拟数据
    return {
      ip: ipAddress,
      country: 'China',
      city: 'Unknown'
    }
  }

  /**
   * 获取高峰活动时间
   */
  private static async getPeakActivityTime(startDate: Date, endDate: Date): Promise<string> {
    const result = await prisma.$queryRaw`
      SELECT 
        HOUR(created_at) as hour,
        COUNT(*) as count
      FROM operation_logs 
      WHERE created_at >= ${startDate} AND created_at <= ${endDate}
      GROUP BY HOUR(created_at)
      ORDER BY count DESC
      LIMIT 1
    ` as Array<{ hour: unknown, count: unknown }>

    if (result.length === 0) return '未知'

    const rawHour = result[0]?.hour as any
    const hourNum = typeof rawHour === 'bigint' ? Number(rawHour) : Number(rawHour)
    const start = isNaN(hourNum) ? 0 : hourNum
    const end = (start + 1) % 24
    return `${start}:00 - ${end}:00`
  }

  /**
   * 计算偏好时间
   */
  private static calculatePreferredHours(stats: any[]): number[] {
    const hourCounts: { [key: number]: number } = {}
    
    stats.forEach(stat => {
      if (stat.peakActivityHour !== null) {
        hourCounts[stat.peakActivityHour] = (hourCounts[stat.peakActivityHour] || 0) + 1
      }
    })

    return Object.entries(hourCounts)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 3)
      .map(([hour]) => parseInt(hour))
  }

  /**
   * 计算风险分数
   */
  private static calculateRiskScore(
    anomalies: any[],
    patterns: any[],
    stats: any[]
  ): number {
    let riskScore = 0

    // 异常记录加分
    anomalies.forEach(anomaly => {
      switch (anomaly.severity) {
        case 'LOW':
          riskScore += 10
          break
        case 'MEDIUM':
          riskScore += 25
          break
        case 'HIGH':
          riskScore += 50
          break
        case 'CRITICAL':
          riskScore += 100
          break
      }
    })

    // 异常模式加分
    const anomalyPatterns = patterns.filter(p => p.status === 'ANOMALY')
    riskScore += anomalyPatterns.length * 20

    return Math.min(riskScore, 100)
  }

  // 其他辅助方法的简化实现...
  private static async getRecentUserActivity(userId: string, days: number): Promise<any> {
    // 实现获取最近用户活动的逻辑
    return {}
  }

  private static async detectUnusualTimeActivity(userId: string, patterns: any[], recentActivity: any): Promise<void> {
    // 实现异常时间活动检测
  }

  private static async detectExcessiveOperations(userId: string, recentActivity: any): Promise<void> {
    // 实现过度操作检测
  }

  private static async detectAbnormalLocation(userId: string, recentActivity: any): Promise<void> {
    // 实现异常位置检测
  }

  private static async detectSuspiciousBehavior(userId: string, recentActivity: any): Promise<void> {
    // 实现可疑行为检测
  }

  private static analyzeLoginPattern(sessions: any[]): any {
    // 实现登录模式分析
    return { pattern: 'login_analysis', confidence: 0.8 }
  }

  private static analyzeDurationPattern(sessions: any[]): any {
    // 实现时长模式分析
    return { pattern: 'duration_analysis', confidence: 0.8 }
  }

  private static async analyzeFeatureUsagePattern(userId: string, startDate: Date): Promise<any> {
    // 实现功能使用模式分析
    return { pattern: 'feature_analysis', confidence: 0.8 }
  }

  private static async analyzeOperationFrequencyPattern(userId: string, startDate: Date): Promise<any> {
    // 实现操作频率模式分析
    return { pattern: 'operation_analysis', confidence: 0.8 }
  }

  private static async upsertBehaviorPattern(
    userId: string,
    patternType: any,
    patternData: any
  ): Promise<void> {
    await prisma.userBehaviorPattern.upsert({
      where: {
        userId_patternType: {
          userId,
          patternType
        }
      },
      update: {
        patternData,
        lastUpdated: new Date()
      },
      create: {
        userId,
        patternType,
        patternData,
        confidenceScore: patternData.confidence || 0
      }
    })
  }
}