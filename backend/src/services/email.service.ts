import nodemailer from 'nodemailer'
import { z } from 'zod'
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

// 邮件配置验证
const emailConfigSchema = z.object({
  host: z.string(),
  port: z.number(),
  user: z.string().email(),
  pass: z.string(),
  fromEmail: z.string().email(),
  fromName: z.string()
})

// 邮件发送参数验证
const sendEmailSchema = z.object({
  to: z.union([z.string().email(), z.array(z.string().email())]),
  subject: z.string().min(1),
  html: z.string().optional(),
  text: z.string().optional(),
  cc: z.union([z.string().email(), z.array(z.string().email())]).optional(),
  bcc: z.union([z.string().email(), z.array(z.string().email())]).optional(),
  attachments: z.array(z.object({
    filename: z.string(),
    path: z.string().optional(),
    content: z.any().optional(),
    contentType: z.string().optional()
  })).optional()
})

export interface EmailOptions {
  to: string | string[]
  subject: string
  html?: string
  text?: string
  cc?: string | string[]
  bcc?: string | string[]
  attachments?: Array<{
    filename: string
    path?: string
    content?: any
    contentType?: string
  }>
}

export interface EmailTemplate {
  subject: string
  html: string
  text?: string
}

class EmailService {
  private transporter: nodemailer.Transporter | null = null
  private config: {
    host: string
    port: number
    user: string
    pass: string
    fromEmail: string
    fromName: string
  } | null = null

  constructor() {
    this.initializeTransporter()
  }

  private initializeTransporter() {
    try {
      const config = {
        host: process.env['SMTP_HOST'] || '',
        port: parseInt(process.env['SMTP_PORT'] || '587'),
        user: process.env['SMTP_USER'] || '',
        pass: process.env['SMTP_PASS'] || '',
        fromEmail: process.env['FROM_EMAIL'] || '',
        fromName: process.env['FROM_NAME'] || '运维服务管理系统'
      }

      // 检查是否所有必需的配置都已设置
      const hasValidConfig = config.host && 
                           config.user && 
                           config.pass && 
                           config.fromEmail && 
                           config.user !== '<EMAIL>' && 
                           config.fromEmail !== '<EMAIL>'

      if (!hasValidConfig) {
        console.log('⚠️  Email service not configured - using placeholder values. Email functionality will be disabled.')
        this.transporter = null
        this.config = null
        return
      }

      // 验证配置
      const validatedConfig = emailConfigSchema.parse(config)
      this.config = validatedConfig

      // 创建传输器
      this.transporter = nodemailer.createTransport({
        host: validatedConfig.host,
        port: validatedConfig.port,
        secure: validatedConfig.port === 465, // true for 465, false for other ports
        auth: {
          user: validatedConfig.user,
          pass: validatedConfig.pass
        },
        tls: {
          rejectUnauthorized: false
        }
      })

      console.log('✅ Email service initialized successfully')
    } catch (error) {
      console.error('❌ Email service initialization failed:', error)
      this.transporter = null
      this.config = null
    }
  }

  // 发送邮件
  async sendEmail(options: EmailOptions): Promise<boolean> {
    try {
      if (!this.transporter || !this.config) {
        console.log('⚠️  Email service not configured - email not sent')
        return false
      }

      // 验证参数
      const validatedOptions = sendEmailSchema.parse(options)

      const mailOptions = {
        from: `"${this.config.fromName}" <${this.config.fromEmail}>`,
        to: Array.isArray(validatedOptions.to) ? validatedOptions.to.join(', ') : validatedOptions.to,
        subject: validatedOptions.subject,
        html: validatedOptions.html,
        text: validatedOptions.text || undefined,
        cc: validatedOptions.cc ? (Array.isArray(validatedOptions.cc) ? validatedOptions.cc.join(', ') : validatedOptions.cc) : undefined,
        bcc: validatedOptions.bcc ? (Array.isArray(validatedOptions.bcc) ? validatedOptions.bcc.join(', ') : validatedOptions.bcc) : undefined,
        attachments: validatedOptions.attachments
      }

      const result = await this.transporter.sendMail(mailOptions)
      console.log('📧 Email sent successfully:', result.messageId)
      return true
    } catch (error) {
      console.error('❌ Email send failed:', error)
      return false
    }
  }

  // 发送模板邮件
  async sendTemplateEmail(
    to: string | string[],
    templateName: string,
    variables: Record<string, any>,
    options?: Partial<EmailOptions>
  ): Promise<boolean> {
    try {
      const template = this.getEmailTemplate(templateName, variables)
      
      const emailOptions: EmailOptions = {
        to,
        subject: template.subject,
        html: template.html,
        ...options
      }
      
      if (template.text) {
        emailOptions.text = template.text
      }
      
      return await this.sendEmail(emailOptions)
    } catch (error) {
      console.error('❌ Template email send failed:', error)
      return false
    }
  }

  // 获取邮件模板
  private getEmailTemplate(templateName: string, variables: Record<string, any>): EmailTemplate {
    const templates: Record<string, (vars: Record<string, any>) => EmailTemplate> = {
      // 服务工单创建通知
      'service-created': (vars) => ({
        subject: `新服务工单 - ${vars['ticketNumber']}`,
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2 style="color: #333;">新服务工单已创建</h2>
            <div style="background: #f5f5f5; padding: 20px; border-radius: 5px; margin: 20px 0;">
              <p><strong>工单号：</strong>${vars['ticketNumber']}</p>
              <p><strong>标题：</strong>${vars['title']}</p>
              <p><strong>客户：</strong>${vars['customerName']}</p>
              <p><strong>优先级：</strong>${vars['priority']}</p>
              <p><strong>类别：</strong>${vars['category']}</p>
              <p><strong>描述：</strong></p>
              <p style="background: white; padding: 10px; border-radius: 3px;">${vars['description']}</p>
            </div>
            <p>请及时处理该工单。</p>
            <p style="color: #666; font-size: 12px;">此邮件由运维服务管理系统自动发送</p>
          </div>
        `,
        text: `新服务工单已创建\n\n工单号：${vars['ticketNumber']}\n标题：${vars['title']}\n客户：${vars['customerName']}\n优先级：${vars['priority']}\n类别：${vars['category']}\n描述：${vars['description']}\n\n请及时处理该工单。`
      }),

      // 服务工单状态更新通知
      'service-status-updated': (vars) => ({
        subject: `工单状态更新 - ${vars['ticketNumber']}`,
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2 style="color: #333;">工单状态已更新</h2>
            <div style="background: #f5f5f5; padding: 20px; border-radius: 5px; margin: 20px 0;">
              <p><strong>工单号：</strong>${vars['ticketNumber']}</p>
              <p><strong>标题：</strong>${vars['title']}</p>
              <p><strong>原状态：</strong>${vars['oldStatus']}</p>
              <p><strong>新状态：</strong><span style="color: #007bff;">${vars['newStatus']}</span></p>
              ${vars['comment'] ? `<p><strong>备注：</strong>${vars['comment']}</p>` : ''}
            </div>
            <p style="color: #666; font-size: 12px;">此邮件由运维服务管理系统自动发送</p>
          </div>
        `,
        text: `工单状态已更新\n\n工单号：${vars['ticketNumber']}\n标题：${vars['title']}\n原状态：${vars['oldStatus']}\n新状态：${vars['newStatus']}${vars['comment'] ? `\n备注：${vars['comment']}` : ''}`
      }),

      // 服务工单分配通知
      'service-assigned': (vars) => ({
        subject: `工单已分配给您 - ${vars['ticketNumber']}`,
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2 style="color: #333;">工单已分配给您</h2>
            <div style="background: #f5f5f5; padding: 20px; border-radius: 5px; margin: 20px 0;">
              <p><strong>工单号：</strong>${vars['ticketNumber']}</p>
              <p><strong>标题：</strong>${vars['title']}</p>
              <p><strong>客户：</strong>${vars['customerName']}</p>
              <p><strong>优先级：</strong>${vars['priority']}</p>
              <p><strong>分配人：</strong>${vars['assignedBy']}</p>
              <p><strong>描述：</strong></p>
              <p style="background: white; padding: 10px; border-radius: 3px;">${vars['description']}</p>
            </div>
            <p>请及时处理该工单。</p>
            <p style="color: #666; font-size: 12px;">此邮件由运维服务管理系统自动发送</p>
          </div>
        `,
        text: `工单已分配给您\n\n工单号：${vars['ticketNumber']}\n标题：${vars['title']}\n客户：${vars['customerName']}\n优先级：${vars['priority']}\n分配人：${vars['assignedBy']}\n描述：${vars['description']}\n\n请及时处理该工单。`
      }),

      // SLA违规警告
      'sla-violation-warning': (vars) => ({
        subject: `SLA违规警告 - ${vars['ticketNumber']}`,
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2 style="color: #dc3545;">SLA违规警告</h2>
            <div style="background: #f8d7da; padding: 20px; border-radius: 5px; margin: 20px 0; border-left: 4px solid #dc3545;">
              <p><strong>工单号：</strong>${vars['ticketNumber']}</p>
              <p><strong>标题：</strong>${vars['title']}</p>
              <p><strong>客户：</strong>${vars['customerName']}</p>
              <p><strong>SLA要求：</strong>${vars['slaRequirement']}</p>
              <p><strong>当前状态：</strong>${vars['currentStatus']}</p>
              <p><strong>超时时间：</strong>${vars['overdueTime']}</p>
            </div>
            <p style="color: #dc3545; font-weight: bold;">请立即处理该工单以避免SLA违规！</p>
            <p style="color: #666; font-size: 12px;">此邮件由运维服务管理系统自动发送</p>
          </div>
        `,
        text: `SLA违规警告\n\n工单号：${vars['ticketNumber']}\n标题：${vars['title']}\n客户：${vars['customerName']}\n SLA要求：${vars['slaRequirement']}\n当前状态：${vars['currentStatus']}\n超时时间：${vars['overdueTime']}\n\n请立即处理该工单以避免SLA违规！`
      })
    }

    const templateFunction = templates[templateName]
    if (!templateFunction) {
      throw new Error(`Email template '${templateName}' not found`)
    }

    return templateFunction(variables)
  }

  // 测试邮件连接
  async testConnection(): Promise<boolean> {
    try {
      if (!this.transporter) {
        return false
      }

      await this.transporter.verify()
      console.log('✅ Email connection test successful')
      return true
    } catch (error) {
      console.error('❌ Email connection test failed:', error)
      return false
    }
  }

  // 发送测试邮件
  async sendTestEmail(testEmail: string, message?: string): Promise<{ success: boolean; message: string; details?: any }> {
    try {
      if (!this.transporter || !this.config) {
        return {
          success: false,
          message: '邮件服务未配置，请检查SMTP设置'
        }
      }

      const testSubject = `运维系统邮件测试 - ${new Date().toLocaleString()}`
      const testContent = message || `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #333;">邮件配置测试</h2>
          <div style="background: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0;">
            <p>这是一封测试邮件，用于验证您的邮件配置是否正常工作。</p>
            <p><strong>测试时间：</strong>${new Date().toLocaleString()}</p>
            <p><strong>SMTP服务器：</strong>${this.config.host}:${this.config.port}</p>
            <p><strong>发送账户：</strong>${this.config.user}</p>
          </div>
          <p style="color: #28a745;">✅ 如果您收到这封邮件，说明邮件配置正常！</p>
          <p style="color: #666; font-size: 12px;">此邮件由运维服务管理系统自动发送</p>
        </div>
      `

      const result = await this.sendEmail({
        to: testEmail,
        subject: testSubject,
        html: testContent
      })

      if (result) {
        // 记录到邮件日志
        await prisma.emailLog.create({
          data: {
            recipient: testEmail,
            subject: testSubject,
            content: testContent,
            status: 'SENT',
            sentAt: new Date(),
            metadata: {
              isTest: true,
              smtpHost: this.config.host,
              smtpPort: this.config.port
            }
          }
        })

        return {
          success: true,
          message: `测试邮件已发送到 ${testEmail}，请检查邮箱（包括垃圾邮件文件夹）`,
          details: {
            recipient: testEmail,
            smtpHost: this.config.host,
            smtpPort: this.config.port,
            sentTime: new Date().toISOString()
          }
        }
      } else {
        return {
          success: false,
          message: '邮件发送失败，请检查SMTP配置或网络连接'
        }
      }
    } catch (error: any) {
      console.error('Email test failed:', error)
      
      // 记录失败日志
      try {
        await prisma.emailLog.create({
          data: {
            recipient: testEmail,
            subject: `测试邮件 - ${new Date().toLocaleString()}`,
            content: 'Email test failed',
            status: 'FAILED',
            errorMessage: error.message || 'Unknown error',
            metadata: {
              isTest: true,
              error: error.toString()
            }
          }
        })
      } catch (logError) {
        console.error('Failed to log email error:', logError)
      }

      return {
        success: false,
        message: `邮件发送失败: ${error.message || '未知错误'}`,
        details: {
          error: error.message,
          smtpHost: this.config?.host,
          smtpPort: this.config?.port
        }
      }
    }
  }

  // 使用数据库模板发送邮件
  async sendEmailWithTemplate(
    templateId: string,
    recipient: string,
    variables: Record<string, any> = {},
    options?: Partial<EmailOptions>
  ): Promise<{ success: boolean; message: string; logId?: string }> {
    try {
      if (!this.transporter || !this.config) {
        return {
          success: false,
          message: '邮件服务未配置'
        }
      }

      // 从数据库获取模板
      const template = await prisma.emailTemplate.findUnique({
        where: { id: templateId }
      })

      if (!template) {
        return {
          success: false,
          message: '邮件模板不存在'
        }
      }

      if (!template.enabled) {
        return {
          success: false,
          message: '邮件模板已禁用'
        }
      }

      // 渲染模板
      let renderedSubject = template.subject
      let renderedContent = template.content

      // 简单的变量替换
      Object.keys(variables).forEach(key => {
        const placeholder = new RegExp(`\\{\\{${key}\\}\\}`, 'g')
        const value = variables[key] || ''
        renderedSubject = renderedSubject.replace(placeholder, value)
        renderedContent = renderedContent.replace(placeholder, value)
      })

      // 创建邮件日志记录
      const emailLog = await prisma.emailLog.create({
        data: {
          templateId: template.id,
          recipient,
          subject: renderedSubject,
          content: renderedContent,
          status: 'PENDING',
          metadata: {
            variables,
            templateName: template.name,
            templateType: template.type
          }
        }
      })

      // 发送邮件
      const emailOptions: EmailOptions = {
        to: recipient,
        subject: renderedSubject,
        html: renderedContent,
        ...options
      }

      const result = await this.sendEmail(emailOptions)

      // 更新邮件日志状态
      if (result) {
        await prisma.emailLog.update({
          where: { id: emailLog.id },
          data: {
            status: 'SENT',
            sentAt: new Date()
          }
        })

        return {
          success: true,
          message: '邮件发送成功',
          logId: emailLog.id
        }
      } else {
        await prisma.emailLog.update({
          where: { id: emailLog.id },
          data: {
            status: 'FAILED',
            errorMessage: '邮件发送失败'
          }
        })

        return {
          success: false,
          message: '邮件发送失败'
        }
      }
    } catch (error: any) {
      console.error('Template email send failed:', error)
      return {
        success: false,
        message: `邮件发送失败: ${error.message || '未知错误'}`
      }
    }
  }

  // 批量发送模板邮件
  async sendBulkEmailWithTemplate(
    templateId: string,
    recipients: string[],
    variables: Record<string, any> = {}
  ): Promise<{ success: number; failed: number; results: any[] }> {
    const results = []
    let successCount = 0
    let failedCount = 0

    for (const recipient of recipients) {
      try {
        const result = await this.sendEmailWithTemplate(templateId, recipient, variables)
        results.push({
          recipient,
          success: result.success,
          message: result.message,
          logId: result.logId
        })

        if (result.success) {
          successCount++
        } else {
          failedCount++
        }
      } catch (error: any) {
        results.push({
          recipient,
          success: false,
          message: error.message || '发送失败',
          error: error.toString()
        })
        failedCount++
      }
    }

    return {
      success: successCount,
      failed: failedCount,
      results
    }
  }

  // 获取邮件发送统计
  async getEmailStats(startDate?: Date, endDate?: Date) {
    const where: any = {}
    
    if (startDate || endDate) {
      where.createdAt = {}
      if (startDate) where.createdAt.gte = startDate
      if (endDate) where.createdAt.lte = endDate
    }

    const [total, sent, failed, pending] = await Promise.all([
      prisma.emailLog.count({ where }),
      prisma.emailLog.count({ where: { ...where, status: 'SENT' } }),
      prisma.emailLog.count({ where: { ...where, status: 'FAILED' } }),
      prisma.emailLog.count({ where: { ...where, status: 'PENDING' } })
    ])

    return {
      total,
      sent,
      failed,
      pending,
      successRate: total > 0 ? (sent / total * 100).toFixed(2) : '0.00'
    }
  }

  // 获取服务状态
  isConfigured(): boolean {
    return this.transporter !== null && this.config !== null
  }

  // 获取配置信息（隐藏敏感数据）
  getConfigInfo() {
    if (!this.config) {
      return null
    }

    return {
      host: this.config.host,
      port: this.config.port,
      user: this.config.user,
      fromEmail: this.config.fromEmail,
      fromName: this.config.fromName,
      configured: true
    }
  }

  /**
   * 发送告警邮件
   */
  async sendAlertEmail(to: string, alertData: {
    ruleName: string
    severity: string
    message: string
    metricValue: number
    threshold: number
    triggeredAt: Date
  }) {
    const severityColors = {
      'LOW': '#28a745',
      'MEDIUM': '#ffc107', 
      'HIGH': '#fd7e14',
      'CRITICAL': '#dc3545'
    }

    const severityLabels = {
      'LOW': '低',
      'MEDIUM': '中',
      'HIGH': '高', 
      'CRITICAL': '严重'
    }

    const template = {
      subject: `系统告警 - ${alertData.ruleName} (${severityLabels[alertData.severity as keyof typeof severityLabels]})`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <div style="background: ${severityColors[alertData.severity as keyof typeof severityColors]}; color: white; padding: 20px; text-align: center;">
            <h2>🚨 系统告警</h2>
            <h3>${alertData.ruleName}</h3>
          </div>
          <div style="padding: 20px; background: #f8f9fa;">
            <div style="background: white; padding: 20px; border-radius: 5px; border-left: 4px solid ${severityColors[alertData.severity as keyof typeof severityColors]};">
              <h4>告警详情</h4>
              <table style="width: 100%; border-collapse: collapse;">
                <tr>
                  <td style="padding: 8px; border-bottom: 1px solid #eee;"><strong>规则名称：</strong></td>
                  <td style="padding: 8px; border-bottom: 1px solid #eee;">${alertData.ruleName}</td>
                </tr>
                <tr>
                  <td style="padding: 8px; border-bottom: 1px solid #eee;"><strong>严重程度：</strong></td>
                  <td style="padding: 8px; border-bottom: 1px solid #eee;">
                    <span style="color: ${severityColors[alertData.severity as keyof typeof severityColors]}; font-weight: bold;">
                      ${severityLabels[alertData.severity as keyof typeof severityLabels]}
                    </span>
                  </td>
                </tr>
                <tr>
                  <td style="padding: 8px; border-bottom: 1px solid #eee;"><strong>告警消息：</strong></td>
                  <td style="padding: 8px; border-bottom: 1px solid #eee;">${alertData.message}</td>
                </tr>
                <tr>
                  <td style="padding: 8px; border-bottom: 1px solid #eee;"><strong>当前值：</strong></td>
                  <td style="padding: 8px; border-bottom: 1px solid #eee;">${alertData.metricValue}</td>
                </tr>
                <tr>
                  <td style="padding: 8px; border-bottom: 1px solid #eee;"><strong>阈值：</strong></td>
                  <td style="padding: 8px; border-bottom: 1px solid #eee;">${alertData.threshold}</td>
                </tr>
                <tr>
                  <td style="padding: 8px;"><strong>触发时间：</strong></td>
                  <td style="padding: 8px;">${alertData.triggeredAt.toLocaleString('zh-CN')}</td>
                </tr>
              </table>
            </div>
          </div>
          <div style="padding: 20px; text-align: center; color: #666; font-size: 12px;">
            <p>请及时处理此告警。如有疑问，请联系系统管理员。</p>
            <p>此邮件由运维服务管理系统自动发送</p>
          </div>
        </div>
      `
    }

    return await this.sendEmail({
      to,
      subject: template.subject,
      html: template.html
    })
  }

  /**
   * 发送SLA违规邮件
   */
  async sendSLAViolationEmail(to: string, violationData: {
    ticketNumber: string
    customerName: string
    projectName: string
    violationType: string
    severity: string
    message: string
    expectedTime: Date
    createdAt: Date
    assignedTo: string
  }) {
    const violationTypeLabels = {
      'RESPONSE_TIME': '响应时间违规',
      'RESOLUTION_TIME': '解决时间违规',
      'ESCALATION': '告警升级'
    }

    const template = {
      subject: `SLA违规警告 - ${violationData.ticketNumber}`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <div style="background: #dc3545; color: white; padding: 20px; text-align: center;">
            <h2>⚠️ SLA违规警告</h2>
            <h3>工单 ${violationData.ticketNumber}</h3>
          </div>
          <div style="padding: 20px; background: #f8f9fa;">
            <div style="background: white; padding: 20px; border-radius: 5px; border-left: 4px solid #dc3545;">
              <h4>违规详情</h4>
              <table style="width: 100%; border-collapse: collapse;">
                <tr>
                  <td style="padding: 8px; border-bottom: 1px solid #eee;"><strong>工单号：</strong></td>
                  <td style="padding: 8px; border-bottom: 1px solid #eee;">${violationData.ticketNumber}</td>
                </tr>
                <tr>
                  <td style="padding: 8px; border-bottom: 1px solid #eee;"><strong>客户：</strong></td>
                  <td style="padding: 8px; border-bottom: 1px solid #eee;">${violationData.customerName}</td>
                </tr>
                <tr>
                  <td style="padding: 8px; border-bottom: 1px solid #eee;"><strong>项目：</strong></td>
                  <td style="padding: 8px; border-bottom: 1px solid #eee;">${violationData.projectName}</td>
                </tr>
                <tr>
                  <td style="padding: 8px; border-bottom: 1px solid #eee;"><strong>违规类型：</strong></td>
                  <td style="padding: 8px; border-bottom: 1px solid #eee;">${violationTypeLabels[violationData.violationType as keyof typeof violationTypeLabels]}</td>
                </tr>
                <tr>
                  <td style="padding: 8px; border-bottom: 1px solid #eee;"><strong>严重程度：</strong></td>
                  <td style="padding: 8px; border-bottom: 1px solid #eee;"><span style="color: #dc3545; font-weight: bold;">${violationData.severity}</span></td>
                </tr>
                <tr>
                  <td style="padding: 8px; border-bottom: 1px solid #eee;"><strong>详细说明：</strong></td>
                  <td style="padding: 8px; border-bottom: 1px solid #eee;">${violationData.message}</td>
                </tr>
                <tr>
                  <td style="padding: 8px; border-bottom: 1px solid #eee;"><strong>期望完成时间：</strong></td>
                  <td style="padding: 8px; border-bottom: 1px solid #eee;">${violationData.expectedTime.toLocaleString('zh-CN')}</td>
                </tr>
                <tr>
                  <td style="padding: 8px; border-bottom: 1px solid #eee;"><strong>工单创建时间：</strong></td>
                  <td style="padding: 8px; border-bottom: 1px solid #eee;">${violationData.createdAt.toLocaleString('zh-CN')}</td>
                </tr>
                <tr>
                  <td style="padding: 8px;"><strong>当前负责人：</strong></td>
                  <td style="padding: 8px;">${violationData.assignedTo}</td>
                </tr>
              </table>
            </div>
          </div>
          <div style="padding: 20px; text-align: center;">
            <p style="color: #dc3545; font-weight: bold; font-size: 16px;">请立即处理此SLA违规情况！</p>
            <p>如需升级或转移，请联系项目经理。</p>
          </div>
          <div style="padding: 20px; text-align: center; color: #666; font-size: 12px;">
            <p>此邮件由运维服务管理系统自动发送</p>
          </div>
        </div>
      `
    }

    return await this.sendEmail({
      to,
      subject: template.subject,
      html: template.html
    })
  }

  /**
   * 发送SLA升级邮件
   */
  async sendSLAEscalationEmail(to: string, escalationData: {
    ticketNumber: string
    customerName: string
    escalationLevel: number
    severity: string
    message: string
    originalCreatedAt: Date
  }) {
    const template = {
      subject: `SLA违规升级 - ${escalationData.ticketNumber} (Level ${escalationData.escalationLevel})`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <div style="background: #ff6b35; color: white; padding: 20px; text-align: center;">
            <h2>🔺 SLA违规升级</h2>
            <h3>工单 ${escalationData.ticketNumber}</h3>
            <h4>升级至 Level ${escalationData.escalationLevel}</h4>
          </div>
          <div style="padding: 20px; background: #f8f9fa;">
            <div style="background: white; padding: 20px; border-radius: 5px; border-left: 4px solid #ff6b35;">
              <h4>升级详情</h4>
              <table style="width: 100%; border-collapse: collapse;">
                <tr>
                  <td style="padding: 8px; border-bottom: 1px solid #eee;"><strong>工单号：</strong></td>
                  <td style="padding: 8px; border-bottom: 1px solid #eee;">${escalationData.ticketNumber}</td>
                </tr>
                <tr>
                  <td style="padding: 8px; border-bottom: 1px solid #eee;"><strong>客户：</strong></td>
                  <td style="padding: 8px; border-bottom: 1px solid #eee;">${escalationData.customerName}</td>
                </tr>
                <tr>
                  <td style="padding: 8px; border-bottom: 1px solid #eee;"><strong>升级级别：</strong></td>
                  <td style="padding: 8px; border-bottom: 1px solid #eee;"><span style="color: #ff6b35; font-weight: bold;">Level ${escalationData.escalationLevel}</span></td>
                </tr>
                <tr>
                  <td style="padding: 8px; border-bottom: 1px solid #eee;"><strong>当前严重程度：</strong></td>
                  <td style="padding: 8px; border-bottom: 1px solid #eee;"><span style="color: #dc3545; font-weight: bold;">${escalationData.severity}</span></td>
                </tr>
                <tr>
                  <td style="padding: 8px; border-bottom: 1px solid #eee;"><strong>升级原因：</strong></td>
                  <td style="padding: 8px; border-bottom: 1px solid #eee;">${escalationData.message}</td>
                </tr>
                <tr>
                  <td style="padding: 8px;"><strong>工单原始创建时间：</strong></td>
                  <td style="padding: 8px;">${escalationData.originalCreatedAt.toLocaleString('zh-CN')}</td>
                </tr>
              </table>
            </div>
          </div>
          <div style="padding: 20px; text-align: center;">
            <p style="color: #ff6b35; font-weight: bold; font-size: 18px;">此工单已升级，需要立即关注！</p>
            <p>作为管理层，请协调资源优先处理此工单。</p>
          </div>
          <div style="padding: 20px; text-align: center; color: #666; font-size: 12px;">
            <p>此邮件由运维服务管理系统自动发送</p>
          </div>
        </div>
      `
    }

    return await this.sendEmail({
      to,
      subject: template.subject,
      html: template.html
    })
  }
}

// 导出单例实例
export const emailService = new EmailService()
export default emailService
