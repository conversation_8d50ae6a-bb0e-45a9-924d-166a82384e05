/**
 * ActionExecutor 注册器
 * 负责自动发现和注册所有动作执行器
 */

import { actionExecutorManager } from './action-executor-manager.service';

/**
 * 执行器注册服务
 */
export class ActionExecutorRegistry {
  private static isRegistered = false;

  /**
   * 注册所有执行器
   */
  static async registerAllExecutors(): Promise<void> {
    if (ActionExecutorRegistry.isRegistered) {
      return;
    }

    try {
      console.log('🚀 开始注册所有ActionExecutor...');

      // 动态导入所有执行器
      const { HttpActionExecutor } = await import('./actions/http-action-executor');
      const { DatabaseActionExecutor } = await import('./actions/database-action-executor');
      const { NotificationActionExecutor } = await import('./actions/notification-action-executor');
      const { BusinessActionExecutor } = await import('./actions/business-action-executor');
      const { ConditionalActionExecutor } = await import('./actions/conditional-action-executor');
      
      // 创建并注册执行器实例
      const executors = [
        new HttpActionExecutor(),
        new DatabaseActionExecutor(),
        new NotificationActionExecutor(),
        new BusinessActionExecutor(),
        new ConditionalActionExecutor()
      ];

      for (const executor of executors) {
        actionExecutorManager.registerExecutor(executor);
      }

      console.log(`✅ 成功注册 ${executors.length} 个ActionExecutor`);
      
      // 初始化ActionExecutor管理器
      await actionExecutorManager.initialize();
      
      ActionExecutorRegistry.isRegistered = true;

    } catch (error) {
      console.error('❌ 注册ActionExecutor失败:', error);
      throw error;
    }
  }

  /**
   * 检查是否已注册
   */
  static isExecutorsRegistered(): boolean {
    return ActionExecutorRegistry.isRegistered;
  }
}

// 自动注册执行器（在模块加载时）
ActionExecutorRegistry.registerAllExecutors().catch(error => {
  console.error('自动注册执行器失败:', error);
});