import { prisma } from '@/config/database.config'
import { CacheService } from '@/config/redis.config'
import { ErrorFactory } from '@/utils/errors.util'

export interface CreateRoleInput {
  name: string
  description: string
  permissions: string[]
}

export interface UpdateRoleInput {
  name?: string
  description?: string
  permissions?: string[]
}

export interface RoleWithStats {
  id: string
  name: string
  description: string
  permissions: string[]
  userCount: number
  createdAt: Date
  updatedAt: Date
}

// 系统预定义权限
export const SYSTEM_PERMISSIONS = {
  // 管理员权限
  'admin:all': '系统管理员（所有权限）',
  
  // 用户管理
  'user:read': '查看用户',
  'user:write': '创建/编辑用户',
  'user:delete': '删除用户',
  'user:manage': '用户管理（包含所有用户相关权限）',
  
  // 角色管理
  'role:read': '查看角色',
  'role:write': '创建/编辑角色',
  'role:delete': '删除角色',
  'role:manage': '角色管理（包含所有角色相关权限）',
  
  // 客户管理
  'customer:read': '查看客户',
  'customer:write': '创建/编辑客户',
  'customer:delete': '删除客户',
  'customer:manage': '客户管理（包含所有客户相关权限）',
  
  // 项目档案
  'archive:read': '查看项目档案',
  'archive:write': '创建/编辑项目档案',
  'archive:delete': '删除项目档案',
  'archive:manage': '项目档案管理（包含所有档案相关权限）',
  
  // 服务工单
  'service:read': '查看服务工单',
  'service:write': '创建/编辑服务工单',
  'service:delete': '删除服务工单',
  'service:assign': '分配服务工单',
  'service:manage': '服务工单管理（包含所有工单相关权限）',
  
  // 配置管理
  'config:read': '查看配置',
  'config:write': '创建/编辑配置',
  'config:delete': '删除配置',
  'config:manage': '配置管理（包含所有配置相关权限）',
  
  // SLA管理
  'sla:read': '查看SLA',
  'sla:write': '创建/编辑SLA',
  'sla:delete': '删除SLA',
  'sla:manage': 'SLA管理（包含所有SLA相关权限）',
  
  // 通知管理
  'notification:read': '查看通知设置',
  'notification:send': '发送通知',
  'notification:manage': '通知管理（包含所有通知相关权限）',
  
  // 审计日志
  'audit:read': '查看审计日志',
  'audit:export': '导出审计日志',
  'audit:manage': '审计管理（包含所有审计相关权限）',
  
  // 文件管理
  'file:upload': '上传文件',
  'file:download': '下载文件',
  'file:delete': '删除文件',
  'file:manage': '文件管理（包含所有文件相关权限）',
  
  // 系统设置
  'system:read': '查看系统设置',
  'system:write': '修改系统设置',
  'system:backup': '系统备份',
  'system:monitor': '系统监控',
  'system:manage': '系统管理（包含所有系统相关权限）'
} as const

// 预定义角色模板
export const ROLE_TEMPLATES = {
  'super-admin': {
    name: 'super-admin',
    description: '超级管理员',
    permissions: ['admin:all']
  },
  'admin': {
    name: 'admin',
    description: '系统管理员',
    permissions: [
      'user:manage',
      'role:manage',
      'customer:manage',
      'archive:manage',
      'service:manage',
      'config:manage',
      'sla:manage',
      'notification:manage',
      'audit:manage',
      'file:manage',
      'system:manage'
    ]
  },
  'manager': {
    name: 'manager',
    description: '项目经理',
    permissions: [
      'customer:read',
      'customer:write',
      'archive:manage',
      'service:manage',
      'config:read',
      'sla:read',
      'notification:send',
      'audit:read',
      'file:manage'
    ]
  },
  'engineer': {
    name: 'engineer',
    description: '运维工程师',
    permissions: [
      'customer:read',
      'archive:read',
      'service:read',
      'service:write',
      'config:read',
      'config:write',
      'sla:read',
      'notification:send',
      'file:upload',
      'file:download'
    ]
  },
  'support': {
    name: 'support',
    description: '技术支持',
    permissions: [
      'customer:read',
      'archive:read',
      'service:read',
      'service:write',
      'sla:read',
      'notification:send',
      'file:upload',
      'file:download'
    ]
  },
  'viewer': {
    name: 'viewer',
    description: '只读用户',
    permissions: [
      'customer:read',
      'archive:read',
      'service:read',
      'config:read',
      'sla:read',
      'file:download'
    ]
  }
} as const

export class RoleService {
  /**
   * 创建角色
   */
  static async createRole(data: CreateRoleInput): Promise<RoleWithStats> {
    // 检查角色名唯一性
    const existingRole = await prisma.role.findUnique({
      where: { name: data.name }
    })

    if (existingRole) {
      throw ErrorFactory.conflict('角色名已存在')
    }

    // 验证权限
    this.validatePermissions(data.permissions)

    // 创建角色
    const role = await prisma.role.create({
      data: {
        name: data.name,
        description: data.description,
        permissions: JSON.stringify(data.permissions)
      }
    })

    return this.formatRoleWithStats(role, 0)
  }

  /**
   * 获取角色列表
   */
  static async getRoles(
    page: number = 1,
    limit: number = 20,
    search?: string
  ) {
    const skip = (page - 1) * limit
    
    const where: any = {}
    
    if (search) {
      where.OR = [
        { name: { contains: search } },
        { description: { contains: search } }
      ]
    }

    const [roles, total] = await Promise.all([
      prisma.role.findMany({
        where,
        skip,
        take: limit,
        include: {
          userRoles: {
            include: {
              user: true
            }
          }
        },
        orderBy: { createdAt: 'desc' }
      }),
      prisma.role.count({ where })
    ])

    return {
      roles: roles.map(role => this.formatRoleWithStats(role, role.userRoles.length)),
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    }
  }

  /**
   * 获取所有角色（用于下拉选择）
   */
  static async getAllRoles(): Promise<Array<{
    id: string
    name: string
    description: string | null
  }>> {
    const roles = await prisma.role.findMany({
      select: {
        id: true,
        name: true,
        description: true
      },
      orderBy: { name: 'asc' }
    })

    return roles
  }

  /**
   * 获取角色详情
   */
  static async getRoleById(id: string): Promise<RoleWithStats> {
    const role = await prisma.role.findUnique({
      where: { id },
      include: {
        userRoles: {
          include: {
            user: true
          }
        }
      }
    })

    if (!role) {
      throw ErrorFactory.notFound('角色')
    }

    return this.formatRoleWithStats(role, role.userRoles.length)
  }

  /**
   * 更新角色
   */
  static async updateRole(id: string, data: UpdateRoleInput): Promise<RoleWithStats> {
    // 检查角色是否存在
    const existingRole = await prisma.role.findUnique({
      where: { id }
    })

    if (!existingRole) {
      throw ErrorFactory.notFound('角色')
    }

    // 检查角色名唯一性
    if (data.name && data.name !== existingRole.name) {
      const nameConflict = await prisma.role.findUnique({
        where: { name: data.name }
      })

      if (nameConflict) {
        throw ErrorFactory.conflict('角色名已存在')
      }
    }

    // 验证权限
    if (data.permissions) {
      this.validatePermissions(data.permissions)
    }

    // 更新角色
    const updateData: any = { updatedAt: new Date() }
    
    if (data.name !== undefined) updateData.name = data.name
    if (data.description !== undefined) updateData.description = data.description
    if (data.permissions !== undefined) {
      updateData.permissions = JSON.stringify(data.permissions)
    }

    const role = await prisma.role.update({
      where: { id },
      data: updateData,
      include: {
        userRoles: {
          include: {
            user: true
          }
        }
      }
    })

    // 清除相关用户的缓存
    await this.clearUserCaches(id)

    return this.formatRoleWithStats(role, role.userRoles.length)
  }

  /**
   * 删除角色
   */
  static async deleteRole(id: string): Promise<void> {
    // 检查角色是否存在
    const role = await prisma.role.findUnique({
      where: { id },
      include: {
        userRoles: {
          include: {
            user: true
          }
        }
      }
    })

    if (!role) {
      throw ErrorFactory.notFound('角色')
    }

    // 检查是否有用户使用此角色
    if (role.userRoles.length > 0) {
      throw ErrorFactory.business(`该角色正被 ${role.userRoles.length} 个用户使用，无法删除`)
    }

    // 删除角色
    await prisma.role.delete({
      where: { id }
    })
  }

  /**
   * 获取系统权限列表
   */
  static getSystemPermissions(): Array<{
    key: string
    description: string
    category: string
  }> {
    return Object.entries(SYSTEM_PERMISSIONS).map(([key, description]) => {
      const category = key.split(':')[0] || 'other'
      return {
        key,
        description,
        category
      }
    })
  }

  /**
   * 获取权限分组
   */
  static getPermissionGroups(): Record<string, Array<{
    key: string
    description: string
  }>> {
    const groups: Record<string, Array<{ key: string, description: string }>> = {}

    Object.entries(SYSTEM_PERMISSIONS).forEach(([key, description]) => {
      const category = key.split(':')[0] || 'other'
      if (!groups[category]) {
        groups[category] = []
      }
      groups[category].push({ key, description })
    })

    return groups
  }

  /**
   * 获取角色模板
   */
  static getRoleTemplates(): Array<{
    name: string
    description: string
    permissions: string[]
  }> {
    return Object.values(ROLE_TEMPLATES).map(template => ({
      name: template.name,
      description: template.description,
      permissions: [...template.permissions]
    }))
  }

  /**
   * 从模板创建角色
   */
  static async createRoleFromTemplate(
    templateName: keyof typeof ROLE_TEMPLATES,
    customName?: string,
    customDescription?: string
  ): Promise<RoleWithStats> {
    const template = ROLE_TEMPLATES[templateName]
    
    if (!template) {
      throw ErrorFactory.notFound('角色模板')
    }

    return this.createRole({
      name: customName || template.name,
      description: customDescription || template.description,
      permissions: [...template.permissions]
    })
  }

  /**
   * 复制角色
   */
  static async duplicateRole(
    id: string,
    newName: string,
    newDescription?: string
  ): Promise<RoleWithStats> {
    const sourceRole = await this.getRoleById(id)
    
    return this.createRole({
      name: newName,
      description: newDescription || `${sourceRole.description} (副本)`,
      permissions: sourceRole.permissions
    })
  }

  /**
   * 获取角色统计信息
   */
  static async getRoleStats() {
    const [
      totalRoles,
      rolesWithUsers,
      permissionUsage,
      templateUsage
    ] = await Promise.all([
      prisma.role.count(),
      prisma.role.findMany({
        include: {
          userRoles: {
            include: {
              user: true
            }
          }
        }
      }),
      this.getPermissionUsageStats(),
      this.getTemplateUsageStats()
    ])

    return {
      totalRoles,
      rolesWithUsers: rolesWithUsers.filter(role => role.userRoles.length > 0).length,
      emptyRoles: rolesWithUsers.filter(role => role.userRoles.length === 0).length,
      roleUserDistribution: rolesWithUsers.map(role => ({
        roleName: role.name,
        userCount: role.userRoles.length
      })),
      permissionUsage,
      templateUsage
    }
  }

  /**
   * 获取权限模板使用统计
   */
  static async getTemplateUsageStats() {
    const templateUsage = await this.getTemplateUsageStatsInternal()
    return templateUsage
  }

  /**
   * 比较角色与模板的相似度
   */
  static async compareRoleWithTemplate(roleId: string, templateName: keyof typeof ROLE_TEMPLATES) {
    const role = await prisma.role.findUnique({
      where: { id: roleId }
    })

    if (!role) {
      throw ErrorFactory.notFound('角色')
    }

    const template = ROLE_TEMPLATES[templateName]
    if (!template) {
      throw ErrorFactory.notFound('模板')
    }

    const rolePermissions = typeof role.permissions === 'string' 
      ? JSON.parse(role.permissions) 
      : role.permissions
    
    const templatePermissions = template.permissions
    
    const commonPermissions = rolePermissions.filter((p: string) => 
      templatePermissions.includes(p)
    )
    
    const onlyInRole = rolePermissions.filter((p: string) => 
      !templatePermissions.includes(p)
    )
    
    const onlyInTemplate = templatePermissions.filter(p => 
      !rolePermissions.includes(p)
    )

    const similarity = commonPermissions.length / 
      Math.max(rolePermissions.length, templatePermissions.length)

    return {
      similarity: Math.round(similarity * 100),
      commonPermissions,
      onlyInRole,
      onlyInTemplate,
      recommendation: similarity > 0.8 ? 'HIGH' : similarity > 0.5 ? 'MEDIUM' : 'LOW'
    }
  }

  /**
   * 获取角色权限分析
   */
  static async getRolePermissionAnalysis(roleId: string) {
    const role = await prisma.role.findUnique({
      where: { id: roleId },
      include: {
        userRoles: {
          include: {
            user: {
              select: {
                username: true,
                fullName: true,
                status: true
              }
            }
          }
        }
      }
    })

    if (!role) {
      throw ErrorFactory.notFound('角色')
    }

    const rolePermissions = typeof role.permissions === 'string' 
      ? JSON.parse(role.permissions) 
      : role.permissions

    // 按模块分组权限
    const permissionGroups = this.groupPermissionsByModule(rolePermissions)
    
    // 查找最相似的模板
    const templateComparisons = await Promise.all(
      Object.keys(ROLE_TEMPLATES).map(async (templateName) => {
        const comparison = await this.compareRoleWithTemplate(
          roleId, 
          templateName as keyof typeof ROLE_TEMPLATES
        )
        return {
          templateName,
          templateDescription: ROLE_TEMPLATES[templateName as keyof typeof ROLE_TEMPLATES].description,
          ...comparison
        }
      })
    )

    // 按相似度排序
    templateComparisons.sort((a, b) => b.similarity - a.similarity)

    return {
      role: {
        id: role.id,
        name: role.name,
        description: role.description,
        permissions: rolePermissions,
        userCount: role.userRoles.length,
        users: role.userRoles.map(ur => ur.user)
      },
      permissionGroups,
      templateComparisons: templateComparisons.slice(0, 3), // 只返回前3个最相似的
      securityLevel: this.calculateSecurityLevel(rolePermissions),
      riskAssessment: this.assessPermissionRisks(rolePermissions)
    }
  }

  /**
   * 获取权限使用统计
   */
  private static async getPermissionUsageStats() {
    const roles = await prisma.role.findMany({
      include: {
        userRoles: {
          include: {
            user: true
          }
        }
      }
    })

    const permissionCount: Record<string, number> = {}

    roles.forEach(role => {
      const permissions = typeof role.permissions === 'string' 
        ? JSON.parse(role.permissions) 
        : role.permissions

      permissions.forEach((permission: string) => {
        permissionCount[permission] = (permissionCount[permission] || 0) + role.userRoles.length
      })
    })

    return Object.entries(permissionCount).map(([permission, userCount]) => ({
      permission,
      userCount,
      description: SYSTEM_PERMISSIONS[permission as keyof typeof SYSTEM_PERMISSIONS] || '未知权限'
    }))
  }

  /**
   * 验证权限列表
   */
  private static validatePermissions(permissions: string[]): void {
    const validPermissions = Object.keys(SYSTEM_PERMISSIONS)
    const invalidPermissions = permissions.filter(
      permission => !validPermissions.includes(permission)
    )

    if (invalidPermissions.length > 0) {
      throw ErrorFactory.validation(
        `无效的权限: ${invalidPermissions.join(', ')}`
      )
    }
  }

  /**
   * 清除用户缓存
   */
  private static async clearUserCaches(roleId: string): Promise<void> {
    const userRoles = await prisma.userRole.findMany({
      where: { roleId },
      select: { userId: true }
    })

    const cacheKeys = userRoles.map(userRole => `user:${userRole.userId}`)
    if (cacheKeys.length > 0) {
      for (const key of cacheKeys) {
        await CacheService.del(key)
      }
    }
  }

  /**
   * 获取模板使用统计（内部方法）
   */
  private static async getTemplateUsageStatsInternal() {
    const roles = await prisma.role.findMany({
      include: {
        userRoles: true
      }
    })

    const templateUsage = Object.keys(ROLE_TEMPLATES).map(templateName => {
      const template = ROLE_TEMPLATES[templateName as keyof typeof ROLE_TEMPLATES]
      
      // 查找与模板完全匹配的角色
      const matchingRoles = roles.filter(role => {
        const rolePermissions = typeof role.permissions === 'string' 
          ? JSON.parse(role.permissions) 
          : role.permissions
        
        // 检查权限是否完全匹配
        if (rolePermissions.length !== template.permissions.length) {
          return false
        }
        
        return template.permissions.every(p => rolePermissions.includes(p))
      })
      
      // 查找相似度高的角色（>80%）
      const similarRoles = roles.filter(role => {
        const rolePermissions = typeof role.permissions === 'string' 
          ? JSON.parse(role.permissions) 
          : role.permissions
        
        const commonPermissions = rolePermissions.filter((p: string) => 
          template.permissions.includes(p)
        )
        
        const similarity = commonPermissions.length / 
          Math.max(rolePermissions.length, template.permissions.length)
        
        return similarity > 0.8 && !matchingRoles.includes(role)
      })
      
      const totalUsers = [
        ...matchingRoles, 
        ...similarRoles
      ].reduce((sum, role) => sum + role.userRoles.length, 0)
      
      return {
        templateName,
        templateDescription: template.description,
        exactMatches: matchingRoles.length,
        similarMatches: similarRoles.length,
        totalRoles: matchingRoles.length + similarRoles.length,
        totalUsers,
        utilizationRate: roles.length > 0 ? 
          Math.round((matchingRoles.length + similarRoles.length) / roles.length * 100) : 0
      }
    })
    
    return templateUsage.sort((a, b) => b.totalUsers - a.totalUsers)
  }

  /**
   * 按模块分组权限
   */
  private static groupPermissionsByModule(permissions: string[]) {
    const groups: Record<string, string[]> = {}
    
    permissions.forEach(permission => {
      const [module] = permission.split(':')
      if (!groups[module]) {
        groups[module] = []
      }
      groups[module].push(permission)
    })

    return Object.entries(groups).map(([module, perms]) => ({
      module,
      permissions: perms,
      count: perms.length
    }))
  }

  /**
   * 计算安全等级
   */
  private static calculateSecurityLevel(permissions: string[]): 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL' {
    if (permissions.includes('admin:all')) {
      return 'CRITICAL'
    }
    
    const highRiskPermissions = [
      'user:delete', 'role:delete', 'system:write', 'system:backup'
    ]
    
    const hasHighRisk = permissions.some(p => highRiskPermissions.includes(p))
    if (hasHighRisk) {
      return 'HIGH'
    }
    
    const writePermissions = permissions.filter(p => p.includes(':write') || p.includes(':manage'))
    if (writePermissions.length > 5) {
      return 'MEDIUM'
    }
    
    return 'LOW'
  }

  /**
   * 评估权限风险
   */
  private static assessPermissionRisks(permissions: string[]) {
    const risks = []
    
    if (permissions.includes('admin:all')) {
      risks.push({
        level: 'CRITICAL',
        message: '拥有系统最高权限，应谨慎分配',
        recommendation: '建议仅给最高管理员分配此权限'
      })
    }
    
    const deletePermissions = permissions.filter(p => p.includes(':delete'))
    if (deletePermissions.length > 3) {
      risks.push({
        level: 'HIGH',
        message: `拥有较多删除权限: ${deletePermissions.join(', ')}`,
        recommendation: '建议将删除权限分配给经验丰富的管理员'
      })
    }
    
    if (permissions.includes('system:backup')) {
      risks.push({
        level: 'MEDIUM',
        message: '拥有系统备份权限',
        recommendation: '需要确保用户能够安全处理备份文件'
      })
    }
    
    return risks
  }

  /**
   * 批量操作角色
   */
  static async batchOperation(
    roleIds: string[],
    operation: 'enable' | 'disable' | 'delete',
    force: boolean = false
  ) {
    const results = {
      successCount: 0,
      errors: [] as Array<{ roleId: string, error: string }>
    }

    for (const roleId of roleIds) {
      try {
        switch (operation) {
          case 'enable':
            // 启用角色（如果有isActive字段）
            await prisma.role.update({
              where: { id: roleId },
              data: { updatedAt: new Date() }
            })
            break
            
          case 'disable':
            // 禁用角色（如果有isActive字段）
            await prisma.role.update({
              where: { id: roleId },
              data: { updatedAt: new Date() }
            })
            break
            
          case 'delete':
            if (!force) {
              // 检查是否有用户使用此角色
              const userCount = await prisma.userRole.count({
                where: { roleId }
              })
              
              if (userCount > 0) {
                results.errors.push({
                  roleId,
                  error: `该角色正被 ${userCount} 个用户使用，无法删除`
                })
                continue
              }
            } else {
              // 强制删除：先删除用户角色关联
              await prisma.userRole.deleteMany({
                where: { roleId }
              })
            }
            
            await prisma.role.delete({
              where: { id: roleId }
            })
            break
        }
        
        results.successCount++
        
        // 清除相关缓存
        await this.clearUserCaches(roleId)
        
      } catch (error) {
        console.error(`Batch operation failed for role ${roleId}:`, error)
        results.errors.push({
          roleId,
          error: error instanceof Error ? error.message : '操作失败'
        })
      }
    }

    return results
  }

  /**
   * 导出角色数据
   */
  static async exportRoles(
    format: 'excel' | 'csv',
    roleIds?: string[],
    includeUsers: boolean = false
  ): Promise<Buffer> {
    const where = roleIds ? { id: { in: roleIds } } : {}
    
    const roles = await prisma.role.findMany({
      where,
      include: {
        userRoles: includeUsers ? {
          include: {
            user: {
              select: {
                username: true,
                fullName: true,
                email: true
              }
            }
          }
        } : false
      },
      orderBy: { createdAt: 'desc' }
    })

    // 转换数据格式
    const exportData = roles.map(role => {
      const permissions = typeof role.permissions === 'string' 
        ? JSON.parse(role.permissions) 
        : role.permissions
      
      const baseData = {
        id: role.id,
        name: role.name,
        description: role.description,
        permissions: permissions.join(', '),
        permissionCount: permissions.length,
        userCount: role.userRoles?.length || 0,
        createdAt: role.createdAt.toISOString(),
        updatedAt: role.updatedAt.toISOString()
      }

      if (includeUsers && role.userRoles) {
        return {
          ...baseData,
          users: role.userRoles.map(ur => 
            `${ur.user.fullName}(${ur.user.username})`
          ).join(', ')
        }
      }

      return baseData
    })

    if (format === 'csv') {
      return this.generateCSV(exportData)
    } else {
      return this.generateExcel(exportData)
    }
  }

  /**
   * 导入角色数据
   */
  static async importRoles(fileBuffer: Buffer, mimeType: string) {
    let rolesData: any[]

    // 解析文件
    if (mimeType.includes('csv') || mimeType.includes('text/plain')) {
      rolesData = this.parseCSV(fileBuffer)
    } else if (mimeType.includes('excel') || mimeType.includes('spreadsheet')) {
      rolesData = this.parseExcel(fileBuffer)
    } else {
      throw ErrorFactory.validation('不支持的文件格式，请上传 CSV 或 Excel 文件')
    }

    const results = {
      successCount: 0,
      errors: [] as Array<{ row: number, error: string, data?: any }>
    }

    // 批量创建角色
    for (let i = 0; i < rolesData.length; i++) {
      const rowData = rolesData[i]
      
      try {
        // 验证数据
        const permissions = typeof rowData.permissions === 'string'
          ? rowData.permissions.split(',').map((p: string) => p.trim()).filter(Boolean)
          : []

        const roleData = {
          name: rowData.name?.trim(),
          description: rowData.description?.trim(),
          permissions
        }

        // 基础验证
        if (!roleData.name) {
          throw new Error('角色名不能为空')
        }
        
        if (!roleData.description) {
          throw new Error('角色描述不能为空')
        }
        
        if (!roleData.permissions.length) {
          throw new Error('权限列表不能为空')
        }

        // 验证权限
        this.validatePermissions(roleData.permissions)

        // 检查角色名唯一性
        const existingRole = await prisma.role.findUnique({
          where: { name: roleData.name }
        })

        if (existingRole) {
          throw new Error(`角色名 "${roleData.name}" 已存在`)
        }

        // 创建角色
        await prisma.role.create({
          data: {
            name: roleData.name,
            description: roleData.description,
            permissions: JSON.stringify(roleData.permissions)
          }
        })

        results.successCount++
        
      } catch (error) {
        console.error(`Import failed for row ${i + 1}:`, error)
        results.errors.push({
          row: i + 1,
          error: error instanceof Error ? error.message : '导入失败',
          data: rowData
        })
      }
    }

    return results
  }

  /**
   * 同步角色权限（移除无效权限）
   */
  static async syncRolePermissions() {
    const validPermissions = Object.keys(SYSTEM_PERMISSIONS)
    const roles = await prisma.role.findMany()
    
    const result = {
      syncedRoles: 0,
      updatedPermissions: [] as string[],
      removedPermissions: [] as string[]
    }

    for (const role of roles) {
      const permissions = typeof role.permissions === 'string' 
        ? JSON.parse(role.permissions) 
        : role.permissions
      
      const validRolePermissions = permissions.filter((p: string) => 
        validPermissions.includes(p)
      )
      
      const removedPerms = permissions.filter((p: string) => 
        !validPermissions.includes(p)
      )

      if (removedPerms.length > 0) {
        await prisma.role.update({
          where: { id: role.id },
          data: {
            permissions: JSON.stringify(validRolePermissions),
            updatedAt: new Date()
          }
        })
        
        result.syncedRoles++
        result.removedPermissions.push(...removedPerms)
        
        // 清除相关用户缓存
        await this.clearUserCaches(role.id)
      }
    }

    return result
  }

  /**
   * 生成CSV文件
   */
  private static generateCSV(data: any[]): Buffer {
    if (!data.length) {
      return Buffer.from('没有数据可导出')
    }

    const headers = Object.keys(data[0])
    const csvContent = [
      headers.join(','),
      ...data.map(row => 
        headers.map(header => {
          const value = row[header] || ''
          // 处理包含逗号的字段
          return value.toString().includes(',') 
            ? `"${value.toString().replace(/"/g, '""')}"`
            : value
        }).join(',')
      )
    ].join('\n')

    return Buffer.from('\ufeff' + csvContent, 'utf-8') // 添加BOM以支持中文
  }

  /**
   * 生成Excel文件
   */
  private static generateExcel(data: any[]): Buffer {
    // 这里需要使用 xlsx 库，简化实现返回CSV格式
    // 实际项目中应该安装并使用 xlsx 库来生成真正的Excel文件
    return this.generateCSV(data)
  }

  /**
   * 解析CSV文件
   */
  private static parseCSV(buffer: Buffer): any[] {
    const content = buffer.toString('utf-8').replace(/^\ufeff/, '') // 移除BOM
    const lines = content.split('\n').filter(line => line.trim())
    
    if (lines.length < 2) {
      throw ErrorFactory.validation('CSV文件至少需要包含表头和一行数据')
    }

    const headers = lines[0].split(',').map(h => h.trim())
    const rows = lines.slice(1)

    return rows.map(line => {
      const values = this.parseCSVLine(line)
      const obj: any = {}
      
      headers.forEach((header, index) => {
        obj[header] = values[index] || ''
      })
      
      return obj
    })
  }

  /**
   * 解析CSV行（处理引号）
   */
  private static parseCSVLine(line: string): string[] {
    const result = []
    let current = ''
    let inQuotes = false
    
    for (let i = 0; i < line.length; i++) {
      const char = line[i]
      
      if (char === '"') {
        if (inQuotes && line[i + 1] === '"') {
          current += '"'
          i++ // 跳过下一个引号
        } else {
          inQuotes = !inQuotes
        }
      } else if (char === ',' && !inQuotes) {
        result.push(current.trim())
        current = ''
      } else {
        current += char
      }
    }
    
    result.push(current.trim())
    return result
  }

  /**
   * 解析Excel文件
   */
  private static parseExcel(buffer: Buffer): any[] {
    // 这里需要使用 xlsx 库，简化实现假设是CSV格式
    // 实际项目中应该安装并使用 xlsx 库来解析真正的Excel文件
    return this.parseCSV(buffer)
  }

  /**
   * 格式化角色数据
   */
  private static formatRoleWithStats(role: any, userCount: number): RoleWithStats {
    return {
      id: role.id,
      name: role.name,
      description: role.description,
      permissions: typeof role.permissions === 'string' 
        ? JSON.parse(role.permissions) 
        : role.permissions,
      userCount,
      createdAt: role.createdAt,
      updatedAt: role.updatedAt
    }
  }
}