import si from 'systeminformation'
import osUtils from 'node-os-utils'
import { prisma } from '@/config/database.config'
import { CacheService } from '@/config/redis.config'
import { RealtimeService } from './realtime.service'
import { AlertEngineService } from './alert-engine.service'
import { SLAViolationAlertService } from './sla-violation-alert.service'
import {
  SystemMetrics,
  ServiceHealth,
  DatabaseHealth,
  RedisHealth,
  ApplicationHealth,
  IntelligentAnalysis,
  MonitoringDashboard,
  HealthSummary,
  ActiveAlert,
  MetricsQuery,
  MetricsResponse,
  PerformanceBenchmark,
  ResourceAlert,
  AnalysisInsight,
  PerformancePrediction,
  Recommendation,
  AnomalyDetection,
  TrendAnalysis,
  ProcessInfo,
  NetworkInterface
} from '@/types/monitor.types'
import { AlertSeverity } from '@prisma/client'
import * as os from 'os'

export class MonitorService {
  private static instance: MonitorService
  private metricsHistory: SystemMetrics[] = []
  private readonly MAX_HISTORY_LENGTH = 1000
  private readonly CACHE_TTL = 30 // 30秒
  private readonly DASHBOARD_CACHE_TTL = 10 // 10秒快照缓存
  private alertEngine: AlertEngineService
  private slaViolationService: SLAViolationAlertService

  private constructor() {
    this.alertEngine = AlertEngineService.getInstance()
    this.slaViolationService = SLAViolationAlertService.getInstance()
    this.startMetricsCollection()
  }

  public static getInstance(): MonitorService {
    if (!MonitorService.instance) {
      MonitorService.instance = new MonitorService()
    }
    return MonitorService.instance
  }

  // 为重度调用加超时保护
  private async withTimeout<T>(promise: Promise<T>, timeoutMs: number, fallback: T): Promise<T> {
    return new Promise<T>((resolve) => {
      let settled = false
      const timer = setTimeout(() => {
        if (!settled) {
          settled = true
          resolve(fallback)
        }
      }, timeoutMs)
      promise
        .then((val) => {
          if (!settled) {
            settled = true
            clearTimeout(timer)
            resolve(val)
          }
        })
        .catch(() => {
          if (!settled) {
            settled = true
            clearTimeout(timer)
            resolve(fallback)
          }
        })
    })
  }

  // 读取/生成仪表盘数据并缓存，支持轻量/完整
  public async getDashboardCached(options?: { detail?: 'lite' | 'full'; sections?: string[] }): Promise<MonitoringDashboard> {
    const detail = options?.detail === 'full' ? 'full' : 'lite'
    const sectionsKey = (options?.sections || []).slice().sort().join(',')
    const cacheKey = `monitor:dashboard:snapshot:${detail}:${sectionsKey}`

    const cached = await CacheService.get(cacheKey)
    if (cached) {
      return JSON.parse(cached)
    }

    // 基础指标优先，重度项加超时
    const [metrics, dbHealth, redisHealth, appHealth] = await Promise.all([
      this.collectSystemMetrics(),
      this.withTimeout(this.getDatabaseHealth(), 200, {
        name: 'MySQL Database', status: 'warning', responseTime: 0, lastCheck: new Date().toISOString(), uptime: 'unknown', details: 'timeout',
        connections: { active: 0, idle: 0, max: 0 }, performance: { queryCount: 0, slowQueries: 0, avgResponseTime: 0 }, statistics: { totalTables: 0, totalRecords: 0, dbSize: 0 }
      } as DatabaseHealth),
      this.withTimeout(this.getRedisHealth(), 150, {
        name: 'Redis Cache', status: 'warning', responseTime: 0, lastCheck: new Date().toISOString(), uptime: 'unknown', details: 'timeout',
        info: { version: 'unknown', mode: 'unknown', role: 'unknown' }, memory: { used: 0, peak: 0, fragmentation: 0 }, stats: { keyCount: 0, hitRate: 0, operationsPerSec: 0 }
      } as RedisHealth),
      this.getApplicationHealth()
    ])

    // 仅 full 时才计算智能分析（已有 300s 缓存，再加超时）
    const analysis: IntelligentAnalysis | undefined = detail === 'full'
      ? await this.withTimeout(this.performIntelligentAnalysis(), 300, {
          score: 0,
          level: 'good',
          insights: [],
          predictions: [],
          recommendations: [],
          trends: [],
          anomalies: []
        })
      : undefined

    const services: ServiceHealth[] = [dbHealth, redisHealth, appHealth]
    const summary = this.calculateHealthSummary(metrics, services, analysis || {
      score: 90,
      level: 'good',
      insights: [],
      predictions: [],
      recommendations: [],
      trends: [],
      anomalies: []
    })

    const alerts = await this.withTimeout(this.getActiveAlerts(), 200, [])

    const snapshot: MonitoringDashboard = {
      summary,
      metrics,
      services,
      analysis: analysis || {
        score: summary.score,
        level: summary.overall === 'healthy' ? 'good' : summary.overall,
        insights: [],
        predictions: [],
        recommendations: [],
        trends: [],
        anomalies: []
      },
      alerts,
      lastUpdated: new Date().toISOString()
    }

    await CacheService.setex(cacheKey, this.DASHBOARD_CACHE_TTL, JSON.stringify(snapshot))
    return snapshot
  }

  /**
   * 启动指标收集
   */
  private startMetricsCollection() {
    setInterval(async () => {
      try {
        const metrics = await this.collectSystemMetrics()
        this.storeMetrics(metrics)
        await this.checkThresholds(metrics)
        
        // 智能告警引擎评估
        try {
          const triggeredAlerts = await this.alertEngine.evaluateMetrics(metrics)
          if (triggeredAlerts.length > 0) {
            console.log(`📊 监控指标评估触发了 ${triggeredAlerts.length} 个告警`)
          }
        } catch (alertError) {
          console.error('告警引擎评估失败:', alertError)
        }
        
        // 实时推送系统监控数据
        await this.pushRealtimeMetrics(metrics)
      } catch (error) {
        console.error('Error collecting metrics:', error)
      }
    }, 30000) // 每30秒收集一次
    
    // SLA违规检查 (每分钟检查一次)
    setInterval(async () => {
      try {
        await this.slaViolationService.triggerSLACheck()
      } catch (error) {
        console.error('SLA违规检查失败:', error)
      }
    }, 60000)
  }
  
  /**
   * 推送实时监控数据
   */
  private async pushRealtimeMetrics(metrics: SystemMetrics) {
    try {
      const realtimeService = RealtimeService.getInstance()
      
      // 推送基础监控指标
      await realtimeService.pushSystemMetrics({
        cpu: metrics.cpu.usage,
        memory: metrics.memory.usage,
        disk: metrics.disk.usage,
        network: Math.min(100, (metrics.network.totalRxSpeed + metrics.network.totalTxSpeed) / 1024 / 1024 * 100) // 转换为百分比
      })
    } catch (error) {
      console.error('Error pushing realtime metrics:', error)
    }
  }
  
  /**
   * 推送系统状态变更
   */
  private async pushSystemStatusChange(status: {
    overall: 'healthy' | 'warning' | 'critical'
    services: Array<{
      name: string
      status: 'healthy' | 'warning' | 'critical' | 'down'
      responseTime: number
      message?: string
    }>
  }) {
    try {
      const realtimeService = RealtimeService.getInstance()
      await realtimeService.pushSystemStatusChange(status)
    } catch (error) {
      console.error('Error pushing system status change:', error)
    }
  }
  
  /**
   * 推送系统告警
   */
  private async pushSystemAlert(alert: {
    title: string
    message: string
    severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
    component: string
  }) {
    try {
      // 可选：将告警行为以“审计事件”写入 system_events（默认关闭，避免混淆日志与告警）
      const AUDIT_ALERTS_TO_EVENTS = (process.env['MONITOR_AUDIT_ALERTS_TO_EVENTS'] || 'false') === 'true'
      if (AUDIT_ALERTS_TO_EVENTS) {
        const severityToLevelMap: Record<string, string> = {
          LOW: 'info',
          MEDIUM: 'warn',
          HIGH: 'error',
          CRITICAL: 'critical'
        }
        const level = severityToLevelMap[alert.severity] || 'info'

        await prisma.systemEvent.create({
          data: {
            level,
            type: 'alert_audit',
            message: `Alert triggered: ${alert.title}`,
            details: {
              component: alert.component,
              severity: alert.severity
            } as any
          }
        }).catch(() => undefined)
      }

      const realtimeService = RealtimeService.getInstance()
      await realtimeService.pushSystemAlert({
        id: `alert_${Date.now()}`,
        ...alert,
        status: 'PENDING' as const
      })
    } catch (error) {
      console.error('Error pushing system alert:', error)
    }
  }

  /**
   * 收集系统指标
   */
  public async collectSystemMetrics(): Promise<SystemMetrics> {
    const cacheKey = 'monitor:system:metrics'
    const ENABLE_TOP_PROCESSES = (process.env['MONITOR_ENABLE_TOP_PROCESSES'] || 'false') === 'true'
    const ENABLE_SMART_ENHANCEMENT = (process.env['MONITOR_ENABLE_SMART'] || 'false') === 'true'
    
    // 尝试从缓存获取
    const cached = await CacheService.get(cacheKey)
    if (cached) {
      return JSON.parse(cached)
    }

    try {
      const [
        cpuInfo,
        memInfo,
        diskInfo,
        networkInfo,
        maybeProcessInfo,
        currentLoad,
        networkStats
      ] = await Promise.all([
        si.cpu(),
        si.mem(),
        si.fsSize(),
        si.networkInterfaces(),
        ENABLE_TOP_PROCESSES ? si.processes() : Promise.resolve(null as any),
        si.currentLoad(),
        si.networkStats()
      ])

      // 获取负载平均值
      const loadAvg = await si.currentLoad()

      // 处理CPU信息
      const cpu = {
        usage: Math.round(currentLoad.currentLoad),
        cores: cpuInfo.cores,
        loadAverage: [currentLoad.avgLoad || 0, currentLoad.currentLoad || 0, currentLoad.fullLoad || 0],
        processes: ENABLE_TOP_PROCESSES && maybeProcessInfo?.list
          ? this.processTopProcesses(maybeProcessInfo.list || [])
          : []
      }

      // 处理内存信息
      const memory = {
        total: Math.round(memInfo.total / 1024 / 1024 / 1024 * 100) / 100, // GB
        used: Math.round(memInfo.used / 1024 / 1024 / 1024 * 100) / 100, // GB
        free: Math.round(memInfo.free / 1024 / 1024 / 1024 * 100) / 100, // GB
        available: Math.round(memInfo.available / 1024 / 1024 / 1024 * 100) / 100, // GB
        usage: Math.round((memInfo.used / memInfo.total) * 100 * 100) / 100, // 保留两位小数
        cached: Math.round((memInfo.cached || 0) / 1024 / 1024 / 1024 * 100) / 100, // GB
        buffers: Math.round((memInfo.buffers || 0) / 1024 / 1024 / 1024 * 100) / 100 // GB
      }

      // 处理磁盘信息 - 优先使用 systeminformation 第三方库
      let totalSize = 0
      let totalUsed = 0
      let totalAvailable = 0
      let usagePercent = 0
      
      try {
        // 优先使用 systeminformation 库，提供跨平台兼容性
        
        // 改进的主分区查找策略
        let primaryDisk = null
        let strategy = ''
        
        // 策略1: macOS 特殊处理 - 优先查找 Data 分区 (主要用户数据分区)
        if (process.platform === 'darwin') {
          primaryDisk = diskInfo.find((disk: any) => 
            disk.mount === '/System/Volumes/Data' && disk.size > 0
          )
          if (primaryDisk) strategy = 'macOS Data分区匹配'
        }
        
        // 策略2: 查找根目录 '/' (Unix系统) - 仅当没有找到Data分区时
        if (!primaryDisk) {
          primaryDisk = diskInfo.find((disk: any) => disk.mount === '/' && disk.size > 0)
          if (primaryDisk) strategy = '根目录精确匹配'
        }
        
        // 策略3: 查找 C: 盘 (Windows系统)
        if (!primaryDisk) {
          primaryDisk = diskInfo.find((disk: any) => 
            (disk.mount === 'C:' || disk.fs === 'C:' || disk.mount === 'C:\\') && disk.size > 0
          )
          if (primaryDisk) strategy = 'Windows C盘匹配'
        }
        
        // 策略4: 查找包含 '/' 的主要分区 (其他APFS分区)
        if (!primaryDisk) {
          primaryDisk = diskInfo.find((disk: any) => 
            disk.mount && disk.mount.includes('/') && 
            !disk.mount.includes('/dev') && 
            !disk.mount.includes('/tmp') &&
            !disk.mount.includes('/var') &&
            !disk.mount.includes('/System/Volumes') &&
            disk.size > 0
          )
          if (primaryDisk) strategy = 'macOS其他分区匹配'
        }
        
        // 策略5: 查找最大的有效分区 (排除系统虚拟分区)
        if (!primaryDisk) {
          const validDisks = diskInfo.filter((disk: any) => 
            disk.size > 0 && 
            !disk.fs.includes('tmpfs') && 
            !disk.fs.includes('devfs') &&
            !disk.fs.includes('proc') &&
            !disk.fs.includes('sys') &&
            !disk.mount?.includes('/snap') &&
            disk.type !== 'squashfs'
          )
          
          primaryDisk = validDisks.sort((a: any, b: any) => b.size - a.size)[0]
          if (primaryDisk) strategy = '最大有效分区'
        }
        
        // 策略6: 使用第一个有大小的分区
        if (!primaryDisk) {
          primaryDisk = diskInfo.find((disk: any) => disk.size > 0)
          if (primaryDisk) strategy = '首个有效分区'
        }
        
        if (primaryDisk && primaryDisk.size > 0) {
          totalSize = primaryDisk.size
          totalUsed = primaryDisk.used
          totalAvailable = primaryDisk.available || (totalSize - totalUsed)
          
          // 修正策略：在 macOS 上优先使用系统命令获取使用率
          let systemUsagePercent = null
          
        if (ENABLE_SMART_ENHANCEMENT && process.platform === 'darwin') {
            try {
              const { execSync } = require('child_process')
              
              // 根据选中的分区查询相应的 df 命令
              let dfCommand = 'df -h /'
              if (primaryDisk.mount === '/System/Volumes/Data') {
                dfCommand = 'df -h /System/Volumes/Data'
              } else if (primaryDisk.mount && primaryDisk.mount !== '/') {
                dfCommand = `df -h "${primaryDisk.mount}"`
              }
              
              const dfOutput = execSync(dfCommand, { encoding: 'utf8', timeout: 5000 })
              const lines = dfOutput.trim().split('\n')
              
              if (lines.length > 1) {
                const parts = lines[1].split(/\s+/)
                if (parts.length >= 5) {
                  // 解析 df 的使用率 (格式: "87%")
                  const dfUsage = parts[4].replace('%', '')
                  systemUsagePercent = parseInt(dfUsage)
                }
              }
            } catch (error) {
              console.log('无法获取 df 使用率:', error.message)
            }
          }
          
          // 使用率优先级：系统命令 > 计算值 > 库提供值
          if (systemUsagePercent !== null && systemUsagePercent > 0) {
            usagePercent = systemUsagePercent
          } else if (totalUsed > 0 && totalSize > 0) {
            usagePercent = Math.round((totalUsed / totalSize) * 100 * 100) / 100
          } else if (primaryDisk.use && primaryDisk.use > 0) {
            usagePercent = Math.round(primaryDisk.use * 100) / 100
          } else {
            usagePercent = 0
          }
          
        } else {
          throw new Error('没有找到任何有效的磁盘分区')
        }
        
        // macOS 系统特殊处理：使用系统命令获取准确的总容量
        if (ENABLE_SMART_ENHANCEMENT && process.platform === 'darwin' && totalSize > 0) {
          try {
            const { execSync } = require('child_process')
            
            // 方法1: 使用对应分区的 df 命令获取精确的容量信息
            let dfCommand = 'df -h /'
            if (primaryDisk.mount === '/System/Volumes/Data') {
              dfCommand = 'df -h /System/Volumes/Data'
            } else if (primaryDisk.mount && primaryDisk.mount !== '/') {
              dfCommand = `df -h "${primaryDisk.mount}"`
            }
            
            const dfOutput = execSync(dfCommand, { encoding: 'utf8', timeout: 5000 })
            const lines = dfOutput.trim().split('\n')
            
            if (lines.length > 1) {
              const parts = lines[1].split(/\s+/)
              if (parts.length >= 4) {
                // 解析 df 输出的容量信息 (格式: "926Gi")
                const totalStr = parts[1]
                const usedStr = parts[2] 
                const availStr = parts[3]
                
                // 转换为字节
                const parseSize = (sizeStr) => {
                  const match = sizeStr.match(/^(\d+\.?\d*)([KMGT]?)i?$/)
                  if (match) {
                    const value = parseFloat(match[1])
                    const unit = match[2]
                    
                    switch (unit) {
                      case 'T': return value * 1024 * 1024 * 1024 * 1024
                      case 'G': return value * 1024 * 1024 * 1024
                      case 'M': return value * 1024 * 1024
                      case 'K': return value * 1024
                      default: return value
                    }
                  }
                  return 0
                }
                
                const dfTotalSize = parseSize(totalStr)
                const dfUsedSize = parseSize(usedStr)
                const dfAvailSize = parseSize(availStr)
                
                if (dfTotalSize > 0) {
                  console.log(`🍎 macOS df 容量: 总计=${Math.round(dfTotalSize/1024/1024/1024*100)/100}GB, 已用=${Math.round(dfUsedSize/1024/1024/1024*100)/100}GB, 可用=${Math.round(dfAvailSize/1024/1024/1024*100)/100}GB`)
                  
                  // 使用 df 的精确数据替换
                  totalSize = dfTotalSize
                  totalUsed = dfUsedSize
                  totalAvailable = dfAvailSize
                  
                  // 尝试获取更准确的可用空间 (检查是否有额外的保留空间)
                  try {
                    const statvfsOutput = execSync(`python3 -c "import os; s=os.statvfs('${primaryDisk.mount || '/'}'); print(f'{s.f_bavail * s.f_frsize}')"`, 
                      { encoding: 'utf8', timeout: 3000 })
                    const actualAvailable = parseInt(statvfsOutput.trim())
                    
                    if (actualAvailable > dfAvailSize && actualAvailable < dfTotalSize) {
                      console.log(`🔍 发现更多可用空间: df=${Math.round(dfAvailSize/1024/1024/1024*100)/100}GB, 实际=${Math.round(actualAvailable/1024/1024/1024*100)/100}GB`)
                      totalAvailable = actualAvailable
                    }
                  } catch (statvfsError) {
                    // statvfs 检查失败，继续使用 df 数据
                  }
                  
                  // 重新计算使用率（如果系统使用率不可用）
                  if (systemUsagePercent === null) {
                    usagePercent = Math.round((totalUsed / totalSize) * 100 * 100) / 100
                  }
                }
              }
            }
            
            // 方法2: 备用 - APFS 容器检测（如果 df 数据不可用）
            if (ENABLE_SMART_ENHANCEMENT && totalSize === primaryDisk.size) {
              const diskutilOutput = execSync('diskutil list', { encoding: 'utf8', timeout: 5000 })
              const apfsMatch = diskutilOutput.match(/APFS Container.*?\+(\d+\.?\d*)\s*(GB|TB)/i)
              
              if (apfsMatch) {
                const containerSize = parseFloat(apfsMatch[1])
                const unit = apfsMatch[2].toUpperCase()
                const containerSizeBytes = unit === 'TB' 
                  ? containerSize * 1024 * 1024 * 1024 * 1024
                  : containerSize * 1024 * 1024 * 1024
                
                if (containerSizeBytes > totalSize * 1.05) {
                  console.log(`🍎 APFS 容器优化: ${Math.round(containerSizeBytes/1024/1024/1024*100)/100}GB -> ${Math.round(totalSize/1024/1024/1024*100)/100}GB`)
                  totalSize = containerSizeBytes
                  totalAvailable = Math.max(0, totalSize - totalUsed)
                  if (systemUsagePercent === null) {
                    usagePercent = Math.round((totalUsed / totalSize) * 100 * 100) / 100
                  }
                }
              }
            }
            
          } catch (apfsError) {
            console.log('macOS 磁盘优化失败，使用原始数据:', apfsError.message)
          }
        }
        
      } catch (error) {
        console.error('systeminformation 库获取磁盘信息失败，使用系统命令备用方案:', error.message)
        
        // 备用方案：使用系统命令
        try {
          const { execSync } = require('child_process')
          let command = 'df -k /' // 默认 Unix 命令
          
          if (process.platform === 'win32') {
            command = 'powershell "Get-WmiObject -Class Win32_LogicalDisk | Where-Object {$_.DeviceID -eq \'C:\'} | Select-Object Size,FreeSpace"'
          }
          
          const output = execSync(command, { encoding: 'utf8', timeout: 10000 })
          
          if (process.platform === 'win32') {
            // Windows PowerShell 解析
            const sizeMatch = output.match(/Size\s*:\s*(\d+)/)
            const freeMatch = output.match(/FreeSpace\s*:\s*(\d+)/)
            
            if (sizeMatch && freeMatch) {
              totalSize = parseInt(sizeMatch[1])
              totalAvailable = parseInt(freeMatch[1])
              totalUsed = totalSize - totalAvailable
              usagePercent = Math.round((totalUsed / totalSize) * 100)
            }
          } else {
            // Unix 系统解析
            const lines = output.trim().split('\n')
            const dataLine = lines[lines.length - 1]
            const parts = dataLine.split(/\s+/)
            
            let offset = isNaN(parseInt(parts[0])) ? 1 : 0
            
            if (parts.length >= offset + 4) {
              totalSize = parseInt(parts[offset]) * 1024      // KB to Bytes
              totalUsed = parseInt(parts[offset + 1]) * 1024  
              totalAvailable = parseInt(parts[offset + 2]) * 1024
              usagePercent = parseInt(parts[offset + 3].replace('%', ''))
            }
          }
          
          console.log(`系统命令备用方案成功: ${Math.round(totalSize/1024/1024/1024*100)/100}GB`)
          
        } catch (cmdError) {
          // 设置合理的默认值
          totalSize = 100 * 1024 * 1024 * 1024   // 100GB
          totalUsed = 30 * 1024 * 1024 * 1024    // 30GB  
          totalAvailable = 70 * 1024 * 1024 * 1024 // 70GB
          usagePercent = 30
        }
      }
      
      const disk = {
        total: Math.round(totalSize / 1024 / 1024 / 1024 * 100) / 100, // GB
        used: Math.round(totalUsed / 1024 / 1024 / 1024 * 100) / 100, // GB
        free: Math.round(totalAvailable / 1024 / 1024 / 1024 * 100) / 100, // GB
        usage: usagePercent, // 直接使用系统命令获取的使用率
        readSpeed: 0, // 需要从历史数据计算
        writeSpeed: 0, // 需要从历史数据计算
        iops: 0 // 需要从系统统计获取
      }

      // 处理网络信息 - 充分利用 systeminformation 库的数据
      const networkInterfaces: NetworkInterface[] = networkInfo
        .filter((iface: any) => !iface.internal && !iface.virtual) // 过滤内部和虚拟接口
        .map((iface: any) => {
          // 查找对应的网络统计数据
          const stats = networkStats.find((stat: any) => stat.iface === iface.iface) || {}
          
          return {
            name: iface.iface,
            ip4: iface.ip4 || '',
            ip6: iface.ip6 || '',
            mac: iface.mac || '',
            internal: iface.internal || false,
            virtual: iface.virtual || false,
            speed: iface.speed || 0,
            // 使用 systeminformation 提供的统计数据
            rxBytes: stats.rx_bytes || 0,
            txBytes: stats.tx_bytes || 0,
            rxSpeed: stats.rx_sec || 0,    // 每秒接收字节数
            txSpeed: stats.tx_sec || 0,    // 每秒发送字节数
            // 额外的网络质量指标
            rxDropped: stats.rx_dropped || 0,
            txDropped: stats.tx_dropped || 0,
            rxErrors: stats.rx_errors || 0,
            txErrors: stats.tx_errors || 0
          }
        })

      // 计算网络总体统计
      const totalRxBytes = networkStats
        .filter((stat: any) => !stat.iface.includes('lo') && !stat.iface.includes('dummy'))
        .reduce((sum: number, stat: any) => sum + (stat.rx_bytes || 0), 0)
      
      const totalTxBytes = networkStats
        .filter((stat: any) => !stat.iface.includes('lo') && !stat.iface.includes('dummy'))
        .reduce((sum: number, stat: any) => sum + (stat.tx_bytes || 0), 0)
      
      const totalRxSpeed = networkStats
        .filter((stat: any) => !stat.iface.includes('lo') && !stat.iface.includes('dummy'))
        .reduce((sum: number, stat: any) => sum + (stat.rx_sec || 0), 0)
      
      const totalTxSpeed = networkStats
        .filter((stat: any) => !stat.iface.includes('lo') && !stat.iface.includes('dummy'))
        .reduce((sum: number, stat: any) => sum + (stat.tx_sec || 0), 0)

      const network = {
        interfaces: networkInterfaces,
        totalRxBytes,
        totalTxBytes,
        totalRxSpeed,  // 实时接收速度 (字节/秒)
        totalTxSpeed,  // 实时发送速度 (字节/秒)
        // 网络质量指标
        quality: {
          totalErrors: networkStats.reduce((sum: number, stat: any) => 
            sum + (stat.rx_errors || 0) + (stat.tx_errors || 0), 0),
          totalDropped: networkStats.reduce((sum: number, stat: any) => 
            sum + (stat.rx_dropped || 0) + (stat.tx_dropped || 0), 0),
          activeInterfaces: networkInterfaces.length
        }
      }
      
      console.log(`网络监控: ${networkInterfaces.length} 个活动接口, 总速度: 上行 ${Math.round(totalTxSpeed/1024)}KB/s, 下行 ${Math.round(totalRxSpeed/1024)}KB/s`)

      const metrics: SystemMetrics = {
        timestamp: new Date().toISOString(),
        cpu,
        memory,
        disk,
        network
      }

      // 缓存结果
      await CacheService.setex(cacheKey, this.CACHE_TTL, JSON.stringify(metrics))

      return metrics
    } catch (error) {
      console.error('Error collecting system metrics:', error)
      throw error
    }
  }

  /**
   * 获取数据库健康状态
   */
  public async getDatabaseHealth(): Promise<DatabaseHealth> {
    const cacheKey = 'monitor:database:health'
    
    const cached = await CacheService.get(cacheKey)
    if (cached) {
      return JSON.parse(cached)
    }

    try {
      const startTime = Date.now()
      
      // 测试数据库连接
      await prisma.$queryRaw`SELECT 1`
      
      const responseTime = Date.now() - startTime

      // 获取数据库统计信息
      const [userCount, serviceCount, logCount] = await Promise.all([
        prisma.user.count(),
        prisma.service.count(),
        prisma.operationLog.count()
      ])

      const health: DatabaseHealth = {
        name: 'MySQL Database',
        status: responseTime < 100 ? 'healthy' : responseTime < 500 ? 'warning' : 'critical',
        responseTime,
        lastCheck: new Date().toISOString(),
        uptime: '99.99%',
        details: `MySQL连接正常，响应时间: ${responseTime}ms`,
        connections: {
          active: 10, // 模拟数据，实际需要从数据库获取
          idle: 5,
          max: 100
        },
        performance: {
          queryCount: userCount + serviceCount + logCount,
          slowQueries: 0,
          avgResponseTime: responseTime
        },
        statistics: {
          totalTables: 20, // 可以通过查询信息架构获取
          totalRecords: userCount + serviceCount + logCount,
          dbSize: 1024 // MB，需要查询数据库大小
        }
      }

      await CacheService.setex(cacheKey, this.CACHE_TTL, JSON.stringify(health))
      return health
    } catch (error) {
      return {
        name: 'MySQL Database',
        status: 'down',
        responseTime: 0,
        lastCheck: new Date().toISOString(),
        uptime: '0%',
        details: error instanceof Error ? error.message : '数据库连接失败',
        connections: {
          active: 0,
          idle: 0,
          max: 0
        },
        performance: {
          queryCount: 0,
          slowQueries: 0,
          avgResponseTime: 0
        },
        statistics: {
          totalTables: 0,
          totalRecords: 0,
          dbSize: 0
        }
      }
    }
  }

  /**
   * 获取Redis健康状态
   */
  public async getRedisHealth(): Promise<RedisHealth> {
    const cacheKey = 'monitor:redis:health'
    
    try {
      const startTime = Date.now()
      
      // 测试Redis连接
      await CacheService.ping()
      
      const responseTime = Date.now() - startTime

      // 获取Redis信息
      const info = await CacheService.info()
      const memory = await CacheService.memory('usage')
      
      const health: RedisHealth = {
        name: 'Redis Cache',
        status: responseTime < 50 ? 'healthy' : responseTime < 200 ? 'warning' : 'critical',
        responseTime,
        lastCheck: new Date().toISOString(),
        uptime: '99.95%',
        details: `Redis连接正常，响应时间: ${responseTime}ms`,
        info: {
          version: '6.2.0', // 从info中解析
          mode: 'standalone',
          role: 'master'
        },
        memory: {
          used: memory || 0,
          peak: memory || 0,
          fragmentation: 1.1
        },
        stats: {
          keyCount: 100, // 通过DBSIZE命令获取
          hitRate: 95.8,
          operationsPerSec: 1000
        }
      }

      await CacheService.setex(cacheKey, this.CACHE_TTL, JSON.stringify(health))
      return health
    } catch (error) {
      return {
        name: 'Redis Cache',
        status: 'down',
        responseTime: 0,
        lastCheck: new Date().toISOString(),
        uptime: '0%',
        details: error instanceof Error ? error.message : 'Redis连接失败',
        info: {
          version: 'unknown',
          mode: 'unknown',
          role: 'unknown'
        },
        memory: {
          used: 0,
          peak: 0,
          fragmentation: 0
        },
        stats: {
          keyCount: 0,
          hitRate: 0,
          operationsPerSec: 0
        }
      }
    }
  }

  /**
   * 获取应用健康状态
   */
  public async getApplicationHealth(): Promise<ApplicationHealth> {
    const memUsage = process.memoryUsage()
    const uptime = process.uptime()
    
    return {
      name: 'Node.js Application',
      status: 'healthy',
      responseTime: 1,
      lastCheck: new Date().toISOString(),
      uptime: this.formatUptime(uptime),
      details: `Node.js ${process.version} 运行正常`,
      version: process.version,
      uptime,
      memoryUsage: {
        rss: Math.round(memUsage.rss / 1024 / 1024 * 100) / 100, // MB
        heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024 * 100) / 100, // MB
        heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024 * 100) / 100, // MB
        external: Math.round(memUsage.external / 1024 / 1024 * 100) / 100, // MB
        arrayBuffers: Math.round((memUsage as any).arrayBuffers / 1024 / 1024 * 100) / 100 // MB
      },
      eventLoop: {
        delay: 0, // 需要通过性能钩子获取
        utilization: 0
      },
      gc: {
        collections: 0,
        duration: 0
      }
    }
  }

  /**
   * 智能分析系统性能
   */
  public async performIntelligentAnalysis(): Promise<IntelligentAnalysis> {
    const cacheKey = 'monitor:analysis:intelligent'
    
    const cached = await CacheService.get(cacheKey)
    if (cached) {
      return JSON.parse(cached)
    }

    try {
      const currentMetrics = await this.collectSystemMetrics()
      const historicalMetrics = this.getHistoricalMetrics(24) // 获取24小时历史数据
      
      // 计算健康评分
      const score = this.calculateHealthScore(currentMetrics)
      const level = this.getHealthLevel(score)
      
      // 生成洞察分析
      const insights = this.generateInsights(currentMetrics, historicalMetrics)
      
      // 性能预测
      const predictions = this.generatePredictions(historicalMetrics)
      
      // 生成建议
      const recommendations = this.generateRecommendations(currentMetrics, insights)
      
      // 趋势分析
      const trends = this.analyzeTrends(historicalMetrics)
      
      // 异常检测
      const anomalies = this.detectAnomalies(currentMetrics, historicalMetrics)

      const analysis: IntelligentAnalysis = {
        score,
        level,
        insights,
        predictions,
        recommendations,
        trends,
        anomalies
      }

      // 缓存分析结果（较长时间，因为分析较复杂）
      await CacheService.setex(cacheKey, 300, JSON.stringify(analysis))

      return analysis
    } catch (error) {
      console.error('Error performing intelligent analysis:', error)
      throw error
    }
  }

  /**
   * 获取监控仪表板数据
   */
  public async getDashboard(): Promise<MonitoringDashboard> {
    // 向后兼容旧调用：使用轻量快照
    return this.getDashboardCached({ detail: 'lite' })
  }

  /**
   * 获取性能基准对比
   */
  public async getPerformanceBenchmarks(): Promise<PerformanceBenchmark[]> {
    const currentMetrics = await this.collectSystemMetrics()
    
    return [
      {
        category: 'CPU',
        metric: 'Usage',
        baseline: 20,
        target: 70,
        current: currentMetrics.cpu.usage,
        percentile: this.calculatePercentile(currentMetrics.cpu.usage, [20, 70]),
        comparison: currentMetrics.cpu.usage <= 70 ? 'within' : 'above',
        recommendation: currentMetrics.cpu.usage > 70 ? '考虑优化CPU密集型任务或扩容' : undefined
      },
      {
        category: 'Memory',
        metric: 'Usage',
        baseline: 30,
        target: 80,
        current: currentMetrics.memory.usage,
        percentile: this.calculatePercentile(currentMetrics.memory.usage, [30, 80]),
        comparison: currentMetrics.memory.usage <= 80 ? 'within' : 'above',
        recommendation: currentMetrics.memory.usage > 80 ? '考虑增加内存或优化内存使用' : undefined
      },
      {
        category: 'Disk',
        metric: 'Usage',
        baseline: 20,
        target: 85,
        current: currentMetrics.disk.usage,
        percentile: this.calculatePercentile(currentMetrics.disk.usage, [20, 85]),
        comparison: currentMetrics.disk.usage <= 85 ? 'within' : 'above',
        recommendation: currentMetrics.disk.usage > 85 ? '需要清理磁盘空间或扩容' : undefined
      }
    ]
  }

  /**
   * 获取资源使用预警
   */
  public async getResourceAlerts(): Promise<ResourceAlert[]> {
    const currentMetrics = await this.collectSystemMetrics()
    const alerts: ResourceAlert[] = []

    // CPU预警
    if (currentMetrics.cpu.usage > 70) {
      alerts.push({
        resource: 'CPU',
        current: currentMetrics.cpu.usage,
        threshold: 80,
        trend: this.calculateTrend(this.metricsHistory.slice(-10).map(m => m.cpu.usage)),
        timeToThreshold: this.estimateTimeToThreshold(currentMetrics.cpu.usage, 80, 'increasing'),
        recommendation: '监控CPU使用率，考虑优化应用性能或进行负载均衡'
      })
    }

    // 内存预警
    if (currentMetrics.memory.usage > 75) {
      alerts.push({
        resource: 'Memory',
        current: currentMetrics.memory.usage,
        threshold: 85,
        trend: this.calculateTrend(this.metricsHistory.slice(-10).map(m => m.memory.usage)),
        timeToThreshold: this.estimateTimeToThreshold(currentMetrics.memory.usage, 85, 'increasing'),
        recommendation: '内存使用率较高，建议检查内存泄漏或增加内存'
      })
    }

    // 磁盘预警
    if (currentMetrics.disk.usage > 80) {
      alerts.push({
        resource: 'Disk',
        current: currentMetrics.disk.usage,
        threshold: 90,
        trend: this.calculateTrend(this.metricsHistory.slice(-10).map(m => m.disk.usage)),
        timeToThreshold: this.estimateTimeToThreshold(currentMetrics.disk.usage, 90, 'increasing'),
        recommendation: '磁盘空间不足，需要清理文件或扩展存储空间'
      })
    }

    return alerts
  }

  // ========== 私有方法 ==========

  /**
   * 存储指标到历史记录
   */
  private storeMetrics(metrics: SystemMetrics) {
    this.metricsHistory.push(metrics)
    
    // 保持历史记录在合理范围内
    if (this.metricsHistory.length > this.MAX_HISTORY_LENGTH) {
      this.metricsHistory.shift()
    }
  }

  /**
   * 检查阈值并触发告警
   */
  private async checkThresholds(metrics: SystemMetrics) {
    const rules = await prisma.alertRule.findMany({
      where: { enabled: true }
    })

    for (const rule of rules) {
      const metricValue = this.getMetricValue(metrics, rule.metricType)
      if (metricValue !== null && this.checkCondition(metricValue, rule.condition, rule.threshold)) {
        await this.triggerAlert(rule, metricValue)
      }
    }
  }

  /**
   * 获取指标值
   */
  private getMetricValue(metrics: SystemMetrics, metricType: string): number | null {
    switch (metricType) {
      case 'CPU':
        return metrics.cpu.usage
      case 'MEMORY':
        return metrics.memory.usage
      case 'DISK':
        return metrics.disk.usage
      case 'NETWORK':
        return metrics.network.totalRxSpeed + metrics.network.totalTxSpeed
      default:
        return null
    }
  }

  /**
   * 检查条件
   */
  private checkCondition(value: number, condition: string, threshold: number): boolean {
    switch (condition) {
      case 'GT':
        return value > threshold
      case 'LT':
        return value < threshold
      case 'GTE':
        return value >= threshold
      case 'LTE':
        return value <= threshold
      case 'EQ':
        return value === threshold
      default:
        return false
    }
  }

  /**
   * 触发告警
   */
  private async triggerAlert(rule: any, metricValue: number) {
    try {
      const alert = await prisma.alert.create({
        data: {
          ruleId: rule.id,
          severity: rule.severity,
          status: 'PENDING',
          message: `${rule.metricType}指标触发告警：当前值${metricValue}，阈值${rule.threshold}`,
          metricValue
        }
      })

      // 实时推送系统告警
      await this.pushSystemAlert({
        title: `${rule.name || rule.metricType}告警`,
        message: alert.message,
        severity: rule.severity as 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL',
        component: rule.metricType
      })
      
      console.log(`🚨 Alert triggered: ${rule.metricType} = ${metricValue} (threshold: ${rule.threshold})`)
    } catch (error) {
      console.error('Error creating alert:', error)
    }
  }

  /**
   * 处理顶级进程
   */
  private processTopProcesses(processes: any[]): ProcessInfo[] {
    return processes
      .sort((a, b) => b.cpu - a.cpu)
      .slice(0, 10)
      .map(proc => ({
        pid: proc.pid,
        name: proc.name,
        cpu: Math.round(proc.cpu * 100) / 100,
        memory: Math.round(proc.memRss / 1024 / 1024 * 100) / 100, // MB
        state: proc.state
      }))
  }

  /**
   * 计算健康评分
   */
  private calculateHealthScore(metrics: SystemMetrics): number {
    let score = 100

    // CPU评分（权重25%）
    if (metrics.cpu.usage > 90) score -= 25
    else if (metrics.cpu.usage > 80) score -= 15
    else if (metrics.cpu.usage > 70) score -= 8
    else if (metrics.cpu.usage > 60) score -= 3

    // 内存评分（权重25%）
    if (metrics.memory.usage > 95) score -= 25
    else if (metrics.memory.usage > 85) score -= 15
    else if (metrics.memory.usage > 75) score -= 8
    else if (metrics.memory.usage > 65) score -= 3

    // 磁盘评分（权重20%）
    if (metrics.disk.usage > 95) score -= 20
    else if (metrics.disk.usage > 85) score -= 12
    else if (metrics.disk.usage > 75) score -= 6
    else if (metrics.disk.usage > 65) score -= 2

    // 负载评分（权重15%）
    const loadAvg = metrics.cpu.loadAverage[0]
    const cores = metrics.cpu.cores
    if (loadAvg > cores * 2) score -= 15
    else if (loadAvg > cores * 1.5) score -= 10
    else if (loadAvg > cores) score -= 5

    // 进程评分（权重15%）
    if (metrics.processes.zombie > 0) score -= 5
    if (metrics.processes.total > 1000) score -= 5
    if (metrics.processes.running > cores * 10) score -= 5

    return Math.max(0, Math.round(score))
  }

  /**
   * 获取健康等级
   */
  private getHealthLevel(score: number): 'excellent' | 'good' | 'warning' | 'critical' {
    if (score >= 90) return 'excellent'
    if (score >= 75) return 'good'
    if (score >= 60) return 'warning'
    return 'critical'
  }

  /**
   * 生成洞察分析
   */
  private generateInsights(current: SystemMetrics, historical: SystemMetrics[]): AnalysisInsight[] {
    const insights: AnalysisInsight[] = []

    // CPU洞察
    if (current.cpu.usage > 80) {
      insights.push({
        category: 'CPU',
        title: 'CPU使用率过高',
        description: `当前CPU使用率为${current.cpu.usage}%，超过了建议阈值`,
        severity: current.cpu.usage > 90 ? 'critical' : 'warning',
        confidence: 0.95,
        impact: 'high',
        source: 'threshold_analysis'
      })
    }

    // 内存洞察
    if (current.memory.usage > 85) {
      insights.push({
        category: 'Memory',
        title: '内存使用率告警',
        description: `当前内存使用率为${current.memory.usage}%，建议检查内存使用情况`,
        severity: current.memory.usage > 95 ? 'critical' : 'warning',
        confidence: 0.9,
        impact: 'high',
        source: 'threshold_analysis'
      })
    }

    // 磁盘洞察
    if (current.disk.usage > 85) {
      insights.push({
        category: 'Disk',
        title: '磁盘空间不足',
        description: `当前磁盘使用率为${current.disk.usage}%，需要清理空间`,
        severity: current.disk.usage > 95 ? 'critical' : 'warning',
        confidence: 1.0,
        impact: 'high',
        source: 'threshold_analysis'
      })
    }

    return insights
  }

  /**
   * 生成性能预测
   */
  private generatePredictions(historical: SystemMetrics[]): PerformancePrediction[] {
    if (historical.length < 10) return []

    const predictions: PerformancePrediction[] = []

    // CPU预测
    const cpuValues = historical.slice(-24).map(m => m.cpu.usage)
    const cpuTrend = this.calculateLinearTrend(cpuValues)
    predictions.push({
      metric: 'CPU Usage',
      currentValue: cpuValues[cpuValues.length - 1],
      predictedValue: Math.max(0, Math.min(100, cpuValues[cpuValues.length - 1] + cpuTrend * 24)),
      timeframe: '24 hours',
      confidence: 0.7,
      trend: cpuTrend > 1 ? 'increasing' : cpuTrend < -1 ? 'decreasing' : 'stable',
      riskLevel: cpuTrend > 2 ? 'high' : cpuTrend > 0.5 ? 'medium' : 'low'
    })

    // 内存预测
    const memValues = historical.slice(-24).map(m => m.memory.usage)
    const memTrend = this.calculateLinearTrend(memValues)
    predictions.push({
      metric: 'Memory Usage',
      currentValue: memValues[memValues.length - 1],
      predictedValue: Math.max(0, Math.min(100, memValues[memValues.length - 1] + memTrend * 24)),
      timeframe: '24 hours',
      confidence: 0.75,
      trend: memTrend > 1 ? 'increasing' : memTrend < -1 ? 'decreasing' : 'stable',
      riskLevel: memTrend > 1.5 ? 'high' : memTrend > 0.3 ? 'medium' : 'low'
    })

    return predictions
  }

  /**
   * 生成建议
   */
  private generateRecommendations(metrics: SystemMetrics, insights: AnalysisInsight[]): Recommendation[] {
    const recommendations: Recommendation[] = []

    // 基于洞察生成建议
    insights.forEach((insight, index) => {
      if (insight.severity === 'critical' || insight.severity === 'warning') {
        recommendations.push({
          id: `rec-${index}`,
          category: insight.category,
          title: `优化${insight.category}使用`,
          description: this.getRecommendationDescription(insight.category, metrics),
          priority: insight.severity === 'critical' ? 'urgent' : 'high',
          difficulty: 'medium',
          estimatedImpact: '显著改善系统性能',
          actionItems: this.getActionItems(insight.category),
          resources: this.getResources(insight.category)
        })
      }
    })

    return recommendations
  }

  /**
   * 分析趋势
   */
  private analyzeTrends(historical: SystemMetrics[]): TrendAnalysis[] {
    if (historical.length < 10) return []

    const trends: TrendAnalysis[] = []

    // CPU趋势分析
    const cpuValues = historical.map(m => m.cpu.usage)
    const cpuTrend = this.calculateLinearTrend(cpuValues)
    trends.push({
      metric: 'CPU Usage',
      period: '24 hours',
      trend: cpuTrend > 1 ? 'upward' : cpuTrend < -1 ? 'downward' : 'stable',
      changeRate: cpuTrend,
      significantEvents: this.findSignificantEvents(historical.map((m, i) => ({
        timestamp: m.timestamp,
        value: m.cpu.usage
      })))
    })

    return trends
  }

  /**
   * 检测异常
   */
  private detectAnomalies(current: SystemMetrics, historical: SystemMetrics[]): AnomalyDetection[] {
    if (historical.length < 20) return []

    const anomalies: AnomalyDetection[] = []

    // CPU异常检测
    const cpuValues = historical.map(m => m.cpu.usage)
    const cpuMean = cpuValues.reduce((a, b) => a + b) / cpuValues.length
    const cpuStd = Math.sqrt(cpuValues.reduce((a, b) => a + Math.pow(b - cpuMean, 2)) / cpuValues.length)
    
    if (Math.abs(current.cpu.usage - cpuMean) > cpuStd * 2) {
      anomalies.push({
        timestamp: current.timestamp,
        metric: 'CPU Usage',
        actualValue: current.cpu.usage,
        expectedValue: cpuMean,
        deviation: Math.abs(current.cpu.usage - cpuMean),
        severity: Math.abs(current.cpu.usage - cpuMean) > cpuStd * 3 ? AlertSeverity.CRITICAL : AlertSeverity.HIGH,
        description: `CPU使用率异常：当前${current.cpu.usage}%，预期${Math.round(cpuMean)}%`,
        confidence: 0.85,
        possibleCauses: ['系统负载突增', '进程异常', '资源竞争']
      })
    }

    return anomalies
  }

  /**
   * 计算健康摘要
   */
  private calculateHealthSummary(
    metrics: SystemMetrics, 
    services: ServiceHealth[], 
    analysis: IntelligentAnalysis
  ): HealthSummary {
    const criticalServices = services.filter(s => s.status === 'critical' || s.status === 'down').length
    const warningServices = services.filter(s => s.status === 'warning').length
    
    let overallStatus: 'healthy' | 'warning' | 'critical' = 'healthy'
    
    if (criticalServices > 0 || analysis.level === 'critical') {
      overallStatus = 'critical'
    } else if (warningServices > 0 || analysis.level === 'warning') {
      overallStatus = 'warning'
    }

    return {
      overall: overallStatus,
      score: analysis.score,
      uptime: this.formatUptime(process.uptime()),
      issues: analysis.insights.filter(i => i.severity === 'warning').length,
      criticalIssues: analysis.insights.filter(i => i.severity === 'critical').length
    }
  }

  /**
   * 获取活跃告警
   */
  private async getActiveAlerts(): Promise<ActiveAlert[]> {
    try {
      const alerts = await prisma.alert.findMany({
        where: {
          status: { in: ['PENDING', 'ACKNOWLEDGED'] }
        },
        include: {
          rule: {
            select: {
              name: true,
              metricType: true
            }
          }
        },
        orderBy: { triggeredAt: 'desc' },
        take: 20
      })

      return alerts.map(alert => ({
        id: alert.id,
        type: alert.rule.metricType,
        severity: alert.severity,
        title: alert.rule.name,
        message: alert.message,
        timestamp: alert.triggeredAt.toISOString(),
        status: alert.status.toLowerCase() as 'active' | 'acknowledged' | 'resolved',
        source: 'system'
      }))
    } catch (error) {
      console.error('Error getting active alerts:', error)
      return []
    }
  }

  /**
   * 获取历史指标
   */
  private getHistoricalMetrics(hours: number): SystemMetrics[] {
    const cutoffTime = new Date(Date.now() - hours * 60 * 60 * 1000)
    return this.metricsHistory.filter(m => new Date(m.timestamp) >= cutoffTime)
  }

  /**
   * 格式化运行时间
   */
  private formatUptime(uptimeInSeconds: number): string {
    const days = Math.floor(uptimeInSeconds / (24 * 60 * 60))
    const hours = Math.floor((uptimeInSeconds % (24 * 60 * 60)) / (60 * 60))
    const minutes = Math.floor((uptimeInSeconds % (60 * 60)) / 60)
    
    return `${days}天 ${hours}小时 ${minutes}分钟`
  }

  /**
   * 计算线性趋势
   */
  private calculateLinearTrend(values: number[]): number {
    const n = values.length
    if (n < 2) return 0

    const xMean = (n - 1) / 2
    const yMean = values.reduce((a, b) => a + b) / n

    let numerator = 0
    let denominator = 0

    for (let i = 0; i < n; i++) {
      numerator += (i - xMean) * (values[i] - yMean)
      denominator += (i - xMean) * (i - xMean)
    }

    return denominator === 0 ? 0 : numerator / denominator
  }

  /**
   * 计算趋势方向
   */
  private calculateTrend(values: number[]): 'increasing' | 'decreasing' | 'stable' {
    if (values.length < 3) return 'stable'
    
    const trend = this.calculateLinearTrend(values)
    if (trend > 1) return 'increasing'
    if (trend < -1) return 'decreasing'
    return 'stable'
  }

  /**
   * 估算达到阈值的时间
   */
  private estimateTimeToThreshold(current: number, threshold: number, trend: string): number | undefined {
    if (trend !== 'increasing' || current >= threshold) return undefined
    
    // 简单的线性估算（实际应该基于历史趋势）
    const rate = 1 // 每分钟增长1%，这里应该基于实际趋势计算
    return Math.round((threshold - current) / rate)
  }

  /**
   * 计算百分位数
   */
  private calculatePercentile(value: number, range: [number, number]): number {
    const [min, max] = range
    if (value <= min) return 0
    if (value >= max) return 100
    return Math.round(((value - min) / (max - min)) * 100)
  }

  /**
   * 获取建议描述
   */
  private getRecommendationDescription(category: string, metrics: SystemMetrics): string {
    switch (category) {
      case 'CPU':
        return `当前CPU使用率为${metrics.cpu.usage}%，建议检查CPU密集型进程并考虑优化或扩容`
      case 'Memory':
        return `当前内存使用率为${metrics.memory.usage}%，建议检查内存泄漏并考虑增加内存`
      case 'Disk':
        return `当前磁盘使用率为${metrics.disk.usage}%，建议清理不必要的文件或扩展存储空间`
      default:
        return '建议监控并优化系统资源使用'
    }
  }

  /**
   * 获取行动项目
   */
  private getActionItems(category: string): string[] {
    const actionMap: Record<string, string[]> = {
      'CPU': [
        '检查CPU使用率最高的进程',
        '优化应用程序性能',
        '考虑负载均衡',
        '监控系统负载'
      ],
      'Memory': [
        '检查内存使用情况',
        '查找并修复内存泄漏',
        '优化数据缓存策略',
        '考虑增加内存'
      ],
      'Disk': [
        '清理临时文件',
        '删除不必要的日志文件',
        '压缩或归档旧数据',
        '考虑扩展存储空间'
      ]
    }
    
    return actionMap[category] || ['监控资源使用情况']
  }

  /**
   * 获取相关资源
   */
  private getResources(category: string): string[] {
    const resourceMap: Record<string, string[]> = {
      'CPU': [
        '系统性能优化指南',
        'CPU监控最佳实践',
        '负载均衡配置'
      ],
      'Memory': [
        '内存管理最佳实践',
        '内存泄漏检测工具',
        '垃圾回收优化'
      ],
      'Disk': [
        '磁盘清理指南',
        '存储扩容方案',
        '数据归档策略'
      ]
    }
    
    return resourceMap[category] || []
  }

  /**
   * 获取历史数据
   */
  public getHistoricalData(metric: string, hours: number, aggregation: string = 'avg') {
    const cutoffTime = new Date(Date.now() - hours * 60 * 60 * 1000)
    const filteredData = this.metricsHistory.filter(m => new Date(m.timestamp) >= cutoffTime)
    
    return filteredData.map(m => ({
      timestamp: m.timestamp,
      value: this.getMetricValue(m, metric.toUpperCase()) || 0
    }))
  }

  /**
   * 获取服务可用性统计
   */
  public async getAvailabilityStats(days: number) {
    // 模拟数据，实际应该从数据库查询
    return {
      database: {
        uptime: 99.95,
        downtime: 0.05,
        incidents: 1,
        mttr: 5 // 平均恢复时间(分钟)
      },
      redis: {
        uptime: 99.99,
        downtime: 0.01,
        incidents: 0,
        mttr: 0
      },
      application: {
        uptime: 99.9,
        downtime: 0.1,
        incidents: 2,
        mttr: 3
      }
    }
  }

  /**
   * 获取系统事件日志
   */
  public async getSystemEvents(params: { page: number; limit: number; level?: string; type?: string }) {
    const { page, limit, level, type } = params
    const skip = (page - 1) * limit
    
    const where: any = {}
    if (level) where.level = level
    if (type) where.type = type
    
    // 从数据库获取系统事件
    const [events, total] = await Promise.all([
      prisma.systemEvent.findMany({
        where,
        skip,
        take: limit,
        orderBy: { createdAt: 'desc' }
      }).catch(() => []), // 如果表不存在，返回空数组
      prisma.systemEvent.count({ where }).catch(() => 0)
    ])
    
    return {
      events: events.map(event => ({
        id: event.id,
        timestamp: event.createdAt.toISOString(),
        level: event.level,
        type: event.type,
        message: event.message,
        details: event.details
      })),
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    }
  }

  /**
   * 运行系统诊断
   */
  public async runSystemDiagnostics() {
    const tests = [
      await this.testDatabaseConnectivity(),
      await this.testRedisConnectivity(),
      await this.testDiskSpace(),
      await this.testMemoryUsage(),
      await this.testCPUUsage(),
      await this.testNetworkConnectivity(),
      await this.testApplicationHealth()
    ]
    
    const passed = tests.filter(test => test.passed).length
    const failed = tests.filter(test => !test.passed).length
    
    return {
      timestamp: new Date().toISOString(),
      overall: failed === 0 ? 'healthy' : failed <= 2 ? 'warning' : 'critical',
      score: Math.round((passed / tests.length) * 100),
      tests,
      summary: {
        total: tests.length,
        passed,
        failed,
        warnings: tests.filter(test => test.severity === 'warning').length
      }
    }
  }

  /**
   * 生成监控报告
   */
  public async generateReport(type: string) {
    const currentMetrics = await this.collectSystemMetrics()
    const analysis = await this.performIntelligentAnalysis()
    const benchmarks = await this.getPerformanceBenchmarks()
    
    const report = {
      type,
      generatedAt: new Date().toISOString(),
      period: type === 'daily' ? '24小时' : type === 'weekly' ? '7天' : '30天',
      summary: {
        overallHealth: analysis.level,
        score: analysis.score,
        keyMetrics: {
          cpuAvg: Math.round(this.metricsHistory.slice(-24).reduce((sum, m) => sum + m.cpu.usage, 0) / 24),
          memoryAvg: Math.round(this.metricsHistory.slice(-24).reduce((sum, m) => sum + m.memory.usage, 0) / 24),
          diskUsage: currentMetrics.disk.usage
        }
      },
      analysis,
      benchmarks,
      recommendations: analysis.recommendations.slice(0, 5), // 前5个重要建议
      trends: analysis.trends,
      incidents: [], // 从数据库查询事故记录
      availability: await this.getAvailabilityStats(type === 'daily' ? 1 : type === 'weekly' ? 7 : 30)
    }
    
    return report
  }

  /**
   * 更新监控阈值
   */
  public async updateThreshold(data: { metric: string; warning: number; critical: number; duration: number }) {
    // 这里应该更新到数据库或配置文件
    // 为演示目的，返回更新后的配置
    return {
      metric: data.metric,
      warning: data.warning,
      critical: data.critical,
      duration: data.duration,
      updatedAt: new Date().toISOString()
    }
  }

  /**
   * 获取监控配置
   */
  public async getMonitoringConfig() {
    return {
      enabled: true,
      interval: 30,
      retention: {
        realtime: 24,
        hourly: 30,
        daily: 12
      },
      thresholds: {
        cpu: { warning: 70, critical: 90, duration: 300 },
        memory: { warning: 80, critical: 95, duration: 300 },
        disk: { warning: 85, critical: 95, duration: 600 },
        network: { warning: 80, critical: 95, duration: 300 }
      },
      alerting: {
        enabled: true,
        channels: ['email', 'sms'],
        cooldown: 1800
      },
      analysis: {
        enabled: true,
        historicalWindow: 168,
        predictionWindow: 24,
        anomalyDetection: {
          enabled: true,
          sensitivity: 0.8,
          algorithm: 'statistical'
        }
      }
    }
  }

  /**
   * 更新监控配置
   */
  public async updateMonitoringConfig(config: any, userId: string) {
    // 这里应该验证配置并保存到数据库
    return {
      ...config,
      updatedBy: userId,
      updatedAt: new Date().toISOString()
    }
  }

  // ========== 诊断测试方法 ==========

  private async testDatabaseConnectivity() {
    try {
      const startTime = Date.now()
      await prisma.$queryRaw`SELECT 1`
      const responseTime = Date.now() - startTime
      
      return {
        name: '数据库连接测试',
        passed: responseTime < 1000,
        responseTime,
        severity: responseTime > 500 ? 'warning' : 'info',
        message: responseTime < 1000 ? '数据库连接正常' : '数据库响应较慢',
        details: { responseTime }
      }
    } catch (error) {
      return {
        name: '数据库连接测试',
        passed: false,
        responseTime: 0,
        severity: 'critical',
        message: '数据库连接失败',
        details: { error: error instanceof Error ? error.message : '未知错误' }
      }
    }
  }

  private async testRedisConnectivity() {
    try {
      const startTime = Date.now()
      await CacheService.ping()
      const responseTime = Date.now() - startTime
      
      return {
        name: 'Redis连接测试',
        passed: responseTime < 500,
        responseTime,
        severity: responseTime > 100 ? 'warning' : 'info',
        message: responseTime < 500 ? 'Redis连接正常' : 'Redis响应较慢',
        details: { responseTime }
      }
    } catch (error) {
      return {
        name: 'Redis连接测试',
        passed: false,
        responseTime: 0,
        severity: 'critical',
        message: 'Redis连接失败',
        details: { error: error instanceof Error ? error.message : '未知错误' }
      }
    }
  }

  private async testDiskSpace() {
    const diskUsage = await this.getDiskUsage()
    const passed = diskUsage.usage < 90
    
    return {
      name: '磁盘空间测试',
      passed,
      responseTime: 0,
      severity: diskUsage.usage > 95 ? 'critical' : diskUsage.usage > 85 ? 'warning' : 'info',
      message: passed ? '磁盘空间充足' : '磁盘空间不足',
      details: { usage: diskUsage.usage, free: diskUsage.free }
    }
  }

  private async testMemoryUsage() {
    const memUsage = this.getMemoryUsage()
    const passed = memUsage.usage < 90
    
    return {
      name: '内存使用测试',
      passed,
      responseTime: 0,
      severity: memUsage.usage > 95 ? 'critical' : memUsage.usage > 85 ? 'warning' : 'info',
      message: passed ? '内存使用正常' : '内存使用率过高',
      details: { usage: memUsage.usage, available: memUsage.free }
    }
  }

  private async testCPUUsage() {
    const cpuUsage = await this.getCpuUsage()
    const passed = cpuUsage < 90
    
    return {
      name: 'CPU使用测试',
      passed,
      responseTime: 0,
      severity: cpuUsage > 95 ? 'critical' : cpuUsage > 80 ? 'warning' : 'info',
      message: passed ? 'CPU使用正常' : 'CPU使用率过高',
      details: { usage: cpuUsage, cores: os.cpus().length }
    }
  }

  private async testNetworkConnectivity() {
    try {
      // 简化网络测试，检查网络接口是否正常
      const networkInterfaces = os.networkInterfaces()
      const hasActiveInterface = Object.values(networkInterfaces)
        .flat()
        .some(iface => !iface?.internal && iface?.address)
      
      return {
        name: '网络连通性测试',
        passed: hasActiveInterface,
        responseTime: 0,
        severity: hasActiveInterface ? 'info' : 'warning',
        message: hasActiveInterface ? '网络接口正常' : '网络接口异常',
        details: { hasActiveInterface }
      }
    } catch (error) {
      return {
        name: '网络连通性测试',
        passed: false,
        responseTime: 0,
        severity: 'warning',
        message: '网络测试失败',
        details: { error: error instanceof Error ? error.message : '未知错误' }
      }
    }
  }

  private async testApplicationHealth() {
    const memUsage = process.memoryUsage()
    const heapUsed = memUsage.heapUsed / memUsage.heapTotal
    const passed = heapUsed < 0.9
    
    return {
      name: '应用健康测试',
      passed,
      responseTime: 0,
      severity: heapUsed > 0.95 ? 'critical' : heapUsed > 0.8 ? 'warning' : 'info',
      message: passed ? '应用运行正常' : '应用内存使用率过高',
      details: {
        heapUsage: Math.round(heapUsed * 100),
        uptime: process.uptime(),
        version: process.version
      }
    }
  }

  /**
   * 提取Top进程信息 - 充分利用 systeminformation 库数据
   */
  private extractTopProcesses(processList: any[]): Array<{
    pid: number
    name: string
    cpu: number
    memory: number
    state: string
    command: string
  }> {
    try {
      return processList
        .filter(proc => proc.pid && proc.name && proc.cpu !== undefined)
        .sort((a, b) => (b.cpu || 0) - (a.cpu || 0)) // 按CPU使用率排序
        .slice(0, 10) // 取前10个
        .map(proc => ({
          pid: proc.pid,
          name: proc.name || 'unknown',
          cpu: Math.round((proc.cpu || 0) * 100) / 100, // CPU使用率百分比
          memory: Math.round((proc.mem || 0) * 100) / 100, // 内存使用率百分比
          state: proc.state || 'unknown',
          command: (proc.command || proc.name || '').slice(0, 50) // 限制命令长度
        }))
    } catch (error) {
      console.error('提取Top进程信息失败:', error)
      return []
    }
  }

  /**
   * AI辅助异常检测（使用简单的统计模型）
   */
  private performAIAnomalyDetection(currentValue: number, historicalValues: number[]): {
    isAnomaly: boolean
    confidence: number
    expectedRange: [number, number]
  } {
    if (historicalValues.length < 20) {
      return { isAnomaly: false, confidence: 0, expectedRange: [0, 100] }
    }

    // 计算统计指标
    const mean = historicalValues.reduce((a, b) => a + b, 0) / historicalValues.length
    const variance = historicalValues.reduce((a, b) => a + Math.pow(b - mean, 2), 0) / historicalValues.length
    const stdDev = Math.sqrt(variance)

    // 使用3-sigma规则检测异常
    const lowerBound = mean - 2 * stdDev
    const upperBound = mean + 2 * stdDev
    
    const isAnomaly = currentValue < lowerBound || currentValue > upperBound
    const deviationScore = Math.abs(currentValue - mean) / stdDev
    const confidence = Math.min(deviationScore / 3, 1) // 归一化到0-1

    return {
      isAnomaly,
      confidence,
      expectedRange: [Math.max(0, lowerBound), Math.min(100, upperBound)]
    }
  }

  /**
   * 性能趋势预测（使用线性回归）
   */
  private predictPerformanceTrend(values: number[], hoursAhead: number = 24): {
    predictedValue: number
    confidence: number
    trend: 'increasing' | 'decreasing' | 'stable'
  } {
    if (values.length < 10) {
      return { predictedValue: values[values.length - 1] || 0, confidence: 0, trend: 'stable' }
    }

    // 简单线性回归
    const n = values.length
    const x = Array.from({length: n}, (_, i) => i)
    const y = values
    
    const xMean = x.reduce((a, b) => a + b, 0) / n
    const yMean = y.reduce((a, b) => a + b, 0) / n
    
    let numerator = 0
    let denominator = 0
    
    for (let i = 0; i < n; i++) {
      numerator += (x[i] - xMean) * (y[i] - yMean)
      denominator += (x[i] - xMean) ** 2
    }
    
    const slope = denominator === 0 ? 0 : numerator / denominator
    const intercept = yMean - slope * xMean
    
    // 预测未来值
    const predictedValue = Math.max(0, Math.min(100, slope * (n + hoursAhead - 1) + intercept))
    
    // 计算R²作为置信度
    let totalSumSquares = 0
    let residualSumSquares = 0
    
    for (let i = 0; i < n; i++) {
      const predicted = slope * i + intercept
      residualSumSquares += (y[i] - predicted) ** 2
      totalSumSquares += (y[i] - yMean) ** 2
    }
    
    const rSquared = totalSumSquares === 0 ? 0 : 1 - (residualSumSquares / totalSumSquares)
    const confidence = Math.max(0, Math.min(1, rSquared))
    
    let trend: 'increasing' | 'decreasing' | 'stable' = 'stable'
    if (Math.abs(slope) > 0.5) {
      trend = slope > 0 ? 'increasing' : 'decreasing'
    }
    
    return { predictedValue, confidence, trend }
  }

  /**
   * 查找重要事件
   */
  private findSignificantEvents(data: { timestamp: string; value: number }[]) {
    const events = []
    
    if (data.length < 10) return events
    
    // 计算移动平均和标准差
    const windowSize = Math.min(10, data.length)
    
    for (let i = windowSize; i < data.length; i++) {
      const window = data.slice(i - windowSize, i)
      const mean = window.reduce((sum, d) => sum + d.value, 0) / windowSize
      const stdDev = Math.sqrt(
        window.reduce((sum, d) => sum + Math.pow(d.value - mean, 2), 0) / windowSize
      )
      
      const current = data[i]
      const deviation = Math.abs(current.value - mean)
      
      // 如果偏差超过2个标准差，认为是重要事件
      if (deviation > stdDev * 2) {
        events.push({
          timestamp: current.timestamp,
          type: current.value > mean ? 'spike' : 'drop',
          value: current.value,
          expectedValue: mean,
          deviation,
          description: `${current.value > mean ? '峰值' : '低谷'}事件：值为${current.value}，预期${Math.round(mean)}`
        })
      }
    }
    
    return events.slice(-10) // 返回最近的10个事件
  }

  /**
   * 健康评分算法优化
   */
  private calculateAdvancedHealthScore(metrics: SystemMetrics, historical: SystemMetrics[]): {
    score: number
    components: Record<string, { score: number; weight: number; impact: string }>
  } {
    const components = {
      cpu: { score: 0, weight: 0.25, impact: 'performance' },
      memory: { score: 0, weight: 0.25, impact: 'stability' },
      disk: { score: 0, weight: 0.2, impact: 'availability' },
      network: { score: 0, weight: 0.1, impact: 'connectivity' },
      processes: { score: 0, weight: 0.1, impact: 'reliability' },
      stability: { score: 0, weight: 0.1, impact: 'overall' }
    }

    // CPU评分
    if (metrics.cpu.usage <= 50) components.cpu.score = 100
    else if (metrics.cpu.usage <= 70) components.cpu.score = 90 - (metrics.cpu.usage - 50) * 1.5
    else if (metrics.cpu.usage <= 85) components.cpu.score = 60 - (metrics.cpu.usage - 70) * 2
    else components.cpu.score = Math.max(0, 30 - (metrics.cpu.usage - 85) * 3)

    // 内存评分
    if (metrics.memory.usage <= 60) components.memory.score = 100
    else if (metrics.memory.usage <= 80) components.memory.score = 90 - (metrics.memory.usage - 60) * 1.5
    else if (metrics.memory.usage <= 90) components.memory.score = 60 - (metrics.memory.usage - 80) * 2
    else components.memory.score = Math.max(0, 40 - (metrics.memory.usage - 90) * 4)

    // 磁盘评分
    if (metrics.disk.usage <= 70) components.disk.score = 100
    else if (metrics.disk.usage <= 85) components.disk.score = 90 - (metrics.disk.usage - 70) * 2
    else if (metrics.disk.usage <= 95) components.disk.score = 60 - (metrics.disk.usage - 85) * 3
    else components.disk.score = Math.max(0, 30 - (metrics.disk.usage - 95) * 6)

    // 网络评分（基于速度和延迟）
    components.network.score = 85 // 简化评分

    // 进程评分
    let processScore = 100
    if (metrics.processes.zombie > 0) processScore -= 20
    if (metrics.processes.total > 500) processScore -= 10
    components.processes.score = Math.max(0, processScore)

    // 稳定性评分（基于历史数据的方差）
    if (historical.length > 10) {
      const cpuValues = historical.slice(-24).map(m => m.cpu.usage)
      const cpuVariance = this.calculateVariance(cpuValues)
      components.stability.score = Math.max(0, 100 - cpuVariance)
    } else {
      components.stability.score = 80
    }

    // 计算加权总分
    const totalScore = Object.values(components).reduce(
      (sum, comp) => sum + comp.score * comp.weight, 0
    )

    return {
      score: Math.round(totalScore),
      components
    }
  }

  private calculateVariance(values: number[]): number {
    if (values.length === 0) return 0
    const mean = values.reduce((a, b) => a + b, 0) / values.length
    return values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length
  }
}