/**
 * 日志聚合器服务
 * 收集、存储和分析来自各个组件的日志
 */

import { EventEmitter } from 'events';
import type {
  ExecutionLog,
  ExecutionContext
} from '@/types/action-executor.types';

/**
 * 日志查询过滤器
 */
export interface LogFilter {
  executionId?: string;
  workflowId?: string;
  stepIndex?: number;
  level?: ExecutionLog['level'] | ExecutionLog['level'][];
  source?: string | string[];
  startTime?: Date;
  endTime?: Date;
  message?: string; // 支持正则表达式
  limit?: number;
  offset?: number;
}

/**
 * 日志统计信息
 */
export interface LogStatistics {
  totalLogs: number;
  levelDistribution: Map<ExecutionLog['level'], number>;
  sourceDistribution: Map<string, number>;
  timeRange: {
    earliest: Date;
    latest: Date;
  };
  topErrors: Array<{
    message: string;
    count: number;
    level: ExecutionLog['level'];
  }>;
  executionDistribution: Map<string, number>;
}

/**
 * 日志聚合结果
 */
export interface LogAggregation {
  logs: ExecutionLog[];
  totalCount: number;
  hasMore: boolean;
  aggregations?: {
    byLevel: Map<ExecutionLog['level'], number>;
    bySource: Map<string, number>;
    byTimeWindow: Map<string, number>;
  };
}

/**
 * 日志导出配置
 */
export interface LogExportConfig {
  format: 'json' | 'csv' | 'txt';
  filter: LogFilter;
  includeMetadata: boolean;
  compression?: boolean;
}

/**
 * 日志聚合器服务
 */
export class LogAggregatorService extends EventEmitter {
  private static instance: LogAggregatorService;
  private logs: ExecutionLog[] = [];
  private logsByExecution = new Map<string, ExecutionLog[]>();
  private logsByWorkflow = new Map<string, ExecutionLog[]>();
  private logsBySource = new Map<string, ExecutionLog[]>();
  private maxLogSize = 50000; // 最多存储50000条日志
  private isInitialized = false;

  constructor() {
    super();
  }

  /**
   * 获取单例实例
   */
  static getInstance(): LogAggregatorService {
    if (!LogAggregatorService.instance) {
      LogAggregatorService.instance = new LogAggregatorService();
    }
    return LogAggregatorService.instance;
  }

  /**
   * 初始化日志聚合器
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      console.log('🚀 初始化LogAggregatorService...');

      this.setupCleanupTimer();
      this.isInitialized = true;

      console.log('✅ LogAggregatorService初始化完成');
      this.emit('initialized');

    } catch (error) {
      console.error('❌ LogAggregatorService初始化失败:', error);
      throw error;
    }
  }

  /**
   * 添加日志
   */
  addLog(
    executionId: string,
    workflowId: string,
    stepIndex: number | undefined,
    log: ExecutionLog
  ): void {
    // 增强日志信息
    const enhancedLog: ExecutionLog = {
      ...log,
      metadata: {
        ...log.metadata,
        executionId,
        workflowId,
        stepIndex,
        logId: this.generateLogId()
      }
    };

    // 添加到主日志列表
    this.logs.push(enhancedLog);

    // 按执行ID分组
    if (!this.logsByExecution.has(executionId)) {
      this.logsByExecution.set(executionId, []);
    }
    this.logsByExecution.get(executionId)!.push(enhancedLog);

    // 按工作流ID分组
    if (!this.logsByWorkflow.has(workflowId)) {
      this.logsByWorkflow.set(workflowId, []);
    }
    this.logsByWorkflow.get(workflowId)!.push(enhancedLog);

    // 按来源分组
    const source = log.source || 'unknown';
    if (!this.logsBySource.has(source)) {
      this.logsBySource.set(source, []);
    }
    this.logsBySource.get(source)!.push(enhancedLog);

    // 限制日志大小
    this.maintainLogSize();

    // 发出事件
    this.emit('log:added', {
      executionId,
      workflowId,
      stepIndex,
      log: enhancedLog
    });

    // 如果是错误日志，发出特殊事件
    if (log.level === 'ERROR') {
      this.emit('log:error', {
        executionId,
        workflowId,
        stepIndex,
        log: enhancedLog
      });
    }
  }

  /**
   * 批量添加日志
   */
  addBatchLogs(
    executionId: string,
    workflowId: string,
    stepIndex: number | undefined,
    logs: ExecutionLog[]
  ): void {
    for (const log of logs) {
      this.addLog(executionId, workflowId, stepIndex, log);
    }

    this.emit('logs:batch_added', {
      executionId,
      workflowId,
      stepIndex,
      count: logs.length
    });
  }

  /**
   * 查询日志
   */
  queryLogs(filter: LogFilter = {}): LogAggregation {
    let filteredLogs = [...this.logs];

    // 应用过滤器
    if (filter.executionId) {
      filteredLogs = filteredLogs.filter(log => 
        log.metadata?.executionId === filter.executionId
      );
    }

    if (filter.workflowId) {
      filteredLogs = filteredLogs.filter(log => 
        log.metadata?.workflowId === filter.workflowId
      );
    }

    if (filter.stepIndex !== undefined) {
      filteredLogs = filteredLogs.filter(log => 
        log.metadata?.stepIndex === filter.stepIndex
      );
    }

    if (filter.level) {
      const levels = Array.isArray(filter.level) ? filter.level : [filter.level];
      filteredLogs = filteredLogs.filter(log => levels.includes(log.level));
    }

    if (filter.source) {
      const sources = Array.isArray(filter.source) ? filter.source : [filter.source];
      filteredLogs = filteredLogs.filter(log => 
        log.source && sources.includes(log.source)
      );
    }

    if (filter.startTime) {
      filteredLogs = filteredLogs.filter(log => 
        log.timestamp >= filter.startTime!
      );
    }

    if (filter.endTime) {
      filteredLogs = filteredLogs.filter(log => 
        log.timestamp <= filter.endTime!
      );
    }

    if (filter.message) {
      const regex = new RegExp(filter.message, 'i');
      filteredLogs = filteredLogs.filter(log => regex.test(log.message));
    }

    // 按时间排序（最新的在前）
    filteredLogs.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());

    const totalCount = filteredLogs.length;

    // 应用分页
    let offset = filter.offset || 0;
    let limit = filter.limit;
    let hasMore = false;

    if (limit) {
      hasMore = totalCount > offset + limit;
      filteredLogs = filteredLogs.slice(offset, offset + limit);
    } else if (offset > 0) {
      filteredLogs = filteredLogs.slice(offset);
    }

    // 生成聚合信息
    const aggregations = this.generateAggregations(filteredLogs);

    return {
      logs: filteredLogs,
      totalCount,
      hasMore,
      aggregations
    };
  }

  /**
   * 获取执行日志
   */
  getExecutionLogs(executionId: string): ExecutionLog[] {
    return this.logsByExecution.get(executionId) || [];
  }

  /**
   * 获取工作流日志
   */
  getWorkflowLogs(workflowId: string): ExecutionLog[] {
    return this.logsByWorkflow.get(workflowId) || [];
  }

  /**
   * 获取来源日志
   */
  getSourceLogs(source: string): ExecutionLog[] {
    return this.logsBySource.get(source) || [];
  }

  /**
   * 搜索日志
   */
  searchLogs(
    query: string,
    filter: Omit<LogFilter, 'message'> = {}
  ): LogAggregation {
    return this.queryLogs({
      ...filter,
      message: query
    });
  }

  /**
   * 获取日志统计
   */
  getLogStatistics(filter: LogFilter = {}): LogStatistics {
    const { logs } = this.queryLogs({ ...filter, limit: undefined, offset: undefined });

    const levelDistribution = new Map<ExecutionLog['level'], number>();
    const sourceDistribution = new Map<string, number>();
    const executionDistribution = new Map<string, number>();
    const errorMessages = new Map<string, number>();

    let earliest: Date | null = null;
    let latest: Date | null = null;

    for (const log of logs) {
      // 级别分布
      levelDistribution.set(log.level, (levelDistribution.get(log.level) || 0) + 1);

      // 来源分布
      const source = log.source || 'unknown';
      sourceDistribution.set(source, (sourceDistribution.get(source) || 0) + 1);

      // 执行分布
      const executionId = log.metadata?.executionId || 'unknown';
      executionDistribution.set(executionId, (executionDistribution.get(executionId) || 0) + 1);

      // 错误消息统计
      if (log.level === 'ERROR') {
        errorMessages.set(log.message, (errorMessages.get(log.message) || 0) + 1);
      }

      // 时间范围
      if (!earliest || log.timestamp < earliest) {
        earliest = log.timestamp;
      }
      if (!latest || log.timestamp > latest) {
        latest = log.timestamp;
      }
    }

    // 获取最常见的错误
    const topErrors = Array.from(errorMessages.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 10)
      .map(([message, count]) => ({
        message,
        count,
        level: 'ERROR' as const
      }));

    return {
      totalLogs: logs.length,
      levelDistribution,
      sourceDistribution,
      timeRange: {
        earliest: earliest || new Date(),
        latest: latest || new Date()
      },
      topErrors,
      executionDistribution
    };
  }

  /**
   * 导出日志
   */
  async exportLogs(config: LogExportConfig): Promise<{
    data: string;
    filename: string;
    contentType: string;
  }> {
    const { logs } = this.queryLogs(config.filter);

    let data: string;
    let contentType: string;
    let extension: string;

    switch (config.format) {
      case 'json':
        data = JSON.stringify(
          config.includeMetadata 
            ? logs 
            : logs.map(({ metadata, ...log }) => log),
          null, 2
        );
        contentType = 'application/json';
        extension = 'json';
        break;

      case 'csv':
        data = this.convertToCSV(logs, config.includeMetadata);
        contentType = 'text/csv';
        extension = 'csv';
        break;

      case 'txt':
        data = this.convertToText(logs, config.includeMetadata);
        contentType = 'text/plain';
        extension = 'txt';
        break;

      default:
        throw new Error(`不支持的导出格式: ${config.format}`);
    }

    const timestamp = new Date().toISOString().split('T')[0];
    const filename = `logs_export_${timestamp}.${extension}`;

    return { data, filename, contentType };
  }

  /**
   * 清除日志
   */
  clearLogs(filter?: LogFilter): number {
    if (!filter) {
      // 清除所有日志
      const count = this.logs.length;
      this.logs = [];
      this.logsByExecution.clear();
      this.logsByWorkflow.clear();
      this.logsBySource.clear();
      
      this.emit('logs:cleared', { count });
      return count;
    }

    // 根据过滤器清除日志
    const { logs: logsToRemove } = this.queryLogs(filter);
    const logIdsToRemove = new Set(
      logsToRemove.map(log => log.metadata?.logId).filter(Boolean)
    );

    // 从主列表中移除
    this.logs = this.logs.filter(log => !logIdsToRemove.has(log.metadata?.logId));

    // 从分组中移除
    this.rebuildLogGroups();

    this.emit('logs:filtered_clear', { count: logsToRemove.length });
    return logsToRemove.length;
  }

  /**
   * 获取实时日志流
   */
  streamLogs(filter: LogFilter = {}): EventEmitter {
    const stream = new EventEmitter();
    
    const handleNewLog = (event: any) => {
      const { log } = event;
      
      // 检查日志是否符合过滤条件
      if (this.logMatchesFilter(log, filter)) {
        stream.emit('log', log);
      }
    };

    this.on('log:added', handleNewLog);

    // 返回取消流的方法
    stream.on('close', () => {
      this.removeListener('log:added', handleNewLog);
    });

    return stream;
  }

  // ========== 私有方法 ==========

  /**
   * 生成日志ID
   */
  private generateLogId(): string {
    return `log_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 维护日志大小
   */
  private maintainLogSize(): void {
    if (this.logs.length > this.maxLogSize) {
      // 移除最旧的日志
      const removeCount = this.logs.length - this.maxLogSize;
      const removedLogs = this.logs.splice(0, removeCount);
      
      // 重建分组索引
      this.rebuildLogGroups();
      
      this.emit('logs:size_limited', { removedCount: removeCount });
    }
  }

  /**
   * 重建日志分组
   */
  private rebuildLogGroups(): void {
    this.logsByExecution.clear();
    this.logsByWorkflow.clear();
    this.logsBySource.clear();

    for (const log of this.logs) {
      const executionId = log.metadata?.executionId;
      const workflowId = log.metadata?.workflowId;
      const source = log.source || 'unknown';

      if (executionId) {
        if (!this.logsByExecution.has(executionId)) {
          this.logsByExecution.set(executionId, []);
        }
        this.logsByExecution.get(executionId)!.push(log);
      }

      if (workflowId) {
        if (!this.logsByWorkflow.has(workflowId)) {
          this.logsByWorkflow.set(workflowId, []);
        }
        this.logsByWorkflow.get(workflowId)!.push(log);
      }

      if (!this.logsBySource.has(source)) {
        this.logsBySource.set(source, []);
      }
      this.logsBySource.get(source)!.push(log);
    }
  }

  /**
   * 生成聚合信息
   */
  private generateAggregations(logs: ExecutionLog[]): {
    byLevel: Map<ExecutionLog['level'], number>;
    bySource: Map<string, number>;
    byTimeWindow: Map<string, number>;
  } {
    const byLevel = new Map<ExecutionLog['level'], number>();
    const bySource = new Map<string, number>();
    const byTimeWindow = new Map<string, number>();

    for (const log of logs) {
      // 按级别聚合
      byLevel.set(log.level, (byLevel.get(log.level) || 0) + 1);

      // 按来源聚合
      const source = log.source || 'unknown';
      bySource.set(source, (bySource.get(source) || 0) + 1);

      // 按时间窗口聚合（小时）
      const timeWindow = log.timestamp.toISOString().substring(0, 13) + ':00:00';
      byTimeWindow.set(timeWindow, (byTimeWindow.get(timeWindow) || 0) + 1);
    }

    return { byLevel, bySource, byTimeWindow };
  }

  /**
   * 检查日志是否匹配过滤器
   */
  private logMatchesFilter(log: ExecutionLog, filter: LogFilter): boolean {
    if (filter.executionId && log.metadata?.executionId !== filter.executionId) {
      return false;
    }

    if (filter.workflowId && log.metadata?.workflowId !== filter.workflowId) {
      return false;
    }

    if (filter.stepIndex !== undefined && log.metadata?.stepIndex !== filter.stepIndex) {
      return false;
    }

    if (filter.level) {
      const levels = Array.isArray(filter.level) ? filter.level : [filter.level];
      if (!levels.includes(log.level)) {
        return false;
      }
    }

    if (filter.source) {
      const sources = Array.isArray(filter.source) ? filter.source : [filter.source];
      if (!log.source || !sources.includes(log.source)) {
        return false;
      }
    }

    if (filter.startTime && log.timestamp < filter.startTime) {
      return false;
    }

    if (filter.endTime && log.timestamp > filter.endTime) {
      return false;
    }

    if (filter.message) {
      const regex = new RegExp(filter.message, 'i');
      if (!regex.test(log.message)) {
        return false;
      }
    }

    return true;
  }

  /**
   * 转换为CSV格式
   */
  private convertToCSV(logs: ExecutionLog[], includeMetadata: boolean): string {
    if (logs.length === 0) return '';

    const headers = ['timestamp', 'level', 'message', 'source'];
    if (includeMetadata) {
      headers.push('executionId', 'workflowId', 'stepIndex', 'metadata');
    }

    const csvLines = [headers.join(',')];

    for (const log of logs) {
      const row = [
        log.timestamp.toISOString(),
        log.level,
        `"${log.message.replace(/"/g, '""')}"`,
        log.source || ''
      ];

      if (includeMetadata) {
        row.push(
          log.metadata?.executionId || '',
          log.metadata?.workflowId || '',
          log.metadata?.stepIndex?.toString() || '',
          `"${JSON.stringify(log.metadata || {}).replace(/"/g, '""')}"`
        );
      }

      csvLines.push(row.join(','));
    }

    return csvLines.join('\n');
  }

  /**
   * 转换为文本格式
   */
  private convertToText(logs: ExecutionLog[], includeMetadata: boolean): string {
    return logs.map(log => {
      let line = `[${log.timestamp.toISOString()}] ${log.level}: ${log.message}`;
      
      if (log.source) {
        line += ` (${log.source})`;
      }

      if (includeMetadata && log.metadata) {
        line += `\n  Metadata: ${JSON.stringify(log.metadata, null, 2)}`;
      }

      return line;
    }).join('\n\n');
  }

  /**
   * 设置清理定时器
   */
  private setupCleanupTimer(): void {
    // 每小时清理一次过期日志
    setInterval(() => {
      this.cleanupExpiredLogs();
    }, 60 * 60 * 1000);
  }

  /**
   * 清理过期日志
   */
  private cleanupExpiredLogs(): void {
    const now = new Date();
    const maxAge = 7 * 24 * 60 * 60 * 1000; // 7天
    const cutoffTime = new Date(now.getTime() - maxAge);

    const originalCount = this.logs.length;
    this.logs = this.logs.filter(log => log.timestamp >= cutoffTime);
    
    if (this.logs.length < originalCount) {
      this.rebuildLogGroups();
      const removedCount = originalCount - this.logs.length;
      
      console.log(`🧹 清理了 ${removedCount} 条过期日志`);
      this.emit('logs:expired_cleanup', { removedCount });
    }
  }

  /**
   * 全局清理
   */
  async cleanup(): Promise<void> {
    console.log('🧹 开始清理LogAggregatorService...');
    
    this.logs = [];
    this.logsByExecution.clear();
    this.logsByWorkflow.clear();
    this.logsBySource.clear();
    this.isInitialized = false;
    
    console.log('✅ LogAggregatorService清理完成');
    this.emit('cleanup:completed');
  }
}

// 导出单例实例
export const logAggregator = LogAggregatorService.getInstance();