/**
 * 执行状态跟踪器
 * 负责跟踪和记录工作流步骤的执行状态和历史
 */

import { EventEmitter } from 'events';
import { prisma } from '@/config/database.config';
import { metricsCollector } from './metrics-collector.service';
import { logAggregator } from './log-aggregator.service';
import type {
  IStateTracker,
  ExecutionState,
  ExecutionStatus,
  ExecutionLog,
  ExecutionError,
  ActionExecutionResult
} from '@/types/action-executor.types';

/**
 * 执行状态跟踪器实现
 */
export class ExecutionStateTracker extends EventEmitter implements IStateTracker {
  private static instance: ExecutionStateTracker;
  private memoryCache = new Map<string, ExecutionState>(); // 内存缓存用于快速访问
  private maxCacheSize = 1000; // 最大缓存大小

  constructor() {
    super();
    this.setupCleanupTimer();
  }

  /**
   * 获取单例实例
   */
  static getInstance(): ExecutionStateTracker {
    if (!ExecutionStateTracker.instance) {
      ExecutionStateTracker.instance = new ExecutionStateTracker();
    }
    return ExecutionStateTracker.instance;
  }

  /**
   * 开始执行跟踪
   */
  async startExecution(executionId: string, workflowId: string, stepIndex: number): Promise<void> {
    try {
      const cacheKey = this.getCacheKey(executionId, stepIndex);
      const startTime = new Date();

      // 创建执行状态
      const state: ExecutionState = {
        executionId,
        workflowId,
        stepIndex,
        status: ExecutionStatus.PENDING,
        startTime,
        logs: [],
        retryCount: 0
      };

      // 更新内存缓存
      this.updateCache(cacheKey, state);

      // 记录到数据库
      await this.saveStateToDatabase(state);

      // 发出事件
      this.emit('execution:started', {
        executionId,
        workflowId,
        stepIndex,
        startTime
      });

      console.log(`📊 开始跟踪执行: ${executionId}:${stepIndex}`);

    } catch (error) {
      console.error(`❌ 开始执行跟踪失败:`, error);
      this.emit('tracking:error', { executionId, stepIndex, error });
      throw error;
    }
  }

  /**
   * 更新执行状态
   */
  async updateStatus(executionId: string, stepIndex: number, status: ExecutionStatus): Promise<void> {
    try {
      const cacheKey = this.getCacheKey(executionId, stepIndex);
      const state = this.memoryCache.get(cacheKey);

      if (!state) {
        console.warn(`⚠️ 执行状态不存在: ${cacheKey}`);
        return;
      }

      // 更新状态
      state.status = status;
      
      if (status === ExecutionStatus.RUNNING) {
        state.startTime = new Date();
      } else if (status === ExecutionStatus.COMPLETED || status === ExecutionStatus.FAILED) {
        state.endTime = new Date();
        state.duration = (state.endTime.getTime() - state.startTime.getTime()) / 1000;
      }

      // 更新缓存
      this.updateCache(cacheKey, state);

      // 更新数据库
      await this.updateStateInDatabase(state);

      // 发出事件
      this.emit('execution:status:updated', {
        executionId,
        stepIndex,
        status,
        timestamp: new Date()
      });

      console.log(`📊 更新执行状态: ${cacheKey} -> ${status}`);

    } catch (error) {
      console.error(`❌ 更新执行状态失败:`, error);
      this.emit('tracking:error', { executionId, stepIndex, error });
      throw error;
    }
  }

  /**
   * 完成执行跟踪
   */
  async completeExecution(
    executionId: string, 
    stepIndex: number, 
    result: ActionExecutionResult
  ): Promise<void> {
    try {
      const cacheKey = this.getCacheKey(executionId, stepIndex);
      const state = this.memoryCache.get(cacheKey);

      if (!state) {
        console.warn(`⚠️ 执行状态不存在: ${cacheKey}`);
        return;
      }

      // 更新完成状态
      state.status = result.success ? ExecutionStatus.COMPLETED : ExecutionStatus.FAILED;
      state.endTime = new Date();
      state.duration = (state.endTime.getTime() - state.startTime.getTime()) / 1000;
      state.result = result;

      if (!result.success && result.error) {
        state.error = result.error;
      }

      // 添加结果日志
      state.logs.push(...result.logs);
      
      // 记录回滚数据
      if (result.rollbackData) {
        state.rollbackData = result.rollbackData;
      }

      // 记录指标和日志到聚合器
      metricsCollector.recordExecution(result);
      logAggregator.addBatchLogs(executionId, state.workflowId, stepIndex, result.logs);

      // 更新缓存
      this.updateCache(cacheKey, state);

      // 更新数据库
      await this.updateStateInDatabase(state);

      // 发出事件
      this.emit('execution:completed', {
        executionId,
        stepIndex,
        success: result.success,
        duration: state.duration,
        timestamp: state.endTime
      });

      console.log(`📊 完成执行跟踪: ${cacheKey} (${result.success ? '成功' : '失败'})`);

    } catch (error) {
      console.error(`❌ 完成执行跟踪失败:`, error);
      this.emit('tracking:error', { executionId, stepIndex, error });
      throw error;
    }
  }

  /**
   * 记录执行失败
   */
  async failExecution(
    executionId: string, 
    stepIndex: number, 
    error: ExecutionError
  ): Promise<void> {
    try {
      const cacheKey = this.getCacheKey(executionId, stepIndex);
      const state = this.memoryCache.get(cacheKey);

      if (!state) {
        console.warn(`⚠️ 执行状态不存在: ${cacheKey}`);
        return;
      }

      // 更新失败状态
      state.status = ExecutionStatus.FAILED;
      state.endTime = new Date();
      state.duration = (state.endTime.getTime() - state.startTime.getTime()) / 1000;
      state.error = error;

      // 添加错误日志
      const errorLog: ExecutionLog = {
        level: 'ERROR',
        message: `步骤执行失败: ${error.message}`,
        timestamp: new Date(),
        metadata: { error: error.details },
        source: 'ExecutionStateTracker'
      };
      state.logs.push(errorLog);

      // 记录错误指标和日志到聚合器
      metricsCollector.recordError(error);
      logAggregator.addLog(executionId, state.workflowId, stepIndex, errorLog);

      // 更新缓存
      this.updateCache(cacheKey, state);

      // 更新数据库
      await this.updateStateInDatabase(state);

      // 发出事件
      this.emit('execution:failed', {
        executionId,
        stepIndex,
        error,
        duration: state.duration,
        timestamp: state.endTime
      });

      console.log(`📊 记录执行失败: ${cacheKey} - ${error.message}`);

    } catch (err) {
      console.error(`❌ 记录执行失败异常:`, err);
      this.emit('tracking:error', { executionId, stepIndex, error: err });
      throw err;
    }
  }

  /**
   * 添加执行日志
   */
  async addLog(executionId: string, stepIndex: number, log: ExecutionLog): Promise<void> {
    try {
      const cacheKey = this.getCacheKey(executionId, stepIndex);
      const state = this.memoryCache.get(cacheKey);

      if (!state) {
        console.warn(`⚠️ 执行状态不存在: ${cacheKey}`);
        return;
      }

      // 添加日志
      state.logs.push(log);

      // 记录日志到聚合器
      logAggregator.addLog(executionId, state.workflowId, stepIndex, log);

      // 限制日志数量（保留最新的100条）
      if (state.logs.length > 100) {
        state.logs = state.logs.slice(-100);
      }

      // 更新缓存
      this.updateCache(cacheKey, state);

      // 异步更新数据库（避免频繁写入影响性能）
      this.updateStateInDatabase(state).catch(error => {
        console.error(`更新数据库日志失败:`, error);
      });

      // 发出事件
      this.emit('execution:log:added', {
        executionId,
        stepIndex,
        log
      });

    } catch (error) {
      console.error(`❌ 添加执行日志失败:`, error);
      this.emit('tracking:error', { executionId, stepIndex, error });
    }
  }

  /**
   * 获取执行状态
   */
  async getExecutionState(executionId: string, stepIndex: number): Promise<ExecutionState | null> {
    try {
      const cacheKey = this.getCacheKey(executionId, stepIndex);
      
      // 优先从缓存获取
      const cachedState = this.memoryCache.get(cacheKey);
      if (cachedState) {
        return { ...cachedState }; // 返回副本
      }

      // 从数据库获取
      const state = await this.getStateFromDatabase(executionId, stepIndex);
      if (state) {
        this.updateCache(cacheKey, state);
      }

      return state;

    } catch (error) {
      console.error(`❌ 获取执行状态失败:`, error);
      this.emit('tracking:error', { executionId, stepIndex, error });
      return null;
    }
  }

  /**
   * 列出执行记录
   */
  async listExecutions(workflowId?: string): Promise<ExecutionState[]> {
    try {
      // 从内存缓存获取
      const cachedStates = Array.from(this.memoryCache.values());
      const filteredStates = workflowId 
        ? cachedStates.filter(state => state.workflowId === workflowId)
        : cachedStates;

      // 如果缓存不为空，返回缓存结果
      if (filteredStates.length > 0) {
        return filteredStates.map(state => ({ ...state })); // 返回副本
      }

      // 从数据库获取
      return await this.getStatesFromDatabase(workflowId);

    } catch (error) {
      console.error(`❌ 列出执行记录失败:`, error);
      this.emit('tracking:error', { workflowId, error });
      return [];
    }
  }

  /**
   * 增加重试计数
   */
  async incrementRetryCount(executionId: string, stepIndex: number): Promise<void> {
    try {
      const cacheKey = this.getCacheKey(executionId, stepIndex);
      const state = this.memoryCache.get(cacheKey);

      if (!state) {
        console.warn(`⚠️ 执行状态不存在: ${cacheKey}`);
        return;
      }

      state.retryCount++;
      state.status = ExecutionStatus.RETRYING;

      // 添加重试日志
      const retryLog: ExecutionLog = {
        level: 'WARN',
        message: `第 ${state.retryCount} 次重试`,
        timestamp: new Date(),
        metadata: { retryCount: state.retryCount },
        source: 'ExecutionStateTracker'
      };
      state.logs.push(retryLog);

      // 记录重试日志到聚合器
      logAggregator.addLog(executionId, state.workflowId, stepIndex, retryLog);

      // 更新缓存和数据库
      this.updateCache(cacheKey, state);
      await this.updateStateInDatabase(state);

      this.emit('execution:retry', {
        executionId,
        stepIndex,
        retryCount: state.retryCount
      });

    } catch (error) {
      console.error(`❌ 增加重试计数失败:`, error);
      this.emit('tracking:error', { executionId, stepIndex, error });
    }
  }

  /**
   * 获取统计信息
   */
  getStatistics(): any {
    const states = Array.from(this.memoryCache.values());
    const totalExecutions = states.length;
    
    const statusStats = states.reduce((acc, state) => {
      acc[state.status] = (acc[state.status] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const avgDuration = states
      .filter(state => state.duration !== undefined)
      .reduce((acc, state) => acc + (state.duration || 0), 0) / 
      (states.filter(state => state.duration !== undefined).length || 1);

    const errorStats = states
      .filter(state => state.error)
      .reduce((acc, state) => {
        if (state.error) {
          acc[state.error.type] = (acc[state.error.type] || 0) + 1;
        }
        return acc;
      }, {} as Record<string, number>);

    return {
      totalExecutions,
      statusDistribution: statusStats,
      averageDuration: avgDuration,
      errorDistribution: errorStats,
      cacheSize: this.memoryCache.size,
      maxCacheSize: this.maxCacheSize
    };
  }

  /**
   * 清理过期状态
   */
  async cleanupExpiredStates(maxAgeHours: number = 24): Promise<number> {
    try {
      const cutoffTime = new Date(Date.now() - maxAgeHours * 60 * 60 * 1000);
      let cleanedCount = 0;

      // 清理内存缓存
      for (const [key, state] of this.memoryCache.entries()) {
        if (state.startTime < cutoffTime && 
            (state.status === ExecutionStatus.COMPLETED || state.status === ExecutionStatus.FAILED)) {
          this.memoryCache.delete(key);
          cleanedCount++;
        }
      }

      // 清理数据库中的过期记录（仅清理已完成的记录）
      const dbCleanedCount = await this.cleanupDatabaseStates(cutoffTime);

      console.log(`🧹 清理过期状态: 内存${cleanedCount}条, 数据库${dbCleanedCount}条`);
      
      this.emit('cleanup:completed', {
        memoryCleaned: cleanedCount,
        databaseCleaned: dbCleanedCount,
        totalCleaned: cleanedCount + dbCleanedCount
      });

      return cleanedCount + dbCleanedCount;

    } catch (error) {
      console.error(`❌ 清理过期状态失败:`, error);
      this.emit('cleanup:error', { error });
      return 0;
    }
  }

  /**
   * 全局清理
   */
  async cleanup(): Promise<void> {
    console.log('🧹 开始清理 ExecutionStateTracker...');
    
    this.memoryCache.clear();
    
    console.log('✅ ExecutionStateTracker 清理完成');
    this.emit('cleanup:completed');
  }

  // ========== 私有方法 ==========

  /**
   * 获取缓存键
   */
  private getCacheKey(executionId: string, stepIndex: number): string {
    return `${executionId}:${stepIndex}`;
  }

  /**
   * 更新缓存
   */
  private updateCache(key: string, state: ExecutionState): void {
    // 如果缓存已满，删除最旧的条目
    if (this.memoryCache.size >= this.maxCacheSize) {
      const firstKey = this.memoryCache.keys().next().value;
      if (firstKey) {
        this.memoryCache.delete(firstKey);
      }
    }

    this.memoryCache.set(key, state);
  }

  /**
   * 保存状态到数据库
   */
  private async saveStateToDatabase(state: ExecutionState): Promise<void> {
    try {
      // 这里应该保存到实际的数据库表
      // 由于WorkflowExecutionStep已存在，我们可以使用它
      await prisma.workflowExecutionStep.create({
        data: {
          executionId: state.executionId, // 这里需要获取WorkflowExecution的实际ID
          stepIndex: state.stepIndex,
          stepName: `Step ${state.stepIndex}`,
          stepType: 'ACTION', // 默认类型
          status: this.mapStatusToPrismaStatus(state.status),
          config: {},
          startedAt: state.startTime,
          logs: state.logs
        }
      });
    } catch (error) {
      // 如果数据库操作失败，记录错误但不抛出异常
      console.error('保存状态到数据库失败:', error);
    }
  }

  /**
   * 从数据库更新状态
   */
  private async updateStateInDatabase(state: ExecutionState): Promise<void> {
    try {
      // 这里应该更新数据库中的记录
      // 实际实现时需要根据具体的数据库结构调整
      console.log('更新数据库状态:', state.executionId, state.stepIndex);
    } catch (error) {
      console.error('更新数据库状态失败:', error);
    }
  }

  /**
   * 从数据库获取状态
   */
  private async getStateFromDatabase(executionId: string, stepIndex: number): Promise<ExecutionState | null> {
    try {
      // 这里应该从数据库查询记录
      // 实际实现时需要根据具体的数据库结构调整
      return null;
    } catch (error) {
      console.error('从数据库获取状态失败:', error);
      return null;
    }
  }

  /**
   * 从数据库获取多个状态
   */
  private async getStatesFromDatabase(workflowId?: string): Promise<ExecutionState[]> {
    try {
      // 这里应该从数据库查询多个记录
      return [];
    } catch (error) {
      console.error('从数据库获取状态列表失败:', error);
      return [];
    }
  }

  /**
   * 清理数据库中的过期状态
   */
  private async cleanupDatabaseStates(cutoffTime: Date): Promise<number> {
    try {
      // 这里应该删除数据库中的过期记录
      return 0;
    } catch (error) {
      console.error('清理数据库状态失败:', error);
      return 0;
    }
  }

  /**
   * 映射状态到Prisma状态
   */
  private mapStatusToPrismaStatus(status: ExecutionStatus): any {
    switch (status) {
      case ExecutionStatus.PENDING:
        return 'PENDING';
      case ExecutionStatus.RUNNING:
        return 'RUNNING';
      case ExecutionStatus.COMPLETED:
        return 'COMPLETED';
      case ExecutionStatus.FAILED:
        return 'FAILED';
      case ExecutionStatus.SKIPPED:
        return 'SKIPPED';
      default:
        return 'PENDING';
    }
  }

  /**
   * 设置清理定时器
   */
  private setupCleanupTimer(): void {
    // 每小时执行一次清理
    setInterval(async () => {
      try {
        await this.cleanupExpiredStates(24); // 清理24小时前的记录
      } catch (error) {
        console.error('定时清理失败:', error);
      }
    }, 60 * 60 * 1000); // 1小时
  }
}

// 导出单例实例
export const executionStateTracker = ExecutionStateTracker.getInstance();