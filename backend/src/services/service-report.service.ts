import { PrismaClient } from '@prisma/client'
import puppeteer from 'puppeteer'
import Handlebars from 'handlebars'
import moment from 'moment'
import path from 'path'
import fs from 'fs/promises'
import { calculateServiceSlaStatus } from '../utils/sla-monitor.util'
import { ChartJSNodeCanvas } from 'chartjs-node-canvas'
import 'chart.js/auto'

const prisma = new PrismaClient()

// 设置中文时间格式
moment.locale('zh-cn')

export interface ServiceReportData {
  service: any
  slaMetrics: any
  timeline: any[]
  statistics: {
    totalWorkHours: number
    responseTime: number
    resolutionTime: number
    commentsCount: number
    attachmentsCount: number
  }
  company: {
    name: string
    logo: string
    address: string
    phone: string
    website: string
  }
  charts?: {
    responseDonut?: string
    resolutionDonut?: string
    slaBars?: string
  }
  stamp?: {
    enabled: boolean
    dataUrl: string
  }
}

export class ServiceReportService {
  private static instance: ServiceReportService
  private templatesPath: string
  private assetsPath: string

  constructor() {
    this.templatesPath = path.join(__dirname, '../templates/reports')
    this.assetsPath = path.join(__dirname, '../assets')
    // 立即注册 Handlebars 助手，避免异步初始化导致模板渲染时找不到助手
    this.registerHandlebarsHelpers()
    // 异步创建目录等非关键初始化
    this.initializeTemplates()
  }

  public static getInstance(): ServiceReportService {
    if (!ServiceReportService.instance) {
      ServiceReportService.instance = new ServiceReportService()
    }
    return ServiceReportService.instance
  }

  /**
   * 初始化模板和Handlebars助手
   */
  private async initializeTemplates() {
    try {
      // 确保模板目录存在
      await fs.mkdir(this.templatesPath, { recursive: true })
      await fs.mkdir(this.assetsPath, { recursive: true })
    } catch (error) {
      console.error('初始化模板失败:', error)
    }
  }

  /**
   * 注册Handlebars助手函数
   */
  private registerHandlebarsHelpers() {
    // 格式化日期（健壮化，兼容 SafeString、未定义、"now" 等）
    Handlebars.registerHelper('formatDate', (date: any, format: any) => {
      const defaultFmt = 'YYYY-MM-DD HH:mm:ss'
      const fmt = typeof format === 'string' ? format : (format && format.toString ? format.toString() : defaultFmt)

      // 将 SafeString/对象 转为普通字符串以便比较
      const dateStr = typeof date === 'string' ? date : (date && date.toString ? date.toString() : '')

      if (dateStr && dateStr.toLowerCase && dateStr.toLowerCase() === 'now') {
        return moment().format(fmt)
      }

      // 支持传入数字时间戳、ISO字符串、Date对象
      const value = (date instanceof Date || typeof date === 'number' || typeof date === 'string')
        ? date
        : undefined

      return moment(value).isValid() ? moment(value).format(fmt) : ''
    })

    // 格式化持续时间
    Handlebars.registerHelper('formatDuration', (minutes: number) => {
      if (!minutes) return '未记录'
      const hours = Math.floor(minutes / 60)
      const mins = minutes % 60
      if (hours > 0) {
        return `${hours}小时${mins > 0 ? mins + '分钟' : ''}`
      }
      return `${mins}分钟`
    })

    // 状态中文映射
    Handlebars.registerHelper('statusText', (status: string) => {
      const statusMap: Record<string, string> = {
        'PENDING': '待处理',
        'IN_PROGRESS': '处理中',
        'WAITING_CUSTOMER': '等待客户',
        'RESOLVED': '已解决',
        'CLOSED': '已关闭'
      }
      return statusMap[status] || status
    })

    // 优先级中文映射
    Handlebars.registerHelper('priorityText', (priority: string) => {
      const priorityMap: Record<string, string> = {
        'LOW': '低',
        'MEDIUM': '中',
        'HIGH': '高',
        'URGENT': '紧急'
      }
      return priorityMap[priority] || priority
    })

    // 类别中文映射
    Handlebars.registerHelper('categoryText', (category: string) => {
      const categoryMap: Record<string, string> = {
        'MAINTENANCE': '运维',
        'SUPPORT': '支持',
        'UPGRADE': '升级',
        'BUGFIX': '故障修复',
        'CONSULTING': '咨询',
        'MONITORING': '监控'
      }
      return categoryMap[category] || category
    })

    // 获取优先级颜色
    Handlebars.registerHelper('priorityColor', (priority: string) => {
      const colorMap: Record<string, string> = {
        'LOW': '#10b981',
        'MEDIUM': '#f59e0b',
        'HIGH': '#ef4444',
        'URGENT': '#dc2626'
      }
      return colorMap[priority] || '#6b7280'
    })

    // 获取状态颜色
    Handlebars.registerHelper('statusColor', (status: string) => {
      const colorMap: Record<string, string> = {
        'PENDING': '#f59e0b',
        'IN_PROGRESS': '#3b82f6',
        'WAITING_CUSTOMER': '#8b5cf6',
        'RESOLVED': '#10b981',
        'CLOSED': '#6b7280'
      }
      return colorMap[status] || '#6b7280'
    })

    // 条件判断
    Handlebars.registerHelper('eq', (a: any, b: any) => a === b)
    Handlebars.registerHelper('gt', (a: number, b: number) => a > b)
    Handlebars.registerHelper('lt', (a: number, b: number) => a < b)

    // JSON stringify
    Handlebars.registerHelper('json', (context: any) => {
      return JSON.stringify(context)
    })

    // 数学运算
    Handlebars.registerHelper('multiply', (a: number, b: number) => a * b)
    Handlebars.registerHelper('add', (a: number, b: number) => a + b)
    Handlebars.registerHelper('subtract', (a: number, b: number) => a - b)

    // 当前时间
    Handlebars.registerHelper('now', () => new Date())

    // 安全HTML渲染（移除危险标签）
    Handlebars.registerHelper('safeHtml', (html: string) => {
      if (!html) return ''
      
      // 简单的HTML清理，移除危险标签
      const cleaned = html
        .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
        .replace(/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi, '')
        .replace(/javascript:/gi, '')
        .replace(/on\w+\s*=\s*["'][^"']*["']/gi, '')
      
      return new Handlebars.SafeString(cleaned)
    })
  }

  /**
   * 生成服务工单PDF报告
   */
  public async generateServiceReport(serviceId: string): Promise<Buffer> {
    console.log(`🔄 开始生成工单报告: ${serviceId}`)

    try {
      // 1. 收集数据
      const reportData = await this.collectServiceData(serviceId)
      
      // 2. 渲染HTML
      const html = await this.renderReportTemplate(reportData)
      
      // 3. 生成PDF (带重试机制)
      let pdf: Buffer
      try {
        pdf = await this.generatePDF(html)
      } catch (pdfError) {
        console.warn(`⚠️ 第一次PDF生成失败，尝试备用方案: ${serviceId}`, pdfError)
        // 备用方案：使用更简单的配置
        pdf = await this.generatePDFFallback(html)
      }
      
      console.log(`✅ 工单报告生成完成: ${serviceId}`)
      return pdf
      
    } catch (error) {
      console.error(`❌ 生成工单报告失败: ${serviceId}`, error)
      throw new Error(`报告生成失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 收集服务工单数据
   */
  private async collectServiceData(serviceId: string): Promise<ServiceReportData> {
    // 获取完整的服务工单数据
    const service = await prisma.service.findUnique({
      where: { id: serviceId },
      include: {
        archive: {
          include: {
            customer: {
              select: {
                id: true,
                name: true,
                company: true,
                level: true,
                contactPerson: true,
                contactEmail: true,
                contactPhone: true,
                address: true
              }
            }
          }
        },
        assignedUser: {
          select: {
            id: true,
            username: true,
            fullName: true,
            email: true,
            phone: true,
            department: true
          }
        },
        createdByUser: {
          select: {
            id: true,
            username: true,
            fullName: true
          }
        },
        slaTemplate: true,
        workLogs: {
          include: {
            user: {
              select: {
                id: true,
                username: true,
                fullName: true
              }
            }
          },
          orderBy: { workDate: 'asc' }
        },
        attachments: {
          include: {
            uploader: {
              select: {
                id: true,
                username: true,
                fullName: true
              }
            }
          },
          orderBy: { uploadedAt: 'asc' }
        },
        comments: {
          include: {
            author: {
              select: {
                id: true,
                username: true,
                fullName: true
              }
            }
          },
          orderBy: { createdAt: 'asc' }
        }
      }
    })

    if (!service) {
      throw new Error(`服务工单不存在: ${serviceId}`)
    }

    // 计算SLA指标
    const slaMetrics = await calculateServiceSlaStatus(serviceId)

    // 生成SLA图表（如果有SLA）
    let charts: ServiceReportData['charts'] | undefined
    if (slaMetrics) {
      try {
        charts = await this.generateSlaCharts(slaMetrics)
      } catch (e) {
        console.warn('生成SLA图表失败，将跳过图表渲染。', e)
      }
    }

    // 构建时间轴
    const timeline = this.buildTimeline(service)

    // 计算统计数据
    const statistics = {
      totalWorkHours: service.workLogs.reduce((sum, log) => sum + log.workHours, 0),
      responseTime: service.actualResponseTime || 0,
      resolutionTime: service.actualResolutionTime || 0,
      commentsCount: service.comments.length,
      attachmentsCount: service.attachments.length
    }

    // 公司信息（可以从配置文件或数据库获取）
    const company = {
      name: '多协云科技有限公司',
      logo: '/assets/logo.png', // 相对路径
      address: '上海市浦东新区张江高科技园区',
      phone: '************',
      website: 'www.duoxieyun.com'
    }

    const result: ServiceReportData = {
      service,
      slaMetrics,
      timeline,
      statistics,
      company
    }
    // 注入印章（可配置）
    const stampDataUrl = await this.tryGetReportStampDataUrl()
    if (stampDataUrl) {
      result.stamp = { enabled: true, dataUrl: stampDataUrl }
    }
    if (charts) {
      (result as any).charts = charts
    }
    return result
  }

  /**
   * 构建服务时间轴
   */
  private buildTimeline(service: any): any[] {
    const timeline: any[] = []

    // 添加创建事件
    timeline.push({
      type: 'created',
      title: '工单创建',
      description: `工单由 ${service.createdByUser.fullName} 创建`,
      timestamp: service.createdAt,
      icon: 'create',
      color: '#3b82f6'
    })

    // 添加首次响应事件
    if (service.firstResponseAt) {
      timeline.push({
        type: 'first_response',
        title: '首次响应',
        description: '工程师首次响应',
        timestamp: service.firstResponseAt,
        icon: 'response',
        color: '#10b981'
      })
    }

    // 添加分配事件
    if (service.assignedUser) {
      timeline.push({
        type: 'assigned',
        title: '工单分配',
        description: `分配给 ${service.assignedUser.fullName}`,
        timestamp: service.updatedAt, // 简化处理，实际可以从操作历史获取
        icon: 'assign',
        color: '#8b5cf6'
      })
    }

    // 添加工作日志事件
    service.workLogs.forEach((log: any) => {
      timeline.push({
        type: 'work_log',
        title: '工作记录',
        description: log.description,
        details: `工时: ${log.workHours}小时`,
        timestamp: log.workDate,
        user: log.user.fullName,
        icon: 'work',
        color: '#f59e0b'
      })
    })

    // 添加评论事件
    service.comments.forEach((comment: any) => {
      timeline.push({
        type: comment.isInternal ? 'internal_comment' : 'comment',
        title: comment.isInternal ? '内部备注' : '客户沟通',
        description: comment.content,
        timestamp: comment.createdAt,
        user: comment.author.fullName,
        icon: comment.isInternal ? 'note' : 'comment',
        color: comment.isInternal ? '#6b7280' : '#06b6d4'
      })
    })

    // 添加结束事件
    if (service.endTime) {
      timeline.push({
        type: 'completed',
        title: '工单完成',
        description: `状态: ${service.status}`,
        timestamp: service.endTime,
        icon: 'complete',
        color: '#10b981'
      })
    }

    // 按时间排序
    return timeline.sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime())
  }

  /**
   * 生成 SLA 图表（返回 dataURL 用于模板 <img src="..." />）
   */
  private async generateSlaCharts(sla: any): Promise<Required<ServiceReportData>['charts']> {
    const width = 600
    const height = 300
    const chartJs = new ChartJSNodeCanvas({ width, height, backgroundColour: 'white' })

    // 响应时间 Donut
    const respUsed = Math.max(0, sla.responseTimeStatus.elapsed)
    const respTarget = Math.max(1, sla.slaTemplate.responseTime)
    const respRemain = Math.max(0, respTarget - respUsed)
    const respImg = await chartJs.renderToDataURL({
      type: 'doughnut',
      data: {
        labels: ['已用', '剩余'],
        datasets: [{
          data: [Math.min(respUsed, respTarget), Math.max(respRemain, 0)],
          backgroundColor: ['#3b82f6', '#e5e7eb'],
          borderWidth: 0
        }]
      },
      options: {
        plugins: {
          legend: { display: true, position: 'bottom' },
          title: { display: true, text: `响应时间（目标 ${respTarget} 分钟）` }
        }
      }
    })

    // 解决时间 Donut
    const resoUsedHours = Math.max(0, sla.resolutionTimeStatus.elapsed)
    const resoTargetHours = Math.max(1, sla.slaTemplate.resolutionTime)
    const resoRemain = Math.max(0, resoTargetHours - resoUsedHours)
    const resoImg = await chartJs.renderToDataURL({
      type: 'doughnut',
      data: {
        labels: ['已用', '剩余'],
        datasets: [{
          data: [Math.min(resoUsedHours, resoTargetHours), Math.max(resoRemain, 0)],
          backgroundColor: ['#10b981', '#e5e7eb'],
          borderWidth: 0
        }]
      },
      options: {
        plugins: {
          legend: { display: true, position: 'bottom' },
          title: { display: true, text: `解决时间（目标 ${resoTargetHours} 小时）` }
        }
      }
    })

    // 目标与进度条形图
    const barImg = await chartJs.renderToDataURL({
      type: 'bar',
      data: {
        labels: ['响应(分)', '解决(时)'],
        datasets: [
          {
            label: '目标',
            data: [respTarget, resoTargetHours],
            backgroundColor: '#e5e7eb'
          },
          {
            label: '已用',
            data: [Math.min(respUsed, respTarget), Math.min(resoUsedHours, resoTargetHours)],
            backgroundColor: ['#3b82f6', '#10b981']
          }
        ]
      },
      options: {
        responsive: false,
        plugins: {
          legend: { display: true, position: 'bottom' },
          title: { display: true, text: 'SLA 目标与进度' }
        },
        scales: {
          y: { beginAtZero: true }
        }
      }
    })

    return {
      responseDonut: respImg,
      resolutionDonut: resoImg,
      slaBars: barImg
    }
  }

  /**
   * 渲染报告模板
   */
  private async renderReportTemplate(data: ServiceReportData): Promise<string> {
    // 兼容多种运行目录（src/ 与 dist/），按顺序尝试寻找模板
    const envPath = process.env['SERVICE_REPORT_TEMPLATE_PATH']
    const candidatePaths = [
      ...(envPath ? [envPath] : []),
      path.join(this.templatesPath, 'service-report.hbs'),
      path.join(process.cwd(), 'backend', 'src', 'templates', 'reports', 'service-report.hbs'),
      path.join(process.cwd(), 'src', 'templates', 'reports', 'service-report.hbs'),
      path.join(__dirname, '../../templates/reports/service-report.hbs')
    ]

    console.log('[PDF] 模板候选路径:', candidatePaths)

    for (const candidate of candidatePaths) {
      try {
        // 先尝试访问，便于输出明确错误
        await fs.access(candidate)
        const templateContent = await fs.readFile(candidate, 'utf-8')
        const template = Handlebars.compile(templateContent)
        console.log(`[PDF] 使用模板: ${candidate}`)
        return template(data)
      } catch (e: any) {
        console.warn(`[PDF] 模板不可用: ${candidate} -> ${e?.code || e?.message}`)
        // 下一候选路径
      }
    }

    // 如果模板文件均不可读，使用内置模板
    console.warn('[PDF] 未找到 service-report.hbs 模板，使用内置模板生成报告')
    return this.getBuiltinTemplate(data)
  }

  /**
   * 获取印章图片的 DataURL（优先读取配置文件路径，其次生成内置SVG印章）
   */
  private async tryGetReportStampDataUrl(): Promise<string | undefined> {
    const enabledEnv = (process.env['REPORT_STAMP_ENABLED'] || 'false').toLowerCase()
    const enabled = enabledEnv === 'true' || enabledEnv === '1' || enabledEnv === 'yes'
    if (!enabled) return undefined

    const customPath = process.env['REPORT_STAMP_PATH']
    if (customPath) {
      try {
        const isAbs = path.isAbsolute(customPath)
        const finalPath = isAbs ? customPath : path.join(process.cwd(), customPath)
        const buf = await fs.readFile(finalPath)
        const ext = path.extname(finalPath).toLowerCase()
        const mime = ext === '.svg' ? 'image/svg+xml' : ext === '.jpg' || ext === '.jpeg' ? 'image/jpeg' : 'image/png'
        return `data:${mime};base64,${buf.toString('base64')}`
      } catch (e) {
        console.warn('[PDF] 无法读取自定义印章文件，回退到内置印章:', e instanceof Error ? e.message : e)
      }
    }

    // 内置示例印章（SVG）：红色圆形“多协云科技 / 公章”
    const svg = `<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" width="300" height="300" viewBox="0 0 300 300">
  <defs>
    <style>
      .seal-stroke { fill: none; stroke: #e02424; stroke-width: 10; }
      .seal-text { fill: #e02424; font-weight: 700; font-family: 'Microsoft YaHei', 'SimHei', Arial, sans-serif; }
    </style>
  </defs>
  <circle cx="150" cy="150" r="140" class="seal-stroke" />
  <text x="150" y="145" text-anchor="middle" font-size="26" class="seal-text">多协云科技</text>
  <text x="150" y="180" text-anchor="middle" font-size="24" class="seal-text">公章</text>
  <circle cx="150" cy="110" r="6" fill="#e02424" />
  <circle cx="150" cy="210" r="6" fill="#e02424" />
  <circle cx="100" cy="160" r="6" fill="#e02424" />
  <circle cx="200" cy="160" r="6" fill="#e02424" />
  <g transform="rotate(-8 150 150)">
    <circle cx="150" cy="150" r="120" class="seal-stroke" opacity="0.25" />
  </g>
  <title>Official Seal - Demo</title>
  <desc>Auto-generated demo stamp</desc>
  </svg>`
    const base64 = Buffer.from(svg, 'utf-8').toString('base64')
    return `data:image/svg+xml;base64,${base64}`
  }

  /**
   * 内置模板（用于初始开发）
   */
  private getBuiltinTemplate(data: ServiceReportData): string {
    // 这里返回一个简化的内置模板，稍后会创建完整的模板文件
    return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="UTF-8">
      <title>服务工单报告 - ${data.service.ticketNumber}</title>
      <style>
        body { font-family: 'SimSun', serif; margin: 0; padding: 20px; }
        .header { text-align: center; border-bottom: 2px solid #2563eb; padding-bottom: 20px; }
        .content { margin-top: 30px; }
        .section { margin-bottom: 30px; }
        h1 { color: #1f2937; font-size: 24px; }
        h2 { color: #374151; font-size: 18px; border-bottom: 1px solid #e5e7eb; padding-bottom: 8px; }
        .info-grid { display: grid; grid-template-columns: repeat(2, 1fr); gap: 20px; }
        .info-item { margin-bottom: 10px; }
        .label { font-weight: bold; color: #6b7280; }
        .timeline { border-left: 2px solid #e5e7eb; padding-left: 20px; }
        .timeline-item { margin-bottom: 20px; position: relative; }
        .timeline-item::before { content: '●'; position: absolute; left: -26px; color: #3b82f6; }
      </style>
    </head>
    <body>
      <div class="header">
        <h1>服务工单报告</h1>
        <p>工单编号: ${data.service.ticketNumber}</p>
        <p>生成时间: ${moment().format('YYYY-MM-DD HH:mm:ss')}</p>
      </div>
      
      <div class="content">
        <div class="section">
          <h2>基本信息</h2>
          <div class="info-grid">
            <div class="info-item">
              <span class="label">标题:</span> ${data.service.title}
            </div>
            <div class="info-item">
              <span class="label">状态:</span> ${data.service.status}
            </div>
            <div class="info-item">
              <span class="label">优先级:</span> ${data.service.priority}
            </div>
            <div class="info-item">
              <span class="label">类别:</span> ${data.service.category}
            </div>
          </div>
        </div>
        
        <div class="section">
          <h2>处理时间轴</h2>
          <div class="timeline">
            ${data.timeline.map(item => `
              <div class="timeline-item">
                <strong>${moment(item.timestamp).format('MM-DD HH:mm')}</strong> - ${item.title}
                <br><small>${item.description}</small>
              </div>
            `).join('')}
          </div>
        </div>
      </div>
    </body>
    </html>
    `
  }

  /**
   * 生成PDF
   */
  private async generatePDF(html: string): Promise<Buffer> {
    const browser = await puppeteer.launch({
      headless: true,
      timeout: 60000, // 增加超时时间到60秒
      // 让Puppeteer自动查找已安装的Chrome
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-gpu',
        '--disable-web-security',
        '--disable-features=VizDisplayCompositor',
        '--no-first-run',
        '--no-default-browser-check',
        '--disable-background-timer-throttling',
        '--disable-backgrounding-occluded-windows',
        '--disable-renderer-backgrounding'
      ]
    })

    try {
      const page = await browser.newPage()
      await page.emulateMediaType('screen')
      
      // 设置页面内容
      await page.setContent(html, {
        waitUntil: 'networkidle2'
      })

      // 生成PDF
      const pdf = await page.pdf({
        format: 'A4',
        preferCSSPageSize: true,
        margin: {
          top: '20mm',
          right: '15mm',
          bottom: '20mm',
          left: '15mm'
        },
        printBackground: true,
        displayHeaderFooter: true,
        headerTemplate: `
          <div style="font-size: 10px; width: 100%; text-align: center; color: #666;">
            <span>服务工单报告 - 多协云科技</span>
          </div>
        `,
        footerTemplate: `
          <div style="font-size: 10px; width: 100%; text-align: center; color: #666;">
            <span>第 <span class="pageNumber"></span> 页，共 <span class="totalPages"></span> 页</span>
          </div>
        `
      })

      return Buffer.from(pdf)
      
    } finally {
      await browser.close()
    }
  }

  /**
   * 备用PDF生成方法 - 使用更简单的配置
   */
  private async generatePDFFallback(html: string): Promise<Buffer> {
    console.log('🔄 使用备用PDF生成方案')
    
    const browser = await puppeteer.launch({
      headless: true,
      timeout: 120000, // 更长的超时时间
      // 让Puppeteer自动查找已安装的Chrome
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-gpu',
        '--single-process', // 使用单进程模式
        '--no-zygote',
        '--disable-extensions',
        '--disable-background-timer-throttling',
        '--disable-backgrounding-occluded-windows',
        '--disable-renderer-backgrounding',
        '--disable-ipc-flooding-protection'
      ]
    })

    try {
      const page = await browser.newPage()
      
      // 设置更长的导航超时
      page.setDefaultNavigationTimeout(60000)
      page.setDefaultTimeout(60000)
      
      // 设置页面内容
      await page.setContent(html, {
        waitUntil: 'domcontentloaded' // 更快的等待条件
      })

      // 生成PDF - 使用简化配置
      const pdf = await page.pdf({
        format: 'A4',
        margin: {
          top: '20mm',
          right: '15mm',
          bottom: '20mm',
          left: '15mm'
        },
        printBackground: true,
        timeout: 60000 // PDF生成超时
      })

      return Buffer.from(pdf)
      
    } finally {
      await browser.close()
    }
  }
}

export default ServiceReportService
