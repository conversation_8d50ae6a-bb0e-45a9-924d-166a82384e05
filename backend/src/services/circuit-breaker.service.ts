/**
 * Circuit Breaker 断路器服务
 * 防止级联失败并提供系统保护
 */

import { EventEmitter } from 'events';

/**
 * 断路器状态
 */
export enum CircuitBreakerState {
  CLOSED = 'CLOSED',     // 正常运行
  OPEN = 'OPEN',         // 断路器打开，阻断请求
  HALF_OPEN = 'HALF_OPEN' // 半开状态，测试恢复
}

/**
 * 断路器配置
 */
export interface CircuitBreakerConfig {
  failureThreshold: number;      // 失败阈值
  recoveryTimeout: number;       // 恢复超时时间（毫秒）
  monitoringPeriod: number;      // 监控周期（毫秒）
  minimumRequestThreshold: number; // 最小请求阈值
  successThreshold: number;      // 成功阈值（半开状态下）
}

/**
 * 执行统计信息
 */
export interface ExecutionStats {
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  lastFailureTime: Date | null;
  lastSuccessTime: Date | null;
  consecutiveFailures: number;
  consecutiveSuccesses: number;
}

/**
 * 断路器实例
 */
export class CircuitBreaker extends EventEmitter {
  private state: CircuitBreakerState = CircuitBreakerState.CLOSED;
  private stats: ExecutionStats = {
    totalRequests: 0,
    successfulRequests: 0,
    failedRequests: 0,
    lastFailureTime: null,
    lastSuccessTime: null,
    consecutiveFailures: 0,
    consecutiveSuccesses: 0
  };
  
  private stateChangedAt: Date = new Date();
  private monitoringWindow: Map<number, boolean> = new Map();

  constructor(
    private name: string,
    private config: CircuitBreakerConfig
  ) {
    super();
    this.setupMonitoring();
  }

  /**
   * 执行受保护的操作
   */
  async execute<T>(operation: () => Promise<T>): Promise<T> {
    if (this.state === CircuitBreakerState.OPEN) {
      if (!this.shouldAttemptReset()) {
        const error = new Error(`Circuit breaker is OPEN for service: ${this.name}`);
        this.emit('request:rejected', { name: this.name, reason: 'circuit_open' });
        throw error;
      } else {
        this.transitionToHalfOpen();
      }
    }

    try {
      this.stats.totalRequests++;
      const result = await operation();
      this.onSuccess();
      return result;
    } catch (error) {
      this.onFailure();
      throw error;
    }
  }

  /**
   * 检查操作是否被允许
   */
  isRequestAllowed(): boolean {
    if (this.state === CircuitBreakerState.CLOSED) {
      return true;
    }
    
    if (this.state === CircuitBreakerState.HALF_OPEN) {
      return true;
    }
    
    return this.shouldAttemptReset();
  }

  /**
   * 获取当前状态
   */
  getState(): CircuitBreakerState {
    return this.state;
  }

  /**
   * 获取统计信息
   */
  getStats(): ExecutionStats & { 
    state: CircuitBreakerState;
    failureRate: number;
    uptime: number;
  } {
    const failureRate = this.stats.totalRequests > 0 
      ? (this.stats.failedRequests / this.stats.totalRequests) * 100 
      : 0;
    
    const uptime = Date.now() - this.stateChangedAt.getTime();

    return {
      ...this.stats,
      state: this.state,
      failureRate: Math.round(failureRate * 100) / 100,
      uptime
    };
  }

  /**
   * 手动重置断路器
   */
  reset(): void {
    this.state = CircuitBreakerState.CLOSED;
    this.stats = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      lastFailureTime: null,
      lastSuccessTime: null,
      consecutiveFailures: 0,
      consecutiveSuccesses: 0
    };
    this.stateChangedAt = new Date();
    this.monitoringWindow.clear();
    
    this.emit('state:changed', { 
      name: this.name, 
      from: this.state, 
      to: CircuitBreakerState.CLOSED,
      reason: 'manual_reset'
    });
  }

  /**
   * 获取配置信息
   */
  getConfig(): CircuitBreakerConfig {
    return { ...this.config };
  }

  // ========== 私有方法 ==========

  /**
   * 处理成功响应
   */
  private onSuccess(): void {
    this.stats.successfulRequests++;
    this.stats.lastSuccessTime = new Date();
    this.stats.consecutiveFailures = 0;
    this.stats.consecutiveSuccesses++;

    // 在半开状态下检查是否应该关闭断路器
    if (this.state === CircuitBreakerState.HALF_OPEN) {
      if (this.stats.consecutiveSuccesses >= this.config.successThreshold) {
        this.transitionToClosed();
      }
    }

    this.recordExecution(true);
    this.emit('request:success', { name: this.name, stats: this.stats });
  }

  /**
   * 处理失败响应
   */
  private onFailure(): void {
    this.stats.failedRequests++;
    this.stats.lastFailureTime = new Date();
    this.stats.consecutiveSuccesses = 0;
    this.stats.consecutiveFailures++;

    this.recordExecution(false);

    // 检查是否应该打开断路器
    if (this.shouldOpenCircuit()) {
      this.transitionToOpen();
    }

    this.emit('request:failure', { 
      name: this.name, 
      stats: this.stats,
      consecutiveFailures: this.stats.consecutiveFailures
    });
  }

  /**
   * 记录执行结果到监控窗口
   */
  private recordExecution(success: boolean): void {
    const now = Date.now();
    const windowStart = now - this.config.monitoringPeriod;
    
    // 清理过期数据
    for (const [timestamp] of this.monitoringWindow) {
      if (timestamp < windowStart) {
        this.monitoringWindow.delete(timestamp);
      }
    }
    
    // 记录当前执行结果
    this.monitoringWindow.set(now, success);
  }

  /**
   * 判断是否应该打开断路器
   */
  private shouldOpenCircuit(): boolean {
    if (this.state === CircuitBreakerState.OPEN) {
      return false;
    }

    // 检查请求数量是否达到最小阈值
    if (this.stats.totalRequests < this.config.minimumRequestThreshold) {
      return false;
    }

    // 检查连续失败次数
    if (this.stats.consecutiveFailures >= this.config.failureThreshold) {
      return true;
    }

    // 检查监控窗口内的失败率
    const windowFailures = Array.from(this.monitoringWindow.values()).filter(success => !success).length;
    const windowTotal = this.monitoringWindow.size;
    
    if (windowTotal >= this.config.minimumRequestThreshold) {
      const failureRate = (windowFailures / windowTotal) * 100;
      return failureRate >= (this.config.failureThreshold / this.config.minimumRequestThreshold) * 100;
    }

    return false;
  }

  /**
   * 判断是否应该尝试重置
   */
  private shouldAttemptReset(): boolean {
    const timeSinceOpen = Date.now() - this.stateChangedAt.getTime();
    return timeSinceOpen >= this.config.recoveryTimeout;
  }

  /**
   * 转换到关闭状态
   */
  private transitionToClosed(): void {
    const previousState = this.state;
    this.state = CircuitBreakerState.CLOSED;
    this.stateChangedAt = new Date();
    this.stats.consecutiveFailures = 0;
    
    this.emit('state:changed', { 
      name: this.name, 
      from: previousState, 
      to: this.state,
      reason: 'success_threshold_reached'
    });
  }

  /**
   * 转换到打开状态
   */
  private transitionToOpen(): void {
    const previousState = this.state;
    this.state = CircuitBreakerState.OPEN;
    this.stateChangedAt = new Date();
    
    this.emit('state:changed', { 
      name: this.name, 
      from: previousState, 
      to: this.state,
      reason: 'failure_threshold_exceeded'
    });
    
    this.emit('circuit:opened', { 
      name: this.name, 
      stats: this.stats,
      config: this.config
    });
  }

  /**
   * 转换到半开状态
   */
  private transitionToHalfOpen(): void {
    const previousState = this.state;
    this.state = CircuitBreakerState.HALF_OPEN;
    this.stateChangedAt = new Date();
    this.stats.consecutiveSuccesses = 0;
    
    this.emit('state:changed', { 
      name: this.name, 
      from: previousState, 
      to: this.state,
      reason: 'recovery_timeout_elapsed'
    });
  }

  /**
   * 设置监控
   */
  private setupMonitoring(): void {
    // 定期清理过期的监控数据
    setInterval(() => {
      const now = Date.now();
      const windowStart = now - this.config.monitoringPeriod;
      
      for (const [timestamp] of this.monitoringWindow) {
        if (timestamp < windowStart) {
          this.monitoringWindow.delete(timestamp);
        }
      }
    }, this.config.monitoringPeriod / 4); // 每1/4周期清理一次
  }
}

/**
 * Circuit Breaker 管理器
 */
export class CircuitBreakerManager extends EventEmitter {
  private static instance: CircuitBreakerManager;
  private breakers = new Map<string, CircuitBreaker>();
  private defaultConfig: CircuitBreakerConfig = {
    failureThreshold: 5,        // 5次失败后打开
    recoveryTimeout: 60000,     // 60秒后尝试恢复
    monitoringPeriod: 60000,    // 60秒监控窗口
    minimumRequestThreshold: 10, // 最少10个请求
    successThreshold: 3         // 3次成功后关闭
  };

  private constructor() {
    super();
  }

  /**
   * 获取单例实例
   */
  static getInstance(): CircuitBreakerManager {
    if (!CircuitBreakerManager.instance) {
      CircuitBreakerManager.instance = new CircuitBreakerManager();
    }
    return CircuitBreakerManager.instance;
  }

  /**
   * 创建或获取断路器
   */
  getCircuitBreaker(name: string, config?: Partial<CircuitBreakerConfig>): CircuitBreaker {
    if (!this.breakers.has(name)) {
      const breakerConfig = { ...this.defaultConfig, ...config };
      const breaker = new CircuitBreaker(name, breakerConfig);
      
      // 转发事件
      breaker.on('state:changed', (event) => {
        this.emit('breaker:state:changed', event);
      });
      
      breaker.on('circuit:opened', (event) => {
        this.emit('breaker:circuit:opened', event);
      });
      
      this.breakers.set(name, breaker);
      
      this.emit('breaker:created', { name, config: breakerConfig });
    }
    
    return this.breakers.get(name)!;
  }

  /**
   * 移除断路器
   */
  removeCircuitBreaker(name: string): boolean {
    const breaker = this.breakers.get(name);
    if (breaker) {
      breaker.removeAllListeners();
      this.breakers.delete(name);
      this.emit('breaker:removed', { name });
      return true;
    }
    return false;
  }

  /**
   * 列出所有断路器
   */
  listCircuitBreakers(): Array<{
    name: string;
    state: CircuitBreakerState;
    stats: any;
    config: CircuitBreakerConfig;
  }> {
    return Array.from(this.breakers.entries()).map(([name, breaker]) => ({
      name,
      state: breaker.getState(),
      stats: breaker.getStats(),
      config: breaker.getConfig()
    }));
  }

  /**
   * 重置所有断路器
   */
  resetAll(): void {
    for (const breaker of this.breakers.values()) {
      breaker.reset();
    }
    this.emit('all:reset');
  }

  /**
   * 获取系统健康状况
   */
  getSystemHealth(): {
    totalBreakers: number;
    openBreakers: number;
    halfOpenBreakers: number;
    closedBreakers: number;
    healthScore: number;
  } {
    const breakers = Array.from(this.breakers.values());
    const total = breakers.length;
    
    const states = breakers.reduce((acc, breaker) => {
      const state = breaker.getState();
      acc[state] = (acc[state] || 0) + 1;
      return acc;
    }, {} as Record<CircuitBreakerState, number>);

    const openCount = states[CircuitBreakerState.OPEN] || 0;
    const halfOpenCount = states[CircuitBreakerState.HALF_OPEN] || 0;
    const closedCount = states[CircuitBreakerState.CLOSED] || 0;

    // 健康分数计算：关闭状态=100分，半开=50分，打开=0分
    const healthScore = total > 0 
      ? ((closedCount * 100 + halfOpenCount * 50) / total)
      : 100;

    return {
      totalBreakers: total,
      openBreakers: openCount,
      halfOpenBreakers: halfOpenCount,
      closedBreakers: closedCount,
      healthScore: Math.round(healthScore)
    };
  }

  /**
   * 清理所有断路器
   */
  cleanup(): void {
    for (const breaker of this.breakers.values()) {
      breaker.removeAllListeners();
    }
    this.breakers.clear();
    this.removeAllListeners();
  }
}

// 导出单例实例
export const circuitBreakerManager = CircuitBreakerManager.getInstance();