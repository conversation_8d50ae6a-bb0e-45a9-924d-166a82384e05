/**
 * 指标收集器服务
 * 收集和分析工作流执行的各种指标和性能数据
 */

import { EventEmitter } from 'events';
import type { WorkflowStepType } from '@prisma/client';
import type {
  ActionExecutionResult,
  ExecutionError,
  ResourceUsage,
  ExecutionMetrics,
  IMetricsCollector,
  ErrorType
} from '@/types/action-executor.types';

/**
 * 实时指标数据
 */
export interface RealTimeMetrics {
  timestamp: Date;
  activeExecutions: number;
  queuedExecutions: number;
  successRate: number;
  averageExecutionTime: number;
  errorRate: number;
  resourceUtilization: ResourceUsage;
}

/**
 * 性能趋势数据
 */
export interface PerformanceTrend {
  timeWindow: string;
  successRate: number;
  averageExecutionTime: number;
  throughput: number;
  errorDistribution: Map<ErrorType, number>;
}

/**
 * 执行器性能比较
 */
export interface ExecutorPerformanceComparison {
  executorType: WorkflowStepType;
  totalExecutions: number;
  successRate: number;
  averageExecutionTime: number;
  medianExecutionTime: number;
  p95ExecutionTime: number;
  p99ExecutionTime: number;
  errorRate: number;
  resourceEfficiency: number;
}

/**
 * 指标收集器服务实现
 */
export class MetricsCollectorService extends EventEmitter implements IMetricsCollector {
  private static instance: MetricsCollectorService;
  private executionMetrics = new Map<WorkflowStepType, ExecutionMetrics>();
  private executionHistory: Array<{
    timestamp: Date;
    executorType: WorkflowStepType;
    executionTime: number;
    success: boolean;
    error?: ExecutionError;
    resourceUsage?: ResourceUsage;
  }> = [];
  
  private realtimeMetrics: RealTimeMetrics[] = [];
  private maxHistorySize = 10000;
  private maxRealtimeSize = 1000;
  private isInitialized = false;

  constructor() {
    super();
  }

  /**
   * 获取单例实例
   */
  static getInstance(): MetricsCollectorService {
    if (!MetricsCollectorService.instance) {
      MetricsCollectorService.instance = new MetricsCollectorService();
    }
    return MetricsCollectorService.instance;
  }

  /**
   * 初始化指标收集器
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      console.log('🚀 初始化MetricsCollectorService...');

      this.setupRealtimeCollection();
      this.setupCleanupTimer();
      this.isInitialized = true;

      console.log('✅ MetricsCollectorService初始化完成');
      this.emit('initialized');

    } catch (error) {
      console.error('❌ MetricsCollectorService初始化失败:', error);
      throw error;
    }
  }

  /**
   * 记录执行结果
   */
  recordExecution(result: ActionExecutionResult): void {
    const executorType = this.extractExecutorType(result);
    if (!executorType) return;

    // 获取或创建执行器指标
    let metrics = this.executionMetrics.get(executorType);
    if (!metrics) {
      metrics = this.createEmptyMetrics();
      this.executionMetrics.set(executorType, metrics);
    }

    // 更新基本指标
    metrics.totalExecutions++;
    
    if (result.success) {
      metrics.successfulExecutions++;
    } else {
      metrics.failedExecutions++;
      
      // 记录错误
      if (result.error) {
        this.recordError(result.error);
      }
    }

    // 更新执行时间
    if (result.executionTime) {
      const currentAvg = metrics.avgExecutionTime;
      const totalExecs = metrics.totalExecutions;
      metrics.avgExecutionTime = ((currentAvg * (totalExecs - 1)) + result.executionTime) / totalExecs;
    }

    // 记录资源使用
    if (result.resourceUsage) {
      this.recordResourceUsage(result.resourceUsage);
      
      // 更新资源利用率
      metrics.resourceUtilization = {
        cpu: (metrics.resourceUtilization.cpu || 0) + (result.resourceUsage.cpu || 0),
        memory: (metrics.resourceUtilization.memory || 0) + (result.resourceUsage.memory || 0),
        io: (metrics.resourceUtilization.io || 0) + (result.resourceUsage.io || 0),
        network: (metrics.resourceUtilization.network || 0) + (result.resourceUsage.network || 0),
        duration: (metrics.resourceUtilization.duration || 0) + result.resourceUsage.duration
      };
    }

    // 添加到历史记录
    this.addToExecutionHistory({
      timestamp: new Date(),
      executorType,
      executionTime: result.executionTime || 0,
      success: result.success,
      error: result.error,
      resourceUsage: result.resourceUsage
    });

    // 发出指标更新事件
    this.emit('metrics:updated', {
      executorType,
      metrics: { ...metrics }
    });
  }

  /**
   * 记录错误
   */
  recordError(error: ExecutionError): void {
    // 为所有执行器记录全局错误分布
    for (const metrics of this.executionMetrics.values()) {
      const currentCount = metrics.errorDistribution.get(error.type) || 0;
      metrics.errorDistribution.set(error.type, currentCount + 1);
    }

    this.emit('error:recorded', { error });
  }

  /**
   * 记录延迟
   */
  recordLatency(executorType: WorkflowStepType, duration: number): void {
    const metrics = this.executionMetrics.get(executorType);
    if (!metrics) return;

    // 更新性能指标
    const performanceKey = `${executorType}_latency`;
    metrics.performanceMetrics.set(performanceKey, duration);

    this.emit('latency:recorded', { executorType, duration });
  }

  /**
   * 记录资源使用
   */
  recordResourceUsage(usage: ResourceUsage): void {
    // 为所有执行器累计资源使用
    for (const metrics of this.executionMetrics.values()) {
      metrics.resourceUtilization.cpu = (metrics.resourceUtilization.cpu || 0) + (usage.cpu || 0);
      metrics.resourceUtilization.memory = (metrics.resourceUtilization.memory || 0) + (usage.memory || 0);
      metrics.resourceUtilization.io = (metrics.resourceUtilization.io || 0) + (usage.io || 0);
      metrics.resourceUtilization.network = (metrics.resourceUtilization.network || 0) + (usage.network || 0);
      metrics.resourceUtilization.duration = (metrics.resourceUtilization.duration || 0) + usage.duration;
    }

    this.emit('resource:recorded', { usage });
  }

  /**
   * 获取指标
   */
  getMetrics(executorType?: WorkflowStepType): ExecutionMetrics {
    if (executorType) {
      return this.executionMetrics.get(executorType) || this.createEmptyMetrics();
    }

    // 返回聚合指标
    const aggregatedMetrics = this.createEmptyMetrics();
    
    for (const metrics of this.executionMetrics.values()) {
      aggregatedMetrics.totalExecutions += metrics.totalExecutions;
      aggregatedMetrics.successfulExecutions += metrics.successfulExecutions;
      aggregatedMetrics.failedExecutions += metrics.failedExecutions;
      
      // 计算加权平均执行时间
      const weight = metrics.totalExecutions;
      const currentWeight = aggregatedMetrics.totalExecutions - weight;
      aggregatedMetrics.avgExecutionTime = (
        (aggregatedMetrics.avgExecutionTime * currentWeight + metrics.avgExecutionTime * weight) /
        aggregatedMetrics.totalExecutions
      ) || 0;

      // 合并错误分布
      for (const [errorType, count] of metrics.errorDistribution) {
        const currentCount = aggregatedMetrics.errorDistribution.get(errorType) || 0;
        aggregatedMetrics.errorDistribution.set(errorType, currentCount + count);
      }

      // 合并性能指标
      for (const [key, value] of metrics.performanceMetrics) {
        aggregatedMetrics.performanceMetrics.set(key, value);
      }

      // 累计资源使用
      aggregatedMetrics.resourceUtilization.cpu = (aggregatedMetrics.resourceUtilization.cpu || 0) + (metrics.resourceUtilization.cpu || 0);
      aggregatedMetrics.resourceUtilization.memory = (aggregatedMetrics.resourceUtilization.memory || 0) + (metrics.resourceUtilization.memory || 0);
      aggregatedMetrics.resourceUtilization.io = (aggregatedMetrics.resourceUtilization.io || 0) + (metrics.resourceUtilization.io || 0);
      aggregatedMetrics.resourceUtilization.network = (aggregatedMetrics.resourceUtilization.network || 0) + (metrics.resourceUtilization.network || 0);
      aggregatedMetrics.resourceUtilization.duration = (aggregatedMetrics.resourceUtilization.duration || 0) + (metrics.resourceUtilization.duration || 0);
    }

    return aggregatedMetrics;
  }

  /**
   * 重置指标
   */
  reset(): void {
    this.executionMetrics.clear();
    this.executionHistory = [];
    this.realtimeMetrics = [];
    
    console.log('📊 指标已重置');
    this.emit('metrics:reset');
  }

  /**
   * 获取实时指标
   */
  getRealTimeMetrics(): RealTimeMetrics {
    if (this.realtimeMetrics.length === 0) {
      return this.createEmptyRealTimeMetrics();
    }
    
    return this.realtimeMetrics[this.realtimeMetrics.length - 1];
  }

  /**
   * 获取性能趋势
   */
  getPerformanceTrend(timeWindow: '1h' | '6h' | '24h' | '7d'): PerformanceTrend[] {
    const now = new Date();
    let startTime: Date;
    let bucketSize: number; // 分钟

    switch (timeWindow) {
      case '1h':
        startTime = new Date(now.getTime() - 60 * 60 * 1000);
        bucketSize = 5; // 5分钟间隔
        break;
      case '6h':
        startTime = new Date(now.getTime() - 6 * 60 * 60 * 1000);
        bucketSize = 30; // 30分钟间隔
        break;
      case '24h':
        startTime = new Date(now.getTime() - 24 * 60 * 60 * 1000);
        bucketSize = 120; // 2小时间隔
        break;
      case '7d':
        startTime = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        bucketSize = 1440; // 24小时间隔
        break;
      default:
        startTime = new Date(now.getTime() - 60 * 60 * 1000);
        bucketSize = 5;
    }

    // 过滤历史数据
    const relevantHistory = this.executionHistory.filter(
      entry => entry.timestamp >= startTime
    );

    // 按时间窗口分组
    const buckets = new Map<string, typeof relevantHistory>();
    for (const entry of relevantHistory) {
      const bucketTime = new Date(
        Math.floor(entry.timestamp.getTime() / (bucketSize * 60 * 1000)) * bucketSize * 60 * 1000
      );
      const bucketKey = bucketTime.toISOString();
      
      if (!buckets.has(bucketKey)) {
        buckets.set(bucketKey, []);
      }
      buckets.get(bucketKey)!.push(entry);
    }

    // 计算趋势数据
    const trends: PerformanceTrend[] = [];
    for (const [timeKey, entries] of buckets) {
      const successfulEntries = entries.filter(e => e.success);
      const successRate = entries.length > 0 ? (successfulEntries.length / entries.length) * 100 : 0;
      
      const avgExecutionTime = entries.length > 0
        ? entries.reduce((sum, e) => sum + e.executionTime, 0) / entries.length
        : 0;

      const errorDistribution = new Map<ErrorType, number>();
      entries.filter(e => e.error).forEach(e => {
        const errorType = e.error!.type;
        errorDistribution.set(errorType, (errorDistribution.get(errorType) || 0) + 1);
      });

      trends.push({
        timeWindow: timeKey,
        successRate,
        averageExecutionTime: avgExecutionTime,
        throughput: entries.length,
        errorDistribution
      });
    }

    return trends.sort((a, b) => new Date(a.timeWindow).getTime() - new Date(b.timeWindow).getTime());
  }

  /**
   * 获取执行器性能比较
   */
  getExecutorPerformanceComparison(): ExecutorPerformanceComparison[] {
    const comparisons: ExecutorPerformanceComparison[] = [];

    for (const [executorType, metrics] of this.executionMetrics) {
      const executorHistory = this.executionHistory.filter(h => h.executorType === executorType);
      const executionTimes = executorHistory.map(h => h.executionTime).sort((a, b) => a - b);
      
      const medianExecutionTime = this.calculateMedian(executionTimes);
      const p95ExecutionTime = this.calculatePercentile(executionTimes, 95);
      const p99ExecutionTime = this.calculatePercentile(executionTimes, 99);
      
      const successRate = metrics.totalExecutions > 0 
        ? (metrics.successfulExecutions / metrics.totalExecutions) * 100 
        : 0;
      
      const errorRate = metrics.totalExecutions > 0 
        ? (metrics.failedExecutions / metrics.totalExecutions) * 100 
        : 0;

      // 简化的资源效率计算
      const resourceEfficiency = this.calculateResourceEfficiency(metrics);

      comparisons.push({
        executorType,
        totalExecutions: metrics.totalExecutions,
        successRate,
        averageExecutionTime: metrics.avgExecutionTime,
        medianExecutionTime,
        p95ExecutionTime,
        p99ExecutionTime,
        errorRate,
        resourceEfficiency
      });
    }

    return comparisons.sort((a, b) => b.totalExecutions - a.totalExecutions);
  }

  /**
   * 导出指标数据
   */
  exportMetrics(): {
    metrics: Map<WorkflowStepType, ExecutionMetrics>;
    history: typeof this.executionHistory;
    realtime: RealTimeMetrics[];
    exportTime: Date;
  } {
    return {
      metrics: new Map(this.executionMetrics),
      history: [...this.executionHistory],
      realtime: [...this.realtimeMetrics],
      exportTime: new Date()
    };
  }

  /**
   * 获取健康评分
   */
  getHealthScore(): {
    overall: number;
    byExecutor: Map<WorkflowStepType, number>;
    factors: {
      successRate: number;
      avgExecutionTime: number;
      errorRate: number;
      resourceUtilization: number;
    };
  } {
    const overallMetrics = this.getMetrics();
    const successRate = overallMetrics.totalExecutions > 0 
      ? (overallMetrics.successfulExecutions / overallMetrics.totalExecutions) * 100 
      : 100;
    
    const errorRate = overallMetrics.totalExecutions > 0 
      ? (overallMetrics.failedExecutions / overallMetrics.totalExecutions) * 100 
      : 0;

    // 性能评分 (执行时间越短越好)
    const avgTimeScore = Math.max(0, 100 - (overallMetrics.avgExecutionTime / 1000) * 10);
    
    // 资源利用率评分
    const resourceScore = 100 - Math.min(100, (overallMetrics.resourceUtilization.cpu || 0) * 100);

    const factors = {
      successRate,
      avgExecutionTime: avgTimeScore,
      errorRate: 100 - errorRate,
      resourceUtilization: resourceScore
    };

    // 加权总分
    const overall = (
      factors.successRate * 0.4 +
      factors.avgExecutionTime * 0.3 +
      factors.errorRate * 0.2 +
      factors.resourceUtilization * 0.1
    );

    // 各执行器健康评分
    const byExecutor = new Map<WorkflowStepType, number>();
    for (const [executorType, metrics] of this.executionMetrics) {
      const execSuccessRate = metrics.totalExecutions > 0 
        ? (metrics.successfulExecutions / metrics.totalExecutions) * 100 
        : 100;
      const execErrorRate = metrics.totalExecutions > 0 
        ? (metrics.failedExecutions / metrics.totalExecutions) * 100 
        : 0;
      const execAvgTimeScore = Math.max(0, 100 - (metrics.avgExecutionTime / 1000) * 10);
      
      const execOverall = (
        execSuccessRate * 0.5 +
        execAvgTimeScore * 0.3 +
        (100 - execErrorRate) * 0.2
      );
      
      byExecutor.set(executorType, Math.round(execOverall));
    }

    return {
      overall: Math.round(overall),
      byExecutor,
      factors: {
        successRate: Math.round(successRate),
        avgExecutionTime: Math.round(avgTimeScore),
        errorRate: Math.round(100 - errorRate),
        resourceUtilization: Math.round(resourceScore)
      }
    };
  }

  // ========== 私有方法 ==========

  /**
   * 从执行结果中提取执行器类型
   */
  private extractExecutorType(result: ActionExecutionResult): WorkflowStepType | null {
    // 这里需要根据实际的结果结构来提取类型
    // 在实际实现中，可能需要在执行结果中包含执行器类型信息
    return result.data?.executorType || null;
  }

  /**
   * 创建空的执行指标
   */
  private createEmptyMetrics(): ExecutionMetrics {
    return {
      totalExecutions: 0,
      successfulExecutions: 0,
      failedExecutions: 0,
      avgExecutionTime: 0,
      resourceUtilization: {
        cpu: 0,
        memory: 0,
        io: 0,
        network: 0,
        duration: 0
      },
      errorDistribution: new Map(),
      performanceMetrics: new Map()
    };
  }

  /**
   * 创建空的实时指标
   */
  private createEmptyRealTimeMetrics(): RealTimeMetrics {
    return {
      timestamp: new Date(),
      activeExecutions: 0,
      queuedExecutions: 0,
      successRate: 0,
      averageExecutionTime: 0,
      errorRate: 0,
      resourceUtilization: {
        cpu: 0,
        memory: 0,
        io: 0,
        network: 0,
        duration: 0
      }
    };
  }

  /**
   * 添加到执行历史
   */
  private addToExecutionHistory(entry: typeof this.executionHistory[0]): void {
    this.executionHistory.push(entry);
    
    // 限制历史记录大小
    if (this.executionHistory.length > this.maxHistorySize) {
      this.executionHistory = this.executionHistory.slice(-this.maxHistorySize);
    }
  }

  /**
   * 设置实时数据收集
   */
  private setupRealtimeCollection(): void {
    setInterval(() => {
      const realtimeMetric = this.collectRealtimeMetric();
      this.realtimeMetrics.push(realtimeMetric);
      
      // 限制实时数据大小
      if (this.realtimeMetrics.length > this.maxRealtimeSize) {
        this.realtimeMetrics = this.realtimeMetrics.slice(-this.maxRealtimeSize);
      }
      
      this.emit('realtime:updated', realtimeMetric);
    }, 5000); // 每5秒收集一次
  }

  /**
   * 收集实时指标
   */
  private collectRealtimeMetric(): RealTimeMetrics {
    const overallMetrics = this.getMetrics();
    const recentHistory = this.executionHistory.filter(
      entry => new Date().getTime() - entry.timestamp.getTime() < 60000 // 最近1分钟
    );

    const successRate = recentHistory.length > 0 
      ? (recentHistory.filter(h => h.success).length / recentHistory.length) * 100 
      : 0;

    const errorRate = recentHistory.length > 0 
      ? (recentHistory.filter(h => !h.success).length / recentHistory.length) * 100 
      : 0;

    const averageExecutionTime = recentHistory.length > 0
      ? recentHistory.reduce((sum, h) => sum + h.executionTime, 0) / recentHistory.length
      : 0;

    return {
      timestamp: new Date(),
      activeExecutions: 0, // 这需要从执行引擎获取
      queuedExecutions: 0, // 这需要从执行引擎获取
      successRate,
      averageExecutionTime,
      errorRate,
      resourceUtilization: overallMetrics.resourceUtilization
    };
  }

  /**
   * 设置清理定时器
   */
  private setupCleanupTimer(): void {
    // 每小时清理一次过期数据
    setInterval(() => {
      this.cleanupExpiredData();
    }, 60 * 60 * 1000);
  }

  /**
   * 清理过期数据
   */
  private cleanupExpiredData(): void {
    const now = new Date();
    const maxAge = 7 * 24 * 60 * 60 * 1000; // 7天

    // 清理过期的执行历史
    const cutoffTime = new Date(now.getTime() - maxAge);
    this.executionHistory = this.executionHistory.filter(
      entry => entry.timestamp >= cutoffTime
    );

    // 清理过期的实时指标
    const realtimeCutoff = new Date(now.getTime() - 24 * 60 * 60 * 1000); // 24小时
    this.realtimeMetrics = this.realtimeMetrics.filter(
      metric => metric.timestamp >= realtimeCutoff
    );

    console.log('🧹 清理过期指标数据');
  }

  /**
   * 计算中位数
   */
  private calculateMedian(values: number[]): number {
    if (values.length === 0) return 0;
    
    const sorted = [...values].sort((a, b) => a - b);
    const mid = Math.floor(sorted.length / 2);
    
    return sorted.length % 2 !== 0 
      ? sorted[mid] 
      : (sorted[mid - 1] + sorted[mid]) / 2;
  }

  /**
   * 计算百分位数
   */
  private calculatePercentile(values: number[], percentile: number): number {
    if (values.length === 0) return 0;
    
    const sorted = [...values].sort((a, b) => a - b);
    const index = Math.ceil((percentile / 100) * sorted.length) - 1;
    
    return sorted[Math.max(0, index)];
  }

  /**
   * 计算资源效率
   */
  private calculateResourceEfficiency(metrics: ExecutionMetrics): number {
    const totalExecutions = metrics.totalExecutions;
    if (totalExecutions === 0) return 100;

    const avgResourceUsage = {
      cpu: (metrics.resourceUtilization.cpu || 0) / totalExecutions,
      memory: (metrics.resourceUtilization.memory || 0) / totalExecutions,
      io: (metrics.resourceUtilization.io || 0) / totalExecutions,
      network: (metrics.resourceUtilization.network || 0) / totalExecutions
    };

    // 简化的效率评分：资源使用越少效率越高
    const efficiency = 100 - Math.min(100, 
      (avgResourceUsage.cpu * 25 + 
       avgResourceUsage.memory * 25 + 
       avgResourceUsage.io * 25 + 
       avgResourceUsage.network * 25)
    );

    return Math.max(0, Math.round(efficiency));
  }

  /**
   * 全局清理
   */
  async cleanup(): Promise<void> {
    console.log('🧹 开始清理MetricsCollectorService...');
    
    this.executionMetrics.clear();
    this.executionHistory = [];
    this.realtimeMetrics = [];
    this.isInitialized = false;
    
    console.log('✅ MetricsCollectorService清理完成');
    this.emit('cleanup:completed');
  }
}

// 导出单例实例
export const metricsCollector = MetricsCollectorService.getInstance();