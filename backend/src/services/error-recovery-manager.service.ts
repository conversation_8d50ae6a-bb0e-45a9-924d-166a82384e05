/**
 * 错误恢复管理器
 * 负责处理工作流执行中的错误恢复和故障转移
 */

import { EventEmitter } from 'events';
import type {
  WorkflowStep,
  ExecutionContext,
  ActionExecutionResult,
  ExecutionError,
  RollbackResult,
  ErrorType,
  ErrorSeverity,
  ErrorHandlingStrategy,
  ErrorHandlingType,
  ExecutionLog
} from '@/types/action-executor.types';
import { actionExecutorManager } from './action-executor-manager.service';
import { executionStateTracker } from './execution-state-tracker.service';
import { executionContextManager } from './execution-context-manager.service';
import { circuitBreakerManager, CircuitBreakerState } from './circuit-breaker.service';
import { deadLetterQueue } from './dead-letter-queue.service';

/**
 * 错误恢复策略
 */
interface RecoveryStrategy {
  type: 'retry' | 'rollback' | 'skip' | 'failover' | 'escalate' | 'compensate';
  maxAttempts?: number;
  delay?: number;
  backoffMultiplier?: number;
  fallbackStep?: WorkflowStep;
  compensationSteps?: WorkflowStep[];
  escalationLevel?: 'warning' | 'error' | 'critical';
  condition?: (error: ExecutionError, context: ExecutionContext) => boolean;
}

/**
 * 恢复会话
 */
interface RecoverySession {
  sessionId: string;
  executionId: string;
  workflowId: string;
  failedStepIndex: number;
  originalError: ExecutionError;
  recoveryStrategy: RecoveryStrategy;
  attemptCount: number;
  startTime: Date;
  logs: ExecutionLog[];
  rollbackSteps: number[];
}

/**
 * 错误恢复管理器
 */
export class ErrorRecoveryManager extends EventEmitter {
  private static instance: ErrorRecoveryManager;
  private recoverySessions = new Map<string, RecoverySession>();
  private recoveryStrategies = new Map<string, RecoveryStrategy[]>();
  private isInitialized = false;

  constructor() {
    super();
    this.setupDefaultStrategies();
  }

  /**
   * 获取单例实例
   */
  static getInstance(): ErrorRecoveryManager {
    if (!ErrorRecoveryManager.instance) {
      ErrorRecoveryManager.instance = new ErrorRecoveryManager();
    }
    return ErrorRecoveryManager.instance;
  }

  /**
   * 初始化错误恢复管理器
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      console.log('🚀 初始化ErrorRecoveryManager...');

      // 初始化Circuit Breaker和Dead Letter Queue
      await deadLetterQueue.initialize();

      this.setupEventListeners();
      this.setupCircuitBreakerIntegration();
      this.isInitialized = true;

      console.log('✅ ErrorRecoveryManager初始化完成');
      this.emit('initialized');

    } catch (error) {
      console.error('❌ ErrorRecoveryManager初始化失败:', error);
      throw error;
    }
  }

  /**
   * 处理执行错误并尝试恢复
   */
  async handleExecutionError(
    step: WorkflowStep,
    context: ExecutionContext,
    error: ExecutionError,
    result: ActionExecutionResult
  ): Promise<ActionExecutionResult> {
    try {
      console.log(`🔧 开始处理执行错误: ${error.code} - ${error.message}`);

      // 创建恢复会话
      const session = await this.createRecoverySession(step, context, error);

      // 选择恢复策略
      const strategy = this.selectRecoveryStrategy(error, step, context);
      session.recoveryStrategy = strategy;

      this.addSessionLog(session, 'INFO', `选择恢复策略: ${strategy.type}`, {
        strategy: strategy.type,
        errorType: error.type,
        severity: error.severity
      });

      // 检查Circuit Breaker状态
      const breakerKey = `${step.type}_${context.workflowId}`;
      const circuitBreaker = circuitBreakerManager.getCircuitBreaker(breakerKey);
      
      if (circuitBreaker.getState() === CircuitBreakerState.OPEN) {
        this.addSessionLog(session, 'WARN', 'Circuit Breaker已打开，将消息发送到死信队列');
        
        // 发送到死信队列
        const dlqMessageId = await deadLetterQueue.enqueue(
          step,
          context,
          result,
          error,
          { recoverySessionId: session.sessionId }
        );
        
        return {
          success: false,
          error: {
            ...error,
            details: { ...error.details, dlqMessageId, circuitBreakerOpen: true }
          },
          logs: result.logs.concat(session.logs)
        };
      }

      // 执行恢复策略
      const recoveryResult = await this.executeRecoveryStrategyWithCircuitBreaker(session, step, context, circuitBreaker);

      // 更新会话状态
      if (recoveryResult.success) {
        this.addSessionLog(session, 'INFO', '错误恢复成功');
        this.emit('recovery:success', {
          sessionId: session.sessionId,
          executionId: context.executionId,
          strategy: strategy.type
        });
      } else {
        this.addSessionLog(session, 'ERROR', '错误恢复失败', {
          error: recoveryResult.error
        });
        this.emit('recovery:failed', {
          sessionId: session.sessionId,
          executionId: context.executionId,
          strategy: strategy.type,
          error: recoveryResult.error
        });
      }

      // 清理会话
      this.cleanupSession(session.sessionId);

      return recoveryResult;

    } catch (recoveryError: any) {
      console.error('❌ 错误恢复过程异常:', recoveryError);
      
      const criticalError: ExecutionError = {
        type: ErrorType.SYSTEM_ERROR,
        code: 'RECOVERY_SYSTEM_FAILURE',
        message: `错误恢复系统异常: ${recoveryError.message}`,
        details: { originalError: error, recoveryError: recoveryError.message },
        recoverable: false,
        severity: ErrorSeverity.CRITICAL,
        timestamp: new Date()
      };

      return {
        success: false,
        error: criticalError,
        logs: result.logs.concat([{
          level: 'ERROR',
          message: `错误恢复系统异常: ${recoveryError.message}`,
          timestamp: new Date(),
          source: 'ErrorRecoveryManager'
        }])
      };
    }
  }

  /**
   * 执行工作流回滚
   */
  async executeWorkflowRollback(
    executionId: string,
    workflowId: string,
    failedStepIndex: number,
    executedSteps: WorkflowStep[]
  ): Promise<RollbackResult[]> {
    try {
      console.log(`🔄 开始工作流回滚: ${workflowId}, 失败步骤: ${failedStepIndex}`);

      const rollbackResults: RollbackResult[] = [];
      
      // 按相反顺序回滚已执行的步骤
      const stepsToRollback = executedSteps
        .filter(step => step.index < failedStepIndex)
        .sort((a, b) => b.index - a.index);

      this.emit('workflow:rollback:started', {
        executionId,
        workflowId,
        stepsToRollback: stepsToRollback.length
      });

      for (const step of stepsToRollback) {
        try {
          // 获取执行上下文
          const context = executionContextManager.getContext(executionId, step.index);
          if (!context) {
            console.warn(`⚠️ 无法获取步骤 ${step.index} 的上下文，跳过回滚`);
            continue;
          }

          console.log(`⏪ 回滚步骤 ${step.index}: ${step.name}`);

          // 执行回滚
          const rollbackResult = await actionExecutorManager.rollbackStep(step, context);
          rollbackResults.push(rollbackResult);

          if (rollbackResult.success) {
            console.log(`✅ 步骤 ${step.index} 回滚成功`);
            await executionStateTracker.addLog(executionId, step.index, {
              level: 'INFO',
              message: `步骤回滚成功: ${step.name}`,
              timestamp: new Date(),
              source: 'ErrorRecoveryManager'
            });
          } else {
            console.error(`❌ 步骤 ${step.index} 回滚失败:`, rollbackResult.error);
            await executionStateTracker.addLog(executionId, step.index, {
              level: 'ERROR',
              message: `步骤回滚失败: ${rollbackResult.error?.message}`,
              timestamp: new Date(),
              source: 'ErrorRecoveryManager'
            });
          }

        } catch (error: any) {
          const rollbackError: RollbackResult = {
            success: false,
            error: {
              type: ErrorType.EXECUTION_ERROR,
              code: 'ROLLBACK_EXCEPTION',
              message: `回滚步骤 ${step.index} 时发生异常: ${error.message}`,
              recoverable: false,
              severity: ErrorSeverity.HIGH,
              timestamp: new Date()
            },
            logs: [{
              level: 'ERROR',
              message: `回滚异常: ${error.message}`,
              timestamp: new Date(),
              source: 'ErrorRecoveryManager'
            }]
          };
          rollbackResults.push(rollbackError);
        }
      }

      const successfulRollbacks = rollbackResults.filter(r => r.success).length;
      console.log(`🔄 工作流回滚完成: ${successfulRollbacks}/${rollbackResults.length} 步骤成功回滚`);

      this.emit('workflow:rollback:completed', {
        executionId,
        workflowId,
        totalSteps: rollbackResults.length,
        successfulSteps: successfulRollbacks
      });

      return rollbackResults;

    } catch (error: any) {
      console.error('❌ 工作流回滚异常:', error);
      throw error;
    }
  }

  /**
   * 注册自定义恢复策略
   */
  registerRecoveryStrategy(
    errorPattern: string,
    strategy: RecoveryStrategy
  ): void {
    if (!this.recoveryStrategies.has(errorPattern)) {
      this.recoveryStrategies.set(errorPattern, []);
    }
    
    this.recoveryStrategies.get(errorPattern)!.push(strategy);
    
    console.log(`📝 注册恢复策略: ${errorPattern} -> ${strategy.type}`);
    this.emit('strategy:registered', { errorPattern, strategy });
  }

  /**
   * 获取恢复会话状态
   */
  getRecoverySession(sessionId: string): RecoverySession | undefined {
    return this.recoverySessions.get(sessionId);
  }

  /**
   * 列出活跃的恢复会话
   */
  listActiveRecoverySessions(): RecoverySession[] {
    return Array.from(this.recoverySessions.values());
  }

  /**
   * 获取恢复统计信息
   */
  getRecoveryStats(): any {
    const sessions = Array.from(this.recoverySessions.values());
    const totalSessions = sessions.length;
    
    const strategyStats = sessions.reduce((acc, session) => {
      const strategyType = session.recoveryStrategy.type;
      acc[strategyType] = (acc[strategyType] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const avgAttempts = totalSessions > 0 
      ? sessions.reduce((sum, s) => sum + s.attemptCount, 0) / totalSessions 
      : 0;

    return {
      activeSessions: totalSessions,
      strategyDistribution: strategyStats,
      averageAttempts: Math.round(avgAttempts * 100) / 100,
      totalStrategies: this.recoveryStrategies.size
    };
  }

  // ========== 私有方法 ==========

  /**
   * 设置默认恢复策略
   */
  private setupDefaultStrategies(): void {
    // 网络错误 - 重试策略
    this.registerRecoveryStrategy('NETWORK_ERROR', {
      type: 'retry',
      maxAttempts: 3,
      delay: 2000,
      backoffMultiplier: 2
    });

    // 超时错误 - 重试策略
    this.registerRecoveryStrategy('TIMEOUT_ERROR', {
      type: 'retry',
      maxAttempts: 2,
      delay: 5000,
      backoffMultiplier: 1.5
    });

    // 认证错误 - 跳过或升级
    this.registerRecoveryStrategy('AUTHENTICATION_ERROR', {
      type: 'escalate',
      escalationLevel: 'error'
    });

    // 数据错误 - 补偿策略
    this.registerRecoveryStrategy('DATA_ERROR', {
      type: 'compensate'
    });

    // 系统错误 - 回滚策略
    this.registerRecoveryStrategy('SYSTEM_ERROR', {
      type: 'rollback'
    });

    // 验证错误 - 跳过策略
    this.registerRecoveryStrategy('VALIDATION_ERROR', {
      type: 'skip',
      condition: (error, context) => error.severity !== ErrorSeverity.CRITICAL
    });
  }

  /**
   * 创建恢复会话
   */
  private async createRecoverySession(
    step: WorkflowStep,
    context: ExecutionContext,
    error: ExecutionError
  ): Promise<RecoverySession> {
    const sessionId = `recovery_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const session: RecoverySession = {
      sessionId,
      executionId: context.executionId,
      workflowId: context.workflowId,
      failedStepIndex: step.index,
      originalError: error,
      recoveryStrategy: { type: 'retry' }, // 临时策略，稍后会被替换
      attemptCount: 0,
      startTime: new Date(),
      logs: [],
      rollbackSteps: []
    };

    this.recoverySessions.set(sessionId, session);

    this.addSessionLog(session, 'INFO', `创建恢复会话: ${step.name}`, {
      stepIndex: step.index,
      errorType: error.type,
      errorCode: error.code
    });

    return session;
  }

  /**
   * 选择恢复策略
   */
  private selectRecoveryStrategy(
    error: ExecutionError,
    step: WorkflowStep,
    context: ExecutionContext
  ): RecoveryStrategy {
    // 首先检查步骤级别的错误处理策略
    if (step.errorHandling) {
      return this.convertErrorHandlingToRecoveryStrategy(step.errorHandling);
    }

    // 根据错误类型查找匹配的策略
    const errorTypeStrategies = this.recoveryStrategies.get(error.type);
    if (errorTypeStrategies) {
      for (const strategy of errorTypeStrategies) {
        if (!strategy.condition || strategy.condition(error, context)) {
          return strategy;
        }
      }
    }

    // 根据错误代码查找匹配的策略
    const errorCodeStrategies = this.recoveryStrategies.get(error.code);
    if (errorCodeStrategies) {
      for (const strategy of errorCodeStrategies) {
        if (!strategy.condition || strategy.condition(error, context)) {
          return strategy;
        }
      }
    }

    // 根据错误严重性选择默认策略
    return this.getDefaultStrategyBySeverity(error.severity);
  }

  /**
   * 执行恢复策略（集成Circuit Breaker）
   */
  private async executeRecoveryStrategyWithCircuitBreaker(
    session: RecoverySession,
    step: WorkflowStep,
    context: ExecutionContext,
    circuitBreaker: any
  ): Promise<ActionExecutionResult> {
    return await circuitBreaker.execute(async () => {
      return await this.executeRecoveryStrategy(session, step, context);
    });
  }

  /**
   * 执行恢复策略
   */
  private async executeRecoveryStrategy(
    session: RecoverySession,
    step: WorkflowStep,
    context: ExecutionContext
  ): Promise<ActionExecutionResult> {
    const strategy = session.recoveryStrategy;
    
    switch (strategy.type) {
      case 'retry':
        return await this.executeRetryStrategy(session, step, context);
      case 'rollback':
        return await this.executeRollbackStrategy(session, step, context);
      case 'skip':
        return await this.executeSkipStrategy(session, step, context);
      case 'failover':
        return await this.executeFailoverStrategy(session, step, context);
      case 'escalate':
        return await this.executeEscalateStrategy(session, step, context);
      case 'compensate':
        return await this.executeCompensateStrategy(session, step, context);
      default:
        throw new Error(`不支持的恢复策略: ${strategy.type}`);
    }
  }

  /**
   * 执行重试策略
   */
  private async executeRetryStrategy(
    session: RecoverySession,
    step: WorkflowStep,
    context: ExecutionContext
  ): Promise<ActionExecutionResult> {
    const strategy = session.recoveryStrategy;
    const maxAttempts = strategy.maxAttempts || 3;
    
    while (session.attemptCount < maxAttempts) {
      session.attemptCount++;
      
      this.addSessionLog(session, 'INFO', `第 ${session.attemptCount} 次重试尝试`);

      // 计算延迟时间
      if (strategy.delay && session.attemptCount > 1) {
        const delay = strategy.delay * Math.pow(strategy.backoffMultiplier || 1, session.attemptCount - 2);
        this.addSessionLog(session, 'DEBUG', `等待 ${delay}ms 后重试`);
        await this.sleep(delay);
      }

      // 重新执行步骤
      const result = await actionExecutorManager.executeStep(step, context);
      
      if (result.success) {
        this.addSessionLog(session, 'INFO', `重试成功，共尝试 ${session.attemptCount} 次`);
        return result;
      } else {
        this.addSessionLog(session, 'WARN', `第 ${session.attemptCount} 次重试失败: ${result.error?.message}`);
        
        // 如果是不可重试的错误，立即停止
        if (result.error && !result.error.recoverable) {
          this.addSessionLog(session, 'ERROR', '错误不可重试，将发送到死信队列');
          
          // 发送到死信队列
          await deadLetterQueue.enqueue(
            step,
            context,
            result,
            result.error,
            { recoverySessionId: session.sessionId, reason: 'non_recoverable' }
          );
          
          return result;
        }
      }
    }

    // 所有重试都失败了
    this.addSessionLog(session, 'ERROR', `重试策略失败，已达到最大重试次数 ${maxAttempts}`);
    
    const retryFailedError: ExecutionError = {
      type: ErrorType.EXECUTION_ERROR,
      code: 'RETRY_EXHAUSTED',
      message: `重试失败，已尝试 ${session.attemptCount} 次`,
      details: { originalError: session.originalError, attempts: session.attemptCount },
      recoverable: false,
      severity: ErrorSeverity.HIGH,
      timestamp: new Date()
    };

    return {
      success: false,
      error: retryFailedError,
      logs: session.logs
    };
  }

  /**
   * 执行回滚策略
   */
  private async executeRollbackStrategy(
    session: RecoverySession,
    step: WorkflowStep,
    context: ExecutionContext
  ): Promise<ActionExecutionResult> {
    this.addSessionLog(session, 'INFO', '执行回滚策略');

    try {
      // 回滚当前步骤
      const rollbackResult = await actionExecutorManager.rollbackStep(step, context);
      
      if (rollbackResult.success) {
        this.addSessionLog(session, 'INFO', '步骤回滚成功');
        
        // 标记为跳过（因为已回滚）
        return {
          success: true,
          data: { action: 'rollback', message: '步骤已回滚' },
          logs: session.logs.concat(rollbackResult.logs),
          nextStep: step.index + 1 // 跳过当前步骤
        };
      } else {
        this.addSessionLog(session, 'ERROR', `步骤回滚失败: ${rollbackResult.error?.message}`);
        return {
          success: false,
          error: rollbackResult.error,
          logs: session.logs.concat(rollbackResult.logs)
        };
      }
    } catch (error: any) {
      const rollbackError: ExecutionError = {
        type: ErrorType.EXECUTION_ERROR,
        code: 'ROLLBACK_FAILED',
        message: `回滚失败: ${error.message}`,
        details: { originalError: session.originalError },
        recoverable: false,
        severity: ErrorSeverity.HIGH,
        timestamp: new Date()
      };

      return {
        success: false,
        error: rollbackError,
        logs: session.logs
      };
    }
  }

  /**
   * 执行跳过策略
   */
  private async executeSkipStrategy(
    session: RecoverySession,
    step: WorkflowStep,
    context: ExecutionContext
  ): Promise<ActionExecutionResult> {
    this.addSessionLog(session, 'INFO', '执行跳过策略，忽略错误继续执行');

    return {
      success: true,
      data: { 
        action: 'skip',
        message: '步骤已跳过',
        skippedDueToError: session.originalError.code
      },
      logs: session.logs,
      nextStep: step.index + 1
    };
  }

  /**
   * 执行故障转移策略
   */
  private async executeFailoverStrategy(
    session: RecoverySession,
    step: WorkflowStep,
    context: ExecutionContext
  ): Promise<ActionExecutionResult> {
    const strategy = session.recoveryStrategy;
    
    if (!strategy.fallbackStep) {
      const error: ExecutionError = {
        type: ErrorType.SYSTEM_ERROR,
        code: 'NO_FALLBACK_STEP',
        message: '故障转移策略缺少备用步骤配置',
        recoverable: false,
        severity: ErrorSeverity.HIGH,
        timestamp: new Date()
      };
      return { success: false, error, logs: session.logs };
    }

    this.addSessionLog(session, 'INFO', '执行故障转移策略，使用备用步骤');

    try {
      const result = await actionExecutorManager.executeStep(strategy.fallbackStep, context);
      
      if (result.success) {
        this.addSessionLog(session, 'INFO', '备用步骤执行成功');
      } else {
        this.addSessionLog(session, 'ERROR', `备用步骤执行失败: ${result.error?.message}`);
      }

      return {
        ...result,
        logs: session.logs.concat(result.logs)
      };
    } catch (error: any) {
      const failoverError: ExecutionError = {
        type: ErrorType.EXECUTION_ERROR,
        code: 'FAILOVER_FAILED',
        message: `故障转移失败: ${error.message}`,
        details: { originalError: session.originalError },
        recoverable: false,
        severity: ErrorSeverity.HIGH,
        timestamp: new Date()
      };

      return {
        success: false,
        error: failoverError,
        logs: session.logs
      };
    }
  }

  /**
   * 执行升级策略
   */
  private async executeEscalateStrategy(
    session: RecoverySession,
    step: WorkflowStep,
    context: ExecutionContext
  ): Promise<ActionExecutionResult> {
    const strategy = session.recoveryStrategy;
    const level = strategy.escalationLevel || 'error';
    
    this.addSessionLog(session, 'WARN', `错误已升级到 ${level} 级别`);

    // 发送升级通知
    this.emit('error:escalated', {
      sessionId: session.sessionId,
      executionId: context.executionId,
      workflowId: context.workflowId,
      stepIndex: step.index,
      error: session.originalError,
      level
    });

    // 升级后通常继续失败，但记录升级信息
    const escalatedError: ExecutionError = {
      ...session.originalError,
      severity: ErrorSeverity.CRITICAL,
      details: {
        ...session.originalError.details,
        escalated: true,
        escalationLevel: level,
        escalatedAt: new Date().toISOString()
      }
    };

    return {
      success: false,
      error: escalatedError,
      logs: session.logs
    };
  }

  /**
   * 执行补偿策略
   */
  private async executeCompensateStrategy(
    session: RecoverySession,
    step: WorkflowStep,
    context: ExecutionContext
  ): Promise<ActionExecutionResult> {
    const strategy = session.recoveryStrategy;
    
    if (!strategy.compensationSteps || strategy.compensationSteps.length === 0) {
      this.addSessionLog(session, 'WARN', '补偿策略缺少补偿步骤配置，将跳过');
      return this.executeSkipStrategy(session, step, context);
    }

    this.addSessionLog(session, 'INFO', `执行补偿策略，共 ${strategy.compensationSteps.length} 个补偿步骤`);

    const compensationResults: ActionExecutionResult[] = [];
    
    for (let i = 0; i < strategy.compensationSteps.length; i++) {
      const compensationStep = strategy.compensationSteps[i];
      
      try {
        this.addSessionLog(session, 'INFO', `执行补偿步骤 ${i + 1}: ${compensationStep.name}`);
        
        const result = await actionExecutorManager.executeStep(compensationStep, context);
        compensationResults.push(result);
        
        if (!result.success) {
          this.addSessionLog(session, 'ERROR', `补偿步骤 ${i + 1} 失败: ${result.error?.message}`);
          // 继续执行其他补偿步骤
        }
      } catch (error: any) {
        this.addSessionLog(session, 'ERROR', `补偿步骤 ${i + 1} 异常: ${error.message}`);
      }
    }

    const successfulCompensations = compensationResults.filter(r => r.success).length;
    this.addSessionLog(session, 'INFO', `补偿完成: ${successfulCompensations}/${compensationResults.length} 个步骤成功`);

    // 补偿策略总是返回成功，但携带补偿信息
    return {
      success: true,
      data: {
        action: 'compensate',
        message: '已执行补偿操作',
        compensationResults: compensationResults.map(r => ({
          success: r.success,
          error: r.error?.message
        }))
      },
      logs: session.logs.concat(
        ...compensationResults.map(r => r.logs)
      )
    };
  }

  /**
   * 转换错误处理策略到恢复策略
   */
  private convertErrorHandlingToRecoveryStrategy(errorHandling: ErrorHandlingStrategy): RecoveryStrategy {
    switch (errorHandling.type) {
      case ErrorHandlingType.RETRY:
        return {
          type: 'retry',
          maxAttempts: errorHandling.maxRetries || 3,
          delay: errorHandling.retryDelay || 1000,
          backoffMultiplier: 1.5
        };
      case ErrorHandlingType.SKIP:
        return { type: 'skip' };
      case ErrorHandlingType.FAIL:
        return { type: 'escalate', escalationLevel: 'critical' };
      case ErrorHandlingType.ROLLBACK:
        return { type: 'rollback' };
      case ErrorHandlingType.FALLBACK:
        return {
          type: 'failover',
          fallbackStep: errorHandling.fallbackAction
        };
      default:
        return { type: 'retry', maxAttempts: 1 };
    }
  }

  /**
   * 根据严重性获取默认策略
   */
  private getDefaultStrategyBySeverity(severity: ErrorSeverity): RecoveryStrategy {
    switch (severity) {
      case ErrorSeverity.LOW:
        return { type: 'skip' };
      case ErrorSeverity.MEDIUM:
        return { type: 'retry', maxAttempts: 2, delay: 1000 };
      case ErrorSeverity.HIGH:
        return { type: 'rollback' };
      case ErrorSeverity.CRITICAL:
        return { type: 'escalate', escalationLevel: 'critical' };
      default:
        return { type: 'retry', maxAttempts: 1 };
    }
  }

  /**
   * 添加会话日志
   */
  private addSessionLog(
    session: RecoverySession,
    level: ExecutionLog['level'],
    message: string,
    metadata?: any
  ): void {
    const log: ExecutionLog = {
      level,
      message,
      timestamp: new Date(),
      metadata,
      source: 'ErrorRecoveryManager'
    };
    
    session.logs.push(log);
    
    // 也记录到控制台
    switch (level) {
      case 'DEBUG':
        console.debug(`[${session.sessionId}] ${message}`, metadata);
        break;
      case 'INFO':
        console.log(`[${session.sessionId}] ${message}`, metadata);
        break;
      case 'WARN':
        console.warn(`[${session.sessionId}] ${message}`, metadata);
        break;
      case 'ERROR':
        console.error(`[${session.sessionId}] ${message}`, metadata);
        break;
    }
  }

  /**
   * 清理恢复会话
   */
  private cleanupSession(sessionId: string): void {
    const session = this.recoverySessions.get(sessionId);
    if (session) {
      this.recoverySessions.delete(sessionId);
      this.emit('session:cleaned', { sessionId });
    }
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    // 监听ActionExecutor管理器的错误事件
    actionExecutorManager.on('step:failed', (event) => {
      this.emit('step:error:detected', event);
    });

    // 定期清理超时的恢复会话
    setInterval(() => {
      this.cleanupExpiredSessions();
    }, 60000); // 每分钟检查一次
  }

  /**
   * 设置Circuit Breaker集成
   */
  private setupCircuitBreakerIntegration(): void {
    // 监听Circuit Breaker事件
    circuitBreakerManager.on('breaker:circuit:opened', (event) => {
      console.log(`⚡ Circuit Breaker已打开: ${event.name}`);
      this.emit('circuit:opened', event);
    });

    circuitBreakerManager.on('breaker:state:changed', (event) => {
      this.emit('circuit:state:changed', event);
    });

    // 监听Dead Letter Queue事件
    deadLetterQueue.on('message:enqueued', (event) => {
      console.log(`📮 消息已加入死信队列: ${event.messageId}`);
      this.emit('dlq:message:enqueued', event);
    });

    deadLetterQueue.on('message:resolved', (event) => {
      console.log(`✅ 死信消息已解决: ${event.messageId}`);
      this.emit('dlq:message:resolved', event);
    });

    deadLetterQueue.on('message:permanently_failed', (event) => {
      console.log(`❌ 死信消息永久失败: ${event.messageId}`);
      this.emit('dlq:message:permanently_failed', event);
    });
  }

  /**
   * 清理过期会话
   */
  private cleanupExpiredSessions(): void {
    const now = Date.now();
    const maxAge = 30 * 60 * 1000; // 30分钟

    for (const [sessionId, session] of this.recoverySessions) {
      if (now - session.startTime.getTime() > maxAge) {
        console.log(`🧹 清理过期恢复会话: ${sessionId}`);
        this.cleanupSession(sessionId);
      }
    }
  }

  /**
   * 休眠工具方法
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 全局清理
   */
  async cleanup(): Promise<void> {
    console.log('🧹 开始清理ErrorRecoveryManager...');
    
    this.recoverySessions.clear();
    this.recoveryStrategies.clear();
    
    // 清理Circuit Breaker和Dead Letter Queue
    circuitBreakerManager.cleanup();
    await deadLetterQueue.globalCleanup();
    
    this.isInitialized = false;
    
    console.log('✅ ErrorRecoveryManager清理完成');
    this.emit('cleanup:completed');
  }
}

// 导出单例实例
export const errorRecoveryManager = ErrorRecoveryManager.getInstance();