/**
 * 权限模板业务逻辑服务
 * 
 * 提供权限模板相关的业务逻辑处理
 */

import { prisma } from '@/config/database.config'
import { 
  validatePermissions,
  filterValidPermissions,
  DEFAULT_ROLE_PERMISSIONS,
  getAllPermissions,
  getPermissionDescription,
  type Permission,
  type RoleName
} from '@/constants/permissions'

export interface PermissionTemplateCreate {
  name: string
  description?: string
  permissions: string[]
  category: 'SYSTEM' | 'BUSINESS' | 'SERVICE' | 'READONLY' | 'CUSTOM'
  version?: string
  metadata?: Record<string, any>
  createdBy: string
}

export interface PermissionTemplateUpdate {
  name?: string
  description?: string
  permissions?: string[]
  category?: 'SYSTEM' | 'BUSINESS' | 'SERVICE' | 'READONLY' | 'CUSTOM'
  version?: string
  metadata?: Record<string, any>
  updatedBy: string
}

export interface PermissionTemplateFilter {
  category?: string
  search?: string
  isDefault?: boolean
  isSystem?: boolean
  createdBy?: string
}

export interface PermissionTemplateApplication {
  templateId: string
  roleId: string
  appliedBy: string
  note?: string
}

// ==================== 模板基础操作服务 ====================

/**
 * 权限模板服务类
 */
export class PermissionTemplateService {
  
  /**
   * 创建权限模板
   */
  async createTemplate(data: PermissionTemplateCreate) {
    // 验证权限格式
    const validation = validatePermissions(data.permissions)
    if (!validation.valid) {
      throw new Error(`权限格式无效: ${validation.invalidPermissions.join(', ')}`)
    }

    // 检查名称唯一性
    const existing = await prisma.permissionTemplate.findUnique({
      where: { name: data.name }
    })

    if (existing) {
      throw new Error('模板名称已存在')
    }

    // 过滤有效权限
    const validPermissions = filterValidPermissions(data.permissions)

    // 创建模板
    const template = await prisma.permissionTemplate.create({
      data: {
        name: data.name,
        description: data.description,
        permissions: JSON.stringify(validPermissions),
        category: data.category,
        version: data.version || '1.0',
        metadata: data.metadata,
        createdBy: data.createdBy
      },
      include: {
        createdByUser: {
          select: {
            id: true,
            username: true,
            fullName: true
          }
        }
      }
    })

    // 记录创建历史
    await this.recordHistory(template.id, 'CREATE', null, {
      name: template.name,
      permissions: validPermissions,
      category: template.category
    }, '创建新权限模板', data.createdBy)

    return template
  }

  /**
   * 更新权限模板
   */
  async updateTemplate(templateId: string, data: PermissionTemplateUpdate) {
    // 获取原模板
    const existingTemplate = await prisma.permissionTemplate.findUnique({
      where: { id: templateId }
    })

    if (!existingTemplate) {
      throw new Error('权限模板不存在')
    }

    if (existingTemplate.isSystem) {
      throw new Error('系统模板不允许修改')
    }

    // 验证新权限（如果有）
    if (data.permissions) {
      const validation = validatePermissions(data.permissions)
      if (!validation.valid) {
        throw new Error(`权限格式无效: ${validation.invalidPermissions.join(', ')}`)
      }
    }

    // 检查名称唯一性（如果要更新名称）
    if (data.name && data.name !== existingTemplate.name) {
      const nameExists = await prisma.permissionTemplate.findUnique({
        where: { name: data.name }
      })
      if (nameExists) {
        throw new Error('模板名称已存在')
      }
    }

    // 准备更新数据
    const updateData: any = {
      updatedBy: data.updatedBy
    }

    if (data.name) updateData.name = data.name
    if (data.description !== undefined) updateData.description = data.description
    if (data.permissions) {
      updateData.permissions = JSON.stringify(filterValidPermissions(data.permissions))
    }
    if (data.category) updateData.category = data.category
    if (data.version) updateData.version = data.version
    if (data.metadata !== undefined) updateData.metadata = data.metadata

    // 更新模板
    const updatedTemplate = await prisma.permissionTemplate.update({
      where: { id: templateId },
      data: updateData,
      include: {
        updatedByUser: {
          select: {
            id: true,
            username: true,
            fullName: true
          }
        }
      }
    })

    // 记录更新历史
    await this.recordHistory(templateId, 'UPDATE', {
      name: existingTemplate.name,
      permissions: existingTemplate.permissions,
      category: existingTemplate.category
    }, {
      name: updatedTemplate.name,
      permissions: updatedTemplate.permissions,
      category: updatedTemplate.category
    }, '更新权限模板', data.updatedBy)

    return updatedTemplate
  }

  /**
   * 删除权限模板
   */
  async deleteTemplate(templateId: string, userId: string) {
    // 获取模板信息
    const template = await prisma.permissionTemplate.findUnique({
      where: { id: templateId },
      include: {
        _count: {
          select: {
            templateUsage: {
              where: { status: 'ACTIVE' }
            }
          }
        }
      }
    })

    if (!template) {
      throw new Error('权限模板不存在')
    }

    if (template.isSystem || template.isDefault) {
      throw new Error('系统模板和默认模板不允许删除')
    }

    if (template._count.templateUsage > 0) {
      throw new Error(`该模板正在被${template._count.templateUsage}个角色使用，无法删除`)
    }

    // 删除模板
    await prisma.permissionTemplate.delete({
      where: { id: templateId }
    })

    return template
  }

  /**
   * 应用模板到角色
   */
  async applyTemplateToRole(templateId: string, roleId: string, appliedBy: string, note?: string) {
    // 获取模板和角色信息
    const [template, role] = await Promise.all([
      prisma.permissionTemplate.findUnique({ where: { id: templateId } }),
      prisma.role.findUnique({ where: { id: roleId } })
    ])

    if (!template) {
      throw new Error('权限模板不存在')
    }

    if (!role) {
      throw new Error('角色不存在')
    }

    // 获取模板权限
    const templatePermissions = Array.isArray(template.permissions) 
      ? template.permissions 
      : JSON.parse(template.permissions as string)

    // 更新角色权限
    await prisma.role.update({
      where: { id: roleId },
      data: {
        permissions: JSON.stringify(templatePermissions)
      }
    })

    // 记录模板使用
    const usage = await prisma.permissionTemplateUsage.upsert({
      where: {
        templateId_roleId: {
          templateId: templateId,
          roleId: roleId
        }
      },
      update: {
        appliedBy: appliedBy,
        appliedAt: new Date(),
        status: 'ACTIVE',
        note: note
      },
      create: {
        templateId: templateId,
        roleId: roleId,
        appliedBy: appliedBy,
        status: 'ACTIVE',
        note: note
      }
    })

    // 记录模板历史
    await this.recordHistory(templateId, 'APPLY', null, {
      appliedToRole: {
        id: role.id,
        name: role.name
      }
    }, `应用模板到角色: ${role.name}`, appliedBy)

    return usage
  }

  /**
   * 批量应用模板到多个角色
   */
  async batchApplyTemplate(templateId: string, applications: PermissionTemplateApplication[]) {
    // 获取模板信息
    const template = await prisma.permissionTemplate.findUnique({
      where: { id: templateId }
    })

    if (!template) {
      throw new Error('权限模板不存在')
    }

    // 获取角色信息
    const roleIds = applications.map(app => app.roleId)
    const roles = await prisma.role.findMany({
      where: { id: { in: roleIds } }
    })

    if (roles.length !== roleIds.length) {
      const existingRoleIds = roles.map(r => r.id)
      const missingRoleIds = roleIds.filter(id => !existingRoleIds.includes(id))
      throw new Error(`角色不存在: ${missingRoleIds.join(', ')}`)
    }

    // 获取模板权限
    const templatePermissions = Array.isArray(template.permissions) 
      ? template.permissions 
      : JSON.parse(template.permissions as string)

    const results = []

    // 逐个应用
    for (const application of applications) {
      try {
        // 更新角色权限
        await prisma.role.update({
          where: { id: application.roleId },
          data: {
            permissions: JSON.stringify(templatePermissions)
          }
        })

        // 记录模板使用
        await prisma.permissionTemplateUsage.upsert({
          where: {
            templateId_roleId: {
              templateId: templateId,
              roleId: application.roleId
            }
          },
          update: {
            appliedBy: application.appliedBy,
            appliedAt: new Date(),
            status: 'ACTIVE',
            note: application.note
          },
          create: {
            templateId: templateId,
            roleId: application.roleId,
            appliedBy: application.appliedBy,
            status: 'ACTIVE',
            note: application.note
          }
        })

        results.push({
          roleId: application.roleId,
          success: true
        })
      } catch (error) {
        results.push({
          roleId: application.roleId,
          success: false,
          error: (error as Error).message
        })
      }
    }

    // 记录批量应用历史
    await this.recordHistory(templateId, 'APPLY', null, {
      batchAppliedToRoles: applications.map(app => app.roleId)
    }, `批量应用模板到${applications.length}个角色`, applications[0]?.appliedBy)

    return results
  }

  /**
   * 复制权限模板
   */
  async copyTemplate(sourceTemplateId: string, newName: string, description?: string, createdBy?: string) {
    // 获取源模板
    const sourceTemplate = await prisma.permissionTemplate.findUnique({
      where: { id: sourceTemplateId }
    })

    if (!sourceTemplate) {
      throw new Error('源权限模板不存在')
    }

    // 检查新名称是否已存在
    const existingTemplate = await prisma.permissionTemplate.findUnique({
      where: { name: newName }
    })

    if (existingTemplate) {
      throw new Error('模板名称已存在')
    }

    // 创建副本
    const copiedTemplate = await prisma.permissionTemplate.create({
      data: {
        name: newName,
        description: description || `复制自：${sourceTemplate.name}`,
        permissions: sourceTemplate.permissions,
        category: sourceTemplate.category,
        version: sourceTemplate.version,
        metadata: {
          ...sourceTemplate.metadata as any,
          copiedFrom: {
            templateId: sourceTemplate.id,
            templateName: sourceTemplate.name,
            copiedAt: new Date().toISOString()
          }
        },
        createdBy: createdBy || sourceTemplate.createdBy
      },
      include: {
        createdByUser: {
          select: {
            id: true,
            username: true,
            fullName: true
          }
        }
      }
    })

    // 记录复制历史
    await this.recordHistory(copiedTemplate.id, 'COPY', null, {
      name: copiedTemplate.name,
      copiedFromTemplateId: sourceTemplate.id,
      copiedFromTemplateName: sourceTemplate.name
    }, '复制权限模板', createdBy || sourceTemplate.createdBy)

    return copiedTemplate
  }

  /**
   * 记录操作历史
   */
  private async recordHistory(
    templateId: string,
    action: 'CREATE' | 'UPDATE' | 'DELETE' | 'APPLY' | 'EXPORT' | 'IMPORT' | 'COPY',
    oldData?: any,
    newData?: any,
    changeReason?: string,
    changedBy?: string
  ) {
    if (!changedBy) return

    await prisma.permissionTemplateHistory.create({
      data: {
        templateId,
        action,
        oldData,
        newData,
        changeReason,
        changedBy
      }
    })
  }

  // ==================== 查询和统计服务 ====================

  /**
   * 获取权限模板列表
   */
  async getTemplates(
    filter: PermissionTemplateFilter = {},
    pagination: { page: number; pageSize: number } = { page: 1, pageSize: 20 }
  ) {
    const { page, pageSize } = pagination
    const skip = (page - 1) * pageSize

    // 构建查询条件
    const where: any = {}
    
    if (filter.category) {
      where.category = filter.category
    }
    
    if (filter.search) {
      where.OR = [
        { name: { contains: filter.search } },
        { description: { contains: filter.search } }
      ]
    }
    
    if (filter.isDefault !== undefined) {
      where.isDefault = filter.isDefault
    }
    
    if (filter.isSystem !== undefined) {
      where.isSystem = filter.isSystem
    }

    if (filter.createdBy) {
      where.createdBy = filter.createdBy
    }

    // 获取数据
    const [templates, total] = await Promise.all([
      prisma.permissionTemplate.findMany({
        where,
        skip,
        take: pageSize,
        orderBy: { updatedAt: 'desc' },
        include: {
          createdByUser: {
            select: {
              id: true,
              username: true,
              fullName: true
            }
          },
          updatedByUser: {
            select: {
              id: true,
              username: true,
              fullName: true
            }
          },
          _count: {
            select: {
              templateUsage: {
                where: { status: 'ACTIVE' }
              }
            }
          }
        }
      }),
      prisma.permissionTemplate.count({ where })
    ])

    return {
      templates,
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize)
    }
  }

  /**
   * 获取权限模板详情
   */
  async getTemplateById(templateId: string, includeHistory: boolean = false) {
    const include: any = {
      createdByUser: {
        select: {
          id: true,
          username: true,
          fullName: true
        }
      },
      updatedByUser: {
        select: {
          id: true,
          username: true,
          fullName: true
        }
      },
      templateUsage: {
        include: {
          role: {
            select: {
              id: true,
              name: true,
              description: true
            }
          },
          appliedByUser: {
            select: {
              id: true,
              username: true,
              fullName: true
            }
          }
        },
        orderBy: { appliedAt: 'desc' }
      }
    }

    if (includeHistory) {
      include.templateHistory = {
        include: {
          changedByUser: {
            select: {
              id: true,
              username: true,
              fullName: true
            }
          }
        },
        orderBy: { createdAt: 'desc' },
        take: 20
      }
    }

    const template = await prisma.permissionTemplate.findUnique({
      where: { id: templateId },
      include
    })

    return template
  }

  /**
   * 比较权限模板
   */
  async compareTemplates(templateIds: string[]) {
    // 获取模板信息
    const templates = await prisma.permissionTemplate.findMany({
      where: { id: { in: templateIds } },
      select: {
        id: true,
        name: true,
        description: true,
        permissions: true,
        category: true,
        version: true
      }
    })

    if (templates.length !== templateIds.length) {
      const existingIds = templates.map(t => t.id)
      const missingIds = templateIds.filter(id => !existingIds.includes(id))
      throw new Error(`模板不存在: ${missingIds.join(', ')}`)
    }

    // 处理权限数据
    const templatesData = templates.map(template => {
      const permissions = Array.isArray(template.permissions) 
        ? template.permissions 
        : JSON.parse(template.permissions as string)

      return {
        ...template,
        permissions: permissions as string[]
      }
    })

    // 获取所有权限的并集
    const allPermissions = new Set<string>()
    templatesData.forEach(template => {
      template.permissions.forEach(permission => allPermissions.add(permission))
    })

    // 构建比较矩阵
    const comparisonMatrix = Array.from(allPermissions).map(permission => {
      const row: any = {
        permission,
        description: getPermissionDescription(permission)
      }

      templatesData.forEach(template => {
        row[template.id] = template.permissions.includes(permission)
      })

      return row
    })

    // 计算相似度
    const similarities: any[] = []
    for (let i = 0; i < templatesData.length; i++) {
      for (let j = i + 1; j < templatesData.length; j++) {
        const template1 = templatesData[i]
        const template2 = templatesData[j]
        
        const set1 = new Set(template1.permissions)
        const set2 = new Set(template2.permissions)
        
        const intersection = new Set([...set1].filter(x => set2.has(x)))
        const union = new Set([...set1, ...set2])
        
        const similarity = (intersection.size / union.size) * 100

        similarities.push({
          template1: { id: template1.id, name: template1.name },
          template2: { id: template2.id, name: template2.name },
          similarity: Math.round(similarity * 100) / 100,
          commonPermissions: intersection.size,
          totalPermissions: union.size,
          onlyInTemplate1: [...set1].filter(x => !set2.has(x)).length,
          onlyInTemplate2: [...set2].filter(x => !set1.has(x)).length
        })
      }
    }

    return {
      templates: templatesData,
      comparisonMatrix,
      similarities,
      totalUniquePermissions: allPermissions.size
    }
  }

  /**
   * 获取权限模板统计
   */
  async getTemplateStats() {
    const [
      totalCount,
      defaultCount,
      systemCount,
      categoryStats,
      usageStats,
      recentActivity
    ] = await Promise.all([
      // 总模板数
      prisma.permissionTemplate.count(),
      
      // 默认模板数
      prisma.permissionTemplate.count({
        where: { isDefault: true }
      }),
      
      // 系统模板数
      prisma.permissionTemplate.count({
        where: { isSystem: true }
      }),
      
      // 分类统计
      prisma.permissionTemplate.groupBy({
        by: ['category'],
        _count: { id: true }
      }),
      
      // 使用情况统计
      prisma.permissionTemplateUsage.groupBy({
        by: ['status'],
        _count: { id: true }
      }),
      
      // 最近活动
      prisma.permissionTemplateHistory.findMany({
        take: 10,
        orderBy: { createdAt: 'desc' },
        include: {
          template: {
            select: { name: true }
          },
          changedByUser: {
            select: { username: true, fullName: true }
          }
        }
      })
    ])

    // 获取最受欢迎的模板
    const popularTemplates = await prisma.permissionTemplate.findMany({
      include: {
        _count: {
          select: {
            templateUsage: {
              where: { status: 'ACTIVE' }
            }
          }
        }
      },
      orderBy: {
        templateUsage: {
          _count: 'desc'
        }
      },
      take: 5
    })

    return {
      overview: {
        total: totalCount,
        default: defaultCount,
        system: systemCount,
        custom: totalCount - defaultCount,
        active: usageStats.find(s => s.status === 'ACTIVE')?._count.id || 0
      },
      categoryDistribution: categoryStats,
      usageDistribution: usageStats,
      popularTemplates,
      recentActivity
    }
  }

  // ==================== 默认模板管理服务 ====================

  /**
   * 创建默认权限模板
   */
  async createDefaultTemplates(createdBy: string) {
    const results = []

    // 基于默认角色权限创建模板
    for (const [roleName, permissions] of Object.entries(DEFAULT_ROLE_PERMISSIONS)) {
      try {
        const templateName = `默认-${this.getRoleDisplayName(roleName as RoleName)}`
        
        // 检查是否已存在
        const existing = await prisma.permissionTemplate.findUnique({
          where: { name: templateName }
        })

        if (existing) {
          results.push({
            roleName,
            templateName,
            success: false,
            message: '模板已存在'
          })
          continue
        }

        // 创建默认模板
        const template = await prisma.permissionTemplate.create({
          data: {
            name: templateName,
            description: `基于${this.getRoleDisplayName(roleName as RoleName)}角色的默认权限配置`,
            permissions: JSON.stringify(permissions),
            category: this.getCategoryByRole(roleName as RoleName),
            version: '1.0',
            isDefault: true,
            isSystem: true,
            createdBy: createdBy,
            metadata: {
              sourceRole: roleName,
              isSystemGenerated: true
            }
          }
        })

        // 记录历史
        await this.recordHistory(template.id, 'CREATE', null, {
          name: template.name,
          permissions: permissions,
          sourceRole: roleName
        }, '创建默认权限模板', createdBy)

        results.push({
          roleName,
          templateName,
          templateId: template.id,
          success: true,
          permissionCount: permissions.length
        })
      } catch (error) {
        results.push({
          roleName,
          templateName: `默认-${this.getRoleDisplayName(roleName as RoleName)}`,
          success: false,
          error: (error as Error).message
        })
      }
    }

    return results
  }

  /**
   * 从角色权限创建模板
   */
  async createTemplateFromRole(roleId: string, templateName: string, description: string, createdBy: string) {
    // 获取角色信息
    const role = await prisma.role.findUnique({
      where: { id: roleId }
    })

    if (!role) {
      throw new Error('角色不存在')
    }

    // 检查模板名称是否已存在
    const existingTemplate = await prisma.permissionTemplate.findUnique({
      where: { name: templateName }
    })

    if (existingTemplate) {
      throw new Error('模板名称已存在')
    }

    // 获取角色权限
    const rolePermissions = Array.isArray(role.permissions) 
      ? role.permissions 
      : JSON.parse(role.permissions as string)

    // 创建模板
    const template = await prisma.permissionTemplate.create({
      data: {
        name: templateName,
        description: description || `基于角色 ${role.name} 创建的权限模板`,
        permissions: JSON.stringify(rolePermissions),
        category: 'CUSTOM',
        version: '1.0',
        metadata: {
          sourceRoleId: role.id,
          sourceRoleName: role.name,
          createdFromRole: true
        },
        createdBy: createdBy
      },
      include: {
        createdByUser: {
          select: {
            id: true,
            username: true,
            fullName: true
          }
        }
      }
    })

    // 记录创建历史
    await this.recordHistory(template.id, 'CREATE', null, {
      name: template.name,
      sourceRoleId: role.id,
      sourceRoleName: role.name
    }, `从角色 ${role.name} 创建权限模板`, createdBy)

    return template
  }

  // ==================== 辅助方法 ====================

  /**
   * 获取角色显示名称
   */
  private getRoleDisplayName(roleName: RoleName): string {
    const roleNames = {
      admin: '超级管理员',
      engineer: '运维工程师',
      customer_service: '客户服务',
      project_manager: '项目经理',
      ops_supervisor: '运维主管',
      readonly: '只读用户'
    }
    return roleNames[roleName] || roleName
  }

  /**
   * 根据角色获取模板分类
   */
  private getCategoryByRole(roleName: RoleName): 'SYSTEM' | 'BUSINESS' | 'SERVICE' | 'READONLY' | 'CUSTOM' {
    const categoryMapping = {
      admin: 'SYSTEM' as const,
      engineer: 'SERVICE' as const,
      customer_service: 'BUSINESS' as const,
      project_manager: 'BUSINESS' as const,
      ops_supervisor: 'SYSTEM' as const,
      readonly: 'READONLY' as const
    }
    return categoryMapping[roleName] || 'CUSTOM'
  }

  /**
   * 验证权限模板数据
   */
  async validateTemplateData(data: Partial<PermissionTemplateCreate>) {
    const errors: string[] = []

    // 验证必需字段
    if (!data.name) {
      errors.push('模板名称不能为空')
    }

    if (!data.permissions || data.permissions.length === 0) {
      errors.push('权限列表不能为空')
    }

    // 验证名称唯一性
    if (data.name) {
      const existing = await prisma.permissionTemplate.findUnique({
        where: { name: data.name }
      })
      if (existing) {
        errors.push('模板名称已存在')
      }
    }

    // 验证权限格式
    if (data.permissions && data.permissions.length > 0) {
      const validation = validatePermissions(data.permissions)
      if (!validation.valid) {
        errors.push(`权限格式无效: ${validation.invalidPermissions.join(', ')}`)
      }
    }

    return {
      valid: errors.length === 0,
      errors
    }
  }

  /**
   * 清理无效的模板使用记录
   */
  async cleanupInvalidUsage() {
    // 查找已删除角色的使用记录
    const invalidUsage = await prisma.permissionTemplateUsage.findMany({
      where: {
        role: null
      },
      select: { id: true }
    })

    if (invalidUsage.length > 0) {
      await prisma.permissionTemplateUsage.deleteMany({
        where: {
          id: { in: invalidUsage.map(u => u.id) }
        }
      })
    }

    return invalidUsage.length
  }
}

// 导出服务实例
export const permissionTemplateService = new PermissionTemplateService()
export default permissionTemplateService