import { prisma } from '@/config/database.config';
import { workflowEngine, type WorkflowConfig } from '@/services/workflow-engine.service';
import type { 
  WorkflowDefinition, 
  WorkflowExecution, 
  WorkflowExecutionStep,
  WorkflowTemplate,
  WorkflowCategory,
  WorkflowPriority,
  WorkflowTriggerType
} from '@prisma/client';

// 工作流创建数据接口
export interface CreateWorkflowData {
  name: string;
  description?: string;
  category: WorkflowCategory;
  priority?: WorkflowPriority;
  config: WorkflowConfig;
  tags?: string[];
  isTemplate?: boolean;
}

// 工作流更新数据接口
export interface UpdateWorkflowData {
  name?: string;
  description?: string;
  category?: WorkflowCategory;
  priority?: WorkflowPriority;
  config?: WorkflowConfig;
  tags?: string[];
  isActive?: boolean;
}

// 工作流查询参数接口
export interface WorkflowQueryParams {
  page?: number;
  limit?: number;
  category?: WorkflowCategory;
  isActive?: boolean;
  search?: string;
  tags?: string[];
  createdBy?: string;
}

// 执行查询参数接口
export interface ExecutionQueryParams {
  page?: number;
  limit?: number;
  workflowId?: string;
  status?: string;
  triggerType?: WorkflowTriggerType;
  dateRange?: {
    start: Date;
    end: Date;
  };
}

// 工作流统计接口
export interface WorkflowStats {
  totalWorkflows: number;
  activeWorkflows: number;
  totalExecutions: number;
  runningExecutions: number;
  successRate: number;
  avgExecutionTime: number;
  executionsByCategory: Record<string, number>;
  executionsByStatus: Record<string, number>;
}

export class WorkflowService {
  /**
   * 创建工作流定义
   */
  static async createWorkflow(data: CreateWorkflowData, userId: string): Promise<WorkflowDefinition> {
    // 验证配置
    this.validateWorkflowConfig(data.config);

    const workflow = await prisma.workflowDefinition.create({
      data: {
        name: data.name,
        description: data.description,
        category: data.category,
        priority: data.priority || 'MEDIUM',
        triggerConfig: data.config.trigger,
        stepsConfig: data.config,
        variables: data.config.variables || {},
        settings: data.config.settings || {},
        tags: data.tags || [],
        isTemplate: data.isTemplate || false,
        createdBy: userId,
        metadata: {
          stepCount: data.config.steps.length,
          hasConditions: data.config.steps.some(step => step.type === 'CONDITION'),
          hasApprovals: data.config.steps.some(step => step.type === 'APPROVAL'),
          estimatedDuration: this.estimateExecutionTime(data.config)
        }
      }
    });

    return workflow;
  }

  /**
   * 更新工作流定义
   */
  static async updateWorkflow(id: string, data: UpdateWorkflowData, userId: string): Promise<WorkflowDefinition> {
    const existingWorkflow = await prisma.workflowDefinition.findUnique({
      where: { id }
    });

    if (!existingWorkflow) {
      throw new Error('工作流不存在');
    }

    // 如果有配置更新，验证新配置
    if (data.config) {
      this.validateWorkflowConfig(data.config);
    }

    const updateData: any = {
      updatedBy: userId
    };

    if (data.name) updateData.name = data.name;
    if (data.description !== undefined) updateData.description = data.description;
    if (data.category) updateData.category = data.category;
    if (data.priority) updateData.priority = data.priority;
    if (data.tags !== undefined) updateData.tags = data.tags;
    if (data.isActive !== undefined) updateData.isActive = data.isActive;

    if (data.config) {
      updateData.triggerConfig = data.config.trigger;
      updateData.stepsConfig = data.config;
      updateData.variables = data.config.variables || {};
      updateData.settings = data.config.settings || {};
      updateData.metadata = {
        stepCount: data.config.steps.length,
        hasConditions: data.config.steps.some(step => step.type === 'CONDITION'),
        hasApprovals: data.config.steps.some(step => step.type === 'APPROVAL'),
        estimatedDuration: this.estimateExecutionTime(data.config)
      };
    }

    return await prisma.workflowDefinition.update({
      where: { id },
      data: updateData
    });
  }

  /**
   * 获取工作流列表
   */
  static async getWorkflows(params: WorkflowQueryParams) {
    const {
      page = 1,
      limit = 20,
      category,
      isActive,
      search,
      tags,
      createdBy
    } = params;

    const skip = (page - 1) * limit;
    const where: any = {};

    if (category) where.category = category;
    if (isActive !== undefined) where.isActive = isActive;
    if (createdBy) where.createdBy = createdBy;
    
    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } }
      ];
    }

    if (tags && tags.length > 0) {
      where.tags = {
        hasEvery: tags
      };
    }

    const [workflows, total] = await Promise.all([
      prisma.workflowDefinition.findMany({
        where,
        include: {
          createdByUser: {
            select: { id: true, fullName: true, email: true }
          },
          updatedByUser: {
            select: { id: true, fullName: true, email: true }
          }
        },
        orderBy: { createdAt: 'desc' },
        skip,
        take: limit
      }),
      prisma.workflowDefinition.count({ where })
    ]);

    return {
      workflows,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    };
  }

  /**
   * 获取工作流详情
   */
  static async getWorkflowById(id: string): Promise<WorkflowDefinition> {
    const workflow = await prisma.workflowDefinition.findUnique({
      where: { id },
      include: {
        createdByUser: {
          select: { id: true, fullName: true, email: true }
        },
        updatedByUser: {
          select: { id: true, fullName: true, email: true }
        },
        executions: {
          orderBy: { createdAt: 'desc' },
          take: 10,
          include: {
            triggeredByUser: {
              select: { id: true, fullName: true, email: true }
            }
          }
        }
      }
    });

    if (!workflow) {
      throw new Error('工作流不存在');
    }

    return workflow;
  }

  /**
   * 删除工作流
   */
  static async deleteWorkflow(id: string): Promise<void> {
    // 检查是否有正在运行的执行
    const runningExecutions = await prisma.workflowExecution.count({
      where: {
        workflowId: id,
        status: { in: ['PENDING', 'RUNNING', 'PAUSED'] }
      }
    });

    if (runningExecutions > 0) {
      throw new Error('无法删除有正在运行执行的工作流');
    }

    await prisma.workflowDefinition.delete({
      where: { id }
    });
  }

  /**
   * 复制工作流
   */
  static async cloneWorkflow(id: string, newName: string, userId: string): Promise<WorkflowDefinition> {
    const originalWorkflow = await prisma.workflowDefinition.findUnique({
      where: { id }
    });

    if (!originalWorkflow) {
      throw new Error('原工作流不存在');
    }

    return await prisma.workflowDefinition.create({
      data: {
        name: newName,
        description: originalWorkflow.description,
        category: originalWorkflow.category,
        priority: originalWorkflow.priority,
        triggerConfig: originalWorkflow.triggerConfig,
        stepsConfig: originalWorkflow.stepsConfig,
        conditions: originalWorkflow.conditions,
        variables: originalWorkflow.variables,
        settings: originalWorkflow.settings,
        tags: originalWorkflow.tags,
        metadata: originalWorkflow.metadata,
        isTemplate: false,
        createdBy: userId
      }
    });
  }

  /**
   * 手动触发工作流
   */
  static async triggerWorkflow(
    id: string, 
    triggerData?: any, 
    userId?: string
  ): Promise<{ executionId: string }> {
    const workflow = await prisma.workflowDefinition.findUnique({
      where: { id }
    });

    if (!workflow || !workflow.isActive) {
      throw new Error('工作流不存在或已禁用');
    }

    const executionId = await workflowEngine.triggerWorkflow(
      id, 
      'MANUAL', 
      triggerData, 
      userId
    );

    return { executionId };
  }

  /**
   * 获取执行列表
   */
  static async getExecutions(params: ExecutionQueryParams) {
    const {
      page = 1,
      limit = 20,
      workflowId,
      status,
      triggerType,
      dateRange
    } = params;

    const skip = (page - 1) * limit;
    const where: any = {};

    if (workflowId) where.workflowId = workflowId;
    if (status) where.status = status;
    if (triggerType) where.triggerType = triggerType;
    
    if (dateRange) {
      where.createdAt = {
        gte: dateRange.start,
        lte: dateRange.end
      };
    }

    const [executions, total] = await Promise.all([
      prisma.workflowExecution.findMany({
        where,
        include: {
          workflowDefinition: {
            select: { 
              id: true, 
              name: true, 
              category: true, 
              priority: true 
            }
          },
          triggeredByUser: {
            select: { id: true, fullName: true, email: true }
          },
          steps: {
            orderBy: { stepIndex: 'asc' }
          }
        },
        orderBy: { createdAt: 'desc' },
        skip,
        take: limit
      }),
      prisma.workflowExecution.count({ where })
    ]);

    return {
      executions,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    };
  }

  /**
   * 获取执行详情
   */
  static async getExecutionById(id: string): Promise<WorkflowExecution> {
    const execution = await prisma.workflowExecution.findUnique({
      where: { id },
      include: {
        workflowDefinition: {
          select: { 
            id: true, 
            name: true, 
            description: true,
            category: true, 
            priority: true 
          }
        },
        triggeredByUser: {
          select: { id: true, fullName: true, email: true }
        },
        steps: {
          orderBy: { stepIndex: 'asc' }
        }
      }
    });

    if (!execution) {
      throw new Error('执行记录不存在');
    }

    return execution;
  }

  /**
   * 暂停执行
   */
  static async pauseExecution(executionId: string): Promise<void> {
    await workflowEngine.pauseExecution(executionId);
  }

  /**
   * 恢复执行
   */
  static async resumeExecution(executionId: string): Promise<void> {
    await workflowEngine.resumeExecution(executionId);
  }

  /**
   * 取消执行
   */
  static async cancelExecution(executionId: string): Promise<void> {
    await workflowEngine.cancelExecution(executionId);
  }

  /**
   * 获取工作流统计
   */
  static async getWorkflowStats(): Promise<WorkflowStats> {
    const [
      totalWorkflows,
      activeWorkflows,
      totalExecutions,
      runningExecutions,
      executionStats,
      categoryStats,
      statusStats
    ] = await Promise.all([
      prisma.workflowDefinition.count(),
      prisma.workflowDefinition.count({ where: { isActive: true } }),
      prisma.workflowExecution.count(),
      prisma.workflowExecution.count({
        where: { status: { in: ['PENDING', 'RUNNING', 'PAUSED'] } }
      }),
      prisma.workflowExecution.aggregate({
        _avg: { executionTime: true },
        _count: { id: true },
        where: { status: 'COMPLETED' }
      }),
      prisma.workflowDefinition.groupBy({
        by: ['category'],
        _count: { id: true }
      }),
      prisma.workflowExecution.groupBy({
        by: ['status'],
        _count: { id: true }
      })
    ]);

    const successfulExecutions = await prisma.workflowExecution.count({
      where: { status: 'COMPLETED' }
    });

    const executionsByCategory = categoryStats.reduce((acc, stat) => {
      acc[stat.category] = stat._count.id;
      return acc;
    }, {} as Record<string, number>);

    const executionsByStatus = statusStats.reduce((acc, stat) => {
      acc[stat.status] = stat._count.id;
      return acc;
    }, {} as Record<string, number>);

    return {
      totalWorkflows,
      activeWorkflows,
      totalExecutions,
      runningExecutions,
      successRate: totalExecutions > 0 ? (successfulExecutions / totalExecutions) * 100 : 0,
      avgExecutionTime: executionStats._avg.executionTime || 0,
      executionsByCategory,
      executionsByStatus
    };
  }

  /**
   * 创建工作流模板
   */
  static async createTemplate(data: CreateWorkflowData, userId: string): Promise<WorkflowTemplate> {
    this.validateWorkflowConfig(data.config);

    return await prisma.workflowTemplate.create({
      data: {
        name: data.name,
        description: data.description,
        category: data.category,
        tags: data.tags || [],
        templateConfig: data.config,
        variables: data.config.variables || {},
        requirements: {
          minVersion: '1.0.0',
          dependencies: []
        },
        createdBy: userId
      }
    });
  }

  /**
   * 从模板创建工作流
   */
  static async createFromTemplate(
    templateId: string, 
    name: string, 
    customizations: Partial<WorkflowConfig>, 
    userId: string
  ): Promise<WorkflowDefinition> {
    const template = await prisma.workflowTemplate.findUnique({
      where: { id: templateId }
    });

    if (!template) {
      throw new Error('模板不存在');
    }

    const baseConfig = template.templateConfig as WorkflowConfig;
    const mergedConfig = {
      ...baseConfig,
      ...customizations,
      variables: {
        ...baseConfig.variables,
        ...customizations.variables
      },
      settings: {
        ...baseConfig.settings,
        ...customizations.settings
      }
    };

    this.validateWorkflowConfig(mergedConfig);

    const workflow = await prisma.workflowDefinition.create({
      data: {
        name,
        description: template.description,
        category: template.category,
        triggerConfig: mergedConfig.trigger,
        stepsConfig: mergedConfig,
        variables: mergedConfig.variables || {},
        settings: mergedConfig.settings || {},
        tags: template.tags,
        createdBy: userId
      }
    });

    // 更新模板使用统计
    await prisma.workflowTemplate.update({
      where: { id: templateId },
      data: { usageCount: { increment: 1 } }
    });

    return workflow;
  }

  /**
   * 验证工作流配置
   */
  private static validateWorkflowConfig(config: WorkflowConfig): void {
    if (!config.trigger) {
      throw new Error('工作流必须包含触发器配置');
    }

    if (!config.steps || config.steps.length === 0) {
      throw new Error('工作流必须包含至少一个步骤');
    }

    // 验证步骤索引的连续性
    const stepIndices = config.steps.map(step => step.index).sort((a, b) => a - b);
    for (let i = 0; i < stepIndices.length; i++) {
      if (stepIndices[i] !== i) {
        throw new Error('步骤索引必须连续且从0开始');
      }
    }

    // 验证条件步骤的跳转目标
    config.steps.forEach(step => {
      if (step.type === 'CONDITION') {
        const trueStep = step.config.trueStep;
        const falseStep = step.config.falseStep;
        
        if (trueStep !== undefined && (trueStep < 0 || trueStep >= config.steps.length)) {
          throw new Error(`条件步骤 ${step.index} 的 trueStep 跳转目标无效`);
        }
        
        if (falseStep !== undefined && (falseStep < 0 || falseStep >= config.steps.length)) {
          throw new Error(`条件步骤 ${step.index} 的 falseStep 跳转目标无效`);
        }
      }
    });
  }

  /**
   * 估算执行时间
   */
  private static estimateExecutionTime(config: WorkflowConfig): number {
    let estimatedTime = 0;

    config.steps.forEach(step => {
      switch (step.type) {
        case 'ACTION':
          estimatedTime += 2; // 2秒
          break;
        case 'CONDITION':
          estimatedTime += 0.5; // 0.5秒
          break;
        case 'APPROVAL':
          estimatedTime += 3600; // 1小时（人工审批）
          break;
        case 'NOTIFICATION':
          estimatedTime += 1; // 1秒
          break;
        case 'DELAY':
          estimatedTime += (step.config.delay || 1000) / 1000;
          break;
        case 'HTTP_REQUEST':
          estimatedTime += 5; // 5秒
          break;
        case 'DATABASE_OPERATION':
          estimatedTime += 1; // 1秒
          break;
        default:
          estimatedTime += 1; // 默认1秒
      }
    });

    return estimatedTime;
  }

  /**
   * 获取预设工作流模板
   */
  static getPresetTemplates(): any[] {
    return [
      {
        name: "服务工单自动分配",
        description: "根据工单类型和工程师工作负载自动分配服务工单",
        category: "SERVICE_AUTOMATION",
        tags: ["自动分配", "负载均衡"],
        config: {
          trigger: {
            type: "EVENT",
            config: {
              event: "service.created",
              conditions: []
            }
          },
          steps: [
            {
              index: 0,
              name: "分析工单类型",
              type: "ACTION",
              config: {
                actionType: "analyze_service_type",
                serviceId: "${triggerData.serviceId}"
              }
            },
            {
              index: 1,
              name: "查找合适工程师",
              type: "ACTION",
              config: {
                actionType: "find_best_engineer",
                category: "${step0.category}",
                priority: "${triggerData.priority}"
              }
            },
            {
              index: 2,
              name: "分配工单",
              type: "ACTION",
              config: {
                actionType: "assign_service",
                serviceId: "${triggerData.serviceId}",
                assignedUserId: "${step1.engineerId}"
              }
            },
            {
              index: 3,
              name: "发送通知",
              type: "NOTIFICATION",
              config: {
                recipients: ["${step1.engineerId}", "${triggerData.customerId}"],
                template: "service_assigned",
                data: {
                  serviceId: "${triggerData.serviceId}",
                  assignedUser: "${step1.engineerName}"
                }
              }
            }
          ],
          variables: {},
          settings: {
            timeout: 300,
            errorHandling: "stop"
          }
        }
      },
      {
        name: "SLA超时预警",
        description: "当服务工单接近或超过SLA时限时自动发送预警通知",
        category: "SLA_MONITORING",
        tags: ["SLA", "预警", "监控"],
        config: {
          trigger: {
            type: "SCHEDULED",
            config: {
              schedule: "*/15 * * * *", // 每15分钟检查
              conditions: []
            }
          },
          steps: [
            {
              index: 0,
              name: "查询即将超时的工单",
              type: "DATABASE_OPERATION",
              config: {
                operation: "select",
                query: "SELECT * FROM services WHERE status IN ('OPEN', 'IN_PROGRESS') AND sla_deadline < DATE_ADD(NOW(), INTERVAL 1 HOUR)"
              }
            },
            {
              index: 1,
              name: "检查是否有超时工单",
              type: "CONDITION",
              config: {
                condition: {
                  type: "compare",
                  left: "${step0.results.length}",
                  operator: ">",
                  right: 0
                },
                trueStep: 2,
                falseStep: -1 // 结束
              }
            },
            {
              index: 2,
              name: "发送预警通知",
              type: "NOTIFICATION",
              config: {
                recipients: ["admin", "manager"],
                template: "sla_warning",
                data: {
                  overdueServices: "${step0.results}"
                }
              }
            }
          ],
          variables: {},
          settings: {
            timeout: 600,
            errorHandling: "continue"
          }
        }
      }
    ];
  }
}