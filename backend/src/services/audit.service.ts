import { prisma } from '@/config/database.config'
import ExcelJS from 'exceljs'
import { RealtimeService } from './realtime.service'

export interface AuditLogEntry {
  userId: string
  action: string
  resource: string
  resourceId?: string | null | undefined
  details?: Record<string, any> | null | undefined
  ipAddress?: string | null | undefined
  userAgent?: string | null | undefined
}

export class AuditService {
  /**
   * 记录操作日志
   */
  static async log(entry: AuditLogEntry): Promise<void> {
    try {
      const logEntry = await prisma.operationLog.create({
        data: {
          userId: entry.userId,
          action: entry.action,
          resource: entry.resource,
          resourceId: entry.resourceId || null,
          details: entry.details ? JSON.stringify(entry.details) : null,
          ipAddress: entry.ipAddress || null,
          userAgent: entry.userAgent || null
        },
        include: {
          user: {
            select: {
              username: true,
              fullName: true
            }
          }
        }
      })

      // 实时推送审计日志
      const realtimeService = RealtimeService.getInstance()
      await realtimeService.pushAuditLog({
        id: logEntry.id,
        userId: entry.userId,
        username: logEntry.user.username,
        fullName: logEntry.user.fullName || logEntry.user.username,
        action: entry.action,
        resource: entry.resource,
        resourceId: entry.resourceId,
        ip: entry.ipAddress || 'unknown',
        details: entry.details
      }).catch(error => {
        console.error('Failed to push audit log realtime:', error)
        // 不抛出错误，避免影响主要业务流程
      })
    } catch (error) {
      console.error('Failed to create audit log:', error)
      // 不抛出错误，避免影响主要业务流程
    }
  }

  /**
   * 记录用户登录
   */
  static async logLogin(userId: string, ipAddress?: string, userAgent?: string): Promise<void> {
    await this.log({
      userId,
      action: 'LOGIN',
      resource: 'AUTH',
      ipAddress: ipAddress || null,
      userAgent: userAgent || null
    })
  }

  /**
   * 记录用户登出
   */
  static async logLogout(userId: string, ipAddress?: string, userAgent?: string): Promise<void> {
    await this.log({
      userId,
      action: 'LOGOUT',
      resource: 'AUTH',
      ipAddress: ipAddress || null,
      userAgent: userAgent || null
    })
  }

  /**
   * 记录客户操作
   */
  static async logCustomerAction(
    userId: string,
    action: 'CREATE' | 'UPDATE' | 'DELETE',
    customerId: string,
    details?: Record<string, any>,
    ipAddress?: string
  ): Promise<void> {
    await this.log({
      userId,
      action,
      resource: 'CUSTOMER',
      resourceId: customerId,
      details: details || null,
      ipAddress: ipAddress || null
    })
  }

  /**
   * 记录项目档案操作
   */
  static async logArchiveAction(
    userId: string,
    action: 'CREATE' | 'UPDATE' | 'DELETE',
    archiveId: string,
    details?: Record<string, any>,
    ipAddress?: string
  ): Promise<void> {
    await this.log({
      userId,
      action,
      resource: 'ARCHIVE',
      resourceId: archiveId,
      details: details || null,
      ipAddress: ipAddress || null
    })
  }

  /**
   * 记录服务工单操作
   */
  static async logServiceAction(
    userId: string,
    action: 'CREATE' | 'UPDATE' | 'DELETE' | 'STATUS_CHANGE',
    serviceId: string,
    details?: Record<string, any>,
    ipAddress?: string
  ): Promise<void> {
    await this.log({
      userId,
      action,
      resource: 'SERVICE',
      resourceId: serviceId,
      details: details || null,
      ipAddress: ipAddress || null
    })
  }

  /**
   * 记录配置操作
   */
  static async logConfigurationAction(
    userId: string,
    action: 'CREATE' | 'UPDATE' | 'DELETE' | 'VIEW',
    configId: string,
    details?: Record<string, any>,
    ipAddress?: string
  ): Promise<void> {
    await this.log({
      userId,
      action,
      resource: 'CONFIGURATION',
      resourceId: configId,
      details: details || null,
      ipAddress: ipAddress || null
    })
  }

  /**
   * 记录SLA操作
   */
  static async logSlaAction(
    userId: string,
    action: 'CREATE' | 'UPDATE' | 'DELETE',
    slaId: string,
    details?: Record<string, any>,
    ipAddress?: string
  ): Promise<void> {
    await this.log({
      userId,
      action,
      resource: 'SLA',
      resourceId: slaId,
      details: details || null,
      ipAddress: ipAddress || null
    })
  }

  /**
   * 记录用户操作
   */
  static async logUserAction(
    userId: string,
    action: 'CREATE' | 'UPDATE' | 'DELETE' | 'BATCH_OPERATION' | 'EXPORT',
    targetUserId: string,
    details?: Record<string, any>,
    ipAddress?: string
  ): Promise<void> {
    await this.log({
      userId,
      action,
      resource: 'USER',
      resourceId: targetUserId,
      details: details || null,
      ipAddress: ipAddress || null
    })
  }

  /**
   * 获取操作日志
   */
  static async getLogs(
    page: number = 1,
    limit: number = 50,
    userId?: string,
    resource?: string,
    action?: string,
    startDate?: Date,
    endDate?: Date
  ) {
    const skip = (page - 1) * limit
    
    const where: any = {}
    
    if (userId) where.userId = userId
    if (resource) where.resource = resource
    if (action) where.action = action
    
    if (startDate || endDate) {
      where.createdAt = {}
      if (startDate) where.createdAt.gte = startDate
      if (endDate) where.createdAt.lte = endDate
    }

    const [logs, total] = await Promise.all([
      prisma.operationLog.findMany({
        where,
        skip,
        take: limit,
        include: {
          user: {
            select: {
              id: true,
              username: true,
              fullName: true
            }
          }
        },
        orderBy: { createdAt: 'desc' }
      }),
      prisma.operationLog.count({ where })
    ])

    return {
      logs: logs.map(log => ({
        ...log,
        // 前端期望字段名为 timestamp
        timestamp: (log as any).createdAt?.toISOString?.() || (log as any).createdAt || new Date().toISOString(),
        details: log.details ? (typeof log.details === 'string' ? JSON.parse(log.details) : log.details) : null
      })),
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    }
  }

  /**
   * 获取用户操作统计
   */
  static async getUserActionStats(
    userId?: string,
    startDate?: Date,
    endDate?: Date
  ) {
    const where: any = {}
    
    if (userId) where.userId = userId
    
    if (startDate || endDate) {
      where.createdAt = {}
      if (startDate) where.createdAt.gte = startDate
      if (endDate) where.createdAt.lte = endDate
    }

    const stats = await prisma.operationLog.groupBy({
      by: ['action', 'resource'],
      where,
      _count: {
        id: true
      },
      orderBy: {
        _count: {
          id: 'desc'
        }
      }
    })

    return stats.map(stat => ({
      action: stat.action,
      resource: stat.resource,
      count: stat._count.id
    }))
  }

  /**
   * 获取操作日志详情
   */
  static async getLogDetail(id: string) {
    const log = await prisma.operationLog.findUnique({
      where: { id },
      include: {
        user: {
          select: {
            id: true,
            username: true,
            fullName: true,
            email: true,
            department: true
          }
        }
      }
    })

    if (!log) {
      throw new Error('审计日志不存在')
    }

    return {
      ...log,
      details: log.details ? (typeof log.details === 'string' ? JSON.parse(log.details) : log.details) : null
    }
  }

  /**
   * 获取时间序列统计
   */
  static async getTimeSeriesStats(
    userId?: string,
    startDate?: Date,
    endDate?: Date,
    groupBy: 'hour' | 'day' | 'week' | 'month' = 'day'
  ) {
    // 设置默认时间范围
    if (!startDate) {
      startDate = new Date()
      startDate.setDate(startDate.getDate() - 30) // 默认最近30天
    }
    if (!endDate) {
      endDate = new Date()
    }

    const where: any = {
      createdAt: {
        gte: startDate,
        lte: endDate
      }
    }
    
    if (userId) where.userId = userId

    let dateFormat: string
    switch (groupBy) {
      case 'hour':
        dateFormat = '%Y-%m-%d %H:00:00'
        break
      case 'day':
        dateFormat = '%Y-%m-%d'
        break
      case 'week':
        dateFormat = '%Y-%u'
        break
      case 'month':
        dateFormat = '%Y-%m'
        break
      default:
        dateFormat = '%Y-%m-%d'
    }

    const result = await prisma.$queryRaw`
      SELECT 
        DATE_FORMAT(created_at, ${dateFormat}) as period,
        action,
        resource,
        COUNT(*) as count
      FROM operation_logs 
      WHERE created_at >= ${startDate} 
        AND created_at <= ${endDate}
        ${userId ? prisma.$queryRaw`AND user_id = ${userId}` : prisma.$queryRaw``}
      GROUP BY period, action, resource
      ORDER BY period ASC
    `

    return result
  }

  /**
   * 风险行为分析
   */
  static async getRiskAnalysis(days: number = 7, threshold: number = 10) {
    const startDate = new Date()
    startDate.setDate(startDate.getDate() - days)

    // 高风险操作类型
    const riskActions = ['DELETE', 'STATUS_CHANGE']
    // 高风险资源类型
    // const riskResources = ['USER', 'CONFIGURATION', 'SERVICE']

    // 频繁操作用户
    const frequentUsers = await prisma.operationLog.groupBy({
      by: ['userId'],
      where: {
        createdAt: { gte: startDate },
        action: { in: riskActions }
      },
      _count: {
        id: true
      },
      having: {
        id: {
          _count: {
            gte: threshold
          }
        }
      },
      orderBy: {
        _count: {
          id: 'desc'
        }
      }
    })

    // 异常时间操作（非工作时间）
    const abnormalTimeOps = await prisma.operationLog.findMany({
      where: {
        createdAt: { gte: startDate },
        action: { in: riskActions },
        OR: [
          { createdAt: { lt: new Date(new Date().setHours(8, 0, 0, 0)) } }, // 早于8点
          { createdAt: { gt: new Date(new Date().setHours(18, 0, 0, 0)) } }  // 晚于18点
        ]
      },
      include: {
        user: {
          select: {
            id: true,
            username: true,
            fullName: true
          }
        }
      },
      orderBy: { createdAt: 'desc' },
      take: 50
    })

    // 失败登录尝试（简化查询，只查找登录操作）
    const failedLoginAttempts = await prisma.operationLog.findMany({
      where: {
        createdAt: { gte: startDate },
        action: 'LOGIN'
      },
      include: {
        user: {
          select: {
            id: true,
            username: true,
            fullName: true
          }
        }
      },
      orderBy: { createdAt: 'desc' },
      take: 50
    })

    // 权限提升操作（简化查询，查找用户更新操作）
    const privilegeEscalations = await prisma.operationLog.findMany({
      where: {
        createdAt: { gte: startDate },
        resource: 'USER',
        action: 'UPDATE'
      },
      include: {
        user: {
          select: {
            id: true,
            username: true,
            fullName: true
          }
        }
      },
      orderBy: { createdAt: 'desc' },
      take: 50
    })

    return {
      frequentUsers: await Promise.all(
        frequentUsers.map(async (user) => {
          const userInfo = await prisma.user.findUnique({
            where: { id: user.userId },
            select: {
              id: true,
              username: true,
              fullName: true,
              email: true
            }
          })
          return {
            user: userInfo,
            operationCount: user._count.id
          }
        })
      ),
      abnormalTimeOperations: abnormalTimeOps.map(log => ({
        ...log,
        details: log.details ? (typeof log.details === 'string' ? JSON.parse(log.details) : log.details) : null
      })),
      failedLoginAttempts: failedLoginAttempts.map(log => ({
        ...log,
        details: log.details ? (typeof log.details === 'string' ? JSON.parse(log.details) : log.details) : null
      })),
      privilegeEscalations: privilegeEscalations.map(log => ({
        ...log,
        details: log.details ? (typeof log.details === 'string' ? JSON.parse(log.details) : log.details) : null
      }))
    }
  }

  /**
   * 系统性能影响分析
   */
  static async getSystemImpactAnalysis(startDate?: Date, endDate?: Date) {
    if (!startDate) {
      startDate = new Date()
      startDate.setHours(0, 0, 0, 0)
    }
    if (!endDate) {
      endDate = new Date()
      endDate.setHours(23, 59, 59, 999)
    }

    const where = {
      createdAt: {
        gte: startDate,
        lte: endDate
      }
    }

    // 总操作量统计
    const totalOperations = await prisma.operationLog.count({ where })

    // 按小时统计操作量
    const hourlyStats = await prisma.$queryRaw`
      SELECT 
        HOUR(created_at) as hour,
        COUNT(*) as count
      FROM operation_logs 
      WHERE created_at >= ${startDate} AND created_at <= ${endDate}
      GROUP BY HOUR(created_at)
      ORDER BY hour ASC
    `

    // 高频操作统计
    const highFrequencyOps = await prisma.operationLog.groupBy({
      by: ['action', 'resource'],
      where,
      _count: {
        id: true
      },
      orderBy: {
        _count: {
          id: 'desc'
        }
      },
      take: 10
    })

    // 错误操作统计（暂时设为0，需要更具体的错误标识机制）
    const errorOperations = 0

    return {
      totalOperations,
      errorRate: totalOperations > 0 ? (errorOperations / totalOperations * 100).toFixed(2) : '0.00',
      hourlyDistribution: hourlyStats,
      topOperations: highFrequencyOps.map(op => ({
        action: op.action,
        resource: op.resource,
        count: op._count.id
      })),
      systemHealth: {
        totalLogs: totalOperations,
        errorLogs: errorOperations,
        successRate: totalOperations > 0 ? ((totalOperations - errorOperations) / totalOperations * 100).toFixed(2) : '100.00'
      }
    }
  }

  /**
   * 清理过期日志
   */
  static async cleanupExpiredLogs(retentionDays: number = 90, dryRun: boolean = false) {
    const cutoffDate = new Date()
    cutoffDate.setDate(cutoffDate.getDate() - retentionDays)

    // 查询将要删除的日志数量
    const expiredLogsCount = await prisma.operationLog.count({
      where: {
        createdAt: {
          lt: cutoffDate
        }
      }
    })

    if (dryRun) {
      return {
        dryRun: true,
        cutoffDate,
        willDeleteCount: expiredLogsCount,
        retentionDays
      }
    }

    // 实际删除过期日志
    const deleteResult = await prisma.operationLog.deleteMany({
      where: {
        createdAt: {
          lt: cutoffDate
        }
      }
    })

    return {
      dryRun: false,
      cutoffDate,
      deletedCount: deleteResult.count,
      retentionDays
    }
  }

  /**
   * 导出为CSV格式
   */
  static async exportLogsToCSV(logs: any[]) {
    const headers = ['时间', '用户', '操作', '资源', '资源ID', 'IP地址', '详情']
    const rows = logs.map(log => [
      log.createdAt.toISOString(),
      log.user?.username || log.user?.fullName || log.userId,
      log.action,
      log.resource,
      log.resourceId || '',
      log.ipAddress || '',
      log.details ? JSON.stringify(log.details) : ''
    ])

    const csvContent = [headers, ...rows]
      .map(row => row.map(field => `"${field.toString().replace(/"/g, '""')}`).join(','))
      .join('\n')

    return Buffer.from('\uFEFF' + csvContent, 'utf8') // 添加BOM以支持Excel中的中文
  }

  /**
   * 导出为Excel格式
   */
  static async exportLogsToExcel(logs: any[]) {
    const workbook = new ExcelJS.Workbook()
    const worksheet = workbook.addWorksheet('审计日志')

    // 设置列标题
    worksheet.columns = [
      { header: '时间', key: 'timestamp', width: 20 },
      { header: '用户', key: 'user', width: 15 },
      { header: '操作', key: 'action', width: 12 },
      { header: '资源', key: 'resource', width: 15 },
      { header: '资源ID', key: 'resourceId', width: 25 },
      { header: 'IP地址', key: 'ipAddress', width: 15 },
      { header: '详情', key: 'details', width: 40 }
    ]

    // 添加数据
    logs.forEach(log => {
      worksheet.addRow({
        timestamp: log.createdAt.toISOString(),
        user: log.user?.username || log.user?.fullName || log.userId,
        action: log.action,
        resource: log.resource,
        resourceId: log.resourceId || '',
        ipAddress: log.ipAddress || '',
        details: log.details ? JSON.stringify(log.details, null, 2) : ''
      })
    })

    // 设置标题行样式
    worksheet.getRow(1).font = { bold: true }
    worksheet.getRow(1).fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFE0E0E0' }
    }

    // 自动调整列宽
    worksheet.columns.forEach(column => {
      if (column.key === 'details') {
        column.width = 50
      }
    })

    // 生成Buffer
    return await workbook.xlsx.writeBuffer()
  }
}