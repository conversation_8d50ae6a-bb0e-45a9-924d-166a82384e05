import { prisma } from '@/config/database.config'
import { CacheService } from '@/config/redis.config'
import { AlertEngineService } from './alert-engine.service'
import { emailService } from './email.service'
import { smsService } from './sms.service'
import { RealtimeService } from './realtime.service'
import { Service, ServiceStatus, Priority } from '@prisma/client'

export interface SLAViolationConfig {
  responseTimeThreshold: number // 响应时间阈值（分钟）
  resolutionTimeThreshold: number // 解决时间阈值（小时）
  escalationLevels: EscalationLevel[]
  notificationChannels: string[]
  businessHoursOnly: boolean
}

export interface EscalationLevel {
  level: number
  delayMinutes: number
  recipients: string[] // 用户ID或邮箱
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
}

export interface SLAViolationAlert {
  serviceId: string
  violationType: 'RESPONSE_TIME' | 'RESOLUTION_TIME' | 'ESCALATION'
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
  message: string
  expectedTime: Date
  actualTime: Date | null
  escalationLevel: number
}

/**
 * SLA违规自动预警服务
 * 监控服务工单的SLA合规性，自动触发告警和升级
 */
export class SLAViolationAlertService {
  private static instance: SLAViolationAlertService
  private alertEngine: AlertEngineService
  private emailService: typeof emailService
  private smsService: typeof smsService
  private realtimeService: RealtimeService

  private constructor() {
    this.alertEngine = AlertEngineService.getInstance()
    this.emailService = emailService
    this.smsService = smsService
    this.realtimeService = RealtimeService.getInstance()
    this.initializeService()
  }

  public static getInstance(): SLAViolationAlertService {
    if (!SLAViolationAlertService.instance) {
      SLAViolationAlertService.instance = new SLAViolationAlertService()
    }
    return SLAViolationAlertService.instance
  }

  /**
   * 初始化服务
   */
  private async initializeService() {
    console.log('📋 SLA违规预警服务初始化中...')
    
    // 定时检查SLA违规
    setInterval(() => {
      this.checkSLAViolations()
    }, 60000) // 每分钟检查一次

    // 定时处理升级
    setInterval(() => {
      this.processEscalations()
    }, 300000) // 每5分钟检查一次升级

    console.log('✅ SLA违规预警服务初始化完成')
  }

  /**
   * 检查所有活跃工单的SLA违规情况
   */
  async checkSLAViolations(): Promise<void> {
    try {
      // 获取所有未完成的工单
      const activeServices = await prisma.service.findMany({
        where: {
          status: {
            in: ['PENDING', 'IN_PROGRESS', 'WAITING_CUSTOMER']
          }
        },
        include: {
          archive: {
            include: {
              customer: true
            }
          },
          assignedUser: true
        }
      })

      for (const service of activeServices) {
        await this.checkServiceSLA(service)
      }

    } catch (error) {
      console.error('检查SLA违规失败:', error)
    }
  }

  /**
   * 检查单个工单的SLA违规
   */
  private async checkServiceSLA(service: any): Promise<void> {
    try {
      const slaConfig = await this.getSLAConfigForService(service)
      if (!slaConfig) return

      const now = new Date()
      const violations: SLAViolationAlert[] = []

      // 1. 检查响应时间违规
      if (service.status === 'PENDING') {
        const responseDeadline = new Date(
          service.createdAt.getTime() + slaConfig.responseTimeThreshold * 60 * 1000
        )
        
        if (now > responseDeadline) {
          violations.push({
            serviceId: service.id,
            violationType: 'RESPONSE_TIME',
            severity: this.calculateViolationSeverity(service.priority, 'RESPONSE_TIME'),
            message: `工单 ${service.ticketNumber} 响应时间超期`,
            expectedTime: responseDeadline,
            actualTime: null,
            escalationLevel: 1
          })
        }
      }

      // 2. 检查解决时间违规
      if (service.status !== 'RESOLVED' && service.status !== 'CLOSED') {
        const resolutionDeadline = new Date(
          service.createdAt.getTime() + slaConfig.resolutionTimeThreshold * 60 * 60 * 1000
        )
        
        if (now > resolutionDeadline) {
          violations.push({
            serviceId: service.id,
            violationType: 'RESOLUTION_TIME',
            severity: this.calculateViolationSeverity(service.priority, 'RESOLUTION_TIME'),
            message: `工单 ${service.ticketNumber} 解决时间超期`,
            expectedTime: resolutionDeadline,
            actualTime: null,
            escalationLevel: 1
          })
        }
      }

      // 3. 处理检测到的违规
      for (const violation of violations) {
        await this.handleSLAViolation(service, violation, slaConfig)
      }

    } catch (error) {
      console.error(`检查工单 ${service.id} SLA违规失败:`, error)
    }
  }

  /**
   * 处理SLA违规
   */
  private async handleSLAViolation(
    service: any,
    violation: SLAViolationAlert,
    slaConfig: SLAViolationConfig
  ): Promise<void> {
    try {
      // 检查是否已经发送过相同的违规告警
      const cacheKey = `sla:violation:${service.id}:${violation.violationType}`
      const existingViolation = await CacheService.get(cacheKey)
      
      if (existingViolation) {
        // 如果已存在，检查是否需要升级
        const violationData = JSON.parse(existingViolation as string)
        await this.checkEscalation(service, violationData, slaConfig)
        return
      }

      // 记录违规
      await this.recordSLAViolation(service, violation)

      // 发送告警通知
      await this.sendViolationNotifications(service, violation, slaConfig)

      // 缓存违规信息
      await CacheService.setex(
        cacheKey, 
        24 * 60 * 60, // 24小时
        JSON.stringify({
          ...violation,
          createdAt: new Date(),
          notificationsSent: 1
        })
      )

      // 实时推送告警
      await this.pushViolationAlert(service, violation)

      console.log(`🚨 SLA违规告警: ${violation.message}`)
    } catch (error) {
      console.error('处理SLA违规失败:', error)
    }
  }

  /**
   * 检查违规升级
   */
  private async checkEscalation(
    service: any,
    violationData: any,
    slaConfig: SLAViolationConfig
  ): Promise<void> {
    try {
      const now = new Date()
      const violationAge = now.getTime() - new Date(violationData.createdAt).getTime()
      const currentLevel = violationData.escalationLevel || 1

      // 查找下一个升级级别
      const nextLevel = slaConfig.escalationLevels.find(level => 
        level.level > currentLevel && 
        violationAge >= level.delayMinutes * 60 * 1000
      )

      if (nextLevel) {
        await this.escalateViolation(service, violationData, nextLevel, slaConfig)
      }
    } catch (error) {
      console.error('检查违规升级失败:', error)
    }
  }

  /**
   * 升级违规告警
   */
  private async escalateViolation(
    service: any,
    violationData: any,
    escalationLevel: EscalationLevel,
    slaConfig: SLAViolationConfig
  ): Promise<void> {
    try {
      const escalationAlert: SLAViolationAlert = {
        ...violationData,
        violationType: 'ESCALATION',
        severity: escalationLevel.severity,
        message: `工单 ${service.ticketNumber} SLA违规升级至Level ${escalationLevel.level}`,
        escalationLevel: escalationLevel.level
      }

      // 发送升级通知
      await this.sendEscalationNotifications(service, escalationAlert, escalationLevel)

      // 更新缓存
      const cacheKey = `sla:violation:${service.id}:${violationData.violationType}`
      await CacheService.setex(
        cacheKey,
        24 * 60 * 60,
        JSON.stringify({
          ...violationData,
          escalationLevel: escalationLevel.level,
          lastEscalated: new Date(),
          notificationsSent: violationData.notificationsSent + 1
        })
      )

      // 记录升级日志
      await this.recordEscalation(service, escalationAlert, escalationLevel)

      console.log(`⬆️ SLA违规升级: ${escalationAlert.message}`)
    } catch (error) {
      console.error('升级违规告警失败:', error)
    }
  }

  /**
   * 处理所有待升级的违规
   */
  private async processEscalations(): Promise<void> {
    try {
      // 这里可以实现更复杂的升级逻辑
      // 例如查询数据库中的待升级记录
      console.log('🔄 处理SLA违规升级检查...')
    } catch (error) {
      console.error('处理升级失败:', error)
    }
  }

  /**
   * 发送违规通知
   */
  private async sendViolationNotifications(
    service: any,
    violation: SLAViolationAlert,
    slaConfig: SLAViolationConfig
  ): Promise<void> {
    try {
      const notificationData = {
        ticketNumber: service.ticketNumber,
        customerName: service.archive?.customer?.name || '未知客户',
        projectName: service.archive?.name || '未知项目',
        violationType: violation.violationType,
        severity: violation.severity,
        message: violation.message,
        expectedTime: violation.expectedTime,
        createdAt: service.createdAt,
        assignedTo: service.assignedUser?.fullName || '未分配'
      }

      // 邮件通知
      if (slaConfig.notificationChannels.includes('email')) {
        await this.sendViolationEmail(notificationData)
      }

      // 短信通知（高优先级违规）
      if (slaConfig.notificationChannels.includes('sms') && 
          ['HIGH', 'CRITICAL'].includes(violation.severity)) {
        await this.sendViolationSMS(notificationData)
      }

      // Webhook通知
      if (slaConfig.notificationChannels.includes('webhook')) {
        await this.sendViolationWebhook(notificationData)
      }

    } catch (error) {
      console.error('发送违规通知失败:', error)
    }
  }

  /**
   * 发送升级通知
   */
  private async sendEscalationNotifications(
    service: any,
    violation: SLAViolationAlert,
    escalationLevel: EscalationLevel
  ): Promise<void> {
    try {
      // 获取升级收件人
      const recipients = await this.getEscalationRecipients(escalationLevel.recipients)

      const escalationData = {
        ticketNumber: service.ticketNumber,
        customerName: service.archive?.customer?.name || '未知客户',
        escalationLevel: escalationLevel.level,
        severity: violation.severity,
        message: violation.message,
        originalCreatedAt: service.createdAt
      }

      // 发送升级邮件
      for (const recipient of recipients) {
        if (recipient.email) {
          await this.sendEscalationEmail(recipient.email, escalationData)
        }
      }

      // 发送升级短信（严重级别）
      if (['HIGH', 'CRITICAL'].includes(violation.severity)) {
        for (const recipient of recipients) {
          if (recipient.phone) {
            await this.sendEscalationSMS(recipient.phone, escalationData)
          }
        }
      }

    } catch (error) {
      console.error('发送升级通知失败:', error)
    }
  }

  /**
   * 获取工单对应的SLA配置
   */
  private async getSLAConfigForService(service: any): Promise<SLAViolationConfig | null> {
    try {
      // 从数据库或缓存获取SLA配置
      // 这里使用默认配置，实际应该从数据库读取
      const defaultConfig: SLAViolationConfig = {
        responseTimeThreshold: this.getResponseTimeByPriority(service.priority),
        resolutionTimeThreshold: this.getResolutionTimeByPriority(service.priority),
        escalationLevels: [
          {
            level: 1,
            delayMinutes: 30,
            recipients: ['admin'],
            severity: 'MEDIUM'
          },
          {
            level: 2,
            delayMinutes: 120,
            recipients: ['admin', 'manager'],
            severity: 'HIGH'
          },
          {
            level: 3,
            delayMinutes: 240,
            recipients: ['admin', 'manager', 'director'],
            severity: 'CRITICAL'
          }
        ],
        notificationChannels: ['email', 'realtime'],
        businessHoursOnly: false
      }

      return defaultConfig
    } catch (error) {
      console.error('获取SLA配置失败:', error)
      return null
    }
  }

  /**
   * 根据优先级获取响应时间阈值（分钟）
   */
  private getResponseTimeByPriority(priority: Priority): number {
    const thresholds = {
      'URGENT': 15,    // 15分钟
      'HIGH': 60,      // 1小时
      'MEDIUM': 240,   // 4小时
      'LOW': 480       // 8小时
    }
    return thresholds[priority] || 240
  }

  /**
   * 根据优先级获取解决时间阈值（小时）
   */
  private getResolutionTimeByPriority(priority: Priority): number {
    const thresholds = {
      'URGENT': 4,     // 4小时
      'HIGH': 24,      // 24小时
      'MEDIUM': 72,    // 72小时
      'LOW': 168       // 7天
    }
    return thresholds[priority] || 72
  }

  /**
   * 计算违规严重级别
   */
  private calculateViolationSeverity(
    priority: Priority, 
    violationType: 'RESPONSE_TIME' | 'RESOLUTION_TIME'
  ): 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL' {
    if (priority === 'URGENT') {
      return violationType === 'RESPONSE_TIME' ? 'CRITICAL' : 'HIGH'
    } else if (priority === 'HIGH') {
      return violationType === 'RESPONSE_TIME' ? 'HIGH' : 'MEDIUM'
    } else if (priority === 'MEDIUM') {
      return 'MEDIUM'
    } else {
      return 'LOW'
    }
  }

  /**
   * 记录SLA违规
   */
  private async recordSLAViolation(service: any, violation: SLAViolationAlert): Promise<void> {
    try {
      // 创建告警记录
      await prisma.alert.create({
        data: {
          ruleId: 'SLA_VIOLATION', // 可以是预定义的SLA规则ID
          severity: violation.severity as any,
          status: 'PENDING',
          message: violation.message,
          metricValue: 0,
          triggeredAt: new Date(),
          notificationsSent: JSON.stringify({
            serviceId: violation.serviceId,
            violationType: violation.violationType,
            expectedTime: violation.expectedTime,
            escalationLevel: violation.escalationLevel
          })
        }
      })

      // 记录操作日志
      await prisma.operationLog.create({
        data: {
          action: 'SLA_VIOLATION_DETECTED',
          resource: 'SERVICE',
          resourceId: service.id,
          details: JSON.stringify(violation),
          userId: 'system'
        }
      })
    } catch (error) {
      console.error('记录SLA违规失败:', error)
    }
  }

  /**
   * 记录升级日志
   */
  private async recordEscalation(
    service: any,
    violation: SLAViolationAlert,
    escalationLevel: EscalationLevel
  ): Promise<void> {
    try {
      await prisma.operationLog.create({
        data: {
          action: 'SLA_ESCALATION',
          resource: 'SERVICE',
          resourceId: service.id,
          details: JSON.stringify({
            violation,
            escalationLevel
          }),
          userId: 'system'
        }
      })
    } catch (error) {
      console.error('记录升级日志失败:', error)
    }
  }

  // ========== 通知方法 ==========

  private async sendViolationEmail(data: any): Promise<void> {
    try {
      const adminEmails = await this.getAdminEmails()
      for (const email of adminEmails) {
        await this.emailService.sendSLAViolationEmail(email, data)
      }
    } catch (error) {
      console.error('发送违规邮件失败:', error)
    }
  }

  private async sendViolationSMS(data: any): Promise<void> {
    try {
      const adminPhones = await this.getAdminPhones()
      const message = `【SLA违规】${data.ticketNumber}: ${data.message}`
      
      for (const phone of adminPhones) {
        await this.smsService.sendSms({
          phoneNumber: phone,
          message: message
        })
      }
    } catch (error) {
      console.error('发送违规短信失败:', error)
    }
  }

  private async sendViolationWebhook(data: any): Promise<void> {
    try {
      // 实现Webhook发送逻辑
      console.log('发送违规Webhook:', data)
    } catch (error) {
      console.error('发送违规Webhook失败:', error)
    }
  }

  private async sendEscalationEmail(email: string, data: any): Promise<void> {
    try {
      await this.emailService.sendSLAEscalationEmail(email, data)
    } catch (error) {
      console.error('发送升级邮件失败:', error)
    }
  }

  private async sendEscalationSMS(phone: string, data: any): Promise<void> {
    try {
      const message = `【SLA升级】${data.ticketNumber} 升级至Level ${data.escalationLevel}`
              await this.smsService.sendSms({
          phoneNumber: phone,
          message: message
        })
    } catch (error) {
      console.error('发送升级短信失败:', error)
    }
  }

  private async pushViolationAlert(service: any, violation: SLAViolationAlert): Promise<void> {
    try {
      await this.realtimeService.pushSystemAlert({
        id: `sla-violation-${service.id}`,
        title: 'SLA违规告警',
        message: violation.message,
        severity: violation.severity as any,
        component: 'SLA',
        status: 'PENDING'
      })
    } catch (error) {
      console.error('推送违规告警失败:', error)
    }
  }

  // ========== 辅助方法 ==========

  private async getAdminEmails(): Promise<string[]> {
    try {
      const admins = await prisma.user.findMany({
        where: {
          userRoles: {
            some: {
              role: {
                name: { in: ['admin', 'system_admin', 'manager'] }
              }
            }
          }
        },
        select: { email: true }
      })
      return admins.map(admin => admin.email)
    } catch (error) {
      console.error('获取管理员邮箱失败:', error)
      return []
    }
  }

  private async getAdminPhones(): Promise<string[]> {
    try {
      const admins = await prisma.user.findMany({
        where: {
          userRoles: {
            some: {
              role: {
                name: { in: ['admin', 'system_admin', 'manager'] }
              }
            }
          },
          phone: { not: null }
        },
        select: { phone: true }
      })
      return admins.filter(admin => admin.phone).map(admin => admin.phone!)
    } catch (error) {
      console.error('获取管理员手机号失败:', error)
      return []
    }
  }

  private async getEscalationRecipients(recipients: string[]): Promise<any[]> {
    try {
      const users = await prisma.user.findMany({
        where: {
          OR: [
            { id: { in: recipients } },
            { email: { in: recipients } },
            {
              userRoles: {
                some: {
                  role: {
                    name: { in: recipients }
                  }
                }
              }
            }
          ]
        },
        select: {
          id: true,
          fullName: true,
          email: true,
          phone: true
        }
      })
      return users
    } catch (error) {
      console.error('获取升级收件人失败:', error)
      return []
    }
  }

  /**
   * 手动触发SLA检查
   */
  async triggerSLACheck(serviceId?: string): Promise<void> {
    if (serviceId) {
      const service = await prisma.service.findUnique({
        where: { id: serviceId },
        include: {
          archive: {
            include: {
              customer: true
            }
          },
          assignedUser: true
        }
      })
      
      if (service) {
        await this.checkServiceSLA(service)
      }
    } else {
      await this.checkSLAViolations()
    }
  }

  /**
   * 获取SLA违规统计
   */
  async getSLAViolationStatistics(timeRange: { start: Date; end: Date }) {
    try {
      const { start, end } = timeRange
      
      // 从告警表获取SLA违规统计
      const violations = await prisma.alert.findMany({
        where: {
          ruleId: 'SLA_VIOLATION',
          triggeredAt: {
            gte: start,
            lte: end
          }
        }
      })

      const stats = {
        total: violations.length,
        bySeverity: {} as Record<string, number>,
        byType: {} as Record<string, number>,
        byStatus: {} as Record<string, number>
      }

      violations.forEach(violation => {
        stats.bySeverity[violation.severity] = (stats.bySeverity[violation.severity] || 0) + 1
        stats.byStatus[violation.status] = (stats.byStatus[violation.status] || 0) + 1
        
        try {
          const metadata = JSON.parse(violation.notificationsSent as string || '{}')
          const type = metadata.violationType || 'UNKNOWN'
          stats.byType[type] = (stats.byType[type] || 0) + 1
        } catch (e) {
          // 忽略解析错误
        }
      })

      return stats
    } catch (error) {
      console.error('获取SLA违规统计失败:', error)
      throw error
    }
  }
}