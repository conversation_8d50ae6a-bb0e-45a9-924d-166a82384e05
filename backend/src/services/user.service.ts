import { prisma } from '@/config/database.config'
import { CacheService } from '@/config/redis.config'
import { hashPassword, verifyPassword } from '@/utils/crypto.util'
import { ErrorFactory } from '@/utils/errors.util'

export interface CreateUserInput {
  username: string
  email: string
  password: string
  fullName: string
  roleId: string
  department?: string | null
  phone?: string | null
}

export interface UpdateUserInput {
  username?: string | null
  email?: string | null
  fullName?: string | null
  roleId?: string | null
  department?: string | null
  phone?: string | null
}

export interface ChangePasswordInput {
  currentPassword: string
  newPassword: string
}

export interface UserWithRole {
  id: string
  username: string
  email: string
  fullName: string
  department: string | null
  phone: string | null
  status: string
  lastLoginAt: Date | null
  createdAt: Date
  updatedAt: Date
  role: {
    id: string
    name: string
    description: string
    permissions: string[]
  }
}

export class UserService {
  /**
   * 创建用户
   */
  static async createUser(data: CreateUserInput): Promise<UserWithRole> {
    // 检查用户名唯一性
    const existingUser = await prisma.user.findFirst({
      where: {
        OR: [
          { username: data.username },
          { email: data.email }
        ]
      }
    })

    if (existingUser) {
      if (existingUser.username === data.username) {
        throw ErrorFactory.conflict('用户名已存在')
      }
      if (existingUser.email === data.email) {
        throw ErrorFactory.conflict('邮箱已存在')
      }
    }

    // 检查角色是否存在
    const role = await prisma.role.findUnique({
      where: { id: data.roleId }
    })

    if (!role) {
      throw ErrorFactory.notFound('角色')
    }

    // 加密密码
    const hashedPassword = await hashPassword(data.password)

    // 创建用户
    const user = await prisma.user.create({
      data: {
        username: data.username,
        email: data.email,
        password: hashedPassword,
        fullName: data.fullName,
        phone: data.phone || null,
        userRoles: {
          create: {
            roleId: data.roleId
          }
        }
      },
      include: {
        userRoles: {
          include: {
            role: true
          }
        }
      }
    })

    return this.formatUserWithRole(user)
  }

  /**
   * 获取用户列表（增强版）
   */
  static async getUsers(
    page: number = 1,
    limit: number = 20,
    search?: string,
    roleId?: string,
    isActive?: boolean,
    department?: string,
    createdFrom?: Date,
    createdTo?: Date,
    contactInfo?: string
  ) {
    const skip = (page - 1) * limit
    
    const where: any = {}
    
    // 基本搜索
    if (search) {
      where.OR = [
        { username: { contains: search } },
        { fullName: { contains: search } },
        { email: { contains: search } }
      ]
    }
    
    // 按角色筛选
    if (roleId) {
      where.userRoles = {
        some: {
          roleId: roleId
        }
      }
    }
    
    // 按状态筛选
    if (isActive !== undefined) {
      where.status = isActive ? 'ACTIVE' : 'INACTIVE'
    }
    
    // 按部门筛选
    if (department) {
      where.department = department
    }

    // 按创建时间筛选
    if (createdFrom || createdTo) {
      where.createdAt = {}
      if (createdFrom) {
        where.createdAt.gte = createdFrom
      }
      if (createdTo) {
        where.createdAt.lte = createdTo
      }
    }

    // 按联系方式搜索
    if (contactInfo) {
      where.OR = [
        ...(where.OR || []),
        { phone: { contains: contactInfo } },
        { email: { contains: contactInfo } }
      ]
    }

    const [users, total] = await Promise.all([
      prisma.user.findMany({
        where,
        skip,
        take: limit,
        include: {
          userRoles: {
            include: {
              role: true
            }
          }
        },
        orderBy: { createdAt: 'desc' }
      }),
      prisma.user.count({ where })
    ])

    return {
      users: users.map(user => this.formatUserWithRole(user)),
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    }
  }

  /**
   * 获取用户详情
   */
  static async getUserById(id: string): Promise<UserWithRole> {
    const user = await prisma.user.findUnique({
      where: { id },
      include: {
        userRoles: {
          include: {
            role: true
          }
        }
      }
    })

    if (!user) {
      throw ErrorFactory.notFound('用户')
    }

    return this.formatUserWithRole(user)
  }

  /**
   * 更新用户信息
   */
  static async updateUser(id: string, data: UpdateUserInput): Promise<UserWithRole> {
    // 检查用户是否存在
    const existingUser = await prisma.user.findUnique({
      where: { id }
    })

    if (!existingUser) {
      throw ErrorFactory.notFound('用户')
    }

    // 检查用户名和邮箱唯一性
    if (data.username || data.email) {
      const conflictUser = await prisma.user.findFirst({
        where: {
          AND: [
            { id: { not: id } },
            {
              OR: [
                ...(data.username ? [{ username: data.username }] : []),
                ...(data.email ? [{ email: data.email }] : [])
              ]
            }
          ]
        }
      })

      if (conflictUser) {
        if (conflictUser.username === data.username) {
          throw ErrorFactory.conflict('用户名已存在')
        }
        if (conflictUser.email === data.email) {
          throw ErrorFactory.conflict('邮箱已存在')
        }
      }
    }

    // 检查角色是否存在
    if (data.roleId) {
      const role = await prisma.role.findUnique({
        where: { id: data.roleId }
      })

      if (!role) {
        throw ErrorFactory.notFound('角色')
      }
    }

    // 更新用户
    const updateData: any = { updatedAt: new Date() }
    
    if (data.username !== undefined) updateData.username = data.username
    if (data.email !== undefined) updateData.email = data.email
    if (data.fullName !== undefined) updateData.fullName = data.fullName
    if (data.phone !== undefined) updateData.phone = data.phone

    const user = await prisma.user.update({
      where: { id },
      data: updateData,
      include: {
        userRoles: {
          include: {
            role: true
          }
        }
      }
    })

    // 清除用户缓存
    await CacheService.del(`user:${id}`)

    return this.formatUserWithRole(user)
  }

  /**
   * 删除用户
   */
  static async deleteUser(id: string): Promise<void> {
    // 检查用户是否存在
    const user = await prisma.user.findUnique({
      where: { id }
    })

    if (!user) {
      throw ErrorFactory.notFound('用户')
    }

    // 检查是否有关联数据
    const [serviceCount, workLogCount, commentCount] = await Promise.all([
      prisma.service.count({ where: { assignedTo: id } }),
      prisma.serviceWorkLog.count({ where: { userId: id } }),
      prisma.serviceComment.count({ where: { authorId: id } })
    ])

    if (serviceCount > 0 || workLogCount > 0 || commentCount > 0) {
      throw ErrorFactory.business('用户有关联数据，无法删除。请先转移或删除相关数据。')
    }

    // 删除用户
    await prisma.user.delete({
      where: { id }
    })

    // 清除用户缓存
    await CacheService.del(`user:${id}`)
  }

  /**
   * 修改密码
   */
  static async changePassword(
    userId: string,
    data: ChangePasswordInput
  ): Promise<void> {
    // 获取用户当前密码
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { password: true }
    })

    if (!user) {
      throw ErrorFactory.notFound('用户')
    }

    // 验证当前密码
    const isCurrentPasswordValid = await verifyPassword(
      data.currentPassword,
      user.password
    )

    if (!isCurrentPasswordValid) {
      throw ErrorFactory.business('当前密码不正确')
    }

    // 加密新密码
    const hashedNewPassword = await hashPassword(data.newPassword)

    // 更新密码
    await prisma.user.update({
      where: { id: userId },
      data: {
        password: hashedNewPassword,
        updatedAt: new Date()
      }
    })

    // 清除用户缓存
    await CacheService.del(`user:${userId}`)
  }

  /**
   * 重置用户密码（管理员功能）
   */
  static async resetPassword(
    userId: string,
    newPassword: string
  ): Promise<void> {
    // 检查用户是否存在
    const user = await prisma.user.findUnique({
      where: { id: userId }
    })

    if (!user) {
      throw ErrorFactory.notFound('用户')
    }

    // 加密新密码
    const hashedPassword = await hashPassword(newPassword)

    // 更新密码
    await prisma.user.update({
      where: { id: userId },
      data: {
        password: hashedPassword,
        updatedAt: new Date()
      }
    })

    // 清除用户缓存
    await CacheService.del(`user:${userId}`)
  }

  /**
   * 切换用户状态
   */
  static async toggleUserStatus(id: string): Promise<UserWithRole> {
    const user = await prisma.user.findUnique({
      where: { id }
    })

    if (!user) {
      throw ErrorFactory.notFound('用户')
    }

    const updatedUser = await prisma.user.update({
      where: { id },
      data: {
        status: user.status === 'ACTIVE' ? 'INACTIVE' : 'ACTIVE',
        updatedAt: new Date()
      },
      include: {
        userRoles: {
          include: {
            role: true
          }
        }
      }
    })

    // 清除用户缓存
    await CacheService.del(`user:${id}`)

    return this.formatUserWithRole(updatedUser)
  }

  /**
   * 更新用户最后登录时间
   */
  static async updateLastLogin(userId: string): Promise<void> {
    // 注意：lastLoginAt 字段在数据库模型中不存在，暂时跳过更新
    // await prisma.user.update({
    //   where: { id: userId },
    //   data: {
    //     lastLoginAt: new Date()
    //   }
    // })

    // 清除用户缓存
    await CacheService.del(`user:${userId}`)
  }

  /**
   * 获取用户统计信息（增强版）
   */
  static async getUserStats() {
    const [
      totalUsers,
      activeUsers,
      recentUsers,
      usersByRole,
      usersByDepartment
    ] = await Promise.all([
      prisma.user.count(),
      prisma.user.count({ where: { status: 'ACTIVE' } }),
      prisma.user.count({
        where: {
          createdAt: {
            gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // 最近30天
          }
        }
      }),
      // 按角色统计用户
      prisma.userRole.groupBy({
        by: ['roleId'],
        _count: {
          userId: true
        }
      }).then(async (groups) => {
        const roleIds = groups.map(g => g.roleId)
        const roles = await prisma.role.findMany({
          where: { id: { in: roleIds } },
          select: { id: true, name: true }
        })
        
        return groups.map(group => {
          const role = roles.find(r => r.id === group.roleId)
          return {
            roleId: group.roleId,
            roleName: role?.name || '未知角色',
            userCount: group._count.userId
          }
        })
      }),
      // 按部门统计用户
      prisma.user.groupBy({
        by: ['department'],
        _count: {
          id: true
        },
        where: {
          department: { not: null }
        }
      }).then(groups => 
        groups.map(group => ({
          department: group.department || '无部门',
          userCount: group._count.id
        }))
      )
    ])

    return {
      totalUsers,
      activeUsers,
      inactiveUsers: totalUsers - activeUsers,
      recentUsers,
      usersByRole,
      usersByDepartment
    }
  }

  /**
   * 获取部门列表（增强版）
   */
  static async getDepartments(): Promise<{ department: string, userCount: number }[]> {
    const departments = await prisma.user.groupBy({
      by: ['department'],
      _count: {
        id: true
      },
      where: {
        department: { not: null }
      }
    })

    return departments
      .map(item => ({
        department: item.department || '无部门',
        userCount: item._count.id
      }))
      .filter(item => item.department !== '无部门')
  }

  /**
   * 格式化用户数据
   */
  private static formatUserWithRole(user: any): UserWithRole {
    // 获取第一个角色（假设用户只有一个角色）
    const userRole = user.userRoles?.[0]
    const role = userRole?.role

    return {
      id: user.id,
      username: user.username,
      email: user.email,
      fullName: user.fullName,
      department: user.department,
      phone: user.phone,
      status: user.status,
      lastLoginAt: user.lastLoginAt,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
      role: role ? {
        id: role.id,
        name: role.name,
        description: role.description || '',
        permissions: typeof role.permissions === 'string' 
          ? JSON.parse(role.permissions) 
          : role.permissions
      } : {
        id: '',
        name: '无角色',
        description: '用户未分配角色',
        permissions: []
      }
    }
  }

  /**
   * 批量操作用户
   */
  static async batchOperateUsers(
    userIds: string[],
    operation: 'enable' | 'disable' | 'delete' | 'assignRole',
    roleId?: string
  ): Promise<{
    success: string[]
    failed: { userId: string, error: string }[]
  }> {
    const success: string[] = []
    const failed: { userId: string, error: string }[] = []

    for (const userId of userIds) {
      try {
        switch (operation) {
          case 'enable':
            await prisma.user.update({
              where: { id: userId },
              data: { status: 'ACTIVE' }
            })
            break
          
          case 'disable':
            await prisma.user.update({
              where: { id: userId },
              data: { status: 'INACTIVE' }
            })
            break
          
          case 'delete':
            // 检查是否有关联数据
            const [serviceCount, workLogCount, commentCount] = await Promise.all([
              prisma.service.count({ where: { assignedTo: userId } }),
              prisma.serviceWorkLog.count({ where: { userId: userId } }),
              prisma.serviceComment.count({ where: { authorId: userId } })
            ])

            if (serviceCount > 0 || workLogCount > 0 || commentCount > 0) {
              throw new Error('用户有关联数据，无法删除')
            }

            await prisma.user.delete({
              where: { id: userId }
            })
            break
          
          case 'assignRole':
            if (!roleId) {
              throw new Error('未提供角色ID')
            }
            
            // 先删除原有角色
            await prisma.userRole.deleteMany({
              where: { userId: userId }
            })
            
            // 分配新角色
            await prisma.userRole.create({
              data: {
                userId: userId,
                roleId: roleId
              }
            })
            break
        }
        
        // 清除缓存
        await CacheService.del(`user:${userId}`)
        success.push(userId)
      } catch (error) {
        failed.push({
          userId,
          error: error instanceof Error ? error.message : '未知错误'
        })
      }
    }

    return { success, failed }
  }

  /**
   * 导出用户数据
   */
  static async exportUsers(format: 'excel' | 'csv', filters?: any): Promise<{ buffer: Buffer }> {
    // 构建查询条件
    const where: any = {}
    
    if (filters) {
      if (filters.search) {
        where.OR = [
          { username: { contains: filters.search } },
          { fullName: { contains: filters.search } },
          { email: { contains: filters.search } }
        ]
      }
      
      if (filters.roleId) {
        where.userRoles = {
          some: {
            roleId: filters.roleId
          }
        }
      }
      
      if (filters.isActive !== undefined) {
        where.status = filters.isActive ? 'ACTIVE' : 'INACTIVE'
      }
      
      if (filters.department) {
        where.department = filters.department
      }
    }

    const users = await prisma.user.findMany({
      where,
      include: {
        userRoles: {
          include: {
            role: true
          }
        }
      },
      orderBy: { createdAt: 'desc' }
    })

    const formattedUsers = users.map(user => ({
      '用户ID': user.id,
      '用户名': user.username,
      '邮箱': user.email,
      '姓名': user.fullName,
      '手机': user.phone || '',
      '部门': user.department || '',
      '状态': user.status === 'ACTIVE' ? '正常' : '禁用',
      '角色': user.userRoles[0]?.role?.name || '无角色',
      '创建时间': user.createdAt.toISOString(),
      '最后更新': user.updatedAt.toISOString()
    }))

    if (format === 'csv') {
      // 简单的CSV生成（实际项目中应使用专业的CSV库）
      const headers = Object.keys(formattedUsers[0] || {})
      const csvContent = [headers.join(',')]
        .concat(formattedUsers.map(user => Object.values(user).map(val => `"${val}"`).join(',')))
        .join('\n')
      
      return { buffer: Buffer.from(csvContent, 'utf8') }
    } else {
      // Excel生成（实际项目中应使用exceljs等库）
      // 这里仅作为示例，返回CSV格式
      const headers = Object.keys(formattedUsers[0] || {})
      const csvContent = [headers.join(',')]
        .concat(formattedUsers.map(user => Object.values(user).map(val => `"${val}"`).join(',')))
        .join('\n')
      
      return { buffer: Buffer.from(csvContent, 'utf8') }
    }
  }

  /**
   * 获取活跃用户统计
   */
  static async getActiveUserStats(days: number = 30) {
    const startDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000)
    
    // 查询最近登录的用户（因为lastLoginAt字段不存在，用操作日志代替）
    const recentActiveUsers = await prisma.operationLog.groupBy({
      by: ['userId'],
      where: {
        createdAt: {
          gte: startDate
        }
      }
    })

    const dailyStats = []
    for (let i = 0; i < days; i++) {
      const date = new Date(Date.now() - i * 24 * 60 * 60 * 1000)
      const dayStart = new Date(date.getFullYear(), date.getMonth(), date.getDate())
      const dayEnd = new Date(dayStart.getTime() + 24 * 60 * 60 * 1000)
      
      const activeCount = await prisma.operationLog.groupBy({
        by: ['userId'],
        where: {
          createdAt: {
            gte: dayStart,
            lt: dayEnd
          }
        }
      })
      
      dailyStats.push({
        date: dayStart.toISOString().split('T')[0],
        activeUsers: activeCount.length
      })
    }

    return {
      totalActiveUsers: recentActiveUsers.length,
      dailyStats: dailyStats.reverse()
    }
  }

  /**
   * 批量导入用户
   */
  static async importUsers(users: CreateUserInput[]): Promise<{
    success: UserWithRole[]
    failed: { user: CreateUserInput, error: string }[]
  }> {
    const success: UserWithRole[] = []
    const failed: { user: CreateUserInput, error: string }[] = []

    for (const userData of users) {
      try {
        const user = await this.createUser(userData)
        success.push(user)
      } catch (error) {
        failed.push({
          user: userData,
          error: error instanceof Error ? error.message : '未知错误'
        })
      }
    }

    return { success, failed }
  }
}