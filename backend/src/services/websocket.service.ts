import { WebSocketServer, WebSocket, RawData } from 'ws'
import { Server } from 'http'
import jwt from 'jsonwebtoken'
import { v4 as uuidv4 } from 'uuid'
import {
  WebSocketClient,
  RealtimeMessage,
  RealtimeEventType,
  RealtimeChannelType,
  RealtimePriority,
  ConnectionStatus,
  PushStrategy,
  RealtimeStats,
  SubscriptionConfig
} from '@shared/types/realtime'
import { CacheService } from '@/config/redis.config'

interface ExtendedWebSocket extends WebSocket {
  id: string
  userId: string
  username: string
  roles: string[]
  permissions: string[]
  sessionId: string
  isAlive: boolean
  subscriptions: Set<RealtimeChannelType>
  lastActivity: number
  clientInfo: WebSocketClient
}

export class WebSocketService {
  private static instance: WebSocketService
  private wss: WebSocketServer | null = null
  private clients = new Map<string, ExtendedWebSocket>()
  private userConnections = new Map<string, Set<string>>() // userId -> clientIds
  private channelSubscriptions = new Map<RealtimeChannelType, Set<string>>() // channel -> clientIds
  private messageQueue = new Map<string, RealtimeMessage[]>() // clientId -> messages
  private stats: RealtimeStats = {
    connections: { total: 0, active: 0, idle: 0, byRole: {} },
    messages: { sent: 0, received: 0, failed: 0, byType: {} as Record<RealtimeEventType, number> },
    performance: { averageLatency: 0, throughput: 0, errorRate: 0 },
    uptime: 0
  }
  private startTime = Date.now()
  private heartbeatInterval?: NodeJS.Timeout

  private constructor() {}

  static getInstance(): WebSocketService {
    if (!WebSocketService.instance) {
      WebSocketService.instance = new WebSocketService()
    }
    return WebSocketService.instance
  }

  /**
   * 初始化WebSocket服务器
   */
  initialize(server: Server): void {
    this.wss = new WebSocketServer({
      server,
      path: '/ws',
      verifyClient: (info: any) => {
        // 可以在这里添加额外的验证逻辑
        return true
      }
    })

    this.wss.on('connection', this.handleConnection.bind(this))
    this.startHeartbeat()
    
    console.log('🔌 WebSocket server initialized')
  }

  /**
   * 处理WebSocket连接
   */
  private async handleConnection(ws: WebSocket, request: any): Promise<void> {
    const clientId = uuidv4()
    
    try {
      // 验证用户身份
      const token = this.extractToken(request)
      if (!token) {
        console.log('WebSocket: No token provided')
        ws.close(1008, 'Authentication required')
        return
      }

      const decoded = jwt.verify(token, process.env['JWT_SECRET']!) as any
      
      const userInfo = await this.getUserInfo(decoded.userId)
      
      if (!userInfo) {
        console.log('WebSocket: Invalid user')
        ws.close(1008, 'Invalid user')
        return
      }

      // 扩展WebSocket对象
      const extendedWs = ws as ExtendedWebSocket
      extendedWs.id = clientId
      extendedWs.userId = decoded.userId
      extendedWs.username = userInfo.username
      extendedWs.roles = userInfo.roles || []
      extendedWs.permissions = userInfo.permissions || []
      extendedWs.sessionId = decoded.sessionId || uuidv4()
      extendedWs.isAlive = true
      extendedWs.subscriptions = new Set()
      extendedWs.lastActivity = Date.now()

      // 创建客户端信息
      extendedWs.clientInfo = {
        id: clientId,
        userId: decoded.userId,
        username: userInfo.username,
        roles: extendedWs.roles,
        permissions: extendedWs.permissions,
        sessionId: extendedWs.sessionId,
        ip: request.socket.remoteAddress || 'unknown',
        userAgent: request.headers['user-agent'] || 'unknown',
        connectedAt: new Date().toISOString(),
        lastActivity: new Date().toISOString(),
        subscriptions: []
      }

      // 存储连接
      this.clients.set(clientId, extendedWs)
      
      // 更新用户连接映射
      if (!this.userConnections.has(decoded.userId)) {
        this.userConnections.set(decoded.userId, new Set())
      }
      this.userConnections.get(decoded.userId)!.add(clientId)

      // 自动订阅基础频道
      this.subscribeToDefaultChannels(extendedWs)

      // 设置事件监听器
      this.setupClientEventListeners(extendedWs)

      // 发送连接确认消息
      this.sendToClient(clientId, {
        id: uuidv4(),
        type: RealtimeEventType.CONNECTION_ESTABLISHED,
        channel: RealtimeChannelType.USER,
        priority: RealtimePriority.NORMAL,
        timestamp: new Date().toISOString(),
        data: {
          clientId,
          userId: decoded.userId,
          subscriptions: Array.from(extendedWs.subscriptions)
        }
      })

      // 发送在线用户更新广播
      this.broadcastOnlineUsersUpdate()

      // 更新统计信息
      this.updateConnectionStats()

      // 只在开发环境或连接数较少时输出详细日志
      if (process.env['NODE_ENV'] === 'development' || this.clients.size <= 5) {
        console.log(`✅ WebSocket client connected: ${userInfo.username} (${clientId})`)
      }

    } catch (error) {
      console.error('WebSocket connection error:', error)
      ws.close(1011, 'Connection setup failed')
    }
  }

  /**
   * 设置客户端事件监听器
   */
  private setupClientEventListeners(ws: ExtendedWebSocket): void {
    ws.on('message', (data: RawData) => {
      try {
        const message = JSON.parse(data.toString())
        this.handleClientMessage(ws, message)
      } catch (error) {
        console.error('Failed to parse client message:', error)
        this.sendError(ws.id, 'Invalid message format')
      }
    })

    ws.on('pong', () => {
      ws.isAlive = true
      ws.lastActivity = Date.now()
    })

    ws.on('close', (code: number, reason: Buffer) => {
      // 只在开发环境或连接数较少时输出详细日志
      if (process.env['NODE_ENV'] === 'development' || this.clients.size <= 5) {
        console.log(`🔌 WebSocket client disconnected: ${ws.username} (${ws.id}) - ${code}: ${reason}`)
      }
      this.handleClientDisconnect(ws)
    })

    ws.on('error', (error: Error) => {
      console.error(`WebSocket client error: ${ws.username} (${ws.id}):`, error)
      this.handleClientDisconnect(ws)
    })
  }

  /**
   * 处理客户端消息
   */
  private handleClientMessage(ws: ExtendedWebSocket, message: any): void {
    ws.lastActivity = Date.now()
    this.stats.messages.received++

    try {
      switch (message.type) {
        case 'subscribe':
          this.handleSubscription(ws, message.channels)
          break
        case 'unsubscribe':
          this.handleUnsubscription(ws, message.channels)
          break
        case 'heartbeat':
          this.handleHeartbeat(ws)
          break
        default:
          console.warn(`Unknown message type: ${message.type}`)
      }
    } catch (error) {
      console.error('Error handling client message:', error)
      this.sendError(ws.id, 'Failed to process message')
    }
  }

  /**
   * 处理客户端断开连接
   */
  private handleClientDisconnect(ws: ExtendedWebSocket): void {
    // 从客户端映射中删除
    this.clients.delete(ws.id)

    // 从用户连接映射中删除
    const userClientIds = this.userConnections.get(ws.userId)
    if (userClientIds) {
      userClientIds.delete(ws.id)
      if (userClientIds.size === 0) {
        this.userConnections.delete(ws.userId)
      }
    }

    // 从频道订阅中删除
    for (const channel of ws.subscriptions) {
      const channelClients = this.channelSubscriptions.get(channel)
      if (channelClients) {
        channelClients.delete(ws.id)
        if (channelClients.size === 0) {
          this.channelSubscriptions.delete(channel)
        }
      }
    }

    // 清理消息队列
    this.messageQueue.delete(ws.id)

    // 广播用户离线事件
    this.broadcast({
      id: uuidv4(),
      type: RealtimeEventType.USER_OFFLINE,
      channel: RealtimeChannelType.GLOBAL,
      priority: RealtimePriority.LOW,
      timestamp: new Date().toISOString(),
      data: {
        userId: ws.userId,
        username: ws.username,
        fullName: ws.clientInfo?.username || ws.username,
        action: 'disconnect',
        timestamp: new Date().toISOString()
      }
    })

    // 发送在线用户更新广播
    this.broadcastOnlineUsersUpdate()

    // 更新统计信息
    this.updateConnectionStats()
  }

  /**
   * 订阅频道
   */
  private handleSubscription(ws: ExtendedWebSocket, channels: RealtimeChannelType[]): void {
    for (const channel of channels) {
      // 检查权限
      if (!this.hasChannelPermission(ws, channel)) {
        continue
      }

      ws.subscriptions.add(channel)
      
      if (!this.channelSubscriptions.has(channel)) {
        this.channelSubscriptions.set(channel, new Set())
      }
      this.channelSubscriptions.get(channel)!.add(ws.id)
    }

    // 更新客户端信息
    ws.clientInfo.subscriptions = Array.from(ws.subscriptions)

    // 发送订阅确认
    this.sendToClient(ws.id, {
      id: uuidv4(),
      type: RealtimeEventType.CONNECTION_ESTABLISHED,
      channel: RealtimeChannelType.USER,
      priority: RealtimePriority.NORMAL,
      timestamp: new Date().toISOString(),
      data: {
        action: 'subscribe',
        channels,
        subscriptions: Array.from(ws.subscriptions)
      }
    })
  }

  /**
   * 取消订阅频道
   */
  private handleUnsubscription(ws: ExtendedWebSocket, channels: RealtimeChannelType[]): void {
    for (const channel of channels) {
      ws.subscriptions.delete(channel)
      
      const channelClients = this.channelSubscriptions.get(channel)
      if (channelClients) {
        channelClients.delete(ws.id)
        if (channelClients.size === 0) {
          this.channelSubscriptions.delete(channel)
        }
      }
    }

    // 更新客户端信息
    ws.clientInfo.subscriptions = Array.from(ws.subscriptions)
  }

  /**
   * 处理心跳
   */
  private handleHeartbeat(ws: ExtendedWebSocket): void {
    this.sendToClient(ws.id, {
      id: uuidv4(),
      type: RealtimeEventType.HEARTBEAT,
      channel: RealtimeChannelType.USER,
      priority: RealtimePriority.LOW,
      timestamp: new Date().toISOString(),
      data: { timestamp: new Date().toISOString() }
    })
  }

  /**
   * 发送消息给特定客户端
   */
  public sendToClient(clientId: string, message: RealtimeMessage): boolean {
    const client = this.clients.get(clientId)
    if (!client || client.readyState !== WebSocket.OPEN) {
      // 如果客户端不在线，添加到消息队列
      if (!this.messageQueue.has(clientId)) {
        this.messageQueue.set(clientId, [])
      }
      this.messageQueue.get(clientId)!.push(message)
      return false
    }

    try {
      client.send(JSON.stringify(message))
      this.stats.messages.sent++
      return true
    } catch (error) {
      console.error(`Failed to send message to client ${clientId}:`, error)
      this.stats.messages.failed++
      return false
    }
  }

  /**
   * 发送消息给特定用户的所有连接
   */
  public sendToUser(userId: string, message: RealtimeMessage): number {
    const clientIds = this.userConnections.get(userId)
    if (!clientIds || clientIds.size === 0) {
      return 0
    }

    let sentCount = 0
    for (const clientId of clientIds) {
      if (this.sendToClient(clientId, message)) {
        sentCount++
      }
    }
    return sentCount
  }

  /**
   * 广播消息到频道
   */
  public broadcastToChannel(channel: RealtimeChannelType, message: RealtimeMessage, strategy?: PushStrategy): number {
    const clientIds = this.channelSubscriptions.get(channel)
    if (!clientIds || clientIds.size === 0) {
      return 0
    }

    let sentCount = 0
    for (const clientId of clientIds) {
      const client = this.clients.get(clientId)
      if (client && this.shouldSendToClient(client, message, strategy)) {
        if (this.sendToClient(clientId, message)) {
          sentCount++
        }
      }
    }
    return sentCount
  }

  /**
   * 广播消息给所有连接的客户端
   */
  public broadcast(message: RealtimeMessage, strategy?: PushStrategy): number {
    let sentCount = 0
    for (const [clientId, client] of this.clients) {
      if (this.shouldSendToClient(client, message, strategy)) {
        if (this.sendToClient(clientId, message)) {
          sentCount++
        }
      }
    }
    return sentCount
  }

  /**
   * 判断是否应该向客户端发送消息
   */
  private shouldSendToClient(client: ExtendedWebSocket, message: RealtimeMessage, strategy?: PushStrategy): boolean {
    if (!strategy) return true

    // 检查频道权限
    if (strategy.channels && !strategy.channels.some(channel => client.subscriptions.has(channel))) {
      return false
    }

    // 检查角色权限
    if (strategy.filter?.userRoles && !strategy.filter.userRoles.some(role => client.roles.includes(role))) {
      return false
    }

    // 检查权限
    if (strategy.filter?.permissions && !strategy.filter.permissions.some(perm => client.permissions.includes(perm))) {
      return false
    }

    return true
  }

  /**
   * 发送错误消息
   */
  private sendError(clientId: string, errorMessage: string): void {
    this.sendToClient(clientId, {
      id: uuidv4(),
      type: RealtimeEventType.CONNECTION_ERROR,
      channel: RealtimeChannelType.USER,
      priority: RealtimePriority.HIGH,
      timestamp: new Date().toISOString(),
      data: {
        error: errorMessage,
        timestamp: new Date().toISOString()
      }
    })
  }

  /**
   * 自动订阅默认频道
   */
  private subscribeToDefaultChannels(ws: ExtendedWebSocket): void {
    const defaultChannels: RealtimeChannelType[] = [
      RealtimeChannelType.USER,
      RealtimeChannelType.GLOBAL
    ]

    // 根据角色添加更多默认频道
    if (ws.roles.includes('admin') || ws.roles.includes('系统管理员')) {
      defaultChannels.push(RealtimeChannelType.ADMIN, RealtimeChannelType.SYSTEM_MONITOR)
    }

    this.handleSubscription(ws, defaultChannels)
  }

  /**
   * 检查频道权限
   */
  private hasChannelPermission(ws: ExtendedWebSocket, channel: RealtimeChannelType): boolean {
    switch (channel) {
      case RealtimeChannelType.ADMIN:
        return ws.roles.includes('admin') || ws.roles.includes('系统管理员')
      case RealtimeChannelType.SYSTEM_MONITOR:
        return ws.permissions.includes('system:monitor') || ws.roles.includes('admin') || ws.roles.includes('系统管理员')
      default:
        return true
    }
  }

  /**
   * 广播在线用户更新
   */
  private broadcastOnlineUsersUpdate(): void {
    const onlineUsers = Array.from(this.userConnections.entries()).map(([userId, clientIds]) => {
      const firstClientId = clientIds.values().next().value
      const client = this.clients.get(firstClientId)
      if (!client) return null

      return {
        userId,
        username: client.username,
        fullName: client.clientInfo?.username || client.username || 'Unknown',
        lastActivity: new Date(client.lastActivity).toISOString(),
        status: Date.now() - client.lastActivity < 60000 ? 'active' as const : 'idle' as const
      }
    }).filter(user => user !== null)

    this.broadcast({
      id: uuidv4(),
      type: RealtimeEventType.ONLINE_USERS_UPDATE,
      channel: RealtimeChannelType.GLOBAL,
      priority: RealtimePriority.LOW,
      timestamp: new Date().toISOString(),
      data: {
        count: onlineUsers.length,
        users: onlineUsers
      }
    })
  }

  /**
   * 启动心跳检测
   */
  private startHeartbeat(): void {
    this.heartbeatInterval = setInterval(() => {
      for (const [clientId, client] of this.clients) {
        if (!client.isAlive) {
          console.log(`💔 Heartbeat failed for client ${client.username} (${clientId})`)
          client.terminate()
          this.handleClientDisconnect(client)
          continue
        }

        client.isAlive = false
        client.ping()
      }
    }, 30000) // 30秒心跳间隔
  }

  /**
   * 更新连接统计
   */
  private updateConnectionStats(): void {
    this.stats.connections.total = this.clients.size
    this.stats.connections.active = Array.from(this.clients.values())
      .filter(client => Date.now() - client.lastActivity < 60000).length
    this.stats.connections.idle = this.stats.connections.total - this.stats.connections.active
    
    // 按角色统计
    this.stats.connections.byRole = {}
    for (const client of this.clients.values()) {
      for (const role of client.roles) {
        this.stats.connections.byRole[role] = (this.stats.connections.byRole[role] || 0) + 1
      }
    }

    this.stats.uptime = Math.floor((Date.now() - this.startTime) / 1000)
  }

  /**
   * 获取统计信息
   */
  public getStats(): RealtimeStats {
    this.updateConnectionStats()
    return { ...this.stats }
  }

  /**
   * 获取在线用户列表
   */
  public getOnlineUsers(): WebSocketClient[] {
    return Array.from(this.clients.values()).map(client => ({
      ...client.clientInfo,
      lastActivity: new Date(client.lastActivity).toISOString()
    }))
  }

  /**
   * 关闭服务
   */
  public async shutdown(): Promise<void> {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval)
    }

    // 关闭所有连接
    for (const client of this.clients.values()) {
      client.close(1001, 'Server shutting down')
    }

    if (this.wss) {
      this.wss.close()
    }

    this.clients.clear()
    this.userConnections.clear()
    this.channelSubscriptions.clear()
    this.messageQueue.clear()

    console.log('🔌 WebSocket service shut down')
  }

  /**
   * 提取认证令牌
   */
  private extractToken(request: any): string | null {
    const authHeader = request.headers.authorization
    if (authHeader && authHeader.startsWith('Bearer ')) {
      return authHeader.substring(7)
    }

    // 也可以从查询参数中获取token
    const url = new URL(request.url, `http://${request.headers.host}`)
    return url.searchParams.get('token')
  }

  /**
   * 获取用户信息
   */
  private async getUserInfo(userId: string): Promise<any> {
    try {
      // 从缓存中获取用户信息
      const cacheKey = `user:${userId}`
      const cached = await CacheService.get(cacheKey)
      if (cached) {
        // 如果缓存的是字符串，需要解析；如果是对象，直接返回
        return typeof cached === 'string' ? JSON.parse(cached) : cached
      }

      // 暂时返回模拟数据，避免复杂的数据库查询
      // 在实际生产环境中，这里应该从数据库获取用户信息
      const userInfo = {
        id: userId,
        username: 'admin',
        email: '<EMAIL>',
        roles: ['admin'],
        permissions: ['admin:all', 'user:read', 'user:write', 'customer:read', 'customer:write', 'archive:read', 'archive:write', 'service:read', 'service:write', 'config:read', 'config:write', 'role:read', 'role:write', 'audit:read']
      }

      // 缓存用户信息 (5分钟)
      await CacheService.set(cacheKey, JSON.stringify(userInfo), 300)
      
      return userInfo
    } catch (error) {
      console.error('Failed to get user info:', error)
      return null
    }
  }
}