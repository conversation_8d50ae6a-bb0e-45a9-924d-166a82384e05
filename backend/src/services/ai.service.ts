import { CacheService } from './cache.service'
import { EncryptionService } from './encryption.service'
import { prisma } from '@/config/database.config'

// 统一使用全局 prisma 实例（类型与模型保持一致）

// AI分析请求接口
interface AIAnalysisRequest {
  description: string
  contextData?: {
    customerId?: string | undefined
    customerType?: string | undefined
    industry?: string | undefined
    historyPattern?: string | undefined
  }
  userId: string
}

// AI配置接口
interface AIConfigurationData {
  provider: string
  model: string
  apiKey?: string | undefined
  temperature: number
  maxTokens: number
  timeout: number
  mode: string
  enabledFields: Record<string, boolean>
  autoFillThreshold: number
}
export type { AIConfigurationData }
// AI反馈接口
interface AIFeedbackData {
  requestId: string
  userId: string
  rating: number
  helpful?: boolean | undefined
  adopted?: Record<string, any> | undefined
  comments?: string | undefined
}

// AI分析响应接口
interface AIAnalysisResponse {
  success: boolean
  requestId: string
  timestamp: string
  suggestions: Array<{
    field: string
    suggested: string
    confidence: number
    reasoning: string
  }>
  overallConfidence: number
  processingTime: number
  warning?: string
}

export class AIService {
  private static readonly CACHE_PREFIX = 'ai:'
  private static readonly CACHE_DURATION = 5 * 60 // 5分钟

  /**
   * 分析工单内容
   */
  static async analyzeTicketContent(request: AIAnalysisRequest): Promise<{
    success: boolean
    data?: AIAnalysisResponse
    message: string
  }> {
    const startTime = Date.now()
    const requestId = `ai_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`

    try {
      // 1. 获取AI配置
      const config = await this.getAIConfiguration()
      
      if (config.mode === 'DISABLED') {
        return {
          success: false,
          message: 'AI功能已禁用'
        }
      }

      // 2. 检查缓存
      const cacheKey = this.generateCacheKey(request.description, request.contextData)
      const cached = await this.getCachedAnalysis(cacheKey)
      if (cached) {
        return {
          success: true,
          data: cached,
          message: 'AI分析完成(缓存)'
        }
      }

      // 3. 构建提示词
      const prompt = await this.buildPrompt(request, config)

      // 4. 调用AI服务
      const aiResponse = await this.callAIProvider(config, prompt)

      // 5. 解析响应
      const suggestions = this.parseAIResponse(aiResponse)

      // 6. 构建结果
      const result: AIAnalysisResponse = {
        success: true,
        requestId,
        timestamp: new Date().toISOString(),
        suggestions,
        overallConfidence: this.calculateOverallConfidence(suggestions),
        processingTime: Date.now() - startTime
      }

      // 7. 保存分析记录
      await this.saveAnalysisRequest({
        requestId,
        userId: request.userId,
        description: request.description,
        contextData: request.contextData,
        provider: config.provider,
        model: config.model,
        prompt,
        response: aiResponse,
        suggestions,
        processingTime: result.processingTime,
        success: true
      })

      // 8. 缓存结果
      await this.setCachedAnalysis(cacheKey, result)

      return {
        success: true,
        data: result,
        message: 'AI分析完成'
      }

    } catch (error: any) {
      console.error('AI分析失败:', error)

      // 保存失败记录
      await this.saveAnalysisRequest({
        requestId,
        userId: request.userId,
        description: request.description,
        contextData: request.contextData,
        provider: 'unknown',
        model: 'unknown',
        prompt: '',
        response: null,
        suggestions: null,
        processingTime: Date.now() - startTime,
        success: false,
        error: error.message
      })

      // 返回回退分析
      return this.getFallbackAnalysis(request, requestId, Date.now() - startTime)
    }
  }

  /**
   * 获取AI配置
   */
  static async getAIConfiguration(): Promise<AIConfigurationData> {
    try {
        // 只获取全局配置（第一条记录）
        const config = await prisma.aIConfiguration.findFirst({
            where: { isActive: true },
        })
        // 如果没有配置，返回默认配置
        if (!config) {
            return this.getDefaultConfiguration()
        }

        const result: AIConfigurationData = {
            provider: config.provider,
            model: config.model,
            apiKey: config.apiKey || undefined,
            temperature: config.temperature,
            maxTokens: config.maxTokens,
            timeout: config.timeout,
            mode: config.mode,
            enabledFields: config.enabledFields as Record<string, boolean>,
            autoFillThreshold: config.autoFillThreshold,
        }
        // try {
        //    // todo 解密API密钥
        //     const decryptedApiKey = config.apiKey ? await EncryptionService.decrypt(config.apiKey) : undefined
        // if (decryptedApiKey) {
        //     result.apiKey = decryptedApiKey
        // }
        // } catch (error) {
        //     console.error('解密API密钥失败:', error)
        //     result.apiKey = config.apiKey || undefined
        // }
           
        return result
    } catch (error) {
      console.error('获取AI配置失败:', error)
      return this.getDefaultConfiguration()
    }
  }

  /**
   * 更新AI配置
   */
  static async updateAIConfiguration(
    configData: AIConfigurationData
  ): Promise<AIConfigurationData> {
    // 加密API密钥
    const encryptedApiKey = configData.apiKey
      ? await EncryptionService.encrypt(configData.apiKey)
      : null

    // 获取或创建全局配置
    const existingConfig = await prisma.aIConfiguration.findFirst()

    if (existingConfig) {
      await prisma.aIConfiguration.update({
        where: { id: existingConfig.id },
        data: {
          provider: configData.provider,
          model: configData.model,
          apiKey: encryptedApiKey,
          temperature: configData.temperature,
          maxTokens: configData.maxTokens,
          timeout: configData.timeout,
          mode: configData.mode,
          enabledFields: configData.enabledFields,
          autoFillThreshold: configData.autoFillThreshold,
          updatedAt: new Date()
        }
      })
    } else {
      await prisma.aIConfiguration.create({
        data: {
          provider: configData.provider,
          model: configData.model,
          apiKey: encryptedApiKey,
          temperature: configData.temperature,
          maxTokens: configData.maxTokens,
          timeout: configData.timeout,
          mode: configData.mode,
          enabledFields: configData.enabledFields,
          autoFillThreshold: configData.autoFillThreshold
        }
      })
    }

    // 清除相关缓存
    await this.clearConfigCache()

    return configData
  }

  /**
   * 提交AI反馈
   */
  static async submitFeedback(feedbackData: AIFeedbackData): Promise<void> {
    const feedbackDataToSave: any = {
      requestId: feedbackData.requestId,
      userId: feedbackData.userId,
      rating: feedbackData.rating,
      helpful: feedbackData.helpful ?? null,
      comments: feedbackData.comments ?? null
    }
    
    if (feedbackData.adopted) {
      feedbackDataToSave.adopted = feedbackData.adopted
    }
    
    await prisma.aIFeedback.create({
      data: feedbackDataToSave
    })
  }

  /**
   * 获取AI统计
   */
  static async getAIStatistics(): Promise<any> {
    const [
      totalRequests,
      successfulRequests,
      averageProcessingTime,
      averageRating
    ] = await Promise.all([
      prisma.aIAnalysisRequest.count(),
      prisma.aIAnalysisRequest.count({ where: { success: true } }),
      prisma.aIAnalysisRequest.aggregate({
        where: { success: true },
        _avg: { processingTime: true }
      }),
      prisma.aIFeedback.aggregate({
        _avg: { rating: true }
      })
    ])

    return {
      totalRequests,
      successfulRequests,
      successRate: totalRequests > 0 ? (successfulRequests / totalRequests) * 100 : 0,
      averageProcessingTime: averageProcessingTime._avg.processingTime || 0,
      averageRating: averageRating._avg.rating || 0
    }
  }

  /**
   * 获取提示词模板列表
   */
  static async getPromptTemplates(): Promise<any[]> {
    return await prisma.aIPromptTemplate.findMany({
      where: { isActive: true },
      orderBy: { createdAt: 'desc' }
    })
  }

  /**
   * 创建提示词模板
   */
  static async createPromptTemplate(templateData: any): Promise<any> {
    return await prisma.aIPromptTemplate.create({
      data: templateData
    })
  }

  /**
   * 更新提示词模板
   */
  static async updatePromptTemplate(id: string, templateData: any): Promise<any> {
    return await prisma.aIPromptTemplate.update({
      where: { id },
      data: {
        ...templateData,
        updatedAt: new Date()
      }
    })
  }

  /**
   * 删除提示词模板
   */
  static async deletePromptTemplate(id: string): Promise<void> {
    await prisma.aIPromptTemplate.update({
      where: { id },
      data: { isActive: false }
    })
  }

  /**
   * 测试AI连接
   */
  static async testConnection(config: AIConfigurationData): Promise<{
    success: boolean
    message: string
    latency?: number
  }> {
    const startTime = Date.now()
    
    try {
      const testPrompt = '请回复"连接测试成功"'
      const response = await this.callAIProvider(config, testPrompt)
      
      return {
        success: true,
        message: 'AI连接测试成功',
        latency: Date.now() - startTime
      }
    } catch (error: any) {
      return {
        success: false,
        message: `AI连接测试失败: ${error.message}`
      }
    }
  }

  // 私有方法
  private static getDefaultConfiguration(): AIConfigurationData {
    return {
      provider: 'openai',
      model: 'gpt-4o-mini',
      temperature: 0.3,
      maxTokens: 1000,
      timeout: 30000,
      mode: 'USER_TRIGGERED',
      enabledFields: {
        title: true,
        category: true,
        priority: true,
        slaTemplate: true
      },
      autoFillThreshold: 0.8
    }
  }

  private static generateCacheKey(description: string, contextData?: any): string {
    const content = description + JSON.stringify(contextData || {})
    return `${this.CACHE_PREFIX}analysis:${Buffer.from(content).toString('base64').substring(0, 20)}`
  }

  private static async getCachedAnalysis(key: string): Promise<AIAnalysisResponse | null> {
    try {
      const cached = await CacheService.get(key)
      return cached ? JSON.parse(cached) : null
    } catch {
      return null
    }
  }

  private static async setCachedAnalysis(key: string, result: AIAnalysisResponse): Promise<void> {
    try {
      await CacheService.set(key, JSON.stringify(result), this.CACHE_DURATION)
    } catch (error) {
      console.warn('缓存AI分析结果失败:', error)
    }
  }

  private static async clearConfigCache(): Promise<void> {
    const pattern = `${this.CACHE_PREFIX}config:global*`

    try {
      await CacheService.deletePattern(pattern)
    } catch (error) {
      console.warn('清除AI配置缓存失败:', error)
    }
  }

  private static async buildPrompt(
    request: AIAnalysisRequest,
    config: AIConfigurationData
  ): Promise<string> {
    // 获取提示词模板
    const template = await prisma.aIPromptTemplate.findFirst({
      where: {
        category: 'ticket_analysis',
        isActive: true,
        provider: config.provider
      }
    }) || await prisma.aIPromptTemplate.findFirst({
      where: {
        category: 'ticket_analysis',
        isActive: true,
        provider: null
      }
    })

    const baseTemplate = template?.template || this.getDefaultPromptTemplate()

    // 替换模板变量
    return baseTemplate
      .replace('{description}', request.description)
      .replace('{customerType}', request.contextData?.customerType || '未知')
      .replace('{industry}', request.contextData?.industry || '未知')
      .replace('{historyPattern}', request.contextData?.historyPattern || '无历史模式')
  }

  private static async callAIProvider(
    config: AIConfigurationData,
    prompt: string
  ): Promise<any> {
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), config.timeout)

    try {
      let response: Response
      console.log(config.provider)
      switch (config.provider) {
        case 'openai':
          response = await fetch('https://api.siliconflow.cn/v1/chat/completions', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${config.apiKey}`
            },
            body: JSON.stringify({
              model: config.model,
              messages: [
                {
                  role: 'system',
                  content: '你是一个专业的IT运维工单分析助手，请按要求分析工单内容并返回标准JSON格式结果。'
                },
                {
                  role: 'user',
                  content: prompt
                }
              ],
              temperature: config.temperature,
              max_tokens: config.maxTokens,
              response_format: { type: 'json_object' }
            }),
            signal: controller.signal
          })
          break

        case 'anthropic':
          response = await fetch('https://api.anthropic.com/v1/messages', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'x-api-key': config.apiKey || '',
              'anthropic-version': '2023-06-01'
            },
            body: JSON.stringify({
              model: config.model,
              max_tokens: config.maxTokens,
              temperature: config.temperature,
              messages: [
                {
                  role: 'user',
                  content: `请你作为专业的IT运维工单分析助手，分析以下内容并返回JSON格式结果：\n\n${prompt}`
                }
              ]
            }),
            signal: controller.signal
          })
          break

        case 'gemini':
          const apiKey = config.apiKey
          response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key=${apiKey}`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              contents: [{
                parts: [{
                  text: `作为专业的IT运维工单分析助手，请分析以下内容并返回JSON格式结果：\n\n${prompt}`
                }]
              }],
              generationConfig: {
                temperature: config.temperature,
                maxOutputTokens: config.maxTokens,
                candidateCount: 1
              }
            }),
            signal: controller.signal
          })
          break

        default:
          throw new Error(`不支持的AI提供商: ${config.provider}`)
      }

      clearTimeout(timeoutId)

      if (!response.ok) {
        throw new Error(`AI API错误: ${response.status} ${response.statusText}`)
      }

      return await response.json()

    } catch (error) {
      clearTimeout(timeoutId)
      throw error
    }
  }

  private static parseAIResponse(aiResponse: any): Array<{
    field: string
    suggested: string
    confidence: number
    reasoning: string
  }> {
    try {
      // 根据不同AI提供商解析响应
      let content = ''

      if (aiResponse.choices && aiResponse.choices[0]) {
        // OpenAI格式
        content = aiResponse.choices[0].message.content
      } else if (aiResponse.content && aiResponse.content[0]) {
        // Anthropic格式
        content = aiResponse.content[0].text
      } else if (aiResponse.candidates && aiResponse.candidates[0]) {
        // Gemini格式
        content = aiResponse.candidates[0].content.parts[0].text
      }

      const parsed = JSON.parse(content)
      const suggestions = []

      // 解析各个字段的建议
      if (parsed.title) {
        suggestions.push({
          field: 'title',
          suggested: parsed.title.suggested,
          confidence: parsed.title.confidence || 50,
          reasoning: parsed.title.reasoning || '基于内容分析生成的标题建议'
        })
      }

      if (parsed.category) {
        suggestions.push({
          field: 'category',
          suggested: parsed.category.suggested,
          confidence: parsed.category.confidence || 50,
          reasoning: parsed.category.reasoning || '基于内容分析的类别建议'
        })
      }

      if (parsed.priority) {
        suggestions.push({
          field: 'priority',
          suggested: parsed.priority.suggested,
          confidence: parsed.priority.confidence || 50,
          reasoning: parsed.priority.reasoning || '基于内容分析的优先级建议'
        })
      }

      if (parsed.slaTemplate) {
        suggestions.push({
          field: 'slaTemplate',
          suggested: parsed.slaTemplate.suggested,
          confidence: parsed.slaTemplate.confidence || 50,
          reasoning: parsed.slaTemplate.reasoning || '基于内容分析的SLA模板建议'
        })
      }

      return suggestions

    } catch (error) {
      console.error('解析AI响应失败:', error)
      return []
    }
  }

  private static calculateOverallConfidence(suggestions: any[]): number {
    if (suggestions.length === 0) return 0

    const totalConfidence = suggestions.reduce((sum, s) => sum + s.confidence, 0)
    return Math.round(totalConfidence / suggestions.length)
  }

  private static async saveAnalysisRequest(data: any): Promise<void> {
    try {
      await prisma.aIAnalysisRequest.create({
        data: {
          requestId: data.requestId,
          userId: data.userId,
          description: data.description,
          contextData: data.contextData,
          provider: data.provider,
          model: data.model,
          prompt: data.prompt,
          response: data.response,
          suggestions: data.suggestions,
          processingTime: data.processingTime,
          success: data.success,
          error: data.error
        }
      })
    } catch (error) {
      console.error('保存AI分析请求失败:', error)
    }
  }

  private static getFallbackAnalysis(
    request: AIAnalysisRequest,
    requestId: string,
    processingTime: number
  ): { success: boolean; data: AIAnalysisResponse; message: string } {
    // 基于关键词的简单分析
    const description = request.description.toLowerCase()

    // 生成标题建议
    const titleSuggestion = this.generateFallbackTitle(request.description)

    // 分析类别
    const categorySuggestion = this.analyzeFallbackCategory(description)

    // 分析优先级
    const prioritySuggestion = this.analyzeFallbackPriority(description, request.contextData)

    // 分析SLA模板
    const slaSuggestion = this.analyzeFallbackSLA(description, request.contextData)

    const suggestions = [
      {
        field: 'title',
        suggested: titleSuggestion.value,
        confidence: titleSuggestion.confidence,
        reasoning: titleSuggestion.reasoning
      },
      {
        field: 'category',
        suggested: categorySuggestion.value,
        confidence: categorySuggestion.confidence,
        reasoning: categorySuggestion.reasoning
      },
      {
        field: 'priority',
        suggested: prioritySuggestion.value,
        confidence: prioritySuggestion.confidence,
        reasoning: prioritySuggestion.reasoning
      },
      {
        field: 'slaTemplate',
        suggested: slaSuggestion.value,
        confidence: slaSuggestion.confidence,
        reasoning: slaSuggestion.reasoning
      }
    ]

    const overallConfidence = Math.round(
      suggestions.reduce((sum, s) => sum + s.confidence, 0) / suggestions.length
    )

    const result: AIAnalysisResponse = {
      success: false,
      requestId,
      timestamp: new Date().toISOString(),
      suggestions,
      overallConfidence,
      processingTime,
      warning: 'AI智能分析服务暂时不可用，基于关键词提供基础建议。建议手动确认或稍后重试智能分析。'
    }

    return {
      success: true,
      data: result,
      message: 'AI分析服务不可用，已提供基于关键词的基础建议'
    }
  }

  // 生成回退标题建议
  private static generateFallbackTitle(description: string): { value: string; confidence: number; reasoning: string } {
    // 提取关键信息生成标题
    const cleanDesc = description.trim()
    let title = cleanDesc
    let confidence = 60
    let reasoning = '基于描述内容生成的标题建议'

    // 如果描述太长，截取前50个字符
    if (cleanDesc.length > 50) {
      title = cleanDesc.substring(0, 47) + '...'
      confidence = 50
      reasoning = '描述较长，截取前部分作为标题'
    }

    // 如果描述很短，直接使用
    if (cleanDesc.length < 10) {
      confidence = 30
      reasoning = '描述较短，建议补充更多信息'
    }

    return { value: title, confidence, reasoning }
  }

  // 分析回退类别
  private static analyzeFallbackCategory(description: string): { value: string; confidence: number; reasoning: string } {
    const keywords = {
      MAINTENANCE: ['维护', '保养', '更新', '升级', '维修', 'maintenance', 'update', 'upgrade'],
      SUPPORT: ['支持', '帮助', '问题', '故障', '错误', '异常', 'support', 'help', 'issue', 'error', 'problem'],
      BUGFIX: ['bug', '缺陷', '修复', '故障', '错误', 'fix', 'defect', 'fault'],
      CONSULTING: ['咨询', '建议', '方案', '规划', 'consulting', 'advice', 'plan'],
      MONITORING: ['监控', '告警', '性能', '监测', 'monitoring', 'alert', 'performance'],
      UPGRADE: ['升级', '更新', '迁移', 'upgrade', 'migration', 'update']
    }

    let bestMatch = 'SUPPORT'
    let maxScore = 0
    let reasoning = '默认支持类别'

    for (const [category, words] of Object.entries(keywords)) {
      const score = words.filter(word => description.includes(word)).length
      if (score > maxScore) {
        maxScore = score
        bestMatch = category
        reasoning = `检测到关键词: ${words.filter(word => description.includes(word)).join(', ')}`
      }
    }

    const confidence = maxScore > 0 ? Math.min(70, 40 + maxScore * 10) : 35

    return { value: bestMatch, confidence, reasoning }
  }

  // 分析回退优先级
  private static analyzeFallbackPriority(description: string, contextData?: any): { value: string; confidence: number; reasoning: string } {
    const urgentKeywords = ['紧急', '严重', '宕机', '无法', '停止', '崩溃', 'urgent', 'critical', 'down', 'crash', 'severe']
    const highKeywords = ['重要', '影响', '问题', '故障', '错误', 'important', 'impact', 'issue', 'error', 'problem']
    const lowKeywords = ['咨询', '建议', '优化', '改进', 'consulting', 'advice', 'optimize', 'improve']

    let priority = 'MEDIUM'
    let confidence = 40
    let reasoning = '默认中等优先级'

    const urgentCount = urgentKeywords.filter(word => description.includes(word)).length
    const highCount = highKeywords.filter(word => description.includes(word)).length
    const lowCount = lowKeywords.filter(word => description.includes(word)).length

    if (urgentCount > 0) {
      priority = 'URGENT'
      confidence = 75
      reasoning = `检测到紧急关键词: ${urgentKeywords.filter(word => description.includes(word)).join(', ')}`
    } else if (highCount > 1) {
      priority = 'HIGH'
      confidence = 65
      reasoning = `检测到重要关键词: ${highKeywords.filter(word => description.includes(word)).join(', ')}`
    } else if (lowCount > 0) {
      priority = 'LOW'
      confidence = 60
      reasoning = `检测到低优先级关键词: ${lowKeywords.filter(word => description.includes(word)).join(', ')}`
    }

    // 考虑客户类型
    if (contextData?.customerType === 'VIP' && priority !== 'LOW') {
      if (priority === 'MEDIUM') priority = 'HIGH'
      if (priority === 'HIGH') priority = 'URGENT'
      confidence += 10
      reasoning += ' (VIP客户优先级提升)'
    }

    return { value: priority, confidence, reasoning }
  }

  // 分析回退SLA模板
  private static analyzeFallbackSLA(description: string, contextData?: any): { value: string; confidence: number; reasoning: string } {
    let slaTemplate = 'standard'
    let confidence = 40
    let reasoning = '默认标准SLA模板'

    // 基于客户类型选择SLA
    if (contextData?.customerType === 'VIP') {
      slaTemplate = 'vip'
      confidence = 70
      reasoning = 'VIP客户使用高级SLA模板'
    } else if (contextData?.customerType === 'Enterprise') {
      slaTemplate = 'enterprise'
      confidence = 65
      reasoning = '企业客户使用企业级SLA模板'
    }

    // 基于紧急程度调整
    const urgentKeywords = ['紧急', '严重', '宕机', 'urgent', 'critical', 'down']
    if (urgentKeywords.some(word => description.includes(word))) {
      slaTemplate = 'urgent'
      confidence = 75
      reasoning = '检测到紧急情况，使用紧急SLA模板'
    }

    return { value: slaTemplate, confidence, reasoning }
  }

  private static getDefaultPromptTemplate(): string {
    return `
你是一个专业的IT运维工单智能分析助手。请仔细分析以下工单描述，并基于语义理解和上下文判断，返回准确的分类建议。

## 工单信息
**描述内容：** {description}
**客户类型：** {customerType}
**行业领域：** {industry}
**历史模式：** {historyPattern}

## 分析任务
请分析工单的真实意图和需求，返回标准JSON格式的结果：

\`\`\`json
{
  "title": {
    "suggested": "简洁清晰的工单标题(不超过50字)",
    "confidence": 85
  },
  "category": {
    "suggested": "服务类别代码",
    "confidence": 90,
    "reasoning": "详细的分类理由和判断依据"
  },
  "priority": {
    "suggested": "优先级代码",
    "confidence": 80,
    "reasoning": "优先级判断的具体理由"
  },
  "slaTemplate": {
    "suggested": "推荐的SLA模板",
    "confidence": 75,
    "reasoning": "SLA模板选择理由"
  }
}
\`\`\`

## 分类标准
**服务类别：**
- MAINTENANCE: 系统维护、定期保养
- SUPPORT: 技术支持、问题咨询
- UPGRADE: 系统升级、功能增强
- BUGFIX: 故障修复、问题解决
- CONSULTING: 技术咨询、方案建议
- MONITORING: 监控告警、性能分析

**优先级：**
- LOW: 一般需求，不影响业务
- MEDIUM: 中等重要，有一定影响
- HIGH: 重要紧急，影响业务运行
- URGENT: 极其紧急，严重影响或中断业务

请确保返回的JSON格式正确，置信度为0-100的整数。
    `.trim()
  }
}
