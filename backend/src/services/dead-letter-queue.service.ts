/**
 * Dead Letter Queue (DLQ) 死信队列服务
 * 用于处理失败的操作和错误恢复
 */

import { EventEmitter } from 'events';
import type {
  WorkflowStep,
  ExecutionContext,
  ActionExecutionResult,
  ExecutionError,
  ExecutionLog
} from '@/types/action-executor.types';

/**
 * 死信消息状态
 */
export enum DeadLetterStatus {
  PENDING = 'PENDING',         // 等待处理
  PROCESSING = 'PROCESSING',   // 正在处理
  RETRY_SCHEDULED = 'RETRY_SCHEDULED', // 已安排重试
  PERMANENTLY_FAILED = 'PERMANENTLY_FAILED', // 永久失败
  RESOLVED = 'RESOLVED'        // 已解决
}

/**
 * 死信消息
 */
export interface DeadLetterMessage {
  id: string;
  executionId: string;
  workflowId: string;
  stepIndex: number;
  step: WorkflowStep;
  context: ExecutionContext;
  originalResult: ActionExecutionResult;
  error: ExecutionError;
  status: DeadLetterStatus;
  retryCount: number;
  maxRetries: number;
  createdAt: Date;
  updatedAt: Date;
  lastRetryAt: Date | null;
  nextRetryAt: Date | null;
  resolvedAt: Date | null;
  metadata: Record<string, any>;
  logs: ExecutionLog[];
}

/**
 * 重试策略配置
 */
export interface RetryStrategy {
  name: string;
  maxRetries: number;
  initialDelay: number;
  backoffMultiplier: number;
  maxDelay: number;
  jitter: boolean;
  conditions: Array<{
    errorType?: string;
    errorCode?: string;
    errorMessage?: string;
    customCondition?: (error: ExecutionError) => boolean;
  }>;
}

/**
 * DLQ 统计信息
 */
export interface DLQStats {
  totalMessages: number;
  pendingMessages: number;
  processingMessages: number;
  retryScheduledMessages: number;
  permanentlyFailedMessages: number;
  resolvedMessages: number;
  averageResolutionTime: number;
  retrySuccessRate: number;
}

/**
 * Dead Letter Queue 服务
 */
export class DeadLetterQueueService extends EventEmitter {
  private static instance: DeadLetterQueueService;
  private messages = new Map<string, DeadLetterMessage>();
  private retryTimers = new Map<string, NodeJS.Timeout>();
  private retryStrategies = new Map<string, RetryStrategy>();
  private processingQueue: string[] = [];
  private maxQueueSize = 10000;
  private isProcessing = false;
  private isInitialized = false;

  constructor() {
    super();
    this.setupDefaultRetryStrategies();
  }

  /**
   * 获取单例实例
   */
  static getInstance(): DeadLetterQueueService {
    if (!DeadLetterQueueService.instance) {
      DeadLetterQueueService.instance = new DeadLetterQueueService();
    }
    return DeadLetterQueueService.instance;
  }

  /**
   * 初始化服务
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      console.log('🚀 初始化DeadLetterQueueService...');

      this.startProcessingLoop();
      this.setupCleanupTimer();
      this.isInitialized = true;

      console.log('✅ DeadLetterQueueService初始化完成');
      this.emit('initialized');

    } catch (error) {
      console.error('❌ DeadLetterQueueService初始化失败:', error);
      throw error;
    }
  }

  /**
   * 添加死信消息
   */
  async enqueue(
    step: WorkflowStep,
    context: ExecutionContext,
    result: ActionExecutionResult,
    error: ExecutionError,
    metadata: Record<string, any> = {}
  ): Promise<string> {
    if (this.messages.size >= this.maxQueueSize) {
      throw new Error(`Dead letter queue is full (${this.maxQueueSize} messages)`);
    }

    const messageId = this.generateMessageId();
    const now = new Date();

    // 选择重试策略
    const strategy = this.selectRetryStrategy(error, step);
    
    const message: DeadLetterMessage = {
      id: messageId,
      executionId: context.executionId,
      workflowId: context.workflowId,
      stepIndex: step.index,
      step: { ...step },
      context: this.sanitizeContext(context),
      originalResult: { ...result },
      error: { ...error },
      status: DeadLetterStatus.PENDING,
      retryCount: 0,
      maxRetries: strategy.maxRetries,
      createdAt: now,
      updatedAt: now,
      lastRetryAt: null,
      nextRetryAt: this.calculateNextRetry(0, strategy),
      resolvedAt: null,
      metadata: { ...metadata, retryStrategy: strategy.name },
      logs: [...(result.logs || [])]
    };

    this.messages.set(messageId, message);
    this.processingQueue.push(messageId);

    this.addMessageLog(message, 'INFO', `消息加入死信队列, 使用策略: ${strategy.name}`);

    this.emit('message:enqueued', {
      messageId,
      executionId: context.executionId,
      workflowId: context.workflowId,
      error: error.code
    });

    console.log(`📮 添加死信消息: ${messageId} (策略: ${strategy.name})`);
    return messageId;
  }

  /**
   * 处理死信消息
   */
  async processMessage(messageId: string): Promise<boolean> {
    const message = this.messages.get(messageId);
    if (!message) {
      console.warn(`⚠️ 死信消息不存在: ${messageId}`);
      return false;
    }

    if (message.status !== DeadLetterStatus.PENDING && message.status !== DeadLetterStatus.RETRY_SCHEDULED) {
      console.warn(`⚠️ 消息状态不允许处理: ${messageId} (状态: ${message.status})`);
      return false;
    }

    try {
      message.status = DeadLetterStatus.PROCESSING;
      message.updatedAt = new Date();

      this.addMessageLog(message, 'INFO', `开始处理消息 (第 ${message.retryCount + 1} 次尝试)`);

      this.emit('message:processing', {
        messageId,
        retryCount: message.retryCount,
        maxRetries: message.maxRetries
      });

      // 这里应该重新执行步骤
      // 由于这是模拟实现，我们使用随机成功率
      const success = await this.simulateRetryExecution(message);

      if (success) {
        await this.resolveMessage(messageId, '重试执行成功');
        return true;
      } else {
        await this.handleRetryFailure(messageId);
        return false;
      }

    } catch (error: any) {
      this.addMessageLog(message, 'ERROR', `处理消息异常: ${error.message}`);
      await this.handleRetryFailure(messageId);
      return false;
    }
  }

  /**
   * 解决死信消息
   */
  async resolveMessage(messageId: string, reason: string = '手动解决'): Promise<void> {
    const message = this.messages.get(messageId);
    if (!message) {
      throw new Error(`消息不存在: ${messageId}`);
    }

    const now = new Date();
    message.status = DeadLetterStatus.RESOLVED;
    message.resolvedAt = now;
    message.updatedAt = now;

    this.addMessageLog(message, 'INFO', `消息已解决: ${reason}`);

    // 清理重试定时器
    const timer = this.retryTimers.get(messageId);
    if (timer) {
      clearTimeout(timer);
      this.retryTimers.delete(messageId);
    }

    this.emit('message:resolved', {
      messageId,
      executionId: message.executionId,
      reason,
      resolutionTime: now.getTime() - message.createdAt.getTime()
    });

    console.log(`✅ 死信消息已解决: ${messageId} - ${reason}`);
  }

  /**
   * 标记消息为永久失败
   */
  async markPermanentlyFailed(messageId: string, reason: string = '超出最大重试次数'): Promise<void> {
    const message = this.messages.get(messageId);
    if (!message) {
      throw new Error(`消息不存在: ${messageId}`);
    }

    const now = new Date();
    message.status = DeadLetterStatus.PERMANENTLY_FAILED;
    message.updatedAt = now;

    this.addMessageLog(message, 'ERROR', `消息永久失败: ${reason}`);

    // 清理重试定时器
    const timer = this.retryTimers.get(messageId);
    if (timer) {
      clearTimeout(timer);
      this.retryTimers.delete(messageId);
    }

    this.emit('message:permanently_failed', {
      messageId,
      executionId: message.executionId,
      workflowId: message.workflowId,
      reason,
      totalRetries: message.retryCount
    });

    console.log(`❌ 死信消息永久失败: ${messageId} - ${reason}`);
  }

  /**
   * 获取死信消息
   */
  getMessage(messageId: string): DeadLetterMessage | undefined {
    return this.messages.get(messageId);
  }

  /**
   * 列出死信消息
   */
  listMessages(filter?: {
    status?: DeadLetterStatus;
    workflowId?: string;
    executionId?: string;
    limit?: number;
    offset?: number;
  }): DeadLetterMessage[] {
    let messages = Array.from(this.messages.values());

    if (filter) {
      if (filter.status) {
        messages = messages.filter(m => m.status === filter.status);
      }
      if (filter.workflowId) {
        messages = messages.filter(m => m.workflowId === filter.workflowId);
      }
      if (filter.executionId) {
        messages = messages.filter(m => m.executionId === filter.executionId);
      }
      if (filter.offset) {
        messages = messages.slice(filter.offset);
      }
      if (filter.limit) {
        messages = messages.slice(0, filter.limit);
      }
    }

    return messages.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
  }

  /**
   * 获取统计信息
   */
  getStats(): DLQStats {
    const messages = Array.from(this.messages.values());
    const total = messages.length;

    const statusCounts = messages.reduce((acc, msg) => {
      acc[msg.status] = (acc[msg.status] || 0) + 1;
      return acc;
    }, {} as Record<DeadLetterStatus, number>);

    const resolvedMessages = messages.filter(m => m.status === DeadLetterStatus.RESOLVED);
    const averageResolutionTime = resolvedMessages.length > 0
      ? resolvedMessages.reduce((sum, msg) => {
          return sum + (msg.resolvedAt!.getTime() - msg.createdAt.getTime());
        }, 0) / resolvedMessages.length
      : 0;

    const retriedMessages = messages.filter(m => m.retryCount > 0);
    const successfulRetries = messages.filter(m => m.retryCount > 0 && m.status === DeadLetterStatus.RESOLVED);
    const retrySuccessRate = retriedMessages.length > 0
      ? (successfulRetries.length / retriedMessages.length) * 100
      : 0;

    return {
      totalMessages: total,
      pendingMessages: statusCounts[DeadLetterStatus.PENDING] || 0,
      processingMessages: statusCounts[DeadLetterStatus.PROCESSING] || 0,
      retryScheduledMessages: statusCounts[DeadLetterStatus.RETRY_SCHEDULED] || 0,
      permanentlyFailedMessages: statusCounts[DeadLetterStatus.PERMANENTLY_FAILED] || 0,
      resolvedMessages: statusCounts[DeadLetterStatus.RESOLVED] || 0,
      averageResolutionTime: Math.round(averageResolutionTime),
      retrySuccessRate: Math.round(retrySuccessRate * 100) / 100
    };
  }

  /**
   * 注册重试策略
   */
  registerRetryStrategy(strategy: RetryStrategy): void {
    this.retryStrategies.set(strategy.name, strategy);
    console.log(`📝 注册重试策略: ${strategy.name}`);
  }

  /**
   * 清理已解决和永久失败的消息
   */
  async cleanup(olderThanDays: number = 7): Promise<number> {
    const cutoffTime = new Date(Date.now() - olderThanDays * 24 * 60 * 60 * 1000);
    let cleanedCount = 0;

    for (const [messageId, message] of this.messages) {
      if (
        (message.status === DeadLetterStatus.RESOLVED || 
         message.status === DeadLetterStatus.PERMANENTLY_FAILED) &&
        message.updatedAt < cutoffTime
      ) {
        this.messages.delete(messageId);
        
        // 清理相关的定时器
        const timer = this.retryTimers.get(messageId);
        if (timer) {
          clearTimeout(timer);
          this.retryTimers.delete(messageId);
        }
        
        cleanedCount++;
      }
    }

    console.log(`🧹 清理了 ${cleanedCount} 个过期的死信消息`);
    this.emit('cleanup:completed', { cleanedCount, cutoffTime });
    
    return cleanedCount;
  }

  // ========== 私有方法 ==========

  /**
   * 设置默认重试策略
   */
  private setupDefaultRetryStrategies(): void {
    // 网络错误重试策略
    this.registerRetryStrategy({
      name: 'network-error',
      maxRetries: 5,
      initialDelay: 2000,
      backoffMultiplier: 2,
      maxDelay: 30000,
      jitter: true,
      conditions: [
        { errorType: 'NETWORK_ERROR' },
        { errorType: 'TIMEOUT_ERROR' }
      ]
    });

    // 系统错误重试策略
    this.registerRetryStrategy({
      name: 'system-error',
      maxRetries: 3,
      initialDelay: 5000,
      backoffMultiplier: 2,
      maxDelay: 60000,
      jitter: true,
      conditions: [
        { errorType: 'SYSTEM_ERROR' },
        { errorType: 'RESOURCE_ERROR' }
      ]
    });

    // 数据错误重试策略
    this.registerRetryStrategy({
      name: 'data-error',
      maxRetries: 2,
      initialDelay: 1000,
      backoffMultiplier: 1.5,
      maxDelay: 10000,
      jitter: false,
      conditions: [
        { errorType: 'DATA_ERROR' },
        { errorType: 'VALIDATION_ERROR' }
      ]
    });

    // 默认重试策略
    this.registerRetryStrategy({
      name: 'default',
      maxRetries: 3,
      initialDelay: 1000,
      backoffMultiplier: 2,
      maxDelay: 30000,
      jitter: true,
      conditions: []
    });
  }

  /**
   * 选择重试策略
   */
  private selectRetryStrategy(error: ExecutionError, step: WorkflowStep): RetryStrategy {
    for (const [name, strategy] of this.retryStrategies) {
      if (name === 'default') continue;
      
      for (const condition of strategy.conditions) {
        if (condition.errorType && condition.errorType === error.type) {
          return strategy;
        }
        if (condition.errorCode && condition.errorCode === error.code) {
          return strategy;
        }
        if (condition.errorMessage && error.message.includes(condition.errorMessage)) {
          return strategy;
        }
        if (condition.customCondition && condition.customCondition(error)) {
          return strategy;
        }
      }
    }

    return this.retryStrategies.get('default')!;
  }

  /**
   * 计算下次重试时间
   */
  private calculateNextRetry(retryCount: number, strategy: RetryStrategy): Date {
    let delay = strategy.initialDelay * Math.pow(strategy.backoffMultiplier, retryCount);
    delay = Math.min(delay, strategy.maxDelay);

    if (strategy.jitter) {
      delay = delay * (0.5 + Math.random() * 0.5); // 50%-100% 的随机延迟
    }

    return new Date(Date.now() + delay);
  }

  /**
   * 处理重试失败
   */
  private async handleRetryFailure(messageId: string): Promise<void> {
    const message = this.messages.get(messageId);
    if (!message) return;

    message.retryCount++;
    message.lastRetryAt = new Date();
    message.updatedAt = new Date();

    if (message.retryCount >= message.maxRetries) {
      await this.markPermanentlyFailed(messageId, '超出最大重试次数');
      return;
    }

    // 安排下次重试
    const strategy = this.retryStrategies.get(message.metadata.retryStrategy) || this.retryStrategies.get('default')!;
    message.nextRetryAt = this.calculateNextRetry(message.retryCount, strategy);
    message.status = DeadLetterStatus.RETRY_SCHEDULED;

    this.addMessageLog(message, 'WARN', `重试失败，安排下次重试: ${message.nextRetryAt.toISOString()}`);

    // 设置重试定时器
    this.scheduleRetry(messageId, message.nextRetryAt);

    this.emit('message:retry_scheduled', {
      messageId,
      retryCount: message.retryCount,
      nextRetryAt: message.nextRetryAt
    });
  }

  /**
   * 安排重试
   */
  private scheduleRetry(messageId: string, retryAt: Date): void {
    const delay = retryAt.getTime() - Date.now();
    
    if (delay <= 0) {
      // 立即重试
      this.processingQueue.push(messageId);
      return;
    }

    const timer = setTimeout(() => {
      this.retryTimers.delete(messageId);
      this.processingQueue.push(messageId);
    }, delay);

    this.retryTimers.set(messageId, timer);
  }

  /**
   * 模拟重试执行
   */
  private async simulateRetryExecution(message: DeadLetterMessage): Promise<boolean> {
    // 模拟执行时间
    await new Promise(resolve => setTimeout(resolve, 100 + Math.random() * 900));

    // 模拟成功率（重试次数越多成功率越高）
    const baseSuccessRate = 0.3;
    const retryBonus = message.retryCount * 0.1;
    const successRate = Math.min(baseSuccessRate + retryBonus, 0.8);

    return Math.random() < successRate;
  }

  /**
   * 清理上下文（移除不可序列化的对象）
   */
  private sanitizeContext(context: ExecutionContext): ExecutionContext {
    return {
      ...context,
      abortController: undefined as any, // 不保存 AbortController
      resources: {
        httpClients: new Map(),
        databaseConnections: new Map(),
        cacheClients: new Map(),
        messageQueues: new Map()
      }
    };
  }

  /**
   * 添加消息日志
   */
  private addMessageLog(
    message: DeadLetterMessage,
    level: ExecutionLog['level'],
    messageText: string,
    metadata?: any
  ): void {
    const log: ExecutionLog = {
      level,
      message: messageText,
      timestamp: new Date(),
      metadata,
      source: 'DeadLetterQueue'
    };
    
    message.logs.push(log);
  }

  /**
   * 生成消息ID
   */
  private generateMessageId(): string {
    return `dlq_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 启动处理循环
   */
  private startProcessingLoop(): void {
    if (this.isProcessing) return;

    this.isProcessing = true;
    
    const processNext = async () => {
      try {
        if (this.processingQueue.length > 0) {
          const messageId = this.processingQueue.shift()!;
          await this.processMessage(messageId);
        }
      } catch (error) {
        console.error('处理死信消息异常:', error);
      }

      // 继续处理下一个消息
      setTimeout(processNext, 100);
    };

    processNext();
  }

  /**
   * 设置清理定时器
   */
  private setupCleanupTimer(): void {
    // 每天清理一次
    setInterval(async () => {
      try {
        await this.cleanup();
      } catch (error) {
        console.error('定时清理异常:', error);
      }
    }, 24 * 60 * 60 * 1000);
  }

  /**
   * 全局清理
   */
  async globalCleanup(): Promise<void> {
    console.log('🧹 开始清理DeadLetterQueueService...');

    this.isProcessing = false;

    // 清理所有定时器
    for (const timer of this.retryTimers.values()) {
      clearTimeout(timer);
    }
    this.retryTimers.clear();

    this.messages.clear();
    this.processingQueue.length = 0;
    this.isInitialized = false;

    console.log('✅ DeadLetterQueueService清理完成');
    this.emit('cleanup:completed');
  }
}

// 导出单例实例
export const deadLetterQueue = DeadLetterQueueService.getInstance();