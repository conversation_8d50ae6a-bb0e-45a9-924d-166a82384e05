import { EventEmitter } from 'events'
import crypto from 'crypto'
import { prisma } from '@/config/database.config'
import { CacheService } from '@/config/redis.config'

/**
 * Webhook触发器服务
 * 处理外部系统的Webhook调用并触发相应的工作流
 */
export class WebhookTriggerService extends EventEmitter {
  private static instance: WebhookTriggerService
  private webhookEndpoints: Map<string, WebhookTrigger> = new Map()
  private requestHistory: Map<string, WebhookRequest[]> = new Map()
  private readonly MAX_HISTORY_SIZE = 100

  // 安全配置
  private readonly WEBHOOK_SECRET_HEADER = 'X-Webhook-Secret'
  private readonly SIGNATURE_HEADER = 'X-Hub-Signature-256'
  private readonly TIMESTAMP_HEADER = 'X-Webhook-Timestamp'
  private readonly MAX_REQUEST_AGE = 300 // 5分钟

  // 速率限制
  private rateLimits: Map<string, RateLimitInfo> = new Map()
  private readonly DEFAULT_RATE_LIMIT = 100 // 每小时100次请求

  private constructor() {
    super()
    this.setMaxListeners(50)
    this.initializeService()
  }

  public static getInstance(): WebhookTriggerService {
    if (!WebhookTriggerService.instance) {
      WebhookTriggerService.instance = new WebhookTriggerService()
    }
    return WebhookTriggerService.instance
  }

  /**
   * 初始化Webhook触发器服务
   */
  private async initializeService() {
    console.log('🔗 Webhook触发器服务初始化中...')
    
    try {
      // 加载现有的Webhook触发器
      await this.loadExistingWebhooks()
      
      // 启动清理任务
      this.startMaintenanceTasks()
      
      // 注册默认的Webhook端点
      await this.registerDefaultWebhooks()
      
      console.log('✅ Webhook触发器服务初始化完成')
    } catch (error) {
      console.error('❌ Webhook触发器服务初始化失败:', error)
      throw error
    }
  }

  /**
   * 加载现有的Webhook触发器
   */
  private async loadExistingWebhooks() {
    try {
      const workflows = await prisma.workflowDefinition.findMany({
        where: {
          isActive: true,
          triggerConfig: {
            path: ['type'],
            equals: 'WEBHOOK'
          }
        }
      })

      for (const workflow of workflows) {
        const triggerConfig = workflow.triggerConfig as any
        if (triggerConfig && triggerConfig.config) {
          await this.registerWebhook(`workflow-${workflow.id}`, {
            endpoint: triggerConfig.config.endpoint,
            method: triggerConfig.config.method || 'POST',
            authentication: triggerConfig.config.authentication,
            filters: triggerConfig.config.filters,
            enabled: true,
            workflowId: workflow.id,
            workflowName: workflow.name,
            description: `Webhook触发器: ${workflow.name}`,
            rateLimit: triggerConfig.config.rateLimit || this.DEFAULT_RATE_LIMIT
          })
        }
      }

      console.log(`📝 加载了 ${workflows.length} 个Webhook触发器`)
    } catch (error) {
      console.error('加载Webhook触发器失败:', error)
    }
  }

  /**
   * 启动维护任务
   */
  private startMaintenanceTasks() {
    // 每小时清理请求历史
    setInterval(() => {
      this.cleanupRequestHistory()
    }, 3600000)

    // 每小时重置速率限制
    setInterval(() => {
      this.resetRateLimits()
    }, 3600000)

    console.log('🔧 Webhook维护任务已启动')
  }

  /**
   * 注册默认的Webhook端点
   */
  private async registerDefaultWebhooks() {
    const defaultWebhooks = [
      {
        webhookId: 'github-integration',
        config: {
          endpoint: '/webhooks/github',
          method: 'POST',
          authentication: {
            type: 'signature',
            secret: process.env.GITHUB_WEBHOOK_SECRET || 'default-secret'
          },
          enabled: true,
          description: 'GitHub集成Webhook',
          rateLimit: 200
        }
      },
      {
        webhookId: 'gitlab-integration',
        config: {
          endpoint: '/webhooks/gitlab',
          method: 'POST',
          authentication: {
            type: 'token',
            token: process.env.GITLAB_WEBHOOK_TOKEN || 'default-token'
          },
          enabled: true,
          description: 'GitLab集成Webhook',
          rateLimit: 200
        }
      },
      {
        webhookId: 'monitoring-alerts',
        config: {
          endpoint: '/webhooks/monitoring',
          method: 'POST',
          authentication: {
            type: 'basic',
            username: process.env.MONITORING_WEBHOOK_USER || 'monitor',
            password: process.env.MONITORING_WEBHOOK_PASS || 'default-pass'
          },
          enabled: true,
          description: '监控系统告警Webhook',
          rateLimit: 500
        }
      }
    ]

    for (const webhook of defaultWebhooks) {
      await this.registerWebhook(webhook.webhookId, webhook.config)
    }

    console.log(`⚙️ 注册了 ${defaultWebhooks.length} 个默认Webhook`)
  }

  /**
   * 注册Webhook触发器
   */
  public async registerWebhook(webhookId: string, config: WebhookTriggerConfig): Promise<void> {
    try {
      // 验证配置
      this.validateWebhookConfig(config)

      // 检查端点是否已存在
      for (const [id, webhook] of this.webhookEndpoints.entries()) {
        if (webhook.config.endpoint === config.endpoint && id !== webhookId) {
          throw new Error(`Webhook端点已存在: ${config.endpoint}`)
        }
      }

      const webhook: WebhookTrigger = {
        id: webhookId,
        config,
        isActive: config.enabled !== false,
        createdAt: new Date(),
        lastRequest: null,
        requestCount: 0,
        successCount: 0,
        errorCount: 0
      }

      this.webhookEndpoints.set(webhookId, webhook)
      this.requestHistory.set(webhookId, [])

      console.log(`🔗 Webhook触发器已注册: ${webhookId} - ${config.method} ${config.endpoint}`)
    } catch (error) {
      console.error(`注册Webhook触发器失败 ${webhookId}:`, error)
      throw error
    }
  }

  /**
   * 处理Webhook请求
   */
  public async handleWebhookRequest(
    endpoint: string,
    method: string,
    headers: Record<string, string>,
    body: any,
    clientIP: string = 'unknown'
  ): Promise<WebhookResponse> {
    const requestId = `webhook-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
    const timestamp = new Date()

    console.log(`🔗 收到Webhook请求: ${method} ${endpoint}`)

    try {
      // 查找匹配的Webhook触发器
      const webhook = this.findMatchingWebhook(endpoint, method)
      if (!webhook) {
        return this.createErrorResponse(404, 'Webhook endpoint not found', requestId)
      }

      // 检查触发器是否启用
      if (!webhook.isActive) {
        return this.createErrorResponse(403, 'Webhook endpoint is disabled', requestId)
      }

      // 检查速率限制
      const rateLimitResult = this.checkRateLimit(webhook.id, clientIP)
      if (!rateLimitResult.allowed) {
        return this.createErrorResponse(429, `Rate limit exceeded: ${rateLimitResult.message}`, requestId)
      }

      // 验证身份认证
      const authResult = this.authenticateRequest(webhook, headers, body)
      if (!authResult.valid) {
        return this.createErrorResponse(401, `Authentication failed: ${authResult.message}`, requestId)
      }

      // 应用过滤器
      const filterResult = this.applyFilters(webhook, headers, body)
      if (!filterResult.passed) {
        return this.createSuccessResponse('Request filtered out', requestId, { 
          filtered: true,
          reason: filterResult.reason 
        })
      }

      // 创建请求记录
      const webhookRequest: WebhookRequest = {
        id: requestId,
        webhookId: webhook.id,
        timestamp,
        method,
        endpoint,
        headers,
        body,
        clientIP,
        status: 'PROCESSING'
      }

      // 添加到历史记录
      this.addToRequestHistory(webhook.id, webhookRequest)

      // 执行触发器
      const executionResult = await this.executeWebhookTrigger(webhook, webhookRequest)

      // 更新请求状态
      webhookRequest.status = executionResult.success ? 'SUCCESS' : 'ERROR'
      webhookRequest.response = executionResult
      webhookRequest.processingTime = Date.now() - timestamp.getTime()

      // 更新统计信息
      this.updateWebhookStats(webhook, executionResult.success)

      if (executionResult.success) {
        return this.createSuccessResponse('Webhook processed successfully', requestId, {
          executionId: executionResult.executionId
        })
      } else {
        return this.createErrorResponse(500, `Processing failed: ${executionResult.error}`, requestId)
      }

    } catch (error: any) {
      console.error(`Webhook请求处理失败 ${requestId}:`, error)
      return this.createErrorResponse(500, `Internal server error: ${error.message}`, requestId)
    }
  }

  /**
   * 查找匹配的Webhook
   */
  private findMatchingWebhook(endpoint: string, method: string): WebhookTrigger | undefined {
    for (const webhook of this.webhookEndpoints.values()) {
      if (webhook.config.endpoint === endpoint && webhook.config.method.toUpperCase() === method.toUpperCase()) {
        return webhook
      }
    }
    return undefined
  }

  /**
   * 检查速率限制
   */
  private checkRateLimit(webhookId: string, clientIP: string): RateLimitResult {
    const webhook = this.webhookEndpoints.get(webhookId)
    if (!webhook) {
      return { allowed: false, message: 'Webhook not found' }
    }

    const rateLimit = webhook.config.rateLimit || this.DEFAULT_RATE_LIMIT
    const rateLimitKey = `${webhookId}:${clientIP}`
    
    let rateLimitInfo = this.rateLimits.get(rateLimitKey)
    const now = Date.now()
    const hourMs = 3600000

    if (!rateLimitInfo) {
      rateLimitInfo = {
        count: 1,
        windowStart: now,
        lastRequest: now
      }
      this.rateLimits.set(rateLimitKey, rateLimitInfo)
      return { allowed: true, remaining: rateLimit - 1 }
    }

    // 检查是否需要重置窗口
    if (now - rateLimitInfo.windowStart >= hourMs) {
      rateLimitInfo.count = 1
      rateLimitInfo.windowStart = now
      rateLimitInfo.lastRequest = now
      return { allowed: true, remaining: rateLimit - 1 }
    }

    // 检查是否超过限制
    if (rateLimitInfo.count >= rateLimit) {
      return { 
        allowed: false, 
        message: `Rate limit of ${rateLimit} requests per hour exceeded`,
        resetTime: new Date(rateLimitInfo.windowStart + hourMs)
      }
    }

    rateLimitInfo.count++
    rateLimitInfo.lastRequest = now
    
    return { allowed: true, remaining: rateLimit - rateLimitInfo.count }
  }

  /**
   * 验证请求身份
   */
  private authenticateRequest(webhook: WebhookTrigger, headers: Record<string, string>, body: any): AuthResult {
    const auth = webhook.config.authentication
    if (!auth) {
      return { valid: true } // 无需验证
    }

    try {
      switch (auth.type) {
        case 'token':
          return this.validateTokenAuth(auth, headers)
        case 'signature':
          return this.validateSignatureAuth(auth, headers, body)
        case 'basic':
          return this.validateBasicAuth(auth, headers)
        default:
          return { valid: false, message: `Unsupported authentication type: ${auth.type}` }
      }
    } catch (error: any) {
      return { valid: false, message: `Authentication error: ${error.message}` }
    }
  }

  /**
   * Token认证
   */
  private validateTokenAuth(auth: any, headers: Record<string, string>): AuthResult {
    const providedToken = headers[this.WEBHOOK_SECRET_HEADER.toLowerCase()] || 
                         headers['authorization']?.replace('Bearer ', '')

    if (!providedToken) {
      return { valid: false, message: 'Token not provided' }
    }

    if (providedToken !== auth.token && providedToken !== auth.secret) {
      return { valid: false, message: 'Invalid token' }
    }

    return { valid: true }
  }

  /**
   * 签名认证（GitHub风格）
   */
  private validateSignatureAuth(auth: any, headers: Record<string, string>, body: any): AuthResult {
    const providedSignature = headers[this.SIGNATURE_HEADER.toLowerCase()]
    const timestamp = headers[this.TIMESTAMP_HEADER.toLowerCase()]

    if (!providedSignature) {
      return { valid: false, message: 'Signature not provided' }
    }

    // 检查请求时效性
    if (timestamp) {
      const requestTime = parseInt(timestamp) * 1000
      const now = Date.now()
      if (Math.abs(now - requestTime) > this.MAX_REQUEST_AGE * 1000) {
        return { valid: false, message: 'Request too old' }
      }
    }

    // 计算期望的签名
    const secret = auth.secret || auth.key
    const payload = typeof body === 'string' ? body : JSON.stringify(body)
    const expectedSignature = 'sha256=' + crypto
      .createHmac('sha256', secret)
      .update(payload)
      .digest('hex')

    if (providedSignature !== expectedSignature) {
      return { valid: false, message: 'Invalid signature' }
    }

    return { valid: true }
  }

  /**
   * Basic认证
   */
  private validateBasicAuth(auth: any, headers: Record<string, string>): AuthResult {
    const authHeader = headers['authorization']
    
    if (!authHeader || !authHeader.startsWith('Basic ')) {
      return { valid: false, message: 'Basic auth header not provided' }
    }

    const credentials = Buffer.from(authHeader.replace('Basic ', ''), 'base64').toString()
    const [username, password] = credentials.split(':')

    if (username !== auth.username || password !== auth.password) {
      return { valid: false, message: 'Invalid credentials' }
    }

    return { valid: true }
  }

  /**
   * 应用过滤器
   */
  private applyFilters(webhook: WebhookTrigger, headers: Record<string, string>, body: any): FilterResult {
    const filters = webhook.config.filters
    if (!filters) {
      return { passed: true }
    }

    try {
      // 检查头部过滤器
      if (filters.headers) {
        for (const [key, expectedValue] of Object.entries(filters.headers)) {
          const actualValue = headers[key.toLowerCase()]
          if (actualValue !== expectedValue) {
            return { 
              passed: false, 
              reason: `Header mismatch: ${key} expected ${expectedValue}, got ${actualValue}` 
            }
          }
        }
      }

      // 检查内容过滤器
      if (filters.body) {
        for (const [path, expectedValue] of Object.entries(filters.body)) {
          const actualValue = this.getValueByPath(body, path)
          if (actualValue !== expectedValue) {
            return { 
              passed: false, 
              reason: `Body mismatch: ${path} expected ${expectedValue}, got ${actualValue}` 
            }
          }
        }
      }

      // 检查事件类型过滤器（常用于GitHub, GitLab等）
      if (filters.eventTypes) {
        const eventType = headers['x-github-event'] || 
                         headers['x-gitlab-event'] || 
                         body?.type || 
                         body?.event_type

        if (eventType && !filters.eventTypes.includes(eventType)) {
          return { 
            passed: false, 
            reason: `Event type ${eventType} not in allowed types: ${filters.eventTypes.join(', ')}` 
          }
        }
      }

      return { passed: true }
    } catch (error: any) {
      return { passed: false, reason: `Filter error: ${error.message}` }
    }
  }

  /**
   * 执行Webhook触发器
   */
  private async executeWebhookTrigger(webhook: WebhookTrigger, request: WebhookRequest): Promise<WebhookExecutionResult> {
    try {
      console.log(`🚀 执行Webhook触发器: ${webhook.id}`)

      let executionId: string | undefined

      if (webhook.config.workflowId) {
        // 触发工作流
        const { workflowEngine } = await import('@/services/workflow-engine.service')
        executionId = await workflowEngine.triggerWorkflow(
          webhook.config.workflowId,
          'WEBHOOK',
          {
            webhookId: webhook.id,
            request: {
              id: request.id,
              method: request.method,
              endpoint: request.endpoint,
              headers: request.headers,
              body: request.body,
              clientIP: request.clientIP,
              timestamp: request.timestamp
            }
          }
        )
      }

      // 发送成功事件
      this.emit('webhook:triggered', {
        webhookId: webhook.id,
        webhook,
        request,
        executionId
      })

      return {
        success: true,
        executionId,
        message: 'Webhook processed successfully'
      }

    } catch (error: any) {
      console.error(`Webhook触发器执行失败 ${webhook.id}:`, error)

      // 发送失败事件
      this.emit('webhook:error', {
        webhookId: webhook.id,
        webhook,
        request,
        error
      })

      return {
        success: false,
        error: error.message,
        message: 'Webhook processing failed'
      }
    }
  }

  /**
   * 辅助方法
   */

  private validateWebhookConfig(config: WebhookTriggerConfig): void {
    if (!config.endpoint) {
      throw new Error('Webhook endpoint is required')
    }

    if (!config.endpoint.startsWith('/')) {
      throw new Error('Webhook endpoint must start with /')
    }

    if (!config.method) {
      config.method = 'POST'
    }

    const validMethods = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH']
    if (!validMethods.includes(config.method.toUpperCase())) {
      throw new Error(`Invalid HTTP method: ${config.method}`)
    }

    if (config.authentication) {
      const validAuthTypes = ['token', 'signature', 'basic']
      if (!validAuthTypes.includes(config.authentication.type)) {
        throw new Error(`Invalid authentication type: ${config.authentication.type}`)
      }
    }
  }

  private addToRequestHistory(webhookId: string, request: WebhookRequest): void {
    const history = this.requestHistory.get(webhookId) || []
    history.push(request)

    // 保持历史记录在合理范围内
    if (history.length > this.MAX_HISTORY_SIZE) {
      history.splice(0, history.length - this.MAX_HISTORY_SIZE)
    }

    this.requestHistory.set(webhookId, history)
  }

  private updateWebhookStats(webhook: WebhookTrigger, success: boolean): void {
    webhook.requestCount++
    webhook.lastRequest = new Date()

    if (success) {
      webhook.successCount++
    } else {
      webhook.errorCount++
    }
  }

  private getValueByPath(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => current && current[key], obj)
  }

  private createSuccessResponse(message: string, requestId: string, data?: any): WebhookResponse {
    return {
      success: true,
      message,
      requestId,
      timestamp: new Date(),
      data
    }
  }

  private createErrorResponse(status: number, message: string, requestId: string): WebhookResponse {
    return {
      success: false,
      status,
      message,
      requestId,
      timestamp: new Date()
    }
  }

  private cleanupRequestHistory(): void {
    const cutoffTime = Date.now() - (24 * 60 * 60 * 1000) // 24小时前

    for (const [webhookId, history] of this.requestHistory.entries()) {
      const validRequests = history.filter(request => 
        request.timestamp.getTime() > cutoffTime
      )
      
      if (validRequests.length === 0) {
        this.requestHistory.delete(webhookId)
      } else {
        this.requestHistory.set(webhookId, validRequests)
      }
    }

    console.log('🧹 Webhook请求历史清理完成')
  }

  private resetRateLimits(): void {
    this.rateLimits.clear()
    console.log('🔄 Webhook速率限制已重置')
  }

  /**
   * 公共API方法
   */

  public getWebhookInfo(webhookId: string): WebhookInfo | undefined {
    const webhook = this.webhookEndpoints.get(webhookId)
    if (!webhook) return undefined

    const history = this.requestHistory.get(webhookId) || []
    const recentRequests = history.slice(-10)

    return {
      webhook,
      recentRequests,
      statistics: {
        totalRequests: webhook.requestCount,
        successfulRequests: webhook.successCount,
        errorRequests: webhook.errorCount,
        successRate: webhook.requestCount > 0 ? (webhook.successCount / webhook.requestCount) * 100 : 0,
        lastRequest: webhook.lastRequest
      }
    }
  }

  public getAllWebhooks(): WebhookInfo[] {
    const webhookInfos: WebhookInfo[] = []
    
    for (const webhookId of this.webhookEndpoints.keys()) {
      const info = this.getWebhookInfo(webhookId)
      if (info) {
        webhookInfos.push(info)
      }
    }

    return webhookInfos
  }

  public getRequestHistory(webhookId: string, limit?: number): WebhookRequest[] {
    const history = this.requestHistory.get(webhookId) || []
    
    if (limit) {
      return history.slice(-limit)
    }
    
    return [...history]
  }

  public async enableWebhook(webhookId: string): Promise<void> {
    const webhook = this.webhookEndpoints.get(webhookId)
    if (webhook) {
      webhook.isActive = true
      console.log(`✅ Webhook触发器已启用: ${webhookId}`)
    }
  }

  public async disableWebhook(webhookId: string): Promise<void> {
    const webhook = this.webhookEndpoints.get(webhookId)
    if (webhook) {
      webhook.isActive = false
      console.log(`⏸️ Webhook触发器已禁用: ${webhookId}`)
    }
  }

  public async removeWebhook(webhookId: string): Promise<void> {
    this.webhookEndpoints.delete(webhookId)
    this.requestHistory.delete(webhookId)
    console.log(`🗑️ Webhook触发器已删除: ${webhookId}`)
  }

  public getRegisteredEndpoints(): string[] {
    return Array.from(this.webhookEndpoints.values()).map(webhook => 
      `${webhook.config.method} ${webhook.config.endpoint}`
    )
  }

  public async stop(): Promise<void> {
    // 清理资源
    this.webhookEndpoints.clear()
    this.requestHistory.clear()
    this.rateLimits.clear()

    // 移除所有监听器
    this.removeAllListeners()

    console.log('⏹️ Webhook触发器服务已停止')
  }
}

/**
 * 相关接口定义
 */
export interface WebhookTriggerConfig {
  endpoint: string                     // Webhook端点路径
  method: string                      // HTTP方法
  authentication?: {                  // 认证配置
    type: 'token' | 'signature' | 'basic'
    token?: string
    secret?: string
    key?: string
    username?: string
    password?: string
  }
  filters?: {                         // 过滤器配置
    headers?: Record<string, string>
    body?: Record<string, any>
    eventTypes?: string[]
  }
  enabled?: boolean                   // 是否启用
  workflowId?: string                 // 关联工作流ID
  workflowName?: string               // 工作流名称
  description?: string                // 描述
  rateLimit?: number                  // 速率限制（每小时）
}

export interface WebhookTrigger {
  id: string                          // Webhook ID
  config: WebhookTriggerConfig        // 配置
  isActive: boolean                   // 是否激活
  createdAt: Date                     // 创建时间
  lastRequest: Date | null            // 最后请求时间
  requestCount: number                // 请求总数
  successCount: number                // 成功请求数
  errorCount: number                  // 错误请求数
}

export interface WebhookRequest {
  id: string                          // 请求ID
  webhookId: string                   // Webhook ID
  timestamp: Date                     // 请求时间
  method: string                      // HTTP方法
  endpoint: string                    // 端点路径
  headers: Record<string, string>     // 请求头
  body: any                          // 请求体
  clientIP: string                   // 客户端IP
  status: 'PROCESSING' | 'SUCCESS' | 'ERROR' // 处理状态
  response?: WebhookExecutionResult   // 响应结果
  processingTime?: number            // 处理时间（毫秒）
}

export interface WebhookResponse {
  success: boolean                    // 是否成功
  status?: number                     // HTTP状态码
  message: string                     // 响应消息
  requestId: string                   // 请求ID
  timestamp: Date                     // 响应时间
  data?: any                         // 响应数据
}

export interface WebhookExecutionResult {
  success: boolean                    // 是否成功
  executionId?: string               // 执行ID
  message: string                    // 消息
  error?: string                     // 错误信息
}

export interface WebhookInfo {
  webhook: WebhookTrigger             // Webhook信息
  recentRequests: WebhookRequest[]    // 最近请求
  statistics: {                       // 统计信息
    totalRequests: number
    successfulRequests: number
    errorRequests: number
    successRate: number
    lastRequest: Date | null
  }
}

export interface RateLimitInfo {
  count: number                       // 当前计数
  windowStart: number                 // 窗口开始时间
  lastRequest: number                 // 最后请求时间
}

export interface RateLimitResult {
  allowed: boolean                    // 是否允许
  remaining?: number                  // 剩余次数
  message?: string                   // 错误消息
  resetTime?: Date                   // 重置时间
}

export interface AuthResult {
  valid: boolean                     // 是否有效
  message?: string                   // 错误消息
}

export interface FilterResult {
  passed: boolean                    // 是否通过
  reason?: string                    // 原因
}

// 导出单例实例
export const webhookTrigger = WebhookTriggerService.getInstance()