import cron from 'node-cron'
import { CacheService } from '@/config/redis.config'
import { prisma } from '@/config/database.config'

/**
 * 定时触发器服务
 * 基于Cron表达式的定时任务触发器
 */
export class CronTriggerService {
  private static instance: CronTriggerService
  private cronJobs: Map<string, cron.ScheduledTask> = new Map()
  private jobConfigs: Map<string, CronTriggerConfig> = new Map()
  private executionHistory: Map<string, CronExecution[]> = new Map()
  private readonly MAX_HISTORY_SIZE = 100

  // 预定义的常用Cron表达式
  public static readonly PRESETS = {
    EVERY_MINUTE: '* * * * *',
    EVERY_5_MINUTES: '*/5 * * * *',
    EVERY_15_MINUTES: '*/15 * * * *',
    EVERY_30_MINUTES: '*/30 * * * *',
    EVERY_HOUR: '0 * * * *',
    EVERY_2_HOURS: '0 */2 * * *',
    EVERY_6_HOURS: '0 */6 * * *',
    EVERY_12_HOURS: '0 */12 * * *',
    DAILY_9AM: '0 9 * * *',
    DAILY_6PM: '0 18 * * *',
    WEEKDAYS_9AM: '0 9 * * 1-5',
    WEEKDAYS_6PM: '0 18 * * 1-5',
    WEEKLY_MONDAY_9AM: '0 9 * * 1',
    WEEKLY_FRIDAY_6PM: '0 18 * * 5',
    MONTHLY_1ST_9AM: '0 9 1 * *',
    QUARTERLY: '0 9 1 1,4,7,10 *',
    YEARLY: '0 9 1 1 *'
  } as const

  // 时区映射
  public static readonly TIMEZONES = {
    SHANGHAI: 'Asia/Shanghai',
    BEIJING: 'Asia/Beijing',
    HONG_KONG: 'Asia/Hong_Kong',
    TOKYO: 'Asia/Tokyo',
    NEW_YORK: 'America/New_York',
    LONDON: 'Europe/London',
    UTC: 'UTC'
  } as const

  private constructor() {
    this.initializeService()
  }

  public static getInstance(): CronTriggerService {
    if (!CronTriggerService.instance) {
      CronTriggerService.instance = new CronTriggerService()
    }
    return CronTriggerService.instance
  }

  /**
   * 初始化定时触发器服务
   */
  private async initializeService() {
    console.log('⏰ 定时触发器服务初始化中...')
    
    try {
      // 加载持久化的定时任务
      await this.loadPersistedJobs()
      
      // 启动系统维护任务
      this.startMaintenanceTasks()
      
      // 注册默认的系统任务
      await this.registerSystemTasks()
      
      console.log('✅ 定时触发器服务初始化完成')
    } catch (error) {
      console.error('❌ 定时触发器服务初始化失败:', error)
      throw error
    }
  }

  /**
   * 加载持久化的定时任务
   */
  private async loadPersistedJobs() {
    try {
      const workflows = await prisma.workflowDefinition.findMany({
        where: {
          isActive: true,
          triggerConfig: {
            path: ['type'],
            equals: 'SCHEDULED'
          }
        }
      })

      for (const workflow of workflows) {
        const triggerConfig = workflow.triggerConfig as any
        if (triggerConfig && triggerConfig.config) {
          await this.scheduleJob(`workflow-${workflow.id}`, {
            schedule: triggerConfig.config.schedule,
            timezone: triggerConfig.config.timezone || CronTriggerService.TIMEZONES.SHANGHAI,
            enabled: triggerConfig.config.enabled !== false,
            maxExecutions: triggerConfig.config.maxExecutions,
            executionCount: 0,
            workflowId: workflow.id,
            workflowName: workflow.name,
            description: `工作流定时触发器: ${workflow.name}`
          })
        }
      }

      console.log(`📝 加载了 ${workflows.length} 个持久化定时任务`)
    } catch (error) {
      console.error('加载持久化定时任务失败:', error)
    }
  }

  /**
   * 启动系统维护任务
   */
  private startMaintenanceTasks() {
    // 每小时清理执行历史
    this.scheduleJob('system-cleanup-history', {
      schedule: '0 * * * *', // 每小时执行
      timezone: CronTriggerService.TIMEZONES.SHANGHAI,
      enabled: true,
      description: '清理执行历史记录',
      handler: () => this.cleanupExecutionHistory()
    })

    // 每天生成定时任务报告
    this.scheduleJob('system-daily-report', {
      schedule: '0 8 * * *', // 每天8点执行
      timezone: CronTriggerService.TIMEZONES.SHANGHAI,
      enabled: true,
      description: '生成定时任务日报',
      handler: () => this.generateDailyReport()
    })

    console.log('🔧 系统维护任务已启动')
  }

  /**
   * 注册默认的系统任务
   */
  private async registerSystemTasks() {
    const systemTasks = [
      {
        name: 'system-health-check',
        schedule: '*/5 * * * *', // 每5分钟
        description: '系统健康检查',
        timezone: CronTriggerService.TIMEZONES.SHANGHAI,
        handler: () => this.performHealthCheck()
      },
      {
        name: 'database-backup',
        schedule: '0 2 * * *', // 每天凌晨2点
        description: '数据库备份',
        timezone: CronTriggerService.TIMEZONES.SHANGHAI,
        handler: () => this.performDatabaseBackup()
      },
      {
        name: 'log-rotation',
        schedule: '0 0 * * 0', // 每周日凌晨
        description: '日志轮转',
        timezone: CronTriggerService.TIMEZONES.SHANGHAI,
        handler: () => this.performLogRotation()
      }
    ]

    for (const task of systemTasks) {
      await this.scheduleJob(task.name, {
        schedule: task.schedule,
        timezone: task.timezone,
        enabled: true,
        description: task.description,
        isSystemTask: true,
        handler: task.handler
      })
    }

    console.log(`⚙️ 注册了 ${systemTasks.length} 个系统任务`)
  }

  /**
   * 调度定时任务
   */
  public async scheduleJob(jobId: string, config: CronTriggerConfig): Promise<void> {
    try {
      // 验证Cron表达式
      if (!cron.validate(config.schedule)) {
        throw new Error(`无效的Cron表达式: ${config.schedule}`)
      }

      // 如果任务已存在，先停止
      if (this.cronJobs.has(jobId)) {
        await this.unscheduleJob(jobId)
      }

      // 创建任务执行器
      const taskExecutor = this.createTaskExecutor(jobId, config)

      // 创建Cron任务
      const task = cron.schedule(config.schedule, taskExecutor, {
        scheduled: false,
        timezone: config.timezone || CronTriggerService.TIMEZONES.SHANGHAI,
        name: jobId
      })

      // 存储任务引用和配置
      this.cronJobs.set(jobId, task)
      this.jobConfigs.set(jobId, config)

      // 如果启用，立即启动任务
      if (config.enabled !== false) {
        task.start()
        console.log(`⏰ 定时任务已启动: ${jobId} - ${config.schedule}`)
      } else {
        console.log(`⏸️ 定时任务已创建但未启动: ${jobId}`)
      }

      // 初始化执行历史
      if (!this.executionHistory.has(jobId)) {
        this.executionHistory.set(jobId, [])
      }

    } catch (error) {
      console.error(`调度定时任务失败 ${jobId}:`, error)
      throw error
    }
  }

  /**
   * 创建任务执行器
   */
  private createTaskExecutor(jobId: string, config: CronTriggerConfig): () => Promise<void> {
    return async () => {
      const execution: CronExecution = {
        id: `${jobId}-${Date.now()}`,
        jobId,
        startTime: new Date(),
        status: 'RUNNING'
      }

      try {
        // 检查最大执行次数限制
        if (config.maxExecutions && config.executionCount && config.executionCount >= config.maxExecutions) {
          console.log(`⏹️ 定时任务已达到最大执行次数: ${jobId}`)
          await this.unscheduleJob(jobId)
          return
        }

        // 添加执行记录
        const history = this.executionHistory.get(jobId)!
        history.push(execution)

        console.log(`🔄 执行定时任务: ${jobId}`)

        let result: any

        // 执行任务
        if (config.handler) {
          // 自定义处理器
          result = await config.handler()
        } else if (config.workflowId) {
          // 触发工作流
          const { workflowEngine } = await import('@/services/workflow-engine.service')
          result = await workflowEngine.triggerWorkflow(
            config.workflowId,
            'SCHEDULED',
            {
              triggeredBy: 'cron-trigger',
              schedule: config.schedule,
              timezone: config.timezone,
              timestamp: new Date()
            }
          )
        } else {
          throw new Error('未指定任务处理器或工作流ID')
        }

        // 更新执行状态
        execution.endTime = new Date()
        execution.duration = execution.endTime.getTime() - execution.startTime.getTime()
        execution.status = 'SUCCESS'
        execution.result = result
        execution.output = typeof result === 'object' ? JSON.stringify(result) : String(result)

        // 增加执行计数
        if (config.executionCount !== undefined) {
          config.executionCount++
        }

        console.log(`✅ 定时任务执行成功: ${jobId} (${execution.duration}ms)`)

      } catch (error: any) {
        // 更新失败状态
        execution.endTime = new Date()
        execution.duration = execution.endTime ? execution.endTime.getTime() - execution.startTime.getTime() : 0
        execution.status = 'FAILED'
        execution.error = error.message
        execution.errorStack = error.stack

        console.error(`❌ 定时任务执行失败 ${jobId}:`, error)

        // 发送错误通知（可选）
        await this.notifyExecutionError(jobId, config, error)
      } finally {
        // 清理过多的历史记录
        const history = this.executionHistory.get(jobId)!
        if (history.length > this.MAX_HISTORY_SIZE) {
          history.splice(0, history.length - this.MAX_HISTORY_SIZE)
        }

        // 缓存最新执行信息
        await this.cacheExecutionInfo(jobId, execution)
      }
    }
  }

  /**
   * 取消调度任务
   */
  public async unscheduleJob(jobId: string): Promise<void> {
    const task = this.cronJobs.get(jobId)
    if (task) {
      task.stop()
      task.destroy()
      this.cronJobs.delete(jobId)
      this.jobConfigs.delete(jobId)
      console.log(`⏹️ 定时任务已停止: ${jobId}`)
    }
  }

  /**
   * 启动任务
   */
  public async startJob(jobId: string): Promise<void> {
    const task = this.cronJobs.get(jobId)
    const config = this.jobConfigs.get(jobId)
    
    if (task && config) {
      config.enabled = true
      task.start()
      console.log(`▶️ 定时任务已启动: ${jobId}`)
    } else {
      throw new Error(`任务不存在: ${jobId}`)
    }
  }

  /**
   * 停止任务
   */
  public async stopJob(jobId: string): Promise<void> {
    const task = this.cronJobs.get(jobId)
    const config = this.jobConfigs.get(jobId)
    
    if (task && config) {
      config.enabled = false
      task.stop()
      console.log(`⏸️ 定时任务已停止: ${jobId}`)
    } else {
      throw new Error(`任务不存在: ${jobId}`)
    }
  }

  /**
   * 手动执行任务
   */
  public async runJobNow(jobId: string): Promise<CronExecution> {
    const config = this.jobConfigs.get(jobId)
    if (!config) {
      throw new Error(`任务不存在: ${jobId}`)
    }

    console.log(`🚀 手动执行定时任务: ${jobId}`)
    const taskExecutor = this.createTaskExecutor(jobId, config)
    await taskExecutor()

    // 返回最新的执行记录
    const history = this.executionHistory.get(jobId) || []
    return history[history.length - 1]
  }

  /**
   * 获取任务信息
   */
  public getJobInfo(jobId: string): CronJobInfo | undefined {
    const config = this.jobConfigs.get(jobId)
    const task = this.cronJobs.get(jobId)
    const history = this.executionHistory.get(jobId) || []
    
    if (!config) return undefined

    const lastExecution = history[history.length - 1]
    const successfulExecutions = history.filter(e => e.status === 'SUCCESS').length
    const failedExecutions = history.filter(e => e.status === 'FAILED').length

    return {
      jobId,
      config,
      isRunning: task?.running || false,
      isScheduled: !!task,
      lastExecution,
      totalExecutions: history.length,
      successfulExecutions,
      failedExecutions,
      successRate: history.length > 0 ? (successfulExecutions / history.length) * 100 : 0,
      nextExecutionTime: this.getNextExecutionTime(config.schedule, config.timezone),
      avgExecutionTime: this.calculateAverageExecutionTime(history)
    }
  }

  /**
   * 获取所有任务信息
   */
  public getAllJobsInfo(): CronJobInfo[] {
    const jobInfos: CronJobInfo[] = []
    
    for (const jobId of this.jobConfigs.keys()) {
      const info = this.getJobInfo(jobId)
      if (info) {
        jobInfos.push(info)
      }
    }

    return jobInfos.sort((a, b) => a.jobId.localeCompare(b.jobId))
  }

  /**
   * 获取任务执行历史
   */
  public getExecutionHistory(jobId: string, limit?: number): CronExecution[] {
    const history = this.executionHistory.get(jobId) || []
    
    if (limit) {
      return history.slice(-limit)
    }
    
    return [...history]
  }

  /**
   * 获取任务统计信息
   */
  public getJobStatistics(): CronStatistics {
    const allJobs = this.getAllJobsInfo()
    
    return {
      totalJobs: allJobs.length,
      runningJobs: allJobs.filter(job => job.isRunning).length,
      scheduledJobs: allJobs.filter(job => job.isScheduled).length,
      totalExecutions: allJobs.reduce((sum, job) => sum + job.totalExecutions, 0),
      successfulExecutions: allJobs.reduce((sum, job) => sum + job.successfulExecutions, 0),
      failedExecutions: allJobs.reduce((sum, job) => sum + job.failedExecutions, 0),
      overallSuccessRate: allJobs.length > 0 
        ? allJobs.reduce((sum, job) => sum + job.successRate, 0) / allJobs.length 
        : 0,
      avgExecutionTime: allJobs.length > 0
        ? allJobs.reduce((sum, job) => sum + job.avgExecutionTime, 0) / allJobs.length
        : 0
    }
  }

  /**
   * 验证Cron表达式
   */
  public static validateCronExpression(expression: string): boolean {
    return cron.validate(expression)
  }

  /**
   * 解析Cron表达式到人类可读格式
   */
  public static parseCronExpression(expression: string): string {
    if (!cron.validate(expression)) {
      return '无效的Cron表达式'
    }

    // 简单的Cron表达式解析（可以使用更完整的库如cronstrue）
    const parts = expression.split(' ')
    if (parts.length !== 5) {
      return '无效的Cron表达式格式'
    }

    const [minute, hour, day, month, weekday] = parts

    // 检查常见模式
    if (expression === '* * * * *') return '每分钟'
    if (expression === '0 * * * *') return '每小时'
    if (expression === '0 0 * * *') return '每天'
    if (expression === '0 0 * * 0') return '每周日'
    if (expression === '0 0 1 * *') return '每月第一天'

    // 构建描述
    let description = ''
    
    if (minute !== '*') description += `分钟${minute} `
    if (hour !== '*') description += `小时${hour} `
    if (day !== '*') description += `日期${day} `
    if (month !== '*') description += `月份${month} `
    if (weekday !== '*') description += `星期${weekday} `

    return description || expression
  }

  /**
   * 获取下次执行时间
   */
  private getNextExecutionTime(schedule: string, timezone?: string): Date | null {
    try {
      // 这里需要使用第三方库来计算下次执行时间
      // 简化实现，返回当前时间加1小时
      return new Date(Date.now() + 3600000)
    } catch {
      return null
    }
  }

  /**
   * 计算平均执行时间
   */
  private calculateAverageExecutionTime(history: CronExecution[]): number {
    const completedExecutions = history.filter(e => e.duration !== undefined)
    if (completedExecutions.length === 0) return 0

    const totalDuration = completedExecutions.reduce((sum, e) => sum + (e.duration || 0), 0)
    return Math.round(totalDuration / completedExecutions.length)
  }

  /**
   * 缓存执行信息
   */
  private async cacheExecutionInfo(jobId: string, execution: CronExecution): Promise<void> {
    const cacheKey = `cron-execution:${jobId}:last`
    await CacheService.setex(cacheKey, 86400, JSON.stringify(execution)) // 缓存24小时
  }

  /**
   * 通知执行错误
   */
  private async notifyExecutionError(jobId: string, config: CronTriggerConfig, error: Error): Promise<void> {
    // 这里可以集成邮件、短信或其他通知方式
    console.error(`📧 定时任务执行失败通知: ${jobId} - ${error.message}`)
  }

  /**
   * 清理执行历史
   */
  private cleanupExecutionHistory(): void {
    const cutoffTime = Date.now() - (7 * 24 * 60 * 60 * 1000) // 7天前

    for (const [jobId, history] of this.executionHistory.entries()) {
      const validExecutions = history.filter(execution => 
        execution.startTime.getTime() > cutoffTime
      )
      
      if (validExecutions.length === 0) {
        this.executionHistory.delete(jobId)
      } else {
        this.executionHistory.set(jobId, validExecutions)
      }
    }

    console.log('🧹 定时任务执行历史清理完成')
  }

  /**
   * 生成日报
   */
  private generateDailyReport(): void {
    const stats = this.getJobStatistics()
    const report = {
      date: new Date().toISOString().split('T')[0],
      statistics: stats,
      topFailedJobs: this.getTopFailedJobs(5)
    }

    console.log('📊 定时任务日报生成完成:', report)
  }

  /**
   * 获取失败最多的任务
   */
  private getTopFailedJobs(limit: number): Array<{jobId: string, failedExecutions: number}> {
    const allJobs = this.getAllJobsInfo()
    
    return allJobs
      .filter(job => job.failedExecutions > 0)
      .sort((a, b) => b.failedExecutions - a.failedExecutions)
      .slice(0, limit)
      .map(job => ({ jobId: job.jobId, failedExecutions: job.failedExecutions }))
  }

  /**
   * 系统维护方法
   */
  
  private async performHealthCheck(): Promise<void> {
    // 执行系统健康检查
    console.log('🩺 执行系统健康检查')
  }

  private async performDatabaseBackup(): Promise<void> {
    // 执行数据库备份
    console.log('💾 执行数据库备份')
  }

  private async performLogRotation(): Promise<void> {
    // 执行日志轮转
    console.log('📜 执行日志轮转')
  }

  /**
   * 停止所有定时任务
   */
  public async stopAll(): Promise<void> {
    for (const [jobId] of this.cronJobs.entries()) {
      await this.unscheduleJob(jobId)
    }
    
    this.executionHistory.clear()
    
    console.log('⏹️ 所有定时任务已停止')
  }
}

/**
 * 相关接口定义
 */
export interface CronTriggerConfig {
  schedule: string                    // Cron表达式
  timezone?: string                   // 时区
  enabled?: boolean                   // 是否启用
  maxExecutions?: number              // 最大执行次数
  executionCount?: number             // 当前执行次数
  workflowId?: string                 // 关联的工作流ID
  workflowName?: string               // 工作流名称
  description?: string                // 描述
  isSystemTask?: boolean              // 是否为系统任务
  handler?: () => Promise<any>        // 自定义处理器
}

export interface CronExecution {
  id: string                          // 执行ID
  jobId: string                       // 任务ID
  startTime: Date                     // 开始时间
  endTime?: Date                      // 结束时间
  duration?: number                   // 执行时长（毫秒）
  status: 'RUNNING' | 'SUCCESS' | 'FAILED' // 执行状态
  result?: any                        // 执行结果
  output?: string                     // 输出信息
  error?: string                      // 错误信息
  errorStack?: string                 // 错误堆栈
}

export interface CronJobInfo {
  jobId: string                       // 任务ID
  config: CronTriggerConfig           // 任务配置
  isRunning: boolean                  // 是否正在运行
  isScheduled: boolean                // 是否已调度
  lastExecution?: CronExecution       // 最后执行记录
  totalExecutions: number             // 总执行次数
  successfulExecutions: number        // 成功执行次数
  failedExecutions: number            // 失败执行次数
  successRate: number                 // 成功率（百分比）
  nextExecutionTime?: Date            // 下次执行时间
  avgExecutionTime: number            // 平均执行时间（毫秒）
}

export interface CronStatistics {
  totalJobs: number                   // 总任务数
  runningJobs: number                 // 运行中任务数
  scheduledJobs: number               // 已调度任务数
  totalExecutions: number             // 总执行次数
  successfulExecutions: number        // 成功执行次数
  failedExecutions: number            // 失败执行次数
  overallSuccessRate: number          // 整体成功率
  avgExecutionTime: number            // 平均执行时间
}

// 导出单例实例
export const cronTrigger = CronTriggerService.getInstance()