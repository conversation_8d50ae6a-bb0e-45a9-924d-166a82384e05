import { EventEmitter } from 'events'
import { prisma } from '@/config/database.config'
import { CacheService } from '@/config/redis.config'

/**
 * 条件触发器服务
 * 基于条件判断的智能触发器系统
 */
export class ConditionalTriggerService extends EventEmitter {
  private static instance: ConditionalTriggerService
  private activeConditions: Map<string, ConditionalTrigger> = new Map()
  private checkIntervals: Map<string, NodeJS.Timeout> = new Map()
  private conditionHistory: Map<string, ConditionEvaluation[]> = new Map()
  private readonly MAX_HISTORY_SIZE = 50

  // 数据源缓存
  private dataSourceCache: Map<string, any> = new Map()
  private cacheExpiry: Map<string, number> = new Map()

  private constructor() {
    super()
    this.setMaxListeners(100)
    this.initializeService()
  }

  public static getInstance(): ConditionalTriggerService {
    if (!ConditionalTriggerService.instance) {
      ConditionalTriggerService.instance = new ConditionalTriggerService()
    }
    return ConditionalTriggerService.instance
  }

  /**
   * 初始化条件触发器服务
   */
  private async initializeService() {
    console.log('🔍 条件触发器服务初始化中...')
    
    try {
      // 加载现有条件触发器
      await this.loadExistingConditionalTriggers()
      
      // 启动数据源缓存清理
      this.startCacheCleanup()
      
      // 注册默认条件
      this.registerBuiltinConditions()
      
      console.log('✅ 条件触发器服务初始化完成')
    } catch (error) {
      console.error('❌ 条件触发器服务初始化失败:', error)
      throw error
    }
  }

  /**
   * 加载现有的条件触发器
   */
  private async loadExistingConditionalTriggers() {
    try {
      const workflows = await prisma.workflowDefinition.findMany({
        where: {
          isActive: true,
          triggerConfig: {
            path: ['type'],
            equals: 'CONDITION'
          }
        }
      })

      for (const workflow of workflows) {
        const triggerConfig = workflow.triggerConfig as any
        if (triggerConfig && triggerConfig.config) {
          const workflowId = Array.isArray(workflow.id) ? workflow.id[0] : workflow.id
          await this.createConditionalTrigger(`workflow-${workflowId}`, {
            conditions: triggerConfig.config.conditions || [],
            operator: triggerConfig.config.operator || 'AND',
            checkInterval: triggerConfig.config.checkInterval || 300,
            cooldownPeriod: triggerConfig.config.cooldownPeriod || 600,
            enabled: true,
            workflowId: workflowId,
            workflowName: workflow.name,
            description: `条件触发器: ${workflow.name}`
          })
        }
      }

      console.log(`📝 加载了 ${workflows.length} 个条件触发器`)
    } catch (error) {
      console.error('加载条件触发器失败:', error)
    }
  }

  /**
   * 注册内置条件
   */
  private registerBuiltinConditions() {
    // 系统负载监控条件
    this.createConditionalTrigger('system-high-cpu', {
      conditions: [
        {
          field: 'cpu.usage',
          operator: 'gt',
          value: 80,
          source: 'metric',
          description: 'CPU使用率超过80%'
        }
      ],
      operator: 'AND',
      checkInterval: 60, // 每分钟检查
      cooldownPeriod: 300, // 5分钟冷却期
      enabled: true,
      description: '系统CPU负载过高监控'
    })

    // 服务工单积压监控
    this.createConditionalTrigger('service-backlog-alert', {
      conditions: [
        {
          field: 'count',
          operator: 'gt',
          value: 10,
          source: 'database',
          query: "SELECT COUNT(*) as count FROM services WHERE status IN ('OPEN', 'PENDING') AND created_at < DATE_SUB(NOW(), INTERVAL 24 HOUR)",
          description: '超过24小时的待处理工单数量大于10'
        }
      ],
      operator: 'AND',
      checkInterval: 1800, // 30分钟检查
      cooldownPeriod: 3600, // 1小时冷却期
      enabled: true,
      description: '服务工单积压监控'
    })

    // 客户满意度监控
    this.createConditionalTrigger('customer-satisfaction-low', {
      conditions: [
        {
          field: 'avg_satisfaction',
          operator: 'lt',
          value: 3.0,
          source: 'database',
          query: "SELECT AVG(satisfaction) as avg_satisfaction FROM services WHERE satisfaction IS NOT NULL AND updated_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)",
          description: '最近7天平均客户满意度低于3.0'
        },
        {
          field: 'sample_size',
          operator: 'gte',
          value: 5,
          source: 'database',
          query: "SELECT COUNT(*) as sample_size FROM services WHERE satisfaction IS NOT NULL AND updated_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)",
          description: '满意度样本数量至少5个'
        }
      ],
      operator: 'AND',
      checkInterval: 3600, // 1小时检查
      cooldownPeriod: 7200, // 2小时冷却期
      enabled: true,
      description: '客户满意度过低监控'
    })

    console.log('🔧 内置条件触发器已注册')
  }

  /**
   * 创建条件触发器
   */
  public async createConditionalTrigger(triggerId: string, config: ConditionalTriggerConfig): Promise<void> {
    try {
      // 验证条件配置
      this.validateConditions(config.conditions)

      const trigger: ConditionalTrigger = {
        id: triggerId,
        config,
        isActive: config.enabled !== false,
        lastCheck: null,
        lastTrigger: null,
        nextCheck: new Date(Date.now() + config.checkInterval * 1000),
        evaluationCount: 0,
        triggerCount: 0
      }

      this.activeConditions.set(triggerId, trigger)
      
      if (trigger.isActive) {
        await this.startConditionMonitoring(triggerId)
      }

      // 初始化历史记录
      this.conditionHistory.set(triggerId, [])

      console.log(`🔍 条件触发器已创建: ${triggerId}`)
    } catch (error) {
      console.error(`创建条件触发器失败 ${triggerId}:`, error)
      throw error
    }
  }

  /**
   * 启动条件监控
   */
  private async startConditionMonitoring(triggerId: string) {
    const trigger = this.activeConditions.get(triggerId)
    if (!trigger) return

    // 清除现有的定时器
    const existingInterval = this.checkIntervals.get(triggerId)
    if (existingInterval) {
      clearInterval(existingInterval)
    }

    // 创建新的检查定时器
    const interval = setInterval(async () => {
      if (trigger.isActive) {
        await this.evaluateConditions(triggerId)
      }
    }, trigger.config.checkInterval * 1000)

    this.checkIntervals.set(triggerId, interval)

    // 立即执行一次检查
    setTimeout(() => this.evaluateConditions(triggerId), 1000)

    console.log(`⏱️ 条件监控已启动: ${triggerId} (间隔: ${trigger.config.checkInterval}秒)`)
  }

  /**
   * 评估条件
   */
  private async evaluateConditions(triggerId: string): Promise<void> {
    const trigger = this.activeConditions.get(triggerId)
    if (!trigger) return

    const evaluation: ConditionEvaluation = {
      id: `${triggerId}-${Date.now()}`,
      triggerId,
      timestamp: new Date(),
      status: 'EVALUATING',
      results: []
    }

    try {
      console.log(`🔍 评估条件: ${triggerId}`)
      
      trigger.lastCheck = new Date()
      trigger.nextCheck = new Date(Date.now() + trigger.config.checkInterval * 1000)
      trigger.evaluationCount++

      // 评估每个条件
      const conditionResults = await Promise.all(
        trigger.config.conditions.map(condition => this.evaluateSingleCondition(condition))
      )

      evaluation.results = conditionResults
      evaluation.status = 'COMPLETED'

      // 根据操作符决定最终结果
      const finalResult = trigger.config.operator === 'AND' 
        ? conditionResults.every(r => r.result)
        : conditionResults.some(r => r.result)

      evaluation.finalResult = finalResult

      // 如果条件满足，检查冷却期
      if (finalResult) {
        const canTrigger = await this.checkCooldownPeriod(triggerId)
        
        if (canTrigger) {
          await this.executeTrigger(triggerId, evaluation)
          trigger.lastTrigger = new Date()
          trigger.triggerCount++
        } else {
          console.log(`❄️ 条件触发器在冷却期: ${triggerId}`)
          evaluation.status = 'COOLDOWN'
        }
      }

    } catch (error: any) {
      evaluation.status = 'ERROR'
      evaluation.error = error.message
      console.error(`条件评估失败 ${triggerId}:`, error)
    } finally {
      // 添加到历史记录
      const history = this.conditionHistory.get(triggerId)!
      history.push(evaluation)
      
      // 保持历史记录在合理范围内
      if (history.length > this.MAX_HISTORY_SIZE) {
        history.splice(0, history.length - this.MAX_HISTORY_SIZE)
      }

      // 缓存评估结果
      await this.cacheEvaluationResult(triggerId, evaluation)
    }
  }

  /**
   * 评估单个条件
   */
  private async evaluateSingleCondition(condition: TriggerCondition): Promise<ConditionResult> {
    const result: ConditionResult = {
      condition,
      result: false,
      actualValue: null,
      timestamp: new Date()
    }

    try {
      // 获取实际值
      const actualValue = await this.getConditionValue(condition)
      result.actualValue = actualValue

      // 执行比较
      result.result = this.compareValues(actualValue, condition.operator, condition.value)
      
      console.log(`📊 条件评估: ${condition.field} ${condition.operator} ${condition.value} = ${result.result} (实际值: ${actualValue})`)

    } catch (error: any) {
      result.error = error.message
      console.error(`单个条件评估失败:`, error)
    }

    return result
  }

  /**
   * 获取条件值
   */
  private async getConditionValue(condition: TriggerCondition): Promise<any> {
    const cacheKey = `condition-value:${condition.source}:${condition.field}:${condition.query}`
    
    // 检查缓存
    const cached = this.dataSourceCache.get(cacheKey)
    const cacheExpiry = this.cacheExpiry.get(cacheKey)
    
    if (cached && cacheExpiry && Date.now() < cacheExpiry) {
      return cached
    }

    let value: any

    try {
      switch (condition.source) {
        case 'database':
          value = await this.getDatabaseValue(condition)
          break
        case 'api':
          value = await this.getApiValue(condition)
          break
        case 'metric':
          value = await this.getMetricValue(condition)
          break
        case 'cache':
          value = await this.getCacheValue(condition)
          break
        case 'variable':
          value = await this.getVariableValue(condition)
          break
        default:
          throw new Error(`不支持的数据源: ${condition.source}`)
      }

      // 缓存结果（根据数据源类型设置不同的过期时间）
      const cacheDuration = this.getCacheDuration(condition.source)
      this.dataSourceCache.set(cacheKey, value)
      this.cacheExpiry.set(cacheKey, Date.now() + cacheDuration)

      return value

    } catch (error) {
      console.error(`获取条件值失败 (${condition.source}):`, error)
      throw error
    }
  }

  /**
   * 从数据库获取值
   */
  private async getDatabaseValue(condition: TriggerCondition): Promise<any> {
    if (!condition.query) {
      throw new Error('数据库条件缺少查询语句')
    }

    try {
      const result = await prisma.$queryRawUnsafe(condition.query)
      
      // 处理查询结果，将 BigInt 转换为字符串
      const processValue = (value: any): any => {
        if (typeof value === 'bigint') {
          return value.toString()
        }
        if (Array.isArray(value)) {
          return value.map(processValue)
        }
        if (value && typeof value === 'object') {
          const processed: any = {}
          for (const [key, val] of Object.entries(value)) {
            processed[key] = processValue(val)
          }
          return processed
        }
        return value
      }
      
      const processedResult = processValue(result)
      
      if (Array.isArray(processedResult) && processedResult.length > 0) {
        return processedResult[0][condition.field] || processedResult[0]
      }
      
      return processedResult
    } catch (error) {
      console.error('数据库查询失败:', error)
      throw new Error(`数据库查询失败: ${error}`)
    }
  }

  /**
   * 从API获取值
   */
  private async getApiValue(condition: TriggerCondition): Promise<any> {
    if (!condition.query) {
      throw new Error('API条件缺少URL')
    }

    try {
      const controller = new AbortController()
      const timeoutId = setTimeout(() => controller.abort(), 10000) // 10秒超时

      const response = await fetch(condition.query, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'OPS-Management-Conditional-Trigger'
        },
        signal: controller.signal
      })

      clearTimeout(timeoutId)

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const data = await response.json()
      return this.extractValueFromPath(data, condition.field)
    } catch (error) {
      console.error('API调用失败:', error)
      throw new Error(`API调用失败: ${error}`)
    }
  }

  /**
   * 获取系统指标值
   */
  private async getMetricValue(condition: TriggerCondition): Promise<any> {
    // 这里应该集成实际的系统监控服务
    // 简化实现，返回模拟数据
    const metrics: Record<string, any> = {
      'cpu.usage': Math.random() * 100,
      'memory.usage': Math.random() * 100,
      'disk.usage': Math.random() * 100,
      'network.rx': Math.random() * 1000,
      'network.tx': Math.random() * 1000,
      'active_connections': Math.floor(Math.random() * 1000),
      'response_time': Math.random() * 1000
    }

    return metrics[condition.field] || 0
  }

  /**
   * 从缓存获取值
   */
  private async getCacheValue(condition: TriggerCondition): Promise<any> {
    const cacheValue = await CacheService.get(condition.field)
    if (cacheValue) {
      try {
        return JSON.parse(cacheValue as string)
      } catch {
        return cacheValue
      }
    }
    return null
  }

  /**
   * 获取变量值
   */
  private async getVariableValue(condition: TriggerCondition): Promise<any> {
    // 从系统变量或环境变量获取
    const value = process.env[condition.field]
    return value !== undefined ? value : null
  }

  /**
   * 比较值
   */
  private compareValues(actual: any, operator: string, expected: any): boolean {
    if (actual === null || actual === undefined) {
      return operator === 'exists' ? false : operator === 'ne'
    }

    switch (operator) {
      case 'eq': return actual == expected // 使用宽松相等
      case 'ne': return actual != expected
      case 'gt': return Number(actual) > Number(expected)
      case 'gte': return Number(actual) >= Number(expected)
      case 'lt': return Number(actual) < Number(expected)
      case 'lte': return Number(actual) <= Number(expected)
      case 'contains': return String(actual).includes(String(expected))
      case 'starts_with': return String(actual).startsWith(String(expected))
      case 'ends_with': return String(actual).endsWith(String(expected))
      case 'matches': return new RegExp(String(expected)).test(String(actual))
      case 'exists': return actual !== null && actual !== undefined
      case 'in': return Array.isArray(expected) ? expected.includes(actual) : false
      case 'not_in': return Array.isArray(expected) ? !expected.includes(actual) : true
      default:
        console.warn(`未知的比较操作符: ${operator}`)
        return false
    }
  }

  /**
   * 检查冷却期
   */
  private async checkCooldownPeriod(triggerId: string): Promise<boolean> {
    const trigger = this.activeConditions.get(triggerId)
    if (!trigger || !trigger.lastTrigger) return true

    const cooldownPeriod = trigger.config.cooldownPeriod || 0
    if (cooldownPeriod === 0) return true

    const timeSinceLastTrigger = Date.now() - trigger.lastTrigger.getTime()
    return timeSinceLastTrigger >= cooldownPeriod * 1000
  }

  /**
   * 执行触发器
   */
  private async executeTrigger(triggerId: string, evaluation: ConditionEvaluation): Promise<void> {
    const trigger = this.activeConditions.get(triggerId)
    if (!trigger) return

    try {
      console.log(`🚀 执行条件触发器: ${triggerId}`)

      if (trigger.config.workflowId) {
        // 触发工作流
        const { workflowEngine } = await import('@/services/workflow-engine.service')
        await workflowEngine.triggerWorkflow(
          trigger.config.workflowId,
          'CONDITION',
          {
            triggerId,
            evaluation,
            conditionResults: evaluation.results,
            timestamp: new Date()
          }
        )
      }

      // 发送触发事件
      this.emit('condition:triggered', {
        triggerId,
        trigger,
        evaluation
      })

      console.log(`✅ 条件触发器执行成功: ${triggerId}`)

    } catch (error) {
      console.error(`条件触发器执行失败 ${triggerId}:`, error)
      this.emit('condition:error', {
        triggerId,
        trigger,
        error: error
      })
    }
  }

  /**
   * 辅助方法
   */

  private validateConditions(conditions: TriggerCondition[]): void {
    if (!Array.isArray(conditions) || conditions.length === 0) {
      throw new Error('条件列表不能为空')
    }

    for (const condition of conditions) {
      if (!condition.field) {
        throw new Error('条件字段不能为空')
      }
      
      if (!condition.operator) {
        throw new Error('条件操作符不能为空')
      }

      if (!condition.source) {
        throw new Error('条件数据源不能为空')
      }

      if (condition.source === 'database' && !condition.query) {
        throw new Error('数据库条件必须提供查询语句')
      }

      if (condition.source === 'api' && !condition.query) {
        throw new Error('API条件必须提供URL')
      }
    }
  }

  private extractValueFromPath(data: any, path: string): any {
    return path.split('.').reduce((obj, key) => obj && obj[key], data)
  }

  private getCacheDuration(source: string): number {
    const durations = {
      database: 60000,    // 1分钟
      api: 300000,        // 5分钟
      metric: 30000,      // 30秒
      cache: 0,           // 不缓存（已经在缓存中）
      variable: 3600000   // 1小时
    }
    return durations[source as keyof typeof durations] || 60000
  }

  /**
   * 安全序列化函数，处理 BigInt
   */
  private safeStringify(obj: any): string {
    return JSON.stringify(obj, (_key, value) => {
      if (typeof value === 'bigint') {
        return value.toString()
      }
      return value
    })
  }

  private async cacheEvaluationResult(triggerId: string, evaluation: ConditionEvaluation): Promise<void> {
    const cacheKey = `conditional-evaluation:${triggerId}:last`
    await CacheService.setex(cacheKey, 3600, this.safeStringify(evaluation)) // 缓存1小时
  }

  private startCacheCleanup(): void {
    // 每小时清理过期缓存
    setInterval(() => {
      const now = Date.now()
      for (const [key, expiry] of this.cacheExpiry.entries()) {
        if (now > expiry) {
          this.dataSourceCache.delete(key)
          this.cacheExpiry.delete(key)
        }
      }
      console.log('🧹 条件触发器缓存清理完成')
    }, 3600000)
  }

  /**
   * 公共API方法
   */

  public async enableTrigger(triggerId: string): Promise<void> {
    const trigger = this.activeConditions.get(triggerId)
    if (trigger) {
      trigger.isActive = true
      await this.startConditionMonitoring(triggerId)
      console.log(`✅ 条件触发器已启用: ${triggerId}`)
    }
  }

  public async disableTrigger(triggerId: string): Promise<void> {
    const trigger = this.activeConditions.get(triggerId)
    if (trigger) {
      trigger.isActive = false
      const interval = this.checkIntervals.get(triggerId)
      if (interval) {
        clearInterval(interval)
        this.checkIntervals.delete(triggerId)
      }
      console.log(`⏸️ 条件触发器已禁用: ${triggerId}`)
    }
  }

  public async removeTrigger(triggerId: string): Promise<void> {
    await this.disableTrigger(triggerId)
    this.activeConditions.delete(triggerId)
    this.conditionHistory.delete(triggerId)
    console.log(`🗑️ 条件触发器已删除: ${triggerId}`)
  }

  public getTriggerInfo(triggerId: string): ConditionalTriggerInfo | undefined {
    const trigger = this.activeConditions.get(triggerId)
    if (!trigger) return undefined

    const history = this.conditionHistory.get(triggerId) || []
    const recentEvaluations = history.slice(-10)
    
    const successCount = history.filter(e => e.status === 'COMPLETED' && e.finalResult).length
    const errorCount = history.filter(e => e.status === 'ERROR').length

    return {
      trigger,
      recentEvaluations,
      statistics: {
        totalEvaluations: trigger.evaluationCount,
        totalTriggers: trigger.triggerCount,
        successRate: trigger.evaluationCount > 0 ? (successCount / trigger.evaluationCount) * 100 : 0,
        errorRate: trigger.evaluationCount > 0 ? (errorCount / trigger.evaluationCount) * 100 : 0,
        lastCheck: trigger.lastCheck,
        nextCheck: trigger.nextCheck
      }
    }
  }

  public getAllTriggers(): ConditionalTriggerInfo[] {
    const triggerInfos: ConditionalTriggerInfo[] = []
    
    for (const triggerId of this.activeConditions.keys()) {
      const info = this.getTriggerInfo(triggerId)
      if (info) {
        triggerInfos.push(info)
      }
    }

    return triggerInfos
  }

  public getEvaluationHistory(triggerId: string, limit?: number): ConditionEvaluation[] {
    const history = this.conditionHistory.get(triggerId) || []
    
    if (limit) {
      return history.slice(-limit)
    }
    
    return [...history]
  }

  public async forceEvaluation(triggerId: string): Promise<ConditionEvaluation | null> {
    const trigger = this.activeConditions.get(triggerId)
    if (!trigger) return null

    console.log(`🔄 强制评估条件触发器: ${triggerId}`)
    await this.evaluateConditions(triggerId)
    
    const history = this.conditionHistory.get(triggerId) || []
    return history[history.length - 1] || null
  }

  public clearDataSourceCache(): void {
    this.dataSourceCache.clear()
    this.cacheExpiry.clear()
    console.log('🧹 数据源缓存已清空')
  }

  public async stop(): Promise<void> {
    // 停止所有监控
    for (const interval of this.checkIntervals.values()) {
      clearInterval(interval)
    }
    this.checkIntervals.clear()

    // 清理资源
    this.activeConditions.clear()
    this.conditionHistory.clear()
    this.dataSourceCache.clear()
    this.cacheExpiry.clear()

    // 移除所有监听器
    this.removeAllListeners()

    console.log('⏹️ 条件触发器服务已停止')
  }
}

/**
 * 相关接口定义
 */
export interface TriggerCondition {
  field: string                     // 检查字段
  operator: 'eq' | 'ne' | 'gt' | 'gte' | 'lt' | 'lte' | 'contains' | 'starts_with' | 'ends_with' | 'matches' | 'exists' | 'in' | 'not_in'
  value: any                        // 比较值
  source: 'database' | 'api' | 'metric' | 'cache' | 'variable'
  query?: string                    // 查询语句或URL
  description?: string              // 条件描述
}

export interface ConditionalTriggerConfig {
  conditions: TriggerCondition[]    // 条件列表
  operator: 'AND' | 'OR'           // 条件关系
  checkInterval: number             // 检查间隔（秒）
  cooldownPeriod?: number          // 冷却期（秒）
  enabled?: boolean                // 是否启用
  workflowId?: string              // 关联工作流ID
  workflowName?: string            // 工作流名称
  description?: string             // 描述
}

export interface ConditionalTrigger {
  id: string                       // 触发器ID
  config: ConditionalTriggerConfig // 配置
  isActive: boolean                // 是否激活
  lastCheck: Date | null           // 最后检查时间
  lastTrigger: Date | null         // 最后触发时间
  nextCheck: Date                  // 下次检查时间
  evaluationCount: number          // 评估次数
  triggerCount: number             // 触发次数
}

export interface ConditionResult {
  condition: TriggerCondition      // 条件
  result: boolean                  // 评估结果
  actualValue: any                 // 实际值
  timestamp: Date                  // 评估时间
  error?: string                   // 错误信息
}

export interface ConditionEvaluation {
  id: string                       // 评估ID
  triggerId: string                // 触发器ID
  timestamp: Date                  // 评估时间
  status: 'EVALUATING' | 'COMPLETED' | 'ERROR' | 'COOLDOWN' // 状态
  results: ConditionResult[]       // 条件结果
  finalResult?: boolean            // 最终结果
  error?: string                   // 错误信息
}

export interface ConditionalTriggerInfo {
  trigger: ConditionalTrigger      // 触发器信息
  recentEvaluations: ConditionEvaluation[] // 最近评估
  statistics: {                    // 统计信息
    totalEvaluations: number
    totalTriggers: number
    successRate: number
    errorRate: number
    lastCheck: Date | null
    nextCheck: Date
  }
}

// 导出单例实例
export const conditionalTrigger = ConditionalTriggerService.getInstance()