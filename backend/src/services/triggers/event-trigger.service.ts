import { EventEmitter } from 'events'
import { prisma } from '@/config/database.config'
import { CacheService } from '@/config/redis.config'
import { RealtimeService } from '@/services/realtime.service'

/**
 * 事件触发器服务
 * 处理各种系统事件并触发相应的工作流
 */
export class EventTriggerService extends EventEmitter {
  private static instance: EventTriggerService
  private eventBuffer: Map<string, any[]> = new Map() // 事件缓冲区
  private eventFilters: Map<string, Function> = new Map() // 事件过滤器

  // 支持的系统事件类型
  public static readonly EVENT_TYPES = {
    // 服务工单事件
    SERVICE_CREATED: 'service.created',
    SERVICE_UPDATED: 'service.updated',
    SERVICE_ASSIGNED: 'service.assigned',
    SERVICE_COMPLETED: 'service.completed',
    SERVICE_CLOSED: 'service.closed',
    SERVICE_REOPENED: 'service.reopened',
    SERVICE_COMMENT_ADDED: 'service.comment.added',
    SERVICE_ATTACHMENT_ADDED: 'service.attachment.added',
    SERVICE_STATUS_CHANGED: 'service.status.changed',
    SERVICE_PRIORITY_CHANGED: 'service.priority.changed',
    SERVICE_OVERDUE: 'service.overdue',
    
    // 用户事件
    USER_LOGIN: 'user.login',
    USER_LOGOUT: 'user.logout',
    USER_CREATED: 'user.created',
    USER_UPDATED: 'user.updated',
    USER_ACTIVATED: 'user.activated',
    USER_DEACTIVATED: 'user.deactivated',
    USER_PASSWORD_CHANGED: 'user.password.changed',
    USER_ROLE_CHANGED: 'user.role.changed',
    
    // 客户事件
    CUSTOMER_CREATED: 'customer.created',
    CUSTOMER_UPDATED: 'customer.updated',
    CUSTOMER_ACTIVATED: 'customer.activated',
    CUSTOMER_DEACTIVATED: 'customer.deactivated',
    
    // 项目档案事件
    ARCHIVE_CREATED: 'archive.created',
    ARCHIVE_UPDATED: 'archive.updated',
    ARCHIVE_STATUS_CHANGED: 'archive.status.changed',
    
    // 系统事件
    SYSTEM_STARTUP: 'system.startup',
    SYSTEM_SHUTDOWN: 'system.shutdown',
    SYSTEM_ERROR: 'system.error',
    SYSTEM_ALERT: 'system.alert',
    SYSTEM_BACKUP_COMPLETED: 'system.backup.completed',
    SYSTEM_BACKUP_FAILED: 'system.backup.failed',
    
    // SLA事件
    SLA_VIOLATION: 'sla.violation',
    SLA_WARNING: 'sla.warning',
    SLA_RECOVERED: 'sla.recovered',
    
    // 工作流事件
    WORKFLOW_STARTED: 'workflow.started',
    WORKFLOW_COMPLETED: 'workflow.completed',
    WORKFLOW_FAILED: 'workflow.failed',
    WORKFLOW_PAUSED: 'workflow.paused',
    WORKFLOW_RESUMED: 'workflow.resumed',
    
    // 自定义事件
    CUSTOM_EVENT: 'custom.event'
  } as const

  private constructor() {
    super()
    this.setMaxListeners(200)
    this.initializeService()
  }

  public static getInstance(): EventTriggerService {
    if (!EventTriggerService.instance) {
      EventTriggerService.instance = new EventTriggerService()
    }
    return EventTriggerService.instance
  }

  /**
   * 初始化事件触发器服务
   */
  private async initializeService() {
    console.log('📡 事件触发器服务初始化中...')
    
    // 设置内置事件过滤器
    this.setupBuiltinFilters()
    
    // 集成数据库变更监听
    this.integrateDatabaseListeners()
    
    // 集成实时服务
    this.integrateRealtimeService()
    
    // 启动事件缓冲区清理
    this.startEventBufferCleanup()
    
    console.log('✅ 事件触发器服务初始化完成')
  }

  /**
   * 设置内置过滤器
   */
  private setupBuiltinFilters() {
    // 高优先级服务过滤器
    this.eventFilters.set('high-priority-service', (data: any) => {
      return data.priority === 'HIGH' || data.priority === 'URGENT'
    })

    // 逾期服务过滤器
    this.eventFilters.set('overdue-service', (data: any) => {
      return data.isOverdue === true || (data.dueDate && new Date(data.dueDate) < new Date())
    })

    // VIP客户过滤器
    this.eventFilters.set('vip-customer', (data: any) => {
      return data.customer?.level === 'VIP' || data.customer?.priority === 'HIGH'
    })

    // 工作时间过滤器
    this.eventFilters.set('business-hours', (data: any) => {
      const now = new Date()
      const hour = now.getHours()
      const day = now.getDay()
      return day >= 1 && day <= 5 && hour >= 9 && hour < 18
    })

    // 非工作时间过滤器
    this.eventFilters.set('non-business-hours', (data: any) => {
      const now = new Date()
      const hour = now.getHours()
      const day = now.getDay()
      return day === 0 || day === 6 || hour < 9 || hour >= 18
    })

    console.log('🔍 内置事件过滤器已设置')
  }

  /**
   * 集成数据库变更监听
   */
  private integrateDatabaseListeners() {
    // 注意：这里使用轮询方式检测变更，实际项目中可以考虑使用数据库触发器
    this.startDatabasePolling()
  }

  /**
   * 集成实时服务
   */
  private integrateRealtimeService() {
    try {
      const realtimeService = RealtimeService.getInstance()
      
      // 监听用户连接状态变化
      // 注意：这里假设RealtimeService有相应的事件，需要根据实际实现调整
    } catch (error) {
      console.warn('无法集成实时服务:', error)
    }
  }

  /**
   * 启动数据库轮询
   */
  private startDatabasePolling() {
    // 每30秒检查一次数据变更
    setInterval(async () => {
      await this.checkDatabaseChanges()
    }, 30000)
  }

  /**
   * 检查数据库变更
   */
  private async checkDatabaseChanges() {
    try {
      // 检查新创建的服务工单
      await this.checkNewServices()
      
      // 检查状态变更的服务
      await this.checkServiceStatusChanges()
      
      // 检查逾期的服务
      await this.checkOverdueServices()
      
    } catch (error) {
      console.error('数据库变更检查失败:', error)
    }
  }

  /**
   * 检查新创建的服务工单
   */
  private async checkNewServices() {
    const lastCheckKey = 'event-trigger:last-service-check'
    const lastCheck = await CacheService.get(lastCheckKey)
    const lastCheckTime = lastCheck ? new Date(lastCheck as string) : new Date(Date.now() - 60000)

    const newServices = await prisma.service.findMany({
      where: {
        createdAt: {
          gt: lastCheckTime
        }
      },
      include: {
        archive: {
          include: {
            customer: true
          }
        },
        assignedUser: true
      }
    })

    for (const service of newServices) {
      await this.emitServiceEvent(EventTriggerService.EVENT_TYPES.SERVICE_CREATED, {
        service,
        serviceId: service.id,
        customerId: service.archive?.customer?.id,
        priority: service.priority,
        category: service.category,
        timestamp: service.createdAt
      })
    }

    if (newServices.length > 0) {
      console.log(`📝 发现 ${newServices.length} 个新服务工单`)
    }

    // 更新最后检查时间
    await CacheService.set(lastCheckKey, new Date().toISOString())
  }

  /**
   * 检查服务状态变更
   */
  private async checkServiceStatusChanges() {
    const lastCheckKey = 'event-trigger:last-status-check'
    const lastCheck = await CacheService.get(lastCheckKey)
    const lastCheckTime = lastCheck ? new Date(lastCheck as string) : new Date(Date.now() - 60000)

    const updatedServices = await prisma.service.findMany({
      where: {
        updatedAt: {
          gt: lastCheckTime
        }
      },
      include: {
        archive: {
          include: {
            customer: true
          }
        },
        assignedUser: true
      }
    })

    for (const service of updatedServices) {
      // 检查缓存中的旧状态
      const oldStatusKey = `service-status:${service.id}`
      const oldStatus = await CacheService.get(oldStatusKey)

      if (oldStatus && oldStatus !== service.status) {
        await this.emitServiceEvent(EventTriggerService.EVENT_TYPES.SERVICE_STATUS_CHANGED, {
          service,
          serviceId: service.id,
          oldStatus,
          newStatus: service.status,
          customerId: service.archive?.customer?.id,
          assignedUserId: service.assignedTo,
          timestamp: service.updatedAt
        })

        // 根据新状态触发特定事件
        switch (service.status) {
          case 'IN_PROGRESS':
            await this.emitServiceEvent(EventTriggerService.EVENT_TYPES.SERVICE_ASSIGNED, {
              service,
              serviceId: service.id,
              assignedUserId: service.assignedTo,
              timestamp: service.updatedAt
            })
            break
          case 'RESOLVED':
            await this.emitServiceEvent(EventTriggerService.EVENT_TYPES.SERVICE_COMPLETED, {
              service,
              serviceId: service.id,
              resolvedBy: service.assignedTo,
              timestamp: service.updatedAt
            })
            break
          case 'CLOSED':
            await this.emitServiceEvent(EventTriggerService.EVENT_TYPES.SERVICE_CLOSED, {
              service,
              serviceId: service.id,
              closedBy: service.assignedTo,
              timestamp: service.updatedAt
            })
            break
        }
      }

      // 更新缓存中的状态
      await CacheService.setex(oldStatusKey, 86400, service.status) // 24小时过期
    }

    // 更新最后检查时间
    await CacheService.set(lastCheckKey, new Date().toISOString())
  }

  /**
   * 检查逾期服务
   */
  private async checkOverdueServices() {
    const now = new Date()
    
    const overdueServices = await prisma.service.findMany({
      where: {
        status: {
          in: ['OPEN', 'IN_PROGRESS', 'PENDING']
        },
        endTime: {
          lt: now
        }
      },
      include: {
        archive: {
          include: {
            customer: true
          }
        },
        assignedUser: true
      }
    })

    for (const service of overdueServices) {
      // 检查是否已经发送过逾期通知
      const overdueNotifiedKey = `overdue-notified:${service.id}`
      const alreadyNotified = await CacheService.get(overdueNotifiedKey)

      if (!alreadyNotified) {
        await this.emitServiceEvent(EventTriggerService.EVENT_TYPES.SERVICE_OVERDUE, {
          service,
          serviceId: service.id,
          customerId: service.archive?.customer?.id,
          assignedUserId: service.assignedTo,
          overdueDuration: now.getTime() - service.endTime!.getTime(),
          timestamp: now
        })

        // 标记已通知，24小时后可以再次通知
        await CacheService.setex(overdueNotifiedKey, 86400, 'true')
      }
    }

    if (overdueServices.length > 0) {
      console.log(`⏰ 发现 ${overdueServices.length} 个逾期服务工单`)
    }
  }

  /**
   * 发送服务相关事件
   */
  private async emitServiceEvent(eventType: string, data: any) {
    const event = {
      id: `${eventType}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      type: eventType,
      source: 'event-trigger-service',
      timestamp: new Date(),
      data
    }

    // 添加到事件缓冲区
    if (!this.eventBuffer.has(eventType)) {
      this.eventBuffer.set(eventType, [])
    }
    this.eventBuffer.get(eventType)!.push(event)

    // 发送事件
    this.emit(eventType, data)
    this.emit('event:triggered', event)

    console.log(`📡 事件已触发: ${eventType} - ${data.serviceId || 'N/A'}`)
  }

  /**
   * 启动事件缓冲区清理
   */
  private startEventBufferCleanup() {
    // 每小时清理一次事件缓冲区
    setInterval(() => {
      this.cleanupEventBuffer()
    }, 3600000)
  }

  /**
   * 清理事件缓冲区
   */
  private cleanupEventBuffer() {
    const cutoffTime = Date.now() - (24 * 60 * 60 * 1000) // 24小时前

    for (const [eventType, events] of this.eventBuffer.entries()) {
      const validEvents = events.filter(event => 
        new Date(event.timestamp).getTime() > cutoffTime
      )
      
      if (validEvents.length === 0) {
        this.eventBuffer.delete(eventType)
      } else {
        this.eventBuffer.set(eventType, validEvents)
      }
    }

    console.log('🧹 事件缓冲区清理完成')
  }

  /**
   * 公共API方法
   */

  /**
   * 手动触发事件
   */
  public async triggerEvent(eventType: string, data: any, source: string = 'manual'): Promise<void> {
    const event = {
      id: `${eventType}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      type: eventType,
      source,
      timestamp: new Date(),
      data
    }

    // 添加到事件缓冲区
    if (!this.eventBuffer.has(eventType)) {
      this.eventBuffer.set(eventType, [])
    }
    this.eventBuffer.get(eventType)!.push(event)

    // 发送事件
    this.emit(eventType, data)
    this.emit('event:triggered', event)

    console.log(`📡 手动触发事件: ${eventType}`)
  }

  /**
   * 注册自定义事件过滤器
   */
  public registerEventFilter(name: string, filter: (data: any) => boolean): void {
    this.eventFilters.set(name, filter)
    console.log(`🔍 注册事件过滤器: ${name}`)
  }

  /**
   * 应用事件过滤器
   */
  public applyFilter(filterName: string, data: any): boolean {
    const filter = this.eventFilters.get(filterName)
    if (!filter) {
      console.warn(`过滤器不存在: ${filterName}`)
      return true
    }
    
    try {
      return filter(data)
    } catch (error) {
      console.error(`过滤器执行失败 ${filterName}:`, error)
      return false
    }
  }

  /**
   * 获取事件统计
   */
  public getEventStatistics(timeRange?: { start: Date, end: Date }) {
    const stats: Record<string, number> = {}
    
    for (const [eventType, events] of this.eventBuffer.entries()) {
      let count = events.length
      
      if (timeRange) {
        count = events.filter(event => {
          const eventTime = new Date(event.timestamp)
          return eventTime >= timeRange.start && eventTime <= timeRange.end
        }).length
      }
      
      stats[eventType] = count
    }

    return stats
  }

  /**
   * 获取最近的事件
   */
  public getRecentEvents(eventType?: string, limit: number = 100) {
    if (eventType) {
      const events = this.eventBuffer.get(eventType) || []
      return events
        .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
        .slice(0, limit)
    }

    // 获取所有类型的最近事件
    const allEvents = []
    for (const events of this.eventBuffer.values()) {
      allEvents.push(...events)
    }

    return allEvents
      .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
      .slice(0, limit)
  }

  /**
   * 清除事件缓冲区
   */
  public clearEventBuffer(eventType?: string): void {
    if (eventType) {
      this.eventBuffer.delete(eventType)
    } else {
      this.eventBuffer.clear()
    }
    
    console.log(`🧹 事件缓冲区已清除: ${eventType || 'ALL'}`)
  }

  /**
   * 获取支持的事件类型
   */
  public getSupportedEventTypes(): string[] {
    return Object.values(EventTriggerService.EVENT_TYPES)
  }

  /**
   * 检查事件类型是否支持
   */
  public isEventTypeSupported(eventType: string): boolean {
    return Object.values(EventTriggerService.EVENT_TYPES).includes(eventType as any)
  }

  /**
   * 处理用户登录事件
   */
  public async handleUserLogin(userId: string, userInfo: any): Promise<void> {
    await this.triggerEvent(EventTriggerService.EVENT_TYPES.USER_LOGIN, {
      userId,
      user: userInfo,
      loginTime: new Date(),
      ipAddress: userInfo.ipAddress,
      userAgent: userInfo.userAgent
    }, 'auth-service')
  }

  /**
   * 处理用户登出事件
   */
  public async handleUserLogout(userId: string, userInfo: any): Promise<void> {
    await this.triggerEvent(EventTriggerService.EVENT_TYPES.USER_LOGOUT, {
      userId,
      user: userInfo,
      logoutTime: new Date(),
      sessionDuration: userInfo.sessionDuration
    }, 'auth-service')
  }

  /**
   * 处理系统告警事件
   */
  public async handleSystemAlert(alertData: any): Promise<void> {
    await this.triggerEvent(EventTriggerService.EVENT_TYPES.SYSTEM_ALERT, {
      alert: alertData,
      severity: alertData.severity,
      component: alertData.component,
      message: alertData.message,
      timestamp: new Date()
    }, 'alert-engine')
  }

  /**
   * 处理SLA违规事件
   */
  public async handleSLAViolation(violationData: any): Promise<void> {
    await this.triggerEvent(EventTriggerService.EVENT_TYPES.SLA_VIOLATION, {
      violation: violationData,
      serviceId: violationData.serviceId,
      violationType: violationData.type,
      severity: violationData.severity,
      expectedTime: violationData.expectedTime,
      actualTime: violationData.actualTime,
      timestamp: new Date()
    }, 'sla-monitor')
  }

  /**
   * 停止事件触发器服务
   */
  public async stop(): Promise<void> {
    // 移除所有监听器
    this.removeAllListeners()
    
    // 清理事件缓冲区
    this.eventBuffer.clear()
    
    // 清理过滤器
    this.eventFilters.clear()
    
    console.log('⏹️ 事件触发器服务已停止')
  }
}

// 导出单例实例
export const eventTrigger = EventTriggerService.getInstance()