/**
 * ActionExecutor 管理器
 * 负责管理所有动作执行器的注册、协调和生命周期
 */

import { EventEmitter } from 'events';
import type { WorkflowStepType } from '@prisma/client';
import type {
  IActionExecutor,
  IActionExecutorManager,
  WorkflowStep,
  ExecutionContext,
  ActionExecutionResult,
  ValidationResult,
  RollbackResult,
  ExecutionError,
  ErrorType,
  ErrorSeverity,
  ExecutionLog,
  ExecutionMetrics,
  ResourceUsage
} from '@/types/action-executor.types';

/**
 * ActionExecutor 管理器实现
 */
export class ActionExecutorManager extends EventEmitter implements IActionExecutorManager {
  private static instance: ActionExecutorManager;
  private executors = new Map<WorkflowStepType, IActionExecutor>();
  private isInitialized = false;
  private metrics = new Map<WorkflowStepType, ExecutionMetrics>();

  constructor() {
    super();
    this.setupEventListeners();
  }

  /**
   * 获取单例实例
   */
  static getInstance(): ActionExecutorManager {
    if (!ActionExecutorManager.instance) {
      ActionExecutorManager.instance = new ActionExecutorManager();
    }
    return ActionExecutorManager.instance;
  }

  /**
   * 初始化管理器
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    try {
      console.log('🚀 初始化 ActionExecutorManager...');

      // 初始化所有注册的执行器
      const initPromises = Array.from(this.executors.values()).map(async (executor) => {
        try {
          if (executor.initialize) {
            await executor.initialize();
          }
          console.log(`✅ 执行器 ${executor.name} 初始化成功`);
        } catch (error) {
          console.error(`❌ 执行器 ${executor.name} 初始化失败:`, error);
          throw error;
        }
      });

      await Promise.all(initPromises);
      this.isInitialized = true;

      console.log(`🎉 ActionExecutorManager 初始化完成，共注册 ${this.executors.size} 个执行器`);
      this.emit('initialized', { executorCount: this.executors.size });

    } catch (error) {
      console.error('❌ ActionExecutorManager 初始化失败:', error);
      this.emit('initialization:failed', { error });
      throw error;
    }
  }

  /**
   * 注册执行器
   */
  registerExecutor(executor: IActionExecutor): void {
    if (this.executors.has(executor.type)) {
      console.warn(`⚠️ 执行器类型 ${executor.type} 已存在，将被覆盖`);
    }

    this.executors.set(executor.type, executor);
    this.initializeMetrics(executor.type);

    console.log(`📦 注册执行器: ${executor.name} (${executor.type}) v${executor.version}`);
    this.emit('executor:registered', { 
      type: executor.type, 
      name: executor.name, 
      version: executor.version 
    });
  }

  /**
   * 取消注册执行器
   */
  unregisterExecutor(type: WorkflowStepType): void {
    const executor = this.executors.get(type);
    if (!executor) {
      console.warn(`⚠️ 执行器类型 ${type} 不存在`);
      return;
    }

    // 清理资源
    if (executor.cleanup) {
      executor.cleanup().catch(error => {
        console.error(`❌ 执行器 ${executor.name} 清理资源失败:`, error);
      });
    }

    this.executors.delete(type);
    this.metrics.delete(type);

    console.log(`🗑️ 取消注册执行器: ${executor.name} (${type})`);
    this.emit('executor:unregistered', { type, name: executor.name });
  }

  /**
   * 获取执行器
   */
  getExecutor(type: WorkflowStepType): IActionExecutor | undefined {
    return this.executors.get(type);
  }

  /**
   * 执行步骤
   */
  async executeStep(step: WorkflowStep, context: ExecutionContext): Promise<ActionExecutionResult> {
    const startTime = Date.now();
    
    try {
      // 验证执行器是否存在
      const executor = this.executors.get(step.type);
      if (!executor) {
        const error: ExecutionError = {
          type: ErrorType.SYSTEM_ERROR,
          code: 'EXECUTOR_NOT_FOUND',
          message: `不支持的步骤类型: ${step.type}`,
          recoverable: false,
          severity: ErrorSeverity.HIGH,
          timestamp: new Date()
        };
        return { success: false, error, logs: [] };
      }

      // 验证步骤配置
      const validation = await this.validateStep(step);
      if (!validation.valid) {
        const error: ExecutionError = {
          type: ErrorType.VALIDATION_ERROR,
          code: 'STEP_VALIDATION_FAILED',
          message: `步骤验证失败: ${validation.errors.join(', ')}`,
          details: { errors: validation.errors, warnings: validation.warnings },
          recoverable: false,
          severity: ErrorSeverity.MEDIUM,
          timestamp: new Date()
        };
        return { success: false, error, logs: [] };
      }

      // 发出执行开始事件
      this.emit('step:started', {
        executionId: context.executionId,
        stepIndex: step.index,
        stepType: step.type,
        stepName: step.name
      });

      // 记录开始日志
      const startLog: ExecutionLog = {
        level: 'INFO',
        message: `开始执行步骤: ${step.name} (${step.type})`,
        timestamp: new Date(),
        source: 'ActionExecutorManager'
      };
      context.logger.info(startLog.message);

      // 执行步骤
      let result = await executor.execute(step, context);
      let retryCount = 0;
      const maxRetries = step.retry?.maxAttempts || 0;

      // 重试逻辑
      while (!result.success && retryCount < maxRetries) {
        retryCount++;
        const delay = this.calculateRetryDelay(step.retry, retryCount);
        
        const retryLog: ExecutionLog = {
          level: 'WARN',
          message: `第 ${retryCount} 次重试执行步骤 ${step.name}，延迟 ${delay}ms`,
          timestamp: new Date(),
          metadata: { retryCount, delay },
          source: 'ActionExecutorManager'
        };
        result.logs.push(retryLog);
        context.logger.warn(retryLog.message);

        await this.sleep(delay);
        result = await executor.execute(step, context);
      }

      // 计算执行时间
      const executionTime = (Date.now() - startTime) / 1000;
      result.executionTime = executionTime;

      // 记录指标
      this.recordMetrics(step.type, result, executionTime);

      // 发出执行完成事件
      this.emit('step:completed', {
        executionId: context.executionId,
        stepIndex: step.index,
        stepType: step.type,
        stepName: step.name,
        success: result.success,
        retryCount,
        executionTime
      });

      // 记录完成日志
      const completeLog: ExecutionLog = {
        level: result.success ? 'INFO' : 'ERROR',
        message: `步骤执行${result.success ? '成功' : '失败'}: ${step.name}`,
        timestamp: new Date(),
        metadata: { 
          executionTime, 
          retryCount,
          success: result.success
        },
        source: 'ActionExecutorManager'
      };
      result.logs.push(completeLog);
      
      if (result.success) {
        context.logger.info(completeLog.message);
      } else {
        context.logger.error(completeLog.message, result.error);
      }

      return result;

    } catch (error: any) {
      const executionTime = (Date.now() - startTime) / 1000;
      
      const executionError: ExecutionError = {
        type: ErrorType.EXECUTION_ERROR,
        code: 'STEP_EXECUTION_ERROR',
        message: error.message || '步骤执行过程中发生未知错误',
        details: { originalError: error },
        recoverable: true,
        severity: ErrorSeverity.HIGH,
        stack: error.stack,
        timestamp: new Date()
      };

      const errorLog: ExecutionLog = {
        level: 'ERROR',
        message: `步骤执行异常: ${step.name} - ${error.message}`,
        timestamp: new Date(),
        metadata: { error: error.message, stack: error.stack },
        source: 'ActionExecutorManager'
      };

      // 发出执行失败事件
      this.emit('step:failed', {
        executionId: context.executionId,
        stepIndex: step.index,
        stepType: step.type,
        stepName: step.name,
        error: executionError,
        executionTime
      });

      context.logger.error(errorLog.message, error);

      return {
        success: false,
        error: executionError,
        logs: [errorLog],
        executionTime
      };
    }
  }

  /**
   * 验证步骤
   */
  async validateStep(step: WorkflowStep): Promise<ValidationResult> {
    try {
      const executor = this.executors.get(step.type);
      if (!executor) {
        return {
          valid: false,
          errors: [`不支持的步骤类型: ${step.type}`],
          warnings: []
        };
      }

      return await executor.validate(step);

    } catch (error: any) {
      return {
        valid: false,
        errors: [`步骤验证异常: ${error.message}`],
        warnings: []
      };
    }
  }

  /**
   * 回滚步骤
   */
  async rollbackStep(step: WorkflowStep, context: ExecutionContext): Promise<RollbackResult> {
    try {
      const executor = this.executors.get(step.type);
      if (!executor) {
        const error: ExecutionError = {
          type: ErrorType.SYSTEM_ERROR,
          code: 'EXECUTOR_NOT_FOUND',
          message: `无法回滚，执行器不存在: ${step.type}`,
          recoverable: false,
          severity: ErrorSeverity.HIGH,
          timestamp: new Date()
        };
        return { success: false, error, logs: [] };
      }

      if (!executor.rollback) {
        const log: ExecutionLog = {
          level: 'WARN',
          message: `执行器 ${executor.name} 不支持回滚操作`,
          timestamp: new Date(),
          source: 'ActionExecutorManager'
        };
        return { success: true, logs: [log] };
      }

      this.emit('step:rollback:started', {
        executionId: context.executionId,
        stepIndex: step.index,
        stepType: step.type
      });

      const result = await executor.rollback(step, context);

      this.emit('step:rollback:completed', {
        executionId: context.executionId,
        stepIndex: step.index,
        stepType: step.type,
        success: result.success
      });

      return result;

    } catch (error: any) {
      const executionError: ExecutionError = {
        type: ErrorType.EXECUTION_ERROR,
        code: 'ROLLBACK_ERROR',
        message: error.message || '回滚过程中发生未知错误',
        recoverable: false,
        severity: ErrorSeverity.HIGH,
        timestamp: new Date()
      };

      return {
        success: false,
        error: executionError,
        logs: [{
          level: 'ERROR',
          message: `回滚异常: ${error.message}`,
          timestamp: new Date(),
          source: 'ActionExecutorManager'
        }]
      };
    }
  }

  /**
   * 列出所有执行器
   */
  listExecutors(): IActionExecutor[] {
    return Array.from(this.executors.values());
  }

  /**
   * 健康检查
   */
  async healthCheck(): Promise<Map<WorkflowStepType, boolean>> {
    const healthStatus = new Map<WorkflowStepType, boolean>();

    const healthCheckPromises = Array.from(this.executors.entries()).map(async ([type, executor]) => {
      try {
        const isHealthy = executor.healthCheck ? await executor.healthCheck() : true;
        healthStatus.set(type, isHealthy);
      } catch (error) {
        console.error(`执行器 ${executor.name} 健康检查失败:`, error);
        healthStatus.set(type, false);
      }
    });

    await Promise.all(healthCheckPromises);
    return healthStatus;
  }

  /**
   * 获取系统统计信息
   */
  getSystemStats(): any {
    const stats = {
      totalExecutors: this.executors.size,
      isInitialized: this.isInitialized,
      executors: Array.from(this.executors.entries()).map(([type, executor]) => ({
        type,
        name: executor.name,
        version: executor.version
      })),
      metrics: Object.fromEntries(this.metrics)
    };

    return stats;
  }

  /**
   * 获取执行器指标
   */
  getExecutorMetrics(type?: WorkflowStepType): ExecutionMetrics | Map<WorkflowStepType, ExecutionMetrics> {
    if (type) {
      return this.metrics.get(type) || this.createEmptyMetrics();
    }
    return new Map(this.metrics);
  }

  /**
   * 清理资源
   */
  async cleanup(): Promise<void> {
    console.log('🧹 开始清理 ActionExecutorManager...');

    const cleanupPromises = Array.from(this.executors.values()).map(async (executor) => {
      if (executor.cleanup) {
        try {
          await executor.cleanup();
          console.log(`✅ 执行器 ${executor.name} 清理完成`);
        } catch (error) {
          console.error(`❌ 执行器 ${executor.name} 清理失败:`, error);
        }
      }
    });

    await Promise.all(cleanupPromises);
    
    this.executors.clear();
    this.metrics.clear();
    this.isInitialized = false;

    console.log('✅ ActionExecutorManager 清理完成');
    this.emit('cleanup:completed');
  }

  // ========== 私有方法 ==========

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    // 全局错误处理
    this.on('error', (error) => {
      console.error('ActionExecutorManager 发生错误:', error);
    });

    // 性能监控
    this.on('step:completed', (event) => {
      if (event.executionTime > 30) { // 超过30秒的执行
        console.warn(`⚠️ 步骤执行时间过长: ${event.stepName} (${event.executionTime}s)`);
      }
    });
  }

  /**
   * 计算重试延迟
   */
  private calculateRetryDelay(retryConfig: any, attempt: number): number {
    if (!retryConfig) return 1000;

    const baseDelay = retryConfig.delay || 1000;
    const multiplier = retryConfig.backoffMultiplier || 1;
    const maxDelay = retryConfig.maxDelay || 30000;

    const delay = baseDelay * Math.pow(multiplier, attempt - 1);
    return Math.min(delay, maxDelay);
  }

  /**
   * 休眠
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 初始化指标
   */
  private initializeMetrics(type: WorkflowStepType): void {
    this.metrics.set(type, this.createEmptyMetrics());
  }

  /**
   * 创建空指标
   */
  private createEmptyMetrics(): ExecutionMetrics {
    return {
      totalExecutions: 0,
      successfulExecutions: 0,
      failedExecutions: 0,
      avgExecutionTime: 0,
      resourceUtilization: {
        cpu: 0,
        memory: 0,
        io: 0,
        network: 0,
        duration: 0
      },
      errorDistribution: new Map(),
      performanceMetrics: new Map()
    };
  }

  /**
   * 记录指标
   */
  private recordMetrics(type: WorkflowStepType, result: ActionExecutionResult, executionTime: number): void {
    const metrics = this.metrics.get(type);
    if (!metrics) return;

    metrics.totalExecutions++;
    
    if (result.success) {
      metrics.successfulExecutions++;
    } else {
      metrics.failedExecutions++;
      if (result.error) {
        const count = metrics.errorDistribution.get(result.error.type) || 0;
        metrics.errorDistribution.set(result.error.type, count + 1);
      }
    }

    // 更新平均执行时间
    metrics.avgExecutionTime = (
      (metrics.avgExecutionTime * (metrics.totalExecutions - 1)) + executionTime
    ) / metrics.totalExecutions;

    // 更新资源使用情况
    if (result.resourceUsage) {
      const usage = metrics.resourceUtilization;
      usage.cpu = (usage.cpu + (result.resourceUsage.cpu || 0)) / 2;
      usage.memory = (usage.memory + (result.resourceUsage.memory || 0)) / 2;
      usage.io = (usage.io + (result.resourceUsage.io || 0)) / 2;
      usage.network = (usage.network + (result.resourceUsage.network || 0)) / 2;
      usage.duration += result.resourceUsage.duration;
    }
  }
}

// 导出单例实例
export const actionExecutorManager = ActionExecutorManager.getInstance();