import { EventEmitter } from 'events'
import * as cron from 'node-cron'
import { prisma } from '@/config/database.config'
import { CacheService } from '@/config/redis.config'
import { workflowEngine } from '@/services/workflow-engine.service'
import { AlertEngineService } from '@/services/alert-engine.service'
import { RealtimeService } from '@/services/realtime.service'
import { eventTrigger } from '@/services/triggers/event-trigger.service'
import { cronTrigger } from '@/services/triggers/cron-trigger.service'
import { conditionalTrigger } from '@/services/triggers/conditional-trigger.service'
import { webhookTrigger } from '@/services/triggers/webhook-trigger.service'
import { 
  WorkflowTriggerType, 
  WorkflowDefinition,
  SystemMetrics
} from '@prisma/client'

/**
 * 触发器基础接口
 */
export interface BaseTrigger {
  id: string
  type: WorkflowTriggerType
  workflowId: string
  config: any
  enabled: boolean
  lastTriggered?: Date
  nextTrigger?: Date
  metadata?: any
}

/**
 * 事件触发器配置
 */
export interface EventTriggerConfig {
  eventTypes: string[]          // 监听的事件类型
  filters?: Record<string, any> // 事件过滤条件
  debounce?: number            // 防抖时间（毫秒）
  maxFrequency?: number        // 最大触发频率（次/小时）
}

/**
 * 定时触发器配置
 */
export interface CronTriggerConfig {
  schedule: string             // Cron表达式
  timezone?: string           // 时区设置
  enabled?: boolean           // 是否启用
  maxExecutions?: number      // 最大执行次数
  executionCount?: number     // 已执行次数
}

/**
 * 条件触发器配置
 */
export interface ConditionalTriggerConfig {
  conditions: TriggerCondition[] // 条件列表
  operator: 'AND' | 'OR'        // 条件关系
  checkInterval: number         // 检查间隔（秒）
  cooldownPeriod?: number       // 冷却期（秒）
}

/**
 * Webhook触发器配置
 */
export interface WebhookTriggerConfig {
  endpoint: string              // Webhook端点路径
  method: 'GET' | 'POST' | 'PUT' | 'DELETE'
  authentication?: {            // 认证设置
    type: 'token' | 'signature' | 'basic'
    config: Record<string, any>
  }
  filters?: Record<string, any> // 请求过滤条件
}

/**
 * 触发条件接口
 */
export interface TriggerCondition {
  field: string                 // 检查字段
  operator: 'eq' | 'ne' | 'gt' | 'gte' | 'lt' | 'lte' | 'contains' | 'exists'
  value: any                   // 比较值
  source: 'database' | 'api' | 'metric' | 'variable'
  query?: string               // 数据查询语句
}

/**
 * 触发事件接口
 */
export interface TriggerEvent {
  id: string
  type: string
  source: string
  timestamp: Date
  data: any
  userId?: string
  sessionId?: string
}

/**
 * 触发器执行结果
 */
export interface TriggerExecutionResult {
  success: boolean
  triggerId: string
  workflowId: string
  executionId?: string
  message: string
  timestamp: Date
  error?: string
}

/**
 * 触发器管理器 - 统一的触发器系统
 * 整合事件触发、定时触发、条件触发和Webhook触发
 */
export class TriggerManagerService extends EventEmitter {
  private static instance: TriggerManagerService
  private triggers: Map<string, BaseTrigger> = new Map()
  private cronJobs: Map<string, cron.ScheduledTask> = new Map()
  private eventListeners: Map<string, Function[]> = new Map()
  private conditionCheckIntervals: Map<string, NodeJS.Timeout> = new Map()
  private webhookEndpoints: Map<string, string> = new Map()
  
  // 触发器状态跟踪
  private triggerStats: Map<string, {
    executions: number
    lastExecution: Date
    avgExecutionTime: number
    successRate: number
    errors: string[]
  }> = new Map()

  // 防抖和限流
  private debounceTimers: Map<string, NodeJS.Timeout> = new Map()
  private rateLimits: Map<string, { count: number; window: Date }> = new Map()

  private constructor() {
    super()
    this.setMaxListeners(100) // 增加最大监听器数量
    this.initializeService()
  }

  public static getInstance(): TriggerManagerService {
    if (!TriggerManagerService.instance) {
      TriggerManagerService.instance = new TriggerManagerService()
    }
    return TriggerManagerService.instance
  }

  /**
   * 初始化触发器管理服务
   */
  private async initializeService() {
    console.log('⚡ 触发器管理器初始化中...')
    
    try {
      // 初始化各个触发器服务
      await this.initializeTriggerServices()
      
      // 加载现有触发器
      await this.loadExistingTriggers()
      
      // 初始化事件总线
      await this.initializeEventBus()
      
      // 启动系统监控
      await this.startSystemMonitoring()
      
      // 设置定期清理任务
      this.startMaintenanceTasks()
      
      console.log('✅ 触发器管理器初始化完成')
    } catch (error) {
      console.error('❌ 触发器管理器初始化失败:', error)
      throw error
    }
  }

  /**
   * 初始化各个触发器服务
   */
  private async initializeTriggerServices() {
    // 服务已经通过import自动初始化
    // 设置事件监听
    this.setupTriggerServiceListeners()
  }

  /**
   * 设置触发器服务监听器
   */
  private setupTriggerServiceListeners() {
    // 事件触发器监听
    eventTrigger.on('event:triggered', (event) => {
      this.emit('trigger:event', event)
    })

    // 条件触发器监听
    conditionalTrigger.on('condition:triggered', (data) => {
      this.emit('trigger:condition', data)
    })

    conditionalTrigger.on('condition:error', (data) => {
      this.emit('trigger:error', data)
    })

    // Webhook触发器监听
    webhookTrigger.on('webhook:triggered', (data) => {
      this.emit('trigger:webhook', data)
    })

    webhookTrigger.on('webhook:error', (data) => {
      this.emit('trigger:error', data)
    })

    console.log('🔗 触发器服务监听器已设置')
  }

  /**
   * 加载现有的工作流触发器
   */
  private async loadExistingTriggers() {
    try {
      const workflows = await prisma.workflowDefinition.findMany({
        where: { isActive: true },
        select: {
          id: true,
          name: true,
          triggerConfig: true
        }
      })

      for (const workflow of workflows) {
        if (workflow.triggerConfig) {
          await this.registerTrigger({
            id: `workflow-${workflow.id}`,
            type: (workflow.triggerConfig as any).type,
            workflowId: workflow.id,
            config: workflow.triggerConfig,
            enabled: true
          })
        }
      }

      console.log(`📝 加载了 ${workflows.length} 个工作流触发器`)
    } catch (error) {
      console.error('加载现有触发器失败:', error)
    }
  }

  /**
   * 初始化事件总线
   */
  private async initializeEventBus() {
    // 集成Alert Engine事件
    this.integrateWithAlertEngine()
    
    // 集成Realtime服务事件
    this.integrateWithRealtimeService()
    
    // 监听系统事件
    this.setupSystemEventListeners()
  }

  /**
   * 集成Alert Engine
   */
  private integrateWithAlertEngine() {
    const alertEngine = AlertEngineService.getInstance()
    
    // 监听告警事件
    this.on('system:alert', async (alertData) => {
      await this.handleSystemEvent({
        id: `alert-${Date.now()}`,
        type: 'alert.triggered',
        source: 'alert-engine',
        timestamp: new Date(),
        data: alertData
      })
    })

    // 监听SLA违规事件
    this.on('system:sla-violation', async (slaData) => {
      await this.handleSystemEvent({
        id: `sla-${Date.now()}`,
        type: 'sla.violation',
        source: 'sla-monitor',
        timestamp: new Date(),
        data: slaData
      })
    })
  }

  /**
   * 集成Realtime服务
   */
  private integrateWithRealtimeService() {
    const realtimeService = RealtimeService.getInstance()
    
    // 监听用户连接事件
    this.on('user:connected', async (userData) => {
      await this.handleSystemEvent({
        id: `user-connect-${Date.now()}`,
        type: 'user.connected',
        source: 'realtime-service',
        timestamp: new Date(),
        data: userData
      })
    })

    // 监听用户断开事件
    this.on('user:disconnected', async (userData) => {
      await this.handleSystemEvent({
        id: `user-disconnect-${Date.now()}`,
        type: 'user.disconnected',
        source: 'realtime-service',
        timestamp: new Date(),
        data: userData
      })
    })
  }

  /**
   * 设置系统事件监听器
   */
  private setupSystemEventListeners() {
    // 监听数据库变更事件（可以通过数据库触发器实现）
    this.on('database:changed', this.handleDatabaseEvent.bind(this))
    
    // 监听服务工单事件
    this.on('service:created', this.handleServiceEvent.bind(this))
    this.on('service:updated', this.handleServiceEvent.bind(this))
    this.on('service:assigned', this.handleServiceEvent.bind(this))
    this.on('service:completed', this.handleServiceEvent.bind(this))
    
    // 监听用户事件
    this.on('user:login', this.handleUserEvent.bind(this))
    this.on('user:logout', this.handleUserEvent.bind(this))
    
    // 监听系统性能事件
    this.on('system:metrics', this.handleMetricsEvent.bind(this))
  }

  /**
   * 注册触发器
   */
  async registerTrigger(trigger: BaseTrigger): Promise<void> {
    try {
      this.triggers.set(trigger.id, trigger)
      
      // 根据触发器类型进行初始化
      switch (trigger.type) {
        case 'SCHEDULED':
          await this.initializeCronTrigger(trigger)
          break
        case 'EVENT':
          await this.initializeEventTrigger(trigger)
          break
        case 'CONDITION':
          await this.initializeConditionalTrigger(trigger)
          break
        case 'WEBHOOK':
          await this.initializeWebhookTrigger(trigger)
          break
        default:
          console.warn(`未知的触发器类型: ${trigger.type}`)
      }

      // 初始化触发器统计
      this.triggerStats.set(trigger.id, {
        executions: 0,
        lastExecution: new Date(),
        avgExecutionTime: 0,
        successRate: 100,
        errors: []
      })

      console.log(`✅ 注册触发器: ${trigger.id} (${trigger.type})`)
    } catch (error) {
      console.error(`注册触发器失败 ${trigger.id}:`, error)
      throw error
    }
  }

  /**
   * 初始化定时触发器
   */
  private async initializeCronTrigger(trigger: BaseTrigger) {
    const config = trigger.config as CronTriggerConfig
    
    if (!cron.validate(config.schedule)) {
      throw new Error(`无效的Cron表达式: ${config.schedule}`)
    }

    const task = cron.schedule(config.schedule, async () => {
      if (trigger.enabled) {
        await this.executeTrigger(trigger.id, {
          source: 'cron',
          timestamp: new Date(),
          schedule: config.schedule
        })
      }
    }, { 
      timezone: config.timezone || 'Asia/Shanghai'
    })

    this.cronJobs.set(trigger.id, task)
    
    if (config.enabled !== false) {
      task.start()
    }
  }

  /**
   * 初始化事件触发器
   */
  private async initializeEventTrigger(trigger: BaseTrigger) {
    const config = trigger.config as EventTriggerConfig
    
    config.eventTypes.forEach(eventType => {
      if (!this.eventListeners.has(eventType)) {
        this.eventListeners.set(eventType, [])
      }
      
      const handler = async (eventData: any) => {
        if (trigger.enabled && this.shouldTriggerEvent(trigger, eventData)) {
          await this.executeTriggerWithDebounce(trigger.id, eventData)
        }
      }
      
      this.eventListeners.get(eventType)!.push(handler)
      this.on(eventType, handler)
    })
  }

  /**
   * 初始化条件触发器
   */
  private async initializeConditionalTrigger(trigger: BaseTrigger) {
    const config = trigger.config as ConditionalTriggerConfig
    
    const checkConditions = async () => {
      if (!trigger.enabled) return
      
      try {
        const conditionsMet = await this.evaluateConditions(config.conditions, config.operator)
        
        if (conditionsMet) {
          // 检查冷却期
          const lastTrigger = this.triggerStats.get(trigger.id)?.lastExecution
          if (lastTrigger && config.cooldownPeriod) {
            const timeSinceLastTrigger = Date.now() - lastTrigger.getTime()
            if (timeSinceLastTrigger < config.cooldownPeriod * 1000) {
              return // 在冷却期内
            }
          }
          
          await this.executeTrigger(trigger.id, {
            source: 'condition',
            timestamp: new Date(),
            conditions: config.conditions,
            operator: config.operator
          })
        }
      } catch (error) {
        console.error(`条件触发器检查失败 ${trigger.id}:`, error)
      }
    }
    
    const interval = setInterval(checkConditions, config.checkInterval * 1000)
    this.conditionCheckIntervals.set(trigger.id, interval)
    
    // 立即执行一次检查
    await checkConditions()
  }

  /**
   * 初始化Webhook触发器
   */
  private async initializeWebhookTrigger(trigger: BaseTrigger) {
    const config = trigger.config as WebhookTriggerConfig
    
    // 注册Webhook端点
    this.webhookEndpoints.set(trigger.id, config.endpoint)
    
    console.log(`🔗 Webhook触发器已注册: ${config.method} ${config.endpoint}`)
  }

  /**
   * 执行触发器（带防抖）
   */
  private async executeTriggerWithDebounce(triggerId: string, data: any) {
    const trigger = this.triggers.get(triggerId)
    if (!trigger) return

    const config = trigger.config as EventTriggerConfig
    const debounceTime = config.debounce || 0

    if (debounceTime > 0) {
      // 清除之前的防抖定时器
      const existingTimer = this.debounceTimers.get(triggerId)
      if (existingTimer) {
        clearTimeout(existingTimer)
      }

      // 设置新的防抖定时器
      const timer = setTimeout(async () => {
        await this.executeTrigger(triggerId, data)
        this.debounceTimers.delete(triggerId)
      }, debounceTime)

      this.debounceTimers.set(triggerId, timer)
    } else {
      await this.executeTrigger(triggerId, data)
    }
  }

  /**
   * 执行触发器
   */
  private async executeTrigger(triggerId: string, triggerData: any): Promise<TriggerExecutionResult> {
    const startTime = Date.now()
    const trigger = this.triggers.get(triggerId)
    
    if (!trigger) {
      return {
        success: false,
        triggerId,
        workflowId: '',
        message: '触发器不存在',
        timestamp: new Date(),
        error: 'Trigger not found'
      }
    }

    try {
      // 检查限流
      if (!this.checkRateLimit(triggerId, trigger.config)) {
        return {
          success: false,
          triggerId,
          workflowId: trigger.workflowId,
          message: '触发频率超限',
          timestamp: new Date(),
          error: 'Rate limit exceeded'
        }
      }

      // 触发工作流执行
      const executionId = await workflowEngine.triggerWorkflow(
        trigger.workflowId,
        trigger.type,
        triggerData,
        'trigger-manager'
      )

      // 更新触发器统计
      const stats = this.triggerStats.get(triggerId)!
      stats.executions += 1
      stats.lastExecution = new Date()
      stats.avgExecutionTime = (stats.avgExecutionTime + (Date.now() - startTime)) / 2
      
      // 发送成功事件
      this.emit('trigger:executed', {
        triggerId,
        workflowId: trigger.workflowId,
        executionId,
        success: true
      })

      const result: TriggerExecutionResult = {
        success: true,
        triggerId,
        workflowId: trigger.workflowId,
        executionId,
        message: '触发器执行成功',
        timestamp: new Date()
      }

      console.log(`⚡ 触发器执行成功: ${triggerId} -> ${executionId}`)
      return result

    } catch (error: any) {
      // 更新错误统计
      const stats = this.triggerStats.get(triggerId)!
      stats.errors.push(error.message)
      if (stats.errors.length > 10) {
        stats.errors = stats.errors.slice(-10) // 保留最近10个错误
      }
      
      // 重新计算成功率
      const totalExecutions = stats.executions + 1
      const successfulExecutions = Math.round(stats.successRate * stats.executions / 100)
      stats.successRate = (successfulExecutions / totalExecutions) * 100
      stats.executions = totalExecutions

      // 发送失败事件
      this.emit('trigger:failed', {
        triggerId,
        workflowId: trigger.workflowId,
        error: error.message,
        success: false
      })

      const result: TriggerExecutionResult = {
        success: false,
        triggerId,
        workflowId: trigger.workflowId,
        message: '触发器执行失败',
        timestamp: new Date(),
        error: error.message
      }

      console.error(`❌ 触发器执行失败 ${triggerId}:`, error.message)
      return result
    }
  }

  /**
   * 处理系统事件
   */
  private async handleSystemEvent(event: TriggerEvent) {
    this.emit(event.type, event.data)
  }

  /**
   * 处理数据库事件
   */
  private async handleDatabaseEvent(event: TriggerEvent) {
    console.log(`📊 数据库变更事件: ${event.type}`, event.data)
  }

  /**
   * 处理服务工单事件
   */
  private async handleServiceEvent(event: TriggerEvent) {
    console.log(`🎫 服务工单事件: ${event.type}`, event.data)
  }

  /**
   * 处理用户事件
   */
  private async handleUserEvent(event: TriggerEvent) {
    console.log(`👤 用户事件: ${event.type}`, event.data)
  }

  /**
   * 处理系统指标事件
   */
  private async handleMetricsEvent(event: TriggerEvent) {
    console.log(`📈 系统指标事件: `, event)
    const metrics = event?.data as SystemMetrics
    
    // 触发系统指标相关的工作流
    this.emit('system:metrics', metrics)
  }

  /**
   * 启动系统监控
   */
  private async startSystemMonitoring() {
    // 每分钟收集系统指标
    setInterval(async () => {
      try {
        const metrics = await this.collectSystemMetrics()
        this.emit('system:metrics', metrics)
      } catch (error) {
        console.error('收集系统指标失败:', error)
      }
    }, 60000)
  }

  /**
   * 收集系统指标
   */
  private async collectSystemMetrics(): Promise<SystemMetrics> {
    // 简化的系统指标收集
    return {
      id: `metrics-${Date.now()}`,
      tags: {},
      timestamp: new Date(),
      metricType: 'system',
      value: Math.random() * 100,
      unit: 'percentage'
    } as SystemMetrics
  }

  /**
   * 启动维护任务
   */
  private startMaintenanceTasks() {
    // 每小时清理过期数据
    setInterval(() => {
      this.cleanupExpiredData()
    }, 3600000)

    // 每天生成触发器统计报告
    setInterval(() => {
      this.generateDailyReport()
    }, 86400000)
  }

  /**
   * 清理过期数据
   */
  private cleanupExpiredData() {
    // 清理过期的防抖定时器
    for (const [triggerId, timer] of this.debounceTimers.entries()) {
      clearTimeout(timer)
    }
    this.debounceTimers.clear()

    // 清理过期的限流记录
    const now = Date.now()
    for (const [triggerId, rateLimit] of this.rateLimits.entries()) {
      if (now - rateLimit.window.getTime() > 3600000) { // 1小时
        this.rateLimits.delete(triggerId)
      }
    }

    console.log('🧹 触发器管理器清理完成')
  }

  /**
   * 生成每日报告
   */
  private generateDailyReport() {
    const report = {
      totalTriggers: this.triggers.size,
      activeTriggers: Array.from(this.triggers.values()).filter(t => t.enabled).length,
      totalExecutions: Array.from(this.triggerStats.values()).reduce((sum, stats) => sum + stats.executions, 0),
      avgSuccessRate: Array.from(this.triggerStats.values()).reduce((sum, stats) => sum + stats.successRate, 0) / this.triggerStats.size
    }

    console.log('📊 触发器管理器日报:', report)
    this.emit('trigger:daily-report', report)
  }

  /**
   * 其他辅助方法
   */
  
  private shouldTriggerEvent(trigger: BaseTrigger, eventData: any): boolean {
    const config = trigger.config as EventTriggerConfig
    
    // 应用过滤器
    if (config.filters) {
      for (const [key, value] of Object.entries(config.filters)) {
        if (eventData[key] !== value) {
          return false
        }
      }
    }
    
    return true
  }

  private checkRateLimit(triggerId: string, config: any): boolean {
    const maxFrequency = config.maxFrequency
    if (!maxFrequency) return true

    const now = new Date()
    const rateLimit = this.rateLimits.get(triggerId)

    if (!rateLimit) {
      this.rateLimits.set(triggerId, { count: 1, window: now })
      return true
    }

    // 检查是否在同一小时内
    const hoursSinceWindow = (now.getTime() - rateLimit.window.getTime()) / 3600000
    
    if (hoursSinceWindow >= 1) {
      // 重置计数器
      this.rateLimits.set(triggerId, { count: 1, window: now })
      return true
    }

    if (rateLimit.count >= maxFrequency) {
      return false // 超过限制
    }

    rateLimit.count++
    return true
  }

  private async evaluateConditions(conditions: TriggerCondition[], operator: 'AND' | 'OR'): Promise<boolean> {
    const results = await Promise.all(conditions.map(condition => this.evaluateCondition(condition)))
    
    return operator === 'AND' ? results.every(r => r) : results.some(r => r)
  }

  private async evaluateCondition(condition: TriggerCondition): Promise<boolean> {
    try {
      let actualValue: any

      switch (condition.source) {
        case 'database':
          if (condition.query) {
            // 执行数据库查询
            const result = await prisma.$queryRawUnsafe(condition.query)
            actualValue = Array.isArray(result) ? result[0]?.[condition.field] : result
          }
          break
        case 'api':
          // API调用获取数据
          if (condition.query) {
            const response = await fetch(condition.query)
            const data = await response.json() as any
            actualValue = data[condition.field]
          }
          break
        case 'metric':
          // 从系统指标获取
          const metrics = await this.collectSystemMetrics()
          actualValue = (metrics as any)[condition.field]
          break
        case 'variable':
          // 从缓存变量获取
          actualValue = await CacheService.get(condition.field)
          if (actualValue) {
            actualValue = JSON.parse(actualValue)
          }
          break
        default:
          return false
      }

      // 执行条件比较
      return this.compareValues(actualValue, condition.operator, condition.value)
    } catch (error) {
      console.error('条件评估失败:', error)
      return false
    }
  }

  private compareValues(actual: any, operator: string, expected: any): boolean {
    switch (operator) {
      case 'eq': return actual === expected
      case 'ne': return actual !== expected
      case 'gt': return actual > expected
      case 'gte': return actual >= expected
      case 'lt': return actual < expected
      case 'lte': return actual <= expected
      case 'contains': return String(actual).includes(String(expected))
      case 'exists': return actual !== null && actual !== undefined
      default: return false
    }
  }

  /**
   * 公共API方法
   */
  
  public async unregisterTrigger(triggerId: string): Promise<void> {
    const trigger = this.triggers.get(triggerId)
    if (!trigger) return

    // 清理资源
    switch (trigger.type) {
      case 'SCHEDULED':
        const cronJob = this.cronJobs.get(triggerId)
        if (cronJob) {
          cronJob.stop()
          cronJob.destroy()
          this.cronJobs.delete(triggerId)
        }
        break
      case 'CONDITION':
        const interval = this.conditionCheckIntervals.get(triggerId)
        if (interval) {
          clearInterval(interval)
          this.conditionCheckIntervals.delete(triggerId)
        }
        break
      case 'WEBHOOK':
        this.webhookEndpoints.delete(triggerId)
        break
    }

    this.triggers.delete(triggerId)
    this.triggerStats.delete(triggerId)
    
    console.log(`🗑️ 触发器已注销: ${triggerId}`)
  }

  public async enableTriggerById(triggerId: string): Promise<void> {
    const trigger = this.triggers.get(triggerId)
    if (trigger) {
      trigger.enabled = true
      console.log(`✅ 触发器已启用: ${triggerId}`)
    }
  }

  public async disableTriggerById(triggerId: string): Promise<void> {
    const trigger = this.triggers.get(triggerId)
    if (trigger) {
      trigger.enabled = false
      console.log(`⏸️ 触发器已禁用: ${triggerId}`)
    }
  }

  public getTriggerStats(triggerId?: string) {
    if (triggerId) {
      return this.triggerStats.get(triggerId)
    }
    return Object.fromEntries(this.triggerStats.entries())
  }

  public getAllTriggers(): BaseTrigger[] {
    return Array.from(this.triggers.values())
  }

  public getTrigger(triggerId: string): BaseTrigger | undefined {
    return this.triggers.get(triggerId)
  }

  public async handleWebhookTrigger(endpoint: string, data: any, method: string): Promise<TriggerExecutionResult | null> {
    // 查找匹配的Webhook触发器
    for (const [triggerId, trigger] of this.triggers.entries()) {
      if (trigger.type === 'WEBHOOK') {
        const config = trigger.config as WebhookTriggerConfig
        if (config.endpoint === endpoint && config.method === method) {
          return await this.executeTrigger(triggerId, {
            source: 'webhook',
            method,
            endpoint,
            data,
            timestamp: new Date()
          })
        }
      }
    }
    
    return null
  }

  /**
   * 发布系统事件 - 供其他服务调用
   */
  public async publishEvent(event: TriggerEvent): Promise<void> {
    this.emit(event.type, event.data)
  }

  /**
   * 获取各个触发器服务的统计信息
   */
  public async getTriggerServiceStats() {
    return {
      eventTrigger: {
        supportedEventTypes: eventTrigger.getSupportedEventTypes(),
        recentEvents: eventTrigger.getRecentEvents('', 10),
        statistics: eventTrigger.getEventStatistics()
      },
      cronTrigger: {
        statistics: cronTrigger.getJobStatistics(),
        allJobs: cronTrigger.getAllJobsInfo()
      },
      conditionalTrigger: {
        allTriggers: conditionalTrigger.getAllTriggers()
      },
      webhookTrigger: {
        allWebhooks: webhookTrigger.getAllWebhooks(),
        registeredEndpoints: webhookTrigger.getRegisteredEndpoints()
      }
    }
  }

  /**
   * 手动触发事件
   */
  public async triggerEvent(eventType: string, data: any, source: string = 'manual'): Promise<void> {
    await eventTrigger.triggerEvent(eventType, data, source)
  }

  /**
   * 处理Webhook请求
   */
  public async handleWebhookRequest(
    endpoint: string,
    method: string,
    headers: Record<string, string>,
    body: any,
    clientIP?: string
  ) {
    return await webhookTrigger.handleWebhookRequest(endpoint, method, headers, body, clientIP)
  }

  /**
   * 手动执行定时任务
   */
  public async runCronJobNow(jobId: string) {
    return await cronTrigger.runJobNow(jobId)
  }

  /**
   * 强制评估条件触发器
   */
  public async forceConditionEvaluation(triggerId: string) {
    return await conditionalTrigger.forceEvaluation(triggerId)
  }

  /**
   * 启用/禁用触发器
   */
  public async enableTrigger(triggerId: string, type: WorkflowTriggerType): Promise<void> {
    switch (type) {
      case 'SCHEDULED':
        await cronTrigger.startJob(triggerId)
        break
      case 'CONDITION':
        await conditionalTrigger.enableTrigger(triggerId)
        break
      case 'WEBHOOK':
        await webhookTrigger.enableWebhook(triggerId)
        break
      default:
        console.warn(`未知的触发器类型: ${type}`)
    }
  }

  public async disableTrigger(triggerId: string, type: WorkflowTriggerType): Promise<void> {
    switch (type) {
      case 'SCHEDULED':
        await cronTrigger.stopJob(triggerId)
        break
      case 'CONDITION':
        await conditionalTrigger.disableTrigger(triggerId)
        break
      case 'WEBHOOK':
        await webhookTrigger.disableWebhook(triggerId)
        break
      default:
        console.warn(`未知的触发器类型: ${type}`)
    }
  }

  /**
   * 停止所有触发器
   */
  public async stopAll(): Promise<void> {
    // 停止所有触发器服务
    await eventTrigger.stop()
    await cronTrigger.stopAll()
    await conditionalTrigger.stop()
    await webhookTrigger.stop()

    // 停止所有cron任务
    this.cronJobs.forEach((job, triggerId) => {
      job.stop()
      job.destroy()
    })
    this.cronJobs.clear()

    // 清理所有定时器
    this.conditionCheckIntervals.forEach(interval => clearInterval(interval))
    this.conditionCheckIntervals.clear()

    this.debounceTimers.forEach(timer => clearTimeout(timer))
    this.debounceTimers.clear()

    // 移除所有监听器
    this.removeAllListeners()

    console.log('⏹️ 所有触发器已停止')
  }
}

// 导出单例实例
export const triggerManager = TriggerManagerService.getInstance()