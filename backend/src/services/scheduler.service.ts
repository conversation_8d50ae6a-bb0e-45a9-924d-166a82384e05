import cron from 'node-cron'
import { prisma } from '@/config/database.config'
import { emailService } from '@/services/email.service'
import { smsService } from '@/services/sms.service'
import { UploadService } from '@/services/upload.service'
import { processSlaAlerts } from '@/utils/sla-monitor.util'
import { TaskExecution } from '@prisma/client'

export interface ScheduledTask {
  name: string
  schedule: string
  description: string
  lastRun?: Date
  nextRun?: Date
  isActive: boolean
  task: () => Promise<any> // Can return some output
}

export class SchedulerService {
  private static tasks: Map<string, cron.ScheduledTask> = new Map()
  private static taskConfigs: Map<string, ScheduledTask> = new Map()

  static init() {
    console.log('🕐 Initializing Scheduler Service...')
    const tasks: ScheduledTask[] = [
      // Task definitions remain the same
      {
        name: 'daily-report',
        schedule: '0 8 * * 1-5', // 工作日早上8点
        description: '发送每日报告',
        isActive: true,
        task: this.sendDailyReport
      },
      {
        name: 'weekly-summary',
        schedule: '0 9 * * 1', // 每周一上午9点
        description: '发送周报',
        isActive: true,
        task: this.sendWeeklySummary
      },
      {
        name: 'monthly-report',
        schedule: '0 10 1 * *', // 每月1号上午10点
        description: '发送月报',
        isActive: true,
        task: this.sendMonthlyReport
      },
      {
        name: 'sla-alert',
        schedule: '*/30 * * * *', // 每30分钟
        description: 'SLA超时预警',
        isActive: true,
        task: this.checkSlaAlerts
      },
      {
        name: 'overdue-tickets',
        schedule: '0 */4 * * *', // 每4小时
        description: '逾期工单提醒',
        isActive: true,
        task: this.notifyOverdueTickets
      },
      {
        name: 'cleanup-files',
        schedule: '0 2 * * 0', // 每周日凌晨2点
        description: '清理过期文件',
        isActive: true,
        task: this.cleanupOldFiles
      },
      {
        name: 'system-health',
        schedule: '*/15 * * * *', // 每15分钟
        description: '系统健康检查',
        isActive: true,
        task: this.systemHealthCheck
      },
      {
        name: 'backup-reminder',
        schedule: '0 18 * * 5', // 每周五下午6点
        description: '数据备份提醒',
        isActive: true,
        task: this.sendBackupReminder
      }
    ]

    tasks.forEach(taskConfig => {
      this.registerTask(taskConfig)
    })

    console.log(`✅ ${tasks.length} scheduled tasks registered.`)
  }

  static registerTask(taskConfig: ScheduledTask) {
    if (!taskConfig.isActive) {
      console.log(`⏸️  Task "${taskConfig.name}" is disabled.`)
      return
    }

    try {
      const loggedTask = this.withTaskLogging(taskConfig)
      const task = cron.schedule(taskConfig.schedule, loggedTask, { scheduled: false })

      this.tasks.set(taskConfig.name, task)
      this.taskConfigs.set(taskConfig.name, taskConfig)

      task.start()
      console.log(`📅 Task "${taskConfig.name}" has been scheduled with schedule: ${taskConfig.schedule}`)
    } catch (error) {
      console.error(`❌ Failed to register task "${taskConfig.name}":`, error)
    }
  }

  private static withTaskLogging(taskConfig: ScheduledTask): () => Promise<void> {
    return async () => {
      let execution: TaskExecution | null = null
      const startTime = Date.now()

      try {
        execution = await prisma.taskExecution.create({
          data: {
            taskName: taskConfig.name,
            status: 'RUNNING',
            startedAt: new Date(),
          },
        })

        console.log(`🔄 Executing task: ${taskConfig.name} (Execution ID: ${execution.id})`)
        const result = await taskConfig.task()
        const duration = Date.now() - startTime

        await prisma.taskExecution.update({
          where: { id: execution.id },
          data: {
            status: 'SUCCESS',
            endedAt: new Date(),
            duration,
            output: result ? JSON.stringify(result) : null,
          },
        })
        console.log(`✅ Task "${taskConfig.name}" executed successfully. Duration: ${duration}ms`)

      } catch (error: any) {
        const duration = Date.now() - startTime
        console.error(`❌ Task "${taskConfig.name}" failed:`, error)

        if (execution) {
          await prisma.taskExecution.update({
            where: { id: execution.id },
            data: {
              status: 'FAILED',
              endedAt: new Date(),
              duration,
              error: error.stack || error.message || String(error),
            },
          })
        }
      }
    }
  }

  static stopAll() {
    this.tasks.forEach((task, name) => {
      task.stop()
      console.log(`⏹️  Task "${name}" has been stopped.`)
    })
    this.tasks.clear()
    this.taskConfigs.clear()
  }

  static getTaskStatus() {
    return Array.from(this.taskConfigs.entries()).map(([name, config]) => ({
      name,
      description: config.description,
      schedule: config.schedule,
      isActive: config.isActive,
      isRunning: this.tasks.get(name)?.running ?? false,
    }))
  }

  static async runTaskNow(taskName: string): Promise<void> {
    const taskConfig = this.taskConfigs.get(taskName)

    if (!taskConfig) {
      throw new Error(`任务 "${taskName}" 未找到.`)
    }

    if (!taskConfig.isActive) {
      console.log(`⏸️  无法运行已禁用的任务 "${taskConfig.name}".`)
      throw new Error(`任务 "${taskName}" 已被禁用.`)
    }

    console.log(`▶️  手动触发任务: ${taskName}`)
    const loggedTask = this.withTaskLogging(taskConfig)
    // Do not wait for the task to complete, run it in the background
    loggedTask()
  }

  // ... (all original task methods like sendDailyReport remain unchanged) ...
  /**
   * 发送每日报告
   */
  private static async sendDailyReport() {
    const today = new Date()
    const yesterday = new Date(today)
    yesterday.setDate(yesterday.getDate() - 1)

    // 获取昨日数据
    const [
      newServices,
      closedServices,
      pendingServices,
      overdueServices
    ] = await Promise.all([
      prisma.service.count({
        where: {
          createdAt: {
            gte: yesterday,
            lt: today
          }
        }
      }),
      prisma.service.count({
        where: {
          status: 'CLOSED',
          updatedAt: {
            gte: yesterday,
            lt: today
          }
        }
      }),
      prisma.service.count({
        where: {
          status: {
            in: ['OPEN', 'IN_PROGRESS', 'PENDING']
          }
        }
      }),
      prisma.service.count({
        where: {
          status: {
            in: ['OPEN', 'IN_PROGRESS', 'PENDING']
          },
          createdAt: {
            lt: new Date(Date.now() - 24 * 60 * 60 * 1000)
          }
        }
      })
    ])

    // 获取管理员邮箱
    const admins = await prisma.user.findMany({
      where: {
        roles: {
          some: {
            role: {
              name: 'Super Admin'
            }
          }
        }
      },
      select: {
        email: true,
        name: true
      }
    })

    if (admins.length === 0) return

    const emailContent = `
      <h2>运维服务日报 - ${yesterday.toLocaleDateString()}</h2>
      <div style="font-family: Arial, sans-serif;">
        <h3>📊 统计数据</h3>
        <ul>
          <li><strong>新增工单:</strong> ${newServices} 个</li>
          <li><strong>已关闭工单:</strong> ${closedServices} 个</li>
          <li><strong>待处理工单:</strong> ${pendingServices} 个</li>
          <li><strong>逾期工单:</strong> ${overdueServices} 个</li>
        </ul>
        
        ${overdueServices > 0 ? `
        <div style="background-color: #ffebee; padding: 10px; border-radius: 5px; margin-top: 20px;">
          <h4 style="color: #c62828;">⚠️ 注意：有 ${overdueServices} 个工单已逾期，请及时处理！</h4>
        </div>
        ` : ''}
        
        <hr style="margin: 20px 0;">
        <p style="color: #666; font-size: 12px;">
          这是系统自动发送的邮件，请勿回复。
        </p>
      </div>
    `

    // 发送邮件给所有管理员
    for (const admin of admins) {
      if (admin.email) {
        try {
          await emailService.sendEmail({
            to: admin.email,
            subject: `运维服务日报 - ${yesterday.toLocaleDateString()}`,
            html: emailContent
          })
        } catch (error) {
          console.error(`发送日报邮件失败 (${admin.email}):`, error)
        }
      }
    }
  }

  /**
   * 发送周报
   */
  private static async sendWeeklySummary() {
    const today = new Date()
    const lastWeek = new Date(today)
    lastWeek.setDate(lastWeek.getDate() - 7)

    // 获取上周数据
    const [
      weeklyServices,
      weeklyCustomers,
      avgResponseTime,
      customerSatisfaction
    ] = await Promise.all([
      prisma.service.count({
        where: {
          createdAt: {
            gte: lastWeek,
            lt: today
          }
        }
      }),
      prisma.customer.count({
        where: {
          createdAt: {
            gte: lastWeek,
            lt: today
          }
        }
      }),
      prisma.service.aggregate({
        where: {
          actualResponseTime: { not: null },
          updatedAt: {
            gte: lastWeek,
            lt: today
          }
        },
        _avg: {
          actualResponseTime: true
        }
      }),
      // 这里可以添加客户满意度计算逻辑
      Promise.resolve(4.2) // 示例数据
    ])

    const emailContent = `
      <h2>运维服务周报 - ${lastWeek.toLocaleDateString()} 至 ${today.toLocaleDateString()}</h2>
      <div style="font-family: Arial, sans-serif;">
        <h3>📈 本周亮点</h3>
        <ul>
          <li><strong>服务工单:</strong> ${weeklyServices} 个</li>
          <li><strong>新增客户:</strong> ${weeklyCustomers} 个</li>
          <li><strong>平均响应时间:</strong> ${Math.round(avgResponseTime._avg.actualResponseTime || 0)} 分钟</li>
          <li><strong>客户满意度:</strong> ${customerSatisfaction}/5.0</li>
        </ul>
        
        <h3>🎯 下周重点</h3>
        <ul>
          <li>继续优化响应时间</li>
          <li>加强客户服务质量</li>
          <li>完善运维流程</li>
        </ul>
      </div>
    `

    // 发送给管理员
    const admins = await prisma.user.findMany({
       where: {
        roles: {
          some: {
            role: {
              name: 'Super Admin'
            }
          }
        }
      },
      select: { email: true }
    })

    for (const admin of admins) {
      if (admin.email) {
        await emailService.sendEmail({
          to: admin.email,
          subject: '运维服务周报',
          html: emailContent
        })
      }
    }
  }

  /**
   * 发送月报
   */
  private static async sendMonthlyReport() {
    const today = new Date()
    const lastMonth = new Date(today.getFullYear(), today.getMonth() - 1, 1)
    const thisMonth = new Date(today.getFullYear(), today.getMonth(), 1)

    // 获取上月数据
    const [
      monthlyServices,
      monthlyRevenue,
      topCustomers,
      slaCompliance
    ] = await Promise.all([
      prisma.service.count({
        where: {
          createdAt: {
            gte: lastMonth,
            lt: thisMonth
          }
        }
      }),
      // 这里可以添加收入计算逻辑
      Promise.resolve(0),
      prisma.service.groupBy({
        by: ['customerId'],
        where: {
          createdAt: {
            gte: lastMonth,
            lt: thisMonth
          }
        },
        _count: {
          id: true
        },
        orderBy: {
          _count: {
            id: 'desc'
          }
        },
        take: 5
      }),
      // SLA 合规性计算
      Promise.resolve(95.8) // 示例数据
    ])

    // 生成月报内容...
    console.log('生成月报:', {
      monthlyServices,
      monthlyRevenue,
      topCustomers: topCustomers.length,
      slaCompliance
    })
  }

  /**
   * SLA超时预警 - 使用新的SLA监控工具
   */
  private static async checkSlaAlerts() {
    try {
      await processSlaAlerts()
    } catch (error) {
      console.error('SLA警报检查任务失败:', error)
    }
  }

  /**
   * 逾期工单提醒
   */
  private static async notifyOverdueTickets() {
    const yesterday = new Date(Date.now() - 24 * 60 * 60 * 1000)
    
    const overdueServices = await prisma.service.findMany({
      where: {
        status: {
          in: ['OPEN', 'IN_PROGRESS', 'PENDING']
        },
        createdAt: {
          lt: yesterday
        }
      },
      include: {
        archive: {
          include: {
            customer: true
          }
        },
        assignedUser: true
      }
    })

    // 发送提醒通知
    for (const service of overdueServices) {
      if (service.assignedUser?.email) {
        await emailService.sendEmail({
          to: service.assignedUser.email,
          subject: `逾期工单提醒 - ${service.ticketNumber}`,
          html: `
            <h3>⏰ 工单逾期提醒</h3>
            <p>您有一个工单已逾期，请及时处理：</p>
            <p><strong>工单号:</strong> ${service.ticketNumber}</p>
            <p><strong>客户:</strong> ${service.archive?.customer?.name}</p>
            <p><strong>创建时间:</strong> ${service.createdAt.toLocaleString()}</p>
            <p><strong>当前状态:</strong> ${service.status}</p>
          `
        })
      }
    }

    if (overdueServices.length > 0) {
      console.log(`📬 发送了 ${overdueServices.length} 条逾期工单提醒`)
    }
  }

  /**
   * 清理过期文件
   */
  private static async cleanupOldFiles() {
    const result = await UploadService.cleanupOldFiles(30)
    console.log('🧹 文件清理完成:', result)
  }

  /**
   * 系统健康检查
   */
  private static async systemHealthCheck() {
    const checks = []

    try {
      // 数据库连接检查
      await prisma.$queryRaw`SELECT 1`
      checks.push({ name: 'Database', status: 'healthy' })
    } catch (error: any) {
      checks.push({ name: 'Database', status: 'error', error: error.message })
    }

    try {
      // 邮件服务检查
      const isConfigured = emailService.isConfigured()
      checks.push({ name: 'Email Service', status: isConfigured ? 'healthy' : 'not-configured' })
    } catch (error: any) {
      checks.push({ name: 'Email Service', status: 'error', error: error.message })
    }

    try {
      // 短信服务检查
      const isConfigured = smsService.isConfigured()
      checks.push({ name: 'SMS Service', status: isConfigured ? 'healthy' : 'not-configured' })
    } catch (error: any) {
      checks.push({ name: 'SMS Service', status: 'error', error: error.message })
    }

    // 检查是否有错误
    const errors = checks.filter(check => check.status === 'error')
    if (errors.length > 0) {
      console.warn('⚠️  系统健康检查发现问题:', errors)
      
      // 发送警告邮件给管理员
      const admins = await prisma.user.findMany({
        where: { roles: { some: { role: { name: 'Super Admin' } } } },
        select: { email: true }
      })

      for (const admin of admins) {
        if (admin.email) {
          await emailService.sendEmail({
            to: admin.email,
            subject: '系统健康检查警告',
            html: `
              <h3>⚠️ 系统健康检查发现问题</h3>
              <ul>
                ${errors.map(err => `<li><strong>${err.name}:</strong> ${err.error}</li>`).join('')}
              </ul>
              <p>请及时检查和修复相关问题。</p>
            `
          })
        }
      }
    }
  }

  /**
   * 数据备份提醒
   */
  private static async sendBackupReminder() {
    const admins = await prisma.user.findMany({
      where: { roles: { some: { role: { name: 'Super Admin' } } } },
      select: { email: true }
    })

    for (const admin of admins) {
      if (admin.email) {
        await emailService.sendEmail({
          to: admin.email,
          subject: '数据备份提醒',
          html: `
            <h3>💾 周末数据备份提醒</h3>
            <p>请记得在本周末进行数据备份操作，确保数据安全。</p>
            <p><strong>建议备份内容：</strong></p>
            <ul>
              <li>数据库完整备份</li>
              <li>上传文件备份</li>
              <li>配置文件备份</li>
              <li>日志文件归档</li>
            </ul>
          `
        })
      }
    }
  }
}
