import multer from 'multer'
import path from 'path'
import fs from 'fs/promises'
import { Request } from 'express'
import crypto from 'crypto'
import { prisma } from '@/config/database.config'

export interface UploadConfig {
  destination: string
  maxFileSize: number
  allowedTypes: string[]
  maxFiles?: number
}

export interface FileInfo {
  fieldname: string
  originalname: string
  encoding: string
  mimetype: string
  size: number
  filename: string
  path: string
}

export class UploadService {
  private static readonly DEFAULT_CONFIG: UploadConfig = {
    destination: 'uploads/',
    maxFileSize: 10 * 1024 * 1024, // 10MB
    allowedTypes: [
      'image/jpeg',
      'image/png', 
      'image/gif',
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'text/plain',
      'application/zip',
      'application/x-rar-compressed'
    ],
    maxFiles: 5
  }

  /**
   * 创建 multer 中间件
   */
  static createUploadMiddleware(config: Partial<UploadConfig> = {}) {
    const finalConfig = { ...this.DEFAULT_CONFIG, ...config }

    // 确保上传目录存在
    this.ensureDirectoryExists(finalConfig.destination)

    const storage = multer.diskStorage({
      destination: (_req, _file, cb) => {
        cb(null, finalConfig.destination)
      },
      filename: (_req, file, cb) => {
        // 生成唯一文件名
        const uniqueSuffix = Date.now() + '-' + crypto.randomBytes(6).toString('hex')
        const ext = path.extname(file.originalname)
        const name = path.basename(file.originalname, ext)
        const sanitizedName = name.replace(/[^a-zA-Z0-9\u4e00-\u9fa5]/g, '_')
        cb(null, `${sanitizedName}_${uniqueSuffix}${ext}`)
      }
    })

    const fileFilter = (_req: Request, file: Express.Multer.File, cb: multer.FileFilterCallback) => {
      if (finalConfig.allowedTypes.includes(file.mimetype)) {
        cb(null, true)
      } else {
        cb(new Error(`不支持的文件类型: ${file.mimetype}`))
      }
    }

    const multerInstance = multer({
      storage,
      fileFilter,
      limits: {
        fileSize: finalConfig.maxFileSize,
        files: finalConfig.maxFiles
      }
    })

    // 返回一个中间件函数，处理文件和文本字段
    return (req: any, res: any, next: any) => {
      console.log('🔍 multer 中间件开始处理...');
      console.log('请求 Content-Type:', req.headers['content-type']);
      
      multerInstance.any()(req, res, (err: any) => {
        console.log('🔍 multer 处理完成');
        console.log('错误:', err);
        console.log('req.body:', req.body);
        console.log('req.files:', req.files);
        
        if (err) {
          console.error('❌ multer 错误:', err);
          return next(err)
        }
        
        // 确保 req.body 存在
        if (!req.body) {
          req.body = {}
        }
        
        // multer.any() 会把所有字段放到 req.body 中
        // 但是我们需要确保文件和非文件字段都正确处理
        if (req.files && req.files.length > 0) {
          // 处理文件字段 - 只保留真正的文件
          const fileFields = req.files.filter((f: any) => f.fieldname && f.filename)
          req.file = fileFields[0] // 兼容单文件上传
          req.files = fileFields // 兼容多文件上传
        }
        
        next()
      })
    }
  }

  /**
   * 处理服务工单附件上传
   */
  static async handleServiceAttachment(
    serviceId: string,
    files: Express.Multer.File[],
    uploadedBy: string
  ) {
    const attachments = []

    for (const file of files) {
      try {
        // 保存到数据库
        const attachment = await prisma.serviceAttachment.create({
          data: {
            serviceId,
            filename: file.filename,
            originalName: file.originalname,
            filePath: file.path,
            fileSize: file.size,
            mimeType: file.mimetype,
            uploadedBy
          }
        })

        attachments.push(attachment)
      } catch (error) {
        // 如果数据库保存失败，删除已上传的文件
        try {
          await fs.unlink(file.path)
        } catch (unlinkError) {
          console.error('Failed to delete file after database error:', unlinkError)
        }
        throw error
      }
    }

    return attachments
  }

  /**
   * 删除附件
   */
  static async deleteAttachment(attachmentId: string, userId: string) {
    const attachment = await prisma.serviceAttachment.findUnique({
      where: { id: attachmentId },
      include: {
        service: true
      }
    })

    if (!attachment) {
      throw new Error('附件不存在')
    }

    // 检查权限：只有上传者或工单负责人可以删除
    if (attachment.uploadedBy !== userId && attachment.service.assignedTo !== userId) {
      throw new Error('无权限删除此附件')
    }

    // 从文件系统删除文件
    try {
      await fs.unlink(attachment.filePath)
    } catch (error) {
      console.error('Failed to delete file from filesystem:', error)
      // 继续删除数据库记录，即使文件删除失败
    }

    // 从数据库删除记录
    await prisma.serviceAttachment.delete({
      where: { id: attachmentId }
    })

    return attachment
  }

  /**
   * 获取附件信息
   */
  static async getAttachment(attachmentId: string) {
    const attachment = await prisma.serviceAttachment.findUnique({
      where: { id: attachmentId },
      include: {
        uploader: {
          select: {
            id: true,
            username: true,
            fullName: true
          }
        },
        service: {
          select: {
            id: true,
            title: true,
            ticketNumber: true
          }
        }
      }
    })

    if (!attachment) {
      throw new Error('附件不存在')
    }

    return attachment
  }

  /**
   * 获取服务工单的所有附件
   */
  static async getServiceAttachments(serviceId: string) {
    return await prisma.serviceAttachment.findMany({
      where: { serviceId },
      include: {
        uploader: {
          select: {
            id: true,
            username: true,
            fullName: true
          }
        }
      },
      orderBy: { uploadedAt: 'desc' }
    })
  }

  /**
   * 获取附件文件内容
   */
  static async getAttachmentFile(attachmentId: string) {
    const attachment = await this.getAttachment(attachmentId)
    
    try {
      const fileBuffer = await fs.readFile(attachment.filePath)
      return {
        buffer: fileBuffer,
        filename: attachment.originalName,
        mimetype: attachment.mimeType,
        size: attachment.fileSize
      }
    } catch (error) {
      throw new Error('文件读取失败')
    }
  }

  /**
   * 清理过期的临时文件
   */
  static async cleanupOldFiles(daysOld: number = 30) {
    const cutoffDate = new Date()
    cutoffDate.setDate(cutoffDate.getDate() - daysOld)

    // 查找过期的附件记录
    const oldAttachments = await prisma.serviceAttachment.findMany({
      where: {
        uploadedAt: {
          lt: cutoffDate
        },
        service: {
          status: {
            in: ['CLOSED', 'RESOLVED']
          }
        }
      }
    })

    let deletedCount = 0
    let errorCount = 0

    for (const attachment of oldAttachments) {
      try {
        await fs.unlink(attachment.filePath)
        await prisma.serviceAttachment.delete({
          where: { id: attachment.id }
        })
        deletedCount++
      } catch (error) {
        console.error(`Failed to delete attachment ${attachment.id}:`, error)
        errorCount++
      }
    }

    return {
      deletedCount,
      errorCount,
      totalProcessed: oldAttachments.length
    }
  }

  /**
   * 获取存储统计信息
   */
  static async getStorageStats() {
    const [
      totalAttachments,
      totalSize,
      attachmentsByType,
      recentAttachments
    ] = await Promise.all([
      prisma.serviceAttachment.count(),
      prisma.serviceAttachment.aggregate({
        _sum: { fileSize: true }
      }),
      prisma.serviceAttachment.groupBy({
        by: ['mimeType'],
        _count: { id: true },
        _sum: { fileSize: true }
      }),
      prisma.serviceAttachment.count({
        where: {
          uploadedAt: {
            gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) // 最近7天
          }
        }
      })
    ])

    return {
      totalAttachments,
      totalSize: totalSize._sum.fileSize || 0,
      recentAttachments,
      typeBreakdown: attachmentsByType.map(item => ({
        mimeType: item.mimeType,
        count: item._count.id,
        totalSize: item._sum.fileSize || 0
      }))
    }
  }

  /**
   * 确保目录存在
   */
  private static async ensureDirectoryExists(dir: string) {
    try {
      await fs.access(dir)
    } catch {
      await fs.mkdir(dir, { recursive: true })
    }
  }

  /**
   * 验证文件类型
   */
  static isValidFileType(mimetype: string, allowedTypes?: string[]): boolean {
    const types = allowedTypes || this.DEFAULT_CONFIG.allowedTypes
    return types.includes(mimetype)
  }

  /**
   * 格式化文件大小
   */
  static formatFileSize(bytes: number): string {
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    if (bytes === 0) return '0 Bytes'
    const i = Math.floor(Math.log(bytes) / Math.log(1024))
    return `${Math.round(bytes / Math.pow(1024, i) * 100) / 100} ${sizes[i]}`
  }
}