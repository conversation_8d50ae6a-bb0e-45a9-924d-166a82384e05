import crypto from 'crypto'

export class EncryptionService {
  private static readonly ALGORITHM = 'aes-256-cbc'
  private static readonly KEY_LENGTH = 32
  private static readonly IV_LENGTH = 16

  /**
   * 获取加密密钥
   */
  private static getEncryptionKey(): Buffer {
    const key = process.env['ENCRYPTION_KEY']
    if (!key) {
      // 如果没有设置加密密钥，使用默认密钥（仅用于开发环境）
      console.warn('未设置ENCRYPTION_KEY环境变量，使用默认密钥（不安全）')
      return crypto.pbkdf2Sync('default-key-for-dev', 'salt', 10000, this.KEY_LENGTH, 'sha256')
    }

    // 如果密钥长度不够，使用PBKDF2派生
    if (key.length < this.KEY_LENGTH) {
      return crypto.pbkdf2Sync(key, 'salt', 10000, this.KEY_LENGTH, 'sha256')
    }

    return Buffer.from(key.substring(0, this.KEY_LENGTH), 'utf8')
  }

  /**
   * 加密数据
   */
  static async encrypt(plaintext: string): Promise<string> {
    try {
      const key = this.getEncryptionKey()
      const iv = crypto.randomBytes(this.IV_LENGTH)

      const cipher = crypto.createCipheriv(this.ALGORITHM, key, iv)

      let encrypted = cipher.update(plaintext, 'utf8', 'hex')
      encrypted += cipher.final('hex')

      // 组合 IV + 加密数据
      const result = iv.toString('hex') + encrypted

      return Buffer.from(result, 'hex').toString('base64')
    } catch (error) {
      console.error('加密失败:', error)
      throw new Error('数据加密失败')
    }
  }

  /**
   * 解密数据
   */
  static async decrypt(encryptedData: string): Promise<string> {
    try {
      const key = this.getEncryptionKey()
      const buffer = Buffer.from(encryptedData, 'base64')
      const data = buffer.toString('hex')

      // 提取 IV 和加密数据
      const iv = Buffer.from(data.substring(0, this.IV_LENGTH * 2), 'hex')
      const encrypted = data.substring(this.IV_LENGTH * 2)

      const decipher = crypto.createDecipheriv(this.ALGORITHM, key, iv)

      let decrypted = decipher.update(encrypted, 'hex', 'utf8')
      decrypted += decipher.final('utf8')

      return decrypted
    } catch (error) {
      console.error('解密失败:', error)
      throw new Error('数据解密失败')
    }
  }

  /**
   * 生成随机密钥
   */
  static generateKey(): string {
    return crypto.randomBytes(this.KEY_LENGTH).toString('hex')
  }

  /**
   * 哈希数据
   */
  static hash(data: string): string {
    return crypto.createHash('sha256').update(data).digest('hex')
  }

  /**
   * 验证哈希
   */
  static verifyHash(data: string, hash: string): boolean {
    return this.hash(data) === hash
  }
}
