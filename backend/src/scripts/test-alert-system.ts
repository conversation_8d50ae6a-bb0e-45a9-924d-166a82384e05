import { AlertEngineService } from '../services/alert-engine.service'
import { MonitorService } from '../services/monitor.service'
import { SLAViolationAlertService } from '../services/sla-violation-alert.service'
import { SystemMetrics } from '../types/monitor.types'

/**
 * 告警系统测试脚本
 * 用于验证告警引擎的功能
 */
async function testAlertSystem() {
  console.log('🧪 开始测试告警系统...\n')

  try {
    const alertEngine = AlertEngineService.getInstance()
    const slaService = SLAViolationAlertService.getInstance()

    // 1. 创建默认告警规则
    console.log('📋 创建默认告警规则...')
    await alertEngine.createDefaultAlertRules()
    console.log('✅ 默认告警规则创建完成\n')

    // 2. 获取所有告警规则
    console.log('📋 获取所有告警规则...')
    const rules = await alertEngine.getAllAlertRules()
    console.log(`✅ 发现 ${rules.length} 条告警规则:`)
    rules.forEach(rule => {
      console.log(`   - ${rule.name} (${rule.metricType}, 阈值: ${rule.threshold}%)`)
    })
    console.log()

    // 3. 模拟高CPU使用率指标
    console.log('🔥 模拟高CPU使用率告警...')
    const highCpuMetrics: SystemMetrics = {
      timestamp: new Date(),
      cpu: {
        usage: 85.5, // 超过80%阈值
        cores: 8,
        model: 'Intel Core i7',
        temperature: 75
      },
      memory: {
        usage: 60.2,
        total: 16 * 1024 * 1024 * 1024, // 16GB
        used: 9.6 * 1024 * 1024 * 1024, // 9.6GB
        available: 6.4 * 1024 * 1024 * 1024, // 6.4GB
        buffers: 0.5 * 1024 * 1024 * 1024,
        cached: 1.5 * 1024 * 1024 * 1024
      },
      disk: {
        usage: 70.1,
        total: 1024 * 1024 * 1024 * 1024, // 1TB
        used: 716.8 * 1024 * 1024 * 1024, // 716.8GB
        available: 307.2 * 1024 * 1024 * 1024, // 307.2GB
        readSpeed: 150.5,
        writeSpeed: 120.3
      },
      network: {
        totalRxSpeed: 50.2,
        totalTxSpeed: 30.8,
        interfaces: []
      },
      processes: {
        total: 156,
        running: 3,
        sleeping: 148,
        zombie: 0,
        stopped: 5,
        topProcesses: []
      },
      uptime: 86400 // 1天
    }

    const triggeredAlerts = await alertEngine.evaluateMetrics(highCpuMetrics)
    console.log(`✅ 评估完成，触发了 ${triggeredAlerts.length} 个告警`)
    triggeredAlerts.forEach(alert => {
      console.log(`   🚨 告警: ${alert.message} (严重程度: ${alert.severity})`)
    })
    console.log()

    // 4. 模拟告警统计
    console.log('📊 获取告警统计...')
    const stats = await alertEngine.getAlertStatistics({
      start: new Date(Date.now() - 24 * 60 * 60 * 1000), // 24小时前
      end: new Date()
    })
    console.log('✅ 告警统计:')
    console.log(`   - 总告警数: ${stats.total}`)
    console.log(`   - 按严重程度统计:`, stats.bySeverity)
    console.log(`   - 按状态统计:`, stats.byStatus)
    console.log(`   - 平均解决时间: ${stats.averageResolutionTime} 分钟`)
    console.log()

    // 5. 测试SLA违规检查
    console.log('⏰ 触发SLA违规检查...')
    await slaService.triggerSLACheck()
    console.log('✅ SLA违规检查完成\n')

    // 6. 获取SLA违规统计
    console.log('📊 获取SLA违规统计...')
    const slaStats = await slaService.getSLAViolationStatistics({
      start: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // 7天前
      end: new Date()
    })
    console.log('✅ SLA违规统计:')
    console.log(`   - 总违规数: ${slaStats.total}`)
    console.log(`   - 按严重程度统计:`, slaStats.bySeverity)
    console.log(`   - 按违规类型统计:`, slaStats.byType)
    console.log(`   - 按状态统计:`, slaStats.byStatus)
    console.log()

    console.log('🎉 告警系统测试完成！所有功能正常运行。')

  } catch (error) {
    console.error('❌ 告警系统测试失败:', error)
    process.exit(1)
  }
}

/**
 * 测试告警规则创建和管理
 */
async function testAlertRuleManagement() {
  console.log('\n🔧 测试告警规则管理...\n')

  try {
    const alertEngine = AlertEngineService.getInstance()

    // 创建自定义告警规则
    console.log('📝 创建自定义告警规则...')
    const customRule = await alertEngine.createAlertRule({
      name: '测试自定义规则',
      description: '用于测试的自定义告警规则',
      metricType: 'MEMORY',
      condition: 'GT',
      threshold: 95,
      duration: 120, // 2分钟
      severity: 'CRITICAL',
      enabled: true,
      notificationChannels: ['email', 'realtime'],
      cooldownPeriod: 600 // 10分钟冷却期
    }, 'TEST_USER')

    console.log(`✅ 创建成功: ${customRule.name} (ID: ${customRule.id})`)

    // 更新告警规则
    console.log('🔄 更新告警规则...')
    const updatedRule = await alertEngine.updateAlertRule(customRule.id, {
      threshold: 90,
      description: '更新后的测试规则描述'
    })
    console.log(`✅ 更新成功，新阈值: ${updatedRule.threshold}%`)

    // 删除告警规则
    console.log('🗑️ 删除测试规则...')
    await alertEngine.deleteAlertRule(customRule.id)
    console.log('✅ 删除成功')

    console.log('🎯 告警规则管理测试完成！')

  } catch (error) {
    console.error('❌ 告警规则管理测试失败:', error)
  }
}

/**
 * 测试告警降噪功能
 */
async function testAlertDeduplication() {
  console.log('\n🔇 测试告警降噪功能...\n')

  try {
    const alertEngine = AlertEngineService.getInstance()

    // 连续发送相同的高CPU告警
    console.log('🔁 模拟连续高CPU使用率...')
    
    const metrics: SystemMetrics = {
      timestamp: new Date(),
      cpu: { usage: 90, cores: 8, model: 'Intel Core i7', temperature: 80 },
      memory: { usage: 60, total: 16e9, used: 9.6e9, available: 6.4e9, buffers: 0.5e9, cached: 1.5e9 },
      disk: { usage: 70, total: 1e12, used: 700e9, available: 300e9, readSpeed: 150, writeSpeed: 120 },
      network: { totalRxSpeed: 50, totalTxSpeed: 30, interfaces: [] },
      processes: { total: 150, running: 3, sleeping: 147, zombie: 0, stopped: 0, topProcesses: [] },
      uptime: 86400
    }

    // 第一次评估
    console.log('📊 第一次指标评估...')
    const alerts1 = await alertEngine.evaluateMetrics(metrics)
    console.log(`结果: ${alerts1.length} 个告警`)

    // 等待1秒后再次评估（应该被降噪抑制）
    await new Promise(resolve => setTimeout(resolve, 1000))
    console.log('📊 第二次指标评估（应该被降噪）...')
    const alerts2 = await alertEngine.evaluateMetrics(metrics)
    console.log(`结果: ${alerts2.length} 个告警`)

    console.log('🎯 告警降噪测试完成！')

  } catch (error) {
    console.error('❌ 告警降噪测试失败:', error)
  }
}

// 运行测试
if (require.main === module) {
  (async () => {
    try {
      await testAlertSystem()
      await testAlertRuleManagement()
      await testAlertDeduplication()
      
      console.log('\n🏁 所有测试完成！')
      process.exit(0)
    } catch (error) {
      console.error('\n💥 测试执行失败:', error)
      process.exit(1)
    }
  })()
}

export {
  testAlertSystem,
  testAlertRuleManagement,
  testAlertDeduplication
}