#!/usr/bin/env ts-node

import { triggerManager } from '@/services/trigger-manager.service'
import { eventTrigger } from '@/services/triggers/event-trigger.service'
import { cronTrigger } from '@/services/triggers/cron-trigger.service'
import { conditionalTrigger } from '@/services/triggers/conditional-trigger.service'
import { webhookTrigger } from '@/services/triggers/webhook-trigger.service'

/**
 * 触发器系统综合演示
 * 展示所有触发器类型的功能和协调工作
 */

class TriggerSystemDemo {
  private isRunning = false
  private demoInterval: NodeJS.Timeout | null = null

  constructor() {
    this.setupExitHandlers()
  }

  /**
   * 启动演示
   */
  async start() {
    console.log('🚀 触发器系统演示启动中...\n')
    this.isRunning = true

    try {
      await this.initializeDemo()
      await this.runDemoScenarios()
      await this.startLiveDemo()
    } catch (error) {
      console.error('❌ 演示运行失败:', error)
    }
  }

  /**
   * 停止演示
   */
  async stop() {
    console.log('\n🛑 正在停止触发器系统演示...')
    this.isRunning = false

    if (this.demoInterval) {
      clearInterval(this.demoInterval)
      this.demoInterval = null
    }

    await this.cleanup()
    console.log('✅ 演示已停止')
    process.exit(0)
  }

  /**
   * 初始化演示环境
   */
  private async initializeDemo() {
    console.log('⚙️ 初始化演示环境...')

    // 等待所有服务初始化完成
    await new Promise(resolve => setTimeout(resolve, 2000))

    // 创建演示用的触发器
    await this.setupDemoTriggers()

    console.log('✅ 演示环境初始化完成\n')
  }

  /**
   * 设置演示用触发器
   */
  private async setupDemoTriggers() {
    console.log('🔧 设置演示触发器...')

    // 1. 创建事件触发器演示
    console.log('  📡 设置事件触发器...')

    // 2. 创建定时任务演示
    console.log('  ⏰ 设置定时触发器...')
    await cronTrigger.scheduleJob('demo-heartbeat', {
      schedule: '*/10 * * * * *', // 每10秒
      enabled: false, // 稍后手动启动
      description: '演示心跳任务',
      handler: async () => {
        const timestamp = new Date().toLocaleTimeString()
        console.log(`💓 [${timestamp}] 系统心跳 - 触发器系统正常运行`)
        return { heartbeat: true, timestamp: new Date() }
      }
    })

    // 3. 创建条件触发器演示
    console.log('  🔍 设置条件触发器...')
    await conditionalTrigger.createConditionalTrigger('demo-cpu-monitor', {
      conditions: [
        {
          field: 'cpu.usage',
          operator: 'gt',
          value: 1, // 设置低阈值以确保在演示中触发
          source: 'metric',
          description: 'CPU使用率监控'
        }
      ],
      operator: 'AND',
      checkInterval: 15, // 15秒检查一次
      enabled: false, // 稍后启用
      description: 'CPU使用率监控演示'
    })

    // 4. 创建Webhook触发器演示
    console.log('  🔗 设置Webhook触发器...')
    await webhookTrigger.registerWebhook('demo-webhook', {
      endpoint: '/webhooks/demo',
      method: 'POST',
      authentication: {
        type: 'token',
        token: 'demo-secret-token'
      },
      enabled: true,
      description: '演示Webhook端点',
      rateLimit: 100
    })

    console.log('✅ 演示触发器设置完成')
  }

  /**
   * 运行演示场景
   */
  private async runDemoScenarios() {
    console.log('🎭 开始演示场景...\n')

    // 场景1: 事件触发器演示
    await this.demoEventTriggers()
    await this.sleep(2000)

    // 场景2: 定时触发器演示
    await this.demoCronTriggers()
    await this.sleep(2000)

    // 场景3: 条件触发器演示
    await this.demoConditionalTriggers()
    await this.sleep(2000)

    // 场景4: Webhook触发器演示
    await this.demoWebhookTriggers()
    await this.sleep(2000)

    // 场景5: 系统统计展示
    await this.demoSystemStats()
    await this.sleep(2000)

    console.log('✅ 所有演示场景完成\n')
  }

  /**
   * 事件触发器演示
   */
  private async demoEventTriggers() {
    console.log('📡 === 事件触发器演示 ===')
    
    const events = [
      { type: 'SERVICE_CREATED', data: { serviceId: 'demo-001', priority: 'HIGH' } },
      { type: 'USER_LOGIN', data: { userId: 'demo-user', timestamp: new Date() } },
      { type: 'SLA_VIOLATION', data: { serviceId: 'demo-002', violationType: 'RESPONSE_TIME' } },
      { type: 'SYSTEM_ALERT', data: { level: 'WARNING', message: '演示警告' } }
    ]

    for (const event of events) {
      console.log(`  🔥 触发事件: ${event.type}`)
      await triggerManager.triggerEvent(event.type, event.data, 'demo')
      await this.sleep(500)
    }

    // 显示事件统计
    const eventStats = eventTrigger.getEventStatistics()
    console.log(`  📊 事件统计: 总事件 ${eventStats['totalEvents'] || 0}, 今日事件 ${eventStats['todayEvents'] || 0}`)
    
    // 显示最近事件
    const recentEvents = eventTrigger.getRecentEvents('', 3)
    console.log(`  📋 最近3个事件:`)
    recentEvents.forEach((event, index) => {
      console.log(`    ${index + 1}. ${event.type} - ${event.timestamp?.toLocaleTimeString()}`)
    })
    
    console.log('✅ 事件触发器演示完成\n')
  }

  /**
   * 定时触发器演示
   */
  private async demoCronTriggers() {
    console.log('⏰ === 定时触发器演示 ===')

    // 启动心跳任务
    console.log('  ▶️ 启动演示心跳任务...')
    await cronTrigger.startJob('demo-heartbeat')

    // 创建一个短期任务进行演示
    await cronTrigger.scheduleJob('demo-short-task', {
      schedule: '*/5 * * * * *', // 每5秒
      enabled: true,
      maxExecutions: 3, // 最多执行3次
      description: '短期演示任务',
      handler: async () => {
        const timestamp = new Date().toLocaleTimeString()
        console.log(`  ⚡ [${timestamp}] 短期任务执行`)
        return { executed: true, timestamp: new Date() }
      }
    })

    // 手动执行任务演示
    console.log('  🚀 手动执行心跳任务:')
    const execution = await cronTrigger.runJobNow('demo-heartbeat')
    console.log(`    结果: ${execution.status}, 耗时: ${execution.duration}ms`)

    // 显示任务统计
    const cronStats = cronTrigger.getJobStatistics()
    console.log(`  📊 定时任务统计: 总任务 ${cronStats.totalJobs}, 运行中 ${cronStats.runningJobs}`)

    // 等待短期任务完成
    console.log('  ⏳ 等待短期任务完成...')
    await this.sleep(20000) // 等待20秒让任务执行完

    console.log('✅ 定时触发器演示完成\n')
  }

  /**
   * 条件触发器演示
   */
  private async demoConditionalTriggers() {
    console.log('🔍 === 条件触发器演示 ===')

    // 启用CPU监控触发器
    console.log('  ▶️ 启用CPU监控触发器...')
    await conditionalTrigger.enableTrigger('demo-cpu-monitor')

    // 手动评估条件
    console.log('  🧪 手动评估条件:')
    const evaluation = await conditionalTrigger.forceEvaluation('demo-cpu-monitor')
    if (evaluation) {
      console.log(`    评估结果: ${evaluation.finalResult ? '✅ 满足' : '❌ 不满足'}`)
      evaluation.results.forEach((result, index) => {
        console.log(`    条件 ${index + 1}: ${result.condition.field} ${result.condition.operator} ${result.condition.value} = ${result.result} (实际值: ${result.actualValue})`)
      })
    }

    // 创建一个会触发的条件
    await conditionalTrigger.createConditionalTrigger('demo-always-trigger', {
      conditions: [
        {
          field: 'cpu.usage',
          operator: 'exists',
          value: null,
          source: 'metric',
          description: '总是满足的条件'
        }
      ],
      operator: 'AND',
      checkInterval: 10,
      enabled: true,
      description: '演示必定触发的条件'
    })

    // 显示触发器信息
    const triggerInfo = conditionalTrigger.getTriggerInfo('demo-always-trigger')
    if (triggerInfo) {
      console.log(`  📊 触发器统计: 评估 ${triggerInfo.statistics.totalEvaluations} 次, 触发 ${triggerInfo.statistics.totalTriggers} 次`)
    }

    console.log('✅ 条件触发器演示完成\n')
  }

  /**
   * Webhook触发器演示
   */
  private async demoWebhookTriggers() {
    console.log('🔗 === Webhook触发器演示 ===')

    // 模拟Webhook请求
    const testRequests = [
      {
        name: '有效请求',
        headers: { 'x-webhook-secret': 'demo-secret-token', 'content-type': 'application/json' },
        body: { event: 'test', data: 'valid request' }
      },
      {
        name: '无效认证',
        headers: { 'x-webhook-secret': 'wrong-token' },
        body: { event: 'test', data: 'invalid auth' }
      },
      {
        name: '无认证头',
        headers: { 'content-type': 'application/json' },
        body: { event: 'test', data: 'no auth' }
      }
    ]

    for (const request of testRequests) {
      console.log(`  🌐 测试${request.name}:`)
      const response = await webhookTrigger.handleWebhookRequest(
        '/webhooks/demo',
        'POST',
        request.headers,
        request.body,
        '127.0.0.1'
      )
      console.log(`    结果: ${response.success ? '✅ 成功' : '❌ 失败'} - ${response.message}`)
    }

    // 显示Webhook统计
    const webhookInfo = webhookTrigger.getWebhookInfo('demo-webhook')
    if (webhookInfo) {
      console.log(`  📊 Webhook统计: 总请求 ${webhookInfo.statistics.totalRequests}, 成功率 ${webhookInfo.statistics.successRate.toFixed(1)}%`)
    }

    console.log('✅ Webhook触发器演示完成\n')
  }

  /**
   * 系统统计演示
   */
  private async demoSystemStats() {
    console.log('📊 === 系统统计展示 ===')

    const stats = await triggerManager.getTriggerServiceStats()
    
    console.log('  🎯 触发器系统概览:')
    console.log(`    事件触发器: ${stats.eventTrigger.supportedEventTypes.length} 种事件类型`)
    console.log(`    定时触发器: ${stats.cronTrigger.statistics.totalJobs} 个任务`)
    console.log(`    条件触发器: ${stats.conditionalTrigger.allTriggers.length} 个触发器`)
    console.log(`    Webhook触发器: ${stats.webhookTrigger.allWebhooks.length} 个端点`)

    console.log('\n  📈 性能指标:')
    console.log(`    事件总数: ${stats.eventTrigger.statistics['totalEvents'] || 0}`)
    console.log(`    任务成功率: ${stats.cronTrigger.statistics.overallSuccessRate.toFixed(1)}%`)
    console.log(`    平均执行时间: ${stats.cronTrigger.statistics.avgExecutionTime.toFixed(0)}ms`)

    console.log('✅ 系统统计展示完成\n')
  }

  /**
   * 开始实时演示
   */
  private async startLiveDemo() {
    console.log('🔴 === 实时演示模式 (按 Ctrl+C 退出) ===\n')

    // 定期显示系统状态
    this.demoInterval = setInterval(async () => {
      if (!this.isRunning) return

      const timestamp = new Date().toLocaleTimeString()
      console.log(`📡 [${timestamp}] 触发器系统运行状态:`)

      // 随机触发一些事件
      const randomEvents = ['HEARTBEAT', 'STATUS_CHECK', 'DEMO_EVENT']
      const randomEvent = randomEvents[Math.floor(Math.random() * randomEvents.length)]
      
      await triggerManager.triggerEvent(randomEvent, {
        demoMode: true,
        timestamp: new Date(),
        randomValue: Math.floor(Math.random() * 100)
      }, 'live-demo')

      // 显示最新统计
      const eventStats = eventTrigger.getEventStatistics()
      console.log(`  活跃事件: ${eventStats['recentEvents'] || 0} (最近1分钟)`)

      const cronStats = cronTrigger.getJobStatistics()
      console.log(`  运行任务: ${cronStats.runningJobs}/${cronStats.totalJobs}`)

      console.log('  ---')
    }, 5000) // 每5秒更新

    // 保持程序运行
    await new Promise(() => {}) // 无限等待，直到外部中断
  }

  /**
   * 清理资源
   */
  private async cleanup() {
    console.log('🧹 清理演示资源...')

    try {
      // 停止所有演示任务
      await cronTrigger.unscheduleJob('demo-heartbeat')
      await cronTrigger.unscheduleJob('demo-short-task')
      
      // 移除条件触发器
      await conditionalTrigger.removeTrigger('demo-cpu-monitor')
      await conditionalTrigger.removeTrigger('demo-always-trigger')
      
      // 移除Webhook
      await webhookTrigger.removeWebhook('demo-webhook')
      
      console.log('✅ 资源清理完成')
    } catch (error) {
      console.error('❌ 清理过程中出错:', error)
    }
  }

  /**
   * 设置退出处理器
   */
  private setupExitHandlers() {
    process.on('SIGINT', async () => {
      console.log('\n收到退出信号...')
      await this.stop()
    })

    process.on('SIGTERM', async () => {
      console.log('\n收到终止信号...')
      await this.stop()
    })

    process.on('uncaughtException', async (error) => {
      console.error('未捕获的异常:', error)
      await this.stop()
    })
  }

  /**
   * 延时辅助函数
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }
}

// 启动演示
async function main() {
  const demo = new TriggerSystemDemo()
  await demo.start()
}

// 如果直接运行此文件，启动演示
if (require.main === module) {
  main().catch(console.error)
}

export { TriggerSystemDemo }