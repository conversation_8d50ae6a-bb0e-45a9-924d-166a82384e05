/**
 * 工作流集成系统演示脚本
 * 展示触发器和执行器系统的完整集成
 */

import { v4 as uuidv4 } from 'uuid';
import { WorkflowStepType } from '@prisma/client';

// 导入所有必要的服务
import { workflowCoordinator } from '../services/workflow-coordinator.service';
import { triggerManager } from '../services/trigger-manager.service';
import { workflowEngine } from '../services/workflow-engine.service';
import { ActionExecutorRegistry } from '../services/action-executor-registry.service';
import { metricsCollector } from '../services/metrics-collector.service';
import { logAggregator } from '../services/log-aggregator.service';

/**
 * 演示完整工作流集成
 */
async function demoWorkflowIntegration(): Promise<void> {
  console.log('\n=== 工作流集成系统演示 ===');
  
  try {
    // 初始化所有组件
    console.log('🚀 初始化集成系统...');
    await ActionExecutorRegistry.registerAllExecutors();
    await workflowCoordinator.initialize({
      enableAutoRecovery: true,
      enableMetricsCollection: true,
      enableLogging: true,
      maxConcurrentSessions: 5
    });

    // 创建演示工作流
    const workflowDefinition = await createDemoWorkflow();
    console.log(`📋 创建工作流: ${workflowDefinition.name}`);

    // 设置触发器
    await setupDemoTriggers(workflowDefinition.id);

    // 演示手动执行
    await demoManualExecution(workflowDefinition.id);

    // 演示触发器执行
    await demoTriggerExecution();

    // 展示系统统计
    await displaySystemStatistics();

  } catch (error) {
    console.error('❌ 集成演示失败:', error);
    throw error;
  }
}

/**
 * 创建演示工作流
 */
async function createDemoWorkflow(): Promise<any> {
  const workflowDefinition = {
    name: '客户服务工单处理流程',
    description: '自动化处理客户服务请求的完整工作流',
    version: '1.0.0',
    definition: {
      steps: [
        {
          index: 0,
          name: '验证客户信息',
          type: WorkflowStepType.CONDITIONAL,
          config: {
            condition: {
              operator: 'exists',
              left: '${customerId}'
            },
            trueAction: {
              type: 'continue',
              message: '客户信息验证通过'
            },
            falseAction: {
              type: 'fail',
              message: '客户信息验证失败'
            }
          }
        },
        {
          index: 1,
          name: '查询客户等级',
          type: WorkflowStepType.DATABASE_QUERY,
          config: {
            operation: 'SELECT',
            query: 'SELECT level FROM customers WHERE id = $1',
            parameters: {
              '$1': '${customerId}'
            }
          }
        },
        {
          index: 2,
          name: '根据客户等级分配优先级',
          type: WorkflowStepType.CONDITIONAL,
          config: {
            condition: {
              operator: 'equals',
              left: '${step_1_result.level}',
              right: 'VIP'
            },
            trueAction: {
              type: 'continue',
              data: { priority: 'HIGH', slaHours: 4 }
            },
            falseAction: {
              type: 'continue',
              data: { priority: 'MEDIUM', slaHours: 24 }
            }
          }
        },
        {
          index: 3,
          name: '创建服务工单',
          type: WorkflowStepType.BUSINESS_LOGIC,
          config: {
            action: 'create_service_ticket',
            parameters: {
              customerId: '${customerId}',
              title: '${requestTitle}',
              description: '${requestDescription}',
              priority: '${step_2_result.data.priority}',
              category: 'SUPPORT'
            }
          }
        },
        {
          index: 4,
          name: '发送确认通知',
          type: WorkflowStepType.NOTIFICATION,
          config: {
            type: 'multi',
            message: {
              subject: '工单创建确认',
              content: '您的服务请求已收到，工单号：${step_3_result.ticketNumber}'
            },
            channels: [
              { type: 'email', config: { recipients: ['${customerEmail}'] } },
              { type: 'sms', config: { recipients: ['${customerPhone}'] } }
            ]
          }
        },
        {
          index: 5,
          name: '内部团队通知',
          type: WorkflowStepType.HTTP_REQUEST,
          config: {
            method: 'POST',
            url: 'https://hooks.slack.com/services/internal/team/notification',
            data: {
              text: '新工单创建: ${step_3_result.ticketNumber}',
              priority: '${step_2_result.data.priority}',
              customer: '${customerId}'
            },
            headers: {
              'Content-Type': 'application/json'
            }
          }
        }
      ],
      variables: {
        customerId: '',
        customerEmail: '',
        customerPhone: '',
        requestTitle: '',
        requestDescription: ''
      },
      settings: {
        timeout: 300000, // 5分钟
        retryPolicy: {
          maxRetries: 2,
          delay: 5000
        }
      }
    }
  };

  // 创建工作流
  const workflow = await workflowEngine.createWorkflow(
    workflowDefinition.name,
    workflowDefinition.description,
    workflowDefinition.definition,
    'system'
  );

  return workflow;
}

/**
 * 设置演示触发器
 */
async function setupDemoTriggers(workflowId: string): Promise<void> {
  console.log('🔔 设置触发器...');

  // 事件触发器 - 新客户请求
  const eventTrigger = await triggerManager.createEventTrigger({
    workflowId,
    eventType: 'customer_request_created',
    name: '客户请求创建触发器',
    description: '当有新的客户请求时自动触发工作流',
    isEnabled: true,
    config: {
      conditions: {
        source: 'customer_portal',
        priority: ['HIGH', 'URGENT']
      }
    }
  });

  // 定时触发器 - 每日汇总
  const cronTrigger = await triggerManager.createCronTrigger({
    workflowId,
    cronExpression: '0 9 * * *', // 每天上午9点
    name: '每日工单汇总',
    description: '每日生成工单处理汇总报告',
    isEnabled: true,
    timeZone: 'Asia/Shanghai',
    config: {
      reportType: 'daily_summary',
      includePending: true
    }
  });

  // Webhook触发器 - 外部系统集成
  const webhookTrigger = await triggerManager.createWebhookTrigger({
    workflowId,
    name: '外部系统集成',
    description: '接收外部CRM系统的客户请求',
    isEnabled: true,
    config: {
      authentication: {
        type: 'bearer',
        token: 'demo-webhook-token'
      },
      validation: {
        requiredFields: ['customerId', 'requestType'],
        allowedSources: ['crm.example.com']
      }
    }
  });

  console.log(`✅ 创建 ${3} 个触发器`);
}

/**
 * 演示手动执行
 */
async function demoManualExecution(workflowId: string): Promise<void> {
  console.log('\n🚀 演示手动工作流执行...');

  // 模拟客户请求数据
  const triggerContext = {
    customerId: 'cust_demo_123',
    customerEmail: '<EMAIL>',
    customerPhone: '+86-138-0013-8000',
    requestTitle: '系统访问问题',
    requestDescription: '无法登录系统，提示密码错误',
    source: 'customer_portal'
  };

  try {
    // 启动工作流执行
    const sessionId = await workflowCoordinator.startWorkflowExecution(
      workflowId,
      triggerContext
    );

    console.log(`📋 启动执行会话: ${sessionId}`);

    // 监听执行进度
    workflowCoordinator.on('workflow:step:completed', (event) => {
      console.log(`✅ 步骤完成: ${event.stepIndex} (会话: ${event.sessionId})`);
    });

    workflowCoordinator.on('workflow:step:failed', (event) => {
      console.log(`❌ 步骤失败: ${event.stepIndex} - ${event.error.message}`);
    });

    workflowCoordinator.on('workflow:execution:completed', (event) => {
      console.log(`🎉 工作流执行完成: ${event.sessionId} (${event.completedSteps}/${event.totalSteps})`);
    });

    workflowCoordinator.on('workflow:execution:failed', (event) => {
      console.log(`💥 工作流执行失败: ${event.sessionId} - ${event.error.message}`);
    });

    // 等待执行完成
    await new Promise(resolve => setTimeout(resolve, 10000));

    // 获取执行结果
    const session = workflowCoordinator.getExecutionSession(sessionId);
    if (session) {
      console.log('📊 执行结果:');
      console.log(`  状态: ${session.status}`);
      console.log(`  完成步骤: ${session.currentStep}/${session.totalSteps}`);
      console.log(`  耗时: ${session.endTime ? session.endTime.getTime() - session.startTime.getTime() : 'N/A'}ms`);
      console.log(`  错误数: ${session.errors.length}`);
    }

  } catch (error: any) {
    console.error('手动执行失败:', error);
  }
}

/**
 * 演示触发器执行
 */
async function demoTriggerExecution(): Promise<void> {
  console.log('\n🎯 演示触发器执行...');

  try {
    // 模拟事件触发
    console.log('📤 发送客户请求事件...');
    
    const eventData = {
      customerId: 'cust_vip_456',
      customerEmail: '<EMAIL>',
      customerPhone: '+86-188-0018-8000',
      requestTitle: '紧急系统故障',
      requestDescription: '生产环境系统无法访问，影响业务',
      priority: 'URGENT',
      source: 'customer_portal'
    };

    await triggerManager.triggerEvent('customer_request_created', eventData);
    console.log('✅ 事件已触发');

    // 等待触发器处理
    await new Promise(resolve => setTimeout(resolve, 2000));

    // 模拟Webhook触发
    console.log('🌐 模拟Webhook调用...');
    
    const webhookData = {
      customerId: 'cust_enterprise_789',
      requestType: 'technical_support',
      severity: 'high',
      description: 'API集成问题需要技术支持',
      source: 'crm.example.com'
    };

    // 这里应该调用实际的webhook端点，但在演示中我们直接触发
    await triggerManager.triggerWebhook('webhook_integration', webhookData);
    console.log('✅ Webhook已触发');

  } catch (error: any) {
    console.error('触发器执行失败:', error);
  }
}

/**
 * 显示系统统计信息
 */
async function displaySystemStatistics(): Promise<void> {
  console.log('\n📊 系统统计信息');
  console.log('================');

  try {
    // 协调器统计
    const coordinatorStatus = workflowCoordinator.getCoordinatorStatus();
    console.log('\n🎯 工作流协调器:');
    console.log(`  状态: ${coordinatorStatus.status}`);
    console.log(`  活跃会话: ${coordinatorStatus.activeSessions}`);
    console.log(`  总会话数: ${coordinatorStatus.totalSessions}`);

    const executionStats = workflowCoordinator.getExecutionStatistics();
    console.log('\n📈 执行统计:');
    console.log(`  完成执行: ${executionStats.completedExecutions}`);
    console.log(`  失败执行: ${executionStats.failedExecutions}`);
    console.log(`  运行中执行: ${executionStats.runningExecutions}`);
    console.log(`  平均执行时间: ${executionStats.averageExecutionTime}ms`);
    console.log(`  成功率: ${executionStats.successRate}%`);

    // 触发器统计
    const triggerStats = await triggerManager.getSystemStatistics();
    console.log('\n🔔 触发器系统:');
    console.log(`  总触发器: ${triggerStats.totalTriggers}`);
    console.log(`  启用触发器: ${triggerStats.enabledTriggers}`);
    console.log(`  活跃监听器: ${triggerStats.activeListeners}`);

    // 指标统计
    const metricsStats = metricsCollector.getMetrics();
    console.log('\n📊 指标收集:');
    console.log(`  总执行数: ${metricsStats.totalExecutions}`);
    console.log(`  成功执行数: ${metricsStats.successfulExecutions}`);
    console.log(`  失败执行数: ${metricsStats.failedExecutions}`);
    console.log(`  平均执行时间: ${metricsStats.avgExecutionTime}ms`);

    // 健康评分
    const healthScore = metricsCollector.getHealthScore();
    console.log('\n💚 系统健康评分:');
    console.log(`  总体评分: ${healthScore.overall}/100`);
    console.log(`  成功率评分: ${healthScore.factors.successRate}`);
    console.log(`  性能评分: ${healthScore.factors.avgExecutionTime}`);
    console.log(`  错误率评分: ${healthScore.factors.errorRate}`);

    // 日志统计
    const logStats = logAggregator.getLogStatistics();
    console.log('\n📝 日志统计:');
    console.log(`  总日志数: ${logStats.totalLogs}`);
    
    const levelDistribution = Array.from(logStats.levelDistribution.entries());
    if (levelDistribution.length > 0) {
      console.log('  级别分布:');
      for (const [level, count] of levelDistribution) {
        console.log(`    ${level}: ${count}`);
      }
    }

    if (logStats.topErrors.length > 0) {
      console.log('  主要错误:');
      logStats.topErrors.slice(0, 3).forEach((error, index) => {
        console.log(`    ${index + 1}. ${error.message} (${error.count}次)`);
      });
    }

  } catch (error: any) {
    console.error('获取统计信息失败:', error);
  }
}

/**
 * 演示错误恢复
 */
async function demoErrorRecovery(): Promise<void> {
  console.log('\n🔧 演示错误恢复机制...');

  // 创建一个会失败的工作流
  const failingWorkflow = {
    name: '错误恢复演示流程',
    description: '演示各种错误场景和恢复机制',
    definition: {
      steps: [
        {
          index: 0,
          name: '正常步骤',
          type: WorkflowStepType.HTTP_REQUEST,
          config: {
            method: 'GET',
            url: 'https://httpbin.org/get'
          }
        },
        {
          index: 1,
          name: '故意失败步骤',
          type: WorkflowStepType.HTTP_REQUEST,
          config: {
            method: 'GET',
            url: 'https://httpbin.org/status/500' // 会返回500错误
          }
        },
        {
          index: 2,
          name: '不会执行的步骤',
          type: WorkflowStepType.NOTIFICATION,
          config: {
            type: 'email',
            message: '这个步骤不会执行',
            recipients: ['<EMAIL>']
          }
        }
      ]
    }
  };

  try {
    // 创建失败测试工作流
    const workflow = await workflowEngine.createWorkflow(
      failingWorkflow.name,
      failingWorkflow.description,
      failingWorkflow.definition,
      'system'
    );

    // 执行工作流（预期会失败）
    const sessionId = await workflowCoordinator.startWorkflowExecution(
      workflow.id,
      { testMode: true }
    );

    console.log(`🚨 启动故意失败的工作流: ${sessionId}`);

    // 等待执行完成
    await new Promise(resolve => setTimeout(resolve, 8000));

    const session = workflowCoordinator.getExecutionSession(sessionId);
    if (session) {
      console.log('🔍 错误恢复结果:');
      console.log(`  执行状态: ${session.status}`);
      console.log(`  完成步骤: ${session.currentStep}/${session.totalSteps}`);
      console.log(`  遇到的错误: ${session.errors.length}`);
      
      if (session.errors.length > 0) {
        console.log('  错误详情:');
        session.errors.forEach((error, index) => {
          console.log(`    ${index + 1}. ${error.code}: ${error.message}`);
        });
      }
    }

  } catch (error: any) {
    console.error('错误恢复演示失败:', error);
  }
}

/**
 * 主演示函数
 */
async function main(): Promise<void> {
  try {
    console.log('🚀 开始工作流集成系统演示...\n');

    await demoWorkflowIntegration();
    await demoErrorRecovery();

    console.log('\n🎉 所有演示完成!');
    
  } catch (error) {
    console.error('❌ 演示过程中发生错误:', error);
  } finally {
    // 清理资源
    console.log('\n🧹 开始清理资源...');
    try {
      await workflowCoordinator.cleanup();
      console.log('✅ 资源清理完成');
    } catch (error) {
      console.error('❌ 资源清理失败:', error);
    }
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main().catch(error => {
    console.error('演示脚本执行失败:', error);
    process.exit(1);
  });
}

export { main as demoWorkflowIntegration };