/**
 * ActionExecutor系统演示脚本
 * 展示各种执行器的功能和用法
 */

import { v4 as uuidv4 } from 'uuid';
import { WorkflowStepType } from '@prisma/client';

// 导入执行器注册器和相关服务
import { ActionExecutorRegistry } from '../services/action-executor-registry.service';
import { stepExecutionEngine } from '../services/step-execution-engine.service';
import { executionContextManager } from '../services/execution-context-manager.service';
import { executionStateTracker } from '../services/execution-state-tracker.service';
import type { WorkflowStep } from '../types/action-executor.types';

/**
 * 演示HTTP请求执行器
 */
async function demoHttpExecutor(): Promise<void> {
  console.log('\n=== 演示HTTP请求执行器 ===');
  
  const executionId = `demo_http_${uuidv4()}`;
  const workflowId = `workflow_${uuidv4()}`;

  const httpStep: WorkflowStep = {
    index: 1,
    name: '获取用户信息',
    type: WorkflowStepType.HTTP_REQUEST,
    config: {
      method: 'GET',
      url: 'https://httpbin.org/get',
      headers: {
        'User-Agent': 'ActionExecutor-Demo/1.0'
      },
      timeout: 10000,
      validateResponse: true
    }
  };

  try {
    const result = await stepExecutionEngine.executeStep(
      executionId,
      workflowId,
      httpStep,
      { userId: 'demo-user-123' }
    );

    console.log('HTTP执行结果:', {
      success: result.success,
      statusCode: result.data?.statusCode,
      executionTime: result.executionTime
    });

    if (!result.success && result.error) {
      console.error('HTTP执行错误:', result.error);
    }
  } catch (error) {
    console.error('HTTP演示异常:', error);
  }
}

/**
 * 演示数据库执行器
 */
async function demoDatabaseExecutor(): Promise<void> {
  console.log('\n=== 演示数据库执行器 ===');
  
  const executionId = `demo_db_${uuidv4()}`;
  const workflowId = `workflow_${uuidv4()}`;

  const dbStep: WorkflowStep = {
    index: 1,
    name: '查询客户数据',
    type: WorkflowStepType.DATABASE_QUERY,
    config: {
      operation: 'SELECT',
      query: 'SELECT id, name, email FROM customers WHERE level = $1 LIMIT $2',
      parameters: {
        '$1': 'VIP',
        '$2': 10
      },
      timeout: 5000,
      transactional: false
    }
  };

  try {
    const result = await stepExecutionEngine.executeStep(
      executionId,
      workflowId,
      dbStep,
      { customerLevel: 'VIP' }
    );

    console.log('数据库执行结果:', {
      success: result.success,
      operation: result.data?.operation,
      affectedRows: result.data?.affectedRows,
      executionTime: result.executionTime
    });

    if (!result.success && result.error) {
      console.error('数据库执行错误:', result.error);
    }
  } catch (error) {
    console.error('数据库演示异常:', error);
  }
}

/**
 * 演示通知执行器
 */
async function demoNotificationExecutor(): Promise<void> {
  console.log('\n=== 演示通知执行器 ===');
  
  const executionId = `demo_notification_${uuidv4()}`;
  const workflowId = `workflow_${uuidv4()}`;

  const notificationStep: WorkflowStep = {
    index: 1,
    name: '发送工单通知',
    type: WorkflowStepType.NOTIFICATION,
    config: {
      type: 'multi',
      message: {
        subject: '新工单创建通知',
        content: '您有一个新的服务工单需要处理，工单号：${ticketNumber}，优先级：${priority}'
      },
      channels: [
        { type: 'email', config: { recipients: ['<EMAIL>'] } },
        { type: 'slack', config: { channel: '#ops-alerts' } }
      ],
      priority: 'high'
    }
  };

  try {
    const result = await stepExecutionEngine.executeStep(
      executionId,
      workflowId,
      notificationStep,
      { 
        ticketNumber: 'TICKET-2024-001',
        priority: 'HIGH'
      }
    );

    console.log('通知执行结果:', {
      success: result.success,
      successCount: result.data?.successCount,
      failureCount: result.data?.failureCount,
      executionTime: result.executionTime
    });

    if (!result.success && result.error) {
      console.error('通知执行错误:', result.error);
    }
  } catch (error) {
    console.error('通知演示异常:', error);
  }
}

/**
 * 演示业务逻辑执行器
 */
async function demoBusinessExecutor(): Promise<void> {
  console.log('\n=== 演示业务逻辑执行器 ===');
  
  const executionId = `demo_business_${uuidv4()}`;
  const workflowId = `workflow_${uuidv4()}`;

  const businessStep: WorkflowStep = {
    index: 1,
    name: '创建服务工单',
    type: WorkflowStepType.BUSINESS_LOGIC,
    config: {
      action: 'create_service_ticket',
      parameters: {
        title: '系统性能优化请求',
        description: '客户反馈系统响应时间较慢，需要进行性能优化',
        category: 'MAINTENANCE',
        priority: 'HIGH',
        customerId: 'customer-123',
        assignedToId: 'engineer-456'
      },
      timeout: 15000
    }
  };

  try {
    const result = await stepExecutionEngine.executeStep(
      executionId,
      workflowId,
      businessStep,
      { requestor: 'demo-user' }
    );

    console.log('业务逻辑执行结果:', {
      success: result.success,
      action: result.data?.action,
      serviceId: result.data?.result?.serviceId,
      ticketNumber: result.data?.result?.ticketNumber,
      executionTime: result.executionTime
    });

    if (!result.success && result.error) {
      console.error('业务逻辑执行错误:', result.error);
    }
  } catch (error) {
    console.error('业务逻辑演示异常:', error);
  }
}

/**
 * 演示条件执行器
 */
async function demoConditionalExecutor(): Promise<void> {
  console.log('\n=== 演示条件执行器 ===');
  
  const executionId = `demo_conditional_${uuidv4()}`;
  const workflowId = `workflow_${uuidv4()}`;

  const conditionalStep: WorkflowStep = {
    index: 1,
    name: '检查服务等级',
    type: WorkflowStepType.CONDITIONAL,
    config: {
      condition: {
        operator: 'equals',
        left: '${customerLevel}',
        right: 'VIP'
      },
      trueAction: {
        type: 'continue',
        message: '客户为VIP等级，继续高优先级处理',
        data: { priority: 'HIGH', slaHours: 4 }
      },
      falseAction: {
        type: 'continue',
        message: '客户为普通等级，标准处理流程',
        data: { priority: 'MEDIUM', slaHours: 24 }
      }
    }
  };

  try {
    // 测试VIP客户场景
    const vipResult = await stepExecutionEngine.executeStep(
      executionId + '_vip',
      workflowId,
      conditionalStep,
      { customerLevel: 'VIP' }
    );

    console.log('VIP客户条件判断结果:', {
      success: vipResult.success,
      conditionResult: vipResult.data?.conditionResult,
      branchExecuted: vipResult.data?.branchExecuted,
      message: vipResult.data?.branchResult?.message,
      executionTime: vipResult.executionTime
    });

    // 测试普通客户场景
    const normalResult = await stepExecutionEngine.executeStep(
      executionId + '_normal',
      workflowId,
      conditionalStep,
      { customerLevel: 'STANDARD' }
    );

    console.log('普通客户条件判断结果:', {
      success: normalResult.success,
      conditionResult: normalResult.data?.conditionResult,
      branchExecuted: normalResult.data?.branchExecuted,
      message: normalResult.data?.branchResult?.message,
      executionTime: normalResult.executionTime
    });

  } catch (error) {
    console.error('条件判断演示异常:', error);
  }
}

/**
 * 演示批量执行
 */
async function demoBatchExecution(): Promise<void> {
  console.log('\n=== 演示批量执行 ===');
  
  const executionId = `demo_batch_${uuidv4()}`;
  const workflowId = `workflow_${uuidv4()}`;

  const steps: WorkflowStep[] = [
    {
      index: 1,
      name: '条件检查',
      type: WorkflowStepType.CONDITIONAL,
      config: {
        condition: {
          operator: 'exists',
          left: '${customerId}'
        },
        trueAction: { type: 'continue', message: '客户ID有效' },
        falseAction: { type: 'fail', message: '缺少客户ID' }
      }
    },
    {
      index: 2,
      name: '创建工单',
      type: WorkflowStepType.BUSINESS_LOGIC,
      config: {
        action: 'create_service_ticket',
        parameters: {
          title: '批量处理演示工单',
          description: '这是一个批量执行的演示工单',
          category: 'SUPPORT',
          priority: 'MEDIUM',
          customerId: '${customerId}'
        }
      }
    },
    {
      index: 3,
      name: '发送通知',
      type: WorkflowStepType.NOTIFICATION,
      config: {
        type: 'email',
        message: '工单 ${ticketNumber} 已创建成功',
        subject: '工单创建通知',
        recipients: ['<EMAIL>']
      }
    }
  ];

  try {
    const results = await stepExecutionEngine.executeBatch(
      executionId,
      workflowId,
      steps,
      { customerId: 'batch-customer-123' }
    );

    console.log('批量执行结果:');
    results.forEach((result, index) => {
      console.log(`  步骤 ${index + 1}: ${result.success ? '成功' : '失败'} (${result.executionTime}s)`);
      if (!result.success && result.error) {
        console.log(`    错误: ${result.error.message}`);
      }
    });

    const successCount = results.filter(r => r.success).length;
    console.log(`批量执行完成: ${successCount}/${results.length} 步骤成功`);

  } catch (error) {
    console.error('批量执行演示异常:', error);
  }
}

/**
 * 演示系统统计
 */
async function demoSystemStats(): Promise<void> {
  console.log('\n=== 系统统计信息 ===');
  
  try {
    // 执行引擎统计
    const engineStats = stepExecutionEngine.getExecutionStats();
    console.log('执行引擎统计:', {
      running: engineStats.running,
      queued: engineStats.queued,
      utilizationRate: `${engineStats.utilizationRate.toFixed(1)}%`
    });

    // 状态跟踪器统计
    const trackerStats = executionStateTracker.getStatistics();
    console.log('状态跟踪器统计:', {
      totalExecutions: trackerStats.totalExecutions,
      averageDuration: `${trackerStats.averageDuration.toFixed(2)}s`,
      cacheSize: trackerStats.cacheSize
    });

    // 上下文管理器统计
    const contextStats = executionContextManager.getSystemStats();
    console.log('上下文管理器统计:', {
      totalContexts: contextStats.totalContexts,
      totalResourcePools: contextStats.totalResourcePools,
      activeExecutions: contextStats.activeExecutions.length
    });

  } catch (error) {
    console.error('获取系统统计异常:', error);
  }
}

/**
 * 主演示函数
 */
async function main(): Promise<void> {
  try {
    console.log('🚀 开始ActionExecutor系统演示...\n');

    // 确保执行器已注册和初始化
    await ActionExecutorRegistry.registerAllExecutors();
    
    // 初始化执行引擎
    await stepExecutionEngine.initialize();
    
    console.log('✅ 系统初始化完成\n');

    // 运行各种演示
    await demoHttpExecutor();
    await demoDatabaseExecutor();
    await demoNotificationExecutor();
    await demoBusinessExecutor();
    await demoConditionalExecutor();
    await demoBatchExecution();
    await demoSystemStats();

    console.log('\n🎉 所有演示完成!');
    
  } catch (error) {
    console.error('❌ 演示过程中发生错误:', error);
  } finally {
    // 清理资源
    console.log('\n🧹 开始清理资源...');
    try {
      await stepExecutionEngine.cleanup();
      await executionStateTracker.cleanup();
      await executionContextManager.cleanup();
      console.log('✅ 资源清理完成');
    } catch (error) {
      console.error('❌ 资源清理失败:', error);
    }
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main().catch(error => {
    console.error('演示脚本执行失败:', error);
    process.exit(1);
  });
}

export { main as demoActionExecutors };