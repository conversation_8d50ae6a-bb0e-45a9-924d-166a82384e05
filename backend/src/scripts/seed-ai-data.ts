/**
 * AI服务初始数据种子脚本
 */

import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function seedAIData() {
  console.log('开始初始化AI服务数据...')

  try {
    // 1. 创建默认AI配置
    const existingConfig = await prisma.aIConfiguration.findFirst()
    if (!existingConfig) {
      await prisma.aIConfiguration.create({
        data: {
          provider: 'openai',
          model: 'gpt-4o-mini',
          temperature: 0.3,
          maxTokens: 1000,
          timeout: 30000,
          mode: 'USER_TRIGGERED',
          enabledFields: {
            title: true,
            category: true,
            priority: true,
            slaTemplate: true
          },
          autoFillThreshold: 0.8,
          isActive: true
        }
      })
      console.log('✓ 创建默认AI配置')
    } else {
      console.log('✓ AI配置已存在，跳过创建')
    }

    // 2. 创建默认提示词模板
    const templates = [
      {
        name: '工单分析通用模板',
        category: 'ticket_analysis',
        template: `
你是一个专业的IT运维工单智能分析助手。请仔细分析以下工单描述，并基于语义理解和上下文判断，返回准确的分类建议。

## 工单信息
**描述内容：** {description}
**客户类型：** {customerType}
**行业领域：** {industry}
**历史模式：** {historyPattern}

## 分析任务
请分析工单的真实意图和需求，返回标准JSON格式的结果：

\`\`\`json
{
  "title": {
    "suggested": "简洁清晰的工单标题(不超过50字)",
    "confidence": 85
  },
  "category": {
    "suggested": "服务类别代码",
    "confidence": 90,
    "reasoning": "详细的分类理由和判断依据"
  },
  "priority": {
    "suggested": "优先级代码",
    "confidence": 80,
    "reasoning": "优先级判断的具体理由"
  },
  "slaTemplate": {
    "suggested": "推荐的SLA模板",
    "confidence": 75,
    "reasoning": "SLA模板选择理由"
  }
}
\`\`\`

## 分类标准
**服务类别：**
- MAINTENANCE: 系统维护、定期保养
- SUPPORT: 技术支持、问题咨询
- UPGRADE: 系统升级、功能增强
- BUGFIX: 故障修复、问题解决
- CONSULTING: 技术咨询、方案建议
- MONITORING: 监控告警、性能分析

**优先级：**
- LOW: 一般需求，不影响业务
- MEDIUM: 中等重要，有一定影响
- HIGH: 重要紧急，影响业务运行
- URGENT: 极其紧急，严重影响或中断业务

请确保返回的JSON格式正确，置信度为0-100的整数。
        `.trim(),
        variables: ['description', 'customerType', 'industry', 'historyPattern'],
        provider: null,
        version: '1.0',
        isActive: true
      },
      {
        name: 'OpenAI专用工单分析模板',
        category: 'ticket_analysis',
        template: `
作为专业的IT运维工单分析助手，请分析以下工单内容并返回JSON格式的分类建议。

工单描述：{description}
客户信息：{customerType} - {industry}
历史模式：{historyPattern}

请返回以下格式的JSON响应：
{
  "title": {"suggested": "标题", "confidence": 数字},
  "category": {"suggested": "类别", "confidence": 数字, "reasoning": "理由"},
  "priority": {"suggested": "优先级", "confidence": 数字, "reasoning": "理由"},
  "slaTemplate": {"suggested": "SLA模板", "confidence": 数字, "reasoning": "理由"}
}

类别选项：MAINTENANCE, SUPPORT, UPGRADE, BUGFIX, CONSULTING, MONITORING
优先级选项：LOW, MEDIUM, HIGH, URGENT
        `.trim(),
        variables: ['description', 'customerType', 'industry', 'historyPattern'],
        provider: 'openai',
        version: '1.0',
        isActive: true
      },
      {
        name: 'Claude专用工单分析模板',
        category: 'ticket_analysis',
        template: `
我需要你作为IT运维专家分析工单内容。

工单详情：
- 描述：{description}
- 客户类型：{customerType}
- 行业：{industry}
- 历史模式：{historyPattern}

请提供JSON格式的分析结果，包含标题建议、类别分类、优先级评估和SLA模板推荐。

格式要求：
{
  "title": {"suggested": "建议标题", "confidence": 置信度},
  "category": {"suggested": "MAINTENANCE|SUPPORT|UPGRADE|BUGFIX|CONSULTING|MONITORING", "confidence": 置信度, "reasoning": "分类理由"},
  "priority": {"suggested": "LOW|MEDIUM|HIGH|URGENT", "confidence": 置信度, "reasoning": "优先级理由"},
  "slaTemplate": {"suggested": "SLA模板名称", "confidence": 置信度, "reasoning": "选择理由"}
}
        `.trim(),
        variables: ['description', 'customerType', 'industry', 'historyPattern'],
        provider: 'anthropic',
        version: '1.0',
        isActive: true
      }
    ]

    // 获取系统管理员用户ID（用于创建模板）
    const adminUser = await prisma.user.findFirst({
      where: {
        userRoles: {
          some: {
            role: {
              name: 'admin'
            }
          }
        }
      }
    })

    if (!adminUser) {
      console.warn('⚠ 未找到管理员用户，使用第一个用户作为创建者')
    }

    const creatorId = adminUser?.id || (await prisma.user.findFirst())?.id

    if (!creatorId) {
      throw new Error('未找到任何用户，无法创建提示词模板')
    }

    for (const template of templates) {
      const existing = await prisma.aIPromptTemplate.findFirst({
        where: {
          name: template.name,
          category: template.category
        }
      })

      if (!existing) {
        await prisma.aIPromptTemplate.create({
          data: {
            ...template,
            createdBy: creatorId
          }
        })
        console.log(`✓ 创建提示词模板: ${template.name}`)
      } else {
        console.log(`✓ 提示词模板已存在: ${template.name}`)
      }
    }

    console.log('✅ AI服务数据初始化完成')

  } catch (error) {
    console.error('❌ AI服务数据初始化失败:', error)
    throw error
  } finally {
    await prisma.$disconnect()
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  seedAIData()
    .then(() => {
      console.log('脚本执行完成')
      process.exit(0)
    })
    .catch((error) => {
      console.error('脚本执行失败:', error)
      process.exit(1)
    })
}

export { seedAIData }
