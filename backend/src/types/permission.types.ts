/**
 * 权限系统类型定义
 * 
 * 定义权限验证相关的TypeScript类型
 */

import { Request } from 'express'
import { Permission, PermissionGroupName, RoleName } from '@/constants/permissions'

// ==================== 基础类型 ====================

/**
 * 权限检查结果
 */
export interface PermissionCheckResult {
  hasPermission: boolean
  missingPermissions?: string[]
  reason?: string
}

/**
 * 用户权限信息
 */
export interface UserPermissionInfo {
  userId: string
  username: string
  email: string
  roles: string[]
  permissions: string[]
  isAdmin: boolean
}

/**
 * 权限验证配置
 */
export interface PermissionValidationConfig {
  requiredPermissions: string[]
  requireAll?: boolean // 是否需要所有权限，默认true
  allowSuperAdmin?: boolean // 是否允许超级管理员绕过，默认true
  customCheck?: (userInfo: UserPermissionInfo, req: Request) => boolean | Promise<boolean>
}

/**
 * 资源访问权限配置
 */
export interface ResourceAccessConfig {
  resourceType: string
  resourceId: string
  ownershipCheck?: (req: Request) => string | Promise<string>
  adminPermissions?: string[]
  readPermissions?: string[]
  writePermissions?: string[]
}

/**
 * 权限装饰器选项
 */
export interface PermissionDecoratorOptions {
  permissions: string[]
  requireAll?: boolean
  allowSuperAdmin?: boolean
  customErrorMessage?: string
  logAccess?: boolean
  rateLimit?: {
    windowMs: number
    maxRequests: number
  }
}

// ==================== 权限策略类型 ====================

/**
 * 权限策略类型
 */
export enum PermissionStrategy {
  STRICT = 'strict',           // 严格模式：必须拥有所有权限
  FLEXIBLE = 'flexible',       // 灵活模式：拥有任一权限即可
  OWNERSHIP = 'ownership',     // 所有权模式：资源所有者或管理员
  CONDITIONAL = 'conditional', // 条件模式：根据条件动态检查
  HIERARCHICAL = 'hierarchical' // 层级模式：支持权限继承
}

/**
 * 权限策略配置
 */
export interface PermissionPolicyConfig {
  strategy: PermissionStrategy
  permissions: string[]
  options?: {
    requireAll?: boolean
    allowSuperAdmin?: boolean
    resourceType?: string
    ownershipField?: string
    condition?: (req: Request) => boolean | Promise<boolean>
    inheritanceRules?: Record<string, string[]>
  }
}

// ==================== 权限验证函数类型 ====================

/**
 * 权限检查函数类型
 */
export type PermissionChecker = (
  userPermissions: string[],
  requiredPermissions: string | string[]
) => boolean

/**
 * 条件权限检查函数类型
 */
export type ConditionalPermissionChecker = (
  req: Request,
  userPermissions: string[]
) => boolean | Promise<boolean>

/**
 * 资源所有权检查函数类型
 */
export type OwnershipChecker = (
  req: Request,
  resourceId?: string
) => string | Promise<string>

// ==================== 权限管理类型 ====================

/**
 * 权限分组信息
 */
export interface PermissionGroupInfo {
  key: PermissionGroupName
  name: string
  description: string
  permissions: Permission[]
  permissionCount: number
}

/**
 * 角色权限映射
 */
export interface RolePermissionMapping {
  roleId: string
  roleName: RoleName
  permissions: Permission[]
  permissionCount: number
  userCount: number
  isDefault: boolean
}

/**
 * 权限统计信息
 */
export interface PermissionStats {
  totalPermissions: number
  usedPermissions: number
  unusedPermissions: number
  coverageRate: number
  roleStats: Array<{
    roleName: string
    permissionCount: number
    userCount: number
  }>
  userStats: Array<{
    userId: string
    username: string
    permissionCount: number
    hasAdminPermission: boolean
  }>
}

// ==================== 权限验证结果类型 ====================

/**
 * 权限验证详细结果
 */
export interface DetailedPermissionResult {
  success: boolean
  userInfo: UserPermissionInfo
  requiredPermissions: string[]
  grantedPermissions: string[]
  missingPermissions: string[]
  checkResults: Array<{
    permission: string
    granted: boolean
    source: 'direct' | 'inherited' | 'super_admin'
  }>
}

/**
 * 批量权限检查结果
 */
export interface BatchPermissionResult {
  overall: boolean
  results: Array<{
    userId: string
    username: string
    hasAllPermissions: boolean
    permissionChecks: Array<{
      permission: string
      granted: boolean
      reason?: string
    }>
  }>
}

// ==================== 权限配置类型 ====================

/**
 * 系统权限配置
 */
export interface SystemPermissionConfig {
  enablePermissionCache: boolean
  cacheExpiration: number
  enableAuditLog: boolean
  enableRoleInheritance: boolean
  defaultPermissions: string[]
  adminPermissions: string[]
  publicPermissions: string[]
}

/**
 * 权限中间件配置
 */
export interface PermissionMiddlewareConfig {
  skipAuthPaths: string[]
  publicPaths: string[]
  adminPaths: string[]
  rateLimitConfig: {
    windowMs: number
    maxRequests: number
    skipSuccessfulRequests: boolean
  }
}

// ==================== 动态权限类型 ====================

/**
 * 动态权限规则
 */
export interface DynamicPermissionRule {
  id: string
  name: string
  description: string
  condition: string | ((req: Request, user: UserPermissionInfo) => boolean)
  grantedPermissions: string[]
  priority: number
  enabled: boolean
}

/**
 * 权限上下文
 */
export interface PermissionContext {
  user: UserPermissionInfo
  request: Request
  resource?: {
    type: string
    id: string
    ownerId?: string
  }
  action: string
  timestamp: Date
}

// ==================== 权限事件类型 ====================

/**
 * 权限事件类型
 */
export enum PermissionEventType {
  PERMISSION_GRANTED = 'permission_granted',
  PERMISSION_DENIED = 'permission_denied',
  ROLE_ASSIGNED = 'role_assigned',
  ROLE_REVOKED = 'role_revoked',
  PERMISSION_UPDATED = 'permission_updated'
}

/**
 * 权限事件数据
 */
export interface PermissionEvent {
  type: PermissionEventType
  userId: string
  targetUserId?: string
  roleId?: string
  permissions?: string[]
  resource?: string
  action: string
  timestamp: Date
  metadata?: Record<string, any>
}

// ==================== 导出类型联合 ====================

/**
 * 所有权限相关类型的联合导出
 */
export type PermissionTypes = {
  // 基础类型
  PermissionCheckResult
  UserPermissionInfo
  PermissionValidationConfig
  ResourceAccessConfig
  PermissionDecoratorOptions

  // 策略类型
  PermissionStrategy
  PermissionPolicyConfig

  // 函数类型
  PermissionChecker
  ConditionalPermissionChecker
  OwnershipChecker

  // 管理类型
  PermissionGroupInfo
  RolePermissionMapping
  PermissionStats

  // 结果类型
  DetailedPermissionResult
  BatchPermissionResult

  // 配置类型
  SystemPermissionConfig
  PermissionMiddlewareConfig

  // 动态权限类型
  DynamicPermissionRule
  PermissionContext

  // 事件类型
  PermissionEventType
  PermissionEvent
}

// ==================== 类型守卫 ====================

/**
 * 检查是否为有效的权限字符串
 */
export function isValidPermissionString(value: any): value is string {
  return typeof value === 'string' && /^[a-z_]+:[a-z_]+(?::[a-z_]+)*$/.test(value)
}

/**
 * 检查是否为权限数组
 */
export function isPermissionArray(value: any): value is string[] {
  return Array.isArray(value) && value.every(isValidPermissionString)
}

/**
 * 检查是否为用户权限信息
 */
export function isUserPermissionInfo(value: any): value is UserPermissionInfo {
  return (
    typeof value === 'object' &&
    value !== null &&
    typeof value.userId === 'string' &&
    typeof value.username === 'string' &&
    typeof value.email === 'string' &&
    Array.isArray(value.roles) &&
    Array.isArray(value.permissions) &&
    typeof value.isAdmin === 'boolean'
  )
}

/**
 * 检查是否为权限验证结果
 */
export function isPermissionCheckResult(value: any): value is PermissionCheckResult {
  return (
    typeof value === 'object' &&
    value !== null &&
    typeof value.hasPermission === 'boolean'
  )
}