import { AlertSeverity } from '@prisma/client'

// 系统监控数据类型
export interface SystemMetrics {
  timestamp: string
  cpu: {
    usage: number
    cores: number
    loadAverage: number[]
    processes: ProcessInfo[]
  }
  memory: {
    total: number
    used: number
    free: number
    available: number
    usage: number
    cached: number
    buffers: number
  }
  disk: {
    total: number
    used: number
    free: number
    usage: number
    readSpeed: number
    writeSpeed: number
    iops: number
  }
  network: {
    interfaces: NetworkInterface[]
    totalRxBytes: number
    totalTxBytes: number
    totalRxSpeed: number
    totalTxSpeed: number
  }
  processes: {
    total: number
    running: number
    sleeping: number
    zombie: number
    stopped: number
  }
}

export interface ProcessInfo {
  pid: number
  name: string
  cpu: number
  memory: number
  state: string
}

export interface NetworkInterface {
  name: string
  ip4: string
  ip6: string
  mac: string
  internal: boolean
  virtual: boolean
  speed: number
  rxBytes: number
  txBytes: number
  rxSpeed: number
  txSpeed: number
}

// 服务健康检查
export interface ServiceHealth {
  name: string
  status: 'healthy' | 'warning' | 'critical' | 'down'
  responseTime: number
  lastCheck: string
  uptime: string
  details: string
  metrics?: any
}

// 数据库健康状态
export interface DatabaseHealth extends ServiceHealth {
  connections: {
    active: number
    idle: number
    max: number
  }
  performance: {
    queryCount: number
    slowQueries: number
    avgResponseTime: number
  }
  statistics: {
    totalTables: number
    totalRecords: number
    dbSize: number
  }
}

// Redis健康状态
export interface RedisHealth extends ServiceHealth {
  info: {
    version: string
    mode: string
    role: string
  }
  memory: {
    used: number
    peak: number
    fragmentation: number
  }
  stats: {
    keyCount: number
    hitRate: number
    operationsPerSec: number
  }
}

// Node.js应用健康状态
export interface ApplicationHealth extends ServiceHealth {
  version: string
  uptime: number
  memoryUsage: {
    rss: number
    heapTotal: number
    heapUsed: number
    external: number
    arrayBuffers: number
  }
  eventLoop: {
    delay: number
    utilization: number
  }
  gc: {
    collections: number
    duration: number
  }
}

// 智能分析结果
export interface IntelligentAnalysis {
  score: number // 健康评分 0-100
  level: 'excellent' | 'good' | 'warning' | 'critical'
  insights: AnalysisInsight[]
  predictions: PerformancePrediction[]
  recommendations: Recommendation[]
  trends: TrendAnalysis[]
  anomalies: AnomalyDetection[]
}

export interface AnalysisInsight {
  category: string
  title: string
  description: string
  severity: 'info' | 'warning' | 'critical'
  confidence: number
  impact: 'low' | 'medium' | 'high'
  source: string
}

export interface PerformancePrediction {
  metric: string
  currentValue: number
  predictedValue: number
  timeframe: string
  confidence: number
  trend: 'increasing' | 'decreasing' | 'stable' | 'volatile'
  riskLevel: 'low' | 'medium' | 'high'
}

export interface Recommendation {
  id: string
  category: string
  title: string
  description: string
  priority: 'low' | 'medium' | 'high' | 'urgent'
  difficulty: 'easy' | 'medium' | 'hard'
  estimatedImpact: string
  actionItems: string[]
  resources?: string[]
}

export interface TrendAnalysis {
  metric: string
  period: string
  trend: 'upward' | 'downward' | 'stable' | 'cyclical'
  changeRate: number
  significantEvents: TrendEvent[]
}

export interface TrendEvent {
  timestamp: string
  type: 'spike' | 'drop' | 'threshold' | 'anomaly'
  value: number
  description: string
}

export interface AnomalyDetection {
  timestamp: string
  metric: string
  actualValue: number
  expectedValue: number
  deviation: number
  severity: AlertSeverity
  description: string
  confidence: number
  possibleCauses: string[]
}

// 监控配置
export interface MonitoringConfig {
  enabled: boolean
  interval: number // 采集间隔（秒）
  retention: {
    realtime: number // 实时数据保留时间（小时）
    hourly: number // 小时数据保留时间（天）
    daily: number // 日数据保留时间（月）
  }
  thresholds: {
    cpu: ThresholdConfig
    memory: ThresholdConfig
    disk: ThresholdConfig
    network: ThresholdConfig
  }
  alerting: AlertingConfig
  analysis: AnalysisConfig
}

export interface ThresholdConfig {
  warning: number
  critical: number
  duration: number // 持续时间（秒）
}

export interface AlertingConfig {
  enabled: boolean
  channels: string[]
  cooldown: number // 冷却时间（秒）
  escalation: {
    enabled: boolean
    levels: EscalationLevel[]
  }
}

export interface EscalationLevel {
  level: number
  threshold: number
  delay: number
  channels: string[]
}

export interface AnalysisConfig {
  enabled: boolean
  aiModel: {
    enabled: boolean
    provider: string
    model: string
    apiKey?: string
  }
  historicalWindow: number // 历史数据窗口（小时）
  predictionWindow: number // 预测窗口（小时）
  anomalyDetection: {
    enabled: boolean
    sensitivity: number // 敏感度 0.1-1.0
    algorithm: 'statistical' | 'ml' | 'hybrid'
  }
}

// API响应类型
export interface MonitoringDashboard {
  summary: HealthSummary
  metrics: SystemMetrics
  services: ServiceHealth[]
  analysis: IntelligentAnalysis
  alerts: ActiveAlert[]
  lastUpdated: string
}

export interface HealthSummary {
  overall: 'healthy' | 'warning' | 'critical'
  score: number
  uptime: string
  issues: number
  criticalIssues: number
}

export interface ActiveAlert {
  id: string
  type: string
  severity: AlertSeverity
  title: string
  message: string
  timestamp: string
  status: 'active' | 'acknowledged' | 'resolved'
  source: string
}

// 历史数据查询
export interface MetricsQuery {
  metrics: string[]
  timeRange: {
    start: Date
    end: Date
  }
  interval: 'minute' | 'hour' | 'day'
  aggregation?: 'avg' | 'max' | 'min' | 'sum'
}

export interface MetricsResponse {
  data: MetricDataPoint[]
  pagination: {
    total: number
    page: number
    limit: number
  }
}

export interface MetricDataPoint {
  timestamp: string
  metrics: Record<string, number>
}

// 性能基准
export interface PerformanceBenchmark {
  category: string
  metric: string
  baseline: number
  target: number
  current: number
  percentile: number
  comparison: 'above' | 'below' | 'within'
  recommendation?: string
}

// 资源使用预警
export interface ResourceAlert {
  resource: string
  current: number
  threshold: number
  trend: 'increasing' | 'decreasing' | 'stable'
  timeToThreshold?: number // 预计到达阈值的时间（分钟）
  recommendation: string
}

export enum MetricType {
  CPU = 'cpu',
  MEMORY = 'memory',
  DISK = 'disk',
  NETWORK = 'network',
  DATABASE = 'database',
  REDIS = 'redis',
  APPLICATION = 'application',
  CUSTOM = 'custom'
}

export enum AlertType {
  THRESHOLD = 'threshold',
  ANOMALY = 'anomaly',
  PREDICTION = 'prediction',
  SERVICE_DOWN = 'service_down',
  PERFORMANCE = 'performance'
}