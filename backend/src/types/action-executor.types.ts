/**
 * ActionExecutor执行器系统类型定义
 */

import type { WorkflowStepType } from '@prisma/client';

// ============= 基础类型 =============

export enum ErrorType {
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  EXECUTION_ERROR = 'EXECUTION_ERROR', 
  TIMEOUT_ERROR = 'TIMEOUT_ERROR',
  NETWORK_ERROR = 'NETWORK_ERROR',
  AUTHENTICATION_ERROR = 'AUTHENTICATION_ERROR',
  AUTHORIZATION_ERROR = 'AUTHORIZATION_ERROR',
  RESOURCE_ERROR = 'RESOURCE_ERROR',
  DATA_ERROR = 'DATA_ERROR',
  SYSTEM_ERROR = 'SYSTEM_ERROR'
}

export enum ErrorSeverity {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM', 
  HIGH = 'HIGH',
  CRITICAL = 'CRITICAL'
}

export enum ErrorHandlingType {
  RETRY = 'RETRY',
  SKIP = 'SKIP',
  FAIL = 'FAIL',
  ROLLBACK = 'ROLLBACK',
  FALLBACK = 'FALLBACK'
}

export enum ExecutionStatus {
  PENDING = 'PENDING',
  RUNNING = 'RUNNING',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
  SKIPPED = 'SKIPPED',
  RETRYING = 'RETRYING'
}

// ============= 执行接口 =============

export interface WorkflowStep {
  index: number;
  name: string;
  type: WorkflowStepType;
  config: any;
  condition?: any;
  timeout?: number;
  retry?: RetryConfig;
  errorHandling?: ErrorHandlingStrategy;
}

export interface RetryConfig {
  maxAttempts: number;
  delay: number;
  backoffMultiplier?: number;
  maxDelay?: number;
}

export interface ValidationResult {
  valid: boolean;
  errors: string[];
  warnings: string[];
}

export interface ExecutionError {
  type: ErrorType;
  code: string;
  message: string;
  details?: any;
  recoverable: boolean;
  severity: ErrorSeverity;
  stack?: string;
  timestamp: Date;
}

export interface ExecutionLog {
  level: 'DEBUG' | 'INFO' | 'WARN' | 'ERROR';
  message: string;
  timestamp: Date;
  metadata?: any;
  source?: string;
}

export interface ActionExecutionResult {
  success: boolean;
  data?: any;
  error?: ExecutionError;
  logs: ExecutionLog[];
  nextStep?: number;
  shouldRetry?: boolean;
  rollbackData?: any;
  executionTime?: number;
  resourceUsage?: ResourceUsage;
}

export interface RollbackResult {
  success: boolean;
  error?: ExecutionError;
  logs: ExecutionLog[];
  restoredState?: any;
}

// ============= 执行上下文 =============

export interface ContextMetadata {
  executionId: string;
  workflowId: string;
  stepIndex: number;
  attemptNumber: number;
  startTime: Date;
  timeout?: number;
  parentContext?: string;
}

export interface ResourcePool {
  httpClients: Map<string, any>;
  databaseConnections: Map<string, any>;
  cacheClients: Map<string, any>;
  messageQueues: Map<string, any>;
}

export interface ResourceUsage {
  cpu?: number;
  memory?: number;
  io?: number;
  network?: number;
  duration: number;
}

export interface ContextLogger {
  debug(message: string, metadata?: any): void;
  info(message: string, metadata?: any): void;
  warn(message: string, metadata?: any): void;
  error(message: string, error?: Error, metadata?: any): void;
}

export interface ExecutionContext {
  executionId: string;
  workflowId: string;
  stepIndex: number;
  variables: Record<string, any>;
  metadata: ContextMetadata;
  resources: ResourcePool;
  logger: ContextLogger;
  abortController: AbortController;
}

// ============= 错误处理 =============

export interface ErrorHandlingStrategy {
  type: ErrorHandlingType;
  maxRetries?: number;
  retryDelay?: number;
  fallbackAction?: WorkflowStep;
  rollbackSteps?: number[];
  continueOnError?: boolean;
  notifyOnError?: boolean;
}

// ============= 执行器接口 =============

export interface IActionExecutor {
  readonly type: WorkflowStepType;
  readonly name: string;
  readonly version: string;
  
  validate(step: WorkflowStep): Promise<ValidationResult>;
  execute(step: WorkflowStep, context: ExecutionContext): Promise<ActionExecutionResult>;
  rollback?(step: WorkflowStep, context: ExecutionContext): Promise<RollbackResult>;
  
  // 可选的生命周期方法
  initialize?(): Promise<void>;
  cleanup?(): Promise<void>;
  healthCheck?(): Promise<boolean>;
}

export interface IActionExecutorManager {
  registerExecutor(executor: IActionExecutor): void;
  unregisterExecutor(type: WorkflowStepType): void;
  getExecutor(type: WorkflowStepType): IActionExecutor | undefined;
  executeStep(step: WorkflowStep, context: ExecutionContext): Promise<ActionExecutionResult>;
  validateStep(step: WorkflowStep): Promise<ValidationResult>;
  rollbackStep(step: WorkflowStep, context: ExecutionContext): Promise<RollbackResult>;
  listExecutors(): IActionExecutor[];
  healthCheck(): Promise<Map<WorkflowStepType, boolean>>;
}

// ============= 状态跟踪 =============

export interface ExecutionState {
  executionId: string;
  workflowId: string;
  stepIndex: number;
  status: ExecutionStatus;
  startTime: Date;
  endTime?: Date;
  duration?: number;
  result?: ActionExecutionResult;
  error?: ExecutionError;
  logs: ExecutionLog[];
  retryCount: number;
  rollbackData?: any;
}

export interface IStateTracker {
  startExecution(executionId: string, workflowId: string, stepIndex: number): Promise<void>;
  updateStatus(executionId: string, stepIndex: number, status: ExecutionStatus): Promise<void>;
  completeExecution(executionId: string, stepIndex: number, result: ActionExecutionResult): Promise<void>;
  failExecution(executionId: string, stepIndex: number, error: ExecutionError): Promise<void>;
  addLog(executionId: string, stepIndex: number, log: ExecutionLog): Promise<void>;
  getExecutionState(executionId: string, stepIndex: number): Promise<ExecutionState | null>;
  listExecutions(workflowId?: string): Promise<ExecutionState[]>;
}

// ============= 资源管理 =============

export interface ResourceConfig {
  httpTimeout: number;
  maxHttpConnections: number;
  databasePoolSize: number;
  cacheTimeout: number;
  memoryLimit: number;
  cpuLimit: number;
}

export interface IResourceManager {
  getHttpClient(identifier: string): any;
  getDatabaseConnection(identifier: string): any;
  getCacheClient(identifier: string): any;
  releaseResources(context: ExecutionContext): Promise<void>;
  checkResourceUsage(): Promise<ResourceUsage>;
}

// ============= 特定动作类型配置 =============

export interface HttpActionConfig {
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  url: string;
  headers?: Record<string, string>;
  data?: any;
  timeout?: number;
  authentication?: {
    type: 'basic' | 'bearer' | 'api-key';
    credentials: any;
  };
  retryPolicy?: RetryConfig;
  validateResponse?: boolean;
  rollbackRequest?: HttpActionConfig;
}

export interface DatabaseActionConfig {
  operation: 'SELECT' | 'INSERT' | 'UPDATE' | 'DELETE' | 'EXECUTE';
  query: string;
  parameters?: Record<string, any>;
  timeout?: number;
  transactional?: boolean;
  rollbackQuery?: string;
}

export interface NotificationActionConfig {
  type: 'email' | 'sms' | 'webhook' | 'slack' | 'teams' | 'dingtalk' | 'multi';
  message: string | object;
  recipients?: string | string[];
  subject?: string;
  template?: string;
  priority?: 'low' | 'normal' | 'high' | 'urgent';
  channels?: Array<{
    type: string;
    config?: any;
  }>;
  retryPolicy?: {
    maxAttempts: number;
    delay: number;
    backoffMultiplier?: number;
  };
  rollbackNotification?: NotificationActionConfig;
}

export interface BusinessActionConfig {
  action: string;
  parameters?: Record<string, any>;
  timeout?: number;
  rollbackAction?: {
    action: string;
    parameters?: Record<string, any>;
  };
  validation?: {
    rules: string[];
    strict?: boolean;
  };
}

export interface ConditionalActionConfig {
  condition: {
    operator: 'equals' | 'not_equals' | 'greater_than' | 'less_than' | 'greater_equal' | 'less_equal' | 'contains' | 'not_contains' | 'exists' | 'not_exists' | 'in' | 'not_in' | 'regex' | 'and' | 'or' | 'not';
    left?: any;
    right?: any;
    conditions?: Array<any>;
  };
  trueAction?: {
    type: 'continue' | 'skip' | 'fail' | 'retry' | 'custom';
    data?: any;
    message?: string;
  };
  falseAction?: {
    type: 'continue' | 'skip' | 'fail' | 'retry' | 'custom';
    data?: any;
    message?: string;
  };
  timeout?: number;
  defaultResult?: boolean;
}

// ============= 监控和指标 =============

export interface ExecutionMetrics {
  totalExecutions: number;
  successfulExecutions: number;
  failedExecutions: number;
  avgExecutionTime: number;
  resourceUtilization: ResourceUsage;
  errorDistribution: Map<ErrorType, number>;
  performanceMetrics: Map<string, number>;
}

export interface IMetricsCollector {
  recordExecution(result: ActionExecutionResult): void;
  recordError(error: ExecutionError): void;
  recordLatency(executorType: WorkflowStepType, duration: number): void;
  recordResourceUsage(usage: ResourceUsage): void;
  getMetrics(executorType?: WorkflowStepType): ExecutionMetrics;
  reset(): void;
}