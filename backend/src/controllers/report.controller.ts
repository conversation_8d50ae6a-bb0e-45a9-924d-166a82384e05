import { Request, Response } from 'express'
import { ReportAnalyticsService, AnalyticsTimeRange, CustomReportConfig } from '@/services/report-analytics.service'
import { ServiceReportService } from '@/services/service-report.service'
// 使用标准的API响应格式
import { z } from 'zod'

/**
 * 报告和数据分析控制器
 * 提供数据分析、报表生成和导出功能的API端点
 */
export class ReportController {
  private reportAnalyticsService: ReportAnalyticsService
  private serviceReportService: ServiceReportService

  constructor() {
    this.reportAnalyticsService = ReportAnalyticsService.getInstance()
    this.serviceReportService = ServiceReportService.getInstance()
  }

  // ========== 数据分析端点 ==========

  /**
   * 获取运维效率分析数据
   * GET /api/v1/reports/operation-efficiency
   */
  async getOperationEfficiency(req: Request, res: Response) {
    try {
      const { start, end } = this.parseTimeRange(req.query)
      const timeRange: AnalyticsTimeRange = { start, end }

      const metrics = await this.reportAnalyticsService.getOperationEfficiencyMetrics(timeRange)
      
      return res.json({ success: true, data: metrics, message: '运维效率数据获取成功' })
    } catch (error) {
      console.error('获取运维效率数据失败:', error)
      return res.status(500).json({ success: false, message: '获取运维效率数据失败' })
    }
  }

  /**
   * 获取客户满意度分析数据
   * GET /api/v1/reports/customer-satisfaction
   */
  async getCustomerSatisfaction(req: Request, res: Response) {
    try {
      const { start, end } = this.parseTimeRange(req.query)
      const timeRange: AnalyticsTimeRange = { start, end }

      const metrics = await this.reportAnalyticsService.getCustomerSatisfactionMetrics(timeRange)
      
      return res.json({ success: true, data: metrics, message: '客户满意度数据获取成功' })
    } catch (error) {
      console.error('获取客户满意度数据失败:', error)
      return res.status(500).json({ success: false, message: '获取客户满意度数据失败' })
    }
  }

  /**
   * 获取SLA合规性报告
   * GET /api/v1/reports/sla-compliance
   */
  async getSLACompliance(req: Request, res: Response) {
    try {
      const { start, end } = this.parseTimeRange(req.query)
      const timeRange: AnalyticsTimeRange = { start, end }

      const report = await this.reportAnalyticsService.getSLAComplianceReport(timeRange)
      
      return res.json({ success: true, data: report, message: 'SLA合规性报告获取成功' })
    } catch (error) {
      console.error('获取SLA合规性报告失败:', error)
      return res.status(500).json({ success: false, message: '获取SLA合规性报告失败' })
    }
  }

  /**
   * 获取服务趋势分析数据
   * GET /api/v1/reports/service-trends
   */
  async getServiceTrends(req: Request, res: Response) {
    try {
      const { start, end } = this.parseTimeRange(req.query)
      const timeRange: AnalyticsTimeRange = { start, end }

      const analysis = await this.reportAnalyticsService.getServiceTrendAnalysis(timeRange)
      
      return res.json({ success: true, data: analysis, message: '服务趋势分析数据获取成功' })
    } catch (error) {
      console.error('获取服务趋势分析失败:', error)
      return res.status(500).json({ success: false, message: '获取服务趋势分析失败' })
    }
  }

  // ========== 自定义报表端点 ==========

  /**
   * 生成自定义报表
   * POST /api/v1/reports/custom
   */
  async generateCustomReport(req: Request, res: Response) {
    try {
      const config = this.validateCustomReportConfig(req.body)
      
      const result = await this.reportAnalyticsService.generateCustomReport(config)
      
      return res.json({ success: true, data: result, message: '自定义报表生成成功' })
    } catch (error) {
      console.error('生成自定义报表失败:', error)
      return res.status(500).json({ success: false, message: error instanceof Error ? error.message : '生成自定义报表失败' })
    }
  }

  /**
   * 获取报表配置模板
   * GET /api/v1/reports/templates
   */
  async getReportTemplates(req: Request, res: Response) {
    try {
      const templates = [
        {
          id: 'efficiency_weekly',
          name: '周度运维效率报表',
          description: '分析工程师工作效率和系统性能指标',
          timeRange: { type: 'last7days' },
          metrics: [
            { type: 'service_count', groupBy: 'day' },
            { type: 'avg_response_time', groupBy: 'day' },
            { type: 'sla_compliance', groupBy: 'engineer' }
          ],
          visualizations: [
            { type: 'line', title: '服务工单趋势', metricIndex: 0 },
            { type: 'bar', title: '响应时间分析', metricIndex: 1 },
            { type: 'table', title: 'SLA合规性统计', metricIndex: 2 }
          ],
          exportFormats: ['pdf', 'excel']
        },
        {
          id: 'satisfaction_monthly',
          name: '月度客户满意度报表',
          description: '分析客户满意度趋势和改进建议',
          timeRange: { type: 'last30days' },
          metrics: [
            { type: 'satisfaction', groupBy: 'week' },
            { type: 'satisfaction', groupBy: 'category' }
          ],
          visualizations: [
            { type: 'line', title: '满意度趋势', metricIndex: 0 },
            { type: 'pie', title: '分类满意度分布', metricIndex: 1 }
          ],
          exportFormats: ['pdf', 'excel', 'csv']
        },
        {
          id: 'sla_quarterly',
          name: '季度SLA合规性报表',
          description: '分析SLA执行情况和违规风险',
          timeRange: { type: 'lastQuarter' },
          metrics: [
            { type: 'sla_compliance', groupBy: 'month' },
            { type: 'sla_compliance', groupBy: 'priority' }
          ],
          visualizations: [
            { type: 'bar', title: 'SLA合规趋势', metricIndex: 0 },
            { type: 'table', title: '优先级合规统计', metricIndex: 1 }
          ],
          exportFormats: ['pdf', 'excel']
        }
      ]
      
      return res.json({ success: true, data: { templates }, message: '报表模板获取成功' })
    } catch (error) {
      console.error('获取报表模板失败:', error)
      return res.status(500).json({ success: false, message: '获取报表模板失败' })
    }
  }

  // ========== 导出功能端点 ==========

  /**
   * 导出Excel报表
   * POST /api/v1/reports/export/excel
   */
  async exportExcel(req: Request, res: Response) {
    try {
      const { data, fileName } = req.body
      
      if (!data || !fileName) {
        return res.status(400).json({ success: false, message: '缺少必要参数: data 和 fileName' })
      }

      const filePath = await this.reportAnalyticsService.exportToExcel(data, fileName)
      
      // 设置下载响应头
      res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
      res.setHeader('Content-Disposition', `attachment; filename="${fileName}.xlsx"`)
      
      // 发送文件
      return res.sendFile(filePath)
    } catch (error) {
      console.error('导出Excel失败:', error)
      return res.status(500).json({ success: false, message: '导出Excel失败' })
    }
  }

  /**
   * 导出CSV数据
   * POST /api/v1/reports/export/csv
   */
  async exportCSV(req: Request, res: Response) {
    try {
      const { data, fileName } = req.body
      
      if (!data || !fileName) {
        return res.status(400).json({ success: false, message: '缺少必要参数: data 和 fileName' })
      }

      const filePath = await this.reportAnalyticsService.exportToCSV(data, fileName)
      
      // 设置下载响应头
      res.setHeader('Content-Type', 'text/csv; charset=utf-8')
      res.setHeader('Content-Disposition', `attachment; filename="${fileName}.csv"`)
      
      // 发送文件
      return res.sendFile(filePath)
    } catch (error) {
      console.error('导出CSV失败:', error)
      return res.status(500).json({ success: false, message: '导出CSV失败' })
    }
  }

  /**
   * 生成PDF报表 (基于现有服务工单PDF功能扩展)
   * POST /api/v1/reports/export/pdf
   */
  async exportPDF(req: Request, res: Response) {
    try {
      const { serviceId, type } = req.body
      
      if (type === 'service' && serviceId) {
        // 使用现有的服务工单PDF生成功能
        const pdfBuffer = await this.serviceReportService.generateServiceReport(serviceId)
        
        res.setHeader('Content-Type', 'application/pdf')
        res.setHeader('Content-Disposition', `attachment; filename="service-report-${serviceId}.pdf"`)
        
        return res.send(pdfBuffer)
      } else {
        return res.status(400).json({ success: false, message: '暂不支持此类型的PDF导出' })
      }
    } catch (error) {
      console.error('导出PDF失败:', error)
      return res.status(500).json({ success: false, message: '导出PDF失败' })
    }
  }

  // ========== 综合仪表板端点 ==========

  /**
   * 获取完整的分析仪表板数据
   * GET /api/v1/reports/dashboard
   */
  async getDashboardData(req: Request, res: Response) {
    try {
      const { start, end } = this.parseTimeRange(req.query)
      const timeRange: AnalyticsTimeRange = { start, end }

      // 并行获取所有分析数据
      const [
        operationEfficiency,
        customerSatisfaction,
        slaCompliance,
        serviceTrends
      ] = await Promise.all([
        this.reportAnalyticsService.getOperationEfficiencyMetrics(timeRange),
        this.reportAnalyticsService.getCustomerSatisfactionMetrics(timeRange),
        this.reportAnalyticsService.getSLAComplianceReport(timeRange),
        this.reportAnalyticsService.getServiceTrendAnalysis(timeRange)
      ])

      const dashboardData = {
        timeRange,
        operationEfficiency,
        customerSatisfaction,
        slaCompliance,
        serviceTrends,
        summary: {
          totalServices: operationEfficiency.totalServices,
          completionRate: operationEfficiency.totalServices > 0 
            ? (operationEfficiency.completedServices / operationEfficiency.totalServices * 100).toFixed(1)
            : '0',
          avgSatisfaction: customerSatisfaction.averageRating.toFixed(1),
          slaComplianceRate: slaCompliance.complianceRate.toFixed(1),
          totalEngineers: operationEfficiency.engineerWorkload.length
        }
      }

      return res.json({ success: true, data: dashboardData, message: '仪表板数据获取成功' })
    } catch (error) {
      console.error('获取仪表板数据失败:', error)
      return res.status(500).json({ success: false, message: '获取仪表板数据失败' })
    }
  }

  /**
   * 获取快速统计数据 (用于首页概览)
   * GET /api/v1/reports/quick-stats
   */
  async getQuickStats(req: Request, res: Response) {
    try {
      // 默认获取最近30天数据
      const end = new Date()
      const start = new Date(end.getTime() - 30 * 24 * 60 * 60 * 1000)
      const timeRange: AnalyticsTimeRange = { start, end }

      const [efficiency, satisfaction, sla] = await Promise.all([
        this.reportAnalyticsService.getOperationEfficiencyMetrics(timeRange),
        this.reportAnalyticsService.getCustomerSatisfactionMetrics(timeRange),
        this.reportAnalyticsService.getSLAComplianceReport(timeRange)
      ])

      const quickStats = {
        totalServices: efficiency.totalServices,
        completionRate: efficiency.totalServices > 0 
          ? Number((efficiency.completedServices / efficiency.totalServices * 100).toFixed(1))
          : 0,
        avgResponseTime: efficiency.avgResponseTimeMinutes,
        avgResolutionTime: efficiency.avgResolutionTimeHours,
        slaComplianceRate: Number(sla.complianceRate.toFixed(1)),
        customerSatisfaction: Number(satisfaction.averageRating.toFixed(1)),
        totalFeedbacks: satisfaction.totalFeedbacks,
        activeEngineers: efficiency.engineerWorkload.filter(e => e.assignedCount > 0).length,
        slaViolations: sla.slaViolations,
        timeRange: {
          start: start.toISOString(),
          end: end.toISOString(),
          days: 30
        }
      }

      return res.json({ success: true, data: quickStats, message: '快速统计数据获取成功' })
    } catch (error) {
      console.error('获取快速统计数据失败:', error)
      return res.status(500).json({ success: false, message: '获取快速统计数据失败' })
    }
  }

  // ========== 工具方法 ==========

  /**
   * 解析时间范围参数
   */
  private parseTimeRange(query: any): { start: Date; end: Date } {
    const { start, end, days } = query

    // 如果提供了具体的开始和结束时间
    if (start && end) {
      return {
        start: new Date(start),
        end: new Date(end)
      }
    }

    // 如果提供了天数
    if (days) {
      const endDate = new Date()
      const startDate = new Date(endDate.getTime() - Number(days) * 24 * 60 * 60 * 1000)
      return {
        start: startDate,
        end: endDate
      }
    }

    // 默认最近7天
    const endDate = new Date()
    const startDate = new Date(endDate.getTime() - 7 * 24 * 60 * 60 * 1000)
    return {
      start: startDate,
      end: endDate
    }
  }

  /**
   * 验证自定义报表配置
   */
  private validateCustomReportConfig(data: any): CustomReportConfig {
    const configSchema = z.object({
      name: z.string().min(1).max(100),
      description: z.string().optional(),
      timeRange: z.object({
        type: z.enum(['custom', 'last7days', 'last30days', 'lastQuarter', 'lastYear']),
        start: z.string().datetime().optional(),
        end: z.string().datetime().optional()
      }),
      metrics: z.array(z.object({
        type: z.enum(['service_count', 'avg_response_time', 'sla_compliance', 'satisfaction', 'workload']),
        groupBy: z.enum(['day', 'week', 'month', 'quarter', 'year', 'category', 'priority', 'engineer']).optional(),
        filters: z.object({
          categories: z.array(z.string()).optional(),
          priorities: z.array(z.string()).optional(),
          engineers: z.array(z.string()).optional(),
          customers: z.array(z.string()).optional()
        }).optional()
      })).min(1),
      visualizations: z.array(z.object({
        type: z.enum(['line', 'bar', 'pie', 'table', 'number']),
        title: z.string().min(1),
        metricIndex: z.number().min(0),
        config: z.record(z.any()).optional()
      })).min(1),
      exportFormats: z.array(z.enum(['pdf', 'excel', 'csv'])).min(1),
      schedule: z.object({
        enabled: z.boolean(),
        frequency: z.enum(['daily', 'weekly', 'monthly']),
        recipients: z.array(z.string().email())
      }).optional(),
      createdBy: z.string()
    })

    const result = configSchema.safeParse(data)
    if (!result.success) {
      throw new Error(`自定义报表配置验证失败: ${result.error.message}`)
    }

    // 转换时间字符串为Date对象
    const config = result.data
    if (config.timeRange.type === 'custom') {
      if (config.timeRange.start) {
        config.timeRange.start = new Date(config.timeRange.start)
      }
      if (config.timeRange.end) {
        config.timeRange.end = new Date(config.timeRange.end)
      }
    }

    return config as CustomReportConfig
  }
}

export const reportController = new ReportController()