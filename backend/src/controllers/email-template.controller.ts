import { Request, Response } from 'express';
import { PrismaClient } from '@prisma/client';
import { asyncHandler } from '../utils/asyncHandler';
import { AppError } from '../utils/errors.util'

const prisma = new PrismaClient();

// 获取邮件模板列表
export const getEmailTemplates = asyncHandler(async (req: Request, res: Response) => {
  const { category, type, enabled, page = '1', limit = '20' } = req.query;
  
  const where: any = {};
  if (category) where.category = category;
  if (type) where.type = type;
  if (enabled !== undefined) where.enabled = enabled === 'true';

  const pageNum = parseInt(page as string);
  const limitNum = parseInt(limit as string);
  const offset = (pageNum - 1) * limitNum;

  const [templates, total] = await Promise.all([
    prisma.emailTemplate.findMany({
      where,
      include: {
        createdByUser: {
          select: {
            id: true,
            username: true,
            fullName: true,
          }
        },
        updatedByUser: {
          select: {
            id: true,
            username: true,
            fullName: true,
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      },
      skip: offset,
      take: limitNum,
    }),
    prisma.emailTemplate.count({ where })
  ]);

  res.json({
    success: true,
    data: templates,
    pagination: {
      page: pageNum,
      limit: limitNum,
      total,
      pages: Math.ceil(total / limitNum),
    }
  });
});

// 根据分类获取邮件模板
export const getEmailTemplatesByCategory = asyncHandler(async (req: Request, res: Response) => {
  const templates = await prisma.emailTemplate.findMany({
    where: {
      enabled: true,
    },
    include: {
      createdByUser: {
        select: {
          id: true,
          username: true,
          fullName: true,
        }
      }
    },
    orderBy: [
      { category: 'asc' },
      { type: 'asc' },
      { name: 'asc' }
    ]
  });

  // 按分类分组
  const groupedTemplates = templates.reduce((acc, template) => {
    const category = template.category;
    if (!acc[category]) {
      acc[category] = [];
    }
    acc[category].push(template);
    return acc;
  }, {} as Record<string, any[]>);

  res.json({
    success: true,
    data: groupedTemplates
  });
});

// 获取单个邮件模板
export const getEmailTemplate = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;

  const template = await prisma.emailTemplate.findUnique({
    where: { id: id as string },
    include: {
      createdByUser: {
        select: {
          id: true,
          username: true,
          fullName: true,
        }
      },
      updatedByUser: {
        select: {
          id: true,
          username: true,
          fullName: true,
        }
      }
    }
  });

  if (!template) {
    throw new AppError('邮件模板不存在', 404)
  }

  res.json({
    success: true,
    data: template
  });
});

// 创建邮件模板
export const createEmailTemplate = asyncHandler(async (req: Request, res: Response) => {
  const { name, type, category, subject, content, description, variables, enabled } = req.body;
  const userId = (req as any).user?.id;

  if (!userId) {
    throw new AppError('用户身份验证失败', 401)
  }

  // 检查同类型下是否已存在同名模板
  const existingTemplate = await prisma.emailTemplate.findFirst({
    where: {
      name,
      type,
    }
  });

  if (existingTemplate) {
    throw new AppError(`${type} 类型下已存在名为 "${name}" 的模板`, 400)
  }

  const template = await prisma.emailTemplate.create({
    data: {
      name,
      type,
      category: category || 'SYSTEM',
      subject,
      content,
      description,
      variables: variables || [],
      enabled: enabled !== undefined ? enabled : true,
      createdBy: userId,
      updatedBy: userId,
    },
    include: {
      createdByUser: {
        select: {
          id: true,
          username: true,
          fullName: true,
        }
      }
    }
  });

  res.status(201).json({
    success: true,
    data: template,
    message: '邮件模板创建成功'
  });
});

// 更新邮件模板
export const updateEmailTemplate = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  const { name, type, category, subject, content, description, variables, enabled } = req.body;
  const userId = (req as any).user?.id;

  if (!userId) {
    throw new AppError('用户身份验证失败', 401);
  }

  const existingTemplate = await prisma.emailTemplate.findUnique({
    where: { id: id as string }
  });

  if (!existingTemplate) {
    throw new AppError('邮件模板不存在', 404);
  }

  // 如果是系统模板，只有超级管理员可以修改
  if (existingTemplate.isSystem && !(req as any).user?.permissions?.includes('system:admin')) {
    throw new AppError('无权限修改系统模板', 403);
  }

  // 检查同类型下是否已存在同名模板（排除当前模板）
  if (name && type) {
    const duplicateTemplate = await prisma.emailTemplate.findFirst({
      where: {
        name,
        type,
        NOT: { id: id as string },
      }
    });

    if (duplicateTemplate) {
      throw new AppError(`${type} 类型下已存在名为 "${name}" 的模板`, 400);
    }
  }

  const template = await prisma.emailTemplate.update({
    where: { id: id as string },
    data: {
      ...(name && { name }),
      ...(type && { type }),
      ...(category && { category }),
      ...(subject && { subject }),
      ...(content && { content }),
      ...(description !== undefined && { description }),
      ...(variables && { variables }),
      ...(enabled !== undefined && { enabled }),
      updatedBy: userId,
    },
    include: {
      createdByUser: {
        select: {
          id: true,
          username: true,
          fullName: true,
        }
      },
      updatedByUser: {
        select: {
          id: true,
          username: true,
          fullName: true,
        }
      }
    }
  });

  res.json({
    success: true,
    data: template,
    message: '邮件模板更新成功'
  });
});

// 删除邮件模板
export const deleteEmailTemplate = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  const userId = (req as any).user?.id;

  if (!userId) {
    throw new AppError('用户身份验证失败', 401);
  }

  const template = await prisma.emailTemplate.findUnique({
    where: { id: id as string }
  });

  if (!template) {
    throw new AppError('邮件模板不存在', 404);
  }

  // 系统模板不能删除
  if (template.isSystem) {
    throw new AppError('系统模板不能删除', 403);
  }

  await prisma.emailTemplate.delete({
    where: { id: id as string }
  });

  res.json({
    success: true,
    message: '邮件模板删除成功'
  });
});

// 复制邮件模板
export const copyEmailTemplate = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  const { name } = req.body;
  const userId = (req as any).user?.id;

  if (!userId) {
    throw new AppError('用户身份验证失败', 401);
  }

  const originalTemplate = await prisma.emailTemplate.findUnique({
    where: { id: id as string }
  });

  if (!originalTemplate) {
    throw new AppError('原模板不存在', 404);
  }

  const newName = name || `${originalTemplate.name} - 副本`;

  // 检查同类型下是否已存在同名模板
  const existingTemplate = await prisma.emailTemplate.findFirst({
    where: {
      name: newName,
      type: originalTemplate.type,
    }
  });

  if (existingTemplate) {
    throw new AppError(`${originalTemplate.type} 类型下已存在名为 "${newName}" 的模板`, 400);
  }

  const template = await prisma.emailTemplate.create({
    data: {
      name: newName,
      type: originalTemplate.type,
      category: originalTemplate.category,
      subject: originalTemplate.subject,
      content: originalTemplate.content,
      description: originalTemplate.description,
      variables: originalTemplate.variables as any,
      enabled: false, // 复制的模板默认禁用
      isSystem: false, // 复制的模板不是系统模板
      createdBy: userId,
      updatedBy: userId,
    },
    include: {
      createdByUser: {
        select: {
          id: true,
          username: true,
          fullName: true,
        }
      }
    }
  });

  res.status(201).json({
    success: true,
    data: template,
    message: '邮件模板复制成功'
  });
});

// 预览邮件模板（渲染变量）
export const previewEmailTemplate = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  const { variables } = req.body;

  const template = await prisma.emailTemplate.findUnique({
    where: { id: id as string }
  });

  if (!template) {
    throw new AppError('邮件模板不存在', 404);
  }

  // 简单的变量替换逻辑
  let renderedSubject = template.subject;
  let renderedContent = template.content;

  if (variables && typeof variables === 'object') {
    Object.keys(variables).forEach(key => {
      const placeholder = `{{${key}}}`;
      const value = variables[key] || '';
      renderedSubject = renderedSubject.replace(new RegExp(placeholder, 'g'), value);
      renderedContent = renderedContent.replace(new RegExp(placeholder, 'g'), value);
    });
  }

  res.json({
    success: true,
    data: {
      id: template.id,
      name: template.name,
      type: template.type,
      originalSubject: template.subject,
      originalContent: template.content,
      renderedSubject,
      renderedContent,
      variables: template.variables,
      providedVariables: variables || {}
    }
  });
});

// 启用/禁用邮件模板
export const toggleEmailTemplate = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  const { enabled } = req.body;
  const userId = (req as any).user?.id;

  if (!userId) {
    throw new AppError('用户身份验证失败', 401);
  }

  const template = await prisma.emailTemplate.findUnique({
    where: { id: id as string }
  });

  if (!template) {
    throw new AppError('邮件模板不存在', 404);
  }

  const updatedTemplate = await prisma.emailTemplate.update({
    where: { id: id as string },
    data: {
      enabled: enabled !== undefined ? enabled : !template.enabled,
      updatedBy: userId,
    }
  });

  res.json({
    success: true,
    data: updatedTemplate,
    message: `邮件模板已${updatedTemplate.enabled ? '启用' : '禁用'}`
  });
});
