import { Request, Response } from 'express'
import { prisma } from '@/config/database.config'
import { z } from 'zod'

// 验证Schema
const createCustomerSchema = z.object({
  name: z.string().min(1).max(100),
  code: z.string().min(1).max(50),
  company: z.string().max(100).optional(),
  industry: z.string().max(50).optional(),
  type: z.enum(['ENTERPRISE', 'INDIVIDUAL', 'GOVERNMENT', 'NONPROFIT']).default('ENTERPRISE'),
  level: z.enum(['BASIC', 'STANDARD', 'PREMIUM', 'ENTERPRISE']).default('STANDARD'),
  contactPerson: z.string().max(50).optional(),
  contactPhone: z.string().max(20).optional(),
  contactEmail: z.string().email().optional(),
  address: z.string().max(200).optional(),
  description: z.string().optional(),
  isVip: z.boolean().default(false)
})

const updateCustomerSchema = createCustomerSchema.partial()

const createContactSchema = z.object({
  name: z.string().min(1).max(50),
  position: z.string().max(50).optional(),
  email: z.string().email().optional(),
  phone: z.string().max(20).optional(),
  isPrimary: z.boolean().default(false)
})

const updateContactSchema = createContactSchema.partial()

const querySchema = z.object({
  page: z.string().optional().transform(val => val ? parseInt(val) : 1),
  limit: z.string().optional().transform(val => val ? parseInt(val) : 10),
  search: z.string().optional(),
  type: z.enum(['ENTERPRISE', 'INDIVIDUAL', 'GOVERNMENT', 'NONPROFIT']).optional(),
  level: z.enum(['BASIC', 'STANDARD', 'PREMIUM', 'ENTERPRISE']).optional(),
  industry: z.string().optional(),
  isVip: z.string().optional().transform(val => val === 'true' ? true : val === 'false' ? false : undefined)
})

// 获取客户列表
export const getCustomers = async (req: Request, res: Response) => {
  try {
    const { page, limit, search, type, level, industry, isVip } = querySchema.parse(req.query)

    const skip = (page - 1) * limit

    // 构建查询条件
    const where: any = {}

    if (search) {
      where.OR = [
        { name: { contains: search } },
        { code: { contains: search } },
        { company: { contains: search } },
        { contactPerson: { contains: search } },
        { contactEmail: { contains: search } },
        { contactPhone: { contains: search } },
        { email: { contains: search } }
      ]
    }

    if (type) {
      where.type = type
    }

    if (level) {
      where.level = level
    }

    if (industry) {
      where.industry = { contains: industry }
    }

    if (isVip !== undefined) {
      where.isVip = isVip
    }

    const [customers, total] = await Promise.all([
      prisma.customer.findMany({
        where,
        skip,
        take: limit,
        include: {
          createdByUser: {
            select: {
              username: true,
              fullName: true
            }
          },
          contacts: {
            where: { isPrimary: true },
            take: 1
          },
          archives: {
            select: {
              id: true,
              name: true,
              status: true
            }
          }
        },
        orderBy: { createdAt: 'desc' }
      }),
      prisma.customer.count({ where })
    ])

    return res.json({
      success: true,
      data: {
        items: customers,
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        message: '请求参数验证失败',
        errors: error.errors
      })
    }

    console.error('Get customers error:', error)
    return res.status(500).json({
      success: false,
      message: '获取客户列表失败'
    })
  }
}

// 获取客户详情
export const getCustomer = async (req: Request, res: Response) => {
  try {
    const { id } = req.params

    if (!id) {
      return res.status(400).json({
        success: false,
        message: 'ID参数缺失'
      })
    }

    const customer = await prisma.customer.findUnique({
      where: { id },
      include: {
        createdByUser: {
          select: {
            username: true,
            fullName: true
          }
        },
        contacts: {
          orderBy: [
            { isPrimary: 'desc' },
            { createdAt: 'asc' }
          ]
        },
        archives: {
          select: {
            id: true,
            name: true,
            status: true,
            technology: true,
            version: true,
            createdAt: true
          },
          orderBy: { createdAt: 'desc' }
        }
      }
    })

    if (!customer) {
      return res.status(404).json({
        success: false,
        message: '客户不存在'
      })
    }

    return res.json({
      success: true,
      data: customer
    })
  } catch (error) {
    console.error('Get customer error:', error)
    return res.status(500).json({
      success: false,
      message: '获取客户详情失败'
    })
  }
}

// 创建客户
export const createCustomer = async (req: Request, res: Response) => {
  try {
    const userId = (req as any).user?.userId
    if (!userId) {
      return res.status(401).json({
        success: false,
        message: '用户未认证'
      })
    }

    const data = createCustomerSchema.parse(req.body)

    // 检查客户编码是否已存在
    const existingCustomer = await prisma.customer.findFirst({
      where: {
        OR: [
          { name: data.name },
          { code: data.code }
        ]
      }
    })

    if (existingCustomer) {
      return res.status(400).json({
        success: false,
        message: existingCustomer.name === data.name ? '客户名称已存在' : '客户编码已存在'
      })
    }

    const customer = await prisma.customer.create({
      data: {
        name: data.name,
        code: data.code,
        type: data.type,
        level: data.level,
        company: data.company || null,
        industry: data.industry || null,
        contactPerson: data.contactPerson || null,
        contactPhone: data.contactPhone || null,
        contactEmail: data.contactEmail || null,
        address: data.address || null,
        description: data.description || null,
        isVip: data.isVip,
        createdBy: userId
      },
      include: {
        createdByUser: {
          select: {
            username: true,
            fullName: true
          }
        }
      }
    })

    return res.status(201).json({
      success: true,
      message: '客户创建成功',
      data: customer
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        message: '请求参数验证失败',
        errors: error.errors
      })
    }

    console.error('Create customer error:', error)
    return res.status(500).json({
      success: false,
      message: '客户创建失败'
    })
  }
}

// 更新客户
export const updateCustomer = async (req: Request, res: Response) => {
  try {
    const { id } = req.params
    const data = updateCustomerSchema.parse(req.body)

    if (!id) {
      return res.status(400).json({
        success: false,
        message: 'ID参数缺失'
      })
    }

    // 检查客户是否存在
    const existingCustomer = await prisma.customer.findUnique({
      where: { id }
    })

    if (!existingCustomer) {
      return res.status(404).json({
        success: false,
        message: '客户不存在'
      })
    }

    // 如果更新名称，检查是否与其他客户冲突
    if (data.name && data.name !== existingCustomer.name) {
      const nameConflict = await prisma.customer.findFirst({
        where: {
          name: data.name,
          id: { not: id }
        }
      })

      if (nameConflict) {
        return res.status(400).json({
          success: false,
          message: '客户名称已存在'
        })
      }
    }

    const updateData: any = {}
    
    if (data.name !== undefined) updateData.name = data.name
    if (data.level !== undefined) updateData.level = data.level
    if (data.company !== undefined) updateData.company = data.company || null
    if (data.industry !== undefined) updateData.industry = data.industry || null
    if (data.contactPerson !== undefined) updateData.contactPerson = data.contactPerson || null
    if (data.address !== undefined) updateData.address = data.address || null
    if (data.description !== undefined) updateData.description = data.description || null
    if (data.contactEmail !== undefined) updateData.contactEmail = data.contactEmail || null
    if (data.contactPhone !== undefined) updateData.contactPhone = data.contactPhone || null

    const customer = await prisma.customer.update({
      where: { id },
      data: updateData,
      include: {
        createdByUser: {
          select: {
            username: true,
            fullName: true
          }
        },
        contacts: {
          orderBy: [
            { isPrimary: 'desc' },
            { createdAt: 'asc' }
          ]
        }
      }
    })

    return res.json({
      success: true,
      message: '客户更新成功',
      data: customer
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        message: '请求参数验证失败',
        errors: error.errors
      })
    }

    console.error('Update customer error:', error)
    return res.status(500).json({
      success: false,
      message: '客户更新失败'
    })
  }
}

// 删除客户
export const deleteCustomer = async (req: Request, res: Response) => {
  try {
    const { id } = req.params

    if (!id) {
      return res.status(400).json({
        success: false,
        message: 'ID参数缺失'
      })
    }

    // 检查客户是否存在
    const customer = await prisma.customer.findUnique({
      where: { id },
      include: {
        archives: true
      }
    })

    if (!customer) {
      return res.status(404).json({
        success: false,
        message: '客户不存在'
      })
    }

    // 检查是否有关联的项目档案
    if (customer.archives.length > 0) {
      return res.status(400).json({
        success: false,
        message: '客户下还有项目档案，无法删除'
      })
    }

    await prisma.customer.delete({
      where: { id }
    })

    return res.json({
      success: true,
      message: '客户删除成功'
    })
  } catch (error) {
    console.error('Delete customer error:', error)
    return res.status(500).json({
      success: false,
      message: '客户删除失败'
    })
  }
}

// 获取客户联系人列表
export const getCustomerContacts = async (req: Request, res: Response) => {
  try {
    const { customerId } = req.params

    if (!customerId) {
      return res.status(400).json({
        success: false,
        message: 'customerId参数缺失'
      })
    }

    // 检查客户是否存在
    const customer = await prisma.customer.findUnique({
      where: { id: customerId }
    })

    if (!customer) {
      return res.status(404).json({
        success: false,
        message: '客户不存在'
      })
    }

    const contacts = await prisma.customerContact.findMany({
      where: { customerId },
      orderBy: [
        { isPrimary: 'desc' },
        { createdAt: 'asc' }
      ]
    })

    return res.json({
      success: true,
      data: contacts
    })
  } catch (error) {
    console.error('Get customer contacts error:', error)
    return res.status(500).json({
      success: false,
      message: '获取客户联系人失败'
    })
  }
}

// 创建客户联系人
export const createCustomerContact = async (req: Request, res: Response) => {
  try {
    const { customerId } = req.params
    const data = createContactSchema.parse(req.body)

    if (!customerId) {
      return res.status(400).json({
        success: false,
        message: 'customerId参数缺失'
      })
    }

    // 检查客户是否存在
    const customer = await prisma.customer.findUnique({
      where: { id: customerId }
    })

    if (!customer) {
      return res.status(404).json({
        success: false,
        message: '客户不存在'
      })
    }

    // 如果设置为主要联系人，先取消其他主要联系人
    if (data.isPrimary) {
      await prisma.customerContact.updateMany({
        where: {
          customerId,
          isPrimary: true
        },
        data: {
          isPrimary: false
        }
      })
    }

    const contact = await prisma.customerContact.create({
      data: {
        name: data.name,
        isPrimary: data.isPrimary,
        email: data.email || null,
        phone: data.phone || null,
        position: data.position || null,
        customerId
      }
    })

    return res.status(201).json({
      success: true,
      message: '联系人创建成功',
      data: contact
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        message: '请求参数验证失败',
        errors: error.errors
      })
    }

    console.error('Create customer contact error:', error)
    return res.status(500).json({
      success: false,
      message: '联系人创建失败'
    })
  }
}

// 更新客户联系人
export const updateCustomerContact = async (req: Request, res: Response) => {
  try {
    const { customerId, contactId } = req.params
    const data = updateContactSchema.parse(req.body)

    if (!customerId || !contactId) {
      return res.status(400).json({
        success: false,
        message: '参数缺失'
      })
    }

    // 检查联系人是否存在
    const contact = await prisma.customerContact.findFirst({
      where: {
        id: contactId,
        customerId
      }
    })

    if (!contact) {
      return res.status(404).json({
        success: false,
        message: '联系人不存在'
      })
    }

    // 如果设置为主要联系人，先取消其他主要联系人
    if (data.isPrimary) {
      await prisma.customerContact.updateMany({
        where: {
          customerId,
          isPrimary: true,
          id: { not: contactId }
        },
        data: {
          isPrimary: false
        }
      })
    }

    const updateData: any = {}
    
    if (data.name !== undefined) updateData.name = data.name
    if (data.isPrimary !== undefined) updateData.isPrimary = data.isPrimary
    if (data.email !== undefined) updateData.email = data.email || null
    if (data.phone !== undefined) updateData.phone = data.phone || null
    if (data.position !== undefined) updateData.position = data.position || null

    const updatedContact = await prisma.customerContact.update({
      where: { id: contactId },
      data: updateData
    })

    return res.json({
      success: true,
      message: '联系人更新成功',
      data: updatedContact
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        message: '请求参数验证失败',
        errors: error.errors
      })
    }

    console.error('Update customer contact error:', error)
    return res.status(500).json({
      success: false,
      message: '联系人更新失败'
    })
  }
}

// 删除客户联系人
export const deleteCustomerContact = async (req: Request, res: Response) => {
  try {
    const { customerId, contactId } = req.params

    if (!customerId || !contactId) {
      return res.status(400).json({
        success: false,
        message: '参数缺失'
      })
    }

    // 检查联系人是否存在
    const contact = await prisma.customerContact.findFirst({
      where: {
        id: contactId,
        customerId
      }
    })

    if (!contact) {
      return res.status(404).json({
        success: false,
        message: '联系人不存在'
      })
    }

    await prisma.customerContact.delete({
      where: { id: contactId }
    })

    return res.json({
      success: true,
      message: '联系人删除成功'
    })
  } catch (error) {
    console.error('Delete customer contact error:', error)
    return res.status(500).json({
      success: false,
      message: '联系人删除失败'
    })
  }
}

// 获取客户统计信息
export const getCustomerStats = async (_req: Request, res: Response) => {
  try {
    const [
      totalCustomers,
      activeCustomers,
      vipCustomers,
      thisMonthCustomers
    ] = await Promise.all([
      // 总客户数
      prisma.customer.count(),

      // 活跃客户数（有项目档案的客户）
      prisma.customer.count({
        where: {
          archives: {
            some: {
              status: {
                in: ['ACTIVE', 'MAINTENANCE']
              }
            }
          }
        }
      }),

      // VIP客户数
      prisma.customer.count({
        where: {
          isVip: true
        }
      }),

      // 本月新增客户
      prisma.customer.count({
        where: {
          createdAt: {
            gte: new Date(new Date().getFullYear(), new Date().getMonth(), 1)
          }
        }
      })
    ])

    return res.json({
      success: true,
      data: {
        total: totalCustomers,
        active: activeCustomers,
        vip: vipCustomers,
        thisMonth: thisMonthCustomers
      }
    })
  } catch (error) {
    console.error('Get customer stats error:', error)
    return res.status(500).json({
      success: false,
      message: '获取客户统计信息失败'
    })
  }
}

// 获取单个客户统计信息
export const getCustomerDetailStats = async (req: Request, res: Response) => {
  try {
    const { id } = req.params

    if (!id) {
      return res.status(400).json({
        success: false,
        message: '客户ID不能为空'
      })
    }

    // 检查客户是否存在
    const customer = await prisma.customer.findUnique({
      where: { id }
    })

    if (!customer) {
      return res.status(404).json({
        success: false,
        message: '客户不存在'
      })
    }

    const [
      projects,
      services,
      pendingServices,
      configurations
    ] = await Promise.all([
      // 项目数量
      prisma.projectArchive.count({
        where: { customerId: id }
      }),

      // 服务工单总数
      prisma.service.count({
        where: {
          archive: {
            customerId: id
          }
        }
      }),

      // 待处理工单数量
      prisma.service.count({
        where: {
          archive: {
            customerId: id
          },
          status: {
            in: ['PENDING', 'IN_PROGRESS', 'WAITING_CUSTOMER']
          }
        }
      }),

      // 配置项数量
      prisma.projectConfiguration.count({
        where: {
          archive: {
            customerId: id
          }
        }
      })
    ])

    return res.json({
      success: true,
      data: {
        projects,
        services,
        pendingServices,
        configurations
      }
    })
  } catch (error) {
    console.error('Get customer detail stats error:', error)
    return res.status(500).json({
      success: false,
      message: '获取客户统计信息失败'
    })
  }
}