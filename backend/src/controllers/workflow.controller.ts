import { Request, Response } from 'express';
import { WorkflowService, type CreateWorkflowData, type UpdateWorkflowData } from '@/services/workflow.service';
import { workflowEngine } from '@/services/workflow-engine.service';
import { z } from 'zod';

// 验证schemas
const CreateWorkflowSchema = z.object({
  name: z.string().min(1, '工作流名称不能为空').max(100, '工作流名称不能超过100个字符'),
  description: z.string().max(500, '描述不能超过500个字符').optional(),
  category: z.enum(['SERVICE_AUTOMATION', 'SLA_MONITORING', 'ALERT_PROCESSING', 'BACKUP_AUTOMATION', 'MAINTENANCE', 'APPROVAL_PROCESS', 'NOTIFICATION', 'DATA_PROCESSING', 'INTEGRATION', 'CUSTOM']),
  priority: z.enum(['LOW', 'MEDIUM', 'HIGH', 'URGENT', 'CRITICAL']).optional(),
  config: z.object({
    trigger: z.object({
      type: z.enum(['MANUAL', 'SCHEDULED', 'EVENT', 'WEBHOOK', 'API', 'CONDITION']),
      config: z.any()
    }),
    steps: z.array(z.object({
      index: z.number().min(0),
      name: z.string().min(1),
      type: z.enum(['ACTION', 'CONDITION', 'APPROVAL', 'NOTIFICATION', 'DELAY', 'PARALLEL', 'LOOP', 'SUBPROCESS', 'SCRIPT', 'HTTP_REQUEST', 'DATABASE_OPERATION', 'FILE_OPERATION']),
      config: z.any(),
      condition: z.any().optional(),
      timeout: z.number().optional(),
      retry: z.object({
        maxAttempts: z.number().min(0),
        delay: z.number().min(0)
      }).optional()
    })).min(1, '至少需要一个步骤'),
    variables: z.record(z.any()).optional(),
    settings: z.object({
      timeout: z.number().optional(),
      maxRetries: z.number().optional(),
      errorHandling: z.enum(['stop', 'continue', 'rollback']).optional(),
      notifications: z.array(z.string()).optional()
    }).optional()
  }),
  tags: z.array(z.string()).optional(),
  isTemplate: z.boolean().optional()
});

const UpdateWorkflowSchema = CreateWorkflowSchema.partial().omit({ config: true }).extend({
  config: CreateWorkflowSchema.shape.config.optional(),
  isActive: z.boolean().optional()
});

const TriggerWorkflowSchema = z.object({
  triggerData: z.any().optional()
});

const QueryParamsSchema = z.object({
  page: z.string().transform(Number).optional(),
  limit: z.string().transform(Number).optional(),
  category: z.string().optional(),
  isActive: z.string().transform(val => val === 'true').optional(),
  search: z.string().optional(),
  tags: z.string().transform(val => val.split(',')).optional(),
  createdBy: z.string().optional()
});

const ExecutionQueryParamsSchema = z.object({
  page: z.string().transform(Number).optional(),
  limit: z.string().transform(Number).optional(),
  workflowId: z.string().optional(),
  status: z.string().optional(),
  triggerType: z.string().optional(),
  startDate: z.string().transform(val => new Date(val)).optional(),
  endDate: z.string().transform(val => new Date(val)).optional()
});

export class WorkflowController {
  /**
   * 创建工作流
   */
  static async createWorkflow(req: Request, res: Response) {
    try {
      const validatedData = CreateWorkflowSchema.parse(req.body);
      const userId = req.user?.id;

      if (!userId) {
        return res.status(401).json({
          success: false,
          message: '用户未认证'
        });
      }

      const workflow = await WorkflowService.createWorkflow(validatedData, userId);

      res.status(201).json({
        success: true,
        data: workflow,
        message: '工作流创建成功'
      });
    } catch (error: any) {
      console.error('创建工作流失败:', error);
      res.status(400).json({
        success: false,
        message: error.message || '创建工作流失败'
      });
    }
  }

  /**
   * 获取工作流列表
   */
  static async getWorkflows(req: Request, res: Response) {
    try {
      const queryParams = QueryParamsSchema.parse(req.query);
      const result = await WorkflowService.getWorkflows(queryParams);

      res.json({
        success: true,
        data: result.workflows,
        pagination: result.pagination
      });
    } catch (error: any) {
      console.error('获取工作流列表失败:', error);
      res.status(500).json({
        success: false,
        message: error.message || '获取工作流列表失败'
      });
    }
  }

  /**
   * 获取工作流详情
   */
  static async getWorkflowById(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const workflow = await WorkflowService.getWorkflowById(id);

      res.json({
        success: true,
        data: workflow
      });
    } catch (error: any) {
      console.error('获取工作流详情失败:', error);
      res.status(404).json({
        success: false,
        message: error.message || '工作流不存在'
      });
    }
  }

  /**
   * 更新工作流
   */
  static async updateWorkflow(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const validatedData = UpdateWorkflowSchema.parse(req.body);
      const userId = req.user?.id;

      if (!userId) {
        return res.status(401).json({
          success: false,
          message: '用户未认证'
        });
      }

      const workflow = await WorkflowService.updateWorkflow(id, validatedData, userId);

      res.json({
        success: true,
        data: workflow,
        message: '工作流更新成功'
      });
    } catch (error: any) {
      console.error('更新工作流失败:', error);
      res.status(400).json({
        success: false,
        message: error.message || '更新工作流失败'
      });
    }
  }

  /**
   * 删除工作流
   */
  static async deleteWorkflow(req: Request, res: Response) {
    try {
      const { id } = req.params;
      await WorkflowService.deleteWorkflow(id);

      res.json({
        success: true,
        message: '工作流删除成功'
      });
    } catch (error: any) {
      console.error('删除工作流失败:', error);
      res.status(400).json({
        success: false,
        message: error.message || '删除工作流失败'
      });
    }
  }

  /**
   * 复制工作流
   */
  static async cloneWorkflow(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const { name } = req.body;
      const userId = req.user?.id;

      if (!userId) {
        return res.status(401).json({
          success: false,
          message: '用户未认证'
        });
      }

      if (!name) {
        return res.status(400).json({
          success: false,
          message: '请提供新工作流名称'
        });
      }

      const workflow = await WorkflowService.cloneWorkflow(id, name, userId);

      res.status(201).json({
        success: true,
        data: workflow,
        message: '工作流复制成功'
      });
    } catch (error: any) {
      console.error('复制工作流失败:', error);
      res.status(400).json({
        success: false,
        message: error.message || '复制工作流失败'
      });
    }
  }

  /**
   * 触发工作流执行
   */
  static async triggerWorkflow(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const validatedData = TriggerWorkflowSchema.parse(req.body);
      const userId = req.user?.id;

      const result = await WorkflowService.triggerWorkflow(
        id, 
        validatedData.triggerData, 
        userId
      );

      res.json({
        success: true,
        data: result,
        message: '工作流触发成功'
      });
    } catch (error: any) {
      console.error('触发工作流失败:', error);
      res.status(400).json({
        success: false,
        message: error.message || '触发工作流失败'
      });
    }
  }

  /**
   * 获取执行列表
   */
  static async getExecutions(req: Request, res: Response) {
    try {
      const queryParams = ExecutionQueryParamsSchema.parse(req.query);
      
      // 构建日期范围
      const dateRange = queryParams.startDate && queryParams.endDate ? {
        start: queryParams.startDate,
        end: queryParams.endDate
      } : undefined;

      const result = await WorkflowService.getExecutions({
        ...queryParams,
        dateRange
      });

      res.json({
        success: true,
        data: result.executions,
        pagination: result.pagination
      });
    } catch (error: any) {
      console.error('获取执行列表失败:', error);
      res.status(500).json({
        success: false,
        message: error.message || '获取执行列表失败'
      });
    }
  }

  /**
   * 获取执行详情
   */
  static async getExecutionById(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const execution = await WorkflowService.getExecutionById(id);

      res.json({
        success: true,
        data: execution
      });
    } catch (error: any) {
      console.error('获取执行详情失败:', error);
      res.status(404).json({
        success: false,
        message: error.message || '执行记录不存在'
      });
    }
  }

  /**
   * 暂停执行
   */
  static async pauseExecution(req: Request, res: Response) {
    try {
      const { id } = req.params;
      await WorkflowService.pauseExecution(id);

      res.json({
        success: true,
        message: '执行已暂停'
      });
    } catch (error: any) {
      console.error('暂停执行失败:', error);
      res.status(400).json({
        success: false,
        message: error.message || '暂停执行失败'
      });
    }
  }

  /**
   * 恢复执行
   */
  static async resumeExecution(req: Request, res: Response) {
    try {
      const { id } = req.params;
      await WorkflowService.resumeExecution(id);

      res.json({
        success: true,
        message: '执行已恢复'
      });
    } catch (error: any) {
      console.error('恢复执行失败:', error);
      res.status(400).json({
        success: false,
        message: error.message || '恢复执行失败'
      });
    }
  }

  /**
   * 取消执行
   */
  static async cancelExecution(req: Request, res: Response) {
    try {
      const { id } = req.params;
      await WorkflowService.cancelExecution(id);

      res.json({
        success: true,
        message: '执行已取消'
      });
    } catch (error: any) {
      console.error('取消执行失败:', error);
      res.status(400).json({
        success: false,
        message: error.message || '取消执行失败'
      });
    }
  }

  /**
   * 获取工作流统计
   */
  static async getStats(req: Request, res: Response) {
    try {
      const stats = await WorkflowService.getWorkflowStats();

      res.json({
        success: true,
        data: stats
      });
    } catch (error: any) {
      console.error('获取统计信息失败:', error);
      res.status(500).json({
        success: false,
        message: error.message || '获取统计信息失败'
      });
    }
  }

  /**
   * 获取正在运行的执行
   */
  static async getRunningExecutions(req: Request, res: Response) {
    try {
      const runningExecutions = workflowEngine.getRunningExecutions();

      res.json({
        success: true,
        data: {
          executions: runningExecutions,
          count: runningExecutions.length
        }
      });
    } catch (error: any) {
      console.error('获取运行中执行失败:', error);
      res.status(500).json({
        success: false,
        message: error.message || '获取运行中执行失败'
      });
    }
  }

  /**
   * 获取预设模板
   */
  static async getPresetTemplates(req: Request, res: Response) {
    try {
      const templates = WorkflowService.getPresetTemplates();

      res.json({
        success: true,
        data: templates
      });
    } catch (error: any) {
      console.error('获取预设模板失败:', error);
      res.status(500).json({
        success: false,
        message: error.message || '获取预设模板失败'
      });
    }
  }

  /**
   * 创建模板
   */
  static async createTemplate(req: Request, res: Response) {
    try {
      const validatedData = CreateWorkflowSchema.parse(req.body);
      const userId = req.user?.id;

      if (!userId) {
        return res.status(401).json({
          success: false,
          message: '用户未认证'
        });
      }

      const template = await WorkflowService.createTemplate(validatedData, userId);

      res.status(201).json({
        success: true,
        data: template,
        message: '模板创建成功'
      });
    } catch (error: any) {
      console.error('创建模板失败:', error);
      res.status(400).json({
        success: false,
        message: error.message || '创建模板失败'
      });
    }
  }

  /**
   * 从模板创建工作流
   */
  static async createFromTemplate(req: Request, res: Response) {
    try {
      const { templateId } = req.params;
      const { name, customizations } = req.body;
      const userId = req.user?.id;

      if (!userId) {
        return res.status(401).json({
          success: false,
          message: '用户未认证'
        });
      }

      if (!name) {
        return res.status(400).json({
          success: false,
          message: '请提供工作流名称'
        });
      }

      const workflow = await WorkflowService.createFromTemplate(
        templateId, 
        name, 
        customizations || {}, 
        userId
      );

      res.status(201).json({
        success: true,
        data: workflow,
        message: '从模板创建工作流成功'
      });
    } catch (error: any) {
      console.error('从模板创建工作流失败:', error);
      res.status(400).json({
        success: false,
        message: error.message || '从模板创建工作流失败'
      });
    }
  }

  /**
   * 验证工作流配置
   */
  static async validateConfig(req: Request, res: Response) {
    try {
      const { config } = req.body;

      if (!config) {
        return res.status(400).json({
          success: false,
          message: '请提供工作流配置'
        });
      }

      // 使用 CreateWorkflowSchema 中的 config 部分进行验证
      CreateWorkflowSchema.shape.config.parse(config);

      res.json({
        success: true,
        data: {
          valid: true,
          estimatedTime: WorkflowService['estimateExecutionTime'](config)
        },
        message: '配置验证通过'
      });
    } catch (error: any) {
      console.error('配置验证失败:', error);
      res.status(400).json({
        success: false,
        message: error.message || '配置验证失败'
      });
    }
  }
}