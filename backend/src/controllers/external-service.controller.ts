/**
 * 外部服务工单控制器 - 外部调用MVP
 * 
 * 处理通过API Key创建的外部工单
 */

import { Response } from 'express'
import { z } from 'zod'
import { prisma } from '@/config/database.config'
import { ApiKeyAuthenticatedRequest } from '@/middleware/api-key-auth.middleware'
import { generateTicketNumber } from '@/utils/ticket.util'

// ==================== 验证Schema ====================

const createExternalServiceSchema = z.object({
  archiveId: z.string().min(1, '项目档案ID不能为空'),
  title: z.string().min(1, '工单标题不能为空').max(200),
  description: z.string().min(1, '工单描述不能为空'),
  category: z.enum(['MAINTENANCE', 'SUPPORT', 'UPGRADE', 'BUGFIX', 'CONSULTING', 'MONITORING']).optional().default('SUPPORT'),
  priority: z.enum(['LOW', 'MEDIUM', 'HIGH', 'URGENT']).optional().default('MEDIUM'),
  externalUserId: z.string().optional(),
  externalAccount: z.string().optional(),
  customerContact: z.string().optional(),
  estimatedHours: z.number().min(0).optional(),
  tags: z.array(z.string()).optional()
})

// ==================== 外部工单管理 ====================

/**
 * 创建外部工单
 */
export const createExternalService = async (req: ApiKeyAuthenticatedRequest, res: Response) => {
  try {
    const {
      archiveId,
      title,
      description,
      category,
      priority,
      externalUserId,
      externalAccount,
      customerContact,
      estimatedHours,
      tags
    } = createExternalServiceSchema.parse(req.body)

    if (!req.apiKey) {
      return res.status(401).json({
        success: false,
        message: 'API Key验证失败'
      })
    }

    // 验证项目档案是否存在
    const archive = await prisma.projectArchive.findUnique({
      where: { id: archiveId },
      select: {
        id: true,
        name: true,
        customerId: true,
        status: true,
        customer: {
          select: {
            id: true,
            name: true,
            code: true
          }
        }
      }
    })

    if (!archive) {
      return res.status(400).json({
        success: false,
        message: '项目档案不存在'
      })
    }

    if (archive.status !== 'ACTIVE') {
      return res.status(400).json({
        success: false,
        message: '项目档案状态不活跃，无法创建工单'
      })
    }

    // 生成工单号
    const ticketNumber = await generateTicketNumber()

    // 创建外部工单
    const service = await prisma.service.create({
      data: {
        archiveId,
        ticketNumber,
        title,
        description,
        category,
        priority,
        source: 'EXTERNAL',
        externalSystemId: req.apiKey.systemId,
        externalUserId,
        externalAccount,
        customerContact,
        estimatedHours,
        tags: tags ? JSON.stringify(tags) : null,
        createdBy: req.apiKey.createdBy
      },
      select: {
        id: true,
        ticketNumber: true,
        title: true,
        description: true,
        category: true,
        priority: true,
        status: true,
        source: true,
        externalSystemId: true,
        externalUserId: true,
        externalAccount: true,
        estimatedHours: true,
        tags: true,
        createdAt: true,
        archive: {
          select: {
            id: true,
            name: true,
            customer: {
              select: {
                id: true,
                name: true,
                code: true
              }
            }
          }
        }
      }
    })

    // 记录操作历史
    await prisma.serviceOperationHistory.create({
      data: {
        serviceId: service.id,
        type: 'CREATE',
        description: `通过外部API创建工单 - 系统: ${req.apiKey.systemId}`,
        note: `外部用户: ${externalUserId || '未指定'}, 外部账号: ${externalAccount || '未指定'}`,
        userId: req.apiKey.createdBy
      }
    })

    res.status(201).json({
      success: true,
      message: '外部工单创建成功',
      data: {
        ...service,
        tags: service.tags ? JSON.parse(service.tags as string) : null
      }
    })
  } catch (error: any) {
    console.error('创建外部工单失败:', error)
    
    if (error.name === 'ZodError') {
      return res.status(400).json({
        success: false,
        message: '请求参数错误',
        errors: error.errors.map((e: any) => ({
          field: e.path.join('.'),
          message: e.message
        }))
      })
    }

    res.status(500).json({
      success: false,
      message: '创建外部工单失败',
      error: error.message
    })
  }
}

/**
 * 获取外部工单列表 (通过API Key关联的系统ID)
 */
export const getExternalServices = async (req: ApiKeyAuthenticatedRequest, res: Response) => {
  try {
    if (!req.apiKey) {
      return res.status(401).json({
        success: false,
        message: 'API Key验证失败'
      })
    }

    const { page = '1', limit = '20', status, priority, externalUserId } = req.query

    const pageNum = parseInt(page as string)
    const limitNum = parseInt(limit as string)
    const skip = (pageNum - 1) * limitNum

    // 构建查询条件
    const where: any = {
      source: 'EXTERNAL',
      externalSystemId: req.apiKey.systemId
    }

    if (status) where.status = status
    if (priority) where.priority = priority
    if (externalUserId) where.externalUserId = externalUserId

    const [services, total] = await Promise.all([
      prisma.service.findMany({
        where,
        select: {
          id: true,
          ticketNumber: true,
          title: true,
          description: true,
          category: true,
          priority: true,
          status: true,
          source: true,
          externalSystemId: true,
          externalUserId: true,
          externalAccount: true,
          customerContact: true,
          estimatedHours: true,
          actualHours: true,
          tags: true,
          firstResponseAt: true,
          createdAt: true,
          updatedAt: true,
          archive: {
            select: {
              id: true,
              name: true,
              customer: {
                select: {
                  id: true,
                  name: true,
                  code: true
                }
              }
            }
          },
          assignedUser: {
            select: {
              id: true,
              username: true,
              fullName: true
            }
          }
        },
        skip,
        take: limitNum,
        orderBy: { createdAt: 'desc' }
      }),
      prisma.service.count({ where })
    ])

    res.json({
      success: true,
      data: {
        services: services.map(service => ({
          ...service,
          tags: service.tags ? JSON.parse(service.tags as string) : null
        })),
        pagination: {
          page: pageNum,
          limit: limitNum,
          total,
          totalPages: Math.ceil(total / limitNum)
        }
      }
    })
  } catch (error: any) {
    console.error('获取外部工单列表失败:', error)
    res.status(500).json({
      success: false,
      message: '获取外部工单列表失败',
      error: error.message
    })
  }
}

/**
 * 获取外部工单详情
 */
export const getExternalService = async (req: ApiKeyAuthenticatedRequest, res: Response) => {
  try {
    if (!req.apiKey) {
      return res.status(401).json({
        success: false,
        message: 'API Key验证失败'
      })
    }

    const { id } = req.params

    const service = await prisma.service.findFirst({
      where: {
        id,
        source: 'EXTERNAL',
        externalSystemId: req.apiKey.systemId
      },
      select: {
        id: true,
        ticketNumber: true,
        title: true,
        description: true,
        category: true,
        priority: true,
        status: true,
        source: true,
        externalSystemId: true,
        externalUserId: true,
        externalAccount: true,
        customerContact: true,
        startTime: true,
        endTime: true,
        estimatedHours: true,
        actualHours: true,
        resolution: true,
        customerFeedback: true,
        satisfaction: true,
        tags: true,
        firstResponseAt: true,
        createdAt: true,
        updatedAt: true,
        archive: {
          select: {
            id: true,
            name: true,
            customer: {
              select: {
                id: true,
                name: true,
                code: true
              }
            }
          }
        },
        assignedUser: {
          select: {
            id: true,
            username: true,
            fullName: true
          }
        },
        workLogs: {
          select: {
            id: true,
            description: true,
            workHours: true,
            workDate: true,
            category: true,
            createdAt: true,
            user: {
              select: {
                username: true,
                fullName: true
              }
            }
          },
          orderBy: { createdAt: 'desc' },
          take: 10
        },
        comments: {
          where: {
            isInternal: false // 只返回外部可见的评论
          },
          select: {
            id: true,
            content: true,
            createdAt: true,
            author: {
              select: {
                username: true,
                fullName: true
              }
            }
          },
          orderBy: { createdAt: 'asc' }
        }
      }
    })

    if (!service) {
      return res.status(404).json({
        success: false,
        message: '外部工单不存在'
      })
    }

    res.json({
      success: true,
      data: {
        ...service,
        tags: service.tags ? JSON.parse(service.tags as string) : null
      }
    })
  } catch (error: any) {
    console.error('获取外部工单详情失败:', error)
    res.status(500).json({
      success: false,
      message: '获取外部工单详情失败',
      error: error.message
    })
  }
}

/**
 * 添加外部工单评论 (客户反馈)
 */
export const addExternalServiceComment = async (req: ApiKeyAuthenticatedRequest, res: Response) => {
  try {
    if (!req.apiKey) {
      return res.status(401).json({
        success: false,
        message: 'API Key验证失败'
      })
    }

    const { id } = req.params
    const { content } = z.object({
      content: z.string().min(1, '评论内容不能为空')
    }).parse(req.body)

    // 验证工单是否存在且属于当前外部系统
    const service = await prisma.service.findFirst({
      where: {
        id,
        source: 'EXTERNAL',
        externalSystemId: req.apiKey.systemId
      }
    })

    if (!service) {
      return res.status(404).json({
        success: false,
        message: '外部工单不存在'
      })
    }

    // 创建评论
    const comment = await prisma.serviceComment.create({
      data: {
        serviceId: id,
        content,
        isInternal: false,
        authorId: req.apiKey.createdBy
      },
      select: {
        id: true,
        content: true,
        createdAt: true,
        author: {
          select: {
            username: true,
            fullName: true
          }
        }
      }
    })

    // 记录操作历史
    await prisma.serviceOperationHistory.create({
      data: {
        serviceId: id,
        type: 'COMMENT',
        description: '外部系统添加评论',
        note: `系统: ${req.apiKey.systemId}`,
        userId: req.apiKey.createdBy
      }
    })

    res.status(201).json({
      success: true,
      message: '评论添加成功',
      data: comment
    })
  } catch (error: any) {
    console.error('添加外部工单评论失败:', error)

    if (error.name === 'ZodError') {
      return res.status(400).json({
        success: false,
        message: '请求参数错误',
        errors: error.errors.map((e: any) => ({
          field: e.path.join('.'),
          message: e.message
        }))
      })
    }

    res.status(500).json({
      success: false,
      message: '添加外部工单评论失败',
      error: error.message
    })
  }
}