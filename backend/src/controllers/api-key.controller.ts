/**
 * API Key 管理控制器 - 外部调用MVP
 * 
 * 提供外部系统API密钥的管理功能
 */

import { Request, Response } from 'express'
import { z } from 'zod'
import { prisma } from '@/config/database.config'
import { AuthenticatedRequest } from '@/middleware/auth.middleware'
import { generateApiKey } from '@/utils/crypto.util'

// ==================== 验证Schema ====================

const createApiKeySchema = z.object({
  name: z.string().min(1, '密钥名称不能为空').max(100),
  systemId: z.string().min(1, '系统标识不能为空').max(50),
  description: z.string().optional(),
  expiresAt: z.string().optional().transform(val => val ? new Date(val) : undefined)
})

// 生成唯一的API Key
async function generateUniqueApiKey(): Promise<string> {
  let apiKey: string
  let attempts = 0
  const maxAttempts = 10

  do {
    apiKey = generateApiKey()
    
    // 检查是否已存在
    const existing = await prisma.apiKey.findUnique({
      where: { keyValue: apiKey }
    })

    if (!existing) {
      break
    }

    attempts++
    if (attempts >= maxAttempts) {
      throw new Error('无法生成唯一的API Key')
    }
  } while (true)

  return apiKey
}

const updateApiKeySchema = z.object({
  name: z.string().min(1).max(100).optional(),
  description: z.string().optional(),
  status: z.enum(['ACTIVE', 'INACTIVE', 'REVOKED']).optional(),
  expiresAt: z.string().optional().transform(val => val ? new Date(val) : undefined)
})

// ==================== API Key 管理 ====================

/**
 * 获取API Key列表
 */
export const getApiKeys = async (req: Request, res: Response) => {
  try {
    const authReq = req as AuthenticatedRequest
    const { page = '1', limit = '20', status, systemId } = req.query

    const pageNum = parseInt(page as string)
    const limitNum = parseInt(limit as string)
    const skip = (pageNum - 1) * limitNum

    // 构建查询条件
    const where: any = {}
    if (status) where.status = status
    if (systemId) where.systemId = systemId

    const [apiKeys, total] = await Promise.all([
      prisma.apiKey.findMany({
        where,
        select: {
          id: true,
          name: true,
          systemId: true,
          description: true,
          status: true,
          keyValue: false, // 不返回实际密钥值
          expiresAt: true,
          lastUsedAt: true,
          usageCount: true,
          createdAt: true,
          createdByUser: {
            select: {
              username: true,
              fullName: true
            }
          }
        },
        skip,
        take: limitNum,
        orderBy: { createdAt: 'desc' }
      }),
      prisma.apiKey.count({ where })
    ])

    res.json({
      success: true,
      data: {
        apiKeys,
        pagination: {
          page: pageNum,
          limit: limitNum,
          total,
          totalPages: Math.ceil(total / limitNum)
        }
      }
    })
  } catch (error: any) {
    console.error('获取API Key列表失败:', error)
    res.status(500).json({
      success: false,
      message: '获取API Key列表失败',
      error: error.message
    })
  }
}

/**
 * 获取单个API Key详情
 */
export const getApiKey = async (req: Request, res: Response) => {
  try {
    const { id } = req.params

    const apiKey = await prisma.apiKey.findUnique({
      where: { id },
      select: {
        id: true,
        name: true,
        systemId: true,
        description: true,
        status: true,
        keyValue: false, // 不返回实际密钥值
        expiresAt: true,
        lastUsedAt: true,
        usageCount: true,
        createdAt: true,
        updatedAt: true,
        createdByUser: {
          select: {
            username: true,
            fullName: true
          }
        }
      }
    })

    if (!apiKey) {
      return res.status(404).json({
        success: false,
        message: 'API Key 不存在'
      })
    }

    res.json({
      success: true,
      data: apiKey
    })
  } catch (error: any) {
    console.error('获取API Key详情失败:', error)
    res.status(500).json({
      success: false,
      message: '获取API Key详情失败',
      error: error.message
    })
  }
}

/**
 * 创建API Key
 */
export const createApiKey = async (req: Request, res: Response) => {
  try {
    const { name, systemId, description, expiresAt } = createApiKeySchema.parse(req.body)
    const authReq = req as AuthenticatedRequest

    // 检查系统ID是否已存在
    const existingKey = await prisma.apiKey.findFirst({
      where: {
        systemId,
        status: { not: 'REVOKED' }
      }
    })

    if (existingKey) {
      return res.status(400).json({
        success: false,
        message: '该系统已存在有效的API Key'
      })
    }

    // 生成唯一的API Key
    const keyValue = await generateUniqueApiKey()

    const apiKey = await prisma.apiKey.create({
      data: {
        name,
        keyValue,
        systemId,
        description,
        expiresAt,
        createdBy: authReq.user!.id
      },
      select: {
        id: true,
        name: true,
        keyValue: true, // 创建时返回密钥值，之后不再返回
        systemId: true,
        description: true,
        status: true,
        expiresAt: true,
        createdAt: true
      }
    })

    res.status(201).json({
      success: true,
      message: 'API Key 创建成功',
      data: apiKey
    })
  } catch (error: any) {
    console.error('创建API Key失败:', error)
    res.status(500).json({
      success: false,
      message: '创建API Key失败',
      error: error.message
    })
  }
}

/**
 * 更新API Key
 */
export const updateApiKey = async (req: Request, res: Response) => {
  try {
    const { id } = req.params
    const updateData = updateApiKeySchema.parse(req.body)

    const apiKey = await prisma.apiKey.findUnique({
      where: { id }
    })

    if (!apiKey) {
      return res.status(404).json({
        success: false,
        message: 'API Key 不存在'
      })
    }

    const updatedApiKey = await prisma.apiKey.update({
      where: { id },
      data: updateData,
      select: {
        id: true,
        name: true,
        systemId: true,
        description: true,
        status: true,
        expiresAt: true,
        updatedAt: true
      }
    })

    res.json({
      success: true,
      message: 'API Key 更新成功',
      data: updatedApiKey
    })
  } catch (error: any) {
    console.error('更新API Key失败:', error)
    res.status(500).json({
      success: false,
      message: '更新API Key失败',
      error: error.message
    })
  }
}

/**
 * 删除/撤销API Key
 */
export const deleteApiKey = async (req: Request, res: Response) => {
  try {
    const { id } = req.params

    const apiKey = await prisma.apiKey.findUnique({
      where: { id }
    })

    if (!apiKey) {
      return res.status(404).json({
        success: false,
        message: 'API Key 不存在'
      })
    }

    // 撤销而不是删除，保留历史记录
    await prisma.apiKey.update({
      where: { id },
      data: {
        status: 'REVOKED'
      }
    })

    res.json({
      success: true,
      message: 'API Key 已撤销'
    })
  } catch (error: any) {
    console.error('撤销API Key失败:', error)
    res.status(500).json({
      success: false,
      message: '撤销API Key失败',
      error: error.message
    })
  }
}

/**
 * 重新生成API Key
 */
export const regenerateApiKey = async (req: Request, res: Response) => {
  try {
    const { id } = req.params

    const apiKey = await prisma.apiKey.findUnique({
      where: { id }
    })

    if (!apiKey) {
      return res.status(404).json({
        success: false,
        message: 'API Key 不存在'
      })
    }

    // 生成新的唯一密钥值
    const newKeyValue = await generateUniqueApiKey()

    const updatedApiKey = await prisma.apiKey.update({
      where: { id },
      data: {
        keyValue: newKeyValue,
        status: 'ACTIVE',
        usageCount: 0,
        lastUsedAt: null
      },
      select: {
        id: true,
        name: true,
        keyValue: true,
        systemId: true,
        status: true,
        updatedAt: true
      }
    })

    res.json({
      success: true,
      message: 'API Key 重新生成成功',
      data: updatedApiKey
    })
  } catch (error: any) {
    console.error('重新生成API Key失败:', error)
    res.status(500).json({
      success: false,
      message: '重新生成API Key失败',
      error: error.message
    })
  }
}

/**
 * 获取API Key使用统计
 */
export const getApiKeyStats = async (req: Request, res: Response) => {
  try {
    const { id } = req.params
    const { days = '30' } = req.query

    const daysNum = parseInt(days as string)
    const startDate = new Date()
    startDate.setDate(startDate.getDate() - daysNum)

    const [usageStats, recentUsage] = await Promise.all([
      // 基础统计
      prisma.apiKey.findUnique({
        where: { id },
        select: {
          usageCount: true,
          lastUsedAt: true,
          createdAt: true
        }
      }),
      // 最近使用记录（通过外部系统标识匹配）
      prisma.service.findMany({
        where: {
          source: 'EXTERNAL',
          externalSystemId: {
            in: await prisma.apiKey.findUnique({ 
              where: { id },
              select: { systemId: true }
            }).then(key => key ? [key.systemId] : [])
          },
          createdAt: { gte: startDate }
        },
        select: {
          createdAt: true,
          status: true
        },
        orderBy: { createdAt: 'desc' }
      })
    ])

    if (!usageStats) {
      return res.status(404).json({
        success: false,
        message: 'API Key 不存在'
      })
    }

    // 按日期统计使用量
    const dailyUsage = recentUsage.reduce((acc: any, service) => {
      const date = service.createdAt.toISOString().split('T')[0]
      acc[date] = (acc[date] || 0) + 1
      return acc
    }, {})

    res.json({
      success: true,
      data: {
        totalUsage: usageStats.usageCount,
        totalServices: recentUsage.length,
        lastUsedAt: usageStats.lastUsedAt,
        recentDays: daysNum,
        dailyUsage,
        recentUsageCount: recentUsage.length
      }
    })
  } catch (error: any) {
    console.error('获取API Key统计失败:', error)
    res.status(500).json({
      success: false,
      message: '获取API Key统计失败',
      error: error.message
    })
  }
}

// ==================== 当前用户（自服务）API Key 管理 ====================

/**
 * 获取当前用户的 API Key 列表
 */
export const getMyApiKeys = async (req: Request, res: Response) => {
  try {
    const authReq = req as AuthenticatedRequest
    const { page = '1', limit = '20', status, systemId } = req.query

    const pageNum = parseInt(page as string)
    const limitNum = parseInt(limit as string)
    const skip = (pageNum - 1) * limitNum

    const where: any = { createdBy: authReq.user!.id }
    if (status) where.status = status
    if (systemId) where.systemId = systemId

    const [apiKeys, total] = await Promise.all([
      prisma.apiKey.findMany({
        where,
        select: {
          id: true,
          name: true,
          systemId: true,
          description: true,
          status: true,
          expiresAt: true,
          lastUsedAt: true,
          usageCount: true,
          createdAt: true,
          updatedAt: true,
        },
        skip,
        take: limitNum,
        orderBy: { createdAt: 'desc' }
      }),
      prisma.apiKey.count({ where })
    ])

    res.json({
      success: true,
      data: {
        apiKeys,
        pagination: {
          page: pageNum,
          limit: limitNum,
          total,
          totalPages: Math.ceil(total / limitNum)
        }
      }
    })
  } catch (error: any) {
    console.error('获取当前用户 API Key 列表失败:', error)
    res.status(500).json({
      success: false,
      message: '获取当前用户 API Key 列表失败',
      error: error.message
    })
  }
}

/**
 * 获取当前用户的单个 API Key 详情
 */
export const getMyApiKey = async (req: Request, res: Response) => {
  try {
    const authReq = req as AuthenticatedRequest
    const { id } = req.params

    const apiKey = await prisma.apiKey.findFirst({
      where: { id, createdBy: authReq.user!.id },
      select: {
        id: true,
        name: true,
        systemId: true,
        description: true,
        status: true,
        expiresAt: true,
        lastUsedAt: true,
        usageCount: true,
        createdAt: true,
        updatedAt: true,
      }
    })

    if (!apiKey) {
      return res.status(404).json({ success: false, message: 'API Key 不存在' })
    }

    res.json({ success: true, data: apiKey })
  } catch (error: any) {
    console.error('获取当前用户 API Key 详情失败:', error)
    res.status(500).json({ success: false, message: '获取 API Key 详情失败', error: error.message })
  }
}

/**
 * 为当前用户创建 API Key
 */
export const createMyApiKey = async (req: Request, res: Response) => {
  try {
    const { name, systemId, description, expiresAt } = createApiKeySchema.parse(req.body)
    const authReq = req as AuthenticatedRequest

    const existingKey = await prisma.apiKey.findFirst({
      where: { systemId, status: { not: 'REVOKED' }, createdBy: authReq.user!.id }
    })

    if (existingKey) {
      return res.status(400).json({ success: false, message: '该系统已存在有效的API Key' })
    }

    const keyValue = await generateUniqueApiKey()

    const apiKey = await prisma.apiKey.create({
      data: {
        name,
        keyValue,
        systemId,
        description,
        expiresAt,
        createdBy: authReq.user!.id
      },
      select: {
        id: true,
        name: true,
        keyValue: true,
        systemId: true,
        description: true,
        status: true,
        expiresAt: true,
        createdAt: true
      }
    })

    res.status(201).json({ success: true, message: 'API Key 创建成功', data: apiKey })
  } catch (error: any) {
    console.error('创建当前用户 API Key 失败:', error)
    res.status(500).json({ success: false, message: '创建 API Key 失败', error: error.message })
  }
}

/**
 * 更新当前用户的 API Key
 */
export const updateMyApiKey = async (req: Request, res: Response) => {
  try {
    const authReq = req as AuthenticatedRequest
    const { id } = req.params
    const updateData = updateApiKeySchema.parse(req.body)

    const apiKey = await prisma.apiKey.findFirst({ where: { id, createdBy: authReq.user!.id } })
    if (!apiKey) {
      return res.status(404).json({ success: false, message: 'API Key 不存在' })
    }

    const updatedApiKey = await prisma.apiKey.update({
      where: { id },
      data: updateData,
      select: { id: true, name: true, systemId: true, description: true, status: true, expiresAt: true, updatedAt: true }
    })

    res.json({ success: true, message: 'API Key 更新成功', data: updatedApiKey })
  } catch (error: any) {
    console.error('更新当前用户 API Key 失败:', error)
    res.status(500).json({ success: false, message: '更新 API Key 失败', error: error.message })
  }
}

/**
 * 撤销当前用户的 API Key
 */
export const deleteMyApiKey = async (req: Request, res: Response) => {
  try {
    const authReq = req as AuthenticatedRequest
    const { id } = req.params

    const apiKey = await prisma.apiKey.findFirst({ where: { id, createdBy: authReq.user!.id } })
    if (!apiKey) {
      return res.status(404).json({ success: false, message: 'API Key 不存在' })
    }

    await prisma.apiKey.update({ where: { id }, data: { status: 'REVOKED' } })
    res.json({ success: true, message: 'API Key 已撤销' })
  } catch (error: any) {
    console.error('撤销当前用户 API Key 失败:', error)
    res.status(500).json({ success: false, message: '撤销 API Key 失败', error: error.message })
  }
}

/**
 * 为当前用户重新生成 API Key
 */
export const regenerateMyApiKey = async (req: Request, res: Response) => {
  try {
    const authReq = req as AuthenticatedRequest
    const { id } = req.params

    const apiKey = await prisma.apiKey.findFirst({ where: { id, createdBy: authReq.user!.id } })
    if (!apiKey) {
      return res.status(404).json({ success: false, message: 'API Key 不存在' })
    }

    const newKeyValue = await generateUniqueApiKey()

    const updatedApiKey = await prisma.apiKey.update({
      where: { id },
      data: { keyValue: newKeyValue, status: 'ACTIVE', usageCount: 0, lastUsedAt: null },
      select: { id: true, name: true, keyValue: true, systemId: true, status: true, updatedAt: true }
    })

    res.json({ success: true, message: 'API Key 重新生成成功', data: updatedApiKey })
  } catch (error: any) {
    console.error('为当前用户重新生成 API Key 失败:', error)
    res.status(500).json({ success: false, message: '重新生成 API Key 失败', error: error.message })
  }
}

/**
 * 获取当前用户 API Key 使用统计
 */
export const getMyApiKeyStats = async (req: Request, res: Response) => {
  try {
    const authReq = req as AuthenticatedRequest
    const { id } = req.params
    const { days = '30' } = req.query

    const owned = await prisma.apiKey.findFirst({ where: { id, createdBy: authReq.user!.id } })
    if (!owned) {
      return res.status(404).json({ success: false, message: 'API Key 不存在' })
    }

    const daysNum = parseInt(days as string)
    const startDate = new Date()
    startDate.setDate(startDate.getDate() - daysNum)

    const [usageStats, recentUsage] = await Promise.all([
      prisma.apiKey.findUnique({ where: { id }, select: { usageCount: true, lastUsedAt: true, createdAt: true, systemId: true } }),
      prisma.service.findMany({
        where: {
          source: 'EXTERNAL',
          externalSystemId: owned.systemId,
          createdAt: { gte: startDate }
        },
        select: { createdAt: true, status: true },
        orderBy: { createdAt: 'desc' }
      })
    ])

    if (!usageStats) {
      return res.status(404).json({ success: false, message: 'API Key 不存在' })
    }

    const dailyUsage = recentUsage.reduce((acc: any, service) => {
      const date = service.createdAt.toISOString().split('T')[0]
      acc[date] = (acc[date] || 0) + 1
      return acc
    }, {})

    res.json({
      success: true,
      data: {
        totalUsage: usageStats.usageCount,
        totalServices: recentUsage.length,
        lastUsedAt: usageStats.lastUsedAt,
        recentDays: daysNum,
        dailyUsage,
        recentUsageCount: recentUsage.length
      }
    })
  } catch (error: any) {
    console.error('获取当前用户 API Key 统计失败:', error)
    res.status(500).json({ success: false, message: '获取 API Key 统计失败', error: error.message })
  }
}