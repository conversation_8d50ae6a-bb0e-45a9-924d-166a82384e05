/**
 * 系统配置控制器
 * 
 * 提供系统配置的管理功能，包括配置的查看、修改、备份等
 * 该控制器展示了权限验证装饰器的完整使用方式
 */

import { Request, Response } from 'express'
import { z } from 'zod'
import { prisma } from '@/config/database.config'
import { enhancedEmailService } from '../services/enhanced-email.service'
import { AuthenticatedRequest } from '@/middleware/auth.middleware'

import {
  SYSTEM_PERMISSIONS,
  ADMIN_ALL,
} from '@/constants/permissions'
import { encrypt, decrypt } from '@/utils/crypto.util'
import * as os from 'os'
import { CacheService } from '@/config/redis.config'
import { SystemService } from '@/services/system.service'
import { SystemConfigCategory } from '@/types/system.types'

// ==================== 验证Schema ====================

const getConfigsSchema = z.object({
  category: z.string().optional(),
  encrypted: z.string().optional().transform(val => val === 'true'),
  page: z.string().optional().transform(val => val ? parseInt(val) : 1),
  limit: z.string().optional().transform(val => val ? parseInt(val) : 20)
})

const updateConfigSchema = z.object({
  value: z.any(),
  description: z.string().optional(),
  isEncrypted: z.boolean().optional(),
  category: z.string().optional()
})

const createConfigSchema = z.object({
  key: z.string().min(1).max(100),
  value: z.any(),
  description: z.string().optional(),
  category: z.string().min(1).max(50),
  isEncrypted: z.boolean().default(false),
  isPublic: z.boolean().default(false)
})

const batchUpdateSchema = z.object({
  configs: z.array(z.object({
    key: z.string(),
    value: z.any(),
    description: z.string().optional()
  })).min(1)
})

const backupConfigsSchema = z.object({
  includeEncrypted: z.boolean().default(false),
  categories: z.array(z.string()).optional()
})

const restoreConfigsSchema = z.object({
  backupData: z.string(),
  overwriteExisting: z.boolean().default(false)
})

// ==================== 系统配置查询 ====================

/**
 * 获取系统配置列表
 * 权限要求：system:read 或 system:write
 * 性能监控：2秒阈值
 * 缓存时间：5分钟
 */
export const getSystemConfigs = async (req: Request, res: Response) => {
  try {
    const { category, encrypted, page, limit } = getConfigsSchema.parse(req.query)
    const authReq = req as AuthenticatedRequest

    // 构建查询条件
    const where: any = {}
    if (category) {
      where.category = category
    }

    // 非管理员只能查看公开配置
    const hasAdminPermission = authReq.user?.permissions.includes(ADMIN_ALL) || 
                              authReq.user?.permissions.includes(SYSTEM_PERMISSIONS.SECURITY)
    if (!hasAdminPermission) {
      where.isPublic = true
    }

    // 分页计算
    const skip = (page - 1) * limit

    // 查询配置
    const [configs, total] = await Promise.all([
      prisma.systemConfig.findMany({
        where,
        select: {
          id: true,
          key: true,
          value: true,
          description: true,
          category: true,
          isEncrypted: true,
          isPublic: true,
          isSystem: true,
          dataType: true,
          defaultValue: true,
          updatedAt: true,
          createdAt: true,
          updatedByUser: {
            select: {
              username: true,
              fullName: true
            }
          }
        },
        skip,
        take: limit,
        orderBy: { key: 'asc' }
      }),
      prisma.systemConfig.count({ where })
    ])

    // 解密加密的配置值（仅管理员）
    const processedConfigs = await Promise.all(
      configs.map(async (config: any) => {
        let value = config.value ?? config.defaultValue
        if (config.isEncrypted && hasAdminPermission && value) {
          try {
            value = await decrypt(value)
          } catch (error) {
            console.warn(`解密配置失败: ${config.key}`, error)
            value = '[解密失败]'
          }
        }
        if (config.isEncrypted && !hasAdminPermission) {
          value = '[加密内容]'
        }

        // 前端所需的简化类型
        const mapType = (dt: any, v: any): 'string' | 'number' | 'boolean' | 'json' | 'array' => {
          switch (String(dt)) {
            case 'BOOLEAN': return 'boolean'
            case 'NUMBER': return 'number'
            case 'JSON': return 'json'
            default: {
              if (typeof v === 'boolean') return 'boolean'
              if (typeof v === 'number') return 'number'
              if (typeof v === 'object') return 'json'
              return 'string'
            }
          }
        }

        return {
          key: config.key,
          label: config.key,
          value,
          type: mapType(config.dataType, value),
          category: config.category,
          description: config.description,
          encrypted: Boolean(config.isEncrypted),
          isSystem: Boolean(config.isSystem),
          isPublic: Boolean(config.isPublic),
          defaultValue: config.defaultValue ?? null,
          updatedAt: config.updatedAt,
          createdAt: config.createdAt
        }
      })
    )

    // 获取分类统计
    const categoryStats = await prisma.systemConfig.groupBy({
      by: ['category'],
      where: !hasAdminPermission ? { isPublic: true } : {},
      _count: { category: true }
    })

    res.json({
      success: true,
      data: {
        configs: processedConfigs,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit)
        },
        categoryStats: categoryStats.map(stat => ({
          category: stat.category,
          count: stat._count.category
        }))
      }
    })
  } catch (error: any) {
    console.error('获取系统配置失败:', error)
    res.status(500).json({
      success: false,
      message: '获取系统配置失败',
      error: error.message
    })
  }
}

/**
 * 获取单个系统配置
 * 权限要求：system:read
 */
export const getSystemConfig = async (req: Request, res: Response) => {
  try {
    const { key } = req.params
    const authReq = req as AuthenticatedRequest

    if (!key) {
      return res.status(400).json({
        success: false,
        message: '配置键不能为空'
      })
    }

    const config = await prisma.systemConfig.findUnique({
      where: { key },
      include: {
        updatedByUser: {
          select: {
            username: true,
            fullName: true
          }
        },
        configHistory: {
          take: 10,
          orderBy: { createdAt: 'desc' },
          include: {
            changedByUser: {
              select: {
                username: true,
                fullName: true
              }
            }
          }
        }
      }
    })

    if (!config) {
      return res.status(404).json({
        success: false,
        message: '配置项不存在'
      })
    }

    // 权限检查：非公开配置需要安全权限
    const hasSecurityPermission = authReq.user?.permissions.includes(ADMIN_ALL) || 
                                  authReq.user?.permissions.includes(SYSTEM_PERMISSIONS.SECURITY)
    
    if (!config.isPublic && !hasSecurityPermission) {
      return res.status(403).json({
        success: false,
        message: '无权访问该配置项'
      })
    }

    // 解密加密值
    if (config.isEncrypted && config.value && hasSecurityPermission) {
      try {
        config.value = await decrypt(config.value as string)
      } catch (error) {
        console.warn(`解密配置失败: ${config.key}`, error)
        config.value = '[解密失败]'
      }
    } else if (config.isEncrypted) {
      config.value = '[加密内容]'
    }

    res.json({
      success: true,
      data: config
    })
  } catch (error: any) {
    console.error('获取系统配置详情失败:', error)
    res.status(500).json({
      success: false,
      message: '获取系统配置详情失败',
      error: error.message
    })
  }
}

// ==================== 系统配置修改 ====================

/**
 * 创建系统配置
 * 权限要求：system:write
 * 限流：每分钟最多10次
 */
export const createSystemConfig = async (req: Request, res: Response) => {
  try {
    const { key, value, description, category, isEncrypted, isPublic } = createConfigSchema.parse(req.body)
    const authReq = req as AuthenticatedRequest

    // 检查配置键是否已存在
    const existingConfig = await prisma.systemConfig.findUnique({
      where: { key }
    })

    if (existingConfig) {
      return res.status(400).json({
        success: false,
        message: '配置键已存在'
      })
    }

    // 处理加密值
    let processedValue = value
    if (isEncrypted) {
      processedValue = await encrypt(JSON.stringify(value))
    }

    // 创建配置
    const config = await prisma.systemConfig.create({
      data: {
        key,
        value: processedValue,
        description: description || null,
        category: category as any,
        isEncrypted,
        isPublic,
        updatedBy: authReq.user!.id
      }
    })

    // 记录历史
    await prisma.systemConfigHistory.create({
      data: {
        configId: config.id,
        oldValue: null as any,
        newValue: processedValue as any,
        changeReason: '创建配置',
        changedBy: authReq.user!.id
      }
    })

    res.status(201).json({
      success: true,
      message: '配置创建成功',
      data: {
        id: config.id,
        key: config.key,
        category: config.category,
        isEncrypted: config.isEncrypted,
        isPublic: config.isPublic
      }
    })
  } catch (error: any) {
    console.error('创建系统配置失败:', error)
    res.status(500).json({
      success: false,
      message: '创建系统配置失败',
      error: error.message
    })
  }
}

/**
 * 更新系统配置
 * 权限要求：system:write，敏感配置需要 system:security
 */
export const updateSystemConfig = async (req: Request, res: Response) => {
  try {
    const { key } = req.params
    const { value, description, isEncrypted, category } = updateConfigSchema.parse(req.body)
    const authReq = req as AuthenticatedRequest

    if (!key) {
      return res.status(400).json({
        success: false,
        message: '配置键不能为空'
      })
    }

    // 获取现有配置
    const existingConfig = await prisma.systemConfig.findUnique({
      where: { key }
    })

    if (!existingConfig) {
      return res.status(404).json({
        success: false,
        message: '配置项不存在'
      })
    }

    // 处理新值
    let processedValue = value
    if (isEncrypted !== undefined ? isEncrypted : existingConfig.isEncrypted) {
      processedValue = await encrypt(JSON.stringify(value))
    }

    // 更新配置
    const updatedConfig = await prisma.systemConfig.update({
      where: { key },
      data: {
        value: processedValue,
        description: description !== undefined ? description : existingConfig.description,
        category: category !== undefined ? category as any : existingConfig.category,
        isEncrypted: isEncrypted !== undefined ? isEncrypted : existingConfig.isEncrypted,
        updatedBy: authReq.user!.id
      }
    })

    // 记录历史
    await prisma.systemConfigHistory.create({
      data: {
        configId: existingConfig.id,
        oldValue: existingConfig.value as any,
        newValue: processedValue as any,
        changeReason: '更新配置',
        changedBy: authReq.user!.id
      }
    })

    // 如果更新的是邮件相关配置，重新加载邮件服务配置
    if (existingConfig.category === 'EMAIL') {
      console.log(`📧 Email configuration updated: ${key}, reloading email service...`)
      try {
        await enhancedEmailService.reloadConfig()
      } catch (error) {
        console.error('Failed to reload email service config:', error)
      }
    }

    res.json({
      success: true,
      message: '配置更新成功',
      data: {
        key: updatedConfig.key,
        category: updatedConfig.category,
        isEncrypted: updatedConfig.isEncrypted,
        updatedAt: updatedConfig.updatedAt
      }
    })
  } catch (error: any) {
    console.error('更新系统配置失败:', error)
    res.status(500).json({
      success: false,
      message: '更新系统配置失败',
      error: error.message
    })
  }
}

/**
 * 批量更新系统配置
 * 权限要求：system:security
 * 限流：每分钟最多5次
 */
export const batchUpdateConfigs = async (req: Request, res: Response) => {
  try {
    const { configs } = batchUpdateSchema.parse(req.body)
    const authReq = req as AuthenticatedRequest

    const results = []
    const errors = []

    for (const configUpdate of configs) {
      try {
        const existingConfig = await prisma.systemConfig.findUnique({
          where: { key: configUpdate.key }
        })

        if (!existingConfig) {
          errors.push({ key: configUpdate.key, error: '配置项不存在' })
          continue
        }

        // 处理值加密
        let processedValue = configUpdate.value
        if (existingConfig.isEncrypted) {
          processedValue = await encrypt(JSON.stringify(configUpdate.value))
        }

        // 更新配置
        const updated = await prisma.systemConfig.update({
          where: { key: configUpdate.key },
          data: {
            value: processedValue,
            description: configUpdate.description !== undefined ? configUpdate.description : existingConfig.description,
            updatedBy: authReq.user!.id
          }
        })

        // 记录历史
        await prisma.systemConfigHistory.create({
          data: {
            configId: existingConfig.id,
            oldValue: existingConfig.value as any,
            newValue: processedValue as any,
            changeReason: '批量更新配置',
            changedBy: authReq.user!.id
          }
        })

        // 如果更新的是邮件相关配置，标记需要重新加载
        if (existingConfig.category === 'EMAIL') {
          // 这里我们暂时只记录，在循环结束后统一重新加载
          console.log(`📧 Email configuration updated in batch: ${configUpdate.key}`)
        }

        results.push({ key: configUpdate.key, success: true })
      } catch (error: any) {
        errors.push({ key: configUpdate.key, error: error.message })
      }
    }

    // 检查是否有邮件配置被更新，如果有则重新加载邮件服务
    const hasEmailConfigUpdate = results.some(r => 
      configs.find(c => c.key === r.key)
    )
    if (hasEmailConfigUpdate) {
      console.log('📧 Email configurations updated in batch, reloading email service...')
      try {
        await enhancedEmailService.reloadConfig()
      } catch (error) {
        console.error('Failed to reload email service config:', error)
      }
    }

    res.json({
      success: true,
      message: `批量更新完成，成功：${results.length}项，失败：${errors.length}项`,
      data: {
        successful: results,
        errors: errors,
        summary: {
          total: configs.length,
          successful: results.length,
          failed: errors.length
        }
      }
    })
  } catch (error: any) {
    console.error('批量更新系统配置失败:', error)
    res.status(500).json({
      success: false,
      message: '批量更新系统配置失败',
      error: error.message
    })
  }
}

// ==================== 高级功能 ====================

/**
 * 删除系统配置
 * 权限要求：system:security（仅超级管理员）
 */
export const deleteSystemConfig = async (req: Request, res: Response) => {
  try {
    const { key } = req.params
    const authReq = req as AuthenticatedRequest

    if (!key) {
      return res.status(400).json({
        success: false,
        message: '配置键不能为空'
      })
    }

    const config = await prisma.systemConfig.findUnique({
      where: { key }
    })

    if (!config) {
      return res.status(404).json({
        success: false,
        message: '配置项不存在'
      })
    }

    // 记录删除历史
    await prisma.systemConfigHistory.create({
      data: {
        configId: config.id,
        oldValue: config.value as any,
        newValue: null as any,
        changeReason: '删除配置',
        changedBy: authReq.user!.id
      }
    })

    // 删除配置
    await prisma.systemConfig.delete({
      where: { key }
    })

    res.json({
      success: true,
      message: '配置删除成功'
    })
  } catch (error: any) {
    console.error('删除系统配置失败:', error)
    res.status(500).json({
      success: false,
      message: '删除系统配置失败',
      error: error.message
    })
  }
}

/**
 * 备份系统配置
 * 权限要求：system:backup
 */
export const backupSystemConfigs = async (req: Request, res: Response) => {
  try {
    const { includeEncrypted, categories } = backupConfigsSchema.parse(req.body)
    const authReq = req as AuthenticatedRequest

    // 构建查询条件
    const where: any = {}
    if (categories && categories.length > 0) {
      where.category = { in: categories }
    }
    if (!includeEncrypted) {
      where.isEncrypted = false
    }

    const configs = await prisma.systemConfig.findMany({
      where,
      select: {
        key: true,
        value: true,
        description: true,
        category: true,
        isEncrypted: true,
        isPublic: true
      }
    })

    // 处理加密配置
    const backupData = await Promise.all(
      configs.map(async (config) => {
        let value = config.value
        if (config.isEncrypted && includeEncrypted) {
          try {
            value = await decrypt(config.value as string)
          } catch (error) {
            console.warn(`解密配置失败: ${config.key}`)
            value = '[解密失败]'
          }
        } else if (config.isEncrypted) {
          value = '[加密内容-已跳过]'
        }

        return {
          ...config,
          value
        }
      })
    )

    const backup = {
      timestamp: new Date().toISOString(),
      version: '1.0',
      includeEncrypted,
      categories: categories || ['all'],
      totalConfigs: backupData.length,
      backupBy: authReq.user!.username,
      configs: backupData
    }

    res.json({
      success: true,
      message: '配置备份成功',
      data: backup
    })
  } catch (error: any) {
    console.error('备份系统配置失败:', error)
    res.status(500).json({
      success: false,
      message: '备份系统配置失败',
      error: error.message
    })
  }
}

/**
 * 恢复系统配置
 * 权限要求：超级管理员
 * 限流：每小时最多1次
 */
export const restoreSystemConfigs = async (req: Request, res: Response) => {
  try {
    const { backupData, overwriteExisting } = restoreConfigsSchema.parse(req.body)
    const authReq = req as AuthenticatedRequest

    let backup: any
    try {
      backup = JSON.parse(backupData)
    } catch (error) {
      return res.status(400).json({
        success: false,
        message: '备份数据格式无效'
      })
    }

    if (!backup.configs || !Array.isArray(backup.configs)) {
      return res.status(400).json({
        success: false,
        message: '备份数据结构无效'
      })
    }

    const results = []
    const errors = []

    for (const configData of backup.configs) {
      try {
        const existingConfig = await prisma.systemConfig.findUnique({
          where: { key: configData.key }
        })

        if (existingConfig && !overwriteExisting) {
          errors.push({ key: configData.key, error: '配置已存在，跳过' })
          continue
        }

        // 处理加密
        let processedValue = configData.value
        if (configData.isEncrypted && configData.value !== '[加密内容-已跳过]') {
          processedValue = await encrypt(JSON.stringify(configData.value))
        }

        if (existingConfig) {
          // 更新现有配置
          await prisma.systemConfig.update({
            where: { key: configData.key },
            data: {
              value: processedValue,
              description: configData.description,
              category: configData.category,
              isEncrypted: configData.isEncrypted,
              isPublic: configData.isPublic,
              updatedBy: authReq.user!.id
            }
          })
        } else {
          // 创建新配置
          await prisma.systemConfig.create({
            data: {
              key: configData.key,
              value: processedValue,
              description: configData.description,
              category: configData.category,
              isEncrypted: configData.isEncrypted,
              isPublic: configData.isPublic,
              updatedBy: authReq.user!.id
            }
          })
        }

        results.push({ key: configData.key, success: true })
      } catch (error: any) {
        errors.push({ key: configData.key, error: error.message })
      }
    }

    res.json({
      success: true,
      message: `配置恢复完成，成功：${results.length}项，失败：${errors.length}项`,
      data: {
        successful: results,
        errors: errors,
        summary: {
          total: backup.configs.length,
          successful: results.length,
          failed: errors.length,
          backupTimestamp: backup.timestamp,
          backupBy: backup.backupBy
        }
      }
    })
  } catch (error: any) {
    console.error('恢复系统配置失败:', error)
    res.status(500).json({
      success: false,
      message: '恢复系统配置失败',
      error: error.message
    })
  }
}

// ==================== 前端所需的额外端点 ====================

/**
 * 获取按分组组织的系统配置
 * 前端需要的分组显示格式
 */
export const getSystemConfigGroups = async (req: Request, res: Response) => {
  try {
    const authReq = req as AuthenticatedRequest

    // 权限检查
    const hasAdminPermission = authReq.user?.permissions.includes(ADMIN_ALL) || 
                              authReq.user?.permissions.includes(SYSTEM_PERMISSIONS.SECURITY)

    // 查询配置
    const where: any = {}
    if (!hasAdminPermission) {
      where.isPublic = true
    }

    const configs = await prisma.systemConfig.findMany({
      where,
      select: {
        id: true,
        key: true,
        value: true,
        description: true,
        category: true,
        dataType: true,
        isEncrypted: true,
        isPublic: true,
        isSystem: true,
        displayOrder: true,
          defaultValue: true,
        updatedAt: true,
        createdAt: true,
        updatedByUser: {
          select: {
            username: true,
            fullName: true
          }
        }
      },
      orderBy: [
        { category: 'asc' },
        { displayOrder: 'asc' },
        { key: 'asc' }
      ]
    })

    // 解密加密配置（仅管理员）
    const processedConfigs = await Promise.all(
      configs.map(async (config: any) => {
        let value = config.value ?? config.defaultValue
        if (config.isEncrypted && hasAdminPermission && value) {
          try {
            value = await decrypt(value)
          } catch (error) {
            console.warn(`解密配置失败: ${config.key}`, error)
            value = '[解密失败]'
          }
        }
        if (config.isEncrypted && !hasAdminPermission) {
          value = '[加密内容]'
        }

        const mapType = (dt: any, v: any): 'string' | 'number' | 'boolean' | 'json' | 'array' => {
          switch (String(dt)) {
            case 'BOOLEAN': return 'boolean'
            case 'NUMBER': return 'number'
            case 'JSON': return 'json'
            default: {
              if (typeof v === 'boolean') return 'boolean'
              if (typeof v === 'number') return 'number'
              if (typeof v === 'object') return 'json'
              return 'string'
            }
          }
        }

        return {
          key: config.key,
          label: config.key,
          value,
          type: mapType(config.dataType, value),
          category: config.category,
          description: config.description,
          encrypted: Boolean(config.isEncrypted),
          isSystem: Boolean(config.isSystem),
          isPublic: Boolean(config.isPublic),
          defaultValue: config.defaultValue ?? null,
          updatedAt: config.updatedAt,
          createdAt: config.createdAt
        }
      })
    )

    // 按分类分组
    const groups = processedConfigs.reduce((acc: any[], config: any) => {
      let group = acc.find(g => g.category === config.category)
      if (!group) {
        group = {
          category: config.category,
          label: getCategoryLabel(config.category),
          description: getCategoryDescription(config.category),
          configs: []
        }
        acc.push(group)
      }
      group.configs.push(config)
      return acc
    }, [])

    res.json({
      success: true,
      data: groups
    })
  } catch (error: any) {
    console.error('获取系统配置分组失败:', error)
    res.status(500).json({
      success: false,
      message: '获取系统配置分组失败',
      error: error.message
    })
  }
}

/**
 * 获取系统配置分类统计
 */
export const getSystemConfigCategories = async (req: Request, res: Response) => {
  try {
    const authReq = req as AuthenticatedRequest

    // 权限检查
    const hasAdminPermission = authReq.user?.permissions.includes(ADMIN_ALL) || 
                              authReq.user?.permissions.includes(SYSTEM_PERMISSIONS.SECURITY)

    const where: any = {}
    if (!hasAdminPermission) {
      where.isPublic = true
    }

    const categoryStats = await prisma.systemConfig.groupBy({
      by: ['category'],
      where,
      _count: { category: true }
    })

    const categories = categoryStats.map(stat => ({
      category: stat.category,
      label: getCategoryLabel(stat.category),
      description: getCategoryDescription(stat.category),
      configCount: stat._count.category
    }))

    res.json({
      success: true,
      data: categories
    })
  } catch (error: any) {
    console.error('获取系统配置分类失败:', error)
    res.status(500).json({
      success: false,
      message: '获取系统配置分类失败',
      error: error.message
    })
  }
}

/**
 * 获取公开配置（无需认证）
 */
export const getPublicConfigs = async (req: Request, res: Response) => {
  try {
    const configs = await prisma.systemConfig.findMany({
      where: {
        isPublic: true,
        isEncrypted: false // 公开配置不包含加密内容
      },
      select: {
        key: true,
        value: true,
        category: true,
        dataType: true
      }
    })

    // 转换为键值对格式
    const configMap = configs.reduce((acc: any, config) => {
      acc[config.key] = config.value
      return acc
    }, {})

    res.json({
      success: true,
      data: configMap
    })
  } catch (error: any) {
    console.error('获取公开配置失败:', error)
    res.status(500).json({
      success: false,
      message: '获取公开配置失败',
      error: error.message
    })
  }
}

/**
 * 获取系统信息（供前端系统配置页展示）
 */
export const getSystemInfo = async (_req: Request, res: Response) => {
  try {
    const totalMem = os.totalmem()
    const freeMem = os.freemem()
    const usedMem = totalMem - freeMem

    const data = {
      version: process.env['APP_VERSION'] || '1.0.0',
      environment: process.env['NODE_ENV'] || 'development',
      uptime: Math.floor(process.uptime()),
      buildTime: process.env['BUILD_TIME'] || new Date().toISOString(),
      nodeVersion: process.version,
      platform: `${os.platform()}-${os.arch()}`,
      memory: {
        used: Math.round(usedMem / 1024 / 1024),
        total: Math.round(totalMem / 1024 / 1024),
        usage: Math.round((usedMem / totalMem) * 100)
      }
    }

    return res.json({ success: true, data })
  } catch (error: any) {
    return res.status(500).json({ success: false, message: '获取系统信息失败', error: error.message })
  }
}

/**
 * 获取健康状态（格式与前端期望对齐）
 */
export const getHealthStatus = async (_req: Request, res: Response) => {
  try {
    const db = await SystemService.getDatabaseStatus()
    const redis = await SystemService.getRedisStatus()
    const checksBase = await SystemService.getHealthCheck()

    const services = [
      {
        name: 'database',
        status: db.status === 'connected' ? 'up' : 'down',
        responseTime: (db as any).responseTime,
        message: db.status === 'connected' ? '数据库连接正常' : '数据库连接异常'
      },
      {
        name: 'redis',
        status: redis.status === 'connected' ? 'up' : 'down',
        responseTime: (redis as any).responseTime,
        message: redis.status === 'connected' ? 'Redis 连接正常' : 'Redis 连接异常'
      },
      {
        name: 'smtp',
        status: 'warning' as const,
        message: '需要使用“测试”按钮验证邮件配置'
      }
    ]

    const checks = [
      {
        name: '磁盘空间',
        status: (checksBase.checks as any).diskSpace?.status === 'healthy' ? 'pass' : (checksBase.checks as any).diskSpace?.status === 'warning' ? 'warn' : 'fail',
        message: (checksBase.checks as any).diskSpace?.message
      },
      {
        name: '内存使用',
        status: (checksBase.checks as any).memory?.status === 'healthy' ? 'pass' : (checksBase.checks as any).memory?.status === 'warning' ? 'warn' : 'fail',
        message: (checksBase.checks as any).memory?.message
      }
    ]

    const anyDown = services.some(s => s.status === 'down')
    const anyWarn = services.some(s => s.status === 'warning') || checks.some(c => c.status === 'warn')
    const status: 'healthy' | 'warning' | 'critical' = anyDown ? 'critical' : anyWarn ? 'warning' : 'healthy'

    return res.json({ success: true, data: { status, services, checks } })
  } catch (error: any) {
    return res.status(500).json({ success: false, message: '获取健康状态失败', error: error.message })
  }
}

/**
 * 测试连通性：database | redis | smtp | sms
 */
export const testConnection = async (req: Request, res: Response) => {
  try {
    const { type } = req.params as { type: 'database' | 'redis' | 'smtp' | 'sms' }
    switch (type) {
      case 'database': {
        const db = await SystemService.getDatabaseStatus()
        return res.json({ success: db.status === 'connected', data: { success: db.status === 'connected', message: db.status === 'connected' ? '数据库连接正常' : '数据库连接异常', details: db } })
      }
      case 'redis': {
        const r = await SystemService.getRedisStatus()
        return res.json({ success: r.status === 'connected', data: { success: r.status === 'connected', message: r.status === 'connected' ? 'Redis 连接正常' : 'Redis 连接异常', details: r } })
      }
      case 'smtp': {
        // 仅做配置有效性快速检测，避免强依赖真实邮件发送
        const emailConfigs = await SystemService.getSystemConfigs({ category: SystemConfigCategory.EMAIL }, true)
        const cfg = emailConfigs.reduce((m: any, c: any) => { m[c.key] = c.value; return m }, {})
        const enabled = !!cfg.EMAIL_ENABLED
        const ok = enabled && !!cfg.SMTP_HOST
        return res.json({ success: ok, data: { success: ok, message: ok ? '邮件配置看起来有效' : '邮件配置缺失或未启用', details: { enabled, host: cfg.SMTP_HOST || '' } } })
      }
      case 'sms': {
        const smsConfigs = await SystemService.getSystemConfigs({ category: SystemConfigCategory.SMS }, true)
        const cfg = smsConfigs.reduce((m: any, c: any) => { m[c.key] = c.value; return m }, {})
        const enabled = !!cfg.SMS_ENABLED
        const ok = enabled && !!cfg.ALI_SMS_ACCESS_KEY_ID
        return res.json({ success: ok, data: { success: ok, message: ok ? '短信配置看起来有效' : '短信配置缺失或未启用' } })
      }
      default:
        return res.status(400).json({ success: false, message: '不支持的测试类型' })
    }
  } catch (error: any) {
    return res.status(500).json({ success: false, message: '连通性测试失败', error: error.message })
  }
}

/**
 * 清理缓存
 */
export const clearCache = async (req: Request, res: Response) => {
  try {
    const { type } = req.body as { type?: 'all' | 'config' | 'user' | 'permission' }
    let cleared = 0
  const delByPattern = async (pattern: string) => {
      const keys = await CacheService.keys(pattern)
      for (const k of keys) {
        await CacheService.del(k)
        cleared += 1
      }
    }

    if (!type || type === 'all') {
      await delByPattern('system:*')
      await delByPattern('user:*')
      await delByPattern('permission:*')
    } else if (type === 'config') {
      await delByPattern('system:config:*')
      await delByPattern('system:configs:*')
    } else if (type === 'user') {
      await delByPattern('user:*')
    } else if (type === 'permission') {
      await delByPattern('permission:*')
    }

    return res.json({ success: true, data: { cleared, message: '缓存清理完成' } })
  } catch (error: any) {
    return res.status(500).json({ success: false, message: '缓存清理失败', error: error.message })
  }
}

/**
 * 通过 key 获取配置历史（前端期望 /system-config/:key/history）
 */
export const getConfigHistoryByKey = async (req: Request, res: Response) => {
  try {
    const key = req.params['key'] as string
    const { page = '1', limit = '20' } = req.query
    const pageNum = parseInt(page as string) || 1
    const limitNum = parseInt(limit as string) || 20

    const config = await prisma.systemConfig.findFirst({ where: { key }, select: { id: true } })
    if (!config) {
      return res.status(404).json({ success: false, message: '配置项不存在' })
    }

    const skip = (pageNum - 1) * limitNum
    const [history, total] = await Promise.all([
      prisma.systemConfigHistory.findMany({
        where: { configId: config.id },
        skip,
        take: limitNum,
        orderBy: { createdAt: 'desc' },
        include: {
          changedByUser: { select: { username: true, fullName: true } }
        }
      }),
      prisma.systemConfigHistory.count({ where: { configId: config.id } })
    ])

    return res.json({
      success: true,
      data: {
        history: history.map(r => ({
          id: r.id,
          key,
          oldValue: r.oldValue,
          newValue: r.newValue,
          changedAt: r.createdAt,
          changedBy: r.changedBy,
          userInfo: r.changedByUser ? { username: r.changedByUser.username, fullName: r.changedByUser.fullName, id: r.changedBy } : undefined
        })),
        pagination: { page: pageNum, limit: limitNum, total, totalPages: Math.ceil(total / limitNum) }
      }
    })
  } catch (error: any) {
    return res.status(500).json({ success: false, message: '获取配置历史失败', error: error.message })
  }
}

/** 重置配置为默认值（按 key） */
export const resetConfigByKey = async (req: Request, res: Response) => {
  try {
    const key = req.params['key'] as string
    const currentUser = (req as any).user
    const config = await prisma.systemConfig.findFirst({ where: { key }, select: { key: true, category: true } })
    if (!config) return res.status(404).json({ success: false, message: '配置项不存在' })

    const updated = await SystemService.resetConfigToDefault(config.category as any, config.key, currentUser.userId)
    return res.json({ success: true, message: '配置已重置为默认值', data: updated })
  } catch (error: any) {
    return res.status(500).json({ success: false, message: '重置配置失败', error: error.message })
  }
}

/** 批量重置配置（按 keys） */
export const batchResetConfigs = async (req: Request, res: Response) => {
  try {
    const { keys } = req.body as { keys: string[] }
    const currentUser = (req as any).user
    if (!Array.isArray(keys) || keys.length === 0) {
      return res.status(400).json({ success: false, message: 'keys 不能为空' })
    }
    let successCount = 0
    let errors: Array<{ key: string; message: string }> = []
    for (const key of keys) {
      try {
        const config = await prisma.systemConfig.findUnique({ where: { key }, select: { key: true, category: true } })
        if (!config) { errors.push({ key, message: '配置不存在' }); continue }
        await SystemService.resetConfigToDefault(config.category as any, config.key, currentUser.userId)
        successCount++
      } catch (e: any) {
        errors.push({ key, message: e?.message || '重置失败' })
      }
    }
    return res.json({ success: errors.length === 0, data: { successCount, errors } })
  } catch (error: any) {
    return res.status(500).json({ success: false, message: '批量重置失败', error: error.message })
  }
}

/** 导出配置（对齐前端期望的 data 结构） */
export const exportConfigs = async (req: Request, res: Response) => {
  try {
    const { categories } = req.query as { categories?: string }
    const list = categories ? (categories.split(',') as any[]) : undefined
    const exportData = await SystemService.exportConfigs(list as any)
    const data = {
      version: exportData.version,
      exportedAt: exportData.timestamp,
      configs: exportData.configs.map(c => ({ key: c.key, value: c.value, type: String(c.dataType || 'STRING').toLowerCase(), category: c.category }))
    }
    return res.json({ success: true, data })
  } catch (error: any) {
    return res.status(500).json({ success: false, message: '导出系统配置失败', error: error.message })
  }
}

// ==================== 工具函数 ====================

/**
 * 获取分类标签
 */
function getCategoryLabel(category: string): string {
  const labels: Record<string, string> = {
    'GENERAL': '基本设置',
    'SECURITY': '安全配置',
    'EMAIL': '邮件配置',
    'SMS': '短信配置',
    'NOTIFICATION': '通知配置',
    'STORAGE': '存储配置',
    'BACKUP': '备份配置',
    'SYSTEM': '系统配置',
    'INTEGRATION': '集成配置',
    'CUSTOM': '自定义配置'
  }
  return labels[category] || category
}

/**
 * 获取分类描述
 */
function getCategoryDescription(category: string): string {
  const descriptions: Record<string, string> = {
    'GENERAL': '网站基本信息和通用设置',
    'SECURITY': '安全策略和权限控制配置',
    'EMAIL': '邮件服务器和发送配置',
    'SMS': '短信服务提供商配置',
    'NOTIFICATION': '系统通知和提醒配置',
    'STORAGE': '文件存储和上传配置',
    'BACKUP': '数据备份和恢复配置',
    'SYSTEM': '系统运行和性能配置',
    'INTEGRATION': '第三方服务集成配置',
    'CUSTOM': '用户自定义配置项'
  }
  return descriptions[category] || ''
}

// ==================== 配置测试功能 ====================

/**
 * 测试邮件配置
 * 权限要求：system:write
 */
export const testEmailConfig = async (req: Request, res: Response) => {
  try {
    const { testEmail } = req.body
    
    if (!testEmail) {
      return res.status(400).json({
        success: false,
        message: '请提供测试邮箱地址'
      })
    }

    // 验证邮箱格式
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(testEmail)) {
      return res.status(400).json({
        success: false,
        message: '邮箱地址格式不正确'
      })
    }

    // 发送测试邮件
    const result = await enhancedEmailService.sendTestEmail(testEmail)

    res.json({
      success: result.success,
      message: result.message,
      data: result.details
    })
  } catch (error: any) {
    console.error('邮件配置测试失败:', error)
    res.status(500).json({
      success: false,
      message: '邮件配置测试失败',
      error: error.message
    })
  }
}

/**
 * 测试模板邮件发送
 * 权限要求：system:write
 */
export const testTemplateEmail = async (req: Request, res: Response) => {
  try {
    const { templateId, testEmail, variables } = req.body
    
    if (!templateId) {
      return res.status(400).json({
        success: false,
        message: '请提供模板ID'
      })
    }

    if (!testEmail) {
      return res.status(400).json({
        success: false,
        message: '请提供测试邮箱地址'
      })
    }

    // 验证邮箱格式
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(testEmail)) {
      return res.status(400).json({
        success: false,
        message: '邮箱地址格式不正确'
      })
    }

    // 使用模板发送测试邮件
    const result = await enhancedEmailService.sendEmailWithTemplate(
      templateId,
      testEmail,
      variables || {}
    )

    res.json({
      success: result.success,
      message: result.message,
      data: {
        logId: result.logId,
        templateId,
        recipient: testEmail,
        variables: variables || {}
      }
    })
  } catch (error: any) {
    console.error('模板邮件测试失败:', error)
    res.status(500).json({
      success: false,
      message: '模板邮件测试失败',
      error: error.message
    })
  }
}

/**
 * 获取邮件服务状态
 * 权限要求：system:read
 */
export const getEmailServiceStatus = async (req: Request, res: Response) => {
  try {
    const isConfigured = enhancedEmailService.isConfigured()
    const configInfo = enhancedEmailService.getConfigInfo()
    
    let connectionStatus = 'unknown'
    if (isConfigured) {
      const testResult = await enhancedEmailService.testConnection()
      connectionStatus = testResult ? 'connected' : 'failed'
    } else {
      connectionStatus = 'not_configured'
    }

    // 获取邮件发送统计（最近7天）
    const endDate = new Date()
    const startDate = new Date()
    startDate.setDate(endDate.getDate() - 7)
    
    const stats = await enhancedEmailService.getEmailStats(startDate, endDate)

    res.json({
      success: true,
      data: {
        configured: isConfigured,
        connectionStatus,
        config: configInfo,
        stats,
        statusText: {
          'connected': '连接正常',
          'failed': '连接失败',
          'not_configured': '未配置',
          'unknown': '状态未知'
        }[connectionStatus] || '状态未知'
      }
    })
  } catch (error: any) {
    console.error('获取邮件服务状态失败:', error)
    res.status(500).json({
      success: false,
      message: '获取邮件服务状态失败',
      error: error.message
    })
  }
}

/**
 * 测试其他服务连接（短信、备份等）
 * 权限要求：system:write
 */
export const testServiceConnection = async (req: Request, res: Response) => {
  try {
    const { serviceType, testData } = req.body
    
    if (!serviceType) {
      return res.status(400).json({
        success: false,
        message: '请指定服务类型'
      })
    }

    let result: { success: boolean; message: string; details?: any }

    switch (serviceType) {
      case 'smtp':
      case 'email':
        // 邮件服务测试
        if (!testData?.testEmail) {
          return res.status(400).json({
            success: false,
            message: '请提供测试邮箱地址'
          })
        }
        result = await enhancedEmailService.sendTestEmail(testData.testEmail)
        break

      case 'sms':
        // 短信服务测试（模拟）
        result = {
          success: false,
          message: '短信服务暂未配置',
          details: {
            serviceType: 'sms',
            status: 'not_implemented'
          }
        }
        break

      case 'backup':
        // 备份服务测试（模拟）
        result = {
          success: false,
          message: '备份服务暂未配置',
          details: {
            serviceType: 'backup',
            status: 'not_implemented'
          }
        }
        break

      case 'database':
        // 数据库连接测试
        try {
          await prisma.$queryRaw`SELECT 1`
          result = {
            success: true,
            message: '数据库连接正常',
            details: {
              serviceType: 'database',
              status: 'connected',
              testTime: new Date().toISOString()
            }
          }
        } catch (error: any) {
          result = {
            success: false,
            message: `数据库连接失败: ${error.message}`,
            details: {
              serviceType: 'database',
              status: 'failed',
              error: error.message
            }
          }
        }
        break

      default:
        return res.status(400).json({
          success: false,
          message: `不支持的服务类型: ${serviceType}`
        })
    }

    res.json({
      success: result.success,
      message: result.message,
      data: result.details
    })
  } catch (error: any) {
    console.error('服务连接测试失败:', error)
    res.status(500).json({
      success: false,
      message: '服务连接测试失败',
      error: error.message
    })
  }
}