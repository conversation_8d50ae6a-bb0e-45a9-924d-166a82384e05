import { Request, Response } from 'express'
import { prisma } from '@/config/database.config'
import { z } from 'zod'
import { encryptObjectFields, decryptObjectFields } from '@/utils/crypto.util'

// 验证Schema
const createConfigurationSchema = z.object({
  archiveId: z.string().min(1),
  configType: z.enum(['SERVER', 'DATABASE', 'VPN', 'ACCOUNT', 'ENVIRONMENT', 'OTHER']),
  title: z.string().min(1).max(200),
  configData: z.record(z.any()),
  encryptedFields: z.array(z.string()).optional(),
  description: z.string().optional(),
  isActive: z.boolean().default(true)
})

const updateConfigurationSchema = createConfigurationSchema.partial().omit({ archiveId: true })

const querySchema = z.object({
  page: z.string().optional().transform(val => val ? parseInt(val) : 1),
  limit: z.string().optional().transform(val => val ? parseInt(val) : 10),
  search: z.string().optional(),
  configType: z.enum(['SERVER', 'DATABASE', 'VPN', 'ACCOUNT', 'ENVIRONMENT', 'OTHER']).optional(),
  archiveId: z.string().optional(),
  customerId: z.string().optional(),
  isActive: z.string().optional().transform(val => val ? val === 'true' : undefined)
})

// 获取配置列表
export const getConfigurations = async (req: Request, res: Response) => {
  try {
    const { page, limit, search, configType, archiveId, customerId, isActive } = querySchema.parse(req.query)
    
    const skip = (page - 1) * limit
    
    // 构建查询条件
    const where: any = {}
    
    if (search) {
      where.OR = [
        { title: { contains: search } },
        { description: { contains: search } }
      ]
    }
    
    if (configType) {
      where.configType = configType
    }
    
    if (archiveId) {
      where.archiveId = archiveId
    }
    
    if (customerId) {
      where.archive = {
        customerId: customerId
      }
    }
    
    if (isActive !== undefined) {
      where.isActive = isActive
    }

    const [configurations, total] = await Promise.all([
      prisma.projectConfiguration.findMany({
        where,
        skip,
        take: limit,
        include: {
          archive: {
            select: {
              id: true,
              name: true,
              customer: {
                select: {
                  id: true,
                  name: true,
                  company: true
                }
              }
            }
          }
        },
        orderBy: { createdAt: 'desc' }
      }),
      prisma.projectConfiguration.count({ where })
    ])

    // 解密配置数据中的敏感字段
    const decryptedConfigurations = configurations.map(config => {
      const encryptedFields = config.encryptedFields as string[] || []
      let configData = config.configData
      
      // 如果configData是字符串，尝试解析为JSON
      if (typeof configData === 'string') {
        try {
          configData = JSON.parse(configData)
        } catch (error) {
          console.error('Failed to parse configData as JSON:', error)
          configData = {}
        }
      }
      
      if (encryptedFields.length > 0 && typeof configData === 'object') {
        const decryptedConfigData = decryptObjectFields(configData, encryptedFields)
        return {
          ...config,
          configData: decryptedConfigData
        }
      }
      
      return {
        ...config,
        configData: configData
      }
    })

    return res.json({
      success: true,
      data: {
        configurations: decryptedConfigurations,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      }
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        message: '请求参数验证失败',
        errors: error.errors
      })
    }

    console.error('Get configurations error:', error)
    return res.status(500).json({
      success: false,
      message: '获取配置列表失败'
    })
  }
}

// 获取配置详情
export const getConfiguration = async (req: Request, res: Response) => {
  try {
    const { id } = req.params

    if (!id) {
      return res.status(400).json({
        success: false,
        message: 'ID参数缺失'
      })
    }

    const configuration = await prisma.projectConfiguration.findUnique({
      where: { id },
      include: {
        archive: {
          include: {
            customer: {
              select: {
                id: true,
                name: true,
                company: true,
                level: true
              }
            }
          }
        }
      }
    })

    if (!configuration) {
      return res.status(404).json({
        success: false,
        message: '配置不存在'
      })
    }

    // 解密配置数据中的敏感字段
    const encryptedFields = configuration.encryptedFields as string[] || []
    if (encryptedFields.length > 0) {
      const decryptedConfigData = decryptObjectFields(configuration.configData, encryptedFields)
      configuration.configData = decryptedConfigData
    }

    return res.json({
      success: true,
      data: configuration
    })
  } catch (error) {
    console.error('Get configuration error:', error)
    return res.status(500).json({
      success: false,
      message: '获取配置详情失败'
    })
  }
}

// 创建配置
export const createConfiguration = async (req: Request, res: Response) => {
  try {
    const data = createConfigurationSchema.parse(req.body)

    // 检查项目档案是否存在
    const archive = await prisma.projectArchive.findUnique({
      where: { id: data.archiveId }
    })

    if (!archive) {
      return res.status(400).json({
        success: false,
        message: '项目档案不存在'
      })
    }

    // 加密敏感字段
    let configData = data.configData
    const encryptedFields = data.encryptedFields || []
    
    if (encryptedFields.length > 0) {
      configData = encryptObjectFields(data.configData, encryptedFields)
    }

    const createData: any = {
      archiveId: data.archiveId,
      configType: data.configType,
      title: data.title,
      configData,
      description: data.description || null,
      isActive: data.isActive,
      lastUpdated: new Date()
    }
    
    if (encryptedFields.length > 0) {
      createData.encryptedFields = encryptedFields
    }

    const configuration = await prisma.projectConfiguration.create({
      data: createData,
      include: {
        archive: {
          select: {
            id: true,
            name: true,
            customer: {
              select: {
                id: true,
                name: true,
                company: true
              }
            }
          }
        }
      }
    })

    // 返回时解密敏感字段
    if (encryptedFields.length > 0) {
      const decryptedConfigData = decryptObjectFields(configuration.configData, encryptedFields)
      configuration.configData = decryptedConfigData
    }

    return res.status(201).json({
      success: true,
      message: '配置创建成功',
      data: configuration
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        message: '请求参数验证失败',
        errors: error.errors
      })
    }

    console.error('Create configuration error:', error)
    return res.status(500).json({
      success: false,
      message: '配置创建失败'
    })
  }
}

// 更新配置
export const updateConfiguration = async (req: Request, res: Response) => {
  try {
    const { id } = req.params
    const data = updateConfigurationSchema.parse(req.body)

    if (!id) {
      return res.status(400).json({
        success: false,
        message: 'ID参数缺失'
      })
    }

    // 检查配置是否存在
    const existingConfiguration = await prisma.projectConfiguration.findUnique({
      where: { id }
    })

    if (!existingConfiguration) {
      return res.status(404).json({
        success: false,
        message: '配置不存在'
      })
    }

    // 处理加密字段
    let configData = data.configData
    const encryptedFields = data.encryptedFields || []
    
    if (configData && encryptedFields.length > 0) {
      configData = encryptObjectFields(configData, encryptedFields)
    }

    const updateData: any = {
      ...data,
      lastUpdated: new Date()
    }

    if (configData) {
      updateData.configData = configData
    }

    if (encryptedFields.length > 0) {
      updateData.encryptedFields = encryptedFields
    }

    const configuration = await prisma.projectConfiguration.update({
      where: { id },
      data: updateData,
      include: {
        archive: {
          select: {
            id: true,
            name: true,
            customer: {
              select: {
                id: true,
                name: true,
                company: true
              }
            }
          }
        }
      }
    })

    // 返回时解密敏感字段
    if (encryptedFields.length > 0 && configuration.configData) {
      const decryptedConfigData = decryptObjectFields(configuration.configData, encryptedFields)
      configuration.configData = decryptedConfigData
    }

    return res.json({
      success: true,
      message: '配置更新成功',
      data: configuration
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        message: '请求参数验证失败',
        errors: error.errors
      })
    }

    console.error('Update configuration error:', error)
    return res.status(500).json({
      success: false,
      message: '配置更新失败'
    })
  }
}

// 删除配置
export const deleteConfiguration = async (req: Request, res: Response) => {
  try {
    const { id } = req.params

    if (!id) {
      return res.status(400).json({
        success: false,
        message: 'ID参数缺失'
      })
    }

    // 检查配置是否存在
    const configuration = await prisma.projectConfiguration.findUnique({
      where: { id }
    })

    if (!configuration) {
      return res.status(404).json({
        success: false,
        message: '配置不存在'
      })
    }

    await prisma.projectConfiguration.delete({
      where: { id }
    })

    return res.json({
      success: true,
      message: '配置删除成功'
    })
  } catch (error) {
    console.error('Delete configuration error:', error)
    return res.status(500).json({
      success: false,
      message: '配置删除失败'
    })
  }
}

// 获取项目档案的配置列表
export const getArchiveConfigurations = async (req: Request, res: Response) => {
  try {
    const { archiveId } = req.params
    const { configType, isActive } = req.query

    if (!archiveId) {
      return res.status(400).json({
        success: false,
        message: 'archiveId参数缺失'
      })
    }

    // 检查项目档案是否存在
    const archive = await prisma.projectArchive.findUnique({
      where: { id: archiveId }
    })

    if (!archive) {
      return res.status(404).json({
        success: false,
        message: '项目档案不存在'
      })
    }

    const where: any = { archiveId }
    
    if (configType && typeof configType === 'string') {
      where.configType = configType
    }
    
    if (isActive !== undefined) {
      where.isActive = isActive === 'true'
    }

    const configurations = await prisma.projectConfiguration.findMany({
      where,
      orderBy: { createdAt: 'desc' }
    })

    // 解密配置数据中的敏感字段
    const decryptedConfigurations = configurations.map(config => {
      const encryptedFields = config.encryptedFields as string[] || []
      if (encryptedFields.length > 0) {
        const decryptedConfigData = decryptObjectFields(config.configData, encryptedFields)
        return {
          ...config,
          configData: decryptedConfigData
        }
      }
      return config
    })

    return res.json({
      success: true,
      data: decryptedConfigurations
    })
  } catch (error) {
    console.error('Get archive configurations error:', error)
    return res.status(500).json({
      success: false,
      message: '获取项目配置失败'
    })
  }
}

// 获取配置统计信息
export const getConfigurationStats = async (_req: Request, res: Response) => {
  try {
    const [
      totalConfigurations,
      typeStats,
      recentConfigurations,
      activeConfigurations
    ] = await Promise.all([
      // 总配置数
      prisma.projectConfiguration.count(),
      
      // 按类型统计
      prisma.projectConfiguration.groupBy({
        by: ['configType'],
        _count: {
          configType: true
        }
      }),
      
      // 最近7天新增配置
      prisma.projectConfiguration.count({
        where: {
          createdAt: {
            gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
          }
        }
      }),
      
      // 活跃配置数
      prisma.projectConfiguration.count({
        where: {
          isActive: true
        }
      })
    ])

    return res.json({
      success: true,
      data: {
        totalConfigurations,
        recentConfigurations,
        activeConfigurations,
        typeDistribution: typeStats.map(stat => ({
          type: stat.configType,
          count: stat._count.configType
        }))
      }
    })
  } catch (error) {
    console.error('Get configuration stats error:', error)
    return res.status(500).json({
      success: false,
      message: '获取配置统计信息失败'
    })
  }
}

// 切换配置状态
export const toggleConfigurationStatus = async (req: Request, res: Response) => {
  try {
    const { id } = req.params

    if (!id) {
      return res.status(400).json({
        success: false,
        message: 'ID参数缺失'
      })
    }

    // 检查配置是否存在
    const configuration = await prisma.projectConfiguration.findUnique({
      where: { id }
    })

    if (!configuration) {
      return res.status(404).json({
        success: false,
        message: '配置不存在'
      })
    }

    const updatedConfiguration = await prisma.projectConfiguration.update({
      where: { id },
      data: {
        isActive: !configuration.isActive,
        lastUpdated: new Date()
      }
    })

    return res.json({
      success: true,
      message: `配置已${updatedConfiguration.isActive ? '启用' : '禁用'}`,
      data: updatedConfiguration
    })
  } catch (error) {
    console.error('Toggle configuration status error:', error)
    return res.status(500).json({
      success: false,
      message: '切换配置状态失败'
    })
  }
}