import { Request, Response } from 'express'
import { prisma } from '@/config/database.config'
import { z } from 'zod'
import { 
  calculateServiceSlaStatus, 
  getAllActiveSlaStatus, 
  getSlaPerformanceStats,
  processSlaAlerts,
  SlaStatus
} from '@/utils/sla-monitor.util'

// 验证Schema
const createSlaTemplateSchema = z.object({
  name: z.string().min(1).max(100),
  description: z.string().optional(),
  responseTime: z.number().min(1), // 响应时间(分钟)
  resolutionTime: z.number().min(1), // 解决时间(小时)
  availability: z.number().min(0).max(100).default(99.9) // 可用性百分比
})

const updateSlaTemplateSchema = createSlaTemplateSchema.partial()

const querySchema = z.object({
  page: z.string().optional().transform(val => val ? parseInt(val) : 1),
  limit: z.string().optional().transform(val => val ? parseInt(val) : 10),
  search: z.string().optional()
})

// 获取SLA模板列表
export const getSlaTemplates = async (req: Request, res: Response) => {
  try {
    const { page, limit, search } = querySchema.parse(req.query)
    
    const skip = (page - 1) * limit
    
    // 构建查询条件
    const where: any = {}
    
    if (search) {
      where.OR = [
        { name: { contains: search } },
        { description: { contains: search } }
      ]
    }

    const [slaTemplates, total] = await Promise.all([
      prisma.slaTemplate.findMany({
        where,
        skip,
        take: limit,
        include: {
          _count: {
            select: {
              services: true
            }
          }
        },
        orderBy: { createdAt: 'desc' }
      }),
      prisma.slaTemplate.count({ where })
    ])

    const totalPages = Math.ceil(total / limit)

    return res.json({
      success: true,
      data: {
        items: slaTemplates,
        pagination: {
          page,
          limit,
          total,
          totalPages
        }
      }
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        message: '请求参数验证失败',
        errors: error.errors
      })
    }

    console.error('Get SLA templates error:', error)
    return res.status(500).json({
      success: false,
      message: '获取SLA模板列表失败'
    })
  }
}

// 获取SLA模板详情
export const getSlaTemplate = async (req: Request, res: Response) => {
  try {
    const { id } = req.params

    if (!id) {
      return res.status(400).json({
        success: false,
        message: 'ID参数缺失'
      })
    }

    const slaTemplate = await prisma.slaTemplate.findUnique({
      where: { id },
      include: {
        services: {
          select: {
            id: true,
            ticketNumber: true,
            title: true,
            status: true,
            priority: true,
            createdAt: true,
            archive: {
              select: {
                name: true,
                customer: {
                  select: {
                    name: true,
                    company: true
                  }
                }
              }
            }
          },
          orderBy: { createdAt: 'desc' },
          take: 10 // 只显示最近10个工单
        },
        _count: {
          select: {
            services: true
          }
        }
      }
    })

    if (!slaTemplate) {
      return res.status(404).json({
        success: false,
        message: 'SLA模板不存在'
      })
    }

    return res.json({
      success: true,
      data: slaTemplate
    })
  } catch (error) {
    console.error('Get SLA template error:', error)
    return res.status(500).json({
      success: false,
      message: '获取SLA模板详情失败'
    })
  }
}

// 创建SLA模板
export const createSlaTemplate = async (req: Request, res: Response) => {
  try {
    const data = createSlaTemplateSchema.parse(req.body)

    // 检查名称是否已存在
    const existingSla = await prisma.slaTemplate.findFirst({
      where: { name: data.name }
    })

    if (existingSla) {
      return res.status(400).json({
        success: false,
        message: 'SLA模板名称已存在'
      })
    }

    const slaTemplate = await prisma.slaTemplate.create({
      data: {
        ...data,
        description: data.description || null
      },
      include: {
        _count: {
          select: {
            services: true
          }
        }
      }
    })

    return res.status(201).json({
      success: true,
      message: 'SLA模板创建成功',
      data: slaTemplate
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        message: '请求参数验证失败',
        errors: error.errors
      })
    }

    console.error('Create SLA template error:', error)
    return res.status(500).json({
      success: false,
      message: 'SLA模板创建失败'
    })
  }
}

// 更新SLA模板
export const updateSlaTemplate = async (req: Request, res: Response) => {
  try {
    const { id } = req.params
    const data = updateSlaTemplateSchema.parse(req.body)

    if (!id) {
      return res.status(400).json({
        success: false,
        message: 'ID参数缺失'
      })
    }

    // 检查SLA模板是否存在
    const existingSla = await prisma.slaTemplate.findUnique({
      where: { id }
    })

    if (!existingSla) {
      return res.status(404).json({
        success: false,
        message: 'SLA模板不存在'
      })
    }

    // 如果更新名称，检查是否与其他模板重复
    if (data.name && data.name !== existingSla.name) {
      const duplicateSla = await prisma.slaTemplate.findFirst({
        where: { 
          name: data.name,
          id: { not: id }
        }
      })

      if (duplicateSla) {
        return res.status(400).json({
          success: false,
          message: 'SLA模板名称已存在'
        })
      }
    }

    const updateData: any = {}
    
    if (data.name !== undefined) updateData.name = data.name
    if (data.responseTime !== undefined) updateData.responseTime = data.responseTime
    if (data.resolutionTime !== undefined) updateData.resolutionTime = data.resolutionTime
    if (data.availability !== undefined) updateData.availability = data.availability
    if (data.description !== undefined) {
      updateData.description = data.description || null
    }

    const slaTemplate = await prisma.slaTemplate.update({
      where: { id },
      data: updateData,
      include: {
        _count: {
          select: {
            services: true
          }
        }
      }
    })

    return res.json({
      success: true,
      message: 'SLA模板更新成功',
      data: slaTemplate
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        message: '请求参数验证失败',
        errors: error.errors
      })
    }

    console.error('Update SLA template error:', error)
    return res.status(500).json({
      success: false,
      message: 'SLA模板更新失败'
    })
  }
}

// 删除SLA模板
export const deleteSlaTemplate = async (req: Request, res: Response) => {
  try {
    const { id } = req.params

    if (!id) {
      return res.status(400).json({
        success: false,
        message: 'ID参数缺失'
      })
    }

    // 检查SLA模板是否存在
    const slaTemplate = await prisma.slaTemplate.findUnique({
      where: { id },
      include: {
        _count: {
          select: {
            services: true
          }
        }
      }
    })

    if (!slaTemplate) {
      return res.status(404).json({
        success: false,
        message: 'SLA模板不存在'
      })
    }

    // 检查是否有关联的服务工单
    if (slaTemplate._count.services > 0) {
      return res.status(400).json({
        success: false,
        message: '该SLA模板已被服务工单使用，无法删除'
      })
    }

    await prisma.slaTemplate.delete({
      where: { id }
    })

    return res.json({
      success: true,
      message: 'SLA模板删除成功'
    })
  } catch (error) {
    console.error('Delete SLA template error:', error)
    return res.status(500).json({
      success: false,
      message: 'SLA模板删除失败'
    })
  }
}

// 获取SLA统计信息
export const getSlaStats = async (_req: Request, res: Response) => {
  try {
    // 获取所有SLA模板的统计信息
    const slaTemplates = await prisma.slaTemplate.findMany({
      include: {
        _count: {
          select: {
            services: true
          }
        }
      }
    })

    // 获取服务工单的SLA达成情况
    const services = await prisma.service.findMany({
      where: {
        slaTemplateId: { not: null },
        status: { in: ['RESOLVED', 'CLOSED'] }
      },
      include: {
        slaTemplate: true
      }
    })

    // 计算SLA达成率
    const slaPerformance = slaTemplates.map(template => {
      const templateServices = services.filter(s => s.slaTemplateId === template.id)

      let responseTimeCompliance = 0
      let resolutionTimeCompliance = 0

      if (templateServices.length > 0) {
        // 计算响应时间达成率
        const responseTimeCompliant = templateServices.filter(service => {
          if (!service.actualResponseTime) return false
          return service.actualResponseTime <= template.responseTime
        }).length

        responseTimeCompliance = (responseTimeCompliant / templateServices.length) * 100

        // 计算解决时间达成率
        const resolutionTimeCompliant = templateServices.filter(service => {
          if (!service.actualResolutionTime) return false
          return service.actualResolutionTime <= template.resolutionTime
        }).length

        resolutionTimeCompliance = (resolutionTimeCompliant / templateServices.length) * 100
      }

      return {
        templateId: template.id,
        templateName: template.name,
        totalServices: template._count.services,
        completedServices: templateServices.length,
        responseTimeCompliance: Math.round(responseTimeCompliance * 100) / 100,
        resolutionTimeCompliance: Math.round(resolutionTimeCompliance * 100) / 100,
        targetAvailability: template.availability
      }
    })

    // 总体统计
    const totalTemplates = slaTemplates.length
    const totalServices = slaTemplates.reduce((sum, template) => sum + template._count.services, 0)
    const avgResponseCompliance = slaPerformance.length > 0
      ? slaPerformance.reduce((sum, perf) => sum + perf.responseTimeCompliance, 0) / slaPerformance.length
      : 0
    const avgResolutionCompliance = slaPerformance.length > 0
      ? slaPerformance.reduce((sum, perf) => sum + perf.resolutionTimeCompliance, 0) / slaPerformance.length
      : 0

    return res.json({
      success: true,
      data: {
        overview: {
          totalTemplates,
          totalServices,
          avgResponseCompliance: Math.round(avgResponseCompliance * 100) / 100,
          avgResolutionCompliance: Math.round(avgResolutionCompliance * 100) / 100
        },
        templatePerformance: slaPerformance
      }
    })
  } catch (error) {
    console.error('Get SLA stats error:', error)
    return res.status(500).json({
      success: false,
      message: '获取SLA统计信息失败'
    })
  }
}

// 获取SLA模板的详细性能报告
export const getSlaPerformanceReport = async (req: Request, res: Response) => {
  try {
    const { id } = req.params
    const { startDate, endDate } = req.query

    if (!id) {
      return res.status(400).json({
        success: false,
        message: 'ID参数缺失'
      })
    }

    // 检查SLA模板是否存在
    const slaTemplate = await prisma.slaTemplate.findUnique({
      where: { id }
    })

    if (!slaTemplate) {
      return res.status(404).json({
        success: false,
        message: 'SLA模板不存在'
      })
    }

    // 构建时间范围查询条件
    const dateFilter: any = {}
    if (startDate) {
      dateFilter.gte = new Date(startDate as string)
    }
    if (endDate) {
      dateFilter.lte = new Date(endDate as string)
    }

    const whereCondition: any = {
      slaTemplateId: id
    }

    if (Object.keys(dateFilter).length > 0) {
      whereCondition.createdAt = dateFilter
    }

    // 获取该SLA模板下的所有服务工单
    const services = await prisma.service.findMany({
      where: whereCondition,
      include: {
        archive: {
          include: {
            customer: {
              select: {
                name: true,
                company: true
              }
            }
          }
        }
      },
      orderBy: { createdAt: 'desc' }
    })

    // 计算性能指标
    const performanceMetrics = services.map(service => {
      let responseTimeMinutes: number | null = null
      let resolutionTimeHours: number | null = null
      let responseTimeCompliant = false
      let resolutionTimeCompliant = false

      // 计算响应时间
      if (service.actualResponseTime) {
        responseTimeMinutes = service.actualResponseTime
        responseTimeCompliant = responseTimeMinutes <= slaTemplate.responseTime
      }

      // 计算解决时间
      if (service.actualResolutionTime) {
        resolutionTimeHours = service.actualResolutionTime
        resolutionTimeCompliant = resolutionTimeHours <= slaTemplate.resolutionTime
      }

      return {
        serviceId: service.id,
        ticketNumber: service.ticketNumber,
        title: service.title,
        status: service.status,
        priority: service.priority,
        customerName: service.archive.customer.name,
        customerCompany: service.archive.customer.company,
        createdAt: service.createdAt,
        responseTime: responseTimeMinutes,
        resolutionTime: resolutionTimeHours,
        responseTimeCompliant,
        resolutionTimeCompliant,
        slaResponseTarget: slaTemplate.responseTime,
        slaResolutionTarget: slaTemplate.resolutionTime
      }
    })

    // 统计汇总
    const totalServices = services.length
    const responseTimeCompliantCount = performanceMetrics.filter(m => m.responseTimeCompliant).length
    const resolutionTimeCompliantCount = performanceMetrics.filter(m => m.resolutionTimeCompliant).length

    const responseTimeCompliance = totalServices > 0 ? (responseTimeCompliantCount / totalServices) * 100 : 0
    const resolutionTimeCompliance = totalServices > 0 ? (resolutionTimeCompliantCount / totalServices) * 100 : 0

    return res.json({
      success: true,
      data: {
        slaTemplate: {
          id: slaTemplate.id,
          name: slaTemplate.name,
          responseTime: slaTemplate.responseTime,
          resolutionTime: slaTemplate.resolutionTime,
          availability: slaTemplate.availability
        },
        summary: {
          totalServices,
          responseTimeCompliance: Math.round(responseTimeCompliance * 100) / 100,
          resolutionTimeCompliance: Math.round(resolutionTimeCompliance * 100) / 100,
          responseTimeCompliantCount,
          resolutionTimeCompliantCount
        },
        services: performanceMetrics
      }
    })
  } catch (error) {
    console.error('Get SLA performance report error:', error)
    return res.status(500).json({
      success: false,
      message: '获取SLA性能报告失败'
    })
  }
}

// 推荐SLA模板
export const getRecommendedSlaTemplates = async (req: Request, res: Response) => {
  try {
    const { customerLevel, serviceCategory, priority } = req.query
    
    // 获取所有SLA模板
    const allTemplates = await prisma.slaTemplate.findMany({
      orderBy: [
        { responseTime: 'asc' }, // 响应时间优先
        { resolutionTime: 'asc' }
      ]
    })

    let recommendedTemplates = [...allTemplates]

    // 根据客户等级过滤推荐
    if (customerLevel) {
      switch (customerLevel) {
        case 'ENTERPRISE':
        case 'PREMIUM':
          // 高端客户推荐响应时间最快的模板
          recommendedTemplates = allTemplates.filter(t => t.responseTime <= 30) // 30分钟内响应
          break
        case 'STANDARD':
          // 标准客户推荐中等响应时间的模板
          recommendedTemplates = allTemplates.filter(t => t.responseTime <= 60) // 1小时内响应
          break
        case 'BASIC':
          // 基础客户推荐响应时间较长的模板
          recommendedTemplates = allTemplates.filter(t => t.responseTime <= 120) // 2小时内响应
          break
      }
    }

    // 根据服务类别进一步过滤
    if (serviceCategory) {
      switch (serviceCategory) {
        case 'BUGFIX':
        case 'URGENT':
          // 紧急类别需要快速响应
          recommendedTemplates = recommendedTemplates.filter(t => 
            t.responseTime <= 15 && t.resolutionTime <= 4
          )
          break
        case 'MAINTENANCE':
          // 维护类别可以较长响应时间
          recommendedTemplates = recommendedTemplates.filter(t => 
            t.resolutionTime <= 24
          )
          break
        case 'SUPPORT':
        case 'CONSULTING':
          // 支持和咨询类别标准响应
          recommendedTemplates = recommendedTemplates.filter(t => 
            t.responseTime <= 60 && t.resolutionTime <= 8
          )
          break
      }
    }

    // 根据优先级进一步筛选
    if (priority) {
      switch (priority) {
        case 'URGENT':
          recommendedTemplates = recommendedTemplates.filter(t => 
            t.responseTime <= 15 && t.resolutionTime <= 2
          )
          break
        case 'HIGH':
          recommendedTemplates = recommendedTemplates.filter(t => 
            t.responseTime <= 30 && t.resolutionTime <= 4
          )
          break
        case 'MEDIUM':
          recommendedTemplates = recommendedTemplates.filter(t => 
            t.responseTime <= 60 && t.resolutionTime <= 8
          )
          break
        case 'LOW':
          // 低优先级可以使用所有模板
          break
      }
    }

    // 如果没有符合条件的推荐模板，返回前3个最快的模板
    if (recommendedTemplates.length === 0) {
      recommendedTemplates = allTemplates.slice(0, 3)
    } else {
      // 限制推荐数量，返回前3个
      recommendedTemplates = recommendedTemplates.slice(0, 3)
    }

    return res.json({
      success: true,
      data: recommendedTemplates
    })
  } catch (error) {
    console.error('Get recommended SLA templates error:', error)
    return res.status(500).json({
      success: false,
      message: '获取推荐SLA模板失败'
    })
  }
}

// 获取单个服务的SLA状态
export const getServiceSlaStatus = async (req: Request, res: Response) => {
  try {
    const { serviceId } = req.params

    if (!serviceId) {
      return res.status(400).json({
        success: false,
        message: '服务ID参数缺失'
      })
    }

    const slaStatus = await calculateServiceSlaStatus(serviceId)

    if (!slaStatus) {
      return res.status(404).json({
        success: false,
        message: '服务不存在或未关联SLA模板'
      })
    }

    return res.json({
      success: true,
      data: slaStatus
    })
  } catch (error) {
    console.error('Get service SLA status error:', error)
    return res.status(500).json({
      success: false,
      message: '获取服务SLA状态失败'
    })
  }
}

// 获取所有活跃服务的SLA状态监控
export const getActiveSlaMonitoring = async (_req: Request, res: Response) => {
  try {
    const activeSlaStatuses = await getAllActiveSlaStatus()

    // 按风险等级分类
    const statusByRisk = {
      CRITICAL: activeSlaStatuses.filter(s => s.riskLevel === 'CRITICAL'),
      HIGH: activeSlaStatuses.filter(s => s.riskLevel === 'HIGH'),
      MEDIUM: activeSlaStatuses.filter(s => s.riskLevel === 'MEDIUM'),
      LOW: activeSlaStatuses.filter(s => s.riskLevel === 'LOW')
    }

    // 按SLA状态分类
    const statusByType = {
      WITHIN_SLA: activeSlaStatuses.filter(s => s.status === SlaStatus.WITHIN_SLA),
      APPROACHING_BREACH: activeSlaStatuses.filter(s => s.status === SlaStatus.APPROACHING_BREACH),
      RESPONSE_BREACH: activeSlaStatuses.filter(s => s.status === SlaStatus.RESPONSE_BREACH),
      RESOLUTION_BREACH: activeSlaStatuses.filter(s => s.status === SlaStatus.RESOLUTION_BREACH),
      FULL_BREACH: activeSlaStatuses.filter(s => s.status === SlaStatus.FULL_BREACH)
    }

    return res.json({
      success: true,
      data: {
        totalServices: activeSlaStatuses.length,
        statusByRisk,
        statusByType,
        allStatuses: activeSlaStatuses
      }
    })
  } catch (error) {
    console.error('Get active SLA monitoring error:', error)
    return res.status(500).json({
      success: false,
      message: '获取SLA监控数据失败'
    })
  }
}

// 获取SLA性能仪表板数据
export const getSlaPerformanceDashboard = async (_req: Request, res: Response) => {
  try {
    const performanceStats = await getSlaPerformanceStats()

    // 获取最近24小时的SLA警报
    const recentAlerts = await prisma.notification.findMany({
      where: {
        type: 'EMAIL',  // 修改为实际存在的类型
        subject: {
          contains: 'SLA'  // 通过subject字段过滤SLA相关通知
        },
        createdAt: {
          gte: new Date(Date.now() - 24 * 60 * 60 * 1000)
        }
      },
      orderBy: { createdAt: 'desc' },
      take: 10
    })

    // 获取SLA合规趋势（最近7天）
    const sevenDaysAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
    const recentServices = await prisma.service.findMany({
      where: {
        slaTemplateId: { not: null },
        createdAt: { gte: sevenDaysAgo },
        status: { in: ['RESOLVED', 'CLOSED'] }
      },
      include: {
        slaTemplate: true
      }
    })

    // 计算每日合规率
    const dailyCompliance = []
    for (let i = 6; i >= 0; i--) {
      const date = new Date(Date.now() - i * 24 * 60 * 60 * 1000)
      const dayStart = new Date(date.getFullYear(), date.getMonth(), date.getDate())
      const dayEnd = new Date(dayStart.getTime() + 24 * 60 * 60 * 1000)

      const dayServices = recentServices.filter(s => 
        s.createdAt >= dayStart && s.createdAt < dayEnd
      )

      if (dayServices.length > 0) {
        const compliantCount = dayServices.filter(service => {
          const responseTime = service.actualResponseTime || 0
          const resolutionTime = service.actualResolutionTime || 0
          return responseTime <= (service.slaTemplate?.responseTime || 0) &&
                 resolutionTime <= (service.slaTemplate?.resolutionTime || 0)
        }).length

        dailyCompliance.push({
          date: dayStart.toISOString().split('T')[0],
          compliance: (compliantCount / dayServices.length) * 100,
          totalServices: dayServices.length,
          compliantServices: compliantCount
        })
      }
    }

    return res.json({
      success: true,
      data: {
        performance: performanceStats,
        recentAlerts: recentAlerts.map(alert => ({
          id: alert.id,
          subject: alert.subject,
          content: alert.content,
          createdAt: alert.createdAt,
          status: alert.status,
          recipient: alert.recipient
        })),
        complianceTrend: dailyCompliance
      }
    })
  } catch (error) {
    console.error('Get SLA performance dashboard error:', error)
    return res.status(500).json({
      success: false,
      message: '获取SLA性能仪表板数据失败'
    })
  }
}

// 手动触发SLA警报检查
export const triggerSlaAlertCheck = async (_req: Request, res: Response) => {
  try {
    await processSlaAlerts()

    return res.json({
      success: true,
      message: 'SLA警报检查已完成'
    })
  } catch (error) {
    console.error('Trigger SLA alert check error:', error)
    return res.status(500).json({
      success: false,
      message: 'SLA警报检查失败'
    })
  }
}

// 获取SLA违约风险预警列表
export const getSlaRiskAlerts = async (req: Request, res: Response) => {
  try {
    const { riskLevel, limit = 20 } = req.query

    const activeSlaStatuses = await getAllActiveSlaStatus()

    // 过滤风险等级
    let filteredStatuses = activeSlaStatuses
    if (riskLevel && typeof riskLevel === 'string') {
      filteredStatuses = activeSlaStatuses.filter(s => s.riskLevel === riskLevel.toUpperCase())
    }

    // 按风险等级和剩余时间排序
    filteredStatuses.sort((a, b) => {
      const riskOrder = { CRITICAL: 4, HIGH: 3, MEDIUM: 2, LOW: 1 }
      if (riskOrder[a.riskLevel] !== riskOrder[b.riskLevel]) {
        return riskOrder[b.riskLevel] - riskOrder[a.riskLevel]
      }
      // 剩余时间最少的排在前面
      return Math.min(a.responseTimeStatus.remaining, a.resolutionTimeStatus.remaining) - 
             Math.min(b.responseTimeStatus.remaining, b.resolutionTimeStatus.remaining)
    })

    const limitNum = parseInt(limit as string)
    const paginatedStatuses = filteredStatuses.slice(0, limitNum)

    return res.json({
      success: true,
      data: {
        total: filteredStatuses.length,
        items: paginatedStatuses,
        riskSummary: {
          critical: activeSlaStatuses.filter(s => s.riskLevel === 'CRITICAL').length,
          high: activeSlaStatuses.filter(s => s.riskLevel === 'HIGH').length,
          medium: activeSlaStatuses.filter(s => s.riskLevel === 'MEDIUM').length,
          low: activeSlaStatuses.filter(s => s.riskLevel === 'LOW').length
        }
      }
    })
  } catch (error) {
    console.error('Get SLA risk alerts error:', error)
    return res.status(500).json({
      success: false,
      message: '获取SLA风险预警列表失败'
    })
  }
}
