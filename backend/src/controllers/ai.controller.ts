import { Response } from 'express'
import { z } from 'zod'
import { PrismaClient } from '@prisma/client'
import { AIService } from '@/services/ai.service'
import { AuditService } from '@/services/audit.service'
import { AuthenticatedRequest } from '@/middleware/auth.middleware'

const prisma = new PrismaClient()

// 请求验证模式
const analyzeContentSchema = z.object({
  description: z.string().min(1, '描述内容不能为空').max(5000, '描述内容过长'),
  contextData: z.object({
    customerId: z.string().optional(),
    customerType: z.string().optional(),
    industry: z.string().optional(),
    historyPattern: z.string().optional(),
  }).optional()
})

const configSchema = z.object({
  provider: z.enum(['openai', 'anthropic', 'gemini', 'local']),
  model: z.string().min(1),
  apiKey: z.string().optional(),
  temperature: z.number().min(0).max(2).default(0.3),
  maxTokens: z.number().min(1).max(4000).default(1000),
  timeout: z.number().min(1000).max(60000).default(30000),
  mode: z.enum(['DISABLED', 'USER_TRIGGERED', 'AGGRESSIVE']).default('USER_TRIGGERED'),
  enabledFields: z.object({
    title: z.boolean().default(true),
    category: z.boolean().default(true),
    priority: z.boolean().default(true),
    slaTemplate: z.boolean().default(true)
  }).default({}),
  autoFillThreshold: z.number().min(0).max(1).default(0.8)
})

const feedbackSchema = z.object({
  requestId: z.string().min(1),
  rating: z.number().min(1).max(5),
  helpful: z.boolean().optional(),
  adopted: z.record(z.any()).optional(),
  comments: z.string().max(1000).optional()
})

const promptTemplateSchema = z.object({
  name: z.string().min(1).max(100),
  category: z.string().min(1).max(50),
  template: z.string().min(1),
  variables: z.array(z.string()).optional(),
  provider: z.string().optional(),
  version: z.string().default('1.0')
})

/**
 * 分析工单内容
 */
export const analyzeTicketContent = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { description, contextData } = analyzeContentSchema.parse(req.body)
    const userId = req.user!.id

    const result = await AIService.analyzeTicketContent({
        description,
        contextData: contextData || {},
        userId,
    })

    // 记录审计日志
    await AuditService.log({
      userId,
      action: 'AI_ANALYZE',
      resource: 'TICKET_CONTENT',
      details: {
        descriptionLength: description.length,
        hasContext: !!contextData,
        success: result.success
      },
      ipAddress: req.ip
    })

    return res.json(result)
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        message: '请求参数验证失败',
        errors: error.errors
      })
    }

    console.error('AI分析工单内容失败:', error)
    return res.status(500).json({
      success: false,
      message: 'AI分析失败，请稍后重试'
    })
  }
}

/**
 * 获取当前有效的AI配置
 */
export const getCurrentAIConfiguration = async (req: AuthenticatedRequest, res: Response) => {
  try {
    // 获取第一个启用的AI配置
    const activeConfig = await prisma.aIConfiguration.findFirst({
      where: { isActive: true },
      orderBy: { createdAt: 'desc' },
      select: {
        id: true,
        provider: true,
        model: true,
        temperature: true,
        maxTokens: true,
        timeout: true,
        mode: true,
        enabledFields: true,
        autoFillThreshold: true,
        isActive: true,
        createdAt: true,
        updatedAt: true
      }
    })

    if (!activeConfig) {
      return res.status(404).json({
        success: false,
        message: '未找到有效的AI配置'
      })
    }

    return res.json({
      success: true,
      data: activeConfig
    })
  } catch (error) {
    console.error('获取当前AI配置失败:', error)
    return res.status(500).json({
      success: false,
      message: '获取AI配置失败'
    })
  }
}

/**
 * 获取AI配置列表
 */
export const getAIConfigurations = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { page = 1, limit = 10, provider, isActive } = req.query

    // 构建查询条件
    const where: any = {}
    
    if (provider) {
      where.provider = provider
    }
    
    if (isActive !== undefined) {
      where.isActive = isActive === 'true'
    }

    const skip = (Number(page) - 1) * Number(limit)

    // 从数据库获取配置数据
    const [configurations, total] = await Promise.all([
      prisma.aIConfiguration.findMany({
        where,
        skip,
        take: Number(limit),
        orderBy: { createdAt: 'desc' },
        select: {
          id: true,
          provider: true,
          model: true,
          temperature: true,
          maxTokens: true,
          timeout: true,
          mode: true,
          enabledFields: true,
          autoFillThreshold: true,
          isActive: true,
          createdAt: true,
          updatedAt: true
        }
      }),
      prisma.aIConfiguration.count({ where })
    ])

    return res.json({
      success: true,
      data: {
        data: configurations,
        pagination: {
          page: Number(page),
          limit: Number(limit),
          total,
          totalPages: Math.ceil(total / Number(limit))
        }
      }
    })
  } catch (error) {
    console.error('获取AI配置列表失败:', error)
    return res.status(500).json({
      success: false,
      message: '获取AI配置列表失败'
    })
  }
}

/**
 * 获取单个AI配置
 */
export const getAIConfiguration = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { id } = req.params

    if (!id) {
      return res.status(400).json({
        success: false,
        message: '配置ID不能为空'
      })
    }

    // 从数据库获取配置
    const configuration = await prisma.aIConfiguration.findUnique({
      where: { id },
      select: {
        id: true,
        provider: true,
        model: true,
        temperature: true,
        maxTokens: true,
        timeout: true,
        mode: true,
        enabledFields: true,
        autoFillThreshold: true,
        isActive: true,
        createdAt: true,
        updatedAt: true
      }
    })

    if (!configuration) {
      return res.status(404).json({
        success: false,
        message: 'AI配置不存在'
      })
    }

    return res.json({
      success: true,
      data: configuration
    })
  } catch (error) {
    console.error('获取AI配置失败:', error)
    return res.status(500).json({
      success: false,
      message: '获取AI配置失败'
    })
  }
}

/**
 * 创建AI配置
 */
export const createAIConfiguration = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const configData = req.body

    // 创建新配置
    const newConfiguration = await prisma.aIConfiguration.create({
      data: {
        provider: configData.provider,
        model: configData.model,
        apiKey: configData.apiKey,
        temperature: configData.temperature || 0.3,
        maxTokens: configData.maxTokens || 1000,
        timeout: configData.timeout || 30000,
        mode: configData.mode || 'USER_TRIGGERED',
        enabledFields: configData.enabledFields || {
          title: true,
          category: true,
          priority: true,
          slaTemplate: true
        },
        autoFillThreshold: configData.autoFillThreshold || 0.8,
        isActive: configData.isActive !== undefined ? configData.isActive : true
      },
      select: {
        id: true,
        provider: true,
        model: true,
        temperature: true,
        maxTokens: true,
        timeout: true,
        mode: true,
        enabledFields: true,
        autoFillThreshold: true,
        isActive: true,
        createdAt: true,
        updatedAt: true
      }
    })

    return res.json({
      success: true,
      data: newConfiguration,
      message: 'AI配置创建成功'
    })
  } catch (error) {
    console.error('创建AI配置失败:', error)
    return res.status(500).json({
      success: false,
      message: '创建AI配置失败'
    })
  }
}

/**
 * 更新AI配置
 */
export const updateAIConfiguration = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { id } = req.params
    const configData = req.body

    if (!id) {
      return res.status(400).json({
        success: false,
        message: '配置ID不能为空'
      })
    }

    // 检查配置是否存在
    const existingConfig = await prisma.aIConfiguration.findUnique({
      where: { id }
    })

    if (!existingConfig) {
      return res.status(404).json({
        success: false,
        message: 'AI配置不存在'
      })
    }

    // 更新配置
    const updatedConfiguration = await prisma.aIConfiguration.update({
      where: { id },
      data: {
        provider: configData.provider,
        model: configData.model,
        apiKey: configData.apiKey,
        temperature: configData.temperature,
        maxTokens: configData.maxTokens,
        timeout: configData.timeout,
        mode: configData.mode,
        enabledFields: configData.enabledFields,
        autoFillThreshold: configData.autoFillThreshold,
        isActive: configData.isActive,
        updatedAt: new Date()
      },
      select: {
        id: true,
        provider: true,
        model: true,
        temperature: true,
        maxTokens: true,
        timeout: true,
        mode: true,
        enabledFields: true,
        autoFillThreshold: true,
        isActive: true,
        createdAt: true,
        updatedAt: true
      }
    })

    return res.json({
      success: true,
      data: updatedConfiguration,
      message: 'AI配置更新成功'
    })
  } catch (error) {
    console.error('更新AI配置失败:', error)
    return res.status(500).json({
      success: false,
      message: '更新AI配置失败'
    })
  }
}

/**
 * 删除AI配置
 */
export const deleteAIConfiguration = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { id } = req.params

    if (!id) {
      return res.status(400).json({
        success: false,
        message: '配置ID不能为空'
      })
    }

    // 检查配置是否存在
    const existingConfig = await prisma.aIConfiguration.findUnique({
      where: { id }
    })

    if (!existingConfig) {
      return res.status(404).json({
        success: false,
        message: 'AI配置不存在'
      })
    }

    // 删除配置
    await prisma.aIConfiguration.delete({
      where: { id }
    })

    return res.json({
      success: true,
      message: 'AI配置删除成功'
    })
  } catch (error) {
    console.error('删除AI配置失败:', error)
    return res.status(500).json({
      success: false,
      message: '删除AI配置失败'
    })
  }
}

/**
 * 获取AI配置
 */
export const getAIConfig = async (_req: AuthenticatedRequest, res: Response) => {
  try {
    const config = await AIService.getAIConfiguration()
    return res.json({
      success: true,
      data: config
    })
  } catch (error) {
    console.error('获取AI配置失败:', error)
    return res.status(500).json({
      success: false,
      message: '获取AI配置失败'
    })
  }
}

/**
 * 更新AI配置
 */
export const updateAIConfig = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const configData = configSchema.parse(req.body)
    const userId = req.user!.id

    const config = await AIService.updateAIConfiguration(configData)

    // 记录审计日志
    await AuditService.log({
      userId,
      action: 'UPDATE',
      resource: 'AI_CONFIG',
      details: { provider: configData.provider, mode: configData.mode },
      ipAddress: req.ip
    })

    return res.json({
      success: true,
      data: config,
      message: 'AI配置更新成功'
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        message: '配置参数验证失败',
        errors: error.errors
      })
    }

    console.error('更新AI配置失败:', error)
    return res.status(500).json({
      success: false,
      message: '更新AI配置失败'
    })
  }
}

/**
 * 提交AI反馈
 */
export const submitFeedback = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const feedbackData = feedbackSchema.parse(req.body)
    const userId = req.user!.id

    await AIService.submitFeedback({
      ...feedbackData,
      userId
    })

    // 记录审计日志
    await AuditService.log({
      userId,
      action: 'SUBMIT',
      resource: 'AI_FEEDBACK',
      details: {
        requestId: feedbackData.requestId,
        rating: feedbackData.rating,
        helpful: feedbackData.helpful
      },
      ipAddress: req.ip
    })

    return res.json({
      success: true,
      message: 'AI反馈提交成功'
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        message: '反馈参数验证失败',
        errors: error.errors
      })
    }

    console.error('提交AI反馈失败:', error)
    return res.status(500).json({
      success: false,
      message: '提交反馈失败'
    })
  }
}

/**
 * 获取AI统计
 */
export const getAIStatistics = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { startDate, endDate, provider } = req.query

    // 构建查询条件
    const where: any = {}
    if (provider) {
      where.provider = provider
    }
    if (startDate || endDate) {
      where.createdAt = {}
      if (startDate) {
        where.createdAt.gte = new Date(startDate as string)
      }
      if (endDate) {
        where.createdAt.lte = new Date(endDate as string)
      }
    }

    // 获取基础统计数据
    const [totalRequests, successfulRequests, avgProcessingTime] = await Promise.all([
      prisma.aIAnalysisRequest.count({ where }),
      prisma.aIAnalysisRequest.count({ where: { ...where, success: true } }),
      prisma.aIAnalysisRequest.aggregate({
        where: { ...where, success: true, processingTime: { not: null } },
        _avg: { processingTime: true }
      })
    ])

    const failedRequests = totalRequests - successfulRequests
    const successRate = totalRequests > 0 ? (successfulRequests / totalRequests) * 100 : 0

    // 获取提供商使用统计
    const providerStats = await prisma.aIAnalysisRequest.groupBy({
      by: ['provider'],
      where,
      _count: { provider: true }
    })

    const providerUsage: Record<string, { count: number; percentage: number }> = {}
    providerStats.forEach(stat => {
      const count = stat._count.provider
      const percentage = totalRequests > 0 ? (count / totalRequests) * 100 : 0
      providerUsage[stat.provider] = { count, percentage: Math.round(percentage * 10) / 10 }
    })

    // 获取最近7天的统计数据
    const sevenDaysAgo = new Date()
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7)

    // 简化的每日统计查询
    const dailyRequests = await prisma.aIAnalysisRequest.findMany({
      where: {
        ...where,
        createdAt: { gte: sevenDaysAgo }
      },
      select: {
        createdAt: true,
        success: true
      }
    })

    // 处理每日统计数据
    const dailyStatsMap = new Map()
    dailyRequests.forEach(request => {
      const date = request.createdAt.toISOString().split('T')[0]
      if (!dailyStatsMap.has(date)) {
        dailyStatsMap.set(date, { requests: 0, successful: 0 })
      }
      const existing = dailyStatsMap.get(date)
      existing.requests += 1
      if (request.success) {
        existing.successful += 1
      }
    })

    const dailyStats = Array.from(dailyStatsMap.entries()).map(([date, stats]) => ({
      date,
      requests: stats.requests,
      successRate: stats.requests > 0 ? Math.round((stats.successful / stats.requests) * 1000) / 10 : 0
    })).sort((a, b) => a.date.localeCompare(b.date))

    const statistics = {
      totalRequests,
      successfulRequests,
      failedRequests,
      averageResponseTime: Math.round(avgProcessingTime._avg.processingTime || 0),
      successRate: Math.round(successRate * 10) / 10,
      providerUsage,
      templateUsage: {}, // 暂时为空，需要实现模板使用统计
      dailyStats
    }

    return res.json({
      success: true,
      data: statistics
    })
  } catch (error) {
    console.error('获取AI统计失败:', error)
    return res.status(500).json({
      success: false,
      message: '获取AI统计失败'
    })
  }
}

/**
 * 获取提示词模板列表
 */
export const getPromptTemplates = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { page = 1, limit = 10, category, provider, isActive } = req.query

    // 构建查询条件
    const where: any = {}
    if (category) {
      where.category = category
    }
    if (provider) {
      where.provider = provider
    }
    if (isActive !== undefined) {
      where.isActive = isActive === 'true'
    }

    // 查询数据库
    const [templates, total] = await Promise.all([
      prisma.aIPromptTemplate.findMany({
        where,
        skip: (Number(page) - 1) * Number(limit),
        take: Number(limit),
        orderBy: { createdAt: 'desc' },
        select: {
          id: true,
          name: true,
          category: true,
          template: true,
          variables: true,
          provider: true,
          version: true,
          isActive: true,
          createdAt: true,
          updatedAt: true,
          creator: {
            select: {
              id: true,
              username: true
            }
          }
        }
      }),
      prisma.aIPromptTemplate.count({ where })
    ])

    return res.json({
      success: true,
      data: {
        data: templates,
        pagination: {
          page: Number(page),
          limit: Number(limit),
          total,
          totalPages: Math.ceil(total / Number(limit))
        }
      }
    })
  } catch (error) {
    console.error('获取提示词模板失败:', error)
    return res.status(500).json({
      success: false,
      message: '获取提示词模板失败'
    })
  }
}

/**
 * 创建提示词模板
 */
export const createPromptTemplate = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const templateData = promptTemplateSchema.parse(req.body)
    const userId = req.user!.id

    const template = await AIService.createPromptTemplate({
      ...templateData,
      createdBy: userId
    })

    // 记录审计日志
    await AuditService.log({
      userId,
      action: 'CREATE',
      resource: 'AI_PROMPT_TEMPLATE',
      details: {
        templateName: templateData.name,
        category: templateData.category
      },
      ipAddress: req.ip
    })

    return res.json({
      success: true,
      data: template,
      message: '提示词模板创建成功'
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        message: '模板参数验证失败',
        errors: error.errors
      })
    }

    console.error('创建提示词模板失败:', error)
    return res.status(500).json({
      success: false,
      message: '创建提示词模板失败'
    })
  }
}

/**
 * 更新提示词模板
 */
export const updatePromptTemplate = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { id } = req.params
    const templateData = promptTemplateSchema.parse(req.body)
    const userId = req.user!.id

    const template = await AIService.updatePromptTemplate(id as string, {
      ...templateData,
      updatedBy: userId
    })

    // 记录审计日志
    await AuditService.log({
      userId,
      action: 'UPDATE',
      resource: 'AI_PROMPT_TEMPLATE',
      details: {
        templateId: id,
        templateName: templateData.name
      },
      ipAddress: req.ip
    })

    return res.json({
      success: true,
      data: template,
      message: '提示词模板更新成功'
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        message: '模板参数验证失败',
        errors: error.errors
      })
    }

    console.error('更新提示词模板失败:', error)
    return res.status(500).json({
      success: false,
      message: '更新提示词模板失败'
    })
  }
}

/**
 * 删除提示词模板
 */
export const deletePromptTemplate = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { id } = req.params
    const userId = req.user!.id

    await AIService.deletePromptTemplate(id as string)

    // 记录审计日志
    await AuditService.log({
      userId,
      action: 'DELETE',
      resource: 'AI_PROMPT_TEMPLATE',
      details: { templateId: id },
      ipAddress: req.ip
    })

    return res.json({
      success: true,
      message: '提示词模板删除成功'
    })
  } catch (error) {
    console.error('删除提示词模板失败:', error)
    return res.status(500).json({
      success: false,
      message: '删除提示词模板失败'
    })
  }
}

/**
 * 测试AI连接
 */
export const testAIConnection = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const configData = configSchema.parse(req.body)
    const userId = req.user!.id

    const result = await AIService.testConnection(configData)

    // 记录审计日志
    await AuditService.log({
      userId,
      action: 'TEST',
      resource: 'AI_CONNECTION',
      details: {
        provider: configData.provider,
        model: configData.model,
        success: result.success
      },
      ipAddress: req.ip
    })

    return res.json(result)
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        message: '配置参数验证失败',
        errors: error.errors
      })
    }

    console.error('测试AI连接失败:', error)
    return res.status(500).json({
      success: false,
      message: '测试AI连接失败'
    })
  }
}

/**
 * 获取AI分析历史
 */
export const getAnalysisHistory = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { page = 1, limit = 10, provider, status, startDate, endDate } = req.query

    // 构建查询条件
    const where: any = {}
    if (provider) {
      where.provider = provider
    }
    if (status) {
      where.success = status === 'COMPLETED'
    }
    if (startDate || endDate) {
      where.createdAt = {}
      if (startDate) {
        where.createdAt.gte = new Date(startDate as string)
      }
      if (endDate) {
        where.createdAt.lte = new Date(endDate as string)
      }
    }

    // 查询数据库
    const [analysisRequests, total] = await Promise.all([
      prisma.aIAnalysisRequest.findMany({
        where,
        skip: (Number(page) - 1) * Number(limit),
        take: Number(limit),
        orderBy: { createdAt: 'desc' },
        select: {
          id: true,
          requestId: true,
          description: true,
          contextData: true,
          provider: true,
          model: true,
          prompt: true,
          response: true,
          suggestions: true,
          processingTime: true,
          success: true,
          error: true,
          createdAt: true,
          user: {
            select: {
              id: true,
              username: true
            }
          }
        }
      }),
      prisma.aIAnalysisRequest.count({ where })
    ])

    // 转换数据格式以匹配前端期望
    const formattedData = analysisRequests.map(request => ({
      id: request.requestId,
      provider: request.provider,
      model: request.model,
      inputData: JSON.stringify({
        description: request.description,
        contextData: request.contextData
      }),
      outputData: request.response ? JSON.stringify(request.response) : null,
      status: request.success ? 'COMPLETED' : 'FAILED',
      processingTime: request.processingTime,
      errorMessage: request.error,
      createdAt: request.createdAt.toISOString(),
      updatedAt: request.createdAt.toISOString(),
      user: request.user
    }))

    return res.json({
      success: true,
      data: {
        data: formattedData,
        pagination: {
          page: Number(page),
          limit: Number(limit),
          total,
          totalPages: Math.ceil(total / Number(limit))
        }
      }
    })
  } catch (error) {
    console.error('获取AI分析历史失败:', error)
    return res.status(500).json({
      success: false,
      message: '获取AI分析历史失败'
    })
  }
}

/**
 * 获取单个AI分析请求
 */
export const getAnalysisRequest = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { id } = req.params

    // 模拟分析请求数据
    const mockRequest = {
      id,
      provider: 'OPENAI',
      model: 'gpt-4',
      inputData: JSON.stringify({
        description: '用户反馈系统登录缓慢，需要排查数据库连接问题',
        contextData: { userAgent: 'Mozilla/5.0', timestamp: '2025-08-10T14:00:00Z' }
      }),
      outputData: JSON.stringify({
        category: '技术支持',
        priority: 'HIGH',
        suggestedActions: ['检查数据库连接', '优化查询性能'],
        confidence: 0.85
      }),
      status: 'COMPLETED',
      processingTime: 1250,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }

    return res.json({
      success: true,
      data: mockRequest
    })
  } catch (error) {
    console.error('获取AI分析请求失败:', error)
    return res.status(500).json({
      success: false,
      message: '获取AI分析请求失败'
    })
  }
}
