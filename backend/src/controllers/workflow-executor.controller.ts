/**
 * 工作流执行器API控制器
 * 提供工作流执行、监控和管理的REST API接口
 */

import { Request, Response } from 'express';
import { z } from 'zod';
import { workflowCoordinator } from '@/services/workflow-coordinator.service';
import { metricsCollector } from '@/services/metrics-collector.service';
import { logAggregator } from '@/services/log-aggregator.service';
import { executionStateTracker } from '@/services/execution-state-tracker.service';
import { logger } from '@/utils/logger.util';
import { successResponse, errorResponse, ValidationError } from '@/utils/response.util';

// ========== 验证Schemas ==========

const ExecuteWorkflowSchema = z.object({
  workflowId: z.string().uuid('无效的工作流ID格式'),
  context: z.record(z.any()).optional().default({}),
  priority: z.enum(['LOW', 'MEDIUM', 'HIGH', 'URGENT']).optional().default('MEDIUM'),
  tags: z.array(z.string()).optional().default([]),
  metadata: z.record(z.any()).optional().default({})
});

const WorkflowExecutionQuerySchema = z.object({
  status: z.enum(['STARTING', 'RUNNING', 'PAUSED', 'STOPPING', 'STOPPED']).optional(),
  workflowId: z.string().uuid().optional(),
  page: z.coerce.number().min(1).optional().default(1),
  limit: z.coerce.number().min(1).max(100).optional().default(20),
  sortBy: z.enum(['startTime', 'endTime', 'status', 'workflowId']).optional().default('startTime'),
  sortOrder: z.enum(['asc', 'desc']).optional().default('desc')
});

const ExecutionControlSchema = z.object({
  sessionId: z.string().min(1, '会话ID不能为空'),
  reason: z.string().optional()
});

const MetricsQuerySchema = z.object({
  executorType: z.string().optional(),
  timeWindow: z.enum(['1h', '6h', '24h', '7d']).optional().default('24h'),
  includeDetails: z.boolean().optional().default(false)
});

const LogQuerySchema = z.object({
  sessionId: z.string().optional(),
  workflowId: z.string().uuid().optional(),
  level: z.enum(['INFO', 'WARN', 'ERROR', 'DEBUG']).optional(),
  page: z.coerce.number().min(1).optional().default(1),
  limit: z.coerce.number().min(1).max(500).optional().default(100),
  startTime: z.string().datetime().optional(),
  endTime: z.string().datetime().optional(),
  search: z.string().optional()
});

// ========== 控制器类 ==========

export class WorkflowExecutorController {

  /**
   * 执行工作流
   * POST /api/v1/workflow/execute
   */
  static async executeWorkflow(req: Request, res: Response): Promise<void> {
    try {
      logger.info('执行工作流请求', { body: req.body, user: req.user?.id });

      // 验证请求数据
      const validatedData = ExecuteWorkflowSchema.parse(req.body);
      const { workflowId, context, priority, tags, metadata } = validatedData;

      // 增强执行上下文
      const enhancedContext = {
        ...context,
        _meta: {
          ...metadata,
          userId: req.user?.id,
          userRole: req.user?.role,
          priority,
          tags,
          requestId: `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          requestTime: new Date().toISOString()
        }
      };

      // 启动工作流执行
      const sessionId = await workflowCoordinator.startWorkflowExecution(
        workflowId,
        enhancedContext
      );

      logger.info('工作流执行启动成功', { 
        sessionId, 
        workflowId,
        userId: req.user?.id 
      });

      // 返回执行会话信息
      const session = workflowCoordinator.getExecutionSession(sessionId);
      const responseData = {
        sessionId,
        workflowId,
        status: session?.status,
        startTime: session?.startTime,
        totalSteps: session?.totalSteps,
        currentStep: session?.currentStep,
        message: '工作流执行已启动'
      };

      successResponse(res, responseData, '工作流执行启动成功');

    } catch (error: any) {
      logger.error('执行工作流失败', { error: error.message, stack: error.stack });
      
      if (error instanceof z.ZodError) {
        return errorResponse(res, 400, '请求参数验证失败', error.errors);
      }

      errorResponse(res, 500, '工作流执行启动失败', error.message);
    }
  }

  /**
   * 获取工作流执行状态
   * GET /api/v1/workflow/execution/:sessionId
   */
  static async getExecutionStatus(req: Request, res: Response): Promise<void> {
    try {
      const { sessionId } = req.params;

      if (!sessionId) {
        return errorResponse(res, 400, '会话ID不能为空');
      }

      const session = workflowCoordinator.getExecutionSession(sessionId);
      
      if (!session) {
        return errorResponse(res, 404, '执行会话不存在');
      }

      // 获取详细的执行信息
      const responseData = {
        sessionId: session.sessionId,
        executionId: session.executionId,
        workflowId: session.workflowId,
        status: session.status,
        startTime: session.startTime,
        endTime: session.endTime,
        currentStep: session.currentStep,
        totalSteps: session.totalSteps,
        errors: session.errors,
        progress: session.totalSteps > 0 ? Math.round((session.currentStep / session.totalSteps) * 100) : 0,
        duration: session.endTime 
          ? session.endTime.getTime() - session.startTime.getTime()
          : Date.now() - session.startTime.getTime()
      };

      logger.info('获取执行状态成功', { sessionId });
      successResponse(res, responseData, '获取执行状态成功');

    } catch (error: any) {
      logger.error('获取执行状态失败', { error: error.message, sessionId: req.params.sessionId });
      errorResponse(res, 500, '获取执行状态失败', error.message);
    }
  }

  /**
   * 列出工作流执行记录
   * GET /api/v1/workflow/executions
   */
  static async listExecutions(req: Request, res: Response): Promise<void> {
    try {
      // 验证查询参数
      const queryParams = WorkflowExecutionQuerySchema.parse(req.query);
      const { status, workflowId, page, limit, sortBy, sortOrder } = queryParams;

      // 获取所有执行会话
      const allSessions = workflowCoordinator.listExecutionSessions();
      
      // 应用过滤条件
      let filteredSessions = allSessions;
      
      if (status) {
        filteredSessions = filteredSessions.filter(session => session.status === status);
      }
      
      if (workflowId) {
        filteredSessions = filteredSessions.filter(session => session.workflowId === workflowId);
      }

      // 排序
      filteredSessions.sort((a, b) => {
        const aValue = a[sortBy as keyof typeof a];
        const bValue = b[sortBy as keyof typeof b];
        
        if (aValue instanceof Date && bValue instanceof Date) {
          return sortOrder === 'desc' 
            ? bValue.getTime() - aValue.getTime()
            : aValue.getTime() - bValue.getTime();
        }
        
        if (typeof aValue === 'string' && typeof bValue === 'string') {
          return sortOrder === 'desc' 
            ? bValue.localeCompare(aValue)
            : aValue.localeCompare(bValue);
        }
        
        return 0;
      });

      // 分页
      const total = filteredSessions.length;
      const skip = (page - 1) * limit;
      const paginatedSessions = filteredSessions.slice(skip, skip + limit);

      // 格式化响应数据
      const formattedSessions = paginatedSessions.map(session => ({
        sessionId: session.sessionId,
        executionId: session.executionId,
        workflowId: session.workflowId,
        status: session.status,
        startTime: session.startTime,
        endTime: session.endTime,
        currentStep: session.currentStep,
        totalSteps: session.totalSteps,
        errorCount: session.errors.length,
        progress: session.totalSteps > 0 ? Math.round((session.currentStep / session.totalSteps) * 100) : 0,
        duration: session.endTime 
          ? session.endTime.getTime() - session.startTime.getTime()
          : Date.now() - session.startTime.getTime()
      }));

      const responseData = {
        executions: formattedSessions,
        pagination: {
          total,
          page,
          limit,
          totalPages: Math.ceil(total / limit)
        }
      };

      logger.info('获取执行列表成功', { total, page, limit });
      successResponse(res, responseData, '获取执行列表成功');

    } catch (error: any) {
      logger.error('获取执行列表失败', { error: error.message });
      
      if (error instanceof z.ZodError) {
        return errorResponse(res, 400, '查询参数验证失败', error.errors);
      }

      errorResponse(res, 500, '获取执行列表失败', error.message);
    }
  }

  /**
   * 暂停工作流执行
   * POST /api/v1/workflow/execution/pause
   */
  static async pauseExecution(req: Request, res: Response): Promise<void> {
    try {
      const validatedData = ExecutionControlSchema.parse(req.body);
      const { sessionId, reason = '用户手动暂停' } = validatedData;

      await workflowCoordinator.pauseWorkflowExecution(sessionId);

      logger.info('暂停工作流执行成功', { sessionId, reason, userId: req.user?.id });
      successResponse(res, { sessionId, status: 'PAUSED' }, '工作流执行已暂停');

    } catch (error: any) {
      logger.error('暂停工作流执行失败', { error: error.message });
      
      if (error instanceof z.ZodError) {
        return errorResponse(res, 400, '请求参数验证失败', error.errors);
      }

      errorResponse(res, 500, '暂停工作流执行失败', error.message);
    }
  }

  /**
   * 恢复工作流执行
   * POST /api/v1/workflow/execution/resume
   */
  static async resumeExecution(req: Request, res: Response): Promise<void> {
    try {
      const validatedData = ExecutionControlSchema.parse(req.body);
      const { sessionId, reason = '用户手动恢复' } = validatedData;

      await workflowCoordinator.resumeWorkflowExecution(sessionId);

      logger.info('恢复工作流执行成功', { sessionId, reason, userId: req.user?.id });
      successResponse(res, { sessionId, status: 'RUNNING' }, '工作流执行已恢复');

    } catch (error: any) {
      logger.error('恢复工作流执行失败', { error: error.message });
      
      if (error instanceof z.ZodError) {
        return errorResponse(res, 400, '请求参数验证失败', error.errors);
      }

      errorResponse(res, 500, '恢复工作流执行失败', error.message);
    }
  }

  /**
   * 停止工作流执行
   * POST /api/v1/workflow/execution/stop
   */
  static async stopExecution(req: Request, res: Response): Promise<void> {
    try {
      const validatedData = ExecutionControlSchema.parse(req.body);
      const { sessionId, reason = '用户手动停止' } = validatedData;

      await workflowCoordinator.stopWorkflowExecution(sessionId, reason);

      logger.info('停止工作流执行成功', { sessionId, reason, userId: req.user?.id });
      successResponse(res, { sessionId, status: 'STOPPED' }, '工作流执行已停止');

    } catch (error: any) {
      logger.error('停止工作流执行失败', { error: error.message });
      
      if (error instanceof z.ZodError) {
        return errorResponse(res, 400, '请求参数验证失败', error.errors);
      }

      errorResponse(res, 500, '停止工作流执行失败', error.message);
    }
  }

  /**
   * 获取协调器状态
   * GET /api/v1/workflow/coordinator/status
   */
  static async getCoordinatorStatus(req: Request, res: Response): Promise<void> {
    try {
      const coordinatorStatus = workflowCoordinator.getCoordinatorStatus();
      const executionStats = workflowCoordinator.getExecutionStatistics();

      const responseData = {
        coordinator: coordinatorStatus,
        statistics: executionStats,
        timestamp: new Date().toISOString()
      };

      logger.info('获取协调器状态成功');
      successResponse(res, responseData, '获取协调器状态成功');

    } catch (error: any) {
      logger.error('获取协调器状态失败', { error: error.message });
      errorResponse(res, 500, '获取协调器状态失败', error.message);
    }
  }

  /**
   * 获取执行指标
   * GET /api/v1/workflow/metrics
   */
  static async getExecutionMetrics(req: Request, res: Response): Promise<void> {
    try {
      // 验证查询参数
      const queryParams = MetricsQuerySchema.parse(req.query);
      const { executorType, timeWindow, includeDetails } = queryParams;

      // 获取指标数据
      const metrics = metricsCollector.getMetrics(executorType as any);
      const healthScore = metricsCollector.getHealthScore();
      const realtimeMetrics = metricsCollector.getRealTimeMetrics();

      let responseData: any = {
        metrics,
        healthScore,
        realtime: realtimeMetrics,
        timestamp: new Date().toISOString()
      };

      // 包含详细信息
      if (includeDetails) {
        const performanceTrend = metricsCollector.getPerformanceTrend(timeWindow);
        const executorComparison = metricsCollector.getExecutorPerformanceComparison();
        
        responseData = {
          ...responseData,
          performanceTrend,
          executorComparison
        };
      }

      logger.info('获取执行指标成功', { executorType, timeWindow, includeDetails });
      successResponse(res, responseData, '获取执行指标成功');

    } catch (error: any) {
      logger.error('获取执行指标失败', { error: error.message });
      
      if (error instanceof z.ZodError) {
        return errorResponse(res, 400, '查询参数验证失败', error.errors);
      }

      errorResponse(res, 500, '获取执行指标失败', error.message);
    }
  }

  /**
   * 获取执行日志
   * GET /api/v1/workflow/logs
   */
  static async getExecutionLogs(req: Request, res: Response): Promise<void> {
    try {
      // 验证查询参数
      const queryParams = LogQuerySchema.parse(req.query);
      const { sessionId, workflowId, level, page, limit, startTime, endTime, search } = queryParams;

      // 构建过滤器
      const filter: any = {};
      if (sessionId) filter.executionId = sessionId;
      if (workflowId) filter.workflowId = workflowId;
      if (level) filter.level = level;
      if (startTime) filter.startTime = new Date(startTime);
      if (endTime) filter.endTime = new Date(endTime);
      if (search) filter.message = search;
      filter.limit = limit;
      filter.offset = (page - 1) * limit;

      // 获取日志数据
      const logResult = logAggregator.queryLogs(filter);
      const logStats = logAggregator.getLogStatistics(filter);

      const responseData = {
        logs: logResult.logs,
        pagination: {
          total: logResult.totalCount,
          page,
          limit,
          totalPages: Math.ceil(logResult.totalCount / limit),
          hasMore: logResult.hasMore
        },
        statistics: {
          totalLogs: logStats.totalLogs,
          levelDistribution: Object.fromEntries(logStats.levelDistribution),
          topErrors: logStats.topErrors
        },
        aggregations: logResult.aggregations ? {
          byLevel: Object.fromEntries(logResult.aggregations.byLevel),
          bySource: Object.fromEntries(logResult.aggregations.bySource)
        } : undefined
      };

      logger.info('获取执行日志成功', { filter, totalLogs: logResult.totalCount });
      successResponse(res, responseData, '获取执行日志成功');

    } catch (error: any) {
      logger.error('获取执行日志失败', { error: error.message });
      
      if (error instanceof z.ZodError) {
        return errorResponse(res, 400, '查询参数验证失败', error.errors);
      }

      errorResponse(res, 500, '获取执行日志失败', error.message);
    }
  }

  /**
   * 导出执行日志
   * GET /api/v1/workflow/logs/export
   */
  static async exportExecutionLogs(req: Request, res: Response): Promise<void> {
    try {
      // 验证查询参数
      const queryParams = LogQuerySchema.extend({
        format: z.enum(['json', 'csv', 'txt']).optional().default('json'),
        includeMetadata: z.boolean().optional().default(true)
      }).parse(req.query);

      const { sessionId, workflowId, level, startTime, endTime, search, format, includeMetadata } = queryParams;

      // 构建过滤器
      const filter: any = {};
      if (sessionId) filter.executionId = sessionId;
      if (workflowId) filter.workflowId = workflowId;
      if (level) filter.level = level;
      if (startTime) filter.startTime = new Date(startTime);
      if (endTime) filter.endTime = new Date(endTime);
      if (search) filter.message = search;

      // 导出日志
      const exportResult = await logAggregator.exportLogs({
        format,
        filter,
        includeMetadata
      });

      // 设置响应头
      res.setHeader('Content-Type', exportResult.contentType);
      res.setHeader('Content-Disposition', `attachment; filename="${exportResult.filename}"`);

      logger.info('导出执行日志成功', { format, filename: exportResult.filename });
      res.send(exportResult.data);

    } catch (error: any) {
      logger.error('导出执行日志失败', { error: error.message });
      
      if (error instanceof z.ZodError) {
        return errorResponse(res, 400, '查询参数验证失败', error.errors);
      }

      errorResponse(res, 500, '导出执行日志失败', error.message);
    }
  }

  /**
   * 获取执行状态跟踪信息
   * GET /api/v1/workflow/execution/:sessionId/tracking
   */
  static async getExecutionTracking(req: Request, res: Response): Promise<void> {
    try {
      const { sessionId } = req.params;

      if (!sessionId) {
        return errorResponse(res, 400, '会话ID不能为空');
      }

      // 获取会话信息
      const session = workflowCoordinator.getExecutionSession(sessionId);
      
      if (!session) {
        return errorResponse(res, 404, '执行会话不存在');
      }

      // 获取详细的跟踪信息
      const trackingData = [];
      
      for (let i = 0; i < session.currentStep; i++) {
        const stepState = await executionStateTracker.getExecutionState(session.executionId, i);
        if (stepState) {
          trackingData.push({
            stepIndex: i,
            status: stepState.status,
            startTime: stepState.startTime,
            endTime: stepState.endTime,
            duration: stepState.duration,
            retryCount: stepState.retryCount,
            error: stepState.error,
            logs: stepState.logs.slice(-10) // 只返回最近10条日志
          });
        }
      }

      const responseData = {
        sessionId,
        executionId: session.executionId,
        workflowId: session.workflowId,
        overallStatus: session.status,
        steps: trackingData,
        summary: {
          totalSteps: session.totalSteps,
          completedSteps: session.currentStep,
          errorCount: session.errors.length,
          startTime: session.startTime,
          endTime: session.endTime
        }
      };

      logger.info('获取执行跟踪信息成功', { sessionId });
      successResponse(res, responseData, '获取执行跟踪信息成功');

    } catch (error: any) {
      logger.error('获取执行跟踪信息失败', { error: error.message, sessionId: req.params.sessionId });
      errorResponse(res, 500, '获取执行跟踪信息失败', error.message);
    }
  }
}