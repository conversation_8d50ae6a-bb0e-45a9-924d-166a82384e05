import { Request, Response } from 'express'
import { z } from 'zod'
import { SystemService } from '@/services/system.service'
import { AuditService } from '@/services/audit.service'
import {
  SystemConfigCategory,
  SystemConfigDataType,
  CreateSystemConfigInput,
  UpdateSystemConfigInput,
  SystemConfigFilter,
  EmailTestConfig,
  SmsTestConfig,
  ConfigImportOptions
} from '@/types/system.types'

// 验证Schema
const createAlertRuleSchema = z.object({
  name: z.string().min(1).max(100),
  description: z.string().max(500).optional(),
  metricType: z.enum(['CPU', 'MEMORY', 'DISK', 'NETWORK', 'SERVICE']),
  condition: z.enum(['>', '<', '>=', '<=', '=']),
  threshold: z.number().min(0).max(100),
  duration: z.number().min(1).max(3600), // 持续时间（秒）
  severity: z.enum(['LOW', 'MEDIUM', 'HIGH', 'CRITICAL']),
  enabled: z.boolean().default(true),
  notificationChannels: z.array(z.enum(['EMAIL', 'SMS', 'WEBHOOK'])).optional()
})

const updateAlertRuleSchema = createAlertRuleSchema.partial()

const queryAlertsSchema = z.object({
  page: z.string().optional().transform(val => val ? parseInt(val) : 1),
  limit: z.string().optional().transform(val => val ? parseInt(val) : 20),
  severity: z.enum(['LOW', 'MEDIUM', 'HIGH', 'CRITICAL']).optional(),
  status: z.enum(['PENDING', 'ACKNOWLEDGED', 'RESOLVED']).optional(),
  dateFrom: z.string().optional().transform(val => val ? new Date(val) : undefined),
  dateTo: z.string().optional().transform(val => val ? new Date(val) : undefined)
})

// 系统配置验证Schema
const createSystemConfigSchema = z.object({
  category: z.nativeEnum(SystemConfigCategory),
  key: z.string().min(1).max(100).regex(/^[A-Z][A-Z0-9_]*$/, '键名必须以大写字母开头，仅包含大写字母、数字和下划线'),
  value: z.any(),
  description: z.string().max(500).optional(),
  dataType: z.nativeEnum(SystemConfigDataType).optional(),
  isEncrypted: z.boolean().optional(),
  isSystem: z.boolean().optional(),
  isPublic: z.boolean().optional(),
  validationRule: z.any().optional(),
  defaultValue: z.any().optional(),
  displayOrder: z.number().min(0).optional()
})

const updateSystemConfigSchema = z.object({
  value: z.any().optional(),
  description: z.string().max(500).optional(),
  isPublic: z.boolean().optional(),
  validationRule: z.any().optional(),
  displayOrder: z.number().min(0).optional(),
  changeReason: z.string().max(200).optional()
})

const batchUpdateConfigSchema = z.object({
  configs: z.array(z.object({
    category: z.nativeEnum(SystemConfigCategory),
    key: z.string().min(1),
    value: z.any(),
    changeReason: z.string().optional()
  }))
})

const getConfigsQuerySchema = z.object({
  category: z.nativeEnum(SystemConfigCategory).optional(),
  isPublic: z.string().optional().transform(val => val === undefined ? undefined : val === 'true'),
  isSystem: z.string().optional().transform(val => val === undefined ? undefined : val === 'true'),
  search: z.string().optional(),
  includeEncrypted: z.string().optional().transform(val => val === undefined ? undefined : val === 'true')
})

const testEmailConfigSchema = z.object({
  recipient: z.string().email(),
  subject: z.string().max(200).optional(),
  content: z.string().max(1000).optional()
})

const testSmsConfigSchema = z.object({
  phone: z.string().regex(/^1[3-9]\d{9}$/, '手机号格式不正确'),
  message: z.string().max(100).optional()
})

const configImportSchema = z.object({
  exportData: z.object({
    version: z.string(),
    timestamp: z.string(),
    configs: z.array(z.any())
  }),
  options: z.object({
    overwriteExisting: z.boolean().optional(),
    skipEncrypted: z.boolean().optional(),
    categories: z.array(z.nativeEnum(SystemConfigCategory)).optional()
  }).optional()
})

const getConfigHistorySchema = z.object({
  page: z.string().optional().transform(val => val ? parseInt(val) : 1),
  limit: z.string().optional().transform(val => val ? parseInt(val) : 20),
  category: z.nativeEnum(SystemConfigCategory).optional(),
  key: z.string().optional()
})

// 获取系统状态
export const getSystemStatus = async (_req: Request, res: Response) => {
  try {
    const status = await SystemService.getSystemStatus()

    return res.json({
      success: true,
      data: status
    })
  } catch (error) {
    console.error('Get system status error:', error)
    return res.status(500).json({
      success: false,
      message: '获取系统状态失败'
    })
  }
}

// 获取系统资源使用情况
export const getSystemResources = async (_req: Request, res: Response) => {
  try {
    const resources = await SystemService.getSystemResources()

    return res.json({
      success: true,
      data: resources
    })
  } catch (error) {
    console.error('Get system resources error:', error)
    return res.status(500).json({
      success: false,
      message: '获取系统资源使用情况失败'
    })
  }
}

// 获取服务健康检查
export const getHealthCheck = async (_req: Request, res: Response) => {
  try {
    const healthCheck = await SystemService.getHealthCheck()

    return res.json({
      success: true,
      data: healthCheck
    })
  } catch (error) {
    console.error('Get health check error:', error)
    return res.status(500).json({
      success: false,
      message: '获取健康检查失败'
    })
  }
}

// 获取数据库状态
export const getDatabaseStatus = async (_req: Request, res: Response) => {
  try {
    const dbStatus = await SystemService.getDatabaseStatus()

    return res.json({
      success: true,
      data: dbStatus
    })
  } catch (error) {
    console.error('Get database status error:', error)
    return res.status(500).json({
      success: false,
      message: '获取数据库状态失败'
    })
  }
}

// 获取Redis状态
export const getRedisStatus = async (_req: Request, res: Response) => {
  try {
    const redisStatus = await SystemService.getRedisStatus()

    return res.json({
      success: true,
      data: redisStatus
    })
  } catch (error) {
    console.error('Get Redis status error:', error)
    return res.status(500).json({
      success: false,
      message: '获取Redis状态失败'
    })
  }
}

// 获取系统性能指标
export const getPerformanceMetrics = async (req: Request, res: Response) => {
  try {
    const { hours = 24 } = req.query
    const hoursNumber = parseInt(hours as string) || 24

    const metrics = await SystemService.getPerformanceMetrics(hoursNumber)

    return res.json({
      success: true,
      data: metrics
    })
  } catch (error) {
    console.error('Get performance metrics error:', error)
    return res.status(500).json({
      success: false,
      message: '获取系统性能指标失败'
    })
  }
}

// 创建告警规则
export const createAlertRule = async (req: Request, res: Response) => {
  try {
    const data = createAlertRuleSchema.parse(req.body)
    const currentUser = (req as any).user

    const rule = await SystemService.createAlertRule(data)

    // 记录操作日志
    await AuditService.log({
      userId: currentUser.userId,
      action: 'CREATE',
      resource: 'ALERT_RULE',
      resourceId: rule.id,
      details: {
        ruleName: data.name,
        metricType: data.metricType,
        threshold: data.threshold
      },
      ipAddress: req.ip
    })

    return res.status(201).json({
      success: true,
      message: '告警规则创建成功',
      data: rule
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        message: '请求参数验证失败',
        errors: error.errors
      })
    }

    console.error('Create alert rule error:', error)
    return res.status(500).json({
      success: false,
      message: '创建告警规则失败'
    })
  }
}

// 获取告警规则列表
export const getAlertRules = async (_req: Request, res: Response) => {
  try {
    const rules = await SystemService.getAlertRules()

    return res.json({
      success: true,
      data: rules
    })
  } catch (error) {
    console.error('Get alert rules error:', error)
    return res.status(500).json({
      success: false,
      message: '获取告警规则失败'
    })
  }
}

// 更新告警规则
export const updateAlertRule = async (req: Request, res: Response) => {
  try {
    const { id } = req.params
    const data = updateAlertRuleSchema.parse(req.body)
    const currentUser = (req as any).user

    if (!id) {
      return res.status(400).json({
        success: false,
        message: 'ID参数缺失'
      })
    }

    const rule = await SystemService.updateAlertRule(id, data)

    // 记录操作日志
    await AuditService.log({
      userId: currentUser.userId,
      action: 'UPDATE',
      resource: 'ALERT_RULE',
      resourceId: id,
      details: data,
      ipAddress: req.ip
    })

    return res.json({
      success: true,
      message: '告警规则更新成功',
      data: rule
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        message: '请求参数验证失败',
        errors: error.errors
      })
    }

    console.error('Update alert rule error:', error)
    
    if (error instanceof Error) {
      if (error.message.includes('不存在')) {
        return res.status(404).json({
          success: false,
          message: error.message
        })
      }
    }

    return res.status(500).json({
      success: false,
      message: '更新告警规则失败'
    })
  }
}

// 删除告警规则
export const deleteAlertRule = async (req: Request, res: Response) => {
  try {
    const { id } = req.params
    const currentUser = (req as any).user

    if (!id) {
      return res.status(400).json({
        success: false,
        message: 'ID参数缺失'
      })
    }

    await SystemService.deleteAlertRule(id)

    // 记录操作日志
    await AuditService.log({
      userId: currentUser.userId,
      action: 'DELETE',
      resource: 'ALERT_RULE',
      resourceId: id,
      details: {},
      ipAddress: req.ip
    })

    return res.json({
      success: true,
      message: '告警规则删除成功'
    })
  } catch (error) {
    console.error('Delete alert rule error:', error)
    
    if (error instanceof Error) {
      if (error.message.includes('不存在')) {
        return res.status(404).json({
          success: false,
          message: error.message
        })
      }
    }

    return res.status(500).json({
      success: false,
      message: '删除告警规则失败'
    })
  }
}

// 获取告警历史
export const getAlerts = async (req: Request, res: Response) => {
  try {
    const {
      page,
      limit,
      severity,
      status,
      dateFrom,
      dateTo
    } = queryAlertsSchema.parse(req.query)

    const result = await SystemService.getAlerts(
      page,
      limit,
      severity,
      status,
      dateFrom,
      dateTo
    )

    return res.json({
      success: true,
      data: result
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        message: '请求参数验证失败',
        errors: error.errors
      })
    }

    console.error('Get alerts error:', error)
    return res.status(500).json({
      success: false,
      message: '获取告警历史失败'
    })
  }
}

// 确认告警
export const acknowledgeAlert = async (req: Request, res: Response) => {
  try {
    const { id } = req.params
    const currentUser = (req as any).user

    if (!id) {
      return res.status(400).json({
        success: false,
        message: 'ID参数缺失'
      })
    }

    const alert = await SystemService.acknowledgeAlert(id, currentUser.userId)

    // 记录操作日志
    await AuditService.log({
      userId: currentUser.userId,
      action: 'UPDATE',
      resource: 'ALERT',
      resourceId: id,
      details: { action: 'acknowledge' },
      ipAddress: req.ip
    })

    return res.json({
      success: true,
      message: '告警已确认',
      data: alert
    })
  } catch (error) {
    console.error('Acknowledge alert error:', error)
    
    if (error instanceof Error) {
      if (error.message.includes('不存在')) {
        return res.status(404).json({
          success: false,
          message: error.message
        })
      }
    }

    return res.status(500).json({
      success: false,
      message: '确认告警失败'
    })
  }
}

// 解决告警
export const resolveAlert = async (req: Request, res: Response) => {
  try {
    const { id } = req.params
    const currentUser = (req as any).user

    if (!id) {
      return res.status(400).json({
        success: false,
        message: 'ID参数缺失'
      })
    }

    const alert = await SystemService.resolveAlert(id, currentUser.userId)

    // 记录操作日志
    await AuditService.log({
      userId: currentUser.userId,
      action: 'UPDATE',
      resource: 'ALERT',
      resourceId: id,
      details: { action: 'resolve' },
      ipAddress: req.ip
    })

    return res.json({
      success: true,
      message: '告警已解决',
      data: alert
    })
  } catch (error) {
    console.error('Resolve alert error:', error)
    
    if (error instanceof Error) {
      if (error.message.includes('不存在')) {
        return res.status(404).json({
          success: false,
          message: error.message
        })
      }
    }

    return res.status(500).json({
      success: false,
      message: '解决告警失败'
    })
  }
}

// 获取告警统计
export const getAlertStats = async (_req: Request, res: Response) => {
  try {
    const stats = await SystemService.getAlertStats()

    return res.json({
      success: true,
      data: stats
    })
  } catch (error) {
    console.error('Get alert stats error:', error)
    return res.status(500).json({
      success: false,
      message: '获取告警统计失败'
    })
  }
}

/**
 * ===== 系统配置管理 =====
 */

// 获取系统配置列表
export const getSystemConfigs = async (req: Request, res: Response) => {
  try {
    const {
      category,
      isPublic,
      isSystem,
      search,
      includeEncrypted
    } = getConfigsQuerySchema.parse(req.query)
    
    const filter: SystemConfigFilter = {}
    if (category) filter.category = category
    if (isPublic !== undefined) filter.isPublic = isPublic
    if (isSystem !== undefined) filter.isSystem = isSystem
    if (search) filter.search = search

    const configs = await SystemService.getSystemConfigs(filter, includeEncrypted)

    return res.json({
      success: true,
      data: configs
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        message: '请求参数验证失败',
        errors: error.errors
      })
    }

    console.error('Get system configs error:', error)
    return res.status(500).json({
      success: false,
      message: '获取系统配置失败'
    })
  }
}

// 获取单个系统配置
export const getSystemConfig = async (req: Request, res: Response) => {
  try {
    const { category, key } = req.params
    const { includeEncrypted } = req.query
    
    if (!category || !key) {
      return res.status(400).json({
        success: false,
        message: '分类和键名参数缺失'
      })
    }

    const config = await SystemService.getSystemConfig(
      category as SystemConfigCategory,
      key,
      includeEncrypted === 'true'
    )

    if (!config) {
      return res.status(404).json({
        success: false,
        message: '配置项不存在'
      })
    }

    return res.json({
      success: true,
      data: config
    })
  } catch (error) {
    console.error('Get system config error:', error)
    return res.status(500).json({
      success: false,
      message: '获取系统配置失败'
    })
  }
}

// 创建系统配置
export const createSystemConfig = async (req: Request, res: Response) => {
  try {
    const data = createSystemConfigSchema.parse(req.body)
    const currentUser = (req as any).user

    const config = await SystemService.createSystemConfig(data, currentUser.userId)

    // 记录操作日志
    await AuditService.log({
      userId: currentUser.userId,
      action: 'CREATE',
      resource: 'SYSTEM_CONFIG',
      resourceId: config.id,
      details: {
        category: data.category,
        key: data.key,
        description: data.description
      },
      ipAddress: req.ip
    })

    return res.status(201).json({
      success: true,
      message: '系统配置创建成功',
      data: config
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        message: '请求参数验证失败',
        errors: error.errors
      })
    }

    console.error('Create system config error:', error)
    
    if (error instanceof Error) {
      if (error.message.includes('已存在')) {
        return res.status(409).json({
          success: false,
          message: error.message
        })
      }
    }

    return res.status(500).json({
      success: false,
      message: '创建系统配置失败'
    })
  }
}

// 更新系统配置
export const updateSystemConfig = async (req: Request, res: Response) => {
  try {
    const { category, key } = req.params
    const data = updateSystemConfigSchema.parse(req.body)
    const currentUser = (req as any).user

    if (!category || !key) {
      return res.status(400).json({
        success: false,
        message: '分类和键名参数缺失'
      })
    }

    const config = await SystemService.updateSystemConfig(
      category as SystemConfigCategory,
      key,
      data,
      currentUser.userId
    )

    // 记录操作日志
    await AuditService.log({
      userId: currentUser.userId,
      action: 'UPDATE',
      resource: 'SYSTEM_CONFIG',
      resourceId: config.id,
      details: {
        category,
        key,
        changeReason: data.changeReason
      },
      ipAddress: req.ip
    })

    return res.json({
      success: true,
      message: '系统配置更新成功',
      data: config
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        message: '请求参数验证失败',
        errors: error.errors
      })
    }

    console.error('Update system config error:', error)
    
    if (error instanceof Error) {
      if (error.message.includes('不存在')) {
        return res.status(404).json({
          success: false,
          message: error.message
        })
      }
    }

    return res.status(500).json({
      success: false,
      message: '更新系统配置失败'
    })
  }
}

// 批量更新系统配置
export const batchUpdateSystemConfigs = async (req: Request, res: Response) => {
  try {
    const { configs } = batchUpdateConfigSchema.parse(req.body)
    const currentUser = (req as any).user

    const results = await SystemService.batchUpdateSystemConfigs(configs, currentUser.userId)

    const successCount = results.filter(r => r.success).length
    const failureCount = results.filter(r => !r.success).length

    // 记录操作日志
    await AuditService.log({
      userId: currentUser.userId,
      action: 'BATCH_UPDATE',
      resource: 'SYSTEM_CONFIG',
      resourceId: 'batch',
      details: {
        totalConfigs: configs.length,
        successCount,
        failureCount
      },
      ipAddress: req.ip
    })

    return res.json({
      success: failureCount === 0,
      message: failureCount === 0 
        ? '所有配置更新成功'
        : `${successCount}个配置更新成功，${failureCount}个失败`,
      data: results
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        message: '请求参数验证失败',
        errors: error.errors
      })
    }

    console.error('Batch update system configs error:', error)
    return res.status(500).json({
      success: false,
      message: '批量更新系统配置失败'
    })
  }
}

// 删除系统配置
export const deleteSystemConfig = async (req: Request, res: Response) => {
  try {
    const { category, key } = req.params
    const currentUser = (req as any).user

    if (!category || !key) {
      return res.status(400).json({
        success: false,
        message: '分类和键名参数缺失'
      })
    }

    await SystemService.deleteSystemConfig(
      category as SystemConfigCategory,
      key
    )

    // 记录操作日志
    await AuditService.log({
      userId: currentUser.userId,
      action: 'DELETE',
      resource: 'SYSTEM_CONFIG',
      resourceId: `${category}.${key}`,
      details: { category, key },
      ipAddress: req.ip
    })

    return res.json({
      success: true,
      message: '系统配置删除成功'
    })
  } catch (error) {
    console.error('Delete system config error:', error)
    
    if (error instanceof Error) {
      if (error.message.includes('不存在')) {
        return res.status(404).json({
          success: false,
          message: error.message
        })
      }
      if (error.message.includes('不能删除系统配置')) {
        return res.status(403).json({
          success: false,
          message: error.message
        })
      }
    }

    return res.status(500).json({
      success: false,
      message: '删除系统配置失败'
    })
  }
}

// 重置配置为默认值
export const resetConfigToDefault = async (req: Request, res: Response) => {
  try {
    const { category, key } = req.params
    const currentUser = (req as any).user

    if (!category || !key) {
      return res.status(400).json({
        success: false,
        message: '分类和键名参数缺失'
      })
    }

    const config = await SystemService.resetConfigToDefault(
      category as SystemConfigCategory,
      key,
      currentUser.userId
    )

    // 记录操作日志
    await AuditService.log({
      userId: currentUser.userId,
      action: 'RESET',
      resource: 'SYSTEM_CONFIG',
      resourceId: config.id,
      details: { category, key, action: 'reset_to_default' },
      ipAddress: req.ip
    })

    return res.json({
      success: true,
      message: '配置已重置为默认值',
      data: config
    })
  } catch (error) {
    console.error('Reset config to default error:', error)
    
    if (error instanceof Error) {
      if (error.message.includes('不存在')) {
        return res.status(404).json({
          success: false,
          message: error.message
        })
      }
      if (error.message.includes('没有设置默认值')) {
        return res.status(400).json({
          success: false,
          message: error.message
        })
      }
    }

    return res.status(500).json({
      success: false,
      message: '重置配置失败'
    })
  }
}

// 获取配置变更历史
export const getConfigHistory = async (req: Request, res: Response) => {
  try {
    const { page, limit, category, key } = getConfigHistorySchema.parse(req.query)

    const result = await SystemService.getConfigHistory(category, key, page, limit)

    return res.json({
      success: true,
      data: result
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        message: '请求参数验证失败',
        errors: error.errors
      })
    }

    console.error('Get config history error:', error)
    return res.status(500).json({
      success: false,
      message: '获取配置历史失败'
    })
  }
}

// 测试邮件配置
export const testEmailConfig = async (req: Request, res: Response) => {
  try {
    const config = testEmailConfigSchema.parse(req.body)
    const currentUser = (req as any).user

    const result = await SystemService.testEmailConfig(config)

    // 记录操作日志
    await AuditService.log({
      userId: currentUser.userId,
      action: 'TEST',
      resource: 'EMAIL_CONFIG',
      resourceId: 'test',
      details: {
        recipient: config.recipient,
        success: result.success
      },
      ipAddress: req.ip
    })

    return res.json({
      success: true,
      data: result
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        message: '请求参数验证失败',
        errors: error.errors
      })
    }

    console.error('Test email config error:', error)
    return res.status(500).json({
      success: false,
      message: '测试邮件配置失败'
    })
  }
}

// 测试短信配置
export const testSmsConfig = async (req: Request, res: Response) => {
  try {
    const config = testSmsConfigSchema.parse(req.body)
    const currentUser = (req as any).user

    const result = await SystemService.testSmsConfig(config)

    // 记录操作日志
    await AuditService.log({
      userId: currentUser.userId,
      action: 'TEST',
      resource: 'SMS_CONFIG',
      resourceId: 'test',
      details: {
        phone: config.phone,
        success: result.success
      },
      ipAddress: req.ip
    })

    return res.json({
      success: true,
      data: result
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        message: '请求参数验证失败',
        errors: error.errors
      })
    }

    console.error('Test SMS config error:', error)
    return res.status(500).json({
      success: false,
      message: '测试短信配置失败'
    })
  }
}

// 导出系统配置
export const exportConfigs = async (req: Request, res: Response) => {
  try {
    const { categories } = req.query
    const currentUser = (req as any).user
    
    let categoryList: SystemConfigCategory[] | undefined
    if (categories && typeof categories === 'string') {
      categoryList = categories.split(',') as SystemConfigCategory[]
    }

    const exportData = await SystemService.exportConfigs(categoryList)

    // 记录操作日志
    await AuditService.log({
      userId: currentUser.userId,
      action: 'EXPORT',
      resource: 'SYSTEM_CONFIG',
      resourceId: 'export',
      details: {
        categories: categoryList,
        configCount: exportData.configs.length
      },
      ipAddress: req.ip
    })

    // 设置下载文件名
    const timestamp = new Date().toISOString().split('T')[0]
    const filename = `system-configs-${timestamp}.json`
    
    res.setHeader('Content-Type', 'application/json')
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`)
    
    return res.json(exportData)
  } catch (error) {
    console.error('Export configs error:', error)
    return res.status(500).json({
      success: false,
      message: '导出系统配置失败'
    })
  }
}

// 导入系统配置
export const importConfigs = async (req: Request, res: Response) => {
  try {
    const { exportData, options } = configImportSchema.parse(req.body)
    const currentUser = (req as any).user

    const result = await SystemService.importConfigs(
      exportData,
      options || {},
      currentUser.userId
    )

    // 记录操作日志
    await AuditService.log({
      userId: currentUser.userId,
      action: 'IMPORT',
      resource: 'SYSTEM_CONFIG',
      resourceId: 'import',
      details: {
        imported: result.imported,
        skipped: result.skipped,
        errors: result.errors.length
      },
      ipAddress: req.ip
    })

    return res.json({
      success: result.success,
      message: result.message,
      data: result
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        message: '请求参数验证失败',
        errors: error.errors
      })
    }

    console.error('Import configs error:', error)
    return res.status(500).json({
      success: false,
      message: '导入系统配置失败'
    })
  }
}

// 初始化默认系统配置
export const initializeDefaultConfigs = async (req: Request, res: Response) => {
  try {
    const currentUser = (req as any).user

    const results = await SystemService.initializeDefaultConfigs(currentUser.userId)
    
    const successCount = results.filter(r => r.success).length
    const failureCount = results.filter(r => !r.success).length

    // 记录操作日志
    await AuditService.log({
      userId: currentUser.userId,
      action: 'INITIALIZE',
      resource: 'SYSTEM_CONFIG',
      resourceId: 'default',
      details: {
        successCount,
        failureCount,
        totalConfigs: results.length
      },
      ipAddress: req.ip
    })

    return res.json({
      success: failureCount === 0,
      message: failureCount === 0
        ? `成功初始化 ${successCount} 个默认配置`
        : `初始化完成，${successCount} 个成功，${failureCount} 个失败`,
      data: results
    })
  } catch (error) {
    console.error('Initialize default configs error:', error)
    return res.status(500).json({
      success: false,
      message: '初始化默认配置失败'
    })
  }
}