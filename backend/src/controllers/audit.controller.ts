import { Request, Response } from 'express'
import { z } from 'zod'
import { AuditService } from '@/services/audit.service'
// 装饰器已移除，改为使用中间件
// import { AUDIT_PERMISSIONS } from '@/constants/permissions'

// 验证Schema
const getLogsSchema = z.object({
  page: z.string().optional().transform(val => val ? parseInt(val) : 1),
  limit: z.string().optional().transform(val => val ? parseInt(val) : 50),
  userId: z.string().optional(),
  resource: z.enum(['AUTH', 'CUSTOMER', 'ARCHIVE', 'SERVICE', 'CONFIGURATION', 'SLA']).optional(),
  action: z.enum(['CREATE', 'UPDATE', 'DELETE', 'VIEW', 'LOGIN', 'LOGOUT', 'STATUS_CHANGE']).optional(),
  startDate: z.string().optional().transform(val => val ? new Date(val) : undefined),
  endDate: z.string().optional().transform(val => val ? new Date(val) : undefined)
})

const getStatsSchema = z.object({
  userId: z.string().optional(),
  startDate: z.string().optional().transform(val => val ? new Date(val) : undefined),
  endDate: z.string().optional().transform(val => val ? new Date(val) : undefined),
  groupBy: z.enum(['hour', 'day', 'week', 'month']).optional().default('day')
})

// 删除未使用的schema

const riskAnalysisSchema = z.object({
  days: z.string().optional().transform(val => val ? parseInt(val) : 7),
  threshold: z.string().optional().transform(val => val ? parseInt(val) : 10)
})

const cleanupSchema = z.object({
  days: z.string().optional().transform(val => val ? parseInt(val) : 90),
  dryRun: z.string().optional().transform(val => val === 'true')
})

// 获取操作日志列表
export const getAuditLogs = async (req: Request, res: Response) => {
  try {
    const {
      page,
      limit,
      userId,
      resource,
      action,
      startDate,
      endDate
    } = getLogsSchema.parse(req.query)

    const result = await AuditService.getLogs(
      page,
      limit,
      userId,
      resource,
      action,
      startDate,
      endDate
    )

    // 前端期望将分页字段提升到 data 顶层
    return res.json({
      success: true,
      data: {
        logs: result.logs,
        total: result.pagination.total,
        page: result.pagination.page,
        limit: result.pagination.limit,
        totalPages: result.pagination.totalPages
      }
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        message: '请求参数验证失败',
        errors: error.errors
      })
    }

    console.error('Get audit logs error:', error)
    return res.status(500).json({
      success: false,
      message: '获取操作日志失败'
    })
  }
}

// 获取用户操作统计
export const getAuditStats = async (req: Request, res: Response) => {
  try {
    const { userId, startDate, endDate } = getStatsSchema.parse(req.query)

    const stats = await AuditService.getUserActionStats(userId, startDate, endDate)

    // 按资源类型分组统计
    const resourceStats: Record<string, Record<string, number>> = {}
    let totalOperations = 0

    stats.forEach(stat => {
      const bucket = resourceStats[stat.resource] || (resourceStats[stat.resource] = {})
      bucket[stat.action] = stat.count
      totalOperations += stat.count
    })

    // 计算最近活跃用户（如果没有指定userId）
    let activeUsers: any[] = []
    if (!userId) {
      // 获取最近7天最活跃的用户
      const recentDate = new Date()
      recentDate.setDate(recentDate.getDate() - 7)

      const userStats = await AuditService.getLogs(1, 100, undefined, undefined, undefined, recentDate)
      const userActivity: Record<string, number> = {}

      userStats.logs.forEach(log => {
        const logUserId = log.userId
        userActivity[logUserId] = (userActivity[logUserId] || 0) + 1
      })

      activeUsers = Object.entries(userActivity)
        .sort(([, a], [, b]) => b - a)
        .slice(0, 10)
        .map(([logUserId, count]) => {
          const userLog = userStats.logs.find(log => log.userId === logUserId)
          return {
            userId: logUserId,
            username: userLog?.user?.username,
            fullName: userLog?.user?.fullName,
            operationCount: count
          }
        })
    }

    return res.json({
      success: true,
      data: {
        totalOperations,
        resourceStats,
        actionBreakdown: stats,
        activeUsers: userId ? [] : activeUsers,
        timeRange: {
          startDate,
          endDate
        }
      }
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        message: '请求参数验证失败',
        errors: error.errors
      })
    }

    console.error('Get audit stats error:', error)
    return res.status(500).json({
      success: false,
      message: '获取操作统计失败'
    })
  }
}

// 获取当前用户的操作日志
export const getMyAuditLogs = async (req: Request, res: Response) => {
  try {
    const userId = (req as any).user?.userId
    if (!userId) {
      return res.status(401).json({
        success: false,
        message: '用户未认证'
      })
    }

    const { page = 1, limit = 20 } = req.query
    const pageNum = parseInt(page as string) || 1
    const limitNum = parseInt(limit as string) || 20

    const result = await AuditService.getLogs(
      pageNum,
      limitNum,
      userId
    )

    return res.json({
      success: true,
      data: result
    })
  } catch (error) {
    console.error('Get my audit logs error:', error)
    return res.status(500).json({
      success: false,
      message: '获取个人操作日志失败'
    })
  }
}

// 获取审计日志详情
export const getAuditLogDetail = async (req: Request, res: Response) => {
  try {
    const { id } = req.params
    if (!id) {
      return res.status(400).json({
        success: false,
        message: '审计日志ID不能为空'
      })
    }

    const logDetail = await AuditService.getLogDetail(id)

    return res.json({
      success: true,
      data: logDetail
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        message: '请求参数验证失败',
        errors: error.errors
      })
    }

    console.error('Get audit log detail error:', error)
    
    if (error instanceof Error) {
      if (error.message.includes('不存在')) {
        return res.status(404).json({
          success: false,
          message: error.message
        })
      }
    }

    return res.status(500).json({
      success: false,
      message: '获取审计日志详情失败'
    })
  }
}

// 获取时间趋势统计
export const getTimeSeriesStats = async (req: Request, res: Response) => {
  try {
    const { userId, startDate, endDate, groupBy } = getStatsSchema.parse(req.query)

    const stats = await AuditService.getTimeSeriesStats(
      userId,
      startDate,
      endDate,
      groupBy
    )

    return res.json({
      success: true,
      data: stats
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        message: '请求参数验证失败',
        errors: error.errors
      })
    }

    console.error('Get time series stats error:', error)
    return res.status(500).json({
      success: false,
      message: '获取时间趋势统计失败'
    })
  }
}

// 风险行为分析
export const getRiskAnalysis = async (req: Request, res: Response) => {
  try {
    const { days, threshold } = riskAnalysisSchema.parse(req.query)

    const analysis = await AuditService.getRiskAnalysis(days, threshold)

    return res.json({
      success: true,
      data: analysis
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        message: '请求参数验证失败',
        errors: error.errors
      })
    }

    console.error('Get risk analysis error:', error)
    return res.status(500).json({
      success: false,
      message: '获取风险分析失败'
    })
  }
}

// 系统性能影响分析
export const getSystemImpactAnalysis = async (req: Request, res: Response) => {
  try {
    const { startDate, endDate } = getStatsSchema.parse(req.query)

    const analysis = await AuditService.getSystemImpactAnalysis(startDate, endDate)

    return res.json({
      success: true,
      data: analysis
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        message: '请求参数验证失败',
        errors: error.errors
      })
    }

    console.error('Get system impact analysis error:', error)
    return res.status(500).json({
      success: false,
      message: '获取系统影响分析失败'
    })
  }
}

// 清理过期日志
export const cleanupExpiredLogs = async (req: Request, res: Response) => {
  try {
    const currentUser = (req as any).user
    
    if (!currentUser.permissions.includes('admin:all')) {
      return res.status(403).json({
        success: false,
        message: '权限不足，只有超级管理员可以执行日志清理'
      })
    }

    const { days, dryRun } = cleanupSchema.parse(req.query)

    const result = await AuditService.cleanupExpiredLogs(days, dryRun)

    // 记录清理操作日志（只有非演练模式才记录）
    if (!dryRun) {
      await AuditService.log({
        userId: currentUser.userId,
        action: 'DELETE',
        resource: 'AUDIT',
        details: {
          action: 'cleanup_expired_logs',
          retentionDays: days,
          deletedCount: result.deletedCount
        },
        ipAddress: (req.headers['x-real-ip'] as string) || ((req.headers['x-forwarded-for'] as string)?.split(',')[0]?.trim()) || req.ip || null
      })
    }

    return res.json({
      success: true,
      message: dryRun ? '演练模式：显示将要删除的日志数量' : '过期日志清理完成',
      data: result
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        message: '请求参数验证失败',
        errors: error.errors
      })
    }

    console.error('Cleanup expired logs error:', error)
    return res.status(500).json({
      success: false,
      message: '清理过期日志失败'
    })
  }
}

// 导出操作日志（管理员功能）
export const exportAuditLogs = async (req: Request, res: Response) => {
  try {
    const currentUser = (req as any).user
    const {
      userId,
      resource,
      action,
      startDate,
      endDate
    } = getLogsSchema.parse(req.query)

    const format = req.query['format'] as string || 'csv'

    if (!['csv', 'excel'].includes(format)) {
      return res.status(400).json({
        success: false,
        message: '不支持的导出格式，支持csv或excel'
      })
    }

    // 获取所有符合条件的日志（不分页）
    const result = await AuditService.getLogs(
      1,
      10000, // 最多导出10000条记录
      userId,
      resource,
      action,
      startDate,
      endDate
    )

    if (result.logs.length === 0) {
      return res.status(404).json({
        success: false,
        message: '没有找到符合条件的审计日志'
      })
    }

    let exportResult
    if (format === 'excel') {
      exportResult = await AuditService.exportLogsToExcel(result.logs)
    } else {
      exportResult = await AuditService.exportLogsToCSV(result.logs)
    }

    // 记录导出操作日志
    await AuditService.log({
      userId: currentUser.userId,
      action: 'EXPORT',
      resource: 'AUDIT',
      details: {
        format,
        filters: { userId, resource, action, startDate, endDate },
        exportedCount: result.logs.length
      },
      ipAddress: (req.headers['x-real-ip'] as string) || ((req.headers['x-forwarded-for'] as string)?.split(',')[0]?.trim()) || req.ip || null
    })

    const timestamp = new Date().toISOString().split('T')[0]
    const filename = `audit_logs_${timestamp}.${format === 'excel' ? 'xlsx' : 'csv'}`

    if (format === 'excel') {
      res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
    } else {
      res.setHeader('Content-Type', 'text/csv; charset=utf-8')
    }
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`)
    res.setHeader('Content-Length', Buffer.byteLength(exportResult))

    return res.send(exportResult)
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        message: '请求参数验证失败',
        errors: error.errors
      })
    }

    console.error('Export audit logs error:', error)
    return res.status(500).json({
      success: false,
      message: '导出操作日志失败'
    })
  }
}