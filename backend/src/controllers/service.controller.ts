import { Request, Response } from 'express'
import { prisma } from '@/config/database.config'
import { z } from 'zod'
import { 
  getServiceOperationHistory,
  recordServiceCreation,
  recordStatusChange,
  recordTransfer,
  recordServiceUpdate,
  recordWorkLogAdded,
  recordCommentAdded
} from '@/utils/service-operation-history.util'
import ServiceReportService from '../services/service-report.service'
import { CacheService } from '@/config/redis.config'
import { RealtimeService } from '@/services/realtime.service'
import { v4 as uuidv4 } from 'uuid'
import path from 'path'
import fs from 'fs/promises'

// 验证Schema
const createServiceSchema = z.object({
  archiveId: z.string().min(1),
  slaTemplateId: z.string().optional(),
  title: z.string().min(1).max(200),
  description: z.string().min(1),
  category: z.enum(['MAINTENANCE', 'SUPPORT', 'UPGRADE', 'BUGFIX', 'CONSULTING', 'MONITORING']).default('SUPPORT'),
  priority: z.enum(['LOW', 'MEDIUM', 'HIGH', 'URGENT']).default('MEDIUM'),
  customerContact: z.string().max(100).optional(),
  estimatedHours: z.number().min(0).optional()
})

const updateServiceSchema = z.object({
  title: z.string().min(1).max(200).optional(),
  description: z.string().min(1).optional(),
  category: z.enum(['MAINTENANCE', 'SUPPORT', 'UPGRADE', 'BUGFIX', 'CONSULTING', 'MONITORING']).optional(),
  priority: z.enum(['LOW', 'MEDIUM', 'HIGH', 'URGENT']).optional(),
  status: z.enum(['PENDING', 'IN_PROGRESS', 'WAITING_CUSTOMER', 'RESOLVED', 'CLOSED']).optional(),
  assignedTo: z.string().optional(),
  customerContact: z.string().max(100).optional(),
  estimatedHours: z.number().min(0).optional(),
  actualHours: z.number().min(0).optional(),
  resolution: z.string().optional(),
  customerFeedback: z.string().optional(),
  satisfaction: z.number().min(1).max(5).optional(),
  // 操作相关字段
  statusNote: z.string().optional(),
  operationType: z.string().optional(),
  operationNote: z.string().optional(),
  transferNote: z.string().optional()
})

const createWorkLogSchema = z.object({
  description: z.string().min(1),
  workHours: z.number().min(0),
  workDate: z.string().transform(val => new Date(val)),
  category: z.enum(['ANALYSIS', 'IMPLEMENTATION', 'TESTING', 'DOCUMENTATION', 'COMMUNICATION', 'MAINTENANCE', 'SUPPORT', 'OTHER']).default('MAINTENANCE')
})

const createCommentSchema = z.object({
  content: z.string().min(1),
  isInternal: z.boolean().default(false)
})

const querySchema = z.object({
  page: z.string().optional().transform(val => val ? parseInt(val) : 1),
  limit: z.string().optional().transform(val => val ? parseInt(val) : 10),
  search: z.string().optional(),
  status: z.enum(['PENDING', 'IN_PROGRESS', 'WAITING_CUSTOMER', 'RESOLVED', 'CLOSED']).optional(),
  category: z.enum(['MAINTENANCE', 'SUPPORT', 'UPGRADE', 'BUGFIX', 'CONSULTING', 'MONITORING']).optional(),
  priority: z.enum(['LOW', 'MEDIUM', 'HIGH', 'URGENT']).optional(),
  assignedTo: z.string().optional(),
  customerId: z.string().optional(),
  archiveId: z.string().optional()
})

// 生成工单号
function generateTicketNumber(): string {
  const now = new Date()
  const year = now.getFullYear()
  const month = String(now.getMonth() + 1).padStart(2, '0')
  const day = String(now.getDate()).padStart(2, '0')
  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0')
  return `OPS-${year}${month}${day}-${random}`
}

// 获取服务工单列表
export const getServices = async (req: Request, res: Response) => {
  try {
    const { page, limit, search, status, category, priority, assignedTo, customerId, archiveId } = querySchema.parse(req.query)
    
    const skip = (page - 1) * limit
    
    // 获取当前用户信息
    const authReq = req as any
    const currentUser = authReq.user
    
    if (!currentUser) {
      return res.status(401).json({
        success: false,
        message: '用户未认证'
      })
    }

    // 检查用户权限
    const userPermissions = currentUser.permissions || []
    const hasViewAllPermission = userPermissions.includes('service:view_all') || userPermissions.includes('admin:all')
    
    // 构建查询条件
    const where: any = {}
    
    if (search) {
      where.OR = [
        { ticketNumber: { contains: search } },
        { title: { contains: search } },
        { description: { contains: search } },
        { customerContact: { contains: search } }
      ]
    }
    
    if (status) {
      where.status = status
    }
    
    if (category) {
      where.category = category
    }
    
    if (priority) {
      where.priority = priority
    }
    
    if (assignedTo) {
      where.assignedTo = assignedTo
    }
    
    if (archiveId) {
      where.archiveId = archiveId
    }
    
    if (customerId) {
      where.archive = {
        customerId: customerId
      }
    }

    // 如果没有查看所有工单的权限，只显示分配给当前用户或由当前用户创建的工单
    if (!hasViewAllPermission) {
      where.OR = [
        { assignedTo: currentUser.id },
        { createdBy: currentUser.id }
      ]
    }

    const [services, total] = await Promise.all([
      prisma.service.findMany({
        where,
        skip,
        take: limit,
        include: {
          archive: {
            select: {
              id: true,
              name: true,
              customer: {
                select: {
                  id: true,
                  name: true,
                  company: true,
                  level: true
                }
              }
            }
          },
          assignedUser: {
            select: {
              id: true,
              username: true,
              fullName: true
            }
          },
          createdByUser: {
            select: {
              id: true,
              username: true,
              fullName: true
            }
          },
          slaTemplate: {
            select: {
              id: true,
              name: true,
              responseTime: true,
              resolutionTime: true
            }
          },
          _count: {
            select: {
              workLogs: true,
              attachments: true,
              comments: true
            }
          }
        },
        orderBy: [
          { priority: 'desc' },
          { createdAt: 'desc' }
        ]
      }),
      prisma.service.count({ where })
    ])

    return res.json({
      success: true,
      data: {
        items: services,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      }
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        message: '请求参数验证失败',
        errors: error.errors
      })
    }

    console.error('Get services error:', error)
    return res.status(500).json({
      success: false,
      message: '获取服务工单列表失败'
    })
  }
}

// 获取服务工单详情
export const getService = async (req: Request, res: Response) => {
  try {
    const { id } = req.params

    if (!id) {
      return res.status(400).json({
        success: false,
        message: 'ID参数缺失'
      })
    }

    // 获取当前用户信息
    const authReq = req as any
    const currentUser = authReq.user
    
    if (!currentUser) {
      return res.status(401).json({
        success: false,
        message: '用户未认证'
      })
    }

    const service = await prisma.service.findUnique({
      where: { id },
      include: {
        archive: {
          include: {
            customer: {
              select: {
                id: true,
                name: true,
                company: true,
                level: true,
                contactPerson: true,
                contactEmail: true,
                contactPhone: true
              }
            }
          }
        },
        assignedUser: {
          select: {
            id: true,
            username: true,
            fullName: true,
            email: true
          }
        },
        createdByUser: {
          select: {
            id: true,
            username: true,
            fullName: true
          }
        },
        slaTemplate: true,
        workLogs: {
          include: {
            user: {
              select: {
                id: true,
                username: true,
                fullName: true
              }
            }
          },
          orderBy: { workDate: 'desc' }
        },
        attachments: {
          include: {
            uploader: {
              select: {
                id: true,
                username: true,
                fullName: true
              }
            }
          },
          orderBy: { uploadedAt: 'desc' }
        },
        comments: {
          include: {
            author: {
              select: {
                id: true,
                username: true,
                fullName: true
              }
            }
          },
          orderBy: { createdAt: 'desc' }
        }
      }
    })

    if (!service) {
      return res.status(404).json({
        success: false,
        message: '服务工单不存在'
      })
    }

    // 检查用户权限
    const userPermissions = currentUser.permissions || []
    const hasViewAllPermission = userPermissions.includes('service:view_all') || userPermissions.includes('admin:all')
    
    // 如果没有查看所有工单的权限，检查是否为工单的负责人或创建者
    if (!hasViewAllPermission) {
      const isAssignedTo = service.assignedTo === currentUser.id
      const isCreatedBy = service.createdBy === currentUser.id
      
      if (!isAssignedTo && !isCreatedBy) {
        return res.status(403).json({
          success: false,
          message: '无权限查看此工单'
        })
      }
    }

    // 获取操作历史
    const operationHistory = await getServiceOperationHistory(service.id)

    return res.json({
      success: true,
      data: {
        ...service,
        operationHistory
      }
    })
  } catch (error) {
    console.error('Get service error:', error)
    return res.status(500).json({
      success: false,
      message: '获取服务工单详情失败'
    })
  }
}

// 创建服务工单
export const createService = async (req: Request, res: Response) => {
  try {
    const userId = (req as any).user?.id
    if (!userId) {
      return res.status(401).json({
        success: false,
        message: '用户未认证'
      })
    }

    const data = createServiceSchema.parse(req.body)

    // 检查项目档案是否存在
    const archive = await prisma.projectArchive.findUnique({
      where: { id: data.archiveId },
      include: {
        customer: true
      }
    })

    if (!archive) {
      return res.status(400).json({
        success: false,
        message: '项目档案不存在'
      })
    }

    // 检查SLA模板是否存在
    if (data.slaTemplateId) {
      const slaTemplate = await prisma.slaTemplate.findUnique({
        where: { id: data.slaTemplateId }
      })

      if (!slaTemplate) {
        return res.status(400).json({
          success: false,
          message: 'SLA模板不存在'
        })
      }
    }

    // 生成唯一工单号
    let ticketNumber: string
    let isUnique = false
    let attempts = 0
    
    do {
      ticketNumber = generateTicketNumber()
      const existing = await prisma.service.findUnique({
        where: { ticketNumber }
      })
      isUnique = !existing
      attempts++
    } while (!isUnique && attempts < 10)

    if (!isUnique) {
      return res.status(500).json({
        success: false,
        message: '生成工单号失败，请重试'
      })
    }

    const service = await prisma.service.create({
      data: {
        archiveId: data.archiveId,
        title: data.title,
        description: data.description,
        category: data.category,
        priority: data.priority,
        customerContact: data.customerContact || null,
        slaTemplateId: data.slaTemplateId || null,
        ticketNumber,
        createdBy: userId,
        startTime: new Date()
      },
      include: {
        archive: {
          include: {
            customer: {
              select: {
                id: true,
                name: true,
                company: true,
                level: true
              }
            }
          }
        },
        createdByUser: {
          select: {
            id: true,
            username: true,
            fullName: true
          }
        },
        slaTemplate: true
      }
    })

    // 记录创建历史
    try {
      await recordServiceCreation(service.id, userId)
    } catch (historyError) {
      console.error('记录创建历史失败:', historyError)
      // 不影响主流程
    }

    return res.status(201).json({
      success: true,
      message: '服务工单创建成功',
      data: service
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        message: '请求参数验证失败',
        errors: error.errors
      })
    }

    console.error('Create service error:', error)
    return res.status(500).json({
      success: false,
      message: '服务工单创建失败'
    })
  }
}

// 更新服务工单
export const updateService = async (req: Request, res: Response) => {
  try {
    const { id } = req.params
    console.log('更新服务工单请求:', { id, body: req.body })
    
    const data = updateServiceSchema.parse(req.body)
    console.log('验证后的数据:', data)
    
    const userId = (req as any).user?.id

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: '用户未认证'
      })
    }

    if (!id) {
      return res.status(400).json({
        success: false,
        message: 'ID参数缺失'
      })
    }

    // 检查服务工单是否存在
    const existingService = await prisma.service.findUnique({
      where: { id }
    })

    if (!existingService) {
      return res.status(404).json({
        success: false,
        message: '服务工单不存在'
      })
    }

    // 如果分配给用户，检查用户是否存在
    if (data.assignedTo) {
      const user = await prisma.user.findUnique({
        where: { id: data.assignedTo }
      })

      if (!user) {
        return res.status(400).json({
          success: false,
          message: '分配的用户不存在'
        })
      }
    }

    // 过滤掉不需要更新到数据库的字段
    const { statusNote, operationType, operationNote, transferNote, ...updateData } = data
    
    // 如果状态改为已解决或已关闭，设置结束时间
    if ((data.status === 'RESOLVED' || data.status === 'CLOSED') && !existingService.endTime) {
      (updateData as any).endTime = new Date()
    }

    // 如果状态从 PENDING 变为其他状态，且还没有首次响应时间，则设置首次响应时间
    if (data.status && 
        existingService.status === 'PENDING' && 
        data.status !== 'PENDING' && 
        !existingService.firstResponseAt) {
      (updateData as any).firstResponseAt = new Date()
      // 计算实际响应时间（分钟）
      ;(updateData as any).actualResponseTime = Math.floor((Date.now() - existingService.createdAt.getTime()) / (1000 * 60))
      console.log(`✅ 设置工单 ${existingService.ticketNumber} 首次响应时间（状态变更）`)
    }

    // 如果分配工单给用户，且还没有首次响应时间，也视为首次响应
    if (data.assignedTo && 
        data.assignedTo !== existingService.assignedTo && 
        !existingService.firstResponseAt) {
      (updateData as any).firstResponseAt = new Date()
      // 计算实际响应时间（分钟）
      ;(updateData as any).actualResponseTime = Math.floor((Date.now() - existingService.createdAt.getTime()) / (1000 * 60))
      console.log(`✅ 设置工单 ${existingService.ticketNumber} 首次响应时间（工单分配）`)
    }

    console.log('准备更新数据库:', { id, updateData, existingStatus: existingService.status })

    const service = await prisma.service.update({
      where: { id },
      data: updateData as any,
      include: {
        archive: {
          include: {
            customer: {
              select: {
                id: true,
                name: true,
                company: true,
                level: true
              }
            }
          }
        },
        assignedUser: {
          select: {
            id: true,
            username: true,
            fullName: true
          }
        },
        createdByUser: {
          select: {
            id: true,
            username: true,
            fullName: true
          }
        },
        slaTemplate: true
      }
    })

    console.log('数据库更新完成:', { id, newStatus: service.status, oldStatus: existingService.status })

    // 记录操作历史
    try {
      // 检查状态是否发生变更
      if (data.status && data.status !== existingService.status) {
        await recordStatusChange(id, existingService.status, data.status, userId, (req.body as any).operationNote)
      }
      
      // 检查是否发生转交
      if (data.assignedTo && data.assignedTo !== existingService.assignedTo) {
        await recordTransfer(id, existingService.assignedTo, data.assignedTo, userId, (req.body as any).transferNote)
      }
      
      // 记录其他更新
      if (!data.status && !data.assignedTo) {
        const updateItems = []
        if (data.title) updateItems.push('标题')
        if (data.description) updateItems.push('描述')
        if (data.priority) updateItems.push('优先级')
        if (data.category) updateItems.push('类别')
        if (data.customerContact) updateItems.push('客户联系人')
        if (data.estimatedHours) updateItems.push('预估工时')
        if (data.actualHours) updateItems.push('实际工时')
        if (data.resolution) updateItems.push('解决方案')
        if (data.customerFeedback) updateItems.push('客户反馈')
        if (data.satisfaction) updateItems.push('满意度')
        
        if (updateItems.length > 0) {
          await recordServiceUpdate(id, userId, `更新了${updateItems.join('、')}`)
        }
      }
    } catch (historyError) {
      console.error('记录操作历史失败:', historyError)
      // 不影响主流程
    }

    return res.json({
      success: true,
      message: '服务工单更新成功',
      data: service
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        message: '请求参数验证失败',
        errors: error.errors
      })
    }

    console.error('Update service error:', error)
    return res.status(500).json({
      success: false,
      message: '服务工单更新失败'
    })
  }
}

// 删除服务工单
export const deleteService = async (req: Request, res: Response) => {
  try {
    const { id } = req.params

    if (!id) {
      return res.status(400).json({
        success: false,
        message: 'ID参数缺失'
      })
    }

    // 检查服务工单是否存在
    const service = await prisma.service.findFirst({
      where: { id },
      include: {
        workLogs: true,
        attachments: true,
        comments: true
      }
    })

    if (!service) {
      return res.status(404).json({
        success: false,
        message: '服务工单不存在'
      })
    }

    // 只有待处理状态的工单才能删除
    if (service.status !== 'PENDING') {
      return res.status(400).json({
        success: false,
        message: '只有待处理状态的工单才能删除'
      })
    }

    // 删除相关数据
    await prisma.$transaction([
      prisma.serviceComment.deleteMany({ where: { serviceId: id } }),
      prisma.serviceAttachment.deleteMany({ where: { serviceId: id } }),
      prisma.serviceWorkLog.deleteMany({ where: { serviceId: id } }),
      prisma.service.delete({ where: { id } })
    ])

    return res.json({
      success: true,
      message: '服务工单删除成功'
    })
  } catch (error) {
    console.error('Delete service error:', error)
    return res.status(500).json({
      success: false,
      message: '服务工单删除失败'
    })
  }
}

// 获取服务工单工作日志
export const getServiceWorkLogs = async (req: Request, res: Response) => {
  try {
    const { serviceId } = req.params

    if (!serviceId) {
      return res.status(400).json({
        success: false,
        message: 'serviceId参数缺失'
      })
    }

    // 检查服务工单是否存在
    const service = await prisma.service.findFirst({
      where: { id: serviceId }
    })

    if (!service) {
      return res.status(404).json({
        success: false,
        message: '服务工单不存在'
      })
    }

    const workLogs = await prisma.serviceWorkLog.findMany({
      where: { serviceId },
      include: {
        user: {
          select: {
            id: true,
            username: true,
            fullName: true
          }
        }
      },
      orderBy: { workDate: 'desc' }
    })

    return res.json({
      success: true,
      data: workLogs
    })
  } catch (error) {
    console.error('Get service work logs error:', error)
    return res.status(500).json({
      success: false,
      message: '获取工作日志失败'
    })
  }
}

// 创建服务工单工作日志
export const createServiceWorkLog = async (req: Request, res: Response) => {
  try {
    const { serviceId } = req.params
    const userId = (req as any).user?.id

    if (!serviceId) {
      return res.status(400).json({
        success: false,
        message: 'serviceId参数缺失'
      })
    }

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: '用户未认证'
      })
    }

    const data = createWorkLogSchema.parse(req.body)

    // 检查服务工单是否存在
    const service = await prisma.service.findFirst({
      where: { id: serviceId }
    })

    if (!service) {
      return res.status(404).json({
        success: false,
        message: '服务工单不存在'
      })
    }

    const workLog = await prisma.serviceWorkLog.create({
      data: {
        description: data.description,
        workHours: data.workHours,
        workDate: data.workDate,
        category: data.category,
        serviceId,
        userId
      },
      include: {
        user: {
          select: {
            id: true,
            username: true,
            fullName: true
          }
        }
      }
    })

    // 更新服务工单的实际工时
    const totalHours = await prisma.serviceWorkLog.aggregate({
      where: { serviceId },
      _sum: {
        workHours: true
      }
    })

    // 检查并更新首次响应时间（添加工作日志也视为响应）
    const updateData: any = {
      actualHours: totalHours._sum?.workHours || 0
    }

    if (!service.firstResponseAt) {
      updateData.firstResponseAt = new Date()
      updateData.actualResponseTime = Math.floor((Date.now() - service.createdAt.getTime()) / (1000 * 60))
      console.log(`✅ 设置工单 ${service.ticketNumber} 首次响应时间（添加工作日志）`)
    }

    await prisma.service.update({
      where: { id: serviceId },
      data: updateData
    })

    // 记录工作日志添加历史
    try {
      await recordWorkLogAdded(serviceId, userId, data.workHours)
    } catch (historyError) {
      console.error('记录工作日志历史失败:', historyError)
      // 不影响主流程
    }

    return res.status(201).json({
      success: true,
      message: '工作日志创建成功',
      data: workLog
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        message: '请求参数验证失败',
        errors: error.errors
      })
    }

    console.error('Create service work log error:', error)
    return res.status(500).json({
      success: false,
      message: '工作日志创建失败'
    })
  }
}

// 获取服务工单评论
export const getServiceComments = async (req: Request, res: Response) => {
  try {
    const { serviceId } = req.params
    const { isInternal } = req.query

    if (!serviceId) {
      return res.status(400).json({
        success: false,
        message: 'serviceId参数缺失'
      })
    }

    // 检查服务工单是否存在
    const service = await prisma.service.findFirst({
      where: { id: serviceId }
    })

    if (!service) {
      return res.status(404).json({
        success: false,
        message: '服务工单不存在'
      })
    }

    const where: any = { serviceId }
    
    if (isInternal !== undefined) {
      where.isInternal = isInternal === 'true'
    }

    const comments = await prisma.serviceComment.findMany({
      where,
      include: {
        author: {
          select: {
            id: true,
            username: true,
            fullName: true
          }
        }
      },
      orderBy: { createdAt: 'desc' }
    })

    return res.json({
      success: true,
      data: comments
    })
  } catch (error) {
    console.error('Get service comments error:', error)
    return res.status(500).json({
      success: false,
      message: '获取评论失败'
    })
  }
}

// 创建服务工单评论
export const createServiceComment = async (req: Request, res: Response) => {
  try {
    const { serviceId } = req.params
    const userId = (req as any).user?.id

    if (!serviceId) {
      return res.status(400).json({
        success: false,
        message: 'serviceId参数缺失'
      })
    }

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: '用户未认证'
      })
    }

    const data = createCommentSchema.parse(req.body)

    // 检查服务工单是否存在
    const service = await prisma.service.findFirst({
      where: { id: serviceId }
    })

    if (!service) {
      return res.status(404).json({
        success: false,
        message: '服务工单不存在'
      })
    }

    const comment = await prisma.serviceComment.create({
      data: {
        content: data.content,
        isInternal: data.isInternal,
        serviceId,
        authorId: userId
      },
      include: {
        author: {
          select: {
            id: true,
            username: true,
            fullName: true
          }
        }
      }
    })

    // 检查并更新首次响应时间（仅对非内部评论）
    if (!data.isInternal && !service.firstResponseAt) {
      try {
        await prisma.service.update({
          where: { id: serviceId },
          data: {
            firstResponseAt: new Date(),
            // 计算实际响应时间（分钟）
            actualResponseTime: Math.floor((Date.now() - service.createdAt.getTime()) / (1000 * 60))
          }
        })
        console.log(`✅ 已更新工单 ${service.ticketNumber} 的首次响应时间`)
      } catch (updateError) {
        console.error('更新首次响应时间失败:', updateError)
        // 不影响主流程
      }
    }

    // 记录评论添加历史
    try {
      await recordCommentAdded(serviceId, userId, data.isInternal)
    } catch (historyError) {
      console.error('记录评论历史失败:', historyError)
      // 不影响主流程
    }

    return res.status(201).json({
      success: true,
      message: '评论创建成功',
      data: comment
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        message: '请求参数验证失败',
        errors: error.errors
      })
    }

    console.error('Create service comment error:', error)
    return res.status(500).json({
      success: false,
      message: '评论创建失败'
    })
  }
}

// 获取服务工单统计信息
export const getServiceStats = async (_req: Request, res: Response) => {
  try {
    const [
      totalServices,
      statusStats,
      categoryStats,
      priorityStats,
      recentServices,
      avgResolutionTime
    ] = await Promise.all([
      // 总工单数
      prisma.service.count(),
      
      // 按状态统计
      prisma.service.groupBy({
        by: ['status'],
        _count: {
          status: true
        }
      }),
      
      // 按类别统计
      prisma.service.groupBy({
        by: ['category'],
        _count: {
          category: true
        }
      }),
      
      // 按优先级统计
      prisma.service.groupBy({
        by: ['priority'],
        _count: {
          priority: true
        }
      }),
      
      // 最近7天新增工单
      prisma.service.count({
        where: {
          createdAt: {
            gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
          }
        }
      }),
      
      // 平均解决时间
      prisma.service.aggregate({
        where: {
          status: 'CLOSED',
          startTime: { not: null },
          endTime: { not: null }
        },
        _avg: {
          actualResolutionTime: true
        }
      })
    ])

    return res.json({
      success: true,
      data: {
        totalServices,
        recentServices,
        avgResolutionTime: avgResolutionTime._avg.actualResolutionTime || 0,
        statusDistribution: statusStats.map(stat => ({
          status: stat.status,
          count: stat._count.status
        })),
        categoryDistribution: categoryStats.map(stat => ({
          category: stat.category,
          count: stat._count.category
        })),
        priorityDistribution: priorityStats.map(stat => ({
          priority: stat.priority,
          count: stat._count.priority
        }))
      }
    })
  } catch (error) {
    console.error('Get service stats error:', error)
    return res.status(500).json({
      success: false,
      message: '获取服务工单统计信息失败'
    })
  }
}

// 导出服务工单PDF报告
export const exportServiceReportPDF = async (req: Request, res: Response) => {
  try {
    const { id } = req.params
    
    console.log('🔄 PDF导出请求:', { 
      serviceId: id, 
      authHeader: req.headers.authorization ? 'Present' : 'Missing',
      userAgent: req.get('User-Agent')
    })
    
    if (!id) {
      console.log('❌ 缺少服务ID参数')
      return res.status(400).json({
        success: false,
        message: 'ID参数缺失'
      })
    }

    // 获取当前用户信息
    const authReq = req as any
    const currentUser = authReq.user
    
    console.log('👤 当前用户信息:', { 
      hasUser: !!currentUser, 
      userId: currentUser?.id,
      permissions: currentUser?.permissions?.length || 0 
    })
    
    if (!currentUser) {
      console.log('❌ 用户未认证')
      return res.status(401).json({
        success: false,
        message: '用户未认证'
      })
    }

    // 检查工单是否存在以及权限
    const service = await prisma.service.findUnique({
      where: { id },
      select: {
        id: true,
        ticketNumber: true,
        assignedTo: true,
        createdBy: true
      }
    })

    if (!service) {
      return res.status(404).json({
        success: false,
        message: '服务工单不存在'
      })
    }

    // 检查权限（只有相关人员可以导出）
    const userPermissions = currentUser.permissions || []
    const hasViewAllPermission = userPermissions.includes('service:view_all') || userPermissions.includes('admin:all')
    const isRelatedUser = service.assignedTo === currentUser.id || service.createdBy === currentUser.id

    if (!hasViewAllPermission && !isRelatedUser) {
      return res.status(403).json({
        success: false,
        message: '无权限导出此工单报告'
      })
    }

    console.log(`🔄 开始生成工单 ${service.ticketNumber} 的PDF报告`)

    // 生成PDF报告
    const reportService = ServiceReportService.getInstance()
    const pdfBuffer = await reportService.generateServiceReport(id)

    // 设置响应头
    const filename = `service-report-${service.ticketNumber}-${new Date().toISOString().split('T')[0]}.pdf`
    
    res.setHeader('Content-Type', 'application/pdf')
    res.setHeader('Content-Disposition', `attachment; filename="${encodeURIComponent(filename)}"`)
    res.setHeader('Content-Length', pdfBuffer.length)

    // 记录导出操作日志
    try {
      const logData: any = {
        userId: currentUser.id,
        action: 'EXPORT',
        resource: 'SERVICE_REPORT',
        resourceId: id,
        details: {
          ticketNumber: service.ticketNumber,
          format: 'PDF',
          filename
        }
      }
      
      if (req.ip) logData.ipAddress = req.ip
      if (req.get('User-Agent')) logData.userAgent = req.get('User-Agent')
      
      await prisma.operationLog.create({
        data: logData
      })
    } catch (logError) {
      console.error('记录导出日志失败:', logError)
      // 不影响主流程
    }

    console.log(`✅ 工单 ${service.ticketNumber} PDF报告生成完成`)

    // 发送PDF文件
    res.send(pdfBuffer)

  } catch (error) {
    console.error('Export service report PDF error:', error)
    return res.status(500).json({
      success: false,
      message: '导出PDF报告失败',
      error: process.env['NODE_ENV'] === 'development' ? (error as Error).message : undefined
    })
  }
}

// 异步导出服务工单PDF报告（队列/后台生成，完成后推送通知）
export const exportServiceReportPDFAsync = async (req: Request, res: Response) => {
  try {
    const { id } = req.params

    if (!id) {
      return res.status(400).json({ success: false, message: 'ID参数缺失' })
    }

    const authReq = req as any
    const currentUser = authReq.user
    if (!currentUser) {
      return res.status(401).json({ success: false, message: '用户未认证' })
    }

    // 校验工单存在
    const service = await prisma.service.findUnique({
      where: { id },
      select: { id: true, ticketNumber: true }
    })
    if (!service) {
      return res.status(404).json({ success: false, message: '服务工单不存在' })
    }

    const jobId = uuidv4()
    const jobKey = `report_export:${jobId}`
    const createdAt = new Date().toISOString()

    const uploadsDir = path.join(process.cwd(), 'uploads', 'reports')
    await fs.mkdir(uploadsDir, { recursive: true })
    const filename = `service-report-${service.ticketNumber}-${createdAt.substring(0,10)}.pdf`
    const filePath = path.join(uploadsDir, filename)

    // 记录初始状态
    const initialJob = {
      jobId,
      userId: currentUser.id,
      serviceId: id,
      ticketNumber: service.ticketNumber,
      status: 'PENDING',
      createdAt,
      filename,
      filePath,
      error: undefined as string | undefined
    }
    await CacheService.set(jobKey, JSON.stringify(initialJob), 24 * 60 * 60) // 24h

    // 异步生成
    ;(async () => {
      const reportService = ServiceReportService.getInstance()
      const realtime = RealtimeService.getInstance()
      try {
        await CacheService.set(jobKey, JSON.stringify({ ...initialJob, status: 'PROCESSING' }), 24 * 60 * 60)
        const pdfBuffer = await reportService.generateServiceReport(id)
        await fs.writeFile(filePath, pdfBuffer)
        await CacheService.set(jobKey, JSON.stringify({ ...initialJob, status: 'DONE' }), 24 * 60 * 60)

        // 推送完成通知（带下载链接）
        const actionUrl = `/api/v1/services/export/pdf/jobs/${jobId}/download`
        await realtime.pushNotification({
          id: jobId,
          type: 'success',
          title: '工单报告已生成',
          message: `工单 ${service.ticketNumber} 的PDF报告已准备就绪，点击下载。`,
          targetUserId: currentUser.id,
          actionUrl,
          persistent: true
        })
      } catch (err: any) {
        console.error('异步生成报告失败:', err)
        await CacheService.set(jobKey, JSON.stringify({ ...initialJob, status: 'FAILED', error: err?.message || '生成失败' }), 24 * 60 * 60)
        await RealtimeService.getInstance().pushNotification({
          id: jobId,
          type: 'error',
          title: '工单报告生成失败',
          message: `工单 ${service.ticketNumber} 的报告生成失败：${err?.message || '未知错误'}`,
          targetUserId: currentUser.id,
          persistent: true
        })
      }
    })()

    return res.json({ success: true, message: '已开始生成报告，完成后将通知您下载', data: { jobId } })
  } catch (error) {
    console.error('Export async error:', error)
    return res.status(500).json({ success: false, message: '启动异步导出失败' })
  }
}

// 查询异步导出任务状态
export const getExportJobStatus = async (req: Request, res: Response) => {
  try {
    const { jobId } = req.params
    const jobKey = `report_export:${jobId}`
    const raw = await CacheService.get<string>(jobKey)
    if (!raw) {
      return res.status(404).json({ success: false, message: '任务不存在或已过期' })
    }
    return res.json({ success: true, data: JSON.parse(raw) })
  } catch (error) {
    return res.status(500).json({ success: false, message: '查询任务失败' })
  }
}

// 下载异步导出结果
export const downloadExportJob = async (req: Request, res: Response) => {
  try {
    const { jobId } = req.params
    const jobKey = `report_export:${jobId}`
    const raw = await CacheService.get<string>(jobKey)
    if (!raw) {
      return res.status(404).json({ success: false, message: '任务不存在或已过期' })
    }
    const job = JSON.parse(raw)
    if (job.status !== 'DONE') {
      return res.status(400).json({ success: false, message: '报告尚未生成完成' })
    }
    const stat = await fs.stat(job.filePath).catch(() => null as any)
    if (!stat) {
      return res.status(404).json({ success: false, message: '文件不存在' })
    }
    res.setHeader('Content-Type', 'application/pdf')
    res.setHeader('Content-Disposition', `attachment; filename="${encodeURIComponent(job.filename)}"`)
    res.sendFile(job.filePath)
  } catch (error) {
    console.error('下载导出文件失败:', error)
    return res.status(500).json({ success: false, message: '下载失败' })
  }
}