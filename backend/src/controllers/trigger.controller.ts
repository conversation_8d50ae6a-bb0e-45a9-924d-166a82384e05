import { Request, Response } from 'express'
import { triggerManager } from '@/services/trigger-manager.service'
import { eventTrigger } from '@/services/triggers/event-trigger.service'
import { cronTrigger } from '@/services/triggers/cron-trigger.service'
import { conditionalTrigger } from '@/services/triggers/conditional-trigger.service'
import { webhookTrigger } from '@/services/triggers/webhook-trigger.service'
import { z } from 'zod'

// 验证schemas
const TriggerEventSchema = z.object({
  eventType: z.string().min(1, '事件类型不能为空'),
  data: z.any(),
  source: z.string().optional().default('manual')
})

const CronJobSchema = z.object({
  jobId: z.string().min(1, '任务ID不能为空'),
  schedule: z.string().min(1, 'Cron表达式不能为空'),
  description: z.string().optional(),
  workflowId: z.string().optional(),
  enabled: z.boolean().optional().default(true),
  timezone: z.string().optional(),
  maxExecutions: z.number().optional(),
  rateLimit: z.number().optional()
})

const ConditionalTriggerSchema = z.object({
  triggerId: z.string().min(1, '触发器ID不能为空'),
  conditions: z.array(z.object({
    field: z.string().min(1),
    operator: z.enum(['eq', 'ne', 'gt', 'gte', 'lt', 'lte', 'contains', 'starts_with', 'ends_with', 'matches', 'exists', 'in', 'not_in']),
    value: z.any(),
    source: z.enum(['database', 'api', 'metric', 'cache', 'variable']),
    query: z.string().optional(),
    description: z.string().optional()
  })).min(1, '至少需要一个条件'),
  operator: z.enum(['AND', 'OR']).default('AND'),
  checkInterval: z.number().min(1).default(300),
  cooldownPeriod: z.number().optional(),
  description: z.string().optional(),
  workflowId: z.string().optional(),
  enabled: z.boolean().optional().default(true)
})

const WebhookTriggerSchema = z.object({
  webhookId: z.string().min(1, 'Webhook ID不能为空'),
  endpoint: z.string().min(1, '端点路径不能为空').startsWith('/', '端点路径必须以/开头'),
  method: z.enum(['GET', 'POST', 'PUT', 'DELETE', 'PATCH']).default('POST'),
  authentication: z.object({
    type: z.enum(['token', 'signature', 'basic']),
    token: z.string().optional(),
    secret: z.string().optional(),
    key: z.string().optional(),
    username: z.string().optional(),
    password: z.string().optional()
  }).optional(),
  filters: z.object({
    headers: z.record(z.string()).optional(),
    body: z.record(z.any()).optional(),
    eventTypes: z.array(z.string()).optional()
  }).optional(),
  description: z.string().optional(),
  workflowId: z.string().optional(),
  enabled: z.boolean().optional().default(true),
  rateLimit: z.number().optional().default(100)
})

/**
 * 触发器管理控制器
 */
export class TriggerController {
  /**
   * 获取触发器系统统计信息
   */
  static async getSystemStats(req: Request, res: Response) {
    try {
      const stats = await triggerManager.getTriggerServiceStats()
      
      res.json({
        success: true,
        data: stats
      })
    } catch (error: any) {
      console.error('获取触发器统计信息失败:', error)
      res.status(500).json({
        success: false,
        message: error.message || '获取统计信息失败'
      })
    }
  }

  /**
   * 获取所有触发器列表
   */
  static async getAllTriggers(req: Request, res: Response) {
    try {
      const allTriggers = {
        eventTriggers: eventTrigger.getSupportedEventTypes(),
        cronTriggers: cronTrigger.getAllJobsInfo(),
        conditionalTriggers: conditionalTrigger.getAllTriggers(),
        webhookTriggers: webhookTrigger.getAllWebhooks()
      }

      res.json({
        success: true,
        data: allTriggers
      })
    } catch (error: any) {
      console.error('获取触发器列表失败:', error)
      res.status(500).json({
        success: false,
        message: error.message || '获取触发器列表失败'
      })
    }
  }

  // ========== 事件触发器相关 ==========

  /**
   * 手动触发事件
   */
  static async triggerEvent(req: Request, res: Response) {
    try {
      const validatedData = TriggerEventSchema.parse(req.body)
      const userId = req.user?.id || 'system'

      await triggerManager.triggerEvent(
        validatedData.eventType,
        {
          ...validatedData.data,
          triggeredBy: userId,
          triggeredAt: new Date()
        },
        validatedData.source
      )

      res.json({
        success: true,
        message: '事件触发成功'
      })
    } catch (error: any) {
      console.error('手动触发事件失败:', error)
      res.status(400).json({
        success: false,
        message: error.message || '事件触发失败'
      })
    }
  }

  /**
   * 获取事件统计
   */
  static async getEventStatistics(req: Request, res: Response) {
    try {
      const { timeRange } = req.query
      
      let dateRange
      if (timeRange) {
        const [startStr, endStr] = (timeRange as string).split(',')
        dateRange = {
          start: new Date(startStr),
          end: new Date(endStr)
        }
      }

      const stats = eventTrigger.getEventStatistics(dateRange)
      
      res.json({
        success: true,
        data: stats
      })
    } catch (error: any) {
      console.error('获取事件统计失败:', error)
      res.status(500).json({
        success: false,
        message: error.message || '获取事件统计失败'
      })
    }
  }

  /**
   * 获取最近事件
   */
  static async getRecentEvents(req: Request, res: Response) {
    try {
      const { eventType, limit = '50' } = req.query
      
      const events = eventTrigger.getRecentEvents(
        eventType as string, 
        parseInt(limit as string)
      )
      
      res.json({
        success: true,
        data: events
      })
    } catch (error: any) {
      console.error('获取最近事件失败:', error)
      res.status(500).json({
        success: false,
        message: error.message || '获取最近事件失败'
      })
    }
  }

  // ========== 定时触发器相关 ==========

  /**
   * 创建定时任务
   */
  static async createCronJob(req: Request, res: Response) {
    try {
      const validatedData = CronJobSchema.parse(req.body)
      const userId = req.user?.id || 'system'

      await cronTrigger.scheduleJob(validatedData.jobId, {
        schedule: validatedData.schedule,
        description: validatedData.description,
        workflowId: validatedData.workflowId,
        enabled: validatedData.enabled,
        timezone: validatedData.timezone,
        maxExecutions: validatedData.maxExecutions,
        rateLimit: validatedData.rateLimit
      })

      res.status(201).json({
        success: true,
        message: '定时任务创建成功'
      })
    } catch (error: any) {
      console.error('创建定时任务失败:', error)
      res.status(400).json({
        success: false,
        message: error.message || '创建定时任务失败'
      })
    }
  }

  /**
   * 获取定时任务信息
   */
  static async getCronJobInfo(req: Request, res: Response) {
    try {
      const { jobId } = req.params
      const jobInfo = cronTrigger.getJobInfo(jobId)
      
      if (!jobInfo) {
        return res.status(404).json({
          success: false,
          message: '定时任务不存在'
        })
      }

      res.json({
        success: true,
        data: jobInfo
      })
    } catch (error: any) {
      console.error('获取定时任务信息失败:', error)
      res.status(500).json({
        success: false,
        message: error.message || '获取任务信息失败'
      })
    }
  }

  /**
   * 手动执行定时任务
   */
  static async runCronJobNow(req: Request, res: Response) {
    try {
      const { jobId } = req.params
      const execution = await triggerManager.runCronJobNow(jobId)

      res.json({
        success: true,
        data: execution,
        message: '任务执行完成'
      })
    } catch (error: any) {
      console.error('手动执行定时任务失败:', error)
      res.status(400).json({
        success: false,
        message: error.message || '任务执行失败'
      })
    }
  }

  /**
   * 启动/停止定时任务
   */
  static async controlCronJob(req: Request, res: Response) {
    try {
      const { jobId } = req.params
      const { action } = req.body // 'start' | 'stop'

      if (action === 'start') {
        await cronTrigger.startJob(jobId)
      } else if (action === 'stop') {
        await cronTrigger.stopJob(jobId)
      } else {
        return res.status(400).json({
          success: false,
          message: '无效的操作，只支持start和stop'
        })
      }

      res.json({
        success: true,
        message: `定时任务已${action === 'start' ? '启动' : '停止'}`
      })
    } catch (error: any) {
      console.error('控制定时任务失败:', error)
      res.status(400).json({
        success: false,
        message: error.message || '任务控制失败'
      })
    }
  }

  /**
   * 获取定时任务执行历史
   */
  static async getCronJobHistory(req: Request, res: Response) {
    try {
      const { jobId } = req.params
      const { limit = '20' } = req.query
      
      const history = cronTrigger.getExecutionHistory(jobId, parseInt(limit as string))
      
      res.json({
        success: true,
        data: history
      })
    } catch (error: any) {
      console.error('获取定时任务历史失败:', error)
      res.status(500).json({
        success: false,
        message: error.message || '获取执行历史失败'
      })
    }
  }

  // ========== 条件触发器相关 ==========

  /**
   * 创建条件触发器
   */
  static async createConditionalTrigger(req: Request, res: Response) {
    try {
      const validatedData = ConditionalTriggerSchema.parse(req.body)
      const userId = req.user?.id || 'system'

      await conditionalTrigger.createConditionalTrigger(validatedData.triggerId, {
        conditions: validatedData.conditions,
        operator: validatedData.operator,
        checkInterval: validatedData.checkInterval,
        cooldownPeriod: validatedData.cooldownPeriod,
        description: validatedData.description,
        workflowId: validatedData.workflowId,
        enabled: validatedData.enabled
      })

      res.status(201).json({
        success: true,
        message: '条件触发器创建成功'
      })
    } catch (error: any) {
      console.error('创建条件触发器失败:', error)
      res.status(400).json({
        success: false,
        message: error.message || '创建条件触发器失败'
      })
    }
  }

  /**
   * 获取条件触发器信息
   */
  static async getConditionalTriggerInfo(req: Request, res: Response) {
    try {
      const { triggerId } = req.params
      const triggerInfo = conditionalTrigger.getTriggerInfo(triggerId)
      
      if (!triggerInfo) {
        return res.status(404).json({
          success: false,
          message: '条件触发器不存在'
        })
      }

      res.json({
        success: true,
        data: triggerInfo
      })
    } catch (error: any) {
      console.error('获取条件触发器信息失败:', error)
      res.status(500).json({
        success: false,
        message: error.message || '获取触发器信息失败'
      })
    }
  }

  /**
   * 强制评估条件触发器
   */
  static async forceEvaluateCondition(req: Request, res: Response) {
    try {
      const { triggerId } = req.params
      const evaluation = await triggerManager.forceConditionEvaluation(triggerId)

      if (!evaluation) {
        return res.status(404).json({
          success: false,
          message: '条件触发器不存在'
        })
      }

      res.json({
        success: true,
        data: evaluation,
        message: '条件评估完成'
      })
    } catch (error: any) {
      console.error('强制评估条件失败:', error)
      res.status(400).json({
        success: false,
        message: error.message || '条件评估失败'
      })
    }
  }

  /**
   * 获取条件触发器评估历史
   */
  static async getConditionEvaluationHistory(req: Request, res: Response) {
    try {
      const { triggerId } = req.params
      const { limit = '20' } = req.query
      
      const history = conditionalTrigger.getEvaluationHistory(triggerId, parseInt(limit as string))
      
      res.json({
        success: true,
        data: history
      })
    } catch (error: any) {
      console.error('获取条件评估历史失败:', error)
      res.status(500).json({
        success: false,
        message: error.message || '获取评估历史失败'
      })
    }
  }

  // ========== Webhook触发器相关 ==========

  /**
   * 注册Webhook触发器
   */
  static async registerWebhook(req: Request, res: Response) {
    try {
      const validatedData = WebhookTriggerSchema.parse(req.body)
      const userId = req.user?.id || 'system'

      await webhookTrigger.registerWebhook(validatedData.webhookId, {
        endpoint: validatedData.endpoint,
        method: validatedData.method,
        authentication: validatedData.authentication,
        filters: validatedData.filters,
        description: validatedData.description,
        workflowId: validatedData.workflowId,
        enabled: validatedData.enabled,
        rateLimit: validatedData.rateLimit
      })

      res.status(201).json({
        success: true,
        message: 'Webhook触发器注册成功'
      })
    } catch (error: any) {
      console.error('注册Webhook触发器失败:', error)
      res.status(400).json({
        success: false,
        message: error.message || 'Webhook注册失败'
      })
    }
  }

  /**
   * 获取Webhook触发器信息
   */
  static async getWebhookInfo(req: Request, res: Response) {
    try {
      const { webhookId } = req.params
      const webhookInfo = webhookTrigger.getWebhookInfo(webhookId)
      
      if (!webhookInfo) {
        return res.status(404).json({
          success: false,
          message: 'Webhook触发器不存在'
        })
      }

      res.json({
        success: true,
        data: webhookInfo
      })
    } catch (error: any) {
      console.error('获取Webhook信息失败:', error)
      res.status(500).json({
        success: false,
        message: error.message || '获取Webhook信息失败'
      })
    }
  }

  /**
   * 获取Webhook请求历史
   */
  static async getWebhookRequestHistory(req: Request, res: Response) {
    try {
      const { webhookId } = req.params
      const { limit = '20' } = req.query
      
      const history = webhookTrigger.getRequestHistory(webhookId, parseInt(limit as string))
      
      res.json({
        success: true,
        data: history
      })
    } catch (error: any) {
      console.error('获取Webhook请求历史失败:', error)
      res.status(500).json({
        success: false,
        message: error.message || '获取请求历史失败'
      })
    }
  }

  /**
   * 处理Webhook请求（通用端点）
   */
  static async handleWebhookRequest(req: Request, res: Response) {
    try {
      const endpoint = req.path
      const method = req.method
      const headers = req.headers as Record<string, string>
      const body = req.body
      const clientIP = req.ip || req.socket.remoteAddress || 'unknown'

      console.log(`🔗 处理Webhook请求: ${method} ${endpoint}`)

      const result = await triggerManager.handleWebhookRequest(
        endpoint,
        method,
        headers,
        body,
        clientIP
      )

      if (result.success) {
        res.status(200).json(result)
      } else {
        res.status(result.status || 400).json(result)
      }
    } catch (error: any) {
      console.error('处理Webhook请求失败:', error)
      res.status(500).json({
        success: false,
        message: error.message || 'Webhook处理失败',
        timestamp: new Date()
      })
    }
  }

  // ========== 通用触发器操作 ==========

  /**
   * 启用/禁用触发器
   */
  static async controlTrigger(req: Request, res: Response) {
    try {
      const { triggerId } = req.params
      const { action, type } = req.body // action: 'enable' | 'disable', type: WorkflowTriggerType

      if (!type) {
        return res.status(400).json({
          success: false,
          message: '必须指定触发器类型'
        })
      }

      if (action === 'enable') {
        await triggerManager.enableTrigger(triggerId, type)
      } else if (action === 'disable') {
        await triggerManager.disableTrigger(triggerId, type)
      } else {
        return res.status(400).json({
          success: false,
          message: '无效的操作，只支持enable和disable'
        })
      }

      res.json({
        success: true,
        message: `触发器已${action === 'enable' ? '启用' : '禁用'}`
      })
    } catch (error: any) {
      console.error('控制触发器失败:', error)
      res.status(400).json({
        success: false,
        message: error.message || '触发器控制失败'
      })
    }
  }

  /**
   * 删除触发器
   */
  static async deleteTrigger(req: Request, res: Response) {
    try {
      const { triggerId } = req.params
      const { type } = req.body // WorkflowTriggerType

      if (!type) {
        return res.status(400).json({
          success: false,
          message: '必须指定触发器类型'
        })
      }

      switch (type) {
        case 'SCHEDULED':
          await cronTrigger.unscheduleJob(triggerId)
          break
        case 'CONDITION':
          await conditionalTrigger.removeTrigger(triggerId)
          break
        case 'WEBHOOK':
          await webhookTrigger.removeWebhook(triggerId)
          break
        default:
          return res.status(400).json({
            success: false,
            message: '不支持的触发器类型'
          })
      }

      res.json({
        success: true,
        message: '触发器删除成功'
      })
    } catch (error: any) {
      console.error('删除触发器失败:', error)
      res.status(400).json({
        success: false,
        message: error.message || '触发器删除失败'
      })
    }
  }

  /**
   * 验证Cron表达式
   */
  static async validateCronExpression(req: Request, res: Response) {
    try {
      const { expression } = req.body
      
      if (!expression) {
        return res.status(400).json({
          success: false,
          message: 'Cron表达式不能为空'
        })
      }

      const isValid = cronTrigger.constructor.validateCronExpression(expression)
      const description = cronTrigger.constructor.parseCronExpression(expression)

      res.json({
        success: true,
        data: {
          valid: isValid,
          description,
          expression
        }
      })
    } catch (error: any) {
      console.error('验证Cron表达式失败:', error)
      res.status(400).json({
        success: false,
        message: error.message || 'Cron表达式验证失败'
      })
    }
  }
}