/**
 * 权限模板管理控制器
 * 
 * 提供权限模板的CRUD操作、应用到角色、导入导出等功能
 */

import { Request, Response } from 'express'
import { z } from 'zod'
import { prisma } from '@/config/database.config'
import { AuthenticatedRequest } from '@/middleware/auth.middleware'
import { 
  validatePermissions,
  filterValidPermissions,
  DEFAULT_ROLE_PERMISSIONS,
  getAllPermissions,
  getPermissionDescription,
  ROLE_PERMISSIONS,
  type Permission,
  type RoleName
} from '@/constants/permissions'

// ==================== 验证Schema ====================

const createTemplateSchema = z.object({
  name: z.string().min(1, '模板名称不能为空').max(100, '模板名称不能超过100个字符'),
  description: z.string().optional(),
  permissions: z.array(z.string()).min(1, '权限列表不能为空'),
  category: z.enum(['SYSTEM', 'BUSINESS', 'SERVICE', 'READONLY', 'CUSTOM']).default('CUSTOM'),
  version: z.string().default('1.0'),
  metadata: z.record(z.any()).optional()
})

const updateTemplateSchema = z.object({
  name: z.string().min(1, '模板名称不能为空').max(100, '模板名称不能超过100个字符').optional(),
  description: z.string().optional(),
  permissions: z.array(z.string()).min(1, '权限列表不能为空').optional(),
  category: z.enum(['SYSTEM', 'BUSINESS', 'SERVICE', 'READONLY', 'CUSTOM']).optional(),
  version: z.string().optional(),
  metadata: z.record(z.any()).optional()
})

const applyTemplateSchema = z.object({
  roleIds: z.array(z.string()).min(1, '请选择至少一个角色'),
  note: z.string().optional()
})

const batchApplyTemplateSchema = z.object({
  templateId: z.string().min(1, '模板ID不能为空'),
  applications: z.array(z.object({
    roleId: z.string().min(1, '角色ID不能为空'),
    note: z.string().optional()
  })).min(1, '至少需要应用到一个角色')
})

const compareTemplateSchema = z.object({
  templateIds: z.array(z.string()).min(2, '至少需要选择两个模板进行比较').max(5, '最多支持比较5个模板')
})

const exportTemplateSchema = z.object({
  templateIds: z.array(z.string()).min(1, '请选择要导出的模板'),
  format: z.enum(['json', 'csv']).default('json')
})

const importTemplateSchema = z.object({
  templates: z.array(z.object({
    name: z.string().min(1, '模板名称不能为空'),
    description: z.string().optional(),
    permissions: z.array(z.string()).min(1, '权限列表不能为空'),
    category: z.enum(['SYSTEM', 'BUSINESS', 'SERVICE', 'READONLY', 'CUSTOM']).default('CUSTOM'),
    version: z.string().default('1.0'),
    metadata: z.record(z.any()).optional()
  })).min(1, '至少需要导入一个模板'),
  overwrite: z.boolean().default(false) // 是否覆盖同名模板
})

// ==================== 模板CRUD操作 ====================

/**
 * 获取权限模板列表
 * @route GET /api/v1/permission-templates
 */
export const getPermissionTemplates = async (req: Request, res: Response) => {
  try {
    const { 
      page = 1, 
      pageSize = 20, 
      category, 
      search, 
      isDefault,
      isSystem 
    } = req.query

    const skip = (Number(page) - 1) * Number(pageSize)
    const take = Number(pageSize)

    // 构建查询条件
    const where: any = {}
    
    if (category) {
      where.category = category as string
    }
    
    if (search) {
      where.OR = [
        { name: { contains: search as string } },
        { description: { contains: search as string } }
      ]
    }
    
    if (isDefault !== undefined) {
      where.isDefault = isDefault === 'true'
    }
    
    if (isSystem !== undefined) {
      where.isSystem = isSystem === 'true'
    }

    // 获取模板列表
    const [templates, total] = await Promise.all([
      prisma.permissionTemplate.findMany({
        where,
        skip,
        take,
        orderBy: { updatedAt: 'desc' },
        include: {
          createdByUser: {
            select: {
              id: true,
              username: true,
              fullName: true
            }
          },
          updatedByUser: {
            select: {
              id: true,
              username: true,
              fullName: true
            }
          },
          templateUsage: {
            where: { status: 'ACTIVE' },
            include: {
              role: {
                select: {
                  id: true,
                  name: true,
                  description: true
                }
              }
            }
          },
          _count: {
            select: {
              templateUsage: {
                where: { status: 'ACTIVE' }
              }
            }
          }
        }
      }),
      prisma.permissionTemplate.count({ where })
    ])

    // 处理模板数据
    const templatesData = templates.map(template => {
      const permissions = Array.isArray(template.permissions) 
        ? template.permissions 
        : JSON.parse(template.permissions as string)

      return {
        id: template.id,
        name: template.name,
        description: template.description,
        permissions,
        permissionCount: permissions.length,
        category: template.category,
        isDefault: template.isDefault,
        isSystem: template.isSystem,
        version: template.version,
        metadata: template.metadata,
        usageCount: template._count.templateUsage,
        usedByRoles: template.templateUsage.map(usage => ({
          roleId: usage.role.id,
          roleName: usage.role.name,
          roleDescription: usage.role.description,
          appliedAt: usage.appliedAt
        })),
        createdBy: template.createdByUser,
        updatedBy: template.updatedByUser,
        createdAt: template.createdAt,
        updatedAt: template.updatedAt
      }
    })

    res.json({
      success: true,
      data: {
        templates: templatesData,
        pagination: {
          page: Number(page),
          pageSize: take,
          total,
          totalPages: Math.ceil(total / take)
        }
      }
    })
  } catch (error: any) {
    console.error('获取权限模板列表失败:', error)
    res.status(500).json({
      success: false,
      message: '获取权限模板列表失败',
      error: error.message
    })
  }
}

/**
 * 获取权限模板详情
 * @route GET /api/v1/permission-templates/:id
 */
export const getPermissionTemplateById = async (req: Request, res: Response) => {
  try {
    const { id } = req.params

    const template = await prisma.permissionTemplate.findUnique({
      where: { id },
      include: {
        createdByUser: {
          select: {
            id: true,
            username: true,
            fullName: true
          }
        },
        updatedByUser: {
          select: {
            id: true,
            username: true,
            fullName: true
          }
        },
        templateUsage: {
          include: {
            role: {
              select: {
                id: true,
                name: true,
                description: true
              }
            },
            appliedByUser: {
              select: {
                id: true,
                username: true,
                fullName: true
              }
            }
          },
          orderBy: { appliedAt: 'desc' }
        },
        templateHistory: {
          include: {
            changedByUser: {
              select: {
                id: true,
                username: true,
                fullName: true
              }
            }
          },
          orderBy: { createdAt: 'desc' },
          take: 10 // 最近10条历史记录
        }
      }
    })

    if (!template) {
      return res.status(404).json({
        success: false,
        message: '权限模板不存在'
      })
    }

    const permissions = Array.isArray(template.permissions) 
      ? template.permissions 
      : JSON.parse(template.permissions as string)

    const templateData = {
      id: template.id,
      name: template.name,
      description: template.description,
      permissions,
      permissionCount: permissions.length,
      permissionDetails: permissions.map(permission => ({
        key: permission,
        name: getPermissionDescription(permission)
      })),
      category: template.category,
      isDefault: template.isDefault,
      isSystem: template.isSystem,
      version: template.version,
      metadata: template.metadata,
      usageHistory: template.templateUsage.map(usage => ({
        id: usage.id,
        roleId: usage.role.id,
        roleName: usage.role.name,
        roleDescription: usage.role.description,
        appliedBy: usage.appliedByUser,
        appliedAt: usage.appliedAt,
        status: usage.status,
        note: usage.note
      })),
      changeHistory: template.templateHistory.map(history => ({
        id: history.id,
        action: history.action,
        oldData: history.oldData,
        newData: history.newData,
        changeReason: history.changeReason,
        changedBy: history.changedByUser,
        createdAt: history.createdAt
      })),
      createdBy: template.createdByUser,
      updatedBy: template.updatedByUser,
      createdAt: template.createdAt,
      updatedAt: template.updatedAt
    }

    res.json({
      success: true,
      data: templateData
    })
  } catch (error: any) {
    console.error('获取权限模板详情失败:', error)
    res.status(500).json({
      success: false,
      message: '获取权限模板详情失败',
      error: error.message
    })
  }
}

/**
 * 创建权限模板
 * @route POST /api/v1/permission-templates
 */
export const createPermissionTemplate = async (req: Request, res: Response) => {
  try {
    const validatedData = createTemplateSchema.parse(req.body)
    const authReq = req as AuthenticatedRequest

    // 验证权限格式
    const validation = validatePermissions(validatedData.permissions)
    if (!validation.valid) {
      return res.status(400).json({
        success: false,
        message: '权限格式无效',
        data: {
          invalidPermissions: validation.invalidPermissions,
          unknownPermissions: validation.unknownPermissions
        }
      })
    }

    // 检查模板名称是否已存在
    const existingTemplate = await prisma.permissionTemplate.findUnique({
      where: { name: validatedData.name }
    })

    if (existingTemplate) {
      return res.status(400).json({
        success: false,
        message: '模板名称已存在'
      })
    }

    // 过滤有效权限
    const validPermissions = filterValidPermissions(validatedData.permissions)

    // 创建模板
    const template = await prisma.permissionTemplate.create({
      data: {
        name: validatedData.name,
        description: validatedData.description,
        permissions: JSON.stringify(validPermissions),
        category: validatedData.category,
        version: validatedData.version,
        metadata: validatedData.metadata,
        createdBy: authReq.user!.id
      },
      include: {
        createdByUser: {
          select: {
            id: true,
            username: true,
            fullName: true
          }
        }
      }
    })

    // 记录操作历史
    await prisma.permissionTemplateHistory.create({
      data: {
        templateId: template.id,
        action: 'CREATE',
        newData: {
          name: template.name,
          permissions: validPermissions,
          category: template.category
        },
        changeReason: '创建新模板',
        changedBy: authReq.user!.id
      }
    })

    // 记录操作日志
    await prisma.operationLog.create({
      data: {
        userId: authReq.user!.id,
        action: 'CREATE_PERMISSION_TEMPLATE',
        resource: 'permission_template',
        resourceId: template.id,
        details: {
          templateName: template.name,
          permissions: validPermissions,
          category: template.category
        }
      }
    })

    res.status(201).json({
      success: true,
      message: '权限模板创建成功',
      data: {
        id: template.id,
        name: template.name,
        description: template.description,
        permissions: validPermissions,
        permissionCount: validPermissions.length,
        category: template.category,
        version: template.version,
        createdBy: template.createdByUser,
        createdAt: template.createdAt
      }
    })
  } catch (error: any) {
    console.error('创建权限模板失败:', error)
    res.status(500).json({
      success: false,
      message: '创建权限模板失败',
      error: error.message
    })
  }
}

/**
 * 更新权限模板
 * @route PUT /api/v1/permission-templates/:id
 */
export const updatePermissionTemplate = async (req: Request, res: Response) => {
  try {
    const { id } = req.params
    const validatedData = updateTemplateSchema.parse(req.body)
    const authReq = req as AuthenticatedRequest

    // 获取原模板数据
    const existingTemplate = await prisma.permissionTemplate.findUnique({
      where: { id }
    })

    if (!existingTemplate) {
      return res.status(404).json({
        success: false,
        message: '权限模板不存在'
      })
    }

    // 检查是否为系统模板
    if (existingTemplate.isSystem) {
      return res.status(403).json({
        success: false,
        message: '系统模板不允许修改'
      })
    }

    // 如果要更新名称，检查新名称是否已存在
    if (validatedData.name && validatedData.name !== existingTemplate.name) {
      const nameExists = await prisma.permissionTemplate.findUnique({
        where: { name: validatedData.name }
      })

      if (nameExists) {
        return res.status(400).json({
          success: false,
          message: '模板名称已存在'
        })
      }
    }

    // 如果要更新权限，验证权限格式
    let validPermissions: string[] | undefined
    if (validatedData.permissions) {
      const validation = validatePermissions(validatedData.permissions)
      if (!validation.valid) {
        return res.status(400).json({
          success: false,
          message: '权限格式无效',
          data: {
            invalidPermissions: validation.invalidPermissions,
            unknownPermissions: validation.unknownPermissions
          }
        })
      }
      validPermissions = filterValidPermissions(validatedData.permissions)
    }

    // 准备更新数据
    const updateData: any = {
      updatedBy: authReq.user!.id
    }

    if (validatedData.name) updateData.name = validatedData.name
    if (validatedData.description !== undefined) updateData.description = validatedData.description
    if (validPermissions) updateData.permissions = JSON.stringify(validPermissions)
    if (validatedData.category) updateData.category = validatedData.category
    if (validatedData.version) updateData.version = validatedData.version
    if (validatedData.metadata !== undefined) updateData.metadata = validatedData.metadata

    // 更新模板
    const updatedTemplate = await prisma.permissionTemplate.update({
      where: { id },
      data: updateData,
      include: {
        updatedByUser: {
          select: {
            id: true,
            username: true,
            fullName: true
          }
        }
      }
    })

    // 记录操作历史
    await prisma.permissionTemplateHistory.create({
      data: {
        templateId: id,
        action: 'UPDATE',
        oldData: {
          name: existingTemplate.name,
          permissions: existingTemplate.permissions,
          category: existingTemplate.category
        },
        newData: {
          name: updatedTemplate.name,
          permissions: updatedTemplate.permissions,
          category: updatedTemplate.category
        },
        changeReason: '更新模板信息',
        changedBy: authReq.user!.id
      }
    })

    // 记录操作日志
    await prisma.operationLog.create({
      data: {
        userId: authReq.user!.id,
        action: 'UPDATE_PERMISSION_TEMPLATE',
        resource: 'permission_template',
        resourceId: id,
        details: {
          templateName: updatedTemplate.name,
          changes: validatedData
        }
      }
    })

    res.json({
      success: true,
      message: '权限模板更新成功',
      data: {
        id: updatedTemplate.id,
        name: updatedTemplate.name,
        description: updatedTemplate.description,
        permissions: validPermissions || JSON.parse(updatedTemplate.permissions as string),
        category: updatedTemplate.category,
        version: updatedTemplate.version,
        updatedBy: updatedTemplate.updatedByUser,
        updatedAt: updatedTemplate.updatedAt
      }
    })
  } catch (error: any) {
    console.error('更新权限模板失败:', error)
    res.status(500).json({
      success: false,
      message: '更新权限模板失败',
      error: error.message
    })
  }
}

/**
 * 删除权限模板
 * @route DELETE /api/v1/permission-templates/:id
 */
export const deletePermissionTemplate = async (req: Request, res: Response) => {
  try {
    const { id } = req.params
    const authReq = req as AuthenticatedRequest

    // 获取模板信息
    const template = await prisma.permissionTemplate.findUnique({
      where: { id },
      include: {
        _count: {
          select: {
            templateUsage: {
              where: { status: 'ACTIVE' }
            }
          }
        }
      }
    })

    if (!template) {
      return res.status(404).json({
        success: false,
        message: '权限模板不存在'
      })
    }

    // 检查是否为系统模板或默认模板
    if (template.isSystem || template.isDefault) {
      return res.status(403).json({
        success: false,
        message: '系统模板和默认模板不允许删除'
      })
    }

    // 检查是否有角色正在使用此模板
    if (template._count.templateUsage > 0) {
      return res.status(400).json({
        success: false,
        message: '该模板正在被使用中，无法删除',
        data: {
          usageCount: template._count.templateUsage
        }
      })
    }

    // 删除模板（关联的历史记录和使用记录会自动级联删除）
    await prisma.permissionTemplate.delete({
      where: { id }
    })

    // 记录操作日志
    await prisma.operationLog.create({
      data: {
        userId: authReq.user!.id,
        action: 'DELETE_PERMISSION_TEMPLATE',
        resource: 'permission_template',
        resourceId: id,
        details: {
          templateName: template.name,
          category: template.category
        }
      }
    })

    res.json({
      success: true,
      message: '权限模板删除成功'
    })
  } catch (error: any) {
    console.error('删除权限模板失败:', error)
    res.status(500).json({
      success: false,
      message: '删除权限模板失败',
      error: error.message
    })
  }
}

// ==================== 模板应用操作 ====================

/**
 * 应用模板到角色
 * @route POST /api/v1/permission-templates/:id/apply
 */
export const applyTemplateToRoles = async (req: Request, res: Response) => {
  try {
    const { id } = req.params
    const { roleIds, note } = applyTemplateSchema.parse(req.body)
    const authReq = req as AuthenticatedRequest

    // 获取模板信息
    const template = await prisma.permissionTemplate.findUnique({
      where: { id }
    })

    if (!template) {
      return res.status(404).json({
        success: false,
        message: '权限模板不存在'
      })
    }

    // 获取模板权限
    const templatePermissions = Array.isArray(template.permissions) 
      ? template.permissions 
      : JSON.parse(template.permissions as string)

    // 验证角色是否存在
    const roles = await prisma.role.findMany({
      where: { id: { in: roleIds } }
    })

    if (roles.length !== roleIds.length) {
      const existingRoleIds = roles.map(role => role.id)
      const missingRoleIds = roleIds.filter(id => !existingRoleIds.includes(id))
      
      return res.status(404).json({
        success: false,
        message: '部分角色不存在',
        data: { missingRoleIds }
      })
    }

    const results = []

    // 逐个应用模板到角色
    for (const roleId of roleIds) {
      const role = roles.find(r => r.id === roleId)!

      // 更新角色权限
      await prisma.role.update({
        where: { id: roleId },
        data: {
          permissions: JSON.stringify(templatePermissions)
        }
      })

      // 记录模板使用
      await prisma.permissionTemplateUsage.upsert({
        where: {
          templateId_roleId: {
            templateId: id,
            roleId: roleId
          }
        },
        update: {
          appliedBy: authReq.user!.id,
          appliedAt: new Date(),
          status: 'ACTIVE',
          note: note
        },
        create: {
          templateId: id,
          roleId: roleId,
          appliedBy: authReq.user!.id,
          status: 'ACTIVE',
          note: note
        }
      })

      results.push({
        roleId,
        roleName: role.name,
        success: true
      })
    }

    // 记录操作历史
    await prisma.permissionTemplateHistory.create({
      data: {
        templateId: id,
        action: 'APPLY',
        newData: {
          appliedToRoles: roles.map(role => ({
            id: role.id,
            name: role.name
          }))
        },
        changeReason: `应用模板到${roles.length}个角色`,
        changedBy: authReq.user!.id
      }
    })

    // 记录操作日志
    await prisma.operationLog.create({
      data: {
        userId: authReq.user!.id,
        action: 'APPLY_PERMISSION_TEMPLATE',
        resource: 'permission_template',
        resourceId: id,
        details: {
          templateName: template.name,
          appliedToRoles: roles.map(role => ({
            id: role.id,
            name: role.name
          })),
          note
        }
      }
    })

    res.json({
      success: true,
      message: `权限模板已成功应用到${results.length}个角色`,
      data: {
        templateId: id,
        templateName: template.name,
        results
      }
    })
  } catch (error: any) {
    console.error('应用权限模板失败:', error)
    res.status(500).json({
      success: false,
      message: '应用权限模板失败',
      error: error.message
    })
  }
}

/**
 * 批量应用模板
 * @route POST /api/v1/permission-templates/batch-apply
 */
export const batchApplyTemplates = async (req: Request, res: Response) => {
  try {
    const { templateId, applications } = batchApplyTemplateSchema.parse(req.body)
    const authReq = req as AuthenticatedRequest

    // 获取模板信息
    const template = await prisma.permissionTemplate.findUnique({
      where: { id: templateId }
    })

    if (!template) {
      return res.status(404).json({
        success: false,
        message: '权限模板不存在'
      })
    }

    // 获取模板权限
    const templatePermissions = Array.isArray(template.permissions) 
      ? template.permissions 
      : JSON.parse(template.permissions as string)

    // 验证所有角色是否存在
    const roleIds = applications.map(app => app.roleId)
    const roles = await prisma.role.findMany({
      where: { id: { in: roleIds } }
    })

    if (roles.length !== roleIds.length) {
      const existingRoleIds = roles.map(role => role.id)
      const missingRoleIds = roleIds.filter(id => !existingRoleIds.includes(id))
      
      return res.status(404).json({
        success: false,
        message: '部分角色不存在',
        data: { missingRoleIds }
      })
    }

    const results = []

    // 批量应用
    for (const application of applications) {
      const role = roles.find(r => r.id === application.roleId)!

      try {
        // 更新角色权限
        await prisma.role.update({
          where: { id: application.roleId },
          data: {
            permissions: JSON.stringify(templatePermissions)
          }
        })

        // 记录模板使用
        await prisma.permissionTemplateUsage.upsert({
          where: {
            templateId_roleId: {
              templateId: templateId,
              roleId: application.roleId
            }
          },
          update: {
            appliedBy: authReq.user!.id,
            appliedAt: new Date(),
            status: 'ACTIVE',
            note: application.note
          },
          create: {
            templateId: templateId,
            roleId: application.roleId,
            appliedBy: authReq.user!.id,
            status: 'ACTIVE',
            note: application.note
          }
        })

        results.push({
          roleId: application.roleId,
          roleName: role.name,
          success: true
        })
      } catch (error) {
        results.push({
          roleId: application.roleId,
          roleName: role.name,
          success: false,
          error: (error as Error).message
        })
      }
    }

    // 记录操作历史
    await prisma.permissionTemplateHistory.create({
      data: {
        templateId: templateId,
        action: 'APPLY',
        newData: {
          batchAppliedToRoles: applications
        },
        changeReason: `批量应用模板到${applications.length}个角色`,
        changedBy: authReq.user!.id
      }
    })

    // 记录操作日志
    await prisma.operationLog.create({
      data: {
        userId: authReq.user!.id,
        action: 'BATCH_APPLY_PERMISSION_TEMPLATE',
        resource: 'permission_template',
        resourceId: templateId,
        details: {
          templateName: template.name,
          applications: applications,
          results: results
        }
      }
    })

    const successCount = results.filter(r => r.success).length
    const failureCount = results.filter(r => !r.success).length

    res.json({
      success: true,
      message: `批量应用完成：成功${successCount}个，失败${failureCount}个`,
      data: {
        templateId,
        templateName: template.name,
        total: results.length,
        success: successCount,
        failure: failureCount,
        results
      }
    })
  } catch (error: any) {
    console.error('批量应用权限模板失败:', error)
    res.status(500).json({
      success: false,
      message: '批量应用权限模板失败',
      error: error.message
    })
  }
}

// ==================== 模板比较和分析 ====================

/**
 * 比较权限模板
 * @route POST /api/v1/permission-templates/compare
 */
export const comparePermissionTemplates = async (req: Request, res: Response) => {
  try {
    const { templateIds } = compareTemplateSchema.parse(req.body)

    // 获取模板信息
    const templates = await prisma.permissionTemplate.findMany({
      where: { id: { in: templateIds } },
      select: {
        id: true,
        name: true,
        description: true,
        permissions: true,
        category: true,
        version: true
      }
    })

    if (templates.length !== templateIds.length) {
      const existingTemplateIds = templates.map(t => t.id)
      const missingTemplateIds = templateIds.filter(id => !existingTemplateIds.includes(id))
      
      return res.status(404).json({
        success: false,
        message: '部分模板不存在',
        data: { missingTemplateIds }
      })
    }

    // 处理权限数据
    const templatesData = templates.map(template => {
      const permissions = Array.isArray(template.permissions) 
        ? template.permissions 
        : JSON.parse(template.permissions as string)

      return {
        id: template.id,
        name: template.name,
        description: template.description,
        permissions: permissions as string[],
        category: template.category,
        version: template.version
      }
    })

    // 获取所有权限的并集
    const allPermissions = new Set<string>()
    templatesData.forEach(template => {
      template.permissions.forEach(permission => allPermissions.add(permission))
    })

    // 构建比较矩阵
    const comparisonMatrix = Array.from(allPermissions).map(permission => {
      const row: any = {
        permission,
        description: getPermissionDescription(permission)
      }

      templatesData.forEach(template => {
        row[template.id] = template.permissions.includes(permission)
      })

      return row
    })

    // 计算相似度
    const similarities: any[] = []
    for (let i = 0; i < templatesData.length; i++) {
      for (let j = i + 1; j < templatesData.length; j++) {
        const template1 = templatesData[i]
        const template2 = templatesData[j]
        
        const set1 = new Set(template1.permissions)
        const set2 = new Set(template2.permissions)
        
        const intersection = new Set([...set1].filter(x => set2.has(x)))
        const union = new Set([...set1, ...set2])
        
        const similarity = (intersection.size / union.size) * 100

        similarities.push({
          template1: { id: template1.id, name: template1.name },
          template2: { id: template2.id, name: template2.name },
          similarity: Math.round(similarity * 100) / 100,
          commonPermissions: intersection.size,
          totalPermissions: union.size,
          onlyInTemplate1: [...set1].filter(x => !set2.has(x)).length,
          onlyInTemplate2: [...set2].filter(x => !set1.has(x)).length
        })
      }
    }

    // 权限分布统计
    const permissionStats = templatesData.map(template => ({
      templateId: template.id,
      templateName: template.name,
      category: template.category,
      totalPermissions: template.permissions.length,
      uniquePermissions: template.permissions.filter(permission => {
        return templatesData.filter(t => t.id !== template.id)
          .every(t => !t.permissions.includes(permission))
      }).length
    }))

    res.json({
      success: true,
      data: {
        templates: templatesData.map(t => ({
          id: t.id,
          name: t.name,
          description: t.description,
          category: t.category,
          version: t.version,
          permissionCount: t.permissions.length
        })),
        comparisonMatrix,
        similarities,
        permissionStats,
        summary: {
          totalTemplates: templatesData.length,
          totalUniquePermissions: allPermissions.size,
          averagePermissionsPerTemplate: Math.round(
            templatesData.reduce((sum, t) => sum + t.permissions.length, 0) / templatesData.length
          )
        }
      }
    })
  } catch (error: any) {
    console.error('比较权限模板失败:', error)
    res.status(500).json({
      success: false,
      message: '比较权限模板失败',
      error: error.message
    })
  }
}

// ==================== 模板导入导出 ====================

/**
 * 导出权限模板
 * @route POST /api/v1/permission-templates/export
 */
export const exportPermissionTemplates = async (req: Request, res: Response) => {
  try {
    const { templateIds, format } = exportTemplateSchema.parse(req.body)

    // 获取模板信息
    const templates = await prisma.permissionTemplate.findMany({
      where: { id: { in: templateIds } },
      include: {
        createdByUser: {
          select: {
            username: true,
            fullName: true
          }
        }
      }
    })

    if (templates.length === 0) {
      return res.status(404).json({
        success: false,
        message: '没有找到要导出的模板'
      })
    }

    // 处理导出数据
    const exportData = templates.map(template => {
      const permissions = Array.isArray(template.permissions) 
        ? template.permissions 
        : JSON.parse(template.permissions as string)

      return {
        name: template.name,
        description: template.description,
        permissions,
        category: template.category,
        version: template.version,
        isDefault: template.isDefault,
        metadata: template.metadata,
        createdBy: template.createdByUser?.username || 'unknown',
        createdAt: template.createdAt.toISOString(),
        exportedAt: new Date().toISOString()
      }
    })

    if (format === 'json') {
      res.setHeader('Content-Type', 'application/json')
      res.setHeader('Content-Disposition', `attachment; filename="permission-templates-${new Date().getTime()}.json"`)
      
      res.json({
        version: '1.0',
        exportedAt: new Date().toISOString(),
        totalTemplates: exportData.length,
        templates: exportData
      })
    } else {
      // CSV format
      const csv = generateCSV(exportData)
      res.setHeader('Content-Type', 'text/csv')
      res.setHeader('Content-Disposition', `attachment; filename="permission-templates-${new Date().getTime()}.csv"`)
      res.send(csv)
    }
  } catch (error: any) {
    console.error('导出权限模板失败:', error)
    res.status(500).json({
      success: false,
      message: '导出权限模板失败',
      error: error.message
    })
  }
}

/**
 * 导入权限模板
 * @route POST /api/v1/permission-templates/import
 */
export const importPermissionTemplates = async (req: Request, res: Response) => {
  try {
    const { templates, overwrite } = importTemplateSchema.parse(req.body)
    const authReq = req as AuthenticatedRequest

    const results = []

    for (const templateData of templates) {
      try {
        // 验证权限格式
        const validation = validatePermissions(templateData.permissions)
        if (!validation.valid) {
          results.push({
            name: templateData.name,
            success: false,
            error: '权限格式无效',
            details: {
              invalidPermissions: validation.invalidPermissions,
              unknownPermissions: validation.unknownPermissions
            }
          })
          continue
        }

        const validPermissions = filterValidPermissions(templateData.permissions)

        // 检查模板是否已存在
        const existingTemplate = await prisma.permissionTemplate.findUnique({
          where: { name: templateData.name }
        })

        if (existingTemplate && !overwrite) {
          results.push({
            name: templateData.name,
            success: false,
            error: '模板名称已存在，未开启覆盖模式'
          })
          continue
        }

        if (existingTemplate && overwrite) {
          // 更新现有模板
          const updated = await prisma.permissionTemplate.update({
            where: { id: existingTemplate.id },
            data: {
              description: templateData.description,
              permissions: JSON.stringify(validPermissions),
              category: templateData.category,
              version: templateData.version,
              metadata: templateData.metadata,
              updatedBy: authReq.user!.id
            }
          })

          // 记录历史
          await prisma.permissionTemplateHistory.create({
            data: {
              templateId: updated.id,
              action: 'IMPORT',
              oldData: {
                permissions: existingTemplate.permissions
              },
              newData: {
                permissions: validPermissions
              },
              changeReason: '模板导入覆盖',
              changedBy: authReq.user!.id
            }
          })

          results.push({
            name: templateData.name,
            success: true,
            action: 'updated',
            templateId: updated.id
          })
        } else {
          // 创建新模板
          const created = await prisma.permissionTemplate.create({
            data: {
              name: templateData.name,
              description: templateData.description,
              permissions: JSON.stringify(validPermissions),
              category: templateData.category,
              version: templateData.version || '1.0',
              metadata: templateData.metadata,
              createdBy: authReq.user!.id
            }
          })

          // 记录历史
          await prisma.permissionTemplateHistory.create({
            data: {
              templateId: created.id,
              action: 'IMPORT',
              newData: {
                name: created.name,
                permissions: validPermissions
              },
              changeReason: '模板导入创建',
              changedBy: authReq.user!.id
            }
          })

          results.push({
            name: templateData.name,
            success: true,
            action: 'created',
            templateId: created.id
          })
        }
      } catch (error) {
        results.push({
          name: templateData.name,
          success: false,
          error: (error as Error).message
        })
      }
    }

    // 记录操作日志
    await prisma.operationLog.create({
      data: {
        userId: authReq.user!.id,
        action: 'IMPORT_PERMISSION_TEMPLATES',
        resource: 'permission_template',
        details: {
          totalTemplates: templates.length,
          overwriteMode: overwrite,
          results: results
        }
      }
    })

    const successCount = results.filter(r => r.success).length
    const failureCount = results.filter(r => !r.success).length

    res.json({
      success: true,
      message: `导入完成：成功${successCount}个，失败${failureCount}个`,
      data: {
        total: results.length,
        success: successCount,
        failure: failureCount,
        results
      }
    })
  } catch (error: any) {
    console.error('导入权限模板失败:', error)
    res.status(500).json({
      success: false,
      message: '导入权限模板失败',
      error: error.message
    })
  }
}

// ==================== 默认模板管理 ====================

/**
 * 创建默认权限模板
 * @route POST /api/v1/permission-templates/create-defaults
 */
export const createDefaultTemplates = async (req: Request, res: Response) => {
  try {
    const authReq = req as AuthenticatedRequest

    const results = []

    // 基于默认角色权限创建模板
    for (const [roleName, permissions] of Object.entries(DEFAULT_ROLE_PERMISSIONS)) {
      try {
        const templateName = `默认-${getRoleDisplayName(roleName as RoleName)}`
        
        // 检查是否已存在
        const existing = await prisma.permissionTemplate.findUnique({
          where: { name: templateName }
        })

        if (existing) {
          results.push({
            roleName,
            templateName,
            success: false,
            message: '模板已存在'
          })
          continue
        }

        // 创建默认模板
        const template = await prisma.permissionTemplate.create({
          data: {
            name: templateName,
            description: `基于${getRoleDisplayName(roleName as RoleName)}角色的默认权限配置`,
            permissions: JSON.stringify(permissions),
            category: getCategoryByRole(roleName as RoleName),
            version: '1.0',
            isDefault: true,
            isSystem: true,
            createdBy: authReq.user!.id,
            metadata: {
              sourceRole: roleName,
              isSystemGenerated: true
            }
          }
        })

        // 记录历史
        await prisma.permissionTemplateHistory.create({
          data: {
            templateId: template.id,
            action: 'CREATE',
            newData: {
              name: template.name,
              permissions: permissions,
              sourceRole: roleName
            },
            changeReason: '创建默认权限模板',
            changedBy: authReq.user!.id
          }
        })

        results.push({
          roleName,
          templateName,
          templateId: template.id,
          success: true,
          permissionCount: permissions.length
        })
      } catch (error) {
        results.push({
          roleName,
          templateName: `默认-${getRoleDisplayName(roleName as RoleName)}`,
          success: false,
          error: (error as Error).message
        })
      }
    }

    // 记录操作日志
    await prisma.operationLog.create({
      data: {
        userId: authReq.user!.id,
        action: 'CREATE_DEFAULT_PERMISSION_TEMPLATES',
        resource: 'permission_template',
        details: {
          totalRoles: Object.keys(DEFAULT_ROLE_PERMISSIONS).length,
          results: results
        }
      }
    })

    const successCount = results.filter(r => r.success).length
    const failureCount = results.filter(r => !r.success).length

    res.json({
      success: true,
      message: `默认模板创建完成：成功${successCount}个，失败${failureCount}个`,
      data: {
        total: results.length,
        success: successCount,
        failure: failureCount,
        results
      }
    })
  } catch (error: any) {
    console.error('创建默认权限模板失败:', error)
    res.status(500).json({
      success: false,
      message: '创建默认权限模板失败',
      error: error.message
    })
  }
}

/**
 * 复制权限模板
 * @route POST /api/v1/permission-templates/:id/copy
 */
export const copyPermissionTemplate = async (req: Request, res: Response) => {
  try {
    const { id } = req.params
    const { name, description } = req.body
    const authReq = req as AuthenticatedRequest

    // 获取源模板
    const sourceTemplate = await prisma.permissionTemplate.findUnique({
      where: { id }
    })

    if (!sourceTemplate) {
      return res.status(404).json({
        success: false,
        message: '源权限模板不存在'
      })
    }

    const copyName = name || `${sourceTemplate.name} - 副本`

    // 检查新名称是否已存在
    const existingTemplate = await prisma.permissionTemplate.findUnique({
      where: { name: copyName }
    })

    if (existingTemplate) {
      return res.status(400).json({
        success: false,
        message: '模板名称已存在'
      })
    }

    // 创建副本
    const copiedTemplate = await prisma.permissionTemplate.create({
      data: {
        name: copyName,
        description: description || `复制自：${sourceTemplate.name}`,
        permissions: sourceTemplate.permissions,
        category: sourceTemplate.category,
        version: sourceTemplate.version,
        metadata: {
          ...sourceTemplate.metadata as any,
          copiedFrom: {
            templateId: sourceTemplate.id,
            templateName: sourceTemplate.name,
            copiedAt: new Date().toISOString()
          }
        },
        createdBy: authReq.user!.id
      },
      include: {
        createdByUser: {
          select: {
            id: true,
            username: true,
            fullName: true
          }
        }
      }
    })

    // 记录历史
    await prisma.permissionTemplateHistory.create({
      data: {
        templateId: copiedTemplate.id,
        action: 'COPY',
        newData: {
          name: copiedTemplate.name,
          copiedFromTemplateId: sourceTemplate.id,
          copiedFromTemplateName: sourceTemplate.name
        },
        changeReason: '复制权限模板',
        changedBy: authReq.user!.id
      }
    })

    // 记录操作日志
    await prisma.operationLog.create({
      data: {
        userId: authReq.user!.id,
        action: 'COPY_PERMISSION_TEMPLATE',
        resource: 'permission_template',
        resourceId: copiedTemplate.id,
        details: {
          sourceTemplateId: sourceTemplate.id,
          sourceTemplateName: sourceTemplate.name,
          newTemplateName: copiedTemplate.name
        }
      }
    })

    res.status(201).json({
      success: true,
      message: '权限模板复制成功',
      data: {
        id: copiedTemplate.id,
        name: copiedTemplate.name,
        description: copiedTemplate.description,
        category: copiedTemplate.category,
        permissions: JSON.parse(copiedTemplate.permissions as string),
        createdBy: copiedTemplate.createdByUser,
        createdAt: copiedTemplate.createdAt
      }
    })
  } catch (error: any) {
    console.error('复制权限模板失败:', error)
    res.status(500).json({
      success: false,
      message: '复制权限模板失败',
      error: error.message
    })
  }
}

// ==================== 辅助函数 ====================

/**
 * 获取角色显示名称
 */
function getRoleDisplayName(roleName: RoleName): string {
  const roleNames = {
    admin: '超级管理员',
    engineer: '运维工程师',
    customer_service: '客户服务',
    project_manager: '项目经理',
    ops_supervisor: '运维主管',
    readonly: '只读用户'
  }
  return roleNames[roleName] || roleName
}

/**
 * 根据角色获取模板分类
 */
function getCategoryByRole(roleName: RoleName): 'SYSTEM' | 'BUSINESS' | 'SERVICE' | 'READONLY' | 'CUSTOM' {
  const categoryMapping = {
    admin: 'SYSTEM' as const,
    engineer: 'SERVICE' as const,
    customer_service: 'BUSINESS' as const,
    project_manager: 'BUSINESS' as const,
    ops_supervisor: 'SYSTEM' as const,
    readonly: 'READONLY' as const
  }
  return categoryMapping[roleName] || 'CUSTOM'
}

/**
 * 生成CSV格式数据
 */
function generateCSV(data: any[]): string {
  if (data.length === 0) return ''

  const headers = ['名称', '描述', '分类', '权限数量', '权限列表', '版本', '创建者', '创建时间']
  const rows = data.map(template => [
    template.name,
    template.description || '',
    template.category,
    template.permissions.length,
    template.permissions.join('; '),
    template.version,
    template.createdBy,
    template.createdAt
  ])

  const csvContent = [headers, ...rows]
    .map(row => row.map(field => `"${field}"`).join(','))
    .join('\n')

  return csvContent
}

/**
 * 获取权限模板统计信息
 * @route GET /api/v1/permission-templates/stats
 */
export const getPermissionTemplateStats = async (req: Request, res: Response) => {
  try {
    // 获取基础统计
    const [
      totalCount,
      defaultCount,
      systemCount,
      categoryStats,
      usageStats,
      recentActivity
    ] = await Promise.all([
      // 总模板数
      prisma.permissionTemplate.count(),
      
      // 默认模板数
      prisma.permissionTemplate.count({
        where: { isDefault: true }
      }),
      
      // 系统模板数
      prisma.permissionTemplate.count({
        where: { isSystem: true }
      }),
      
      // 分类统计
      prisma.permissionTemplate.groupBy({
        by: ['category'],
        _count: { id: true }
      }),
      
      // 使用情况统计
      prisma.permissionTemplateUsage.groupBy({
        by: ['status'],
        _count: { id: true }
      }),
      
      // 最近活动
      prisma.permissionTemplateHistory.findMany({
        take: 10,
        orderBy: { createdAt: 'desc' },
        include: {
          template: {
            select: { name: true }
          },
          changedByUser: {
            select: { username: true, fullName: true }
          }
        }
      })
    ])

    // 获取最受欢迎的模板
    const popularTemplates = await prisma.permissionTemplate.findMany({
      include: {
        _count: {
          select: {
            templateUsage: {
              where: { status: 'ACTIVE' }
            }
          }
        }
      },
      orderBy: {
        templateUsage: {
          _count: 'desc'
        }
      },
      take: 5
    })

    res.json({
      success: true,
      data: {
        overview: {
          total: totalCount,
          default: defaultCount,
          system: systemCount,
          custom: totalCount - defaultCount,
          active: usageStats.find(s => s.status === 'ACTIVE')?._count.id || 0
        },
        categoryDistribution: categoryStats.map(stat => ({
          category: stat.category,
          count: stat._count.id
        })),
        usageDistribution: usageStats.map(stat => ({
          status: stat.status,
          count: stat._count.id
        })),
        popularTemplates: popularTemplates.map(template => ({
          id: template.id,
          name: template.name,
          category: template.category,
          usageCount: template._count.templateUsage
        })),
        recentActivity: recentActivity.map(activity => ({
          id: activity.id,
          action: activity.action,
          templateName: activity.template.name,
          changedBy: activity.changedByUser?.fullName || activity.changedByUser?.username,
          changeReason: activity.changeReason,
          createdAt: activity.createdAt
        }))
      }
    })
  } catch (error: any) {
    console.error('获取权限模板统计失败:', error)
    res.status(500).json({
      success: false,
      message: '获取权限模板统计失败',
      error: error.message
    })
  }
}