import { Request, Response } from 'express';
import { asyncHand<PERSON> } from '@/utils/asyncHandler';
import { prisma } from '@/config/database.config';
import { SchedulerService } from '@/services/scheduler.service';
import { TaskStatus } from '@prisma/client';

/**
 * @description Get a paginated list of task executions
 * @route GET /api/v1/tasks
 */
export const getTaskExecutions = asyncHandler(async (req: Request, res: Response) => {
  const page = parseInt(req.query.page as string) || 1;
  const limit = parseInt(req.query.limit as string) || 10;
  const status = req.query.status as TaskStatus | undefined;

  const skip = (page - 1) * limit;

  const where: any = {};
  if (status && Object.values(TaskStatus).includes(status)) {
    where.status = status;
  }

  const [tasks, total] = await Promise.all([
    prisma.taskExecution.findMany({
      where,
      skip,
      take: limit,
      orderBy: { startedAt: 'desc' },
    }),
    prisma.taskExecution.count({ where }),
  ]);

  res.status(200).json({
    success: true,
    data: {
      tasks,
      pagination: { page, limit, total, totalPages: Math.ceil(total / limit) },
    },
    message: 'Task executions fetched successfully',
  });
});

/**
 * @description Get details for a single task execution
 * @route GET /api/v1/tasks/:id
 */
export const getTaskExecutionDetails = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  const task = await prisma.taskExecution.findUniqueOrThrow({
    where: { id },
  });

  res.status(200).json({
    success: true,
    data: task,
    message: 'Task execution details fetched successfully',
  });
});

/**
 * @description Retry a failed task execution
 * @route POST /api/v1/tasks/:id/retry
 */
export const retryTaskExecution = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;

  const execution = await prisma.taskExecution.findUniqueOrThrow({
    where: { id },
  });

  if (execution.status !== 'FAILED') {
    return res.status(400).json({
      success: false,
      message: `Cannot retry a task that has not failed. Current status: ${execution.status}`,
    });
  }

  await SchedulerService.runTaskNow(execution.taskName);

  res.status(200).json({
    success: true,
    message: `Task "${execution.taskName}" has been scheduled for a retry.`,
  });
});
