import { Request, Response } from 'express'
import { AlertEngineService, AlertRuleConfig } from '@/services/alert-engine.service'
import { asyncHandler } from '@/utils/asyncHandler'
import { prisma } from '@/config/database.config'
import { z } from 'zod'

// 输入验证模式
const CreateAlertRuleSchema = z.object({
  name: z.string().min(1, '规则名称不能为空').max(100, '规则名称过长'),
  description: z.string().optional(),
  metricType: z.enum(['CPU', 'MEMORY', 'DISK', 'NETWORK', 'SERVICE', 'DATABASE', 'REDIS']),
  condition: z.enum(['GT', 'LT', 'GTE', 'LTE', 'EQ', 'NEQ']),
  threshold: z.number().min(0).max(100),
  duration: z.number().min(0).max(3600),
  severity: z.enum(['LOW', 'MEDIUM', 'HIGH', 'CRITICAL']),
  enabled: z.boolean().default(true),
  notificationChannels: z.array(z.string()).min(1, '至少需要一个通知渠道'),
  cooldownPeriod: z.number().optional(),
  tags: z.array(z.string()).optional()
})

const UpdateAlertRuleSchema = CreateAlertRuleSchema.partial()

const AlertQuerySchema = z.object({
  page: z.string().transform(val => parseInt(val) || 1),
  limit: z.string().transform(val => Math.min(parseInt(val) || 10, 100)),
  severity: z.enum(['LOW', 'MEDIUM', 'HIGH', 'CRITICAL']).optional(),
  status: z.enum(['PENDING', 'ACKNOWLEDGED', 'RESOLVED', 'CANCELLED']).optional(),
  ruleId: z.string().optional(),
  startDate: z.string().optional(),
  endDate: z.string().optional()
})

/**
 * 智能告警引擎控制器
 * 
 * 提供告警规则管理、告警处理、统计分析等REST API
 * 
 * @swagger
 * tags:
 *   - name: AlertEngine
 *     description: 智能告警引擎管理
 * 
 * components:
 *   schemas:
 *     AlertRule:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           description: 告警规则ID
 *         name:
 *           type: string
 *           description: 规则名称
 *         description:
 *           type: string
 *           description: 规则描述
 *         metricType:
 *           type: string
 *           enum: [CPU, MEMORY, DISK, NETWORK, SERVICE, DATABASE, REDIS]
 *           description: 指标类型
 *         condition:
 *           type: string
 *           enum: [GT, LT, GTE, LTE, EQ, NEQ]
 *           description: 条件类型
 *         threshold:
 *           type: number
 *           description: 阈值
 *         duration:
 *           type: number
 *           description: 持续时间(秒)
 *         severity:
 *           type: string
 *           enum: [LOW, MEDIUM, HIGH, CRITICAL]
 *           description: 严重程度
 *         enabled:
 *           type: boolean
 *           description: 是否启用
 *         notificationChannels:
 *           type: array
 *           items:
 *             type: string
 *           description: 通知渠道
 *     Alert:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           description: 告警ID
 *         ruleId:
 *           type: string
 *           description: 关联的规则ID
 *         severity:
 *           type: string
 *           enum: [LOW, MEDIUM, HIGH, CRITICAL]
 *           description: 严重程度
 *         status:
 *           type: string
 *           enum: [PENDING, ACKNOWLEDGED, RESOLVED, CANCELLED]
 *           description: 告警状态
 *         message:
 *           type: string
 *           description: 告警消息
 *         metricValue:
 *           type: number
 *           description: 触发时的指标值
 *         triggeredAt:
 *           type: string
 *           format: date-time
 *           description: 触发时间
 */
export class AlertEngineController {
  private alertEngine: AlertEngineService

  constructor() {
    this.alertEngine = AlertEngineService.getInstance()
  }

  /**
   * 创建告警规则
   * 
   * @swagger
   * /api/v1/alert-engine/rules:
   *   post:
   *     tags: [AlertEngine]
   *     summary: 创建告警规则
   *     description: 创建新的智能告警规则
   *     security:
   *       - bearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - name
   *               - metricType
   *               - condition
   *               - threshold
   *               - duration
   *               - severity
   *               - notificationChannels
   *             properties:
   *               name:
   *                 type: string
   *                 description: 规则名称
   *                 example: "CPU使用率告警"
   *               description:
   *                 type: string
   *                 description: 规则描述
   *                 example: "当CPU使用率超过80%时触发"
   *               metricType:
   *                 type: string
   *                 enum: [CPU, MEMORY, DISK, NETWORK, SERVICE, DATABASE, REDIS]
   *                 description: 指标类型
   *                 example: "CPU"
   *               condition:
   *                 type: string
   *                 enum: [GT, LT, GTE, LTE, EQ, NEQ]
   *                 description: 条件类型
   *                 example: "GT"
   *               threshold:
   *                 type: number
   *                 description: 阈值(0-100)
   *                 example: 80
   *               duration:
   *                 type: number
   *                 description: 持续时间(秒)
   *                 example: 300
   *               severity:
   *                 type: string
   *                 enum: [LOW, MEDIUM, HIGH, CRITICAL]
   *                 description: 严重程度
   *                 example: "HIGH"
   *               enabled:
   *                 type: boolean
   *                 description: 是否启用
   *                 default: true
   *               notificationChannels:
   *                 type: array
   *                 items:
   *                   type: string
   *                 description: 通知渠道
   *                 example: ["email", "realtime"]
   *               cooldownPeriod:
   *                 type: number
   *                 description: 冷却期(秒)
   *                 example: 300
   *               tags:
   *                 type: array
   *                 items:
   *                   type: string
   *                 description: 标签
   *                 example: ["system", "cpu"]
   *     responses:
   *       201:
   *         description: 告警规则创建成功
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 success:
   *                   type: boolean
   *                   example: true
   *                 message:
   *                   type: string
   *                   example: "告警规则创建成功"
   *                 data:
   *                   $ref: '#/components/schemas/AlertRule'
   *       400:
   *         description: 请求参数错误
   *       401:
   *         description: 未授权访问
   *       403:
   *         description: 权限不足
   */
  createAlertRule = asyncHandler(async (req: Request, res: Response) => {
    const userId = req.user?.id
    if (!userId) {
      return res.status(401).json({ message: '未授权访问' })
    }

    // 验证输入
    const validatedData = CreateAlertRuleSchema.parse(req.body)
    
    // 检查规则名称是否已存在
    const existingRule = await prisma.alertRule.findFirst({
      where: { name: validatedData.name }
    })
    
    if (existingRule) {
      return res.status(400).json({ 
        message: '规则名称已存在',
        code: 'RULE_NAME_EXISTS'
      })
    }

    const rule = await this.alertEngine.createAlertRule(validatedData as AlertRuleConfig, userId)
    
    res.status(201).json({
      success: true,
      message: '告警规则创建成功',
      data: rule
    })
  })

  /**
   * 更新告警规则
   */
  updateAlertRule = asyncHandler(async (req: Request, res: Response) => {
    const { ruleId } = req.params
    const userId = req.user?.id

    if (!userId) {
      return res.status(401).json({ message: '未授权访问' })
    }

    // 验证输入
    const validatedData = UpdateAlertRuleSchema.parse(req.body)

    // 检查规则是否存在
    const existingRule = await prisma.alertRule.findUnique({
      where: { id: ruleId }
    })

    if (!existingRule) {
      return res.status(404).json({
        message: '告警规则不存在',
        code: 'RULE_NOT_FOUND'
      })
    }

    // 如果更新名称，检查新名称是否已存在
    if (validatedData.name && validatedData.name !== existingRule.name) {
      const nameExists = await prisma.alertRule.findFirst({
        where: { 
          name: validatedData.name,
          id: { not: ruleId }
        }
      })
      
      if (nameExists) {
        return res.status(400).json({
          message: '规则名称已存在',
          code: 'RULE_NAME_EXISTS'
        })
      }
    }

    const updatedRule = await this.alertEngine.updateAlertRule(ruleId, validatedData)
    
    res.json({
      success: true,
      message: '告警规则更新成功',
      data: updatedRule
    })
  })

  /**
   * 删除告警规则
   */
  deleteAlertRule = asyncHandler(async (req: Request, res: Response) => {
    const { ruleId } = req.params
    const userId = req.user?.id

    if (!userId) {
      return res.status(401).json({ message: '未授权访问' })
    }

    // 检查规则是否存在
    const existingRule = await prisma.alertRule.findUnique({
      where: { id: ruleId }
    })

    if (!existingRule) {
      return res.status(404).json({
        message: '告警规则不存在',
        code: 'RULE_NOT_FOUND'
      })
    }

    // 检查是否有关联的未解决告警
    const activeAlerts = await prisma.alert.count({
      where: {
        ruleId,
        status: { in: ['PENDING', 'ACKNOWLEDGED'] }
      }
    })

    if (activeAlerts > 0) {
      return res.status(400).json({
        message: `无法删除规则，存在${activeAlerts}个未解决的告警`,
        code: 'HAS_ACTIVE_ALERTS'
      })
    }

    await this.alertEngine.deleteAlertRule(ruleId)
    
    res.json({
      success: true,
      message: '告警规则删除成功'
    })
  })

  /**
   * 获取所有告警规则
   */
  getAlertRules = asyncHandler(async (req: Request, res: Response) => {
    const { includeDisabled = 'false' } = req.query
    
    const rules = await this.alertEngine.getAllAlertRules(
      includeDisabled === 'true'
    )
    
    res.json({
      success: true,
      data: rules,
      total: rules.length
    })
  })

  /**
   * 获取单个告警规则详情
   */
  getAlertRule = asyncHandler(async (req: Request, res: Response) => {
    const { ruleId } = req.params
    
    const rule = await prisma.alertRule.findUnique({
      where: { id: ruleId },
      include: {
        alerts: {
          where: {
            triggeredAt: {
              gte: new Date(Date.now() - 24 * 60 * 60 * 1000) // 最近24小时
            }
          },
          orderBy: { triggeredAt: 'desc' },
          take: 10
        }
      }
    })

    if (!rule) {
      return res.status(404).json({
        message: '告警规则不存在',
        code: 'RULE_NOT_FOUND'
      })
    }

    res.json({
      success: true,
      data: rule
    })
  })

  /**
   * 获取告警列表
   * 
   * @swagger
   * /api/v1/alert-engine/alerts:
   *   get:
   *     tags: [AlertEngine]
   *     summary: 获取告警列表
   *     description: 获取系统告警列表，支持分页和过滤
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - name: page
   *         in: query
   *         description: 页码
   *         schema:
   *           type: integer
   *           default: 1
   *       - name: limit
   *         in: query
   *         description: 每页数量(最大100)
   *         schema:
   *           type: integer
   *           default: 10
   *           maximum: 100
   *       - name: severity
   *         in: query
   *         description: 按严重程度过滤
   *         schema:
   *           type: string
   *           enum: [LOW, MEDIUM, HIGH, CRITICAL]
   *       - name: status
   *         in: query
   *         description: 按状态过滤
   *         schema:
   *           type: string
   *           enum: [PENDING, ACKNOWLEDGED, RESOLVED, CANCELLED]
   *       - name: ruleId
   *         in: query
   *         description: 按规则ID过滤
   *         schema:
   *           type: string
   *       - name: startDate
   *         in: query
   *         description: 开始日期(ISO格式)
   *         schema:
   *           type: string
   *           format: date-time
   *       - name: endDate
   *         in: query
   *         description: 结束日期(ISO格式)
   *         schema:
   *           type: string
   *           format: date-time
   *     responses:
   *       200:
   *         description: 获取告警列表成功
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 success:
   *                   type: boolean
   *                   example: true
   *                 data:
   *                   type: array
   *                   items:
   *                     allOf:
   *                       - $ref: '#/components/schemas/Alert'
   *                       - type: object
   *                         properties:
   *                           rule:
   *                             type: object
   *                             properties:
   *                               name:
   *                                 type: string
   *                               metricType:
   *                                 type: string
   *                               description:
   *                                 type: string
   *                 pagination:
   *                   type: object
   *                   properties:
   *                     page:
   *                       type: integer
   *                       example: 1
   *                     limit:
   *                       type: integer
   *                       example: 10
   *                     total:
   *                       type: integer
   *                       example: 50
   *                     totalPages:
   *                       type: integer
   *                       example: 5
   *       401:
   *         description: 未授权访问
   */
  getAlerts = asyncHandler(async (req: Request, res: Response) => {
    // 验证查询参数
    const {
      page,
      limit,
      severity,
      status,
      ruleId,
      startDate,
      endDate
    } = AlertQuerySchema.parse(req.query)

    // 构建查询条件
    const where: any = {}
    
    if (severity) where.severity = severity
    if (status) where.status = status
    if (ruleId) where.ruleId = ruleId
    
    if (startDate || endDate) {
      where.triggeredAt = {}
      if (startDate) where.triggeredAt.gte = new Date(startDate)
      if (endDate) where.triggeredAt.lte = new Date(endDate)
    }

    // 分页查询
    const skip = (page - 1) * limit
    
    const [alerts, total] = await Promise.all([
      prisma.alert.findMany({
        where,
        include: {
          rule: {
            select: {
              name: true,
              metricType: true,
              description: true
            }
          }
        },
        orderBy: [
          { status: 'asc' }, // 未解决的在前
          { severity: 'desc' }, // 高优先级在前
          { triggeredAt: 'desc' } // 最新的在前
        ],
        skip,
        take: limit
      }),
      prisma.alert.count({ where })
    ])

    res.json({
      success: true,
      data: alerts,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    })
  })

  /**
   * 确认告警
   */
  acknowledgeAlert = asyncHandler(async (req: Request, res: Response) => {
    const { alertId } = req.params
    const { note } = req.body
    const userId = req.user?.id

    if (!userId) {
      return res.status(401).json({ message: '未授权访问' })
    }

    // 检查告警是否存在
    const alert = await prisma.alert.findUnique({
      where: { id: alertId }
    })

    if (!alert) {
      return res.status(404).json({
        message: '告警不存在',
        code: 'ALERT_NOT_FOUND'
      })
    }

    if (alert.status !== 'PENDING') {
      return res.status(400).json({
        message: '只能确认待处理状态的告警',
        code: 'INVALID_ALERT_STATUS'
      })
    }

    await this.alertEngine.acknowledgeAlert(alertId, userId, note)
    
    res.json({
      success: true,
      message: '告警确认成功'
    })
  })

  /**
   * 解决告警
   */
  resolveAlert = asyncHandler(async (req: Request, res: Response) => {
    const { alertId } = req.params
    const { note } = req.body
    const userId = req.user?.id

    if (!userId) {
      return res.status(401).json({ message: '未授权访问' })
    }

    // 检查告警是否存在
    const alert = await prisma.alert.findUnique({
      where: { id: alertId }
    })

    if (!alert) {
      return res.status(404).json({
        message: '告警不存在',
        code: 'ALERT_NOT_FOUND'
      })
    }

    if (alert.status === 'RESOLVED') {
      return res.status(400).json({
        message: '告警已解决',
        code: 'ALERT_ALREADY_RESOLVED'
      })
    }

    await this.alertEngine.resolveAlert(alertId, userId, note)
    
    res.json({
      success: true,
      message: '告警解决成功'
    })
  })

  /**
   * 批量操作告警
   */
  bulkAlertOperation = asyncHandler(async (req: Request, res: Response) => {
    const { alertIds, operation, note } = req.body
    const userId = req.user?.id

    if (!userId) {
      return res.status(401).json({ message: '未授权访问' })
    }

    if (!Array.isArray(alertIds) || alertIds.length === 0) {
      return res.status(400).json({
        message: '请选择要操作的告警',
        code: 'NO_ALERTS_SELECTED'
      })
    }

    if (!['acknowledge', 'resolve'].includes(operation)) {
      return res.status(400).json({
        message: '不支持的操作类型',
        code: 'INVALID_OPERATION'
      })
    }

    const results = []
    
    for (const alertId of alertIds) {
      try {
        if (operation === 'acknowledge') {
          await this.alertEngine.acknowledgeAlert(alertId, userId, note)
        } else if (operation === 'resolve') {
          await this.alertEngine.resolveAlert(alertId, userId, note)
        }
        results.push({ alertId, success: true })
      } catch (error) {
        results.push({ 
          alertId, 
          success: false, 
          error: error instanceof Error ? error.message : '操作失败' 
        })
      }
    }

    const successCount = results.filter(r => r.success).length
    const failCount = results.filter(r => !r.success).length

    res.json({
      success: failCount === 0,
      message: `成功处理${successCount}个告警${failCount > 0 ? `，失败${failCount}个` : ''}`,
      data: results
    })
  })

  /**
   * 获取告警统计信息
   */
  getAlertStatistics = asyncHandler(async (req: Request, res: Response) => {
    const { 
      startDate = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
      endDate = new Date().toISOString() 
    } = req.query as any

    const timeRange = {
      start: new Date(startDate),
      end: new Date(endDate)
    }

    const statistics = await this.alertEngine.getAlertStatistics(timeRange)
    
    res.json({
      success: true,
      data: statistics
    })
  })

  /**
   * 获取告警趋势
   */
  getAlertTrends = asyncHandler(async (req: Request, res: Response) => {
    const { 
      days = '7',
      groupBy = 'day' // hour, day, week
    } = req.query as any

    const daysNum = parseInt(days)
    const startDate = new Date(Date.now() - daysNum * 24 * 60 * 60 * 1000)
    const endDate = new Date()

    // 根据groupBy参数确定时间分组格式
    let dateFormat = '%Y-%m-%d'
    if (groupBy === 'hour') {
      dateFormat = '%Y-%m-%d %H:00:00'
    } else if (groupBy === 'week') {
      dateFormat = '%Y-week-%u'
    }

    const trends = await prisma.$queryRaw`
      SELECT 
        DATE_FORMAT(triggered_at, ${dateFormat}) as period,
        severity,
        COUNT(*) as count
      FROM alerts 
      WHERE triggered_at >= ${startDate} AND triggered_at <= ${endDate}
      GROUP BY period, severity
      ORDER BY period ASC
    ` as any[]

    // 处理数据格式
    const processedTrends = trends.reduce((acc: any, item: any) => {
      const period = item.period
      if (!acc[period]) {
        acc[period] = { period, LOW: 0, MEDIUM: 0, HIGH: 0, CRITICAL: 0, total: 0 }
      }
      acc[period][item.severity] = Number(item.count)
      acc[period].total += Number(item.count)
      return acc
    }, {})

    res.json({
      success: true,
      data: Object.values(processedTrends)
    })
  })

  /**
   * 测试告警规则
   */
  testAlertRule = asyncHandler(async (req: Request, res: Response) => {
    const { ruleId } = req.params
    const { testValue } = req.body

    // 获取规则
    const rule = await prisma.alertRule.findUnique({
      where: { id: ruleId }
    })

    if (!rule) {
      return res.status(404).json({
        message: '告警规则不存在',
        code: 'RULE_NOT_FOUND'
      })
    }

    // 模拟指标值进行测试
    const mockMetrics: any = {
      cpu: { usage: rule.metricType === 'CPU' ? testValue : 50 },
      memory: { usage: rule.metricType === 'MEMORY' ? testValue : 60 },
      disk: { usage: rule.metricType === 'DISK' ? testValue : 70 },
      network: { 
        totalRxSpeed: rule.metricType === 'NETWORK' ? testValue / 2 : 100,
        totalTxSpeed: rule.metricType === 'NETWORK' ? testValue / 2 : 100
      }
    }

    // 测试条件
    const conditionResult = this.testCondition(testValue, rule.condition, rule.threshold)
    
    res.json({
      success: true,
      data: {
        rule: {
          name: rule.name,
          metricType: rule.metricType,
          condition: rule.condition,
          threshold: rule.threshold,
          severity: rule.severity
        },
        test: {
          testValue,
          conditionMet: conditionResult,
          wouldTrigger: conditionResult && rule.enabled,
          message: conditionResult 
            ? `告警将被触发：${rule.metricType}值${testValue}${this.getConditionText(rule.condition)}${rule.threshold}`
            : `告警不会触发：${rule.metricType}值${testValue}不满足条件`
        }
      }
    })
  })

  /**
   * 获取告警配置选项
   */
  getAlertOptions = asyncHandler(async (req: Request, res: Response) => {
    res.json({
      success: true,
      data: {
        metricTypes: [
          { value: 'CPU', label: 'CPU使用率' },
          { value: 'MEMORY', label: '内存使用率' },
          { value: 'DISK', label: '磁盘使用率' },
          { value: 'NETWORK', label: '网络流量' },
          { value: 'SERVICE', label: '服务状态' },
          { value: 'DATABASE', label: '数据库' },
          { value: 'REDIS', label: 'Redis缓存' }
        ],
        conditions: [
          { value: 'GT', label: '大于 (>)' },
          { value: 'GTE', label: '大于等于 (>=)' },
          { value: 'LT', label: '小于 (<)' },
          { value: 'LTE', label: '小于等于 (<=)' },
          { value: 'EQ', label: '等于 (=)' },
          { value: 'NEQ', label: '不等于 (≠)' }
        ],
        severities: [
          { value: 'LOW', label: '低', color: 'green' },
          { value: 'MEDIUM', label: '中', color: 'yellow' },
          { value: 'HIGH', label: '高', color: 'orange' },
          { value: 'CRITICAL', label: '严重', color: 'red' }
        ],
        notificationChannels: [
          { value: 'email', label: '邮件通知' },
          { value: 'sms', label: '短信通知' },
          { value: 'webhook', label: 'Webhook' },
          { value: 'realtime', label: '实时推送' }
        ]
      }
    })
  })

  // 私有辅助方法
  private testCondition(value: number, condition: string, threshold: number): boolean {
    switch (condition) {
      case 'GT': return value > threshold
      case 'GTE': return value >= threshold
      case 'LT': return value < threshold
      case 'LTE': return value <= threshold
      case 'EQ': return value === threshold
      case 'NEQ': return value !== threshold
      default: return false
    }
  }

  private getConditionText(condition: string): string {
    const conditionMap: Record<string, string> = {
      'GT': '大于',
      'GTE': '大于等于',
      'LT': '小于',
      'LTE': '小于等于',
      'EQ': '等于',
      'NEQ': '不等于'
    }
    return conditionMap[condition] || condition
  }
}