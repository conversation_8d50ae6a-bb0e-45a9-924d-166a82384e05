import { Request, Response } from 'express'
import { z } from 'zod'
import { RoleService, ROLE_TEMPLATES } from '@/services/role.service'
import { AuditService } from '@/services/audit.service'

// 验证Schema
const createRoleSchema = z.object({
  name: z.string().min(1).max(50).regex(/^[a-zA-Z0-9_-]+$/, '角色名只能包含字母、数字、下划线和横线'),
  description: z.string().min(1).max(200),
  permissions: z.array(z.string()).min(1, '至少需要一个权限')
})

const updateRoleSchema = createRoleSchema.partial()

const querySchema = z.object({
  page: z.string().optional().transform(val => val ? parseInt(val) : 1),
  limit: z.string().optional().transform(val => val ? parseInt(val) : 20),
  search: z.string().optional()
})

const createFromTemplateSchema = z.object({
  templateName: z.enum(Object.keys(ROLE_TEMPLATES) as [keyof typeof ROLE_TEMPLATES]),
  customName: z.string().optional(),
  customDescription: z.string().optional()
})

const duplicateRoleSchema = z.object({
  newName: z.string().min(1).max(50).regex(/^[a-zA-Z0-9_-]+$/, '角色名只能包含字母、数字、下划线和横线'),
  newDescription: z.string().optional()
})

const batchOperationSchema = z.object({
  roleIds: z.array(z.string()).min(1, '至少选择一个角色'),
  operation: z.enum(['enable', 'disable', 'delete']),
  force: z.boolean().optional().default(false) // 强制删除，即使有关联用户
})

const exportRolesSchema = z.object({
  format: z.enum(['excel', 'csv']).default('excel'),
  roleIds: z.array(z.string()).optional(),
  includeUsers: z.boolean().optional().default(false)
})

// 获取角色列表
export const getRoles = async (req: Request, res: Response) => {
  try {
    const { page, limit, search } = querySchema.parse(req.query)

    const result = await RoleService.getRoles(page, limit, search)

    return res.json({
      success: true,
      data: result
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        message: '请求参数验证失败',
        errors: error.errors
      })
    }

    console.error('Get roles error:', error)
    return res.status(500).json({
      success: false,
      message: '获取角色列表失败'
    })
  }
}

// 获取所有角色（用于下拉选择）
export const getAllRoles = async (_req: Request, res: Response) => {
  try {
    const roles = await RoleService.getAllRoles()

    return res.json({
      success: true,
      data: roles
    })
  } catch (error) {
    console.error('Get all roles error:', error)
    return res.status(500).json({
      success: false,
      message: '获取角色列表失败'
    })
  }
}

// 获取角色详情
export const getRole = async (req: Request, res: Response) => {
  try {
    const { id } = req.params

    if (!id) {
      return res.status(400).json({
        success: false,
        message: 'ID参数缺失'
      })
    }

    const role = await RoleService.getRoleById(id)

    return res.json({
      success: true,
      data: role
    })
  } catch (error) {
    console.error('Get role error:', error)
    
    if (error instanceof Error) {
      if (error.message.includes('不存在')) {
        return res.status(404).json({
          success: false,
          message: error.message
        })
      }
    }

    return res.status(500).json({
      success: false,
      message: '获取角色详情失败'
    })
  }
}

// 创建角色
export const createRole = async (req: Request, res: Response) => {
  try {
    const data = createRoleSchema.parse(req.body)
    const currentUser = (req as any).user

    const role = await RoleService.createRole(data)

    // 记录操作日志
    await AuditService.log({
      userId: currentUser.userId,
      action: 'CREATE',
      resource: 'ROLE',
      resourceId: role.id,
      details: {
        roleName: data.name,
        permissions: data.permissions
      },
      ipAddress: req.ip
    })

    return res.status(201).json({
      success: true,
      message: '角色创建成功',
      data: role
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        message: '请求参数验证失败',
        errors: error.errors
      })
    }

    console.error('Create role error:', error)
    
    if (error instanceof Error) {
      if (error.message.includes('已存在')) {
        return res.status(409).json({
          success: false,
          message: error.message
        })
      }
      if (error.message.includes('无效的权限')) {
        return res.status(400).json({
          success: false,
          message: error.message
        })
      }
    }

    return res.status(500).json({
      success: false,
      message: '角色创建失败'
    })
  }
}

// 更新角色
export const updateRole = async (req: Request, res: Response) => {
  try {
    const { id } = req.params
    const data = updateRoleSchema.parse(req.body)
    const currentUser = (req as any).user

    if (!id) {
      return res.status(400).json({
        success: false,
        message: 'ID参数缺失'
      })
    }

    const role = await RoleService.updateRole(id, data)

    // 记录操作日志
    await AuditService.log({
      userId: currentUser.userId,
      action: 'UPDATE',
      resource: 'ROLE',
      resourceId: id,
      details: data,
      ipAddress: req.ip
    })

    return res.json({
      success: true,
      message: '角色更新成功',
      data: role
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        message: '请求参数验证失败',
        errors: error.errors
      })
    }

    console.error('Update role error:', error)
    
    if (error instanceof Error) {
      if (error.message.includes('不存在')) {
        return res.status(404).json({
          success: false,
          message: error.message
        })
      }
      if (error.message.includes('已存在')) {
        return res.status(409).json({
          success: false,
          message: error.message
        })
      }
      if (error.message.includes('无效的权限')) {
        return res.status(400).json({
          success: false,
          message: error.message
        })
      }
    }

    return res.status(500).json({
      success: false,
      message: '角色更新失败'
    })
  }
}

// 删除角色
export const deleteRole = async (req: Request, res: Response) => {
  try {
    const { id } = req.params
    const currentUser = (req as any).user

    if (!id) {
      return res.status(400).json({
        success: false,
        message: 'ID参数缺失'
      })
    }

    await RoleService.deleteRole(id)

    // 记录操作日志
    await AuditService.log({
      userId: currentUser.userId,
      action: 'DELETE',
      resource: 'ROLE',
      resourceId: id,
      details: {},
      ipAddress: req.ip
    })

    return res.json({
      success: true,
      message: '角色删除成功'
    })
  } catch (error) {
    console.error('Delete role error:', error)
    
    if (error instanceof Error) {
      if (error.message.includes('不存在')) {
        return res.status(404).json({
          success: false,
          message: error.message
        })
      }
      if (error.message.includes('正被') && error.message.includes('使用')) {
        return res.status(400).json({
          success: false,
          message: error.message
        })
      }
    }

    return res.status(500).json({
      success: false,
      message: '角色删除失败'
    })
  }
}

// 获取系统权限列表
export const getSystemPermissions = async (_req: Request, res: Response) => {
  try {
    const permissions = RoleService.getSystemPermissions()

    return res.json({
      success: true,
      data: permissions
    })
  } catch (error) {
    console.error('Get system permissions error:', error)
    return res.status(500).json({
      success: false,
      message: '获取系统权限失败'
    })
  }
}

// 获取权限分组
export const getPermissionGroups = async (_req: Request, res: Response) => {
  try {
    const groups = RoleService.getPermissionGroups()
    
    // 转换为前端期望的数组格式
    const groupsArray = Object.entries(groups).map(([name, permissions]) => ({
      name,
      label: name.charAt(0).toUpperCase() + name.slice(1), // 首字母大写作为标签
      permissions: permissions.map(p => ({
        name: p.key,
        label: p.description,
        description: p.description
      }))
    }))

    return res.json({
      success: true,
      data: groupsArray
    })
  } catch (error) {
    console.error('Get permission groups error:', error)
    return res.status(500).json({
      success: false,
      message: '获取权限分组失败'
    })
  }
}

// 获取角色模板
export const getRoleTemplates = async (_req: Request, res: Response) => {
  try {
    const templates = RoleService.getRoleTemplates()

    return res.json({
      success: true,
      data: templates
    })
  } catch (error) {
    console.error('Get role templates error:', error)
    return res.status(500).json({
      success: false,
      message: '获取角色模板失败'
    })
  }
}

// 从模板创建角色
export const createRoleFromTemplate = async (req: Request, res: Response) => {
  try {
    const data = createFromTemplateSchema.parse(req.body)
    const currentUser = (req as any).user

    const role = await RoleService.createRoleFromTemplate(
      data.templateName,
      data.customName,
      data.customDescription
    )

    // 记录操作日志
    await AuditService.log({
      userId: currentUser.userId,
      action: 'CREATE',
      resource: 'ROLE',
      resourceId: role.id,
      details: {
        action: 'create_from_template',
        templateName: data.templateName,
        roleName: role.name
      },
      ipAddress: req.ip
    })

    return res.status(201).json({
      success: true,
      message: '角色创建成功',
      data: role
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        message: '请求参数验证失败',
        errors: error.errors
      })
    }

    console.error('Create role from template error:', error)
    
    if (error instanceof Error) {
      if (error.message.includes('不存在')) {
        return res.status(404).json({
          success: false,
          message: error.message
        })
      }
      if (error.message.includes('已存在')) {
        return res.status(409).json({
          success: false,
          message: error.message
        })
      }
    }

    return res.status(500).json({
      success: false,
      message: '从模板创建角色失败'
    })
  }
}

// 复制角色
export const duplicateRole = async (req: Request, res: Response) => {
  try {
    const { id } = req.params
    const data = duplicateRoleSchema.parse(req.body)
    const currentUser = (req as any).user

    if (!id) {
      return res.status(400).json({
        success: false,
        message: 'ID参数缺失'
      })
    }

    const role = await RoleService.duplicateRole(
      id,
      data.newName,
      data.newDescription
    )

    // 记录操作日志
    await AuditService.log({
      userId: currentUser.userId,
      action: 'CREATE',
      resource: 'ROLE',
      resourceId: role.id,
      details: {
        action: 'duplicate_role',
        sourceRoleId: id,
        newRoleName: data.newName
      },
      ipAddress: req.ip
    })

    return res.status(201).json({
      success: true,
      message: '角色复制成功',
      data: role
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        message: '请求参数验证失败',
        errors: error.errors
      })
    }

    console.error('Duplicate role error:', error)
    
    if (error instanceof Error) {
      if (error.message.includes('不存在')) {
        return res.status(404).json({
          success: false,
          message: error.message
        })
      }
      if (error.message.includes('已存在')) {
        return res.status(409).json({
          success: false,
          message: error.message
        })
      }
    }

    return res.status(500).json({
      success: false,
      message: '角色复制失败'
    })
  }
}

// 获取角色统计信息
export const getRoleStats = async (_req: Request, res: Response) => {
  try {
    const stats = await RoleService.getRoleStats()

    return res.json({
      success: true,
      data: stats
    })
  } catch (error) {
    console.error('Get role stats error:', error)
    return res.status(500).json({
      success: false,
      message: '获取角色统计失败'
    })
  }
}

// 获取权限模板使用统计
export const getTemplateUsageStats = async (_req: Request, res: Response) => {
  try {
    const stats = await RoleService.getTemplateUsageStats()

    return res.json({
      success: true,
      data: stats
    })
  } catch (error) {
    console.error('Get template usage stats error:', error)
    return res.status(500).json({
      success: false,
      message: '获取模板使用统计失败'
    })
  }
}

// 比较角色与模板
export const compareRoleWithTemplate = async (req: Request, res: Response) => {
  try {
    const { id } = req.params
    const { templateName } = req.query

    if (!id) {
      return res.status(400).json({
        success: false,
        message: '角色ID参数缺失'
      })
    }

    if (!templateName) {
      return res.status(400).json({
        success: false,
        message: '模板名称参数缺失'
      })
    }

    const comparison = await RoleService.compareRoleWithTemplate(
      id, 
      templateName as keyof typeof RoleService['ROLE_TEMPLATES']
    )

    return res.json({
      success: true,
      data: comparison
    })
  } catch (error) {
    console.error('Compare role with template error:', error)
    
    if (error instanceof Error) {
      if (error.message.includes('不存在')) {
        return res.status(404).json({
          success: false,
          message: error.message
        })
      }
    }

    return res.status(500).json({
      success: false,
      message: '比较角色与模板失败'
    })
  }
}

// 获取角色权限分析
export const getRolePermissionAnalysis = async (req: Request, res: Response) => {
  try {
    const { id } = req.params

    if (!id) {
      return res.status(400).json({
        success: false,
        message: 'ID参数缺失'
      })
    }

    const analysis = await RoleService.getRolePermissionAnalysis(id)

    return res.json({
      success: true,
      data: analysis
    })
  } catch (error) {
    console.error('Get role permission analysis error:', error)
    
    if (error instanceof Error) {
      if (error.message.includes('不存在')) {
        return res.status(404).json({
          success: false,
          message: error.message
        })
      }
    }

    return res.status(500).json({
      success: false,
      message: '获取角色权限分析失败'
    })
  }
}

// 批量操作角色
export const batchOperationRoles = async (req: Request, res: Response) => {
  try {
    const data = batchOperationSchema.parse(req.body)
    const currentUser = (req as any).user

    const result = await RoleService.batchOperation(
      data.roleIds,
      data.operation,
      data.force
    )

    // 记录操作日志
    await AuditService.log({
      userId: currentUser.userId,
      action: 'BATCH_OPERATION',
      resource: 'ROLE',
      details: {
        operation: data.operation,
        roleIds: data.roleIds,
        affectedCount: result.successCount,
        errors: result.errors
      },
      ipAddress: req.ip
    })

    return res.json({
      success: true,
      message: `批量操作完成，成功处理 ${result.successCount} 个角色`,
      data: result
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        message: '请求参数验证失败',
        errors: error.errors
      })
    }

    console.error('Batch operation roles error:', error)
    
    if (error instanceof Error) {
      if (error.message.includes('权限不足')) {
        return res.status(403).json({
          success: false,
          message: error.message
        })
      }
    }

    return res.status(500).json({
      success: false,
      message: '批量操作失败'
    })
  }
}

// 导出角色数据
export const exportRoles = async (req: Request, res: Response) => {
  try {
    const data = exportRolesSchema.parse(req.query)
    const currentUser = (req as any).user

    const exportData = await RoleService.exportRoles(
      data.format,
      data.roleIds,
      data.includeUsers
    )

    // 记录操作日志
    await AuditService.log({
      userId: currentUser.userId,
      action: 'EXPORT',
      resource: 'ROLE',
      details: {
        format: data.format,
        includeUsers: data.includeUsers,
        roleCount: data.roleIds?.length || 'all'
      },
      ipAddress: req.ip
    })

    // 设置响应头
    const filename = `roles_${new Date().toISOString().split('T')[0]}.${data.format}`
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`)
    res.setHeader('Content-Type', 
      data.format === 'excel' 
        ? 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        : 'text/csv'
    )

    return res.send(exportData)
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        message: '请求参数验证失败',
        errors: error.errors
      })
    }

    console.error('Export roles error:', error)
    return res.status(500).json({
      success: false,
      message: '导出角色数据失败'
    })
  }
}

// 导入角色数据
export const importRoles = async (req: Request, res: Response) => {
  try {
    const file = req.file
    const currentUser = (req as any).user

    if (!file) {
      return res.status(400).json({
        success: false,
        message: '请上传文件'
      })
    }

    const result = await RoleService.importRoles(file.buffer, file.mimetype)

    // 记录操作日志
    await AuditService.log({
      userId: currentUser.userId,
      action: 'IMPORT',
      resource: 'ROLE',
      details: {
        filename: file.originalname,
        successCount: result.successCount,
        errorCount: result.errors.length,
        errors: result.errors
      },
      ipAddress: req.ip
    })

    return res.json({
      success: true,
      message: `导入完成，成功创建 ${result.successCount} 个角色`,
      data: result
    })
  } catch (error) {
    console.error('Import roles error:', error)
    
    if (error instanceof Error) {
      if (error.message.includes('格式不支持')) {
        return res.status(400).json({
          success: false,
          message: error.message
        })
      }
    }

    return res.status(500).json({
      success: false,
      message: '导入角色数据失败'
    })
  }
}

// 同步角色权限
export const syncRolePermissions = async (req: Request, res: Response) => {
  try {
    const currentUser = (req as any).user

    const result = await RoleService.syncRolePermissions()

    // 记录操作日志
    await AuditService.log({
      userId: currentUser.userId,
      action: 'SYNC',
      resource: 'ROLE',
      details: {
        syncedRoles: result.syncedRoles,
        updatedPermissions: result.updatedPermissions,
        removedPermissions: result.removedPermissions
      },
      ipAddress: req.ip
    })

    return res.json({
      success: true,
      message: '权限同步完成',
      data: result
    })
  } catch (error) {
    console.error('Sync role permissions error:', error)
    return res.status(500).json({
      success: false,
      message: '同步角色权限失败'
    })
  }
}