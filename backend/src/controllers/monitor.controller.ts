import { Request, Response } from 'express'
import { z } from 'zod'
import { MonitorService } from '@/services/monitor.service'
import { AuditService } from '@/services/audit.service'

// 验证Schema
const getMetricsQuerySchema = z.object({
  timeRange: z.string().optional().default('1h'),
  interval: z.enum(['minute', 'hour', 'day']).optional().default('minute'),
  metrics: z.string().optional().transform(val => val ? val.split(',') : undefined)
})

const getHistoricalDataSchema = z.object({
  metric: z.string(),
  hours: z.string().optional().transform(val => val ? parseInt(val) : 24),
  aggregation: z.enum(['avg', 'max', 'min', 'sum']).optional().default('avg')
})

const updateThresholdSchema = z.object({
  metric: z.string(),
  warning: z.number().min(0).max(100),
  critical: z.number().min(0).max(100),
  duration: z.number().min(1).max(3600)
})

// 获取监控仪表板数据
export const getDashboard = async (req: Request, res: Response) => {
  try {
    const monitor = MonitorService.getInstance()
    const detailParam = (req.query['detail'] as string) || 'lite'
    const sectionsParam = (req.query['sections'] as string) || ''
    const sections = sectionsParam ? sectionsParam.split(',').map(s => s.trim()).filter(Boolean) : undefined
    const dashboard = await monitor.getDashboardCached({ detail: detailParam === 'full' ? 'full' : 'lite', sections })

    return res.json({
      success: true,
      data: dashboard
    })
  } catch (error) {
    console.error('Get dashboard error:', error)
    return res.status(500).json({
      success: false,
      message: '获取监控仪表板失败'
    })
  }
}

// 获取实时系统指标
export const getSystemMetrics = async (_req: Request, res: Response) => {
  try {
    const monitor = MonitorService.getInstance()
    const metrics = await monitor.collectSystemMetrics()

    return res.json({
      success: true,
      data: metrics
    })
  } catch (error) {
    console.error('Get system metrics error:', error)
    return res.status(500).json({
      success: false,
      message: '获取系统指标失败'
    })
  }
}

// 获取数据库健康状态
export const getDatabaseHealth = async (_req: Request, res: Response) => {
  try {
    const monitor = MonitorService.getInstance()
    const health = await monitor.getDatabaseHealth()

    return res.json({
      success: true,
      data: health
    })
  } catch (error) {
    console.error('Get database health error:', error)
    return res.status(500).json({
      success: false,
      message: '获取数据库健康状态失败'
    })
  }
}

// 获取Redis健康状态
export const getRedisHealth = async (_req: Request, res: Response) => {
  try {
    const monitor = MonitorService.getInstance()
    const health = await monitor.getRedisHealth()

    return res.json({
      success: true,
      data: health
    })
  } catch (error) {
    console.error('Get Redis health error:', error)
    return res.status(500).json({
      success: false,
      message: '获取Redis健康状态失败'
    })
  }
}

// 获取应用健康状态
export const getApplicationHealth = async (_req: Request, res: Response) => {
  try {
    const monitor = MonitorService.getInstance()
    const health = await monitor.getApplicationHealth()

    return res.json({
      success: true,
      data: health
    })
  } catch (error) {
    console.error('Get application health error:', error)
    return res.status(500).json({
      success: false,
      message: '获取应用健康状态失败'
    })
  }
}

// 获取智能分析结果
export const getIntelligentAnalysis = async (_req: Request, res: Response) => {
  try {
    const monitor = MonitorService.getInstance()
    const analysis = await monitor.performIntelligentAnalysis()

    return res.json({
      success: true,
      data: analysis
    })
  } catch (error) {
    console.error('Get intelligent analysis error:', error)
    return res.status(500).json({
      success: false,
      message: '获取智能分析失败'
    })
  }
}

// 获取性能基准对比
export const getPerformanceBenchmarks = async (_req: Request, res: Response) => {
  try {
    const monitor = MonitorService.getInstance()
    const benchmarks = await monitor.getPerformanceBenchmarks()

    return res.json({
      success: true,
      data: benchmarks
    })
  } catch (error) {
    console.error('Get performance benchmarks error:', error)
    return res.status(500).json({
      success: false,
      message: '获取性能基准对比失败'
    })
  }
}

// 获取资源预警
export const getResourceAlerts = async (_req: Request, res: Response) => {
  try {
    const monitor = MonitorService.getInstance()
    const alerts = await monitor.getResourceAlerts()

    return res.json({
      success: true,
      data: alerts
    })
  } catch (error) {
    console.error('Get resource alerts error:', error)
    return res.status(500).json({
      success: false,
      message: '获取资源预警失败'
    })
  }
}

// 获取历史监控数据
export const getHistoricalMetrics = async (req: Request, res: Response) => {
  try {
    const { metric, hours, aggregation } = getHistoricalDataSchema.parse(req.query)
    const monitor = MonitorService.getInstance()
    
    // 从内存获取历史数据（实际生产环境应该从时间序列数据库获取）
    const historicalData = monitor.getHistoricalData(metric, hours, aggregation)

    return res.json({
      success: true,
      data: historicalData
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        message: '请求参数验证失败',
        errors: error.errors
      })
    }

    console.error('Get historical metrics error:', error)
    return res.status(500).json({
      success: false,
      message: '获取历史监控数据失败'
    })
  }
}

// 获取服务可用性统计
export const getAvailabilityStats = async (req: Request, res: Response) => {
  try {
    const { days = '7' } = req.query
    const daysNumber = parseInt(days as string) || 7
    
    const monitor = MonitorService.getInstance()
    const stats = await monitor.getAvailabilityStats(daysNumber)

    return res.json({
      success: true,
      data: stats
    })
  } catch (error) {
    console.error('Get availability stats error:', error)
    return res.status(500).json({
      success: false,
      message: '获取服务可用性统计失败'
    })
  }
}

// 获取系统事件日志
export const getSystemEvents = async (req: Request, res: Response) => {
  try {
    const { page = '1', limit = '20', level, type } = req.query
    const pageNumber = parseInt(page as string) || 1
    const limitNumber = parseInt(limit as string) || 20
    
    const monitor = MonitorService.getInstance()
    const events = await monitor.getSystemEvents({
      page: pageNumber,
      limit: limitNumber,
      level: level as string,
      type: type as string
    })

    return res.json({
      success: true,
      data: events
    })
  } catch (error) {
    console.error('Get system events error:', error)
    return res.status(500).json({
      success: false,
      message: '获取系统事件日志失败'
    })
  }
}

// 运行系统诊断
export const runSystemDiagnostics = async (req: Request, res: Response) => {
  try {
    const currentUser = (req as any).user
    const monitor = MonitorService.getInstance()
    const diagnostics = await monitor.runSystemDiagnostics()

    // 记录操作日志
    await AuditService.log({
      userId: currentUser.userId,
      action: 'EXECUTE',
      resource: 'SYSTEM_DIAGNOSTICS',
      resourceId: 'diagnostics',
      details: { testsRun: diagnostics.tests.length },
      ipAddress: req.ip
    })

    return res.json({
      success: true,
      data: diagnostics
    })
  } catch (error) {
    console.error('Run system diagnostics error:', error)
    return res.status(500).json({
      success: false,
      message: '运行系统诊断失败'
    })
  }
}

// 生成监控报告
export const generateReport = async (req: Request, res: Response) => {
  try {
    const { type = 'daily', format = 'json' } = req.query
    const currentUser = (req as any).user
    
    const monitor = MonitorService.getInstance()
    const report = await monitor.generateReport(type as string)

    // 记录操作日志
    await AuditService.log({
      userId: currentUser.userId,
      action: 'GENERATE',
      resource: 'MONITORING_REPORT',
      resourceId: 'report',
      details: { type, format },
      ipAddress: req.ip
    })

    if (format === 'pdf') {
      // 这里可以集成PDF生成库
      return res.status(501).json({
        success: false,
        message: 'PDF格式暂未支持'
      })
    }

    return res.json({
      success: true,
      data: report
    })
  } catch (error) {
    console.error('Generate report error:', error)
    return res.status(500).json({
      success: false,
      message: '生成监控报告失败'
    })
  }
}

// 更新监控阈值
export const updateThreshold = async (req: Request, res: Response) => {
  try {
    const data = updateThresholdSchema.parse(req.body)
    const currentUser = (req as any).user
    
    const monitor = MonitorService.getInstance()
    const threshold = await monitor.updateThreshold(data)

    // 记录操作日志
    await AuditService.log({
      userId: currentUser.userId,
      action: 'UPDATE',
      resource: 'MONITORING_THRESHOLD',
      resourceId: data.metric,
      details: data,
      ipAddress: req.ip
    })

    return res.json({
      success: true,
      message: '阈值更新成功',
      data: threshold
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        message: '请求参数验证失败',
        errors: error.errors
      })
    }

    console.error('Update threshold error:', error)
    return res.status(500).json({
      success: false,
      message: '更新监控阈值失败'
    })
  }
}

// 获取监控配置
export const getMonitoringConfig = async (_req: Request, res: Response) => {
  try {
    const monitor = MonitorService.getInstance()
    const config = await monitor.getMonitoringConfig()

    return res.json({
      success: true,
      data: config
    })
  } catch (error) {
    console.error('Get monitoring config error:', error)
    return res.status(500).json({
      success: false,
      message: '获取监控配置失败'
    })
  }
}

// 更新监控配置
export const updateMonitoringConfig = async (req: Request, res: Response) => {
  try {
    const currentUser = (req as any).user
    const monitor = MonitorService.getInstance()
    const config = await monitor.updateMonitoringConfig(req.body, currentUser.userId)

    // 记录操作日志
    await AuditService.log({
      userId: currentUser.userId,
      action: 'UPDATE',
      resource: 'MONITORING_CONFIG',
      resourceId: 'config',
      details: req.body,
      ipAddress: req.ip
    })

    return res.json({
      success: true,
      message: '监控配置更新成功',
      data: config
    })
  } catch (error) {
    console.error('Update monitoring config error:', error)
    return res.status(500).json({
      success: false,
      message: '更新监控配置失败'
    })
  }
}
