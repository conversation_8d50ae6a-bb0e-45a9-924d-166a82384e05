import { Request, Response } from 'express'
import { z } from 'zod'
import { RealtimeService } from '@/services/realtime.service'
import { WebSocketService } from '@/services/websocket.service'
import { AuditService } from '@/services/audit.service'
import {
  RealtimeEventType,
  RealtimeChannelType,
  RealtimePriority
} from '@shared/types/realtime'

// 验证Schema
const pushSystemMetricsSchema = z.object({
  cpu: z.number().min(0).max(100),
  memory: z.number().min(0).max(100),
  disk: z.number().min(0).max(100),
  network: z.number().min(0).max(100)
})

const pushSystemStatusSchema = z.object({
  overall: z.enum(['healthy', 'warning', 'critical']),
  services: z.array(z.object({
    name: z.string(),
    status: z.enum(['healthy', 'warning', 'critical', 'down']),
    responseTime: z.number(),
    message: z.string().optional()
  }))
})

const pushSystemAlertSchema = z.object({
  title: z.string().min(1).max(200),
  message: z.string().min(1).max(1000),
  severity: z.enum(['LOW', 'MEDIUM', 'HIGH', 'CRITICAL']),
  component: z.string().min(1).max(100),
  status: z.enum(['PENDING', 'ACKNOWLEDGED', 'RESOLVED']).default('PENDING')
})

const pushNotificationSchema = z.object({
  type: z.enum(['info', 'warning', 'error', 'success']),
  title: z.string().min(1).max(200),
  message: z.string().min(1).max(1000),
  targetUserId: z.string().optional(),
  actionUrl: z.string().url().optional(),
  persistent: z.boolean().default(false)
})

const pushServiceEventSchema = z.object({
  eventType: z.enum(['SERVICE_CREATED', 'SERVICE_UPDATED', 'SERVICE_STATUS_CHANGED', 'SERVICE_ASSIGNED']),
  serviceData: z.object({
    id: z.string(),
    ticketNumber: z.string(),
    title: z.string(),
    status: z.string(),
    priority: z.string(),
    assignedTo: z.string().optional(),
    customerName: z.string(),
    updatedBy: z.string(),
    changes: z.record(z.any()).optional()
  })
})

const broadcastMessageSchema = z.object({
  type: z.nativeEnum(RealtimeEventType),
  channel: z.nativeEnum(RealtimeChannelType),
  priority: z.nativeEnum(RealtimePriority).default(RealtimePriority.NORMAL),
  data: z.any(),
  targetUserId: z.string().optional(),
  strategy: z.object({
    channels: z.array(z.nativeEnum(RealtimeChannelType)).optional(),
    priority: z.nativeEnum(RealtimePriority).optional(),
    throttle: z.object({
      enabled: z.boolean(),
      interval: z.number(),
      maxPerInterval: z.number()
    }).optional(),
    filter: z.object({
      userRoles: z.array(z.string()).optional(),
      permissions: z.array(z.string()).optional(),
      conditions: z.record(z.any()).optional()
    }).optional()
  }).optional()
})

/**
 * 获取实时推送统计信息
 */
export const getRealtimeStats = async (req: Request, res: Response) => {
  try {
    const currentUser = (req as any).user
    
    // 检查权限
    if (!currentUser.permissions.includes('system:monitor') && !currentUser.roles.includes('admin')) {
      return res.status(403).json({
        success: false,
        message: '没有权限查看实时推送统计'
      })
    }

    const realtimeService = RealtimeService.getInstance()
    const stats = realtimeService.getRealtimeStats()

    return res.json({
      success: true,
      data: stats
    })
  } catch (error) {
    console.error('Get realtime stats error:', error)
    return res.status(500).json({
      success: false,
      message: '获取实时推送统计失败'
    })
  }
}

/**
 * 获取在线用户列表
 */
export const getOnlineUsers = async (req: Request, res: Response) => {
  try {
    const currentUser = (req as any).user
    
    // 检查权限
    if (!currentUser.permissions.includes('user:view') && !currentUser.roles.includes('admin')) {
      return res.status(403).json({
        success: false,
        message: '没有权限查看在线用户'
      })
    }

    const realtimeService = RealtimeService.getInstance()
    const onlineUsers = realtimeService.getOnlineUsers()

    return res.json({
      success: true,
      data: {
        count: onlineUsers.length,
        users: onlineUsers
      }
    })
  } catch (error) {
    console.error('Get online users error:', error)
    return res.status(500).json({
      success: false,
      message: '获取在线用户失败'
    })
  }
}

/**
 * 推送系统监控指标
 */
export const pushSystemMetrics = async (req: Request, res: Response) => {
  try {
    const data = pushSystemMetricsSchema.parse(req.body)
    const currentUser = (req as any).user

    // 检查权限
    if (!currentUser.permissions.includes('system:monitor') && !currentUser.roles.includes('admin')) {
      return res.status(403).json({
        success: false,
        message: '没有权限推送系统监控数据'
      })
    }

    const realtimeService = RealtimeService.getInstance()
    const sentCount = await realtimeService.pushSystemMetrics(data)

    // 记录操作日志
    await AuditService.log({
      userId: currentUser.userId,
      action: 'PUSH',
      resource: 'SYSTEM_METRICS',
      resourceId: 'realtime',
      details: {
        metrics: data,
        sentCount
      },
      ipAddress: req.ip
    })

    return res.json({
      success: true,
      message: `系统监控数据已推送给 ${sentCount} 个客户端`,
      data: { sentCount }
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        message: '请求参数验证失败',
        errors: error.errors
      })
    }

    console.error('Push system metrics error:', error)
    return res.status(500).json({
      success: false,
      message: '推送系统监控数据失败'
    })
  }
}

/**
 * 推送系统状态变更
 */
export const pushSystemStatus = async (req: Request, res: Response) => {
  try {
    const data = pushSystemStatusSchema.parse(req.body)
    const currentUser = (req as any).user

    // 检查权限
    if (!currentUser.permissions.includes('system:monitor') && !currentUser.roles.includes('admin')) {
      return res.status(403).json({
        success: false,
        message: '没有权限推送系统状态'
      })
    }

    const realtimeService = RealtimeService.getInstance()
    const sentCount = await realtimeService.pushSystemStatusChange(data)

    // 记录操作日志
    await AuditService.log({
      userId: currentUser.userId,
      action: 'PUSH',
      resource: 'SYSTEM_STATUS',
      resourceId: 'realtime',
      details: {
        status: data,
        sentCount
      },
      ipAddress: req.ip
    })

    return res.json({
      success: true,
      message: `系统状态变更已推送给 ${sentCount} 个客户端`,
      data: { sentCount }
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        message: '请求参数验证失败',
        errors: error.errors
      })
    }

    console.error('Push system status error:', error)
    return res.status(500).json({
      success: false,
      message: '推送系统状态失败'
    })
  }
}

/**
 * 推送系统告警
 */
export const pushSystemAlert = async (req: Request, res: Response) => {
  try {
    const data = pushSystemAlertSchema.parse(req.body)
    const currentUser = (req as any).user

    // 检查权限
    if (!currentUser.permissions.includes('system:monitor') && !currentUser.roles.includes('admin')) {
      return res.status(403).json({
        success: false,
        message: '没有权限推送系统告警'
      })
    }

    const alertData = {
      id: `alert_${Date.now()}`,
      ...data,
      acknowledgedBy: data.status === 'ACKNOWLEDGED' ? currentUser.userId : undefined,
      resolvedBy: data.status === 'RESOLVED' ? currentUser.userId : undefined
    }

    const realtimeService = RealtimeService.getInstance()
    const sentCount = await realtimeService.pushSystemAlert(alertData)

    // 记录操作日志
    await AuditService.log({
      userId: currentUser.userId,
      action: 'PUSH',
      resource: 'SYSTEM_ALERT',
      resourceId: alertData.id,
      details: {
        alert: alertData,
        sentCount
      },
      ipAddress: req.ip
    })

    return res.json({
      success: true,
      message: `系统告警已推送给 ${sentCount} 个客户端`,
      data: { sentCount, alertId: alertData.id }
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        message: '请求参数验证失败',
        errors: error.errors
      })
    }

    console.error('Push system alert error:', error)
    return res.status(500).json({
      success: false,
      message: '推送系统告警失败'
    })
  }
}

/**
 * 推送通知
 */
export const pushNotification = async (req: Request, res: Response) => {
  try {
    const data = pushNotificationSchema.parse(req.body)
    const currentUser = (req as any).user

    const notificationData = {
      id: `notification_${Date.now()}`,
      ...data
    }

    const realtimeService = RealtimeService.getInstance()
    const sentCount = await realtimeService.pushNotification(notificationData)

    // 记录操作日志
    await AuditService.log({
      userId: currentUser.userId,
      action: 'PUSH',
      resource: 'NOTIFICATION',
      resourceId: notificationData.id,
      details: {
        notification: notificationData,
        sentCount
      },
      ipAddress: req.ip
    })

    return res.json({
      success: true,
      message: `通知已推送给 ${sentCount} 个客户端`,
      data: { sentCount, notificationId: notificationData.id }
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        message: '请求参数验证失败',
        errors: error.errors
      })
    }

    console.error('Push notification error:', error)
    return res.status(500).json({
      success: false,
      message: '推送通知失败'
    })
  }
}

/**
 * 推送服务工单事件
 */
export const pushServiceEvent = async (req: Request, res: Response) => {
  try {
    const { eventType, serviceData } = pushServiceEventSchema.parse(req.body)
    const currentUser = (req as any).user

    const realtimeService = RealtimeService.getInstance()
    const sentCount = await realtimeService.pushServiceEvent(
      eventType as any,
      serviceData
    )

    // 记录操作日志
    await AuditService.log({
      userId: currentUser.userId,
      action: 'PUSH',
      resource: 'SERVICE_EVENT',
      resourceId: serviceData.id,
      details: {
        eventType,
        serviceData,
        sentCount
      },
      ipAddress: req.ip
    })

    return res.json({
      success: true,
      message: `服务工单事件已推送给 ${sentCount} 个客户端`,
      data: { sentCount }
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        message: '请求参数验证失败',
        errors: error.errors
      })
    }

    console.error('Push service event error:', error)
    return res.status(500).json({
      success: false,
      message: '推送服务工单事件失败'
    })
  }
}

/**
 * 广播自定义消息
 */
export const broadcastMessage = async (req: Request, res: Response) => {
  try {
    const data = broadcastMessageSchema.parse(req.body)
    const currentUser = (req as any).user

    // 检查权限 - 只有管理员可以广播自定义消息
    if (!currentUser.roles.includes('admin') && !currentUser.roles.includes('系统管理员')) {
      return res.status(403).json({
        success: false,
        message: '没有权限广播消息'
      })
    }

    const message = {
      id: `broadcast_${Date.now()}`,
      type: data.type,
      channel: data.channel,
      priority: data.priority,
      timestamp: new Date().toISOString(),
      data: data.data,
      metadata: {
        userId: currentUser.userId,
        source: 'admin_broadcast'
      }
    }

    const webSocketService = WebSocketService.getInstance()
    let sentCount = 0

    if (data.targetUserId) {
      // 发送给特定用户
      sentCount = webSocketService.sendToUser(data.targetUserId, message)
    } else if (data.channel) {
      // 发送给特定频道
      sentCount = webSocketService.broadcastToChannel(data.channel, message, data.strategy)
    } else {
      // 广播给所有用户
      sentCount = webSocketService.broadcast(message, data.strategy)
    }

    // 记录操作日志
    await AuditService.log({
      userId: currentUser.userId,
      action: 'BROADCAST',
      resource: 'CUSTOM_MESSAGE',
      resourceId: message.id,
      details: {
        message: {
          ...message,
          data: typeof message.data === 'object' ? '[Object]' : message.data
        },
        targetUserId: data.targetUserId,
        channel: data.channel,
        sentCount
      },
      ipAddress: req.ip
    })

    return res.json({
      success: true,
      message: `消息已广播给 ${sentCount} 个客户端`,
      data: { sentCount, messageId: message.id }
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        message: '请求参数验证失败',
        errors: error.errors
      })
    }

    console.error('Broadcast message error:', error)
    return res.status(500).json({
      success: false,
      message: '广播消息失败'
    })
  }
}

/**
 * 测试连接
 */
export const testConnection = async (req: Request, res: Response) => {
  try {
    const currentUser = (req as any).user
    
    const testMessage = {
      id: `test_${Date.now()}`,
      type: RealtimeEventType.HEARTBEAT,
      channel: RealtimeChannelType.USER,
      priority: RealtimePriority.LOW,
      timestamp: new Date().toISOString(),
      data: {
        message: 'WebSocket连接测试',
        timestamp: new Date().toISOString(),
        from: 'server'
      }
    }

    const webSocketService = WebSocketService.getInstance()
    const sentCount = webSocketService.sendToUser(currentUser.userId, testMessage)

    return res.json({
      success: true,
      message: sentCount > 0 ? 'WebSocket连接正常' : '用户未连接WebSocket',
      data: { 
        sentCount, 
        connected: sentCount > 0,
        messageId: testMessage.id 
      }
    })
  } catch (error) {
    console.error('Test connection error:', error)
    return res.status(500).json({
      success: false,
      message: '测试连接失败'
    })
  }
}

/**
 * 获取WebSocket连接详情
 */
export const getConnectionDetails = async (req: Request, res: Response) => {
  try {
    const currentUser = (req as any).user
    
    // 检查权限
    if (!currentUser.permissions.includes('system:monitor') && !currentUser.roles.includes('admin')) {
      return res.status(403).json({
        success: false,
        message: '没有权限查看连接详情'
      })
    }

    const webSocketService = WebSocketService.getInstance()
    const onlineUsers = webSocketService.getOnlineUsers()
    const stats = webSocketService.getStats()

    return res.json({
      success: true,
      data: {
        onlineUsers,
        stats,
        summary: {
          totalConnections: stats.connections.total,
          activeConnections: stats.connections.active,
          idleConnections: stats.connections.idle,
          messagesSent: stats.messages.sent,
          messagesReceived: stats.messages.received,
          uptime: stats.uptime
        }
      }
    })
  } catch (error) {
    console.error('Get connection details error:', error)
    return res.status(500).json({
      success: false,
      message: '获取连接详情失败'
    })
  }
}