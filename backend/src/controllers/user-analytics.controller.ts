import { Request, Response } from 'express'
import { UserAnalyticsService } from '@/services/user-analytics.service'
import { AuditService } from '@/services/audit.service'
import { prisma } from '@/config/database.config'

export class UserAnalyticsController {
  
  /**
   * 获取用户活动概览
   */
  static async getActivitySummary(req: Request, res: Response) {
    try {
      const { startDate, endDate } = req.query
      
      const start = startDate ? new Date(startDate as string) : undefined
      const end = endDate ? new Date(endDate as string) : undefined
      
      const summary = await UserAnalyticsService.getActivitySummary(start, end)
      
      // 记录操作日志
      await AuditService.log({
        userId: req.user!.id,
        action: 'VIEW',
        resource: 'USER_ANALYTICS',
        details: { type: 'activity_summary', dateRange: { startDate: start, endDate: end } },
        ipAddress: req.ip
      })
      
      res.json({
        success: true,
        data: summary
      })
    } catch (error) {
      console.error('获取用户活动概览失败:', error)
      res.status(500).json({
        success: false,
        message: '获取用户活动概览失败'
      })
    }
  }

  /**
   * 获取在线用户列表
   */
  static async getOnlineUsers(req: Request, res: Response) {
    try {
      const onlineUsers = await UserAnalyticsService.getOnlineUsers()
      
      res.json({
        success: true,
        data: {
          count: onlineUsers.length,
          users: onlineUsers
        }
      })
    } catch (error) {
      console.error('获取在线用户失败:', error)
      res.status(500).json({
        success: false,
        message: '获取在线用户失败'
      })
    }
  }

  /**
   * 获取用户行为分析
   */
  static async getUserBehaviorAnalysis(req: Request, res: Response) {
    try {
      const { userId } = req.params
      const { days = 30 } = req.query
      
      if (!userId) {
        return res.status(400).json({
          success: false,
          message: '用户ID不能为空'
        })
      }
      
      const analysis = await UserAnalyticsService.getUserBehaviorAnalysis(
        userId, 
        parseInt(days as string)
      )
      
      // 记录操作日志
      await AuditService.log({
        userId: req.user!.id,
        action: 'VIEW',
        resource: 'USER_BEHAVIOR',
        resourceId: userId,
        details: { days: parseInt(days as string) },
        ipAddress: req.ip
      })
      
      res.json({
        success: true,
        data: analysis
      })
    } catch (error) {
      console.error('获取用户行为分析失败:', error)
      res.status(500).json({
        success: false,
        message: '获取用户行为分析失败'
      })
    }
  }

  /**
   * 获取用户活动热力图数据
   */
  static async getActivityHeatmap(req: Request, res: Response) {
    try {
      const { userId, days = 30 } = req.query
      
      const heatmapData = await UserAnalyticsService.getActivityHeatmap(
        userId as string,
        parseInt(days as string)
      )
      
      res.json({
        success: true,
        data: heatmapData
      })
    } catch (error) {
      console.error('获取活动热力图失败:', error)
      res.status(500).json({
        success: false,
        message: '获取活动热力图失败'
      })
    }
  }

  /**
   * 获取用户活动统计
   */
  static async getUserActivityStats(req: Request, res: Response) {
    try {
      const { 
        page = 1, 
        limit = 20, 
        userId, 
        startDate, 
        endDate, 
        sortBy = 'activityScore',
        sortOrder = 'desc'
      } = req.query
      
      const skip = (parseInt(page as string) - 1) * parseInt(limit as string)
      const take = parseInt(limit as string)
      
      const where: any = {}
      
      if (userId) {
        where.userId = userId
      }
      
      if (startDate || endDate) {
        where.date = {}
        if (startDate) where.date.gte = new Date(startDate as string)
        if (endDate) where.date.lte = new Date(endDate as string)
      }
      
      const orderBy: any = {}
      orderBy[sortBy as string] = sortOrder
      
      const [stats, total] = await Promise.all([
        prisma.userActivityStats.findMany({
          where,
          skip,
          take,
          include: {
            user: {
              select: {
                id: true,
                username: true,
                fullName: true,
                email: true,
                department: true,
                avatar: true
              }
            }
          },
          orderBy
        }),
        prisma.userActivityStats.count({ where })
      ])
      
      res.json({
        success: true,
        data: {
          stats,
          pagination: {
            page: parseInt(page as string),
            limit: parseInt(limit as string),
            total,
            totalPages: Math.ceil(total / parseInt(limit as string))
          }
        }
      })
    } catch (error) {
      console.error('获取用户活动统计失败:', error)
      res.status(500).json({
        success: false,
        message: '获取用户活动统计失败'
      })
    }
  }

  /**
   * 获取用户行为模式
   */
  static async getUserBehaviorPatterns(req: Request, res: Response) {
    try {
      const { userId } = req.params
      
      if (!userId) {
        return res.status(400).json({
          success: false,
          message: '用户ID不能为空'
        })
      }
      
      const patterns = await prisma.userBehaviorPattern.findMany({
        where: { userId },
        orderBy: { lastUpdated: 'desc' }
      })
      
      res.json({
        success: true,
        data: patterns
      })
    } catch (error) {
      console.error('获取用户行为模式失败:', error)
      res.status(500).json({
        success: false,
        message: '获取用户行为模式失败'
      })
    }
  }

  /**
   * 获取用户活动异常
   */
  static async getUserAnomalies(req: Request, res: Response) {
    try {
      const { 
        page = 1, 
        limit = 20, 
        userId, 
        severity, 
        anomalyType,
        isResolved 
      } = req.query
      
      const skip = (parseInt(page as string) - 1) * parseInt(limit as string)
      const take = parseInt(limit as string)
      
      const where: any = {}
      
      if (userId) where.userId = userId
      if (severity) where.severity = severity
      if (anomalyType) where.anomalyType = anomalyType
      if (isResolved !== undefined) where.isResolved = isResolved === 'true'
      
      const [anomalies, total] = await Promise.all([
        prisma.userActivityAnomaly.findMany({
          where,
          skip,
          take,
          include: {
            user: {
              select: {
                id: true,
                username: true,
                fullName: true,
                department: true
              }
            },
            resolvedByUser: {
              select: {
                id: true,
                username: true,
                fullName: true
              }
            }
          },
          orderBy: { detectedAt: 'desc' }
        }),
        prisma.userActivityAnomaly.count({ where })
      ])
      
      res.json({
        success: true,
        data: {
          anomalies,
          pagination: {
            page: parseInt(page as string),
            limit: parseInt(limit as string),
            total,
            totalPages: Math.ceil(total / parseInt(limit as string))
          }
        }
      })
    } catch (error) {
      console.error('获取用户活动异常失败:', error)
      res.status(500).json({
        success: false,
        message: '获取用户活动异常失败'
      })
    }
  }

  /**
   * 标记异常为已解决
   */
  static async resolveAnomaly(req: Request, res: Response) {
    try {
      const { anomalyId } = req.params
      const { notes } = req.body
      
      if (!anomalyId) {
        return res.status(400).json({
          success: false,
          message: '异常ID不能为空'
        })
      }
      
      const anomaly = await prisma.userActivityAnomaly.update({
        where: { id: anomalyId },
        data: {
          isResolved: true,
          resolvedAt: new Date(),
          resolvedBy: req.user!.id,
          notes
        },
        include: {
          user: {
            select: {
              username: true,
              fullName: true
            }
          }
        }
      })
      
      // 记录操作日志
      await AuditService.log({
        userId: req.user!.id,
        action: 'RESOLVE',
        resource: 'USER_ANOMALY',
        resourceId: anomalyId,
        details: { notes, targetUser: anomaly.user },
        ipAddress: req.ip
      })
      
      res.json({
        success: true,
        data: anomaly,
        message: '异常已标记为已解决'
      })
    } catch (error) {
      console.error('标记异常为已解决失败:', error)
      res.status(500).json({
        success: false,
        message: '标记异常为已解决失败'
      })
    }
  }

  /**
   * 触发异常检测
   */
  static async triggerAnomalyDetection(req: Request, res: Response) {
    try {
      const { userId } = req.body
      
      if (userId) {
        // 检测特定用户
        await UserAnalyticsService.detectAnomalies(userId)
      } else {
        // 检测所有活跃用户
        const activeUsers = await prisma.user.findMany({
          where: { status: 'ACTIVE' },
          select: { id: true }
        })
        
        await Promise.all(
          activeUsers.map(user => 
            UserAnalyticsService.detectAnomalies(user.id)
          )
        )
      }
      
      // 记录操作日志
      await AuditService.log({
        userId: req.user!.id,
        action: 'TRIGGER',
        resource: 'ANOMALY_DETECTION',
        details: { targetUserId: userId },
        ipAddress: req.ip
      })
      
      res.json({
        success: true,
        message: '异常检测已触发'
      })
    } catch (error) {
      console.error('触发异常检测失败:', error)
      res.status(500).json({
        success: false,
        message: '触发异常检测失败'
      })
    }
  }

  /**
   * 更新用户行为模式
   */
  static async updateBehaviorPatterns(req: Request, res: Response) {
    try {
      const { userId } = req.body
      
      if (userId) {
        await UserAnalyticsService.updateBehaviorPatterns(userId)
      } else {
        // 更新所有活跃用户的行为模式
        const activeUsers = await prisma.user.findMany({
          where: { status: 'ACTIVE' },
          select: { id: true }
        })
        
        await Promise.all(
          activeUsers.map(user => 
            UserAnalyticsService.updateBehaviorPatterns(user.id)
          )
        )
      }
      
      // 记录操作日志
      await AuditService.log({
        userId: req.user!.id,
        action: 'UPDATE',
        resource: 'BEHAVIOR_PATTERNS',
        details: { targetUserId: userId },
        ipAddress: req.ip
      })
      
      res.json({
        success: true,
        message: '用户行为模式更新完成'
      })
    } catch (error) {
      console.error('更新用户行为模式失败:', error)
      res.status(500).json({
        success: false,
        message: '更新用户行为模式失败'
      })
    }
  }

  /**
   * 导出用户活动报告
   */
  static async exportActivityReport(req: Request, res: Response) {
    try {
      const { startDate, endDate, userIds, format = 'json' } = req.query
      
      if (!startDate || !endDate) {
        return res.status(400).json({
          success: false,
          message: '开始时间和结束时间不能为空'
        })
      }
      
      const start = new Date(startDate as string)
      const end = new Date(endDate as string)
      const users = userIds ? (userIds as string).split(',') : undefined
      
      const report = await UserAnalyticsService.exportActivityReport(start, end, users)
      
      // 记录操作日志
      await AuditService.log({
        userId: req.user!.id,
        action: 'EXPORT',
        resource: 'ACTIVITY_REPORT',
        details: { 
          dateRange: { startDate: start, endDate: end },
          userCount: users?.length || 'all',
          format 
        },
        ipAddress: req.ip
      })
      
      if (format === 'csv') {
        res.setHeader('Content-Type', 'text/csv')
        res.setHeader('Content-Disposition', 'attachment; filename=activity-report.csv')
        // 这里需要实现CSV格式转换
        res.send('CSV format not implemented yet')
      } else {
        res.json({
          success: true,
          data: report
        })
      }
    } catch (error) {
      console.error('导出用户活动报告失败:', error)
      res.status(500).json({
        success: false,
        message: '导出用户活动报告失败'
      })
    }
  }

  /**
   * 获取用户排行榜
   */
  static async getUserRankings(req: Request, res: Response) {
    try {
      const { 
        type = 'activity', 
        period = 'week', 
        limit = 20 
      } = req.query
      
      let startDate: Date
      const endDate = new Date()
      
      switch (period) {
        case 'day':
          startDate = new Date(endDate.getTime() - 24 * 60 * 60 * 1000)
          break
        case 'week':
          startDate = new Date(endDate.getTime() - 7 * 24 * 60 * 60 * 1000)
          break
        case 'month':
          startDate = new Date(endDate.getTime() - 30 * 24 * 60 * 60 * 1000)
          break
        default:
          startDate = new Date(endDate.getTime() - 7 * 24 * 60 * 60 * 1000)
      }
      
      let orderBy: any
      let selectField: string
      
      switch (type) {
        case 'activity':
          orderBy = { activityScore: 'desc' }
          selectField = 'activityScore'
          break
        case 'login':
          orderBy = { loginCount: 'desc' }
          selectField = 'loginCount'
          break
        case 'duration':
          orderBy = { totalDurationMinutes: 'desc' }
          selectField = 'totalDurationMinutes'
          break
        case 'operation':
          orderBy = { operationCount: 'desc' }
          selectField = 'operationCount'
          break
        default:
          orderBy = { activityScore: 'desc' }
          selectField = 'activityScore'
      }
      
      const rankings = await prisma.userActivityStats.findMany({
        where: {
          date: {
            gte: startDate,
            lte: endDate
          }
        },
        include: {
          user: {
            select: {
              id: true,
              username: true,
              fullName: true,
              avatar: true,
              department: true
            }
          }
        },
        orderBy,
        take: parseInt(limit as string)
      })
      
      const result = rankings.map((stat, index) => ({
        rank: index + 1,
        user: stat.user,
        value: stat[selectField as keyof typeof stat],
        activityScore: stat.activityScore,
        loginCount: stat.loginCount,
        totalDurationMinutes: stat.totalDurationMinutes,
        operationCount: stat.operationCount
      }))
      
      res.json({
        success: true,
        data: {
          type,
          period,
          rankings: result
        }
      })
    } catch (error) {
      console.error('获取用户排行榜失败:', error)
      res.status(500).json({
        success: false,
        message: '获取用户排行榜失败'
      })
    }
  }
}