/**
 * 权限管理控制器
 * 
 * 提供权限查询、角色权限管理等功能
 */

import { Request, Response } from 'express'
import { z } from 'zod'
import { prisma } from '@/config/database.config'
import { AuthenticatedRequest } from '@/middleware/auth.middleware'
import { 
  getAllPermissions, 
  getPermissionGroups, 
  getPermissionDescription,
  validatePermissions,
  filterValidPermissions,
  DEFAULT_ROLE_PERMISSIONS,
  PERMISSION_DESCRIPTIONS,
  type Permission,
  type PermissionGroupName
} from '@/constants/permissions'

// ==================== 验证Schema ====================

const updateRolePermissionsSchema = z.object({
  permissions: z.array(z.string()).min(0, '权限列表不能为空')
})

const batchUpdatePermissionsSchema = z.object({
  roleUpdates: z.array(z.object({
    roleId: z.string().min(1, '角色ID不能为空'),
    permissions: z.array(z.string())
  })).min(1, '至少需要更新一个角色')
})

const permissionCheckSchema = z.object({
  userId: z.string().min(1, '用户ID不能为空'),
  permissions: z.array(z.string()).min(1, '权限列表不能为空')
})

// ==================== 权限查询接口 ====================

/**
 * 获取所有权限列表
 * @route GET /api/v1/permissions
 */
export const getAllPermissionsList = async (req: Request, res: Response) => {
  try {
    const permissions = getAllPermissions()
    const groups = getPermissionGroups()
    
    // 构建权限树结构
    const permissionTree = Object.entries(groups).map(([groupKey, groupInfo]) => ({
      key: groupKey,
      name: groupInfo.name,
      description: groupInfo.description,
      permissions: groupInfo.permissions.map(permission => ({
        key: permission,
        name: getPermissionDescription(permission),
        description: PERMISSION_DESCRIPTIONS[permission]
      }))
    }))

    res.json({
      success: true,
      data: {
        total: permissions.length,
        permissions: permissionTree,
        flatPermissions: permissions.map(permission => ({
          key: permission,
          name: getPermissionDescription(permission),
          description: PERMISSION_DESCRIPTIONS[permission]
        }))
      }
    })
  } catch (error: any) {
    console.error('获取权限列表失败:', error)
    res.status(500).json({
      success: false,
      message: '获取权限列表失败',
      error: error.message
    })
  }
}

/**
 * 获取权限分组
 * @route GET /api/v1/permissions/groups
 */
export const getPermissionGroupsList = async (req: Request, res: Response) => {
  try {
    const groups = getPermissionGroups()
    
    const groupsList = Object.entries(groups).map(([key, group]) => ({
      key,
      name: group.name,
      description: group.description,
      permissionCount: group.permissions.length,
      permissions: group.permissions
    }))

    res.json({
      success: true,
      data: groupsList
    })
  } catch (error: any) {
    console.error('获取权限分组失败:', error)
    res.status(500).json({
      success: false,
      message: '获取权限分组失败',
      error: error.message
    })
  }
}

/**
 * 获取角色权限映射
 * @route GET /api/v1/permissions/role-mapping
 */
export const getRolePermissionMapping = async (req: Request, res: Response) => {
  try {
    // 获取所有角色及其权限
    const roles = await prisma.role.findMany({
      select: {
        id: true,
        name: true,
        description: true,
        permissions: true,
        _count: {
          select: {
            userRoles: true
          }
        }
      }
    })

    // 处理权限数据
    const roleMapping = roles.map(role => {
      const permissions = typeof role.permissions === 'string' 
        ? JSON.parse(role.permissions) 
        : role.permissions as string[]
      
      const validPermissions = filterValidPermissions(permissions)

      return {
        id: role.id,
        name: role.name,
        description: role.description,
        permissions: validPermissions,
        permissionCount: validPermissions.length,
        userCount: role._count.userRoles,
        permissionDetails: validPermissions.map(permission => ({
          key: permission,
          name: getPermissionDescription(permission)
        }))
      }
    })

    res.json({
      success: true,
      data: roleMapping
    })
  } catch (error: any) {
    console.error('获取角色权限映射失败:', error)
    res.status(500).json({
      success: false,
      message: '获取角色权限映射失败',
      error: error.message
    })
  }
}

// ==================== 权限验证接口 ====================

/**
 * 验证权限格式
 * @route POST /api/v1/permissions/validate
 */
export const validatePermissionFormat = async (req: Request, res: Response) => {
  try {
    const { permissions }: { permissions: string[] } = req.body

    if (!Array.isArray(permissions)) {
      return res.status(400).json({
        success: false,
        message: '权限列表必须是数组'
      })
    }

    const validation = validatePermissions(permissions)

    res.json({
      success: true,
      data: {
        valid: validation.valid,
        total: permissions.length,
        validCount: permissions.length - validation.invalidPermissions.length - validation.unknownPermissions.length,
        invalidPermissions: validation.invalidPermissions,
        unknownPermissions: validation.unknownPermissions,
        details: {
          invalidFormat: validation.invalidPermissions,
          unknownInSystem: validation.unknownPermissions
        }
      }
    })
  } catch (error: any) {
    console.error('验证权限格式失败:', error)
    res.status(500).json({
      success: false,
      message: '验证权限格式失败',
      error: error.message
    })
  }
}

/**
 * 检查用户权限
 * @route POST /api/v1/permissions/check
 */
export const checkUserPermissions = async (req: Request, res: Response) => {
  try {
    const { userId, permissions } = permissionCheckSchema.parse(req.body)

    // 获取用户权限
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: {
        userRoles: {
          include: {
            role: {
              select: {
                name: true,
                permissions: true
              }
            }
          }
        }
      }
    })

    if (!user) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      })
    }

    // 获取用户所有权限
    const userPermissions = user.userRoles.reduce<string[]>((acc, ur) => {
      const rolePermissions = typeof ur.role.permissions === 'string' 
        ? JSON.parse(ur.role.permissions) 
        : ur.role.permissions
      return [...acc, ...rolePermissions]
    }, [])

    const uniqueUserPermissions = [...new Set(userPermissions)]

    // 检查每个权限
    const permissionCheck = permissions.map(permission => {
      const hasPermission = uniqueUserPermissions.includes('admin:all') || 
                           uniqueUserPermissions.includes(permission)
      return {
        permission,
        name: getPermissionDescription(permission),
        hasPermission,
        source: hasPermission ? (
          uniqueUserPermissions.includes('admin:all') ? 'super_admin' : 'direct'
        ) : null
      }
    })

    const hasAllPermissions = permissionCheck.every(item => item.hasPermission)

    res.json({
      success: true,
      data: {
        userId,
        username: user.username,
        hasAllPermissions,
        userPermissions: uniqueUserPermissions,
        permissionCheck,
        summary: {
          total: permissions.length,
          granted: permissionCheck.filter(item => item.hasPermission).length,
          denied: permissionCheck.filter(item => !item.hasPermission).length
        }
      }
    })
  } catch (error: any) {
    console.error('检查用户权限失败:', error)
    res.status(500).json({
      success: false,
      message: '检查用户权限失败',
      error: error.message
    })
  }
}

// ==================== 角色权限管理接口 ====================

/**
 * 更新角色权限
 * @route PUT /api/v1/permissions/roles/:roleId
 */
export const updateRolePermissions = async (req: Request, res: Response) => {
  try {
    const { roleId } = req.params
    const { permissions } = updateRolePermissionsSchema.parse(req.body)
    const authReq = req as AuthenticatedRequest

    // 验证角色是否存在
    const role = await prisma.role.findUnique({
      where: { id: roleId }
    })

    if (!role) {
      return res.status(404).json({
        success: false,
        message: '角色不存在'
      })
    }

    // 验证权限格式
    const validation = validatePermissions(permissions)
    if (!validation.valid) {
      return res.status(400).json({
        success: false,
        message: '权限格式无效',
        data: {
          invalidPermissions: validation.invalidPermissions,
          unknownPermissions: validation.unknownPermissions
        }
      })
    }

    // 过滤有效权限
    const validPermissions = filterValidPermissions(permissions)

    // 更新角色权限
    const updatedRole = await prisma.role.update({
      where: { id: roleId },
      data: {
        permissions: JSON.stringify(validPermissions)
      }
    })

    // 记录操作日志
    await prisma.operationLog.create({
      data: {
        userId: authReq.user!.id,
        action: 'UPDATE_ROLE_PERMISSIONS',
        resource: 'role',
        resourceId: roleId,
        details: {
          oldPermissions: typeof role.permissions === 'string' 
            ? JSON.parse(role.permissions) 
            : role.permissions,
          newPermissions: validPermissions,
          roleName: role.name
        }
      }
    })

    res.json({
      success: true,
      message: '角色权限更新成功',
      data: {
        roleId,
        roleName: role.name,
        permissions: validPermissions,
        permissionCount: validPermissions.length
      }
    })
  } catch (error: any) {
    console.error('更新角色权限失败:', error)
    res.status(500).json({
      success: false,
      message: '更新角色权限失败',
      error: error.message
    })
  }
}

/**
 * 批量更新角色权限
 * @route POST /api/v1/permissions/roles/batch-update
 */
export const batchUpdateRolePermissions = async (req: Request, res: Response) => {
  try {
    const { roleUpdates } = batchUpdatePermissionsSchema.parse(req.body)
    const authReq = req as AuthenticatedRequest

    // 验证所有角色是否存在
    const roleIds = roleUpdates.map(update => update.roleId)
    const roles = await prisma.role.findMany({
      where: { id: { in: roleIds } }
    })

    if (roles.length !== roleIds.length) {
      const existingRoleIds = roles.map(role => role.id)
      const missingRoleIds = roleIds.filter(id => !existingRoleIds.includes(id))
      
      return res.status(404).json({
        success: false,
        message: '部分角色不存在',
        data: { missingRoleIds }
      })
    }

    // 验证所有权限
    const allPermissions = roleUpdates.flatMap(update => update.permissions)
    const validation = validatePermissions(allPermissions)
    
    if (!validation.valid) {
      return res.status(400).json({
        success: false,
        message: '部分权限格式无效',
        data: {
          invalidPermissions: validation.invalidPermissions,
          unknownPermissions: validation.unknownPermissions
        }
      })
    }

    // 批量更新
    const updateResults = []
    
    for (const update of roleUpdates) {
      const role = roles.find(r => r.id === update.roleId)!
      const validPermissions = filterValidPermissions(update.permissions)
      
      const updatedRole = await prisma.role.update({
        where: { id: update.roleId },
        data: {
          permissions: JSON.stringify(validPermissions)
        }
      })

      // 记录操作日志
      await prisma.operationLog.create({
        data: {
          userId: authReq.user!.id,
          action: 'BATCH_UPDATE_ROLE_PERMISSIONS',
          resource: 'role',
          resourceId: update.roleId,
          details: {
            oldPermissions: typeof role.permissions === 'string' 
              ? JSON.parse(role.permissions) 
              : role.permissions,
            newPermissions: validPermissions,
            roleName: role.name
          }
        }
      })

      updateResults.push({
        roleId: update.roleId,
        roleName: role.name,
        permissions: validPermissions,
        permissionCount: validPermissions.length
      })
    }

    res.json({
      success: true,
      message: '批量更新角色权限成功',
      data: {
        updated: updateResults.length,
        results: updateResults
      }
    })
  } catch (error: any) {
    console.error('批量更新角色权限失败:', error)
    res.status(500).json({
      success: false,
      message: '批量更新角色权限失败',
      error: error.message
    })
  }
}

// ==================== 默认权限管理 ====================

/**
 * 获取默认角色权限配置
 * @route GET /api/v1/permissions/default-roles
 */
export const getDefaultRolePermissions = async (req: Request, res: Response) => {
  try {
    const defaultRoles = Object.entries(DEFAULT_ROLE_PERMISSIONS).map(([roleName, permissions]) => ({
      roleName,
      permissions,
      permissionCount: permissions.length,
      permissionDetails: permissions.map(permission => ({
        key: permission,
        name: getPermissionDescription(permission)
      }))
    }))

    res.json({
      success: true,
      data: defaultRoles
    })
  } catch (error: any) {
    console.error('获取默认角色权限失败:', error)
    res.status(500).json({
      success: false,
      message: '获取默认角色权限失败',
      error: error.message
    })
  }
}

/**
 * 重置角色权限为默认值
 * @route POST /api/v1/permissions/roles/:roleId/reset
 */
export const resetRolePermissions = async (req: Request, res: Response) => {
  try {
    const { roleId } = req.params
    const authReq = req as AuthenticatedRequest

    // 获取角色信息
    const role = await prisma.role.findUnique({
      where: { id: roleId }
    })

    if (!role) {
      return res.status(404).json({
        success: false,
        message: '角色不存在'
      })
    }

    // 获取默认权限
    const defaultPermissions = DEFAULT_ROLE_PERMISSIONS[role.name as keyof typeof DEFAULT_ROLE_PERMISSIONS]
    
    if (!defaultPermissions) {
      return res.status(400).json({
        success: false,
        message: '该角色没有预定义的默认权限配置'
      })
    }

    // 更新角色权限
    const updatedRole = await prisma.role.update({
      where: { id: roleId },
      data: {
        permissions: JSON.stringify(defaultPermissions)
      }
    })

    // 记录操作日志
    await prisma.operationLog.create({
      data: {
        userId: authReq.user!.id,
        action: 'RESET_ROLE_PERMISSIONS',
        resource: 'role',
        resourceId: roleId,
        details: {
          oldPermissions: typeof role.permissions === 'string' 
            ? JSON.parse(role.permissions) 
            : role.permissions,
          newPermissions: defaultPermissions,
          roleName: role.name
        }
      }
    })

    res.json({
      success: true,
      message: '角色权限已重置为默认配置',
      data: {
        roleId,
        roleName: role.name,
        permissions: defaultPermissions,
        permissionCount: defaultPermissions.length
      }
    })
  } catch (error: any) {
    console.error('重置角色权限失败:', error)
    res.status(500).json({
      success: false,
      message: '重置角色权限失败',
      error: error.message
    })
  }
}

// ==================== 权限统计接口 ====================

/**
 * 获取权限统计信息
 * @route GET /api/v1/permissions/stats
 */
export const getPermissionStats = async (req: Request, res: Response) => {
  try {
    // 获取基础统计
    const totalPermissions = getAllPermissions().length
    const permissionGroups = getPermissionGroups()
    const totalGroups = Object.keys(permissionGroups).length

    // 获取角色统计
    const roles = await prisma.role.findMany({
      select: {
        id: true,
        name: true,
        permissions: true,
        _count: {
          select: {
            userRoles: true
          }
        }
      }
    })

    const roleStats = roles.map(role => {
      const permissions = typeof role.permissions === 'string' 
        ? JSON.parse(role.permissions) 
        : role.permissions as string[]
      
      return {
        roleName: role.name,
        permissionCount: permissions.length,
        userCount: role._count.userRoles,
        hasAdminPermission: permissions.includes('admin:all')
      }
    })

    // 获取用户权限分布
    const users = await prisma.user.findMany({
      include: {
        userRoles: {
          include: {
            role: {
              select: {
                permissions: true
              }
            }
          }
        }
      }
    })

    const userPermissionStats = users.map(user => {
      const allPermissions = user.userRoles.reduce<string[]>((acc, ur) => {
        const rolePermissions = typeof ur.role.permissions === 'string' 
          ? JSON.parse(ur.role.permissions) 
          : ur.role.permissions
        return [...acc, ...rolePermissions]
      }, [])
      
      const uniquePermissions = [...new Set(allPermissions)]
      
      return {
        userId: user.id,
        username: user.username,
        permissionCount: uniquePermissions.length,
        hasAdminPermission: uniquePermissions.includes('admin:all')
      }
    })

    // 权限覆盖率统计
    const allUserPermissions = new Set<string>()
    users.forEach(user => {
      user.userRoles.forEach(ur => {
        const rolePermissions = typeof ur.role.permissions === 'string' 
          ? JSON.parse(ur.role.permissions) 
          : ur.role.permissions
        rolePermissions.forEach((permission: string) => allUserPermissions.add(permission))
      })
    })

    const usedPermissions = Array.from(allUserPermissions).filter(p => p !== 'admin:all')
    const unusedPermissions = getAllPermissions().filter(p => !usedPermissions.includes(p))

    res.json({
      success: true,
      data: {
        permissions: {
          total: totalPermissions,
          groups: totalGroups,
          used: usedPermissions.length,
          unused: unusedPermissions.length,
          coverage: Math.round((usedPermissions.length / totalPermissions) * 100)
        },
        roles: {
          total: roles.length,
          stats: roleStats,
          avgPermissionsPerRole: Math.round(roleStats.reduce((sum, role) => sum + role.permissionCount, 0) / roles.length)
        },
        users: {
          total: users.length,
          stats: userPermissionStats,
          avgPermissionsPerUser: Math.round(userPermissionStats.reduce((sum, user) => sum + user.permissionCount, 0) / users.length),
          adminUsers: userPermissionStats.filter(user => user.hasAdminPermission).length
        },
        coverage: {
          usedPermissions,
          unusedPermissions,
          permissionUsageRate: Math.round((usedPermissions.length / totalPermissions) * 100)
        }
      }
    })
  } catch (error: any) {
    console.error('获取权限统计失败:', error)
    res.status(500).json({
      success: false,
      message: '获取权限统计失败',
      error: error.message
    })
  }
}