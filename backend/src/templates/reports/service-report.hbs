<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>服务工单报告 - {{service.ticketNumber}}</title>
  <style>
    /* 基础样式 */
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Microsoft YaHei', 'SimHei', Arial, sans-serif;
      font-size: 12px;
      line-height: 1.6;
      color: #333;
      background: #fff;
    }

    /* 页面布局 */
    @page {
      size: A4;
      margin: 15mm 20mm 20mm 20mm;
    }

    /* 标题样式 */
    h1 {
      font-size: 24px;
      font-weight: bold;
      color: #1f2937;
      margin-bottom: 8px;
    }

    h2 {
      font-size: 16px;
      font-weight: bold;
      color: #374151;
      margin: 20px 0 12px 0;
      padding-bottom: 8px;
      border-bottom: 2px solid #e5e7eb;
    }

    h3 {
      font-size: 14px;
      font-weight: bold;
      color: #4b5563;
      margin: 15px 0 8px 0;
    }

    /* 报告头部 */
    .report-header {
      text-align: center;
      padding: 20px 0;
      border-bottom: 3px solid #2563eb;
      margin-bottom: 30px;
    }

    .company-logo {
      width: 80px;
      height: 80px;
      margin: 0 auto 15px;
      background: #f3f4f6;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24px;
      color: #2563eb;
      font-weight: bold;
    }

    .report-title {
      font-size: 28px;
      font-weight: bold;
      color: #1f2937;
      margin-bottom: 10px;
    }

    .ticket-number {
      font-size: 20px;
      color: #2563eb;
      font-weight: bold;
      margin-bottom: 8px;
    }

    .generation-info {
      font-size: 11px;
      color: #6b7280;
    }

    /* 信息网格 */
    .info-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 15px;
      margin-bottom: 20px;
    }

    .info-item {
      display: flex;
      margin-bottom: 8px;
    }

    .info-label {
      font-weight: bold;
      color: #6b7280;
      min-width: 80px;
      margin-right: 10px;
    }

    .info-value {
      color: #1f2937;
      flex: 1;
    }

    /* 状态标签 */
    .status-badge {
      display: inline-block;
      padding: 4px 12px;
      border-radius: 20px;
      font-size: 11px;
      font-weight: bold;
      color: white;
    }

    .priority-badge {
      display: inline-block;
      padding: 4px 12px;
      border-radius: 20px;
      font-size: 11px;
      font-weight: bold;
      color: white;
    }

    /* SLA 指标卡片 */
    .sla-metrics {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 15px;
      margin: 20px 0;
    }

    .sla-card {
      background: #f8fafc;
      border: 1px solid #e2e8f0;
      border-radius: 8px;
      padding: 15px;
      text-align: center;
    }

    .sla-card h4 {
      font-size: 12px;
      color: #64748b;
      margin-bottom: 8px;
      font-weight: normal;
    }

    .sla-value {
      font-size: 18px;
      font-weight: bold;
      margin-bottom: 5px;
    }

    .sla-value.success { color: #059669; }
    .sla-value.warning { color: #d97706; }
    .sla-value.danger { color: #dc2626; }

    .sla-subtitle {
      font-size: 10px;
      color: #64748b;
    }

    /* 进度条 */
    .progress-bar {
      width: 100%;
      height: 8px;
      background: #e5e7eb;
      border-radius: 4px;
      overflow: hidden;
      margin: 8px 0;
    }

    .progress-fill {
      height: 100%;
      border-radius: 4px;
      transition: width 0.3s ease;
    }

    .progress-success { background: #10b981; }
    .progress-warning { background: #f59e0b; }
    .progress-danger { background: #ef4444; }

    /* 时间轴 */
    .timeline {
      position: relative;
      padding-left: 30px;
    }

    .timeline::before {
      content: '';
      position: absolute;
      left: 15px;
      top: 0;
      bottom: 0;
      width: 2px;
      background: #e5e7eb;
    }

    .timeline-item {
      position: relative;
      margin-bottom: 25px;
      background: #fff;
    }

    .timeline-item::before {
      content: '';
      position: absolute;
      left: -22px;
      top: 5px;
      width: 12px;
      height: 12px;
      border-radius: 50%;
      border: 2px solid #fff;
      z-index: 1;
    }

    .timeline-item.created::before { background: #3b82f6; }
    .timeline-item.response::before { background: #10b981; }
    .timeline-item.assigned::before { background: #8b5cf6; }
    .timeline-item.work::before { background: #f59e0b; }
    .timeline-item.comment::before { background: #06b6d4; }
    .timeline-item.note::before { background: #6b7280; }
    .timeline-item.complete::before { background: #10b981; }

    .timeline-content {
      padding: 10px 15px;
      background: #f8fafc;
      border-radius: 8px;
      border-left: 4px solid #e5e7eb;
    }

    .timeline-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 5px;
    }

    .timeline-title {
      font-weight: bold;
      color: #1f2937;
      font-size: 13px;
    }

    .timeline-time {
      font-size: 11px;
      color: #6b7280;
    }

    .timeline-description {
      color: #4b5563;
      font-size: 12px;
      margin-bottom: 5px;
    }

    .timeline-user {
      font-size: 11px;
      color: #6b7280;
      font-style: italic;
    }

    /* 表格样式 */
    .data-table {
      width: 100%;
      border-collapse: collapse;
      margin: 15px 0;
      font-size: 11px;
    }

    .data-table th,
    .data-table td {
      padding: 8px 12px;
      text-align: left;
      border-bottom: 1px solid #e5e7eb;
    }

    .data-table th {
      background: #f8fafc;
      font-weight: bold;
      color: #374151;
    }

    .data-table tr:hover {
      background: #f9fafb;
    }

    /* 工作日志样式 */
    .work-log-item {
      background: #f8fafc;
      border: 1px solid #e2e8f0;
      border-radius: 6px;
      padding: 12px;
      margin-bottom: 12px;
    }

    .work-log-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;
    }

    .work-log-user {
      font-weight: bold;
      color: #1f2937;
    }

    .work-log-date {
      font-size: 11px;
      color: #6b7280;
    }

    .work-log-hours {
      background: #dbeafe;
      color: #1e40af;
      padding: 2px 8px;
      border-radius: 12px;
      font-size: 10px;
      font-weight: bold;
    }

    .work-log-description {
      color: #4b5563;
      line-height: 1.5;
    }

    /* 评论样式 */
    .comment-item {
      background: #fff;
      border: 1px solid #e5e7eb;
      border-radius: 6px;
      padding: 12px;
      margin-bottom: 12px;
    }

    .comment-item.internal {
      background: #fef3c7;
      border-color: #f59e0b;
    }

    .comment-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;
    }

    .comment-author {
      font-weight: bold;
      color: #1f2937;
    }

    .comment-date {
      font-size: 11px;
      color: #6b7280;
    }

    .comment-type {
      background: #06b6d4;
      color: white;
      padding: 2px 6px;
      border-radius: 10px;
      font-size: 10px;
    }

    .comment-type.internal {
      background: #f59e0b;
    }

    .comment-content {
      color: #4b5563;
      line-height: 1.5;
    }

    /* 附件列表 */
    .attachment-list {
      list-style: none;
    }

    .attachment-item {
      display: flex;
      align-items: center;
      padding: 8px 0;
      border-bottom: 1px solid #f3f4f6;
    }

    .attachment-icon {
      width: 20px;
      height: 20px;
      background: #e5e7eb;
      border-radius: 4px;
      margin-right: 10px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 10px;
      color: #6b7280;
    }

    .attachment-info {
      flex: 1;
    }

    .attachment-name {
      font-weight: bold;
      color: #1f2937;
      font-size: 11px;
    }

    .attachment-meta {
      font-size: 10px;
      color: #6b7280;
    }

    /* 统计卡片 */
    .stats-grid {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 15px;
      margin: 20px 0;
    }

    .stat-card {
      background: #f8fafc;
      border: 1px solid #e2e8f0;
      border-radius: 6px;
      padding: 12px;
      text-align: center;
    }

    .stat-value {
      font-size: 20px;
      font-weight: bold;
      color: #1f2937;
      margin-bottom: 5px;
    }

    .stat-label {
      font-size: 10px;
      color: #6b7280;
      text-transform: uppercase;
    }

    /* 分页控制 */
    .page-break {
      page-break-before: always;
    }

    .section-break {
      page-break-inside: avoid;
    }

    /* 富文本内容 */
    .rich-content {
      line-height: 1.6;
      color: #374151;
    }

    .rich-content p {
      margin-bottom: 8px;
    }

    .rich-content ul,
    .rich-content ol {
      margin: 8px 0 8px 20px;
    }

    .rich-content li {
      margin-bottom: 4px;
    }

    .rich-content strong {
      font-weight: bold;
      color: #1f2937;
    }

    .rich-content em {
      font-style: italic;
    }

    /* 响应式调整 */
    @media print {
      .page-break {
        page-break-before: always;
      }
      
      .timeline-item {
        page-break-inside: avoid;
      }
      
      .work-log-item,
      .comment-item {
        page-break-inside: avoid;
      }
    }

    /* 公司页脚 */
    .company-footer {
      margin-top: 40px;
      padding-top: 20px;
      border-top: 1px solid #e5e7eb;
      text-align: center;
      font-size: 10px;
      color: #6b7280;
    }

    /* 报告印章（默认固定到每页右下） */
    .report-stamp {
      position: fixed;
      right: 30mm;
      bottom: 25mm;
      width: 120px;
      opacity: 0.9;
      z-index: 50;
      pointer-events: none;
    }
  </style>
</head>
<body>
  {{#if stamp.enabled}}
    <img class="report-stamp" src="{{stamp.dataUrl}}" alt="official-seal" />
  {{/if}}
  <!-- 报告头部 -->
  <div class="report-header">
    <div class="company-logo">
      多协云
    </div>
    <div class="report-title">服务工单报告</div>
    <div class="ticket-number">{{service.ticketNumber}}</div>
    <div class="generation-info">
      生成时间：{{formatDate "now" "YYYY年MM月DD日 HH:mm"}} | 
      报告版本：V1.0
    </div>
  </div>

  <!-- 工单概览 -->
  <div class="section section-break">
    <h2>📋 工单概览</h2>
    
    <div class="info-grid">
      <div>
        <div class="info-item">
          <span class="info-label">工单标题：</span>
          <span class="info-value">{{service.title}}</span>
        </div>
        <div class="info-item">
          <span class="info-label">当前状态：</span>
          <span class="info-value">
            <span class="status-badge" style="background-color: {{statusColor service.status}};">
              {{statusText service.status}}
            </span>
          </span>
        </div>
        <div class="info-item">
          <span class="info-label">优先级：</span>
          <span class="info-value">
            <span class="priority-badge" style="background-color: {{priorityColor service.priority}};">
              {{priorityText service.priority}}
            </span>
          </span>
        </div>
        <div class="info-item">
          <span class="info-label">服务类别：</span>
          <span class="info-value">{{categoryText service.category}}</span>
        </div>
      </div>
      <div>
        <div class="info-item">
          <span class="info-label">创建时间：</span>
          <span class="info-value">{{formatDate service.createdAt}}</span>
        </div>
        <div class="info-item">
          <span class="info-label">负责工程师：</span>
          <span class="info-value">
            {{#if service.assignedUser}}
              {{service.assignedUser.fullName}} ({{service.assignedUser.department}})
            {{else}}
              未分配
            {{/if}}
          </span>
        </div>
        <div class="info-item">
          <span class="info-label">创建人：</span>
          <span class="info-value">{{service.createdByUser.fullName}}</span>
        </div>
        <div class="info-item">
          <span class="info-label">完成时间：</span>
          <span class="info-value">
            {{#if service.endTime}}
              {{formatDate service.endTime}}
            {{else}}
              进行中
            {{/if}}
          </span>
        </div>
      </div>
    </div>

    <!-- 问题描述 -->
    <h3>问题描述</h3>
    <div class="rich-content">
      {{{safeHtml service.description}}}
    </div>

    {{#if service.resolution}}
    <h3>解决方案</h3>
    <div class="rich-content">
      {{{safeHtml service.resolution}}}
    </div>
    {{/if}}
  </div>

  <!-- 客户信息 -->
  <div class="section section-break">
    <h2>👤 客户信息</h2>
    
    <div class="info-grid">
      <div>
        <div class="info-item">
          <span class="info-label">客户名称：</span>
          <span class="info-value">{{service.archive.customer.name}}</span>
        </div>
        <div class="info-item">
          <span class="info-label">公司名称：</span>
          <span class="info-value">{{service.archive.customer.company}}</span>
        </div>
        <div class="info-item">
          <span class="info-label">客户等级：</span>
          <span class="info-value">{{service.archive.customer.level}}</span>
        </div>
        {{#if service.archive.customer.address}}
        <div class="info-item">
          <span class="info-label">地址：</span>
          <span class="info-value">{{service.archive.customer.address}}</span>
        </div>
        {{/if}}
      </div>
      <div>
        <div class="info-item">
          <span class="info-label">联系人：</span>
          <span class="info-value">{{service.archive.customer.contactPerson}}</span>
        </div>
        <div class="info-item">
          <span class="info-label">联系邮箱：</span>
          <span class="info-value">{{service.archive.customer.contactEmail}}</span>
        </div>
        <div class="info-item">
          <span class="info-label">联系电话：</span>
          <span class="info-value">{{service.archive.customer.contactPhone}}</span>
        </div>
      </div>
    </div>
  </div>

  <!-- SLA 绩效指标 -->
  {{#if slaMetrics}}
  <div class="section section-break">
    <h2>🎯 SLA 绩效指标</h2>
    
    <div class="sla-metrics">
      <div class="sla-card">
        <h4>响应时间达成</h4>
        <div class="sla-value {{#if slaMetrics.responseTimeStatus.isBreached}}danger{{else}}success{{/if}}">
          {{formatDuration slaMetrics.responseTimeStatus.elapsed}}
        </div>
        <div class="progress-bar">
          <div class="progress-fill {{#if slaMetrics.responseTimeStatus.isBreached}}progress-danger{{else}}progress-success{{/if}}" 
               style="width: {{#if (gt slaMetrics.responseTimeStatus.percentage 100)}}100{{else}}{{slaMetrics.responseTimeStatus.percentage}}{{/if}}%;"></div>
        </div>
        <div class="sla-subtitle">
          目标: {{slaMetrics.slaTemplate.responseTime}}分钟
        </div>
      </div>
      
      <div class="sla-card">
        <h4>解决时间达成</h4>
        <div class="sla-value {{#if slaMetrics.resolutionTimeStatus.isBreached}}danger{{else}}success{{/if}}">
          {{formatDuration (multiply slaMetrics.resolutionTimeStatus.elapsed 60)}}
        </div>
        <div class="progress-bar">
          <div class="progress-fill {{#if slaMetrics.resolutionTimeStatus.isBreached}}progress-danger{{else}}progress-success{{/if}}" 
               style="width: {{#if (gt slaMetrics.resolutionTimeStatus.percentage 100)}}100{{else}}{{slaMetrics.resolutionTimeStatus.percentage}}{{/if}}%;"></div>
        </div>
        <div class="sla-subtitle">
          目标: {{slaMetrics.slaTemplate.resolutionTime}}小时
        </div>
      </div>
      
      <div class="sla-card">
        <h4>风险等级</h4>
        <div class="sla-value {{#eq slaMetrics.riskLevel 'LOW'}}success{{/eq}}{{#eq slaMetrics.riskLevel 'MEDIUM'}}warning{{/eq}}{{#eq slaMetrics.riskLevel 'HIGH'}}danger{{/eq}}{{#eq slaMetrics.riskLevel 'CRITICAL'}}danger{{/eq}}">
          {{slaMetrics.riskLevel}}
        </div>
        <div class="sla-subtitle">
          整体评估: {{slaMetrics.status}}
        </div>
      </div>
    </div>
  </div>
  {{else}}
  <div class="section section-break">
    <h2>🎯 SLA 绩效指标</h2>
    <div style="color:#6b7280; font-size:12px;">未设置SLA模板或暂无法计算SLA指标。</div>
  </div>
  {{/if}}

  {{#if charts}}
  <div class="section section-break">
    <h2>📈 SLA 可视化图表</h2>
    <div class="info-grid">
      <div>
        {{#if charts.responseDonut}}
        <img src="{{charts.responseDonut}}" alt="响应时间图表" style="width: 100%; max-width: 520px;" />
        {{/if}}
      </div>
      <div>
        {{#if charts.resolutionDonut}}
        <img src="{{charts.resolutionDonut}}" alt="解决时间图表" style="width: 100%; max-width: 520px;" />
        {{/if}}
      </div>
    </div>
    <div style="margin-top: 12px;">
      {{#if charts.slaBars}}
      <img src="{{charts.slaBars}}" alt="SLA目标与进度" style="width: 100%;" />
      {{/if}}
    </div>
  </div>
  {{else}}
  <div class="section section-break">
    <h2>📈 SLA 可视化图表</h2>
    <div style="color:#6b7280; font-size:12px;">暂无可视化图表。</div>
  </div>
  {{/if}}

  <!-- 统计概要 -->
  <div class="section section-break">
    <h2>📊 统计概要</h2>
    
    <div class="stats-grid">
      <div class="stat-card">
        <div class="stat-value">{{statistics.totalWorkHours}}</div>
        <div class="stat-label">总工时（小时）</div>
      </div>
      <div class="stat-card">
        <div class="stat-value">{{statistics.commentsCount}}</div>
        <div class="stat-label">沟通记录</div>
      </div>
      <div class="stat-card">
        <div class="stat-value">{{statistics.attachmentsCount}}</div>
        <div class="stat-label">相关附件</div>
      </div>
      <div class="stat-card">
        <div class="stat-value">
          {{#if service.satisfaction}}
            {{service.satisfaction}}分
          {{else}}
            未评价
          {{/if}}
        </div>
        <div class="stat-label">客户满意度</div>
      </div>
    </div>
  </div>

  <!-- 处理时间轴 -->
  <div class="section page-break">
    <h2>⏱️ 处理时间轴</h2>
    
    <div class="timeline">
      {{#each timeline}}
      <div class="timeline-item {{type}}">
        <div class="timeline-content">
          <div class="timeline-header">
            <span class="timeline-title">{{title}}</span>
            <span class="timeline-time">{{formatDate timestamp "MM-DD HH:mm"}}</span>
          </div>
          <div class="timeline-description">{{description}}</div>
          {{#if details}}
          <div class="timeline-description"><small>{{details}}</small></div>
          {{/if}}
          {{#if user}}
          <div class="timeline-user">操作人: {{user}}</div>
          {{/if}}
        </div>
      </div>
      {{/each}}
    </div>
  </div>

  <!-- 详细工作日志 -->
  {{#if (gt service.workLogs.length 0)}}
  <div class="section page-break">
    <h2>📝 详细工作日志</h2>
    
    {{#each service.workLogs}}
    <div class="work-log-item">
      <div class="work-log-header">
        <div>
          <span class="work-log-user">{{user.fullName}}</span>
          <span class="work-log-date">{{formatDate workDate "YYYY-MM-DD"}}</span>
        </div>
        <span class="work-log-hours">{{workHours}}小时</span>
      </div>
      <div class="work-log-description">
        {{{safeHtml description}}}
      </div>
    </div>
    {{/each}}
  </div>
  {{/if}}

  <!-- 沟通记录 -->
  {{#if (gt service.comments.length 0)}}
  <div class="section page-break">
    <h2>💬 沟通记录</h2>
    
    {{#each service.comments}}
    <div class="comment-item {{#if isInternal}}internal{{/if}}">
      <div class="comment-header">
        <div>
          <span class="comment-author">{{author.fullName}}</span>
          <span class="comment-type {{#if isInternal}}internal{{/if}}">
            {{#if isInternal}}内部备注{{else}}客户沟通{{/if}}
          </span>
        </div>
        <span class="comment-date">{{formatDate createdAt}}</span>
      </div>
      <div class="comment-content">
        {{{safeHtml content}}}
      </div>
    </div>
    {{/each}}
  </div>
  {{/if}}

  <!-- 相关附件 -->
  {{#if (gt service.attachments.length 0)}}
  <div class="section section-break">
    <h2>📎 相关附件</h2>
    
    <ul class="attachment-list">
      {{#each service.attachments}}
      <li class="attachment-item">
        <div class="attachment-icon">📄</div>
        <div class="attachment-info">
          <div class="attachment-name">{{originalName}}</div>
          <div class="attachment-meta">
            上传时间: {{formatDate uploadedAt}} | 
            上传人: {{uploader.fullName}} | 
            大小: {{fileSize}} bytes
          </div>
        </div>
      </li>
      {{/each}}
    </ul>
  </div>
  {{/if}}

  <!-- 客户反馈 -->
  {{#if service.customerFeedback}}
  <div class="section section-break">
    <h2>💭 客户反馈</h2>
    <div class="rich-content">
      {{{safeHtml service.customerFeedback}}}
    </div>
    
    {{#if service.satisfaction}}
    <div style="margin-top: 15px;">
      <strong>客户满意度评分：</strong>
      <span style="color: #059669; font-size: 16px; font-weight: bold;">
        {{service.satisfaction}}/5 分
      </span>
    </div>
    {{/if}}
  </div>
  {{/if}}

  <!-- 公司页脚 -->
  <div class="company-footer">
    <div>{{company.name}}</div>
    <div>{{company.address}} | {{company.phone}} | {{company.website}}</div>
    <div>本报告由系统自动生成，如有疑问请联系技术支持</div>
  </div>
</body>
</html>
