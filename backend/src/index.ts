import express from 'express'
import { createServer } from 'http'
import cors from 'cors'
import helmet from 'helmet'
import morgan from 'morgan'
import rateLimit from 'express-rate-limit'
import cookieParser from 'cookie-parser'
import dotenv from 'dotenv'

import { errorHandler } from '@/middleware/error.middleware'
import { autoRefreshMiddleware } from '@/middleware/auto-refresh.middleware'
import routes from '@/routes'
import { setupSwagger } from '@/config/swagger.config'
import { connectDatabase } from '@/config/database.config'
import { CacheService } from '@/config/redis.config'
import { SchedulerService } from '@/services/scheduler.service'
import { MonitorService } from '@/services/monitor.service'
import { WebSocketService } from '@/services/websocket.service'
import { RealtimeService } from '@/services/realtime.service'
import externalRoutes from '@/routes/external.routes'

dotenv.config()

const app = express()
const server = createServer(app)
const PORT = process.env['PORT'] || 3001

// 在反向代理(如 Nginx)之后，启用 trust proxy 以便 req.ip 返回真实客户端 IP
// 如果仅有一层代理，使用 1；可用环境变量覆盖
app.set('trust proxy', (process.env['TRUST_PROXY'] as any) ?? 1)

// Security middleware
app.use(helmet())

// CORS配置 - 区分内部和外部API
// 为外部API配置宽松且健壮的CORS策略：
// - 允许常见开发环境域名（localhost、127.0.0.1等）
// - 支持 null Origin（本地文件打开）
// - 让 CORS 自动镜像 Access-Control-Request-Headers
// - 显式处理预检 OPTIONS，避免被鉴权/限流中间件拦截
const externalCors = cors({
  origin: function (origin, callback) {
    // 允许无 Origin 的请求（如直接 API 调用、Postman 等）
    if (!origin) return callback(null, true)
    
    // 允许常见开发环境域名
    const allowedOrigins = [
      /^http:\/\/localhost(:\d+)?$/,
      /^http:\/\/127\.0\.0\.1(:\d+)?$/,
      /^http:\/\/0\.0\.0\.0(:\d+)?$/,
      /^https?:\/\/.*\.local(:\d+)?$/,
      // 如果需要允许特定生产域名，在这里添加
    ]
    
    // 检查是否在允许列表中
    const isAllowed = allowedOrigins.some(pattern => pattern.test(origin))
    if (isAllowed) {
      return callback(null, true)
    }
    
    // 开发模式下允许所有来源（可选）
    if (process.env['NODE_ENV'] === 'development') {
      return callback(null, true)
    }
    
    // 其他情况拒绝
    callback(new Error('Not allowed by CORS'))
  },
  credentials: false, // 外部API不使用cookies
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  // 不设置 allowedHeaders，默认镜像浏览器的 Access-Control-Request-Headers
  preflightContinue: false,
  optionsSuccessStatus: 200,
  maxAge: 86400
})
// 预检请求优先处理
app.options('/api/external/*', externalCors)
// 实际请求应用外部CORS（必须在其他CORS中间件之前）
app.use('/api/external', externalCors)

// 为内部API配置严格的CORS策略  
app.use('/api/v1', cors({
  origin: process.env['FRONTEND_URL'] || 'http://localhost:3000',
  credentials: true
}))

// 其他路由的默认CORS配置（不包括/api/external路径）
app.use((req, res, next) => {
  if (req.path.startsWith('/api/external')) {
    return next() // 跳过默认CORS，已由外部CORS处理
  }
  cors({
    origin: process.env['FRONTEND_URL'] || 'http://localhost:3000', 
    credentials: true
  })(req, res, next)
})

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 10000, // limit each IP to 100 requests per windowMs
  message: 'Too many requests from this IP, please try again later.'
})
app.use('/api', limiter)

// Middleware
app.use(morgan('combined'))
app.use(cookieParser())

// 注意：express.json() 和 express.urlencoded() 中间件
// 必须在文件上传路由之后应用，避免干扰 multipart/form-data 请求

// Health check
app.get('/health', (_req, res) => {
  res.status(200).json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    uptime: process.uptime()
  })
})
// 外部API路由 (不带版本前缀)
app.use('/api/external', externalRoutes)
// Auto-refresh middleware (在API路由之前应用，但在健康检查之后)
// app.use('/api', autoRefreshMiddleware)

// 为除上传路由外的所有路由添加 JSON 解析中间件
app.use((req, res, next) => {
  // 跳过上传路由的 JSON 解析
  if (req.path.startsWith('/api/v1/upload')) {
    return next()
  }
  express.json({ limit: '10mb' })(req, res, next)
})

app.use((req, res, next) => {
  // 跳过上传路由的 URL 编码解析
  if (req.path.startsWith('/api/v1/upload')) {
    return next()
  }
  express.urlencoded({ extended: true, limit: '10mb' })(req, res, next)
})

// API Routes
app.use('/api', routes)

// Setup Swagger documentation
setupSwagger(app)

// Error handling middleware (must be last)
app.use(errorHandler)

// 404 handler
app.use('*', (_req, res) => {
  res.status(404).json({
    success: false,
    message: 'Route not found'
  })
})

async function startServer() {
  try {
    // 仅在生产环境连接数据库
    await connectDatabase()
    
    // 初始化Redis连接
    console.log('🔗 Connecting to Redis...')
    // Redis连接会在第一次使用时自动建立
    
    // 初始化任务调度器
    SchedulerService.init()
    
    // 初始化监控服务
    console.log('📊 Initializing monitoring service...')
    MonitorService.getInstance()
    
    // 初始化WebSocket服务
    console.log('🔌 Initializing WebSocket service...')
    WebSocketService.getInstance().initialize(server)
    
    // 初始化实时推送服务
    console.log('📡 Initializing realtime push service...')
    RealtimeService.getInstance()
    
    server.listen(PORT, () => {
      console.log(`🚀 Server running on port ${PORT}`)
      // console.log(`📚 API Documentation: http://localhost:${PORT}/api-docs`)
      // console.log(`🏥 Health Check: http://localhost:${PORT}/health`)
      // console.log(`🔌 WebSocket Server: ws://localhost:${PORT}/ws`)
      // console.log('')
      // console.log('📋 Available API Endpoints:')
      // console.log('')
      // console.log('  🔐 认证 (Authentication):')
      // console.log('    POST /api/v1/auth/register - 用户注册')
      // console.log('    POST /api/v1/auth/login - 用户登录')
      // console.log('    POST /api/v1/auth/refresh - 刷新令牌')
      // console.log('    POST /api/v1/auth/logout - 用户登出')
      // console.log('    GET  /api/v1/auth/me - 获取当前用户信息')
      // console.log('    POST /api/v1/auth/change-password - 修改密码')
      // console.log('')
      // console.log('  👥 客户管理 (Customers):')
      // console.log('    GET  /api/v1/customers - 获取客户列表')
      // console.log('    POST /api/v1/customers - 创建客户')
      // console.log('    GET  /api/v1/customers/{id} - 获取客户详情')
      // console.log('    PUT  /api/v1/customers/{id} - 更新客户')
      // console.log('    DELETE /api/v1/customers/{id} - 删除客户')
      // console.log('    GET  /api/v1/customers/stats - 客户统计')
      // console.log('')
      // console.log('  📁 项目档案 (Archives):')
      // console.log('    GET  /api/v1/archives - 获取项目档案列表')
      // console.log('    POST /api/v1/archives - 创建项目档案')
      // console.log('    GET  /api/v1/archives/{id} - 获取项目档案详情')
      // console.log('    PUT  /api/v1/archives/{id} - 更新项目档案')
      // console.log('    DELETE /api/v1/archives/{id} - 删除项目档案')
      // console.log('    GET  /api/v1/archives/stats - 项目档案统计')
      // console.log('')
      // console.log('  🎫 服务工单 (Services):')
      // console.log('    GET  /api/v1/services - 获取服务工单列表')
      // console.log('    POST /api/v1/services - 创建服务工单')
      // console.log('    GET  /api/v1/services/{id} - 获取服务工单详情')
      // console.log('    PUT  /api/v1/services/{id} - 更新服务工单')
      // console.log('    DELETE /api/v1/services/{id} - 删除服务工单')
      // console.log('    GET  /api/v1/services/stats - 服务工单统计')
      // console.log('')
      // console.log('  ⚙️  配置管理 (Configurations):')
      // console.log('    GET  /api/v1/configurations - 获取配置列表')
      // console.log('    POST /api/v1/configurations - 创建配置')
      // console.log('    GET  /api/v1/configurations/{id} - 获取配置详情')
      // console.log('    PUT  /api/v1/configurations/{id} - 更新配置')
      // console.log('    DELETE /api/v1/configurations/{id} - 删除配置')
      // console.log('    GET  /api/v1/configurations/stats - 配置统计')
      // console.log('')
      // console.log('  🛡️  权限管理 (Permissions):')
      // console.log('    GET  /api/v1/permissions - 获取权限列表')
      // console.log('    GET  /api/v1/permissions/groups - 获取权限分组')
      // console.log('    POST /api/v1/permissions/validate - 验证权限格式')
      // console.log('    POST /api/v1/permissions/check - 检查用户权限')
      // console.log('')
      // console.log('  📋  权限模板 (Permission Templates):')
      // console.log('    GET  /api/v1/permission-templates - 获取权限模板列表')
      // console.log('    POST /api/v1/permission-templates - 创建权限模板')
      // console.log('    GET  /api/v1/permission-templates/{id} - 获取模板详情')
      // console.log('    PUT  /api/v1/permission-templates/{id} - 更新权限模板')
      // console.log('    DELETE /api/v1/permission-templates/{id} - 删除权限模板')
      // console.log('    POST /api/v1/permission-templates/{id}/apply - 应用模板到角色')
      // console.log('    POST /api/v1/permission-templates/batch-apply - 批量应用模板')
      // console.log('    POST /api/v1/permission-templates/compare - 比较权限模板')
      // console.log('    POST /api/v1/permission-templates/export - 导出权限模板')
      // console.log('    POST /api/v1/permission-templates/import - 导入权限模板')
      // console.log('    GET  /api/v1/permission-templates/stats - 权限模板统计')
      // console.log('')
      // console.log('  📡 实时推送 (Realtime):') 
      // console.log('    GET  /api/v1/realtime/stats - 获取实时推送统计')
      // console.log('    GET  /api/v1/realtime/online-users - 获取在线用户')
      // console.log('    POST /api/v1/realtime/push/system-metrics - 推送系统监控指标')
      // console.log('    POST /api/v1/realtime/push/system-status - 推送系统状态变更')
      // console.log('    POST /api/v1/realtime/push/system-alert - 推送系统告警')
      // console.log('    POST /api/v1/realtime/push/notification - 推送通知')
      // console.log('    POST /api/v1/realtime/broadcast - 广播自定义消息')
      // console.log('    POST /api/v1/realtime/test-connection - 测试WebSocket连接')
      // console.log('    GET  /api/v1/realtime/connections - 获取连接详情')
    })
  } catch (error) {
    console.error('❌ Failed to start server:', error)
    process.exit(1)
  }
}

startServer()

// 优雅关闭
process.on('SIGINT', async () => {
  console.log('🛑 Shutting down server...')
  try {
    // 停止所有定时任务
    SchedulerService.stopAll()
    
    // 关闭WebSocket服务
    await WebSocketService.getInstance().shutdown()
    
    // 关闭实时推送服务
    await RealtimeService.getInstance().cleanup()
    
    // 断开Redis连接
    await CacheService.disconnect()
    
    console.log('👋 Server shut down gracefully')
    process.exit(0)
  } catch (error) {
    console.error('❌ Error during shutdown:', error)
    process.exit(1)
  }
})