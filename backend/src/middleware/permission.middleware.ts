/**
 * 权限检查中间件
 * 
 * 提供API路由级别的权限检查功能
 */

import { Request, Response, NextFunction } from 'express'
import { AuthenticatedRequest } from './auth.middleware'
import { hasPermission, hasAnyPermission, isSuperAdmin } from '@/utils/permission.util'

/**
 * 权限检查中间件 - 需要拥有所有指定权限
 * @param requiredPermissions 需要的权限列表
 * @returns Express中间件函数
 */
export function checkPermissions(requiredPermissions: string | string[]) {
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      const authReq = req as AuthenticatedRequest
      
      if (!authReq.user) {
        return res.status(401).json({
          success: false,
          message: '未认证用户'
        })
      }

      const userPermissions = authReq.user.permissions || []
      const permissions = Array.isArray(requiredPermissions) ? requiredPermissions : [requiredPermissions]

      // 检查权限
      if (!hasPermission(userPermissions, permissions)) {
        return res.status(403).json({
          success: false,
          message: '权限不足',
          error: `需要权限: ${permissions.join(', ')}`
        })
      }

      next()
    } catch (error) {
      console.error('权限检查中间件错误:', error)
      res.status(500).json({
        success: false,
        message: '权限检查失败'
      })
    }
  }
}

/**
 * 权限检查中间件 - 需要拥有任一权限
 * @param requiredPermissions 需要的权限列表（满足其中一个即可）
 * @returns Express中间件函数
 */
export function checkAnyPermission(requiredPermissions: string[]) {
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      const authReq = req as AuthenticatedRequest
      
      if (!authReq.user) {
        return res.status(401).json({
          success: false,
          message: '未认证用户'
        })
      }

      const userPermissions = authReq.user.permissions || []

      // 检查权限
      if (!hasAnyPermission(userPermissions, requiredPermissions)) {
        return res.status(403).json({
          success: false,
          message: '权限不足',
          error: `需要以下权限之一: ${requiredPermissions.join(', ')}`
        })
      }

      next()
    } catch (error) {
      console.error('权限检查中间件错误:', error)
      res.status(500).json({
        success: false,
        message: '权限检查失败'
      })
    }
  }
}

/**
 * 超级管理员权限检查中间件
 * @returns Express中间件函数
 */
export function checkSuperAdmin() {
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      const authReq = req as AuthenticatedRequest
      
      if (!authReq.user) {
        return res.status(401).json({
          success: false,
          message: '未认证用户'
        })
      }

      const userPermissions = authReq.user.permissions || []

      // 检查是否为超级管理员
      if (!isSuperAdmin(userPermissions)) {
        return res.status(403).json({
          success: false,
          message: '需要超级管理员权限'
        })
      }

      next()
    } catch (error) {
      console.error('超级管理员权限检查中间件错误:', error)
      res.status(500).json({
        success: false,
        message: '权限检查失败'
      })
    }
  }
}

/**
 * 资源所有权检查中间件
 * @param resourceUserIdParam 资源所有者ID参数名
 * @param adminPermissions 管理员权限列表
 * @returns Express中间件函数
 */
export function checkOwnership(resourceUserIdParam: string, adminPermissions: string[] = ['admin:all']) {
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      const authReq = req as AuthenticatedRequest
      
      if (!authReq.user) {
        return res.status(401).json({
          success: false,
          message: '未认证用户'
        })
      }

      const currentUserId = authReq.user.id
      const resourceUserId = req.params[resourceUserIdParam] || req.body[resourceUserIdParam]
      const userPermissions = authReq.user.permissions || []

      // 检查是否为资源拥有者
      if (currentUserId === resourceUserId) {
        return next()
      }

      // 检查是否拥有管理员权限
      if (hasAnyPermission(userPermissions, adminPermissions)) {
        return next()
      }

      res.status(403).json({
        success: false,
        message: '只能访问自己的资源或需要管理员权限'
      })
    } catch (error) {
      console.error('资源所有权检查中间件错误:', error)
      res.status(500).json({
        success: false,
        message: '权限检查失败'
      })
    }
  }
}

/**
 * 条件权限检查中间件
 * @param conditionFn 条件判断函数，返回需要的权限列表
 * @returns Express中间件函数
 */
export function checkConditionalPermissions(
  conditionFn: (req: Request) => string[]
) {
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      const authReq = req as AuthenticatedRequest
      
      if (!authReq.user) {
        return res.status(401).json({
          success: false,
          message: '未认证用户'
        })
      }

      const userPermissions = authReq.user.permissions || []
      const requiredPermissions = conditionFn(req)

      // 检查权限
      if (!hasPermission(userPermissions, requiredPermissions)) {
        return res.status(403).json({
          success: false,
          message: '权限不足',
          error: `需要权限: ${requiredPermissions.join(', ')}`
        })
      }

      next()
    } catch (error) {
      console.error('条件权限检查中间件错误:', error)
      res.status(500).json({
        success: false,
        message: '权限检查失败'
      })
    }
  }
}

/**
 * 组合权限检查中间件 - 支持多种权限组合逻辑
 * @param permissionConfig 权限配置
 * @returns Express中间件函数
 */
export function checkCombinedPermissions(permissionConfig: {
  required?: string[]      // 必须拥有的权限（AND关系）
  anyOf?: string[]        // 拥有其中任一权限即可（OR关系）
  forbidden?: string[]    // 禁止拥有的权限
  superAdminBypass?: boolean  // 超级管理员是否可绕过检查
}) {
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      const authReq = req as AuthenticatedRequest
      
      if (!authReq.user) {
        return res.status(401).json({
          success: false,
          message: '未认证用户'
        })
      }

      const userPermissions = authReq.user.permissions || []
      
      // 超级管理员绕过检查
      if (permissionConfig.superAdminBypass !== false && isSuperAdmin(userPermissions)) {
        return next()
      }

      // 检查禁止权限
      if (permissionConfig.forbidden && permissionConfig.forbidden.length > 0) {
        if (hasAnyPermission(userPermissions, permissionConfig.forbidden)) {
          return res.status(403).json({
            success: false,
            message: '权限冲突',
            error: `禁止拥有权限: ${permissionConfig.forbidden.join(', ')}`
          })
        }
      }

      // 检查必须权限
      if (permissionConfig.required && permissionConfig.required.length > 0) {
        if (!hasPermission(userPermissions, permissionConfig.required)) {
          return res.status(403).json({
            success: false,
            message: '权限不足',
            error: `需要权限: ${permissionConfig.required.join(', ')}`
          })
        }
      }

      // 检查任一权限
      if (permissionConfig.anyOf && permissionConfig.anyOf.length > 0) {
        if (!hasAnyPermission(userPermissions, permissionConfig.anyOf)) {
          return res.status(403).json({
            success: false,
            message: '权限不足',
            error: `需要以下权限之一: ${permissionConfig.anyOf.join(', ')}`
          })
        }
      }

      next()
    } catch (error) {
      console.error('组合权限检查中间件错误:', error)
      res.status(500).json({
        success: false,
        message: '权限检查失败'
      })
    }
  }
}