/**
 * 增强版权限验证中间件
 * 
 * 提供更灵活和强大的权限验证功能
 */

import { Request, Response, NextFunction } from 'express'
import { AuthenticatedRequest } from './auth.middleware'
import { UnauthorizedError, ForbiddenError } from './error.middleware'
import { CacheService } from '@/config/redis.config'
import { 
  hasPermission, 
  hasAnyPermission, 
  isSuperAdmin,
  getUserPermissions,
  checkOwnershipOrAdmin,
  expandPermissions
} from '@/utils/permission.util'
import {
  PermissionStrategy,
  PermissionPolicyConfig,
  UserPermissionInfo,
  ResourceAccessConfig,
  PermissionContext,
  DetailedPermissionResult
} from '@/types/permission.types'
import { ADMIN_ALL } from '@/constants/permissions'

// ==================== 权限策略实现 ====================

/**
 * 权限策略处理器
 */
class PermissionPolicyHandler {
  /**
   * 执行权限策略检查
   */
  static async execute(
    policy: PermissionPolicyConfig,
    context: PermissionContext
  ): Promise<DetailedPermissionResult> {
    const { user, request } = context
    const { strategy, permissions, options = {} } = policy

    const result: DetailedPermissionResult = {
      success: false,
      userInfo: user,
      requiredPermissions: permissions,
      grantedPermissions: [],
      missingPermissions: [],
      checkResults: []
    }

    // 超级管理员检查
    if (options.allowSuperAdmin !== false && user.isAdmin) {
      result.success = true
      result.grantedPermissions = permissions
      result.checkResults = permissions.map(permission => ({
        permission,
        granted: true,
        source: 'super_admin' as const
      }))
      return result
    }

    // 根据策略执行不同的权限检查
    switch (strategy) {
      case PermissionStrategy.STRICT:
        return this.handleStrictStrategy(policy, context, result)
      
      case PermissionStrategy.FLEXIBLE:
        return this.handleFlexibleStrategy(policy, context, result)
      
      case PermissionStrategy.OWNERSHIP:
        return this.handleOwnershipStrategy(policy, context, result)
      
      case PermissionStrategy.CONDITIONAL:
        return this.handleConditionalStrategy(policy, context, result)
      
      case PermissionStrategy.HIERARCHICAL:
        return this.handleHierarchicalStrategy(policy, context, result)
      
      default:
        throw new Error(`不支持的权限策略: ${strategy}`)
    }
  }

  /**
   * 严格策略：必须拥有所有权限
   */
  private static async handleStrictStrategy(
    policy: PermissionPolicyConfig,
    context: PermissionContext,
    result: DetailedPermissionResult
  ): Promise<DetailedPermissionResult> {
    const { user } = context
    const { permissions } = policy

    for (const permission of permissions) {
      const granted = user.permissions.includes(permission)
      
      result.checkResults.push({
        permission,
        granted,
        source: granted ? 'direct' : 'direct'
      })

      if (granted) {
        result.grantedPermissions.push(permission)
      } else {
        result.missingPermissions.push(permission)
      }
    }

    result.success = result.missingPermissions.length === 0
    return result
  }

  /**
   * 灵活策略：拥有任一权限即可
   */
  private static async handleFlexibleStrategy(
    policy: PermissionPolicyConfig,
    context: PermissionContext,
    result: DetailedPermissionResult
  ): Promise<DetailedPermissionResult> {
    const { user } = context
    const { permissions } = policy

    let hasAnyPermission = false

    for (const permission of permissions) {
      const granted = user.permissions.includes(permission)
      
      result.checkResults.push({
        permission,
        granted,
        source: granted ? 'direct' : 'direct'
      })

      if (granted) {
        result.grantedPermissions.push(permission)
        hasAnyPermission = true
      } else {
        result.missingPermissions.push(permission)
      }
    }

    result.success = hasAnyPermission
    return result
  }

  /**
   * 所有权策略：资源所有者或管理员
   */
  private static async handleOwnershipStrategy(
    policy: PermissionPolicyConfig,
    context: PermissionContext,
    result: DetailedPermissionResult
  ): Promise<DetailedPermissionResult> {
    const { user, request, resource } = context
    const { options = {} } = policy

    // 检查是否为资源所有者
    if (resource && resource.ownerId === user.userId) {
      result.success = true
      result.grantedPermissions = policy.permissions
      result.checkResults = policy.permissions.map(permission => ({
        permission,
        granted: true,
        source: 'direct' as const
      }))
      return result
    }

    // 检查管理员权限
    const adminPermissions = options.adminPermissions || [ADMIN_ALL]
    if (hasAnyPermission(user.permissions, adminPermissions)) {
      result.success = true
      result.grantedPermissions = policy.permissions
      result.checkResults = policy.permissions.map(permission => ({
        permission,
        granted: true,
        source: 'direct' as const
      }))
      return result
    }

    result.success = false
    result.missingPermissions = policy.permissions
    return result
  }

  /**
   * 条件策略：根据条件动态检查
   */
  private static async handleConditionalStrategy(
    policy: PermissionPolicyConfig,
    context: PermissionContext,
    result: DetailedPermissionResult
  ): Promise<DetailedPermissionResult> {
    const { request, user } = context
    const { options = {} } = policy

    // 执行条件检查
    if (options.condition) {
      const shouldCheck = await Promise.resolve(options.condition(request))
      
      if (!shouldCheck) {
        // 条件不满足，直接通过
        result.success = true
        result.grantedPermissions = policy.permissions
        result.checkResults = policy.permissions.map(permission => ({
          permission,
          granted: true,
          source: 'direct' as const
        }))
        return result
      }
    }

    // 条件满足，执行常规权限检查
    return this.handleStrictStrategy(policy, context, result)
  }

  /**
   * 层级策略：支持权限继承
   */
  private static async handleHierarchicalStrategy(
    policy: PermissionPolicyConfig,
    context: PermissionContext,
    result: DetailedPermissionResult
  ): Promise<DetailedPermissionResult> {
    const { user } = context
    const { permissions, options = {} } = policy

    // 展开权限（包括继承权限）
    const expandedUserPermissions = expandPermissions(user.permissions)

    for (const permission of permissions) {
      const granted = expandedUserPermissions.includes(permission)
      
      result.checkResults.push({
        permission,
        granted,
        source: user.permissions.includes(permission) ? 'direct' : 'inherited'
      })

      if (granted) {
        result.grantedPermissions.push(permission)
      } else {
        result.missingPermissions.push(permission)
      }
    }

    result.success = result.missingPermissions.length === 0
    return result
  }
}

// ==================== 增强权限中间件 ====================

/**
 * 增强权限检查中间件工厂
 */
export function createPermissionPolicy(policy: PermissionPolicyConfig) {
  return async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    try {
      if (!req.user) {
        throw new UnauthorizedError('用户未认证')
      }

      // 构建权限上下文
      const context: PermissionContext = {
        user: {
          userId: req.user.id,
          username: req.user.username,
          email: req.user.email,
          roles: req.user.roles,
          permissions: req.user.permissions,
          isAdmin: req.user.permissions.includes(ADMIN_ALL)
        },
        request: req,
        action: req.method,
        timestamp: new Date()
      }

      // 执行权限策略检查
      const result = await PermissionPolicyHandler.execute(policy, context)

      if (!result.success) {
        throw new ForbiddenError(
          `权限不足。缺少权限: ${result.missingPermissions.join(', ')}`
        )
      }

      // 将检查结果附加到请求对象
      req.permissionCheckResult = result

      next()
    } catch (error) {
      next(error)
    }
  }
}

/**
 * 资源级权限检查中间件工厂
 */
export function createResourcePermissionCheck(config: ResourceAccessConfig) {
  return async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    try {
      if (!req.user) {
        throw new UnauthorizedError('用户未认证')
      }

      const userPermissions = req.user.permissions
      const { resourceType, adminPermissions = [ADMIN_ALL] } = config

      // 检查管理员权限
      if (hasAnyPermission(userPermissions, adminPermissions)) {
        next()
        return
      }

      // 根据请求方法检查相应权限
      const method = req.method.toLowerCase()
      let requiredPermissions: string[] = []

      if (['get', 'head'].includes(method) && config.readPermissions) {
        requiredPermissions = config.readPermissions
      } else if (['post', 'put', 'patch', 'delete'].includes(method) && config.writePermissions) {
        requiredPermissions = config.writePermissions
      }

      if (requiredPermissions.length === 0) {
        next()
        return
      }

      // 检查基础权限
      if (!hasAnyPermission(userPermissions, requiredPermissions)) {
        throw new ForbiddenError(`需要权限: ${requiredPermissions.join(' 或 ')}`)
      }

      // 检查所有权（如果配置了）
      if (config.ownershipCheck) {
        const resourceOwnerId = await Promise.resolve(config.ownershipCheck(req))
        
        if (resourceOwnerId !== req.user.id && !hasAnyPermission(userPermissions, adminPermissions)) {
          throw new ForbiddenError('只能访问自己的资源')
        }
      }

      next()
    } catch (error) {
      next(error)
    }
  }
}

/**
 * 动态权限检查中间件
 */
export function createDynamicPermissionCheck(
  getPermissions: (req: AuthenticatedRequest) => Promise<string[]> | string[]
) {
  return async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    try {
      if (!req.user) {
        throw new UnauthorizedError('用户未认证')
      }

      const requiredPermissions = await Promise.resolve(getPermissions(req))
      const userPermissions = req.user.permissions

      if (!hasPermission(userPermissions, requiredPermissions)) {
        throw new ForbiddenError(`需要权限: ${requiredPermissions.join(', ')}`)
      }

      next()
    } catch (error) {
      next(error)
    }
  }
}

/**
 * 权限缓存中间件
 */
export function createPermissionCache(ttl: number = 300) {
  return async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    try {
      if (!req.user) {
        next()
        return
      }

      const cacheKey = `user_permissions:${req.user.id}`
      let userPermissions = await CacheService.get<UserPermissionInfo>(cacheKey)

      if (!userPermissions) {
        userPermissions = {
          userId: req.user.id,
          username: req.user.username,
          email: req.user.email,
          roles: req.user.roles,
          permissions: req.user.permissions,
          isAdmin: req.user.permissions.includes(ADMIN_ALL)
        }

        await CacheService.set(cacheKey, userPermissions, ttl)
      }

      // 更新请求中的用户信息
      req.user.permissions = userPermissions.permissions
      req.user.roles = userPermissions.roles

      next()
    } catch (error) {
      next(error)
    }
  }
}

/**
 * 权限审计中间件
 */
export function createPermissionAudit() {
  return async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    const startTime = Date.now()

    // 监听响应完成事件
    res.on('finish', () => {
      const endTime = Date.now()
      const duration = endTime - startTime
      
      // 记录权限访问日志
      console.log(`权限审计: ${req.user?.username} - ${req.method} ${req.path} - ${res.statusCode} - ${duration}ms`)
      
      // 这里可以添加更详细的审计日志记录
      if (res.statusCode === 403) {
        console.warn(`权限拒绝: ${req.user?.username} 访问 ${req.path} 被拒绝`)
      }
    })

    next()
  }
}

// ==================== 类型扩展 ====================

declare global {
  namespace Express {
    interface Request {
      permissionCheckResult?: DetailedPermissionResult
    }
  }
}

// ==================== 导出 ====================

export {
  PermissionPolicyHandler,
  createPermissionPolicy,
  createResourcePermissionCheck,
  createDynamicPermissionCheck,
  createPermissionCache,
  createPermissionAudit
}

// 便捷的权限策略创建函数
export const PermissionPolicies = {
  /**
   * 严格权限策略
   */
  strict: (permissions: string[], options?: Partial<PermissionPolicyConfig['options']>) =>
    createPermissionPolicy({
      strategy: PermissionStrategy.STRICT,
      permissions,
      options
    }),

  /**
   * 灵活权限策略
   */
  flexible: (permissions: string[], options?: Partial<PermissionPolicyConfig['options']>) =>
    createPermissionPolicy({
      strategy: PermissionStrategy.FLEXIBLE,
      permissions,
      options
    }),

  /**
   * 所有权权限策略
   */
  ownership: (permissions: string[], adminPermissions?: string[]) =>
    createPermissionPolicy({
      strategy: PermissionStrategy.OWNERSHIP,
      permissions,
      options: { adminPermissions }
    }),

  /**
   * 条件权限策略
   */
  conditional: (
    permissions: string[], 
    condition: (req: Request) => boolean | Promise<boolean>
  ) =>
    createPermissionPolicy({
      strategy: PermissionStrategy.CONDITIONAL,
      permissions,
      options: { condition }
    })
}