import { AnyZodObject, ZodError } from 'zod'
import type { Request, Response, NextFunction } from 'express'

export function validate(schemas: { body?: AnyZodObject; query?: AnyZodObject; params?: AnyZodObject }) {
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      if (schemas.body) req.body = schemas.body.parse(req.body)
      if (schemas.query) req.query = schemas.query.parse(req.query)
      if (schemas.params) req.params = schemas.params.parse(req.params)
      return next()
    } catch (error) {
      if (error instanceof ZodError) {
        return res.status(400).json({ code: 'VALIDATION_ERROR', message: '请求参数验证失败', errors: error.flatten() })
      }
      return next(error as Error)
    }
  }
}
