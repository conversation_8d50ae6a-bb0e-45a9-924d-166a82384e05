import { Request, Response, NextFunction } from 'express'
import jwt from 'jsonwebtoken'
import { CacheService } from '@/config/redis.config'
import { refreshAccessToken, TokenPayload } from '@/utils/jwt.util'

export interface AutoRefreshRequest extends Request {
  user?: {
    id: string
    username: string
    email: string
    roles: string[]
    permissions: string[]
  }
  refreshedToken?: string
}

/**
 * 检查token是否需要刷新
 * @param tokenExp token过期时间戳
 * @param refreshThreshold 刷新阈值（秒），默认5分钟
 * @returns 是否需要刷新
 */
function needsRefresh(tokenExp: number, refreshThreshold: number = 300): boolean {
  const now = Math.floor(Date.now() / 1000)
  const timeToExpiry = tokenExp - now
  return timeToExpiry < refreshThreshold && timeToExpiry > 0
}

/**
 * 获取刷新锁，防止并发刷新
 * @param userId 用户ID
 * @returns 是否成功获取锁
 */
async function acquireRefreshLock(userId: string): Promise<boolean> {
  const refreshKey = `refresh_lock:${userId}`
  const lockDuration = 30 // 30秒锁定时间
  
  try {
    const lockAcquired = await CacheService.setNX(refreshKey, '1', lockDuration)
    return lockAcquired
  } catch (error) {
    console.error('Failed to acquire refresh lock:', error)
    return false
  }
}

/**
 * 释放刷新锁
 * @param userId 用户ID
 */
async function releaseRefreshLock(userId: string): Promise<void> {
  const refreshKey = `refresh_lock:${userId}`
  try {
    await CacheService.del(refreshKey)
  } catch (error) {
    console.error('Failed to release refresh lock:', error)
  }
}

/**
 * 等待其他刷新操作完成
 * @param userId 用户ID
 * @param maxWait 最大等待时间（毫秒）
 * @returns 是否等待成功
 */
async function waitForRefreshCompletion(userId: string, maxWait: number = 5000): Promise<boolean> {
  const refreshKey = `refresh_lock:${userId}`
  const startTime = Date.now()
  
  while (Date.now() - startTime < maxWait) {
    try {
      const lockExists = await CacheService.exists(refreshKey)
      if (!lockExists) {
        return true // 锁已释放，刷新完成
      }
      // 等待100毫秒后重试
      await new Promise(resolve => setTimeout(resolve, 100))
    } catch (error) {
      console.error('Error waiting for refresh completion:', error)
      return false
    }
  }
  
  return false // 等待超时
}

/**
 * 自动Token刷新中间件
 * 在请求处理过程中检测token是否即将过期，如果是则自动刷新
 */
export async function autoRefreshMiddleware(
  req: AutoRefreshRequest,
  res: Response,
  next: NextFunction
): Promise<void> {
  try {
    const authHeader = req.headers.authorization
    const token = authHeader && authHeader.split(' ')[1]

    // 如果没有token，直接跳过
    if (!token) {
      return next()
    }

    // 解码token以获取过期时间和用户信息
    let decoded: any
    try {
      decoded = jwt.decode(token) as any
    } catch (error) {
      // token格式无效，让后续的认证中间件处理
      return next()
    }

    // 检查token是否需要刷新
    if (!needsRefresh(decoded.exp)) {
      return next()
    }

    const userId = decoded.userId
    if (!userId) {
      return next()
    }

    console.log(`🔄 Token即将过期，准备自动刷新 (用户: ${decoded.username})`)

    // 尝试获取刷新锁
    const lockAcquired = await acquireRefreshLock(userId)
    
    if (!lockAcquired) {
      // 如果没有获取到锁，等待其他请求完成刷新
      console.log(`⏳ 等待其他请求完成刷新 (用户: ${decoded.username})`)
      const waitSuccess = await waitForRefreshCompletion(userId)
      
      if (waitSuccess) {
        console.log(`✅ 其他请求已完成刷新 (用户: ${decoded.username})`)
      } else {
        console.log(`⚠️ 等待刷新超时 (用户: ${decoded.username})`)
      }
      
      return next()
    }

    try {
      // 获取refreshToken
      const refreshToken = req.cookies.refreshToken
      
      if (!refreshToken) {
        console.log(`❌ 未找到refreshToken (用户: ${decoded.username})`)
        await releaseRefreshLock(userId)
        return next()
      }

      // 执行token刷新
      const newTokens = await refreshAccessToken(refreshToken)
      
      console.log(`✅ Token自动刷新成功 (用户: ${decoded.username})`)

      // 通过响应头返回新的accessToken
      res.set('X-New-Access-Token', newTokens.accessToken)
      res.set('X-Token-Refreshed', 'true')
      
      // 更新refreshToken Cookie
      res.cookie('refreshToken', newTokens.refreshToken, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax',
        maxAge: 7 * 24 * 60 * 60 * 1000 // 7天
      })

      // 将新token存储到请求对象中，供后续中间件使用
      req.refreshedToken = newTokens.accessToken
      
      // 更新Authorization头，确保后续中间件使用新token
      req.headers.authorization = `Bearer ${newTokens.accessToken}`

    } catch (error) {
      console.error(`❌ Token自动刷新失败 (用户: ${decoded.username}):`, error)
      // 刷新失败，但不阻断请求，让认证中间件处理过期token
    } finally {
      // 释放刷新锁
      await releaseRefreshLock(userId)
    }

    next()
  } catch (error) {
    console.error('自动刷新中间件异常:', error)
    next() // 发生异常时不阻断请求流程
  }
}

/**
 * 检查是否存在刷新锁（用于调试）
 * @param userId 用户ID
 * @returns 是否存在刷新锁
 */
export async function hasRefreshLock(userId: string): Promise<boolean> {
  try {
    const refreshKey = `refresh_lock:${userId}`
    return await CacheService.exists(refreshKey)
  } catch (error) {
    console.error('检查刷新锁状态失败:', error)
    return false
  }
}

/**
 * 清理过期的刷新锁（维护用途）
 */
export async function cleanupExpiredRefreshLocks(): Promise<void> {
  try {
    // Redis的key会自动过期，但可以在这里添加额外的清理逻辑
    console.log('🧹 清理过期的刷新锁')
  } catch (error) {
    console.error('清理过期刷新锁失败:', error)
  }
}