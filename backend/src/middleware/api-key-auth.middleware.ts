/**
 * API Key 验证中间件 - 外部调用MVP
 * 
 * 用于验证外部API调用的API Key
 */

import { Request, Response, NextFunction } from 'express'
import { prisma } from '@/config/database.config'

// 扩展Request类型以包含API Key信息
export interface ApiKeyAuthenticatedRequest extends Request {
  apiKey?: {
    id: string
    name: string
    systemId: string
    createdBy: string
    usageCount: number
  }
}

/**
 * API Key验证中间件
 */
export const authenticateApiKey = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    // 获取API Key从请求头
    let apiKeyHeader = req.headers['x-api-key'] as string
    // 如果apiKeyHeader为空，则从请求参数中获取
    if (!apiKeyHeader) {
      apiKeyHeader = req.query['api-key'] as string
    }
    console.log('apiKeyHeader', apiKeyHeader)
    if (!apiKeyHeader) {
      return res.status(401).json({
        success: false,
        message: '缺少API Key',
        code: 'MISSING_API_KEY'
      })
    }

    // 查找API Key
    const apiKey = await prisma.apiKey.findUnique({
      where: {
        keyValue: apiKeyHeader
      },
      select: {
        id: true,
        name: true,
        systemId: true,
        status: true,
        expiresAt: true,
        usageCount: true,
        createdBy: true,
        createdByUser: {
          select: {
            id: true,
            username: true,
            status: true
          }
        }
      }
    })

    if (!apiKey) {
      return res.status(401).json({
        success: false,
        message: 'API Key无效',
        code: 'INVALID_API_KEY'
      })
    }

    // 检查API Key状态
    if (apiKey.status !== 'ACTIVE') {
      return res.status(401).json({
        success: false,
        message: `API Key已${apiKey.status === 'REVOKED' ? '撤销' : apiKey.status === 'INACTIVE' ? '禁用' : '过期'}`,
        code: 'API_KEY_INACTIVE'
      })
    }

    // 检查过期时间
    if (apiKey.expiresAt && apiKey.expiresAt < new Date()) {
      // 更新状态为过期
      await prisma.apiKey.update({
        where: { id: apiKey.id },
        data: { status: 'EXPIRED' }
      })

      return res.status(401).json({
        success: false,
        message: 'API Key已过期',
        code: 'API_KEY_EXPIRED'
      })
    }

    // 检查创建用户状态
    if (!apiKey.createdByUser || apiKey.createdByUser.status !== 'ACTIVE') {
      return res.status(401).json({
        success: false,
        message: 'API Key关联用户已被禁用',
        code: 'USER_INACTIVE'
      })
    }

    // 更新使用统计（异步执行，不影响响应）
    prisma.apiKey.update({
      where: { id: apiKey.id },
      data: {
        usageCount: { increment: 1 },
        lastUsedAt: new Date()
      }
    }).catch(error => {
      console.error('更新API Key使用统计失败:', error)
    })

    // 将API Key信息附加到请求对象
    const authReq = req as ApiKeyAuthenticatedRequest
    authReq.apiKey = {
      id: apiKey.id,
      name: apiKey.name,
      systemId: apiKey.systemId,
      createdBy: apiKey.createdBy,
      usageCount: apiKey.usageCount
    }

    next()
  } catch (error: any) {
    console.error('API Key验证失败:', error)
    return res.status(500).json({
      success: false,
      message: 'API Key验证失败',
      code: 'AUTH_ERROR'
    })
  }
}

/**
 * API Key速率限制中间件（可选）
 */
export const rateLimitApiKey = (requestsPerHour: number = 1000) => {
  const requestCounts = new Map<string, { count: number, resetTime: number }>()

  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      const authReq = req as ApiKeyAuthenticatedRequest
      if (!authReq.apiKey) {
        return res.status(401).json({
          success: false,
          message: '未通过API Key验证'
        })
      }

      const apiKeyId = authReq.apiKey.id
      const now = Date.now()
      const hourInMs = 60 * 60 * 1000

      // 获取或创建请求计数
      const current = requestCounts.get(apiKeyId) || { count: 0, resetTime: now + hourInMs }

      // 检查是否需要重置计数
      if (now > current.resetTime) {
        current.count = 0
        current.resetTime = now + hourInMs
      }

      // 检查是否超过限制
      if (current.count >= requestsPerHour) {
        return res.status(429).json({
          success: false,
          message: '请求频率超过限制',
          code: 'RATE_LIMIT_EXCEEDED',
          retryAfter: Math.ceil((current.resetTime - now) / 1000)
        })
      }

      // 增加计数
      current.count++
      requestCounts.set(apiKeyId, current)

      next()
    } catch (error: any) {
      console.error('API Key速率限制检查失败:', error)
      next()
    }
  }
}