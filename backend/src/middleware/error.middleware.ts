import { Request, Response, NextFunction } from 'express'
import { ZodError } from 'zod'
import { AppError, ErrorResponse } from '@/utils/errors.util'

// 保持向后兼容性的旧错误类型
export interface ApiError extends Error {
  statusCode?: number
  code?: string
  details?: any
}

export class CustomError extends Error implements ApiError {
  public statusCode: number
  public code: string
  public details?: any

  constructor(message: string, statusCode = 500, code = 'INTERNAL_SERVER_ERROR', details?: any) {
    super(message)
    this.statusCode = statusCode
    this.code = code
    this.details = details
    this.name = 'CustomError'
  }
}

export class ValidationError extends CustomError {
  constructor(message: string, details?: any) {
    super(message, 400, 'VALIDATION_ERROR', details)
    this.name = 'ValidationError'
  }
}

export class NotFoundError extends CustomError {
  constructor(message: string = '资源不存在') {
    super(message, 404, 'NOT_FOUND')
    this.name = 'NotFoundError'
  }
}

export class UnauthorizedError extends CustomError {
  constructor(message: string = '认证失败') {
    super(message, 401, 'UNAUTHORIZED')
    this.name = 'UnauthorizedError'
  }
}

export class ForbiddenError extends CustomError {
  constructor(message: string = '权限不足') {
    super(message, 403, 'FORBIDDEN')
    this.name = 'ForbiddenError'
  }
}

export class ConflictError extends CustomError {
  constructor(message: string = '资源冲突') {
    super(message, 409, 'CONFLICT')
    this.name = 'ConflictError'
  }
}

export function errorHandler(
  error: ApiError,
  req: Request,
  res: Response,
  _next: NextFunction
): void {
  // 默认错误信息
  let statusCode = 500
  let message = '内部服务器错误'
  let code = 'INTERNAL_SERVER_ERROR'
  let details: any = undefined

  // 处理不同类型的错误
  if (error instanceof AppError) {
    const response = ErrorResponse.format(error, process.env['NODE_ENV'] === 'development')
    res.status(error.statusCode).json(response)
    return
  } else if (error instanceof CustomError) {
    statusCode = error.statusCode
    message = error.message
    code = error.code
    details = error.details
  } else if (error instanceof ZodError) {
    statusCode = 400
    message = '参数验证失败'
    code = 'VALIDATION_ERROR'
    details = error.errors.map(err => ({
      field: err.path.join('.'),
      message: err.message
    }))
  } else if (error.name === 'PrismaClientKnownRequestError') {
    // Prisma 数据库错误
    const prismaError = error as any
    
    switch (prismaError.code) {
      case 'P2002':
        statusCode = 409
        message = '数据已存在，违反唯一约束'
        code = 'DUPLICATE_ENTRY'
        details = prismaError.meta
        break
      case 'P2025':
        statusCode = 404
        message = '记录不存在'
        code = 'RECORD_NOT_FOUND'
        break
      default:
        statusCode = 500
        message = '数据库操作失败'
        code = 'DATABASE_ERROR'
        details = process.env['NODE_ENV'] === 'development' ? prismaError.meta : undefined
    }
  } else if (error.name === 'JsonWebTokenError') {
    statusCode = 401
    message = 'JWT token 无效'
    code = 'INVALID_TOKEN'
  } else if (error.name === 'TokenExpiredError') {
    statusCode = 401
    message = 'JWT token 已过期'
    code = 'TOKEN_EXPIRED'
  } else if (error.statusCode) {
    statusCode = error.statusCode
    message = error.message
    code = error.code || 'HTTP_ERROR'
  }

  // 记录错误日志
  console.error('Error occurred:', {
    url: req.url,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    error: {
      name: error.name,
      message: error.message,
      stack: process.env['NODE_ENV'] === 'development' ? error.stack : undefined
    }
  })

  // 返回错误响应
  const response: any = {
    success: false,
    message,
    code
  }

  if (details) {
    response.details = details
  }

  // 开发环境下返回错误堆栈
  if (process.env['NODE_ENV'] === 'development' && error.stack) {
    response.stack = error.stack
  }

  res.status(statusCode).json(response)
}

// 异步错误处理包装器
export function asyncHandler<T extends any[]>(
  fn: (req: Request, res: Response, next: NextFunction, ...args: T) => Promise<any>
) {
  return (req: Request, res: Response, next: NextFunction, ...args: T) => {
    Promise.resolve(fn(req, res, next, ...args)).catch(next)
  }
}