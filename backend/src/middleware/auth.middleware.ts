import { Request, Response, NextFunction } from 'express'
import jwt from 'jsonwebtoken'
import { prisma } from '@/config/database.config'
import { CacheService } from '@/config/redis.config'
import { UnauthorizedError, ForbiddenError } from './error.middleware'
import { AutoRefreshRequest } from './auto-refresh.middleware'
import { 
  hasPermission, 
  hasAnyPermission,
  expandPermissions, 
  filterValidPermissions, 
  checkOwnershipOrAdmin as checkOwnership
} from '@/utils/permission.util'
import { ADMIN_ALL } from '@/constants/permissions'
import { UserAnalyticsService } from '@/services/user-analytics.service'

export interface AuthenticatedRequest extends AutoRefreshRequest {
  user?: {
    id: string
    username: string
    email: string
    roles: string[]
    permissions: string[]
  }
}

interface JWTPayload {
  userId: string
  username: string
  email: string
  role: string
  permissions: string[]
  iat: number
  exp: number
}

// JWT 验证中间件
export async function authenticateToken(
  req: AuthenticatedRequest,
  _res: Response,
  next: NextFunction
): Promise<void> {
  try {
    const authHeader = req.headers.authorization
    const token = authHeader && authHeader.split(' ')[1]

    if (!token) {
      throw new UnauthorizedError('访问令牌不存在')
    }

    // 检查token是否在黑名单中
    const isBlacklisted = await CacheService.exists(`blacklist:${token}`)
    if (isBlacklisted) {
      throw new UnauthorizedError('访问令牌已失效')
    }

    // 验证 JWT
    let decoded: JWTPayload
    try {
      decoded = jwt.verify(token, process.env['JWT_SECRET']!) as JWTPayload
    } catch (jwtError) {
      // 如果token验证失败，检查是否是由于自动刷新导致的
      if (req.refreshedToken) {
        console.log('🔄 使用自动刷新的token重新验证')
        try {
          decoded = jwt.verify(req.refreshedToken, process.env['JWT_SECRET']!) as JWTPayload
        } catch (refreshedTokenError) {
          console.error('刷新后的token验证也失败:', refreshedTokenError)
          throw jwtError // 抛出原始错误
        }
      } else {
        throw jwtError
      }
    }
    // 尝试从缓存获取用户信息
    let cached = await CacheService.get<any>(`user:${decoded.userId}`)
    let userInfo = null
    // 如果缓存存在，检查是否需要解析
    if (cached) {
      userInfo = typeof cached === 'string' ? JSON.parse(cached) : cached
    }

    if (!userInfo) {
      // 从数据库获取用户信息
      const user = await prisma.user.findUnique({
        where: { id: decoded.userId },
        include: {
          userRoles: {
            include: {
              role: true
            }
          }
        }
      })

      if (!user || user.status !== 'ACTIVE') {
        throw new UnauthorizedError('用户不存在或已被禁用')
      }

      // 提取角色和权限
      const roles = user.userRoles.map(ur => ur.role.name)
      const permissions = user.userRoles.reduce<string[]>((acc, ur) => {
        const rolePermissions = typeof ur.role.permissions === 'string' 
          ? JSON.parse(ur.role.permissions) 
          : ur.role.permissions
        return [...acc, ...rolePermissions]
      }, [])
      
      // 过滤有效权限并展开隐含权限
      const validPermissions = filterValidPermissions([...new Set(permissions)])
      const expandedPermissions = expandPermissions(validPermissions)

      userInfo = {
        id: user.id,
        username: user.username,
        email: user.email,
        roles,
        permissions: expandedPermissions
      }

      // 缓存用户信息 (5分钟)
      await CacheService.set(`user:${decoded.userId}`, userInfo, 300)
    } else {
      // 如果从缓存获取，但JWT中有权限信息，优先使用JWT中的权限
      if (decoded.permissions && decoded.permissions.length > 0) {
        userInfo.permissions = decoded.permissions
      }
    }

    req.user = userInfo
    
    // 更新用户会话活动（异步执行，不影响请求响应）
    if (req.headers.authorization) {
      const token = req.headers.authorization.split(' ')[1]
      if (token) {
        UserAnalyticsService.updateSessionActivity(token, {
          pageView: true,
          operationType: req.method === 'GET' ? 'VIEW' : req.method,
          featureName: extractFeatureName(req.path)
        }).catch(error => {
          console.error('更新用户会话活动失败:', error)
        })
      }
    }
    
    next()
  } catch (error) {
    
    if (error instanceof jwt.JsonWebTokenError) {
      next(new UnauthorizedError('访问令牌无效'))
    } else if (error instanceof jwt.TokenExpiredError) {
      next(new UnauthorizedError('访问令牌已过期'))
    } else {
      next(error)
    }
  }
}

// 权限检查中间件工厂
export function requirePermissions(...permissions: string[]) {
  return async (req: AuthenticatedRequest, _res: Response, next: NextFunction) => {
    try {
      if (!req.user) {
        throw new UnauthorizedError('用户未认证')
      }

      const userPermissions = req.user.permissions
      const hasRequiredPermission = hasPermission(userPermissions, permissions)

      if (!hasRequiredPermission) {
        throw new ForbiddenError(`需要权限: ${permissions.join(', ')}`)
      }

      next()
    } catch (error) {
      next(error)
    }
  }
}

// 角色检查中间件工厂
export function requireRoles(...roles: string[]) {
  return async (req: AuthenticatedRequest, _res: Response, next: NextFunction) => {
    try {
      if (!req.user) {
        throw new UnauthorizedError('用户未认证')
      }

      const userRoles = req.user.roles
      const hasRole = roles.some(role => 
        userRoles.includes(role) || userRoles.includes('admin')
      )

      if (!hasRole) {
        throw new ForbiddenError(`需要角色: ${roles.join(', ')}`)
      }

      next()
    } catch (error) {
      next(error)
    }
  }
}

// 可选认证中间件（不强制要求认证）
export async function optionalAuth(
  req: AuthenticatedRequest,
  _res: Response,
  next: NextFunction
): Promise<void> {
  try {
    const authHeader = req.headers.authorization
    const token = authHeader && authHeader.split(' ')[1]

    if (token) {
      const decoded = jwt.verify(token, process.env['JWT_SECRET']!) as JWTPayload
      
      const userInfo = await CacheService.get<any>(`user:${decoded.userId}`)
      if (userInfo) {
        req.user = userInfo
      }
    }

    next()
  } catch (error) {
    // 可选认证失败时不抛出错误，继续执行
    next()
  }
}

// 任一权限检查中间件工厂
export function requireAnyPermission(...permissions: string[]) {
  return async (req: AuthenticatedRequest, _res: Response, next: NextFunction) => {
    try {
      if (!req.user) {
        throw new UnauthorizedError('用户未认证')
      }

      const userPermissions = req.user.permissions
      const hasRequiredPermission = hasAnyPermission(userPermissions, permissions)

      if (!hasRequiredPermission) {
        throw new ForbiddenError(`需要以下权限之一: ${permissions.join(', ')}`)
      }

      next()
    } catch (error) {
      next(error)
    }
  }
}

// 超级管理员权限检查中间件
export function requireSuperAdmin() {
  return async (req: AuthenticatedRequest, _res: Response, next: NextFunction) => {
    try {
      if (!req.user) {
        throw new UnauthorizedError('用户未认证')
      }

      const userPermissions = req.user.permissions
      if (!userPermissions.includes(ADMIN_ALL)) {
        throw new ForbiddenError('需要超级管理员权限')
      }

      next()
    } catch (error) {
      next(error)
    }
  }
}

// 资源所有者或管理员权限检查
export function requireOwnershipOrAdmin(
  getUserId: (req: Request) => string,
  adminPermissions: string[] = [ADMIN_ALL]
) {
  return async (req: AuthenticatedRequest, _res: Response, next: NextFunction) => {
    try {
      if (!req.user) {
        throw new UnauthorizedError('用户未认证')
      }

      const targetUserId = getUserId(req)
      const hasAccess = checkOwnership(req, targetUserId, adminPermissions)

      if (!hasAccess) {
        throw new ForbiddenError('只能访问自己的资源或需要管理员权限')
      }

      next()
    } catch (error) {
      next(error)
    }
  }
}

// 动态权限检查中间件
export function requireDynamicPermissions(
  getPermissions: (req: AuthenticatedRequest) => string[] | Promise<string[]>
) {
  return async (req: AuthenticatedRequest, _res: Response, next: NextFunction) => {
    try {
      if (!req.user) {
        throw new UnauthorizedError('用户未认证')
      }

      const requiredPermissions = await Promise.resolve(getPermissions(req))
      const userPermissions = req.user.permissions
      const hasRequiredPermission = hasPermission(userPermissions, requiredPermissions)

      if (!hasRequiredPermission) {
        throw new ForbiddenError(`需要权限: ${requiredPermissions.join(', ')}`)
      }

      next()
    } catch (error) {
      next(error)
    }
  }
}

// 条件权限检查中间件
export function requireConditionalPermissions(
  condition: (req: AuthenticatedRequest) => boolean | Promise<boolean>,
  permissions: string[]
) {
  return async (req: AuthenticatedRequest, _res: Response, next: NextFunction) => {
    try {
      if (!req.user) {
        throw new UnauthorizedError('用户未认证')
      }

      const shouldCheck = await Promise.resolve(condition(req))
      
      if (shouldCheck) {
        const userPermissions = req.user.permissions
        const hasRequiredPermission = hasPermission(userPermissions, permissions)

        if (!hasRequiredPermission) {
          throw new ForbiddenError(`需要权限: ${permissions.join(', ')}`)
        }
      }

      next()
    } catch (error) {
      next(error)
    }
  }
}

// 资源级权限检查中间件
export function requireResourcePermission(
  _getResourceInfo: (req: AuthenticatedRequest) => { type: string; id: string } | Promise<{ type: string; id: string }>,
  basePermission: string,
  viewAllPermission?: string
) {
  return async (req: AuthenticatedRequest, _res: Response, next: NextFunction) => {
    try {
      if (!req.user) {
        throw new UnauthorizedError('用户未认证')
      }

      const userPermissions = req.user.permissions
      
      // 检查基础权限
      if (!hasPermission(userPermissions, basePermission)) {
        throw new ForbiddenError(`需要权限: ${basePermission}`)
      }

      // 如果有查看所有权限，则允许访问
      if (viewAllPermission && hasPermission(userPermissions, viewAllPermission)) {
        next()
        return
      }

      // TODO: 这里可以添加具体的资源级权限检查逻辑
      // 比如检查用户是否为资源的创建者或负责人
      
      next()
    } catch (error) {
      next(error)
    }
  }
}

// 部门权限检查中间件
export function requireDepartmentAccess(
  getDepartment: (req: AuthenticatedRequest) => string | Promise<string>
) {
  return async (req: AuthenticatedRequest, _res: Response, next: NextFunction) => {
    try {
      if (!req.user) {
        throw new UnauthorizedError('用户未认证')
      }

      // 超级管理员可以访问所有部门
      if (req.user.permissions.includes(ADMIN_ALL)) {
        next()
        return
      }

      const targetDepartment = await Promise.resolve(getDepartment(req))
      const currentUser = await prisma.user.findUnique({
        where: { id: req.user.id },
        select: { department: true }
      })

      // 检查是否为同一部门或有跨部门权限
      if (currentUser?.department !== targetDepartment) {
        throw new ForbiddenError('只能访问本部门的资源')
      }

      next()
    } catch (error) {
      next(error)
    }
  }
}

// 提取功能名称的辅助函数
function extractFeatureName(path: string): string {
  const pathSegments = path.split('/').filter(Boolean)
  if (pathSegments.length >= 3) {
    return pathSegments[2] || 'unknown' // 通常是 /api/v1/feature-name 的格式
  }
  return 'unknown'
}

// 导出别名
export const authMiddleware = authenticateToken