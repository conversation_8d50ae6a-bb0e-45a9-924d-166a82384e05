import type { Application } from 'express'
import swaggerUi from 'swagger-ui-express'
import path from 'node:path'
import fs from 'node:fs'
import yaml from 'js-yaml'

export function setupSwagger(app: Application): void {
  try {
    const specPath = path.join(__dirname, '../../docs/openapi/openapi.yaml')
    const fileContents = fs.readFileSync(specPath, 'utf8')
    const spec = yaml.load(fileContents) as Record<string, unknown>

    app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(spec, {
      explorer: true,
      customCss: '.swagger-ui .topbar { display: none }',
      customSiteTitle: '运维服务管理系统 API 文档'
    }))

    app.get('/api-docs.json', (_req, res) => {
      res.setHeader('Content-Type', 'application/json')
      res.send(spec)
    })
  } catch (error) {
    console.error('Failed to load OpenAPI spec:', error)
  }
}
