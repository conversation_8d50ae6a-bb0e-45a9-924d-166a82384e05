import express from 'express'
import { authMiddleware } from '@/middleware/auth.middleware'
import * as realtimeController from '@/controllers/realtime.controller'

const router = express.Router()

// 所有实时推送路由都需要认证
router.use(authMiddleware)

router.get('/stats', realtimeController.getRealtimeStats)

router.get('/online-users', realtimeController.getOnlineUsers)

router.post('/push/system-metrics', realtimeController.pushSystemMetrics)

router.post('/push/system-status', realtimeController.pushSystemStatus)

router.post('/push/system-alert', realtimeController.pushSystemAlert)

router.post('/push/notification', realtimeController.pushNotification)

router.post('/push/service-event', realtimeController.pushServiceEvent)

router.post('/broadcast', realtimeController.broadcastMessage)

router.post('/test-connection', realtimeController.testConnection)

router.get('/connections', realtimeController.getConnectionDetails)

export default router