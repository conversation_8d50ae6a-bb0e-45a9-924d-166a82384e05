import { Router } from 'express'
import { AlertEngineController } from '@/controllers/alert-engine.controller'
import { authMiddleware } from '@/middleware/auth.middleware'
import { checkPermissions } from '@/middleware/permission.middleware'

const router = Router()
const alertEngineController = new AlertEngineController()

// 所有告警相关路由都需要认证
router.use(authMiddleware)

// ============ 告警规则管理 ============

// 创建告警规则 (需要管理员或系统管理员权限)
router.post(
  '/rules',
  checkPermissions(['admin:all']),
  alertEngineController.createAlertRule
)

// 更新告警规则
router.put(
  '/rules/:ruleId',
  checkPermissions(['admin:all']),
  alertEngineController.updateAlertRule
)

// 删除告警规则
router.delete(
  '/rules/:ruleId',
  checkPermissions(['admin:all']),
  alertEngineController.deleteAlertRule
)

// 获取所有告警规则
router.get('/rules', alertEngineController.getAlertRules)

// 获取单个告警规则详情
router.get('/rules/:ruleId', alertEngineController.getAlertRule)

// 测试告警规则
router.post(
  '/rules/:ruleId/test',
  checkPermissions(['admin:all']),
  alertEngineController.testAlertRule
)

// ============ 告警管理 ============

// 获取告警列表
router.get('/alerts', alertEngineController.getAlerts)

// 确认告警
router.patch('/alerts/:alertId/acknowledge', alertEngineController.acknowledgeAlert)

// 解决告警
router.patch('/alerts/:alertId/resolve', alertEngineController.resolveAlert)

// 批量操作告警
router.patch(
  '/alerts/bulk',
  checkPermissions(['admin:all', 'service:write']),
  alertEngineController.bulkAlertOperation
)

// ============ 统计和分析 ============

// 获取告警统计信息
router.get('/statistics', alertEngineController.getAlertStatistics)

// 获取告警趋势
router.get('/trends', alertEngineController.getAlertTrends)

// ============ 配置和选项 ============

// 获取告警配置选项
router.get('/options', alertEngineController.getAlertOptions)

export { router as alertEngineRoutes }