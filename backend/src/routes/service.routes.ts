import { Router } from 'express'
import {
  getServices,
  getService,
  createService,
  updateService,
  deleteService,
  getServiceWorkLogs,
  createServiceWorkLog,
  getServiceComments,
  createServiceComment,
  getServiceStats,
  exportServiceReportPDF
} from '@/controllers/service.controller'
import { authenticateToken, requirePermissions } from '@/middleware/auth.middleware'
import { asyncHandler } from '@/utils/asyncHandler'
import { exportServiceReportPDFAsync, getExportJobStatus, downloadExportJob } from '@/controllers/service.controller'

const router = Router()

router.get('/', authenticateToken, requirePermissions('service:read'), getServices)
router.get('/stats', authenticateToken, requirePermissions('service:read'), getServiceStats)
router.get('/:id', authenticateToken, requirePermissions('service:read'), getService)
router.post('/', authenticateToken, requirePermissions('service:write'), createService)
router.put('/:id', authenticateToken, requirePermissions('service:write'), updateService)
router.delete('/:id', authenticateToken, requirePermissions('service:write'), deleteService)
router.get('/:serviceId/work-logs', authenticateToken, requirePermissions('service:read'), getServiceWorkLogs)
router.post('/:serviceId/work-logs', authenticateToken, requirePermissions('service:write'), createServiceWorkLog)
router.get('/:serviceId/comments', authenticateToken, requirePermissions('service:read'), getServiceComments)
router.post('/:serviceId/comments', authenticateToken, requirePermissions('service:write'), createServiceComment)

// PDF报告导出路由
router.get('/:id/export/pdf', authenticateToken, requirePermissions('service:read'), exportServiceReportPDF)

// 异步导出
router.post('/:id/export/pdf/async', authenticateToken, requirePermissions('service:read'), exportServiceReportPDFAsync)
router.get('/export/pdf/jobs/:jobId', authenticateToken, requirePermissions('service:read'), getExportJobStatus)
router.get('/export/pdf/jobs/:jobId/download', authenticateToken, requirePermissions('service:read'), downloadExportJob)

router.get(
  '/:serviceId/history',
  authenticateToken,
  requirePermissions('service:read'),
  asyncHandler(async (req, res) => {
    const { serviceId } = req.params

    if (!serviceId) {
      res.status(400).json({ success: false, message: 'serviceId参数缺失' })
      return
    }

    const { getServiceOperationHistory } = await import('@/utils/service-operation-history.util')
    const history = await getServiceOperationHistory(serviceId)

    res.json({ success: true, data: history })
  })
)

export default router
