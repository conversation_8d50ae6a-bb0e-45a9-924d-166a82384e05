import { Router } from 'express'
import { authMiddleware } from '@/middleware/auth.middleware'
import { getSystemConfigGroups, getSystemConfigCategories, getPublicConfigs, getSystemInfo, getHealthStatus, testConnection, clearCache, getConfigHistoryByKey, resetConfigByKey, batchResetConfigs, exportConfigs, getSystemConfigs, getSystemConfig, createSystemConfig, updateSystemConfig, batchUpdateConfigs, deleteSystemConfig, backupSystemConfigs, restoreSystemConfigs, testEmailConfig, testTemplateEmail, getEmailServiceStatus, testServiceConnection } from '@/controllers/system-config.controller'

const router = Router()

router.use(authMiddleware)

router.get('/groups', getSystemConfigGroups)

router.get('/categories', getSystemConfigCategories)

router.get('/public', getPublicConfigs)

// ==================== 前端额外所需接口 ====================
// 系统信息与健康状态
router.get('/system-info', getSystemInfo)
router.get('/health', getHealthStatus)
router.post('/test/:type', testConnection)
// 清理缓存
router.post('/clear-cache', clearCache)

// ==================== 配置测试功能 ====================
// 邮件服务相关测试
router.post('/test-email', testEmailConfig)
router.post('/test-template-email', testTemplateEmail)
router.get('/email-status', getEmailServiceStatus)
// 通用服务测试
router.post('/test-service', testServiceConnection)

// 历史与重置
router.get('/:key/history', getConfigHistoryByKey)
router.post('/:key/reset', resetConfigByKey)
router.post('/batch-reset', batchResetConfigs)
// 导出（对齐前端 service.exportConfigs）
router.get('/export', exportConfigs)

// ==================== 基本CRUD端点 ====================

router.get('/', getSystemConfigs)

router.get('/:key', getSystemConfig)

router.post('/', createSystemConfig)

router.put('/:key', updateSystemConfig)

router.post('/batch', batchUpdateConfigs)

router.delete('/:key', deleteSystemConfig)

router.post('/backup', backupSystemConfigs)

router.post('/restore', restoreSystemConfigs)

export default router