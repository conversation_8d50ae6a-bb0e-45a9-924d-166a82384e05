import { Router } from 'express'
import {
  getSlaTemplates,
  getSlaTemplate,
  createSlaTemplate,
  updateSlaTemplate,
  deleteSlaTemplate,
  getSlaStats,
  getSlaPerformanceReport,
  getRecommendedSlaTemplates,
  getServiceSlaStatus,
  getActiveSlaMonitoring,
  getSlaPerformanceDashboard,
  triggerSlaAlertCheck,
  getSlaRiskAlerts
} from '@/controllers/sla.controller'
import { authenticateToken, requirePermissions } from '@/middleware/auth.middleware'

const router = Router()

router.get('/templates', authenticateToken, requirePermissions('sla:read'), getSlaTemplates)

router.get('/templates/recommend', authenticateToken, requirePermissions('sla:read'), getRecommendedSlaTemplates)

router.get('/templates/:id', authenticateToken, requirePermissions('sla:read'), getSlaTemplate)

router.post('/templates', authenticateToken, requirePermissions('sla:write'), createSlaTemplate)

router.put('/templates/:id', authenticateToken, requirePermissions('sla:write'), updateSlaTemplate)

router.delete('/templates/:id', authenticateToken, requirePermissions('sla:write'), deleteSlaTemplate)

router.get('/stats', authenticateToken, requirePermissions('sla:read'), getSlaStats)

router.get('/templates/:id/performance', authenticateToken, requirePermissions('sla:read'), getSlaPerformanceReport)

router.get('/services/:serviceId/status', authenticateToken, requirePermissions('sla:read'), getServiceSlaStatus)

router.get('/monitoring/active', authenticateToken, requirePermissions('sla:read'), getActiveSlaMonitoring)

router.get('/dashboard', authenticateToken, requirePermissions('sla:read'), getSlaPerformanceDashboard)

router.post('/alerts/trigger', authenticateToken, requirePermissions('sla:write'), triggerSlaAlertCheck)

router.get('/risks', authenticateToken, requirePermissions('sla:read'), getSlaRiskAlerts)

export default router
