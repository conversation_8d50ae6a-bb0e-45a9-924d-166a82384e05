import { Router } from 'express'
import { authMiddleware, requirePermissions } from '@/middleware/auth.middleware'
import { UploadService } from '@/services/upload.service'
import {
  getRoles,
  getAllRoles,
  getRole,
  createRole,
  updateRole,
  deleteRole,
  getSystemPermissions,
  getPermissionGroups,
  getRoleTemplates,
  createRoleFromTemplate,
  duplicateRole,
  getRoleStats,
  getTemplateUsageStats,
  compareRoleWithTemplate,
  getRolePermissionAnalysis,
  batchOperationRoles,
  exportRoles,
  importRoles,
  syncRolePermissions
} from '@/controllers/role.controller'

const router = Router()

router.get('/', authMiddleware, requirePermissions('role:read'), getRoles)

router.get('/all', authMiddleware, requirePermissions('role:read'), getAllRoles)

router.get('/permissions', authMiddleware, requirePermissions('role:read'), getSystemPermissions)

router.get('/permissions/groups', authMiddleware, requirePermissions('role:read'), getPermissionGroups)

router.get('/templates', authMiddleware, requirePermissions('role:read'), getRoleTemplates)

router.get('/stats', authMiddleware, requirePermissions('role:read'), getRoleStats)

router.get('/template-usage', authMiddleware, requirePermissions('role:read'), getTemplateUsageStats)

router.get('/:id/compare', authMiddleware, requirePermissions('role:read'), compareRoleWithTemplate)

router.get('/:id/analysis', authMiddleware, requirePermissions('role:read'), getRolePermissionAnalysis)

router.get('/:id', authMiddleware, requirePermissions('role:read'), getRole)

router.post('/', authMiddleware, requirePermissions('role:write'), createRole)

router.post('/from-template', authMiddleware, requirePermissions('role:write'), createRoleFromTemplate)

router.post('/:id/duplicate', authMiddleware, requirePermissions('role:write'), duplicateRole)

router.put('/:id', authMiddleware, requirePermissions('role:write'), updateRole)

router.post('/batch', authMiddleware, requirePermissions('role:delete'), batchOperationRoles)

router.get('/export', authMiddleware, requirePermissions('role:read'), exportRoles)

router.post('/import', 
  authMiddleware, 
  requirePermissions('role:write'),
  UploadService.createUploadMiddleware({
    maxFileSize: 5 * 1024 * 1024, // 5MB
    allowedTypes: [
      'text/csv',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    ],
    maxFiles: 1
  }),
  importRoles
)

router.post('/sync-permissions', authMiddleware, requirePermissions('role:manage'), syncRolePermissions)

router.delete('/:id', authMiddleware, requirePermissions('role:delete'), deleteRole)

export default router