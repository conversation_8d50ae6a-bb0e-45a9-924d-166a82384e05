import { Router } from 'express'
import {
  getArchives,
  getArchive,
  createArchive,
  updateArchive,
  deleteArchive,
  getArchiveStats,
  getCustomerArchives
} from '@/controllers/archive.controller'
import { authenticateToken, requirePermissions } from '@/middleware/auth.middleware'

const router = Router()

router.get('/', authenticateToken, requirePermissions('archive:read'), getArchives)

router.get('/stats', authenticateToken, requirePermissions('archive:read'), getArchiveStats)

router.get('/customer/:customerId', authenticateToken, requirePermissions('archive:read'), getCustomerArchives)

router.get('/:id', authenticateToken, requirePermissions('archive:read'), getArchive)

router.post('/', authenticateToken, requirePermissions('archive:write'), createArchive)

router.put('/:id', authenticateToken, requirePermissions('archive:write'), updateArchive)

router.delete('/:id', authenticateToken, requirePermissions('archive:write'), deleteArchive)

export default router