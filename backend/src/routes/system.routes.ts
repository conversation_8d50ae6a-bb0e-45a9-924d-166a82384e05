import { Router } from 'express'
import { authMiddleware } from '@/middleware/auth.middleware'
import * as SystemController from '@/controllers/system.controller'

const router = Router()

// 所有系统监控路由都需要认证
router.use(authMiddleware)

// 系统状态和监控
router.get('/status', SystemController.getSystemStatus)
router.get('/resources', SystemController.getSystemResources)
router.get('/health', SystemController.getHealthCheck)
router.get('/database', SystemController.getDatabaseStatus)
router.get('/redis', SystemController.getRedisStatus)
router.get('/performance', SystemController.getPerformanceMetrics)

// 告警规则管理
router.post('/alert-rules', SystemController.createAlertRule)
router.get('/alert-rules', SystemController.getAlertRules)
router.put('/alert-rules/:id', SystemController.updateAlertRule)
router.delete('/alert-rules/:id', SystemController.deleteAlertRule)

// 告警历史和处理
router.get('/alerts', SystemController.getAlerts)
router.put('/alerts/:id/acknowledge', SystemController.acknowledgeAlert)
router.put('/alerts/:id/resolve', SystemController.resolveAlert)
router.get('/alert-stats', SystemController.getAlertStats)

// 系统配置管理路由
router.get('/configs', SystemController.getSystemConfigs)
router.get('/configs/:category/:key', SystemController.getSystemConfig)
router.post('/configs', SystemController.createSystemConfig)
router.put('/configs/:category/:key', SystemController.updateSystemConfig)
router.patch('/configs/batch', SystemController.batchUpdateSystemConfigs)
router.delete('/configs/:category/:key', SystemController.deleteSystemConfig)
router.post('/configs/:category/:key/reset', SystemController.resetConfigToDefault)

// 配置历史和管理
router.get('/config-history', SystemController.getConfigHistory)
router.post('/configs/test-email', SystemController.testEmailConfig)
router.post('/configs/test-sms', SystemController.testSmsConfig)
router.get('/configs/export', SystemController.exportConfigs)
router.post('/configs/import', SystemController.importConfigs)
router.post('/configs/initialize', SystemController.initializeDefaultConfigs)

export default router