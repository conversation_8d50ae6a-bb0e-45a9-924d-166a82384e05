import express from 'express'
import { TriggerController } from '@/controllers/trigger.controller'
import { authenticateToken } from '@/middleware/auth.middleware'
import { checkPermissions } from '@/middleware/permission.middleware'

const router = express.Router()

// 应用认证中间件到所有路由
router.use(authenticateToken)

/**
 * 触发器管理路由
 * 提供完整的触发器系统API
 */

// ========== 系统状态和统计 ==========

/**
 * @swagger
 * /api/v1/triggers/stats:
 *   get:
 *     summary: 获取触发器系统统计信息
 *     tags: [Triggers]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 触发器系统统计信息
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     eventTrigger:
 *                       type: object
 *                     cronTrigger:
 *                       type: object
 *                     conditionalTrigger:
 *                       type: object
 *                     webhookTrigger:
 *                       type: object
 */
router.get('/stats', 
  checkPermissions('system:view'),
  TriggerController.getSystemStats
)

/**
 * @swagger
 * /api/v1/triggers:
 *   get:
 *     summary: 获取所有触发器列表
 *     tags: [Triggers]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 所有触发器列表
 */
router.get('/', 
  checkPermissions('trigger:view'),
  TriggerController.getAllTriggers
)

// ========== 事件触发器相关 ==========

/**
 * @swagger
 * /api/v1/triggers/event:
 *   post:
 *     summary: 手动触发事件
 *     tags: [Event Triggers]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - eventType
 *               - data
 *             properties:
 *               eventType:
 *                 type: string
 *                 description: 事件类型
 *                 example: SERVICE_CREATED
 *               data:
 *                 type: object
 *                 description: 事件数据
 *               source:
 *                 type: string
 *                 description: 事件源
 *                 default: manual
 *     responses:
 *       200:
 *         description: 事件触发成功
 */
router.post('/event', 
  checkPermissions('trigger:execute'),
  TriggerController.triggerEvent
)

/**
 * @swagger
 * /api/v1/triggers/event/statistics:
 *   get:
 *     summary: 获取事件触发统计
 *     tags: [Event Triggers]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: timeRange
 *         schema:
 *           type: string
 *           description: 时间范围，格式：startTime,endTime
 *     responses:
 *       200:
 *         description: 事件统计信息
 */
router.get('/event/statistics', 
  checkPermissions('trigger:view'),
  TriggerController.getEventStatistics
)

/**
 * @swagger
 * /api/v1/triggers/event/recent:
 *   get:
 *     summary: 获取最近事件
 *     tags: [Event Triggers]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: eventType
 *         schema:
 *           type: string
 *           description: 事件类型过滤
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 50
 *           description: 返回数量限制
 *     responses:
 *       200:
 *         description: 最近事件列表
 */
router.get('/event/recent', 
  checkPermissions('trigger:view'),
  TriggerController.getRecentEvents
)

// ========== 定时触发器相关 ==========

/**
 * @swagger
 * /api/v1/triggers/cron:
 *   post:
 *     summary: 创建定时任务
 *     tags: [Cron Triggers]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - jobId
 *               - schedule
 *             properties:
 *               jobId:
 *                 type: string
 *                 description: 任务ID
 *               schedule:
 *                 type: string
 *                 description: Cron表达式
 *                 example: "0 9 * * *"
 *               description:
 *                 type: string
 *                 description: 任务描述
 *               workflowId:
 *                 type: string
 *                 description: 关联工作流ID
 *               enabled:
 *                 type: boolean
 *                 default: true
 *               timezone:
 *                 type: string
 *                 description: 时区
 *               maxExecutions:
 *                 type: integer
 *                 description: 最大执行次数
 *               rateLimit:
 *                 type: integer
 *                 description: 速率限制
 *     responses:
 *       201:
 *         description: 定时任务创建成功
 */
router.post('/cron', 
  checkPermissions('trigger:create'),
  TriggerController.createCronJob
)

/**
 * @swagger
 * /api/v1/triggers/cron/{jobId}:
 *   get:
 *     summary: 获取定时任务信息
 *     tags: [Cron Triggers]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: jobId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: 定时任务信息
 */
router.get('/cron/:jobId', 
  checkPermissions('trigger:view'),
  TriggerController.getCronJobInfo
)

/**
 * @swagger
 * /api/v1/triggers/cron/{jobId}/run:
 *   post:
 *     summary: 手动执行定时任务
 *     tags: [Cron Triggers]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: jobId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: 任务执行结果
 */
router.post('/cron/:jobId/run', 
  checkPermissions('trigger:execute'),
  TriggerController.runCronJobNow
)

/**
 * @swagger
 * /api/v1/triggers/cron/{jobId}/control:
 *   post:
 *     summary: 控制定时任务（启动/停止）
 *     tags: [Cron Triggers]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: jobId
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - action
 *             properties:
 *               action:
 *                 type: string
 *                 enum: [start, stop]
 *                 description: 操作类型
 *     responses:
 *       200:
 *         description: 操作成功
 */
router.post('/cron/:jobId/control', 
  checkPermissions('trigger:manage'),
  TriggerController.controlCronJob
)

/**
 * @swagger
 * /api/v1/triggers/cron/{jobId}/history:
 *   get:
 *     summary: 获取定时任务执行历史
 *     tags: [Cron Triggers]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: jobId
 *         required: true
 *         schema:
 *           type: string
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 20
 *     responses:
 *       200:
 *         description: 执行历史记录
 */
router.get('/cron/:jobId/history', 
  checkPermissions('trigger:view'),
  TriggerController.getCronJobHistory
)

// ========== 条件触发器相关 ==========

/**
 * @swagger
 * /api/v1/triggers/condition:
 *   post:
 *     summary: 创建条件触发器
 *     tags: [Conditional Triggers]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - triggerId
 *               - conditions
 *             properties:
 *               triggerId:
 *                 type: string
 *               conditions:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     field:
 *                       type: string
 *                     operator:
 *                       type: string
 *                       enum: [eq, ne, gt, gte, lt, lte, contains, starts_with, ends_with, matches, exists, in, not_in]
 *                     value:
 *                       description: 比较值
 *                     source:
 *                       type: string
 *                       enum: [database, api, metric, cache, variable]
 *                     query:
 *                       type: string
 *                       description: 查询语句或URL
 *                     description:
 *                       type: string
 *               operator:
 *                 type: string
 *                 enum: [AND, OR]
 *                 default: AND
 *               checkInterval:
 *                 type: integer
 *                 default: 300
 *                 description: 检查间隔（秒）
 *               cooldownPeriod:
 *                 type: integer
 *                 description: 冷却期（秒）
 *               description:
 *                 type: string
 *               workflowId:
 *                 type: string
 *               enabled:
 *                 type: boolean
 *                 default: true
 *     responses:
 *       201:
 *         description: 条件触发器创建成功
 */
router.post('/condition', 
  checkPermissions('trigger:create'),
  TriggerController.createConditionalTrigger
)

/**
 * @swagger
 * /api/v1/triggers/condition/{triggerId}:
 *   get:
 *     summary: 获取条件触发器信息
 *     tags: [Conditional Triggers]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: triggerId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: 条件触发器信息
 */
router.get('/condition/:triggerId', 
  checkPermissions('trigger:view'),
  TriggerController.getConditionalTriggerInfo
)

/**
 * @swagger
 * /api/v1/triggers/condition/{triggerId}/evaluate:
 *   post:
 *     summary: 强制评估条件触发器
 *     tags: [Conditional Triggers]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: triggerId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: 评估结果
 */
router.post('/condition/:triggerId/evaluate', 
  checkPermissions('trigger:execute'),
  TriggerController.forceEvaluateCondition
)

/**
 * @swagger
 * /api/v1/triggers/condition/{triggerId}/history:
 *   get:
 *     summary: 获取条件评估历史
 *     tags: [Conditional Triggers]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: triggerId
 *         required: true
 *         schema:
 *           type: string
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 20
 *     responses:
 *       200:
 *         description: 评估历史记录
 */
router.get('/condition/:triggerId/history', 
  checkPermissions('trigger:view'),
  TriggerController.getConditionEvaluationHistory
)

// ========== Webhook触发器相关 ==========

/**
 * @swagger
 * /api/v1/triggers/webhook:
 *   post:
 *     summary: 注册Webhook触发器
 *     tags: [Webhook Triggers]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - webhookId
 *               - endpoint
 *             properties:
 *               webhookId:
 *                 type: string
 *               endpoint:
 *                 type: string
 *                 description: 端点路径（必须以/开头）
 *               method:
 *                 type: string
 *                 enum: [GET, POST, PUT, DELETE, PATCH]
 *                 default: POST
 *               authentication:
 *                 type: object
 *                 properties:
 *                   type:
 *                     type: string
 *                     enum: [token, signature, basic]
 *                   token:
 *                     type: string
 *                   secret:
 *                     type: string
 *                   key:
 *                     type: string
 *                   username:
 *                     type: string
 *                   password:
 *                     type: string
 *               filters:
 *                 type: object
 *                 properties:
 *                   headers:
 *                     type: object
 *                   body:
 *                     type: object
 *                   eventTypes:
 *                     type: array
 *                     items:
 *                       type: string
 *               description:
 *                 type: string
 *               workflowId:
 *                 type: string
 *               enabled:
 *                 type: boolean
 *                 default: true
 *               rateLimit:
 *                 type: integer
 *                 default: 100
 *     responses:
 *       201:
 *         description: Webhook触发器注册成功
 */
router.post('/webhook', 
  checkPermissions('trigger:create'),
  TriggerController.registerWebhook
)

/**
 * @swagger
 * /api/v1/triggers/webhook/{webhookId}:
 *   get:
 *     summary: 获取Webhook触发器信息
 *     tags: [Webhook Triggers]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: webhookId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Webhook触发器信息
 */
router.get('/webhook/:webhookId', 
  checkPermissions('trigger:view'),
  TriggerController.getWebhookInfo
)

/**
 * @swagger
 * /api/v1/triggers/webhook/{webhookId}/history:
 *   get:
 *     summary: 获取Webhook请求历史
 *     tags: [Webhook Triggers]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: webhookId
 *         required: true
 *         schema:
 *           type: string
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 20
 *     responses:
 *       200:
 *         description: 请求历史记录
 */
router.get('/webhook/:webhookId/history', 
  checkPermissions('trigger:view'),
  TriggerController.getWebhookRequestHistory
)

// ========== 通用触发器操作 ==========

/**
 * @swagger
 * /api/v1/triggers/{triggerId}/control:
 *   post:
 *     summary: 控制触发器（启用/禁用）
 *     tags: [Triggers]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: triggerId
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - action
 *               - type
 *             properties:
 *               action:
 *                 type: string
 *                 enum: [enable, disable]
 *               type:
 *                 type: string
 *                 description: 触发器类型
 *     responses:
 *       200:
 *         description: 操作成功
 */
router.post('/:triggerId/control', 
  checkPermissions('trigger:manage'),
  TriggerController.controlTrigger
)

/**
 * @swagger
 * /api/v1/triggers/{triggerId}:
 *   delete:
 *     summary: 删除触发器
 *     tags: [Triggers]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: triggerId
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - type
 *             properties:
 *               type:
 *                 type: string
 *                 description: 触发器类型
 *     responses:
 *       200:
 *         description: 删除成功
 */
router.delete('/:triggerId', 
  checkPermissions('trigger:delete'),
  TriggerController.deleteTrigger
)

// ========== 工具和验证 ==========

/**
 * @swagger
 * /api/v1/triggers/cron/validate:
 *   post:
 *     summary: 验证Cron表达式
 *     tags: [Cron Triggers]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - expression
 *             properties:
 *               expression:
 *                 type: string
 *                 description: Cron表达式
 *                 example: "0 9 * * *"
 *     responses:
 *       200:
 *         description: 验证结果
 */
router.post('/cron/validate', 
  checkPermissions('trigger:view'),
  TriggerController.validateCronExpression
)

// ========== Webhook处理端点（无需认证）==========

/**
 * Webhook处理端点 - 接受所有webhook请求
 * 这个路由不需要认证，因为它是外部系统调用的
 */
router.all('/webhook/*', TriggerController.handleWebhookRequest)

export default router