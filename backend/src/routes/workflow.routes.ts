import { Router } from 'express';
import { WorkflowController } from '@/controllers/workflow.controller';
import { authMiddleware } from '@/middleware/auth.middleware';
import { checkPermissions } from '@/middleware/permission.middleware';

const router = Router();

// 应用认证中间件到所有路由
router.use(authMiddleware);

// ========== 工作流统计和监控 ==========

/**
 * @swagger
 * /api/v1/workflows/stats:
 *   get:
 *     summary: 获取工作流统计
 *     tags: [Workflows]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 获取成功
 */
router.get('/stats', checkPermissions(['workflow:read', 'admin:all'], [], false), WorkflowController.getStats);

/**
 * @swagger
 * /api/v1/workflows/running:
 *   get:
 *     summary: 获取正在运行的执行
 *     tags: [Workflows]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 获取成功
 */
router.get('/running', checkPermissions(['workflow:read', 'admin:all'], [], false), WorkflowController.getRunningExecutions);

// ========== 工作流执行管理 ==========

/**
 * @swagger
 * /api/v1/workflows/executions:
 *   get:
 *     summary: 获取执行列表
 *     tags: [Workflows]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *         description: 页码
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *         description: 每页数量
 *       - in: query
 *         name: workflowId
 *         schema:
 *           type: string
 *         description: 工作流ID
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *         description: 执行状态
 *       - in: query
 *         name: triggerType
 *         schema:
 *           type: string
 *         description: 触发类型
 *       - in: query
 *         name: startDate
 *         schema:
 *           type: string
 *           format: date-time
 *         description: 开始日期
 *       - in: query
 *         name: endDate
 *         schema:
 *           type: string
 *           format: date-time
 *         description: 结束日期
 *     responses:
 *       200:
 *         description: 获取成功
 */
router.get('/executions', checkPermissions(['workflow:read', 'admin:all'], [], false), WorkflowController.getExecutions);

/**
 * @swagger
 * /api/v1/workflows/executions/{id}:
 *   get:
 *     summary: 获取执行详情
 *     tags: [Workflows]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 执行ID
 *     responses:
 *       200:
 *         description: 获取成功
 *       404:
 *         description: 执行记录不存在
 */
router.get('/executions/:id', checkPermissions(['workflow:read', 'admin:all'], [], false), WorkflowController.getExecutionById);

/**
 * @swagger
 * /api/v1/workflows/executions/{id}/pause:
 *   post:
 *     summary: 暂停执行
 *     tags: [Workflows]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 执行ID
 *     responses:
 *       200:
 *         description: 暂停成功
 *       400:
 *         description: 暂停失败
 */
router.post('/executions/:id/pause', checkPermissions(['workflow:execute', 'admin:all'], [], false), WorkflowController.pauseExecution);

/**
 * @swagger
 * /api/v1/workflows/executions/{id}/resume:
 *   post:
 *     summary: 恢复执行
 *     tags: [Workflows]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 执行ID
 *     responses:
 *       200:
 *         description: 恢复成功
 *       400:
 *         description: 恢复失败
 */
router.post('/executions/:id/resume', checkPermissions(['workflow:execute', 'admin:all'], [], false), WorkflowController.resumeExecution);

/**
 * @swagger
 * /api/v1/workflows/executions/{id}/cancel:
 *   post:
 *     summary: 取消执行
 *     tags: [Workflows]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 执行ID
 *     responses:
 *       200:
 *         description: 取消成功
 *       400:
 *         description: 取消失败
 */
router.post('/executions/:id/cancel', checkPermissions(['workflow:execute', 'admin:all'], [], false), WorkflowController.cancelExecution);

// ========== 工作流模板管理 ==========

/**
 * @swagger
 * /api/v1/workflows/templates/presets:
 *   get:
 *     summary: 获取预设模板
 *     tags: [Workflows]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 获取成功
 */
router.get('/templates/presets', checkPermissions(['workflow:read', 'admin:all'], [], false), WorkflowController.getPresetTemplates);

/**
 * @swagger
 * /api/v1/workflows/templates:
 *   post:
 *     summary: 创建模板
 *     tags: [Workflows]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *               - category
 *               - config
 *             properties:
 *               name:
 *                 type: string
 *               description:
 *                 type: string
 *               category:
 *                 type: string
 *               config:
 *                 type: object
 *               tags:
 *                 type: array
 *                 items:
 *                   type: string
 *     responses:
 *       201:
 *         description: 创建成功
 *       400:
 *         description: 创建失败
 */
router.post('/templates', checkPermissions(['workflow:create', 'admin:all'], [], false), WorkflowController.createTemplate);

/**
 * @swagger
 * /api/v1/workflows/templates/{templateId}/create:
 *   post:
 *     summary: 从模板创建工作流
 *     tags: [Workflows]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: templateId
 *         required: true
 *         schema:
 *           type: string
 *         description: 模板ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *             properties:
 *               name:
 *                 type: string
 *                 description: 工作流名称
 *               customizations:
 *                 type: object
 *                 description: 自定义配置
 *     responses:
 *       201:
 *         description: 创建成功
 *       400:
 *         description: 创建失败
 *       404:
 *         description: 模板不存在
 */
router.post('/templates/:templateId/create', checkPermissions(['workflow:create', 'admin:all'], [], false), WorkflowController.createFromTemplate);

// ========== 工具和验证 ==========

/**
 * @swagger
 * /api/v1/workflows/validate-config:
 *   post:
 *     summary: 验证工作流配置
 *     tags: [Workflows]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - config
 *             properties:
 *               config:
 *                 type: object
 *                 description: 工作流配置
 *     responses:
 *       200:
 *         description: 验证通过
 *       400:
 *         description: 验证失败
 */
router.post('/validate-config', checkPermissions(['workflow:read', 'admin:all'], [], false), WorkflowController.validateConfig);

// ========== 工作流定义管理 ==========

/**
 * @swagger
 * /api/v1/workflows:
 *   post:
 *     summary: 创建工作流
 *     tags: [Workflows]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *               - category
 *               - config
 *             properties:
 *               name:
 *                 type: string
 *                 description: 工作流名称
 *               description:
 *                 type: string
 *                 description: 工作流描述
 *               category:
 *                 type: string
 *                 enum: [SERVICE_AUTOMATION, SLA_MONITORING, ALERT_PROCESSING, BACKUP_AUTOMATION, MAINTENANCE, APPROVAL_PROCESS, NOTIFICATION, DATA_PROCESSING, INTEGRATION, CUSTOM]
 *                 description: 工作流类别
 *               priority:
 *                 type: string
 *                 enum: [LOW, MEDIUM, HIGH, URGENT, CRITICAL]
 *                 description: 优先级
 *               config:
 *                 type: object
 *                 description: 工作流配置
 *               tags:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: 标签
 *               isTemplate:
 *                 type: boolean
 *                 description: 是否为模板
 *     responses:
 *       201:
 *         description: 工作流创建成功
 *       400:
 *         description: 请求参数无效
 *       401:
 *         description: 未授权
 */
router.post('/', checkPermissions(['workflow:create', 'admin:all'], [], false), WorkflowController.createWorkflow);

/**
 * @swagger
 * /api/v1/workflows:
 *   get:
 *     summary: 获取工作流列表
 *     tags: [Workflows]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: 页码
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 20
 *         description: 每页数量
 *       - in: query
 *         name: category
 *         schema:
 *           type: string
 *         description: 工作流类别
 *       - in: query
 *         name: isActive
 *         schema:
 *           type: boolean
 *         description: 是否激活
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: 搜索关键词
 *       - in: query
 *         name: tags
 *         schema:
 *           type: string
 *         description: 标签（逗号分隔）
 *     responses:
 *       200:
 *         description: 获取成功
 */
router.get('/', checkPermissions(['workflow:read', 'admin:all'], [], false), WorkflowController.getWorkflows);

/**
 * @swagger
 * /api/v1/workflows/{id}:
 *   get:
 *     summary: 获取工作流详情
 *     tags: [Workflows]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 工作流ID
 *     responses:
 *       200:
 *         description: 获取成功
 *       404:
 *         description: 工作流不存在
 */
router.get('/:id', checkPermissions(['workflow:read', 'admin:all'], [], false), WorkflowController.getWorkflowById);

/**
 * @swagger
 * /api/v1/workflows/{id}:
 *   put:
 *     summary: 更新工作流
 *     tags: [Workflows]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 工作流ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *               description:
 *                 type: string
 *               category:
 *                 type: string
 *               priority:
 *                 type: string
 *               config:
 *                 type: object
 *               tags:
 *                 type: array
 *                 items:
 *                   type: string
 *               isActive:
 *                 type: boolean
 *     responses:
 *       200:
 *         description: 更新成功
 *       400:
 *         description: 请求参数无效
 *       404:
 *         description: 工作流不存在
 */
router.put('/:id', checkPermissions(['workflow:update', 'admin:all'], [], false), WorkflowController.updateWorkflow);

/**
 * @swagger
 * /api/v1/workflows/{id}:
 *   delete:
 *     summary: 删除工作流
 *     tags: [Workflows]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 工作流ID
 *     responses:
 *       200:
 *         description: 删除成功
 *       400:
 *         description: 删除失败
 *       404:
 *         description: 工作流不存在
 */
router.delete('/:id', checkPermissions(['workflow:delete', 'admin:all'], [], false), WorkflowController.deleteWorkflow);

/**
 * @swagger
 * /api/v1/workflows/{id}/clone:
 *   post:
 *     summary: 复制工作流
 *     tags: [Workflows]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 工作流ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *             properties:
 *               name:
 *                 type: string
 *                 description: 新工作流名称
 *     responses:
 *       201:
 *         description: 复制成功
 *       400:
 *         description: 复制失败
 *       404:
 *         description: 原工作流不存在
 */
router.post('/:id/clone', checkPermissions(['workflow:create', 'admin:all'], [], false), WorkflowController.cloneWorkflow);

/**
 * @swagger
 * /api/v1/workflows/{id}/trigger:
 *   post:
 *     summary: 触发工作流执行
 *     tags: [Workflows]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 工作流ID
 *     requestBody:
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               triggerData:
 *                 type: object
 *                 description: 触发数据
 *     responses:
 *       200:
 *         description: 触发成功
 *       400:
 *         description: 触发失败
 *       404:
 *         description: 工作流不存在
 */
router.post('/:id/trigger', checkPermissions(['workflow:execute', 'admin:all'], [], false), WorkflowController.triggerWorkflow);

export default router;