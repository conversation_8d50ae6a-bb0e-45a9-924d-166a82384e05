import { Router } from 'express'
import { authMiddleware, requirePermissions } from '@/middleware/auth.middleware'
import {
  getUsers,
  getUser,
  createUser,
  updateUser,
  deleteUser,
  changePassword,
  resetPassword,
  toggleUserStatus,
  getUserStats,
  getDepartments,
  importUsers,
  getCurrentUser,
  updateCurrentUser,
  batchOperateUsers,
  exportUsers,
  getActiveUserStats
} from '@/controllers/user.controller'

const router = Router()

router.get('/', authMiddleware, requirePermissions('user:read'), getUsers)

router.get('/me', authMiddleware, getCurrentUser)

router.put('/me', authMiddleware, updateCurrentUser)

router.get('/stats', authMiddleware, requirePermissions('user:read'), getUserStats)

router.get('/active-stats', authMiddleware, requirePermissions('user:read'), getActiveUserStats)

router.get('/export', authMiddleware, requirePermissions('user:export'), exportUsers)

router.post('/batch', authMiddleware, requirePermissions('user:manage'), batchOperateUsers)

router.get('/departments', authMiddleware, getDepartments)

router.get('/:id', authMiddleware, requirePermissions('user:read'), getUser)

router.post('/', authMiddleware, requirePermissions('user:write'), createUser)

router.post('/import', authMiddleware, requirePermissions('user:write'), importUsers)

router.put('/:id', authMiddleware, requirePermissions('user:write'), updateUser)

router.delete('/:id', authMiddleware, requirePermissions('user:delete'), deleteUser)

router.post('/:id/change-password', authMiddleware, changePassword)

router.post('/:id/reset-password', authMiddleware, requirePermissions('user:write'), resetPassword)

router.patch('/:id/toggle-status', authMiddleware, requirePermissions('user:write'), toggleUserStatus)

export default router