/**
 * 工作流执行器API路由
 * 提供工作流执行、监控和管理的REST API端点
 */

import { Router } from 'express';
import { WorkflowExecutorController } from '@/controllers/workflow-executor.controller';
import { authMiddleware } from '@/middleware/auth.middleware';
import { validateRequest } from '@/middleware/validation.middleware';

const router = Router();

// ========== 认证中间件 ==========
// 所有工作流执行器端点都需要认证
router.use(authMiddleware);

// ========== 工作流执行管理 ==========

/**
 * @swagger
 * /api/v1/workflow/execute:
 *   post:
 *     tags: [Workflow Executor]
 *     summary: 执行工作流
 *     description: 启动工作流执行，返回执行会话ID
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - workflowId
 *             properties:
 *               workflowId:
 *                 type: string
 *                 format: uuid
 *                 description: 工作流ID
 *                 example: "123e4567-e89b-12d3-a456-************"
 *               context:
 *                 type: object
 *                 description: 执行上下文变量
 *                 example: 
 *                   customerId: "customer_001"
 *                   requestType: "urgent"
 *               priority:
 *                 type: string
 *                 enum: [LOW, MEDIUM, HIGH, URGENT]
 *                 default: MEDIUM
 *                 description: 执行优先级
 *               tags:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: 执行标签
 *                 example: ["urgent", "customer-service"]
 *               metadata:
 *                 type: object
 *                 description: 执行元数据
 *     responses:
 *       200:
 *         description: 工作流执行启动成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   properties:
 *                     sessionId:
 *                       type: string
 *                       description: 执行会话ID
 *                     workflowId:
 *                       type: string
 *                       description: 工作流ID
 *                     status:
 *                       type: string
 *                       description: 执行状态
 *                     startTime:
 *                       type: string
 *                       format: date-time
 *                       description: 开始时间
 *                     totalSteps:
 *                       type: integer
 *                       description: 总步骤数
 *                     currentStep:
 *                       type: integer
 *                       description: 当前步骤
 *                 message:
 *                   type: string
 *                   example: "工作流执行启动成功"
 *       400:
 *         description: 请求参数验证失败
 *       401:
 *         description: 未认证
 *       500:
 *         description: 内部服务器错误
 */
router.post('/execute', WorkflowExecutorController.executeWorkflow);

/**
 * @swagger
 * /api/v1/workflow/execution/{sessionId}:
 *   get:
 *     tags: [Workflow Executor]
 *     summary: 获取工作流执行状态
 *     description: 根据会话ID获取工作流执行的详细状态信息
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: sessionId
 *         required: true
 *         schema:
 *           type: string
 *         description: 执行会话ID
 *         example: "wf_session_1692012345_abc123"
 *     responses:
 *       200:
 *         description: 获取执行状态成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   properties:
 *                     sessionId:
 *                       type: string
 *                     executionId:
 *                       type: string
 *                     workflowId:
 *                       type: string
 *                     status:
 *                       type: string
 *                       enum: [STARTING, RUNNING, PAUSED, STOPPING, STOPPED]
 *                     startTime:
 *                       type: string
 *                       format: date-time
 *                     endTime:
 *                       type: string
 *                       format: date-time
 *                       nullable: true
 *                     currentStep:
 *                       type: integer
 *                     totalSteps:
 *                       type: integer
 *                     errors:
 *                       type: array
 *                       items:
 *                         type: object
 *                     progress:
 *                       type: integer
 *                       description: 执行进度百分比
 *                     duration:
 *                       type: integer
 *                       description: 执行时长(毫秒)
 *       404:
 *         description: 执行会话不存在
 *       500:
 *         description: 内部服务器错误
 */
router.get('/execution/:sessionId', WorkflowExecutorController.getExecutionStatus);

/**
 * @swagger
 * /api/v1/workflow/executions:
 *   get:
 *     tags: [Workflow Executor]
 *     summary: 列出工作流执行记录
 *     description: 获取工作流执行记录列表，支持过滤和分页
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [STARTING, RUNNING, PAUSED, STOPPING, STOPPED]
 *         description: 执行状态过滤
 *       - in: query
 *         name: workflowId
 *         schema:
 *           type: string
 *           format: uuid
 *         description: 工作流ID过滤
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: 页码
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 20
 *         description: 每页条数
 *       - in: query
 *         name: sortBy
 *         schema:
 *           type: string
 *           enum: [startTime, endTime, status, workflowId]
 *           default: startTime
 *         description: 排序字段
 *       - in: query
 *         name: sortOrder
 *         schema:
 *           type: string
 *           enum: [asc, desc]
 *           default: desc
 *         description: 排序顺序
 *     responses:
 *       200:
 *         description: 获取执行列表成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   properties:
 *                     executions:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           sessionId:
 *                             type: string
 *                           executionId:
 *                             type: string
 *                           workflowId:
 *                             type: string
 *                           status:
 *                             type: string
 *                           startTime:
 *                             type: string
 *                             format: date-time
 *                           endTime:
 *                             type: string
 *                             format: date-time
 *                             nullable: true
 *                           currentStep:
 *                             type: integer
 *                           totalSteps:
 *                             type: integer
 *                           errorCount:
 *                             type: integer
 *                           progress:
 *                             type: integer
 *                           duration:
 *                             type: integer
 *                     pagination:
 *                       type: object
 *                       properties:
 *                         total:
 *                           type: integer
 *                         page:
 *                           type: integer
 *                         limit:
 *                           type: integer
 *                         totalPages:
 *                           type: integer
 *       400:
 *         description: 查询参数验证失败
 *       500:
 *         description: 内部服务器错误
 */
router.get('/executions', WorkflowExecutorController.listExecutions);

// ========== 工作流执行控制 ==========

/**
 * @swagger
 * /api/v1/workflow/execution/pause:
 *   post:
 *     tags: [Workflow Executor]
 *     summary: 暂停工作流执行
 *     description: 暂停指定会话的工作流执行
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - sessionId
 *             properties:
 *               sessionId:
 *                 type: string
 *                 description: 执行会话ID
 *               reason:
 *                 type: string
 *                 description: 暂停原因
 *                 default: "用户手动暂停"
 *     responses:
 *       200:
 *         description: 工作流执行已暂停
 *       400:
 *         description: 请求参数验证失败
 *       500:
 *         description: 内部服务器错误
 */
router.post('/execution/pause', WorkflowExecutorController.pauseExecution);

/**
 * @swagger
 * /api/v1/workflow/execution/resume:
 *   post:
 *     tags: [Workflow Executor]
 *     summary: 恢复工作流执行
 *     description: 恢复暂停的工作流执行
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - sessionId
 *             properties:
 *               sessionId:
 *                 type: string
 *                 description: 执行会话ID
 *               reason:
 *                 type: string
 *                 description: 恢复原因
 *                 default: "用户手动恢复"
 *     responses:
 *       200:
 *         description: 工作流执行已恢复
 *       400:
 *         description: 请求参数验证失败
 *       500:
 *         description: 内部服务器错误
 */
router.post('/execution/resume', WorkflowExecutorController.resumeExecution);

/**
 * @swagger
 * /api/v1/workflow/execution/stop:
 *   post:
 *     tags: [Workflow Executor]
 *     summary: 停止工作流执行
 *     description: 停止指定会话的工作流执行
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - sessionId
 *             properties:
 *               sessionId:
 *                 type: string
 *                 description: 执行会话ID
 *               reason:
 *                 type: string
 *                 description: 停止原因
 *                 default: "用户手动停止"
 *     responses:
 *       200:
 *         description: 工作流执行已停止
 *       400:
 *         description: 请求参数验证失败
 *       500:
 *         description: 内部服务器错误
 */
router.post('/execution/stop', WorkflowExecutorController.stopExecution);

// ========== 监控和统计 ==========

/**
 * @swagger
 * /api/v1/workflow/coordinator/status:
 *   get:
 *     tags: [Workflow Executor]
 *     summary: 获取协调器状态
 *     description: 获取工作流协调器的运行状态和统计信息
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 获取协调器状态成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     coordinator:
 *                       type: object
 *                       properties:
 *                         status:
 *                           type: string
 *                         activeSessions:
 *                           type: integer
 *                         totalSessions:
 *                           type: integer
 *                         config:
 *                           type: object
 *                     statistics:
 *                       type: object
 *                       properties:
 *                         completedExecutions:
 *                           type: integer
 *                         failedExecutions:
 *                           type: integer
 *                         runningExecutions:
 *                           type: integer
 *                         averageExecutionTime:
 *                           type: number
 *                         successRate:
 *                           type: number
 *                     timestamp:
 *                       type: string
 *                       format: date-time
 *       500:
 *         description: 内部服务器错误
 */
router.get('/coordinator/status', WorkflowExecutorController.getCoordinatorStatus);

/**
 * @swagger
 * /api/v1/workflow/metrics:
 *   get:
 *     tags: [Workflow Executor]
 *     summary: 获取执行指标
 *     description: 获取工作流执行的性能指标和统计数据
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: executorType
 *         schema:
 *           type: string
 *         description: 执行器类型过滤
 *       - in: query
 *         name: timeWindow
 *         schema:
 *           type: string
 *           enum: [1h, 6h, 24h, 7d]
 *           default: 24h
 *         description: 时间窗口
 *       - in: query
 *         name: includeDetails
 *         schema:
 *           type: boolean
 *           default: false
 *         description: 是否包含详细信息
 *     responses:
 *       200:
 *         description: 获取执行指标成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     metrics:
 *                       type: object
 *                     healthScore:
 *                       type: number
 *                     realtime:
 *                       type: object
 *                     performanceTrend:
 *                       type: object
 *                     executorComparison:
 *                       type: object
 *                     timestamp:
 *                       type: string
 *                       format: date-time
 *       400:
 *         description: 查询参数验证失败
 *       500:
 *         description: 内部服务器错误
 */
router.get('/metrics', WorkflowExecutorController.getExecutionMetrics);

// ========== 日志管理 ==========

/**
 * @swagger
 * /api/v1/workflow/logs:
 *   get:
 *     tags: [Workflow Executor]
 *     summary: 获取执行日志
 *     description: 获取工作流执行日志，支持过滤和分页
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: sessionId
 *         schema:
 *           type: string
 *         description: 执行会话ID过滤
 *       - in: query
 *         name: workflowId
 *         schema:
 *           type: string
 *           format: uuid
 *         description: 工作流ID过滤
 *       - in: query
 *         name: level
 *         schema:
 *           type: string
 *           enum: [INFO, WARN, ERROR, DEBUG]
 *         description: 日志级别过滤
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: 页码
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 500
 *           default: 100
 *         description: 每页条数
 *       - in: query
 *         name: startTime
 *         schema:
 *           type: string
 *           format: date-time
 *         description: 开始时间过滤
 *       - in: query
 *         name: endTime
 *         schema:
 *           type: string
 *           format: date-time
 *         description: 结束时间过滤
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: 日志内容搜索关键词
 *     responses:
 *       200:
 *         description: 获取执行日志成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     logs:
 *                       type: array
 *                       items:
 *                         type: object
 *                     pagination:
 *                       type: object
 *                     statistics:
 *                       type: object
 *                     aggregations:
 *                       type: object
 *       400:
 *         description: 查询参数验证失败
 *       500:
 *         description: 内部服务器错误
 */
router.get('/logs', WorkflowExecutorController.getExecutionLogs);

/**
 * @swagger
 * /api/v1/workflow/logs/export:
 *   get:
 *     tags: [Workflow Executor]
 *     summary: 导出执行日志
 *     description: 导出工作流执行日志到指定格式的文件
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: sessionId
 *         schema:
 *           type: string
 *         description: 执行会话ID过滤
 *       - in: query
 *         name: workflowId
 *         schema:
 *           type: string
 *           format: uuid
 *         description: 工作流ID过滤
 *       - in: query
 *         name: level
 *         schema:
 *           type: string
 *           enum: [INFO, WARN, ERROR, DEBUG]
 *         description: 日志级别过滤
 *       - in: query
 *         name: format
 *         schema:
 *           type: string
 *           enum: [json, csv, txt]
 *           default: json
 *         description: 导出格式
 *       - in: query
 *         name: includeMetadata
 *         schema:
 *           type: boolean
 *           default: true
 *         description: 是否包含元数据
 *       - in: query
 *         name: startTime
 *         schema:
 *           type: string
 *           format: date-time
 *         description: 开始时间过滤
 *       - in: query
 *         name: endTime
 *         schema:
 *           type: string
 *           format: date-time
 *         description: 结束时间过滤
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: 日志内容搜索关键词
 *     responses:
 *       200:
 *         description: 导出执行日志成功
 *         content:
 *           application/octet-stream:
 *             schema:
 *               type: string
 *               format: binary
 *           application/json:
 *             schema:
 *               type: object
 *           text/csv:
 *             schema:
 *               type: string
 *           text/plain:
 *             schema:
 *               type: string
 *       400:
 *         description: 查询参数验证失败
 *       500:
 *         description: 内部服务器错误
 */
router.get('/logs/export', WorkflowExecutorController.exportExecutionLogs);

// ========== 执行状态跟踪 ==========

/**
 * @swagger
 * /api/v1/workflow/execution/{sessionId}/tracking:
 *   get:
 *     tags: [Workflow Executor]
 *     summary: 获取执行状态跟踪信息
 *     description: 获取工作流执行的详细步骤跟踪信息
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: sessionId
 *         required: true
 *         schema:
 *           type: string
 *         description: 执行会话ID
 *     responses:
 *       200:
 *         description: 获取执行跟踪信息成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     sessionId:
 *                       type: string
 *                     executionId:
 *                       type: string
 *                     workflowId:
 *                       type: string
 *                     overallStatus:
 *                       type: string
 *                     steps:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           stepIndex:
 *                             type: integer
 *                           status:
 *                             type: string
 *                           startTime:
 *                             type: string
 *                             format: date-time
 *                           endTime:
 *                             type: string
 *                             format: date-time
 *                           duration:
 *                             type: integer
 *                           retryCount:
 *                             type: integer
 *                           error:
 *                             type: object
 *                           logs:
 *                             type: array
 *                             maxItems: 10
 *                     summary:
 *                       type: object
 *                       properties:
 *                         totalSteps:
 *                           type: integer
 *                         completedSteps:
 *                           type: integer
 *                         errorCount:
 *                           type: integer
 *                         startTime:
 *                           type: string
 *                           format: date-time
 *                         endTime:
 *                           type: string
 *                           format: date-time
 *       404:
 *         description: 执行会话不存在
 *       500:
 *         description: 内部服务器错误
 */
router.get('/execution/:sessionId/tracking', WorkflowExecutorController.getExecutionTracking);

export default router;