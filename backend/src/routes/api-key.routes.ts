import { Router } from 'express'
import { authMiddleware, requirePermissions } from '@/middleware/auth.middleware'
import { getApi<PERSON>eys, getApi<PERSON>ey, createApi<PERSON><PERSON>, update<PERSON>pi<PERSON>ey, deleteApi<PERSON>ey, regenerate<PERSON>pi<PERSON>ey, getApiKeyStats, getMyApiKeys, getMyApiKey, createMyApiKey, updateMyApiKey, deleteMyApiKey, regenerateMyApiKey, getMyApiKeyStats } from '@/controllers/api-key.controller'

const router = Router()

router.use(authMiddleware)

router.get('/', requirePermissions('admin:read'), getApiKeys)

router.get('/:id', requirePermissions('admin:read'), getApiKey)

router.post('/', requirePermissions('admin:write'), createApiKey)

router.put('/:id', requirePermissions('admin:write'), updateApiKey)

router.delete('/:id', requirePermissions('admin:write'), deleteApiKey)

router.post('/:id/regenerate', requirePermissions('admin:write'), regenerateApiKey)

router.get('/:id/stats', requirePermissions('admin:read'), getApiKeyStats)

export default router

// 自服务（当前用户）API Key 路由
const meRouter = Router()

meRouter.use(authMiddleware)

meRouter.get('/', getMyApiKeys)
meRouter.get('/:id', getMyApiKey)
meRouter.post('/', createMyApiKey)
meRouter.put('/:id', updateMyApiKey)
meRouter.delete('/:id', deleteMyApiKey)
meRouter.post('/:id/regenerate', regenerateMyApiKey)
meRouter.get('/:id/stats', getMyApiKeyStats)

export { meRouter as meApiKeyRoutes }