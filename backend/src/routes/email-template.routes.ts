import { Router } from 'express';
import {
  getEmailTemplates,
  getEmailTemplatesByCategory,
  getEmailTemplate,
  createEmailTemplate,
  updateEmailTemplate,
  deleteEmailTemplate,
  copyEmailTemplate,
  previewEmailTemplate,
  toggleEmailTemplate,
} from '../controllers/email-template.controller';
import { authenticateToken } from '../middleware/auth.middleware';
import { checkPermissions } from '../middleware/permission.middleware'

const router = Router();

// 所有路由都需要认证
router.use(authenticateToken)

// 获取邮件模板列表 - 需要系统读取权限
router.get('/', checkPermissions(['system:read']), getEmailTemplates)

// 根据分类获取邮件模板 - 需要系统读取权限
router.get('/categories', checkPermissions(['system:read']), getEmailTemplatesByCategory);

// 获取单个邮件模板 - 需要系统读取权限
router.get('/:id', checkPermissions(['system:read']), getEmailTemplate);

// 预览邮件模板 - 需要系统读取权限
router.post('/:id/preview', checkPermissions(['system:read']), previewEmailTemplate);

// 复制邮件模板 - 需要系统写入权限
router.post('/:id/copy', checkPermissions(['system:write']), copyEmailTemplate);

// 启用/禁用邮件模板 - 需要系统写入权限
router.patch('/:id/toggle', checkPermissions(['system:write']), toggleEmailTemplate);

// 创建邮件模板 - 需要系统写入权限
router.post('/', checkPermissions(['system:write']), createEmailTemplate);

// 更新邮件模板 - 需要系统写入权限
router.put('/:id', checkPermissions(['system:write']), updateEmailTemplate);

// 删除邮件模板 - 需要系统写入权限
router.delete('/:id', checkPermissions(['system:write']), deleteEmailTemplate);

export default router;
