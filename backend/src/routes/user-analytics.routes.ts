import { Router } from 'express'
import { UserAnalyticsController } from '@/controllers/user-analytics.controller'
import { authMiddleware, requirePermissions } from '@/middleware/auth.middleware'

const router = Router()

// 应用认证中间件
router.use(authMiddleware)

router.get('/summary', 
  requirePermissions('system:analytics:view'),
  UserAnalyticsController.getActivitySummary
)

router.get('/online', 
  requirePermissions('system:users:view'),
  UserAnalyticsController.getOnlineUsers
)

router.get('/behavior/:userId', 
  requirePermissions('system:analytics:view'),
  UserAnalyticsController.getUserBehaviorAnalysis
)

router.get('/heatmap', 
  requirePermissions('system:analytics:view'),
  UserAnalyticsController.getActivityHeatmap
)

router.get('/stats', 
  requirePermissions('system:analytics:view'),
  UserAnalyticsController.getUserActivityStats
)

router.get('/patterns/:userId', 
  requirePermissions('system:analytics:view'),
  UserAnalyticsController.getUserBehaviorPatterns
)

router.get('/anomalies', 
  requirePermissions('system:analytics:view'),
  UserAnalyticsController.getUserAnomalies
)

router.put('/anomalies/:anomalyId/resolve', 
  requirePermissions('system:analytics:manage'),
  UserAnalyticsController.resolveAnomaly
)

router.post('/detect-anomalies', 
  requirePermissions('system:analytics:manage'),
  UserAnalyticsController.triggerAnomalyDetection
)

router.post('/update-patterns', 
  requirePermissions('system:analytics:manage'),
  UserAnalyticsController.updateBehaviorPatterns
)

router.get('/export', 
  requirePermissions('system:analytics:export'),
  UserAnalyticsController.exportActivityReport
)

router.get('/rankings', 
  requirePermissions('system:analytics:view'),
  UserAnalyticsController.getUserRankings
)

export default router