import { Router } from 'express'
import { authMiddleware } from '@/middleware/auth.middleware'
import {
  getMyApi<PERSON><PERSON>s,
  getMyApi<PERSON>ey,
  createMyApi<PERSON>ey,
  updateMyApiKey,
  deleteMyApiKey,
  regenerateMyApiKey,
  getMyApiKeyStats,
} from '@/controllers/api-key.controller'

const router = Router()

// 仅需登录，无需 admin 权限
router.use(authMiddleware)

router.get('/', getMyApiKeys)
router.get('/:id', getMyApiKey)
router.post('/', createMyApiKey)
router.put('/:id', updateMyApiKey)
router.delete('/:id', deleteMyApiKey)
router.post('/:id/regenerate', regenerateMyApiKey)
router.get('/:id/stats', getMyApiKeyStats)

export default router


