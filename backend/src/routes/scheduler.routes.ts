import { Router } from 'express'
import { authMiddleware, requirePermissions } from '@/middleware/auth.middleware'
import {
  getTaskStatus,
  executeTask,
  getSystemStats
} from '@/controllers/scheduler.controller'

const router = Router()

router.get('/tasks', authMiddleware, requirePermissions(['admin:all']), getTaskStatus)

router.post('/tasks/:taskName/execute', authMiddleware, requirePermissions(['admin:all']), executeTask)

router.get('/system-stats', authMiddleware, requirePermissions(['admin:all']), getSystemStats)

export default router