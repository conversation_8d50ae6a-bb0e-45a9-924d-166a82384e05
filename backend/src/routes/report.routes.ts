import express from 'express'
import { reportController } from '@/controllers/report.controller'
import { authMiddleware } from '@/middleware/auth.middleware'
import { checkPermissions } from '@/middleware/permission.middleware'

const router = express.Router()

// 应用认证中间件到所有报表路由
router.use(authMiddleware)

// ========== 数据分析路由 ==========

/**
 * 获取运维效率分析数据
 * GET /api/v1/reports/operation-efficiency?start=2024-01-01&end=2024-01-31
 * GET /api/v1/reports/operation-efficiency?days=30
 */
router.get(
  '/operation-efficiency',
  checkPermissions(['report:read', 'admin:all']),
  (req, res) => reportController.getOperationEfficiency(req, res)
)

/**
 * 获取客户满意度分析数据
 * GET /api/v1/reports/customer-satisfaction?start=2024-01-01&end=2024-01-31
 */
router.get(
  '/customer-satisfaction',
  checkPermissions(['report:read', 'admin:all']),
  (req, res) => reportController.getCustomerSatisfaction(req, res)
)

/**
 * 获取SLA合规性报告
 * GET /api/v1/reports/sla-compliance?days=7
 */
router.get(
  '/sla-compliance',
  checkPermissions(['report:read', 'admin:all']),
  (req, res) => reportController.getSLACompliance(req, res)
)

/**
 * 获取服务趋势分析数据
 * GET /api/v1/reports/service-trends?days=30
 */
router.get(
  '/service-trends',
  checkPermissions(['report:read', 'admin:all']),
  (req, res) => reportController.getServiceTrends(req, res)
)

/**
 * 获取综合仪表板数据
 * GET /api/v1/reports/dashboard?days=30
 */
router.get(
  '/dashboard',
  checkPermissions(['report:read', 'admin:all']),
  (req, res) => reportController.getDashboardData(req, res)
)

/**
 * 获取快速统计数据 (用于首页概览)
 * GET /api/v1/reports/quick-stats
 */
router.get(
  '/quick-stats',
  checkPermissions(['report:read', 'admin:all']),
  (req, res) => reportController.getQuickStats(req, res)
)

// ========== 自定义报表路由 ==========

/**
 * 生成自定义报表
 * POST /api/v1/reports/custom
 */
router.post(
  '/custom',
  checkPermissions(['report:write', 'admin:all']),
  (req, res) => reportController.generateCustomReport(req, res)
)

/**
 * 获取报表配置模板
 * GET /api/v1/reports/templates
 */
router.get(
  '/templates',
  checkPermissions(['report:read', 'admin:all']),
  (req, res) => reportController.getReportTemplates(req, res)
)

// ========== 导出功能路由 ==========

/**
 * 导出Excel报表
 * POST /api/v1/reports/export/excel
 * Body: { data: any, fileName: string }
 */
router.post(
  '/export/excel',
  checkPermissions(['report:export', 'admin:all']),
  (req, res) => reportController.exportExcel(req, res)
)

/**
 * 导出CSV数据
 * POST /api/v1/reports/export/csv
 * Body: { data: any[], fileName: string }
 */
router.post(
  '/export/csv',
  checkPermissions(['report:export', 'admin:all']),
  (req, res) => reportController.exportCSV(req, res)
)

/**
 * 导出PDF报表
 * POST /api/v1/reports/export/pdf
 * Body: { serviceId?: string, type: 'service' }
 */
router.post(
  '/export/pdf',
  checkPermissions(['report:export', 'admin:all']),
  (req, res) => reportController.exportPDF(req, res)
)

export default router