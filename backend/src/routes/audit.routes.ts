import { Router } from 'express'
import { authMiddleware, requirePermissions } from '@/middleware/auth.middleware'
import {
  getAuditLogs,
  getAuditStats,
  getMyAuditLogs,
  getAuditLogDetail,
  getTimeSeriesStats,
  getRiskAnalysis,
  getSystemImpactAnalysis,
  cleanupExpiredLogs,
  exportAuditLogs
} from '@/controllers/audit.controller'

const router = Router()

router.get('/logs', authMiddleware, requirePermissions(['admin:all']), getAuditLogs)

router.get('/stats', authMiddleware, requirePermissions(['admin:all']), getAuditStats)

router.get('/my-logs', authMiddleware, getMyAuditLogs)

router.get('/logs/:id', authMiddleware, requirePermissions(['admin:all']), getAuditLogDetail)

router.get('/time-series', authMiddleware, requirePermissions(['admin:all']), getTimeSeriesStats)

router.get('/risk-analysis', authMiddleware, requirePermissions(['admin:all']), getRiskAnalysis)

router.get('/system-impact', authMiddleware, requirePermissions(['admin:all']), getSystemImpactAnalysis)

router.delete('/cleanup', authMiddleware, requirePermissions(['admin:all']), cleanupExpiredLogs)

router.get('/export', authMiddleware, requirePermissions(['admin:all']), exportAuditLogs)

export default router