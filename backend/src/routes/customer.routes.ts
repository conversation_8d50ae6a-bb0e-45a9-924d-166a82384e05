import { Router } from 'express'
import {
  getCustomers,
  getCustomer,
  createCustomer,
  updateCustomer,
  deleteCustomer,
  getCustomerContacts,
  createCustomerContact,
  updateCustomerContact,
  deleteCustomerContact,
  getCustomerStats,
  getCustomerDetailStats
} from '@/controllers/customer.controller'
import { authenticateToken, requirePermissions } from '@/middleware/auth.middleware'

const router = Router()

router.get('/', authenticateToken, requirePermissions('customer:read'), getCustomers)

router.get('/stats', authenticateToken, requirePermissions('customer:read'), getCustomerStats)

router.get('/:id', authenticateToken, requirePermissions('customer:read'), getCustomer)

router.get('/:id/stats', authenticateToken, requirePermissions('customer:read'), getCustomerDetailStats)

router.post('/', authenticateToken, requirePermissions('customer:write'), createCustomer)

router.put('/:id', authenticateToken, requirePermissions('customer:write'), updateCustomer)

router.delete('/:id', authenticateToken, requirePermissions('customer:write'), deleteCustomer)

router.get('/:customerId/contacts', authenticateToken, requirePermissions('customer:read'), getCustomerContacts)

router.post('/:customerId/contacts', authenticateToken, requirePermissions('customer:write'), createCustomerContact)

router.put('/:customerId/contacts/:contactId', authenticateToken, requirePermissions('customer:write'), updateCustomerContact)

router.delete('/:customerId/contacts/:contactId', authenticateToken, requirePermissions('customer:write'), deleteCustomerContact)

export default router