import { Router } from 'express'
import { authMiddleware } from '@/middleware/auth.middleware'
import { createExternalService, getExternalServices, getExternalService, addExternalServiceComment } from '@/controllers/external-service.controller'

const router = Router()

router.use(authMiddleware)

router.post('/services', createExternalService)

router.get('/services', getExternalServices)

router.get('/services/:id', getExternalService)

router.post('/services/:id/comments', addExternalServiceComment)

export default router