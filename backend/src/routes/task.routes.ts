import { Router } from 'express';
import {
  getTaskExecutions,
  getTaskExecutionDetails,
  retryTaskExecution,
} from '@/controllers/task.controller';
import { authenticateToken, requirePermissions } from '@/middleware/auth.middleware';

const router = Router();

// All routes in this file require admin-level permissions
router.use(authenticateToken, requirePermissions('admin:all'));

// Get list of task executions
router.get('/', getTaskExecutions);

// Get details of a single task execution
router.get('/:id', getTaskExecutionDetails);

// Retry a failed task
router.post('/:id/retry', retryTaskExecution);

export default router;
