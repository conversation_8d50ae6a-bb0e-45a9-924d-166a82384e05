import { Router } from 'express'
import { authenticateToken, requirePermissions } from '@/middleware/auth.middleware'
import { getAllPermissionsList, getPermissionGroupsList, getRolePermissionMapping, getPermissionStats, validatePermissionFormat, checkUserPermissions, getDefaultRolePermissions, updateRolePermissions, resetRolePermissions, batchUpdateRolePermissions } from '@/controllers/permission.controller'

const router = Router()

router.get('/', authenticateToken, requirePermissions('role:read'), getAllPermissionsList)

router.get('/groups', authenticateToken, requirePermissions('role:read'), getPermissionGroupsList)

router.get('/role-mapping', authenticateToken, requirePermissions('role:read'), getRolePermissionMapping)

router.get('/stats', authenticateToken, requirePermissions('role:read'), getPermissionStats)

// ==================== 权限验证接口 ====================

router.post('/validate', authenticateToken, requirePermissions('role:permission_manage'), validatePermissionFormat)

router.post('/check', authenticateToken, requirePermissions('user:view_sensitive'), checkUserPermissions)

// ==================== 角色权限管理 ====================

router.get('/default-roles', authenticateToken, requirePermissions('role:read'), getDefaultRolePermissions)

router.put('/roles/:roleId', authenticateToken, requirePermissions('role:permission_manage'), updateRolePermissions)

router.post('/roles/:roleId/reset', authenticateToken, requirePermissions('role:permission_manage'), resetRolePermissions)

router.post('/roles/batch-update', authenticateToken, requirePermissions('role:permission_manage'), batchUpdateRolePermissions)

export default router
