import { Router } from 'express'
import { authMiddleware } from '@/middleware/auth.middleware'
import { requirePermissions } from '@/middleware/auth.middleware'
import { getPermissionTemplates, getPermissionTemplateById, getPermissionTemplateStats, createPermissionTemplate, updatePermissionTemplate, deletePermissionTemplate, applyTemplateToRoles, batchApplyTemplates, comparePermissionTemplates, exportPermissionTemplates, importPermissionTemplates, createDefaultTemplates, copyPermissionTemplate } from '@/controllers/permission-template.controller'

const router = Router()
router.use(authMiddleware)
router.get('/', requirePermissions(`ROLE_PERMISSIONS.READ`), getPermissionTemplates)

router.get('/:id', 
  requirePermissions(`ROLE_PERMISSIONS.READ`), 
  getPermissionTemplateById
)

router.get('/stats', 
  requirePermissions(`ROLE_PERMISSIONS.READ`), 
  getPermissionTemplateStats
)

// ==================== 权限模板管理路由 ====================

router.post('/', 
  requirePermissions(`ROLE_PERMISSIONS.WRITE, ROLE_PERMISSIONS.PERMISSION_MANAGE`), 
  createPermissionTemplate
)

router.put('/:id', 
  requirePermissions(`ROLE_PERMISSIONS.WRITE, ROLE_PERMISSIONS.PERMISSION_MANAGE`), 
  updatePermissionTemplate
)

router.delete('/:id', 
  requirePermissions(`ROLE_PERMISSIONS.DELETE`), 
  deletePermissionTemplate
)

// ==================== 权限模板应用路由 ====================

router.post('/:id/apply', 
  requirePermissions(`ROLE_PERMISSIONS.PERMISSION_MANAGE`), 
  applyTemplateToRoles
)

router.post('/batch-apply', 
  requirePermissions(`ROLE_PERMISSIONS.PERMISSION_MANAGE`), 
  batchApplyTemplates
)

// ==================== 权限模板比较和分析路由 ====================

router.post('/compare', 
  requirePermissions(`ROLE_PERMISSIONS.READ`), 
  comparePermissionTemplates
)

// ==================== 权限模板导入导出路由 ====================

router.post('/export', 
  requirePermissions(`ROLE_PERMISSIONS.READ`), 
  exportPermissionTemplates
)

router.post('/import', 
  requirePermissions(`ROLE_PERMISSIONS.WRITE, ROLE_PERMISSIONS.PERMISSION_MANAGE`), 
  importPermissionTemplates
)

// ==================== 默认模板管理路由 ====================

router.post('/create-defaults', 
  requirePermissions(`ADMIN_ALL`), 
  createDefaultTemplates
)

router.post('/:id/copy', 
  requirePermissions(`ROLE_PERMISSIONS.WRITE, ADMIN_ALL`), 
  copyPermissionTemplate
)

export default router