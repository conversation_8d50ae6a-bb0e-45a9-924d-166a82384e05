import { Router } from 'express'
import { authMiddleware } from '@/middleware/auth.middleware'
import * as MonitorController from '@/controllers/monitor.controller'

const router = Router()

// 所有监控路由都需要认证
router.use(authMiddleware)

// 监控仪表板
router.get('/dashboard', MonitorController.getDashboard)

// 系统指标
router.get('/metrics/system', MonitorController.getSystemMetrics)
router.get('/metrics/historical', MonitorController.getHistoricalMetrics)

// 服务健康检查
router.get('/health/database', MonitorController.getDatabaseHealth)
router.get('/health/redis', MonitorController.getRedisHealth)
router.get('/health/application', MonitorController.getApplicationHealth)

// 智能分析
router.get('/analysis/intelligent', MonitorController.getIntelligentAnalysis)
router.get('/analysis/benchmarks', MonitorController.getPerformanceBenchmarks)
router.get('/analysis/alerts', MonitorController.getResourceAlerts)

// 统计和报告
router.get('/stats/availability', MonitorController.getAvailabilityStats)
router.get('/events', MonitorController.getSystemEvents)
router.get('/report', MonitorController.generateReport)

// 系统诊断
router.post('/diagnostics', MonitorController.runSystemDiagnostics)

// 配置管理
router.get('/config', MonitorController.getMonitoringConfig)
router.put('/config', MonitorController.updateMonitoringConfig)
router.put('/thresholds', MonitorController.updateThreshold)

export default router
