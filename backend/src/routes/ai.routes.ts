import { Router } from 'express'
import { authMiddleware, requirePermissions } from '@/middleware/auth.middleware'
import * as AIController from '@/controllers/ai.controller'

const router = Router()

// 所有AI路由都需要认证
router.use(authMiddleware)

// AI配置管理 - 仅管理员可操作
router.get('/configuration/current', AIController.getCurrentAIConfiguration)
router.get('/configurations', requirePermissions('admin:all'), AIController.getAIConfigurations)
router.get('/configurations/:id', requirePermissions('admin:all'), AIController.getAIConfiguration)
router.post('/configurations', requirePermissions('admin:all'), AIController.createAIConfiguration)
router.put('/configurations/:id', requirePermissions('admin:all'), AIController.updateAIConfiguration)
router.delete('/configurations/:id', requirePermissions('admin:all'), AIController.deleteAIConfiguration)
router.get('/config', requirePermissions('admin:all'), AIController.getAIConfig)
router.put('/config', requirePermissions('admin:all'), AIController.updateAIConfig)
router.put('/config/:userId', requirePermissions('admin:all'), AIController.updateAIConfig)

// AI工单分析 - 所有用户可使用
router.post('/analyze-content', AIController.analyzeTicketContent)

// AI反馈 - 所有用户可提交
router.post('/feedback', AIController.submitFeedback)

// AI分析历史 - 仅管理员可查看
router.get('/analysis-requests', requirePermissions('admin:all'), AIController.getAnalysisHistory)
router.get('/analysis-requests/:id', requirePermissions('admin:all'), AIController.getAnalysisRequest)

// AI统计 - 仅管理员可查看
router.get('/statistics', requirePermissions('admin:all'), AIController.getAIStatistics)

// AI提示词模板管理 - 仅管理员可操作
router.get('/prompt-templates', requirePermissions('admin:all'), AIController.getPromptTemplates)
router.post('/prompt-templates', requirePermissions('admin:all'), AIController.createPromptTemplate)
router.put('/prompt-templates/:id', requirePermissions('admin:all'), AIController.updatePromptTemplate)
router.delete('/prompt-templates/:id', requirePermissions('admin:all'), AIController.deletePromptTemplate)

// AI服务测试 - 仅管理员可测试
router.post('/test-connection', requirePermissions('admin:all'), AIController.testAIConnection)

export default router
