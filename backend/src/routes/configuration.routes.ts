import { Router } from 'express'
import {
  getConfigurations,
  getConfiguration,
  createConfiguration,
  updateConfiguration,
  deleteConfiguration,
  getArchiveConfigurations,
  getConfigurationStats,
  toggleConfigurationStatus
} from '@/controllers/configuration.controller'
import { authenticateToken, requirePermissions } from '@/middleware/auth.middleware'

const router = Router()

router.get('/', authenticateToken, requirePermissions('config:read'), getConfigurations)

router.get('/stats', authenticateToken, requirePermissions('config:read'), getConfigurationStats)

router.get('/archive/:archiveId', authenticateToken, requirePermissions('config:read'), getArchiveConfigurations)

router.get('/:id', authenticateToken, requirePermissions('config:read'), getConfiguration)

router.post('/', authenticateToken, requirePermissions('config:write'), createConfiguration)

router.put('/:id', authenticateToken, requirePermissions('config:write'), updateConfiguration)

router.delete('/:id', authenticateToken, requirePermissions('config:write'), deleteConfiguration)

router.patch('/:id/toggle-status', authenticateToken, requirePermissions('config:write'), toggleConfigurationStatus)

export default router