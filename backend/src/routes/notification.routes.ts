import { Router } from 'express'
import {
  sendEmail,
  sendTemplateEmail,
  sendSms,
  sendTemplateSms,
  sendBulkSms,
  testEmailConnection,
  getNotificationStatus
} from '@/controllers/notification.controller'
import { authenticateToken, requirePermissions } from '@/middleware/auth.middleware'

const router = Router()

router.post('/email', authenticateToken, requirePermissions('notification:send'), sendEmail)

router.post('/email/template', authenticateToken, requirePermissions('notification:send'), sendTemplateEmail)

router.post('/sms', authenticateToken, requirePermissions('notification:send'), sendSms)

router.post('/sms/template', authenticateToken, requirePermissions('notification:send'), sendTemplateSms)

router.post('/sms/bulk', authenticateToken, requirePermissions('notification:send'), sendBulkSms)

router.post('/email/test', authenticateToken, requirePermissions('notification:manage'), testEmailConnection)

router.get('/status', authenticateToken, requirePermissions('notification:read'), getNotificationStatus)

export default router
