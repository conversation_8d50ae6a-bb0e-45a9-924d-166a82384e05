import { Router } from 'express'
import { authMiddleware, requirePermissions } from '@/middleware/auth.middleware'
import { UploadService } from '@/services/upload.service'
import {
  uploadServiceAttachments,
  getServiceAttachments,
  downloadAttachment,
  previewAttachment,
  deleteAttachment,
  getStorageStats,
  cleanupOldFiles
} from '@/controllers/upload.controller'

const router = Router()

// 配置不同类型的上传中间件
const serviceAttachmentUpload = UploadService.createUploadMiddleware({
  destination: 'uploads/service-attachments/',
  maxFileSize: 10 * 1024 * 1024, // 10MB
  maxFiles: 5
})

// 原始路径：/service/:serviceId/attachments（路径参数）
router.post(
  '/service/:serviceId/attachments',
  authMiddleware,
  requirePermissions('service:write'),
  serviceAttachmentUpload,
  uploadServiceAttachments
)

// 临时测试路由 - 直接使用原生 multer
const multer = require('multer');
const simpleUpload = multer().any();

router.post(
  '/test-simple-multer',
  simpleUpload,
  async (req: any, res: any) => {
    console.log('=== 简单 multer 测试 ===');
    console.log('req.body:', req.body);
    console.log('req.files:', req.files);
    
    return res.json({
      success: true,
      message: '简单multer测试',
      body: req.body,
      files: req.files
    });
  }
);

// 临时测试路由 - 测试FormData解析
router.post(
  '/test-formdata',
  serviceAttachmentUpload,
  async (req: any, res: any) => {
    console.log('=== FormData 测试 ===');
    console.log('req.body:', req.body);
    console.log('req.files:', req.files);
    console.log('req.file:', req.file);
    
    return res.json({
      success: true,
      body: req.body,
      files: req.files,
      file: req.file
    });
  }
);

// 快速修复：直接使用简单的 multer
const quickFixUpload = multer({
  dest: 'uploads/service-attachments/',
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB
    files: 5
  }
}).any();

// 新增路径：/service-attachment（从请求体获取serviceId）
router.post(
  '/service-attachment',
  authMiddleware,
  requirePermissions('service:write'),
  quickFixUpload, // 使用简单的 multer 配置
  async (req: any, res: any) => {
    try {
      // 调试：打印请求信息
      console.log('📝 快速修复 multer 上传请求信息:');
      console.log('Content-Type:', req.headers['content-type']);
      console.log('req.body:', req.body);
      console.log('req.body type:', typeof req.body);
      console.log('req.body keys:', req.body ? Object.keys(req.body) : 'no keys');
      console.log('req.files:', req.files);
      console.log('req.user:', req.user);

      // 尝试从多个地方获取serviceId - 处理 null prototype 问题
      let serviceId = null;
      
      if (req.body && req.body.serviceId) {
        serviceId = req.body.serviceId;
      } else if (req.query && req.query.serviceId) {
        serviceId = req.query.serviceId;  
      } else if (req.params && req.params.serviceId) {
        serviceId = req.params.serviceId;
      }
      
      console.log('🔍 serviceId 获取结果:', serviceId);
      
      // 如果还是没有，尝试从URL路径中提取
      if (!serviceId && req.url && req.url.includes('service/')) {
        const matches = req.url.match(/service\/([^\/]+)/);
        if (matches && matches[1]) {
          serviceId = matches[1];
        }
      }

      const userId = req.user?.id

      if (!userId) {
        return res.status(401).json({
          success: false,
          message: '用户未认证'
        })
      }

      if (!serviceId) {
        console.error('❌ serviceId参数缺失');
        console.error('req.body:', req.body);
        console.error('req.query:', req.query);
        console.error('req.params:', req.params);
        console.error('req.url:', req.url);
        return res.status(400).json({
          success: false,
          message: 'serviceId参数缺失，请检查请求参数'
        })
      }

      // 使用 .any() 后，文件存储在 req.files 中
      const files = req.files || [];
      const file = Array.isArray(files) ? files[0] : files;
      
      if (!file) {
        return res.status(400).json({
          success: false,
          message: '未选择文件'
        })
      }

      // 处理单文件上传
      const attachments = await UploadService.handleServiceAttachment(
        serviceId,
        Array.isArray(files) ? files : [file],
        userId
      )

      if (!attachments || attachments.length === 0) {
        throw new Error('文件上传失败：无法保存到数据库')
      }

      const attachment = attachments[0]!

      return res.status(201).json({
        success: true,
        message: '文件上传成功',
        data: {
          id: attachment.id,
          filename: attachment.filename,
          originalName: attachment.originalName,
          fileSize: attachment.fileSize,
          mimeType: attachment.mimeType,
          uploadedAt: attachment.uploadedAt,
          formattedSize: UploadService.formatFileSize(attachment.fileSize),
          filePath: attachment.filePath
        }
      })
    } catch (error: any) {
      console.error('上传服务附件失败:', error)
      return res.status(500).json({
        success: false,
        message: error.message || '文件上传失败'
      })
    }
  }
)

router.get(
  '/service/:serviceId/attachments',
  authMiddleware,
  requirePermissions('service:read'),
  getServiceAttachments
)

router.get(
  '/attachments/:attachmentId/download',
  authMiddleware,
  requirePermissions('service:read'),
  downloadAttachment
)

router.get(
  '/attachments/:attachmentId/preview',
  authMiddleware,
  requirePermissions('service:read'),
  previewAttachment
)

router.delete(
  '/attachments/:attachmentId',
  authMiddleware,
  requirePermissions('service:write'),
  deleteAttachment
)

router.get(
  '/stats',
  authMiddleware,
  requirePermissions('admin:all'),
  getStorageStats
)

router.post(
  '/cleanup',
  authMiddleware,
  requirePermissions('admin:all'),
  cleanupOldFiles
)

export default router