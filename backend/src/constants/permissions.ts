/**
 * 权限常量定义
 * 
 * 权限命名规则：
 * - 格式：模块名:操作类型[:子操作]
 * - 模块名：业务模块的简称
 * - 操作类型：read(查看)、write(编辑)、delete(删除)、manage(管理)、export(导出)、import(导入)
 * - 子操作：特定的业务操作
 * 
 * 权限层级：
 * - admin:all：超级管理员权限，拥有所有权限
 * - 模块级权限：如 user:*、customer:* 等
 * - 功能级权限：如 user:read、user:write 等
 * - 操作级权限：如 user:export、user:import 等
 */

// ==================== 系统管理权限 ====================

/** 超级管理员权限 */
export const ADMIN_ALL = 'admin:all'

/** 用户管理权限 */
export const USER_PERMISSIONS = {
  READ: 'user:read',           // 查看用户
  WRITE: 'user:write',         // 创建/编辑用户
  DELETE: 'user:delete',       // 删除用户
  MANAGE: 'user:manage',       // 用户管理（批量操作、状态切换）
  EXPORT: 'user:export',       // 导出用户数据
  IMPORT: 'user:import',       // 导入用户数据
  RESET_PASSWORD: 'user:reset_password', // 重置密码
  VIEW_SENSITIVE: 'user:view_sensitive', // 查看敏感信息（如登录记录）
} as const

/** 角色管理权限 */
export const ROLE_PERMISSIONS = {
  READ: 'role:read',           // 查看角色
  WRITE: 'role:write',         // 创建/编辑角色
  DELETE: 'role:delete',       // 删除角色
  ASSIGN: 'role:assign',       // 分配角色
  PERMISSION_MANAGE: 'role:permission_manage', // 权限管理
} as const

/** 审计日志权限 */
export const AUDIT_PERMISSIONS = {
  READ: 'audit:read',          // 查看审计日志
  EXPORT: 'audit:export',      // 导出审计日志
  DELETE: 'audit:delete',      // 删除审计日志
} as const

/** 系统配置权限 */
export const SYSTEM_PERMISSIONS = {
  READ: 'system:read',         // 查看系统配置
  WRITE: 'system:write',       // 修改系统配置
  SECURITY: 'system:security', // 安全配置管理
  BACKUP: 'system:backup',     // 备份恢复
  MONITOR: 'system:monitor',   // 系统监控
  ANALYTICS_VIEW: 'system:analytics:view',     // 用户活动分析查看
  ANALYTICS_MANAGE: 'system:analytics:manage', // 用户活动分析管理
  ANALYTICS_EXPORT: 'system:analytics:export', // 用户活动分析导出
} as const

// ==================== 业务模块权限 ====================

/** 客户管理权限 */
export const CUSTOMER_PERMISSIONS = {
  READ: 'customer:read',       // 查看客户
  WRITE: 'customer:write',     // 创建/编辑客户
  DELETE: 'customer:delete',   // 删除客户
  EXPORT: 'customer:export',   // 导出客户数据
  CONTACT_MANAGE: 'customer:contact_manage', // 联系人管理
  VIEW_ALL: 'customer:view_all', // 查看所有客户（跨部门）
} as const

/** 项目档案权限 */
export const ARCHIVE_PERMISSIONS = {
  READ: 'archive:read',        // 查看项目档案
  WRITE: 'archive:write',      // 创建/编辑项目档案
  DELETE: 'archive:delete',    // 删除项目档案
  CONFIG_READ: 'archive:config_read',   // 查看项目配置
  CONFIG_WRITE: 'archive:config_write', // 修改项目配置
  CONFIG_SENSITIVE: 'archive:config_sensitive', // 查看敏感配置信息
  EXPORT: 'archive:export',    // 导出项目档案
  VIEW_ALL: 'archive:view_all', // 查看所有项目（跨负责人）
} as const

/** 服务工单权限 */
export const SERVICE_PERMISSIONS = {
  READ: 'service:read',        // 查看服务工单
  WRITE: 'service:write',      // 创建/编辑服务工单
  DELETE: 'service:delete',    // 删除服务工单
  ASSIGN: 'service:assign',    // 分配工单
  TRANSFER: 'service:transfer', // 转交工单
  CLOSE: 'service:close',      // 关闭工单
  REOPEN: 'service:reopen',    // 重新打开工单
  COMMENT: 'service:comment',  // 添加评论
  COMMENT_INTERNAL: 'service:comment_internal', // 内部评论
  ATTACHMENT: 'service:attachment', // 附件管理
  WORK_LOG: 'service:work_log', // 工作日志
  EXPORT: 'service:export',    // 导出工单数据
  STATS: 'service:stats',      // 查看统计数据
  VIEW_ALL: 'service:view_all', // 查看所有工单（跨分配人）
  BULK_OPERATION: 'service:bulk_operation', // 批量操作
} as const

/** SLA管理权限 */
export const SLA_PERMISSIONS = {
  READ: 'sla:read',            // 查看SLA模板
  WRITE: 'sla:write',          // 创建/编辑SLA模板
  DELETE: 'sla:delete',        // 删除SLA模板
  MONITOR: 'sla:monitor',      // SLA监控
  REPORT: 'sla:report',        // SLA报表
} as const

/** 配置管理权限 */
export const CONFIG_PERMISSIONS = {
  READ: 'config:read',         // 查看配置
  WRITE: 'config:write',       // 创建/编辑配置
  DELETE: 'config:delete',     // 删除配置
  SENSITIVE: 'config:sensitive', // 敏感配置管理
  EXPORT: 'config:export',     // 导出配置（脱敏）
  BACKUP: 'config:backup',     // 配置备份
} as const

// ==================== 通知和文件权限 ====================

/** 通知管理权限 */
export const NOTIFICATION_PERMISSIONS = {
  READ: 'notification:read',   // 查看通知
  WRITE: 'notification:write', // 创建/编辑通知
  DELETE: 'notification:delete', // 删除通知
  TEMPLATE_MANAGE: 'notification:template_manage', // 通知模板管理
  SEND: 'notification:send',   // 发送通知
  CONFIG: 'notification:config', // 通知配置
} as const

/** 文件管理权限 */
export const FILE_PERMISSIONS = {
  READ: 'file:read',           // 查看文件
  UPLOAD: 'file:upload',       // 上传文件
  DELETE: 'file:delete',       // 删除文件
  DOWNLOAD: 'file:download',   // 下载文件
  MANAGE: 'file:manage',       // 文件管理
} as const

// ==================== 监控告警权限 ====================

/** 监控告警权限 */
export const ALERT_PERMISSIONS = {
  READ: 'alert:read',          // 查看告警
  WRITE: 'alert:write',        // 创建/编辑告警规则
  DELETE: 'alert:delete',      // 删除告警规则
  ACKNOWLEDGE: 'alert:acknowledge', // 确认告警
  RESOLVE: 'alert:resolve',    // 解决告警
  CONFIG: 'alert:config',      // 告警配置
} as const

// ==================== 权限分组定义 ====================

/** 所有权限常量 */
export const ALL_PERMISSIONS = {
  // 系统管理
  ADMIN_ALL,
  ...USER_PERMISSIONS,
  ...ROLE_PERMISSIONS,
  ...AUDIT_PERMISSIONS,
  ...SYSTEM_PERMISSIONS,
  
  // 业务模块
  ...CUSTOMER_PERMISSIONS,
  ...ARCHIVE_PERMISSIONS,
  ...SERVICE_PERMISSIONS,
  ...SLA_PERMISSIONS,
  ...CONFIG_PERMISSIONS,
  
  // 通知和文件
  ...NOTIFICATION_PERMISSIONS,
  ...FILE_PERMISSIONS,
  
  // 监控告警
  ...ALERT_PERMISSIONS,
} as const

/** 权限分组 - 用于权限管理界面 */
export const PERMISSION_GROUPS = {
  SYSTEM: {
    name: '系统管理',
    description: '用户、角色、系统配置等管理权限',
    permissions: [
      USER_PERMISSIONS.READ,
      USER_PERMISSIONS.WRITE,
      USER_PERMISSIONS.DELETE,
      USER_PERMISSIONS.MANAGE,
      USER_PERMISSIONS.EXPORT,
      USER_PERMISSIONS.IMPORT,
      USER_PERMISSIONS.RESET_PASSWORD,
      USER_PERMISSIONS.VIEW_SENSITIVE,
      ROLE_PERMISSIONS.READ,
      ROLE_PERMISSIONS.WRITE,
      ROLE_PERMISSIONS.DELETE,
      ROLE_PERMISSIONS.ASSIGN,
      ROLE_PERMISSIONS.PERMISSION_MANAGE,
      AUDIT_PERMISSIONS.READ,
      AUDIT_PERMISSIONS.EXPORT,
      AUDIT_PERMISSIONS.DELETE,
      SYSTEM_PERMISSIONS.READ,
      SYSTEM_PERMISSIONS.WRITE,
      SYSTEM_PERMISSIONS.SECURITY,
      SYSTEM_PERMISSIONS.BACKUP,
      SYSTEM_PERMISSIONS.MONITOR,
      SYSTEM_PERMISSIONS.ANALYTICS_VIEW,
      SYSTEM_PERMISSIONS.ANALYTICS_MANAGE,
      SYSTEM_PERMISSIONS.ANALYTICS_EXPORT,
    ],
  },
  
  BUSINESS: {
    name: '业务管理',
    description: '客户、项目档案、服务工单等业务权限',
    permissions: [
      CUSTOMER_PERMISSIONS.READ,
      CUSTOMER_PERMISSIONS.WRITE,
      CUSTOMER_PERMISSIONS.DELETE,
      CUSTOMER_PERMISSIONS.EXPORT,
      CUSTOMER_PERMISSIONS.CONTACT_MANAGE,
      CUSTOMER_PERMISSIONS.VIEW_ALL,
      ARCHIVE_PERMISSIONS.READ,
      ARCHIVE_PERMISSIONS.WRITE,
      ARCHIVE_PERMISSIONS.DELETE,
      ARCHIVE_PERMISSIONS.CONFIG_READ,
      ARCHIVE_PERMISSIONS.CONFIG_WRITE,
      ARCHIVE_PERMISSIONS.CONFIG_SENSITIVE,
      ARCHIVE_PERMISSIONS.EXPORT,
      ARCHIVE_PERMISSIONS.VIEW_ALL,
      SERVICE_PERMISSIONS.READ,
      SERVICE_PERMISSIONS.WRITE,
      SERVICE_PERMISSIONS.DELETE,
      SERVICE_PERMISSIONS.ASSIGN,
      SERVICE_PERMISSIONS.TRANSFER,
      SERVICE_PERMISSIONS.CLOSE,
      SERVICE_PERMISSIONS.REOPEN,
      SERVICE_PERMISSIONS.COMMENT,
      SERVICE_PERMISSIONS.COMMENT_INTERNAL,
      SERVICE_PERMISSIONS.ATTACHMENT,
      SERVICE_PERMISSIONS.WORK_LOG,
      SERVICE_PERMISSIONS.EXPORT,
      SERVICE_PERMISSIONS.STATS,
      SERVICE_PERMISSIONS.VIEW_ALL,
      SERVICE_PERMISSIONS.BULK_OPERATION,
    ],
  },
  
  CONFIGURATION: {
    name: '配置管理',
    description: 'SLA、系统配置、项目配置等管理权限',
    permissions: [
      SLA_PERMISSIONS.READ,
      SLA_PERMISSIONS.WRITE,
      SLA_PERMISSIONS.DELETE,
      SLA_PERMISSIONS.MONITOR,
      SLA_PERMISSIONS.REPORT,
      CONFIG_PERMISSIONS.READ,
      CONFIG_PERMISSIONS.WRITE,
      CONFIG_PERMISSIONS.DELETE,
      CONFIG_PERMISSIONS.SENSITIVE,
      CONFIG_PERMISSIONS.EXPORT,
      CONFIG_PERMISSIONS.BACKUP,
    ],
  },
  
  COMMUNICATION: {
    name: '通知文件',
    description: '通知管理和文件管理权限',
    permissions: [
      NOTIFICATION_PERMISSIONS.READ,
      NOTIFICATION_PERMISSIONS.WRITE,
      NOTIFICATION_PERMISSIONS.DELETE,
      NOTIFICATION_PERMISSIONS.TEMPLATE_MANAGE,
      NOTIFICATION_PERMISSIONS.SEND,
      NOTIFICATION_PERMISSIONS.CONFIG,
      FILE_PERMISSIONS.READ,
      FILE_PERMISSIONS.UPLOAD,
      FILE_PERMISSIONS.DELETE,
      FILE_PERMISSIONS.DOWNLOAD,
      FILE_PERMISSIONS.MANAGE,
    ],
  },
  
  MONITORING: {
    name: '监控告警',
    description: '系统监控和告警管理权限',
    permissions: [
      ALERT_PERMISSIONS.READ,
      ALERT_PERMISSIONS.WRITE,
      ALERT_PERMISSIONS.DELETE,
      ALERT_PERMISSIONS.ACKNOWLEDGE,
      ALERT_PERMISSIONS.RESOLVE,
      ALERT_PERMISSIONS.CONFIG,
    ],
  },
} as const

// ==================== 默认角色权限配置 ====================

/** 默认角色权限配置 */
export const DEFAULT_ROLE_PERMISSIONS = {
  // 超级管理员 - 拥有所有权限
  admin: [ADMIN_ALL],
  
  // 运维工程师 - 主要负责技术实施和维护
  engineer: [
    // 客户信息查看
    CUSTOMER_PERMISSIONS.READ,
    
    // 项目档案完整权限
    ARCHIVE_PERMISSIONS.READ,
    ARCHIVE_PERMISSIONS.WRITE,
    ARCHIVE_PERMISSIONS.CONFIG_READ,
    ARCHIVE_PERMISSIONS.CONFIG_WRITE,
    ARCHIVE_PERMISSIONS.CONFIG_SENSITIVE,
    ARCHIVE_PERMISSIONS.VIEW_ALL,
    
    // 服务工单完整权限
    SERVICE_PERMISSIONS.READ,
    SERVICE_PERMISSIONS.WRITE,
    SERVICE_PERMISSIONS.COMMENT,
    SERVICE_PERMISSIONS.COMMENT_INTERNAL,
    SERVICE_PERMISSIONS.ATTACHMENT,
    SERVICE_PERMISSIONS.WORK_LOG,
    SERVICE_PERMISSIONS.TRANSFER,
    SERVICE_PERMISSIONS.CLOSE,
    SERVICE_PERMISSIONS.REOPEN,
    SERVICE_PERMISSIONS.VIEW_ALL,
    
    // 配置管理权限
    CONFIG_PERMISSIONS.READ,
    CONFIG_PERMISSIONS.WRITE,
    CONFIG_PERMISSIONS.SENSITIVE,
    
    // SLA查看权限
    SLA_PERMISSIONS.READ,
    SLA_PERMISSIONS.MONITOR,
    
    // 文件管理权限
    FILE_PERMISSIONS.READ,
    FILE_PERMISSIONS.UPLOAD,
    FILE_PERMISSIONS.DOWNLOAD,
    
    // 告警处理权限
    ALERT_PERMISSIONS.READ,
    ALERT_PERMISSIONS.ACKNOWLEDGE,
    ALERT_PERMISSIONS.RESOLVE,
    
    // 通知权限
    NOTIFICATION_PERMISSIONS.READ,
    NOTIFICATION_PERMISSIONS.SEND,
  ],
  
  // 客户服务 - 主要负责客户沟通和工单管理
  customer_service: [
    // 客户管理权限
    CUSTOMER_PERMISSIONS.READ,
    CUSTOMER_PERMISSIONS.WRITE,
    CUSTOMER_PERMISSIONS.CONTACT_MANAGE,
    
    // 项目档案查看权限
    ARCHIVE_PERMISSIONS.READ,
    ARCHIVE_PERMISSIONS.CONFIG_READ, // 基础配置查看，不包含敏感信息
    
    // 服务工单权限（不包含内部评论和敏感操作）
    SERVICE_PERMISSIONS.READ,
    SERVICE_PERMISSIONS.WRITE,
    SERVICE_PERMISSIONS.COMMENT,
    SERVICE_PERMISSIONS.ATTACHMENT,
    SERVICE_PERMISSIONS.ASSIGN,
    SERVICE_PERMISSIONS.VIEW_ALL,
    
    // SLA查看权限
    SLA_PERMISSIONS.READ,
    
    // 文件基础权限
    FILE_PERMISSIONS.READ,
    FILE_PERMISSIONS.UPLOAD,
    FILE_PERMISSIONS.DOWNLOAD,
    
    // 通知权限
    NOTIFICATION_PERMISSIONS.READ,
    NOTIFICATION_PERMISSIONS.SEND,
  ],
  
  // 项目经理 - 负责项目管理和统筹
  project_manager: [
    // 客户管理权限
    CUSTOMER_PERMISSIONS.READ,
    CUSTOMER_PERMISSIONS.WRITE,
    CUSTOMER_PERMISSIONS.CONTACT_MANAGE,
    CUSTOMER_PERMISSIONS.EXPORT,
    CUSTOMER_PERMISSIONS.VIEW_ALL,
    
    // 项目档案管理权限
    ARCHIVE_PERMISSIONS.READ,
    ARCHIVE_PERMISSIONS.WRITE,
    ARCHIVE_PERMISSIONS.CONFIG_READ,
    ARCHIVE_PERMISSIONS.EXPORT,
    ARCHIVE_PERMISSIONS.VIEW_ALL,
    
    // 服务工单管理权限
    SERVICE_PERMISSIONS.READ,
    SERVICE_PERMISSIONS.WRITE,
    SERVICE_PERMISSIONS.ASSIGN,
    SERVICE_PERMISSIONS.TRANSFER,
    SERVICE_PERMISSIONS.COMMENT,
    SERVICE_PERMISSIONS.ATTACHMENT,
    SERVICE_PERMISSIONS.EXPORT,
    SERVICE_PERMISSIONS.STATS,
    SERVICE_PERMISSIONS.VIEW_ALL,
    SERVICE_PERMISSIONS.BULK_OPERATION,
    
    // SLA管理权限
    SLA_PERMISSIONS.READ,
    SLA_PERMISSIONS.WRITE,
    SLA_PERMISSIONS.MONITOR,
    SLA_PERMISSIONS.REPORT,
    
    // 文件管理权限
    FILE_PERMISSIONS.READ,
    FILE_PERMISSIONS.UPLOAD,
    FILE_PERMISSIONS.DELETE,
    FILE_PERMISSIONS.DOWNLOAD,
    FILE_PERMISSIONS.MANAGE,
    
    // 告警查看权限
    ALERT_PERMISSIONS.READ,
    
    // 通知权限
    NOTIFICATION_PERMISSIONS.READ,
    NOTIFICATION_PERMISSIONS.WRITE,
    NOTIFICATION_PERMISSIONS.SEND,
  ],
  
  // 运维主管 - 负责运维团队管理
  ops_supervisor: [
    // 用户管理权限（限制删除）
    USER_PERMISSIONS.READ,
    USER_PERMISSIONS.WRITE,
    USER_PERMISSIONS.MANAGE,
    USER_PERMISSIONS.EXPORT,
    USER_PERMISSIONS.VIEW_SENSITIVE,
    
    // 角色基础权限
    ROLE_PERMISSIONS.READ,
    ROLE_PERMISSIONS.ASSIGN,
    
    // 审计日志权限
    AUDIT_PERMISSIONS.READ,
    AUDIT_PERMISSIONS.EXPORT,
    
    // 用户活动分析权限
    SYSTEM_PERMISSIONS.ANALYTICS_VIEW,
    
    // 业务全权限
    ...Object.values(CUSTOMER_PERMISSIONS),
    ...Object.values(ARCHIVE_PERMISSIONS),
    ...Object.values(SERVICE_PERMISSIONS),
    ...Object.values(SLA_PERMISSIONS),
    ...Object.values(CONFIG_PERMISSIONS),
    ...Object.values(NOTIFICATION_PERMISSIONS),
    ...Object.values(FILE_PERMISSIONS),
    ...Object.values(ALERT_PERMISSIONS),
  ],
  
  // 只读用户 - 仅查看权限
  readonly: [
    CUSTOMER_PERMISSIONS.READ,
    ARCHIVE_PERMISSIONS.READ,
    ARCHIVE_PERMISSIONS.CONFIG_READ,
    SERVICE_PERMISSIONS.READ,
    SLA_PERMISSIONS.READ,
    FILE_PERMISSIONS.READ,
    FILE_PERMISSIONS.DOWNLOAD,
    ALERT_PERMISSIONS.READ,
    NOTIFICATION_PERMISSIONS.READ,
  ],
} as const

// ==================== 权限描述映射 ====================

/** 权限描述映射 - 用于界面显示 */
export const PERMISSION_DESCRIPTIONS: Record<string, string> = {
  // 超级管理员
  [ADMIN_ALL]: '超级管理员权限',
  
  // 用户管理
  [USER_PERMISSIONS.READ]: '查看用户信息',
  [USER_PERMISSIONS.WRITE]: '创建和编辑用户',
  [USER_PERMISSIONS.DELETE]: '删除用户',
  [USER_PERMISSIONS.MANAGE]: '用户管理（批量操作、状态切换）',
  [USER_PERMISSIONS.EXPORT]: '导出用户数据',
  [USER_PERMISSIONS.IMPORT]: '导入用户数据',
  [USER_PERMISSIONS.RESET_PASSWORD]: '重置用户密码',
  [USER_PERMISSIONS.VIEW_SENSITIVE]: '查看敏感用户信息',
  
  // 角色管理
  [ROLE_PERMISSIONS.READ]: '查看角色信息',
  [ROLE_PERMISSIONS.WRITE]: '创建和编辑角色',
  [ROLE_PERMISSIONS.DELETE]: '删除角色',
  [ROLE_PERMISSIONS.ASSIGN]: '分配角色给用户',
  [ROLE_PERMISSIONS.PERMISSION_MANAGE]: '管理角色权限',
  
  // 审计日志
  [AUDIT_PERMISSIONS.READ]: '查看审计日志',
  [AUDIT_PERMISSIONS.EXPORT]: '导出审计日志',
  [AUDIT_PERMISSIONS.DELETE]: '删除审计日志',
  
  // 系统配置
  [SYSTEM_PERMISSIONS.READ]: '查看系统配置',
  [SYSTEM_PERMISSIONS.WRITE]: '修改系统配置',
  [SYSTEM_PERMISSIONS.SECURITY]: '安全配置管理',
  [SYSTEM_PERMISSIONS.BACKUP]: '系统备份和恢复',
  [SYSTEM_PERMISSIONS.MONITOR]: '系统监控',
  [SYSTEM_PERMISSIONS.ANALYTICS_VIEW]: '查看用户活动分析',
  [SYSTEM_PERMISSIONS.ANALYTICS_MANAGE]: '管理用户活动分析',
  [SYSTEM_PERMISSIONS.ANALYTICS_EXPORT]: '导出用户活动报告',
  
  // 客户管理
  [CUSTOMER_PERMISSIONS.READ]: '查看客户信息',
  [CUSTOMER_PERMISSIONS.WRITE]: '创建和编辑客户',
  [CUSTOMER_PERMISSIONS.DELETE]: '删除客户',
  [CUSTOMER_PERMISSIONS.EXPORT]: '导出客户数据',
  [CUSTOMER_PERMISSIONS.CONTACT_MANAGE]: '管理客户联系人',
  [CUSTOMER_PERMISSIONS.VIEW_ALL]: '查看所有客户（跨部门）',
  
  // 项目档案
  [ARCHIVE_PERMISSIONS.READ]: '查看项目档案',
  [ARCHIVE_PERMISSIONS.WRITE]: '创建和编辑项目档案',
  [ARCHIVE_PERMISSIONS.DELETE]: '删除项目档案',
  [ARCHIVE_PERMISSIONS.CONFIG_READ]: '查看项目配置',
  [ARCHIVE_PERMISSIONS.CONFIG_WRITE]: '修改项目配置',
  [ARCHIVE_PERMISSIONS.CONFIG_SENSITIVE]: '查看敏感配置信息',
  [ARCHIVE_PERMISSIONS.EXPORT]: '导出项目档案',
  [ARCHIVE_PERMISSIONS.VIEW_ALL]: '查看所有项目（跨负责人）',
  
  // 服务工单
  [SERVICE_PERMISSIONS.READ]: '查看服务工单',
  [SERVICE_PERMISSIONS.WRITE]: '创建和编辑服务工单',
  [SERVICE_PERMISSIONS.DELETE]: '删除服务工单',
  [SERVICE_PERMISSIONS.ASSIGN]: '分配服务工单',
  [SERVICE_PERMISSIONS.TRANSFER]: '转交服务工单',
  [SERVICE_PERMISSIONS.CLOSE]: '关闭服务工单',
  [SERVICE_PERMISSIONS.REOPEN]: '重新打开服务工单',
  [SERVICE_PERMISSIONS.COMMENT]: '添加工单评论',
  [SERVICE_PERMISSIONS.COMMENT_INTERNAL]: '添加内部评论',
  [SERVICE_PERMISSIONS.ATTACHMENT]: '管理工单附件',
  [SERVICE_PERMISSIONS.WORK_LOG]: '记录工作日志',
  [SERVICE_PERMISSIONS.EXPORT]: '导出工单数据',
  [SERVICE_PERMISSIONS.STATS]: '查看工单统计',
  [SERVICE_PERMISSIONS.VIEW_ALL]: '查看所有工单（跨分配人）',
  [SERVICE_PERMISSIONS.BULK_OPERATION]: '批量操作工单',
  
  // SLA管理
  [SLA_PERMISSIONS.READ]: '查看SLA模板',
  [SLA_PERMISSIONS.WRITE]: '创建和编辑SLA模板',
  [SLA_PERMISSIONS.DELETE]: '删除SLA模板',
  [SLA_PERMISSIONS.MONITOR]: 'SLA监控',
  [SLA_PERMISSIONS.REPORT]: 'SLA报表',
  
  // 配置管理
  [CONFIG_PERMISSIONS.READ]: '查看配置信息',
  [CONFIG_PERMISSIONS.WRITE]: '创建和编辑配置',
  [CONFIG_PERMISSIONS.DELETE]: '删除配置',
  [CONFIG_PERMISSIONS.SENSITIVE]: '管理敏感配置',
  [CONFIG_PERMISSIONS.EXPORT]: '导出配置数据',
  [CONFIG_PERMISSIONS.BACKUP]: '配置备份',
  
  // 通知管理
  [NOTIFICATION_PERMISSIONS.READ]: '查看通知',
  [NOTIFICATION_PERMISSIONS.WRITE]: '创建和编辑通知',
  [NOTIFICATION_PERMISSIONS.DELETE]: '删除通知',
  [NOTIFICATION_PERMISSIONS.TEMPLATE_MANAGE]: '管理通知模板',
  [NOTIFICATION_PERMISSIONS.SEND]: '发送通知',
  [NOTIFICATION_PERMISSIONS.CONFIG]: '通知配置',
  
  // 文件管理
  [FILE_PERMISSIONS.READ]: '查看文件',
  [FILE_PERMISSIONS.UPLOAD]: '上传文件',
  [FILE_PERMISSIONS.DELETE]: '删除文件',
  [FILE_PERMISSIONS.DOWNLOAD]: '下载文件',
  [FILE_PERMISSIONS.MANAGE]: '文件管理',
  
  // 监控告警
  [ALERT_PERMISSIONS.READ]: '查看告警信息',
  [ALERT_PERMISSIONS.WRITE]: '创建和编辑告警规则',
  [ALERT_PERMISSIONS.DELETE]: '删除告警规则',
  [ALERT_PERMISSIONS.ACKNOWLEDGE]: '确认告警',
  [ALERT_PERMISSIONS.RESOLVE]: '解决告警',
  [ALERT_PERMISSIONS.CONFIG]: '告警配置',
}

// ==================== 类型定义 ====================

/** 权限值类型 */
export type Permission = typeof ALL_PERMISSIONS[keyof typeof ALL_PERMISSIONS]

/** 权限分组名称类型 */
export type PermissionGroupName = keyof typeof PERMISSION_GROUPS

/** 角色名称类型 */
export type RoleName = keyof typeof DEFAULT_ROLE_PERMISSIONS

// ==================== 工具函数 ====================

/**
 * 获取所有权限列表
 */
export function getAllPermissions(): Permission[] {
  return Object.values(ALL_PERMISSIONS)
}

/**
 * 获取权限分组
 */
export function getPermissionGroups() {
  return PERMISSION_GROUPS
}

/**
 * 获取角色默认权限
 */
export function getDefaultRolePermissions(roleName: RoleName): Permission[] {
  return DEFAULT_ROLE_PERMISSIONS[roleName] || []
}

/**
 * 获取权限描述
 */
export function getPermissionDescription(permission: Permission): string {
  return PERMISSION_DESCRIPTIONS[permission] || permission
}

/**
 * 检查权限是否存在
 */
export function isValidPermission(permission: string): permission is Permission {
  return Object.values(ALL_PERMISSIONS).includes(permission as Permission)
}

/**
 * 根据模块获取相关权限
 */
export function getModulePermissions(module: string): Permission[] {
  const moduleUpper = module.toUpperCase()
  
  switch (moduleUpper) {
    case 'USER':
      return Object.values(USER_PERMISSIONS)
    case 'ROLE':
      return Object.values(ROLE_PERMISSIONS)
    case 'AUDIT':
      return Object.values(AUDIT_PERMISSIONS)
    case 'SYSTEM':
      return Object.values(SYSTEM_PERMISSIONS)
    case 'CUSTOMER':
      return Object.values(CUSTOMER_PERMISSIONS)
    case 'ARCHIVE':
      return Object.values(ARCHIVE_PERMISSIONS)
    case 'SERVICE':
      return Object.values(SERVICE_PERMISSIONS)
    case 'SLA':
      return Object.values(SLA_PERMISSIONS)
    case 'CONFIG':
      return Object.values(CONFIG_PERMISSIONS)
    case 'NOTIFICATION':
      return Object.values(NOTIFICATION_PERMISSIONS)
    case 'FILE':
      return Object.values(FILE_PERMISSIONS)
    case 'ALERT':
      return Object.values(ALERT_PERMISSIONS)
    default:
      return []
  }
}