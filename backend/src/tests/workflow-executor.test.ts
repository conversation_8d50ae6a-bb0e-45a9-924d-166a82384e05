/**
 * 工作流执行器API测试用例
 * 测试工作流执行、监控和管理的REST API端点
 */

import request from 'supertest';
import { app } from '../index'; // 假设主应用导出
import { workflowCoordinator } from '@/services/workflow-coordinator.service';
import { metricsCollector } from '@/services/metrics-collector.service';
import { logAggregator } from '@/services/log-aggregator.service';
import { executionStateTracker } from '@/services/execution-state-tracker.service';

// 模拟认证中间件
jest.mock('@/middleware/auth.middleware', () => ({
  authMiddleware: (req: any, res: any, next: any) => {
    req.user = {
      id: 'test-user-001',
      role: 'ADMIN'
    };
    next();
  }
}));

// 模拟工作流协调器服务
jest.mock('@/services/workflow-coordinator.service', () => ({
  workflowCoordinator: {
    startWorkflowExecution: jest.fn(),
    pauseWorkflowExecution: jest.fn(),
    resumeWorkflowExecution: jest.fn(),
    stopWorkflowExecution: jest.fn(),
    getExecutionSession: jest.fn(),
    listExecutionSessions: jest.fn(),
    getCoordinatorStatus: jest.fn(),
    getExecutionStatistics: jest.fn()
  }
}));

// 模拟指标收集器服务
jest.mock('@/services/metrics-collector.service', () => ({
  metricsCollector: {
    getMetrics: jest.fn(),
    getHealthScore: jest.fn(),
    getRealTimeMetrics: jest.fn(),
    getPerformanceTrend: jest.fn(),
    getExecutorPerformanceComparison: jest.fn()
  }
}));

// 模拟日志聚合器服务
jest.mock('@/services/log-aggregator.service', () => ({
  logAggregator: {
    queryLogs: jest.fn(),
    getLogStatistics: jest.fn(),
    exportLogs: jest.fn()
  }
}));

// 模拟执行状态跟踪器服务
jest.mock('@/services/execution-state-tracker.service', () => ({
  executionStateTracker: {
    getExecutionState: jest.fn()
  }
}));

describe('Workflow Executor API', () => {
  
  beforeEach(() => {
    // 重置所有mock
    jest.clearAllMocks();
  });

  describe('POST /api/v1/workflow/execute', () => {
    
    it('应该成功启动工作流执行', async () => {
      const mockSessionId = 'wf_session_12345_abc';
      const mockSession = {
        sessionId: mockSessionId,
        executionId: 'exec_12345',
        workflowId: 'workflow_001',
        status: 'STARTING',
        startTime: new Date(),
        totalSteps: 3,
        currentStep: 0
      };

      (workflowCoordinator.startWorkflowExecution as jest.Mock).mockResolvedValue(mockSessionId);
      (workflowCoordinator.getExecutionSession as jest.Mock).mockReturnValue(mockSession);

      const requestBody = {
        workflowId: '550e8400-e29b-41d4-a716-************',
        context: { customerId: 'cust_001' },
        priority: 'HIGH',
        tags: ['urgent', 'test'],
        metadata: { source: 'api_test' }
      };

      const response = await request(app)
        .post('/api/v1/workflow/execute')
        .send(requestBody)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.sessionId).toBe(mockSessionId);
      expect(response.body.data.workflowId).toBe('workflow_001');
      expect(response.body.data.status).toBe('STARTING');
      expect(response.body.message).toBe('工作流执行启动成功');

      expect(workflowCoordinator.startWorkflowExecution).toHaveBeenCalledWith(
        requestBody.workflowId,
        expect.objectContaining({
          customerId: 'cust_001',
          _meta: expect.objectContaining({
            userId: 'test-user-001',
            userRole: 'ADMIN',
            priority: 'HIGH',
            tags: ['urgent', 'test']
          })
        })
      );
    });

    it('应该验证请求参数', async () => {
      const invalidRequest = {
        workflowId: 'invalid-uuid',
        context: 'invalid-context'
      };

      const response = await request(app)
        .post('/api/v1/workflow/execute')
        .send(invalidRequest)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe('请求参数验证失败');
    });

    it('应该处理工作流启动失败', async () => {
      (workflowCoordinator.startWorkflowExecution as jest.Mock).mockRejectedValue(
        new Error('工作流不存在')
      );

      const requestBody = {
        workflowId: '550e8400-e29b-41d4-a716-************',
        context: {}
      };

      const response = await request(app)
        .post('/api/v1/workflow/execute')
        .send(requestBody)
        .expect(500);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe('工作流执行启动失败');
    });
  });

  describe('GET /api/v1/workflow/execution/:sessionId', () => {
    
    it('应该返回执行状态信息', async () => {
      const sessionId = 'wf_session_12345_abc';
      const mockSession = {
        sessionId,
        executionId: 'exec_12345',
        workflowId: 'workflow_001',
        status: 'RUNNING',
        startTime: new Date('2024-01-01T10:00:00Z'),
        endTime: null,
        currentStep: 2,
        totalSteps: 5,
        errors: []
      };

      (workflowCoordinator.getExecutionSession as jest.Mock).mockReturnValue(mockSession);

      const response = await request(app)
        .get(`/api/v1/workflow/execution/${sessionId}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.sessionId).toBe(sessionId);
      expect(response.body.data.status).toBe('RUNNING');
      expect(response.body.data.progress).toBe(40); // 2/5 * 100
      expect(response.body.data.duration).toBeGreaterThan(0);
    });

    it('应该处理会话不存在的情况', async () => {
      const sessionId = 'non-existent-session';
      (workflowCoordinator.getExecutionSession as jest.Mock).mockReturnValue(null);

      const response = await request(app)
        .get(`/api/v1/workflow/execution/${sessionId}`)
        .expect(404);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe('执行会话不存在');
    });
  });

  describe('GET /api/v1/workflow/executions', () => {
    
    it('应该返回执行列表', async () => {
      const mockSessions = [
        {
          sessionId: 'session_001',
          executionId: 'exec_001',
          workflowId: 'workflow_001',
          status: 'RUNNING',
          startTime: new Date('2024-01-01T10:00:00Z'),
          endTime: null,
          currentStep: 1,
          totalSteps: 3,
          errors: []
        },
        {
          sessionId: 'session_002',
          executionId: 'exec_002',
          workflowId: 'workflow_002',
          status: 'STOPPED',
          startTime: new Date('2024-01-01T09:00:00Z'),
          endTime: new Date('2024-01-01T09:05:00Z'),
          currentStep: 3,
          totalSteps: 3,
          errors: []
        }
      ];

      (workflowCoordinator.listExecutionSessions as jest.Mock).mockReturnValue(mockSessions);

      const response = await request(app)
        .get('/api/v1/workflow/executions')
        .query({ page: 1, limit: 10, status: 'RUNNING' })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.executions).toHaveLength(1); // 只有状态为RUNNING的
      expect(response.body.data.pagination.total).toBe(1);
    });

    it('应该支持查询参数验证', async () => {
      const response = await request(app)
        .get('/api/v1/workflow/executions')
        .query({ page: 0, limit: 101 }) // 无效的参数
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe('查询参数验证失败');
    });
  });

  describe('POST /api/v1/workflow/execution/pause', () => {
    
    it('应该成功暂停工作流执行', async () => {
      const sessionId = 'wf_session_12345_abc';
      (workflowCoordinator.pauseWorkflowExecution as jest.Mock).mockResolvedValue(undefined);

      const response = await request(app)
        .post('/api/v1/workflow/execution/pause')
        .send({ sessionId, reason: '测试暂停' })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.message).toBe('工作流执行已暂停');
      expect(workflowCoordinator.pauseWorkflowExecution).toHaveBeenCalledWith(sessionId);
    });

    it('应该处理暂停失败', async () => {
      const sessionId = 'invalid_session';
      (workflowCoordinator.pauseWorkflowExecution as jest.Mock).mockRejectedValue(
        new Error('会话不存在')
      );

      const response = await request(app)
        .post('/api/v1/workflow/execution/pause')
        .send({ sessionId })
        .expect(500);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe('暂停工作流执行失败');
    });
  });

  describe('POST /api/v1/workflow/execution/resume', () => {
    
    it('应该成功恢复工作流执行', async () => {
      const sessionId = 'wf_session_12345_abc';
      (workflowCoordinator.resumeWorkflowExecution as jest.Mock).mockResolvedValue(undefined);

      const response = await request(app)
        .post('/api/v1/workflow/execution/resume')
        .send({ sessionId, reason: '测试恢复' })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.message).toBe('工作流执行已恢复');
      expect(workflowCoordinator.resumeWorkflowExecution).toHaveBeenCalledWith(sessionId);
    });
  });

  describe('POST /api/v1/workflow/execution/stop', () => {
    
    it('应该成功停止工作流执行', async () => {
      const sessionId = 'wf_session_12345_abc';
      (workflowCoordinator.stopWorkflowExecution as jest.Mock).mockResolvedValue(undefined);

      const response = await request(app)
        .post('/api/v1/workflow/execution/stop')
        .send({ sessionId, reason: '测试停止' })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.message).toBe('工作流执行已停止');
      expect(workflowCoordinator.stopWorkflowExecution).toHaveBeenCalledWith(sessionId, '测试停止');
    });
  });

  describe('GET /api/v1/workflow/coordinator/status', () => {
    
    it('应该返回协调器状态', async () => {
      const mockCoordinatorStatus = {
        status: 'RUNNING',
        activeSessions: 2,
        totalSessions: 5,
        config: { maxConcurrentSessions: 10 }
      };

      const mockStatistics = {
        completedExecutions: 15,
        failedExecutions: 2,
        runningExecutions: 2,
        averageExecutionTime: 3500,
        successRate: 88.24
      };

      (workflowCoordinator.getCoordinatorStatus as jest.Mock).mockReturnValue(mockCoordinatorStatus);
      (workflowCoordinator.getExecutionStatistics as jest.Mock).mockReturnValue(mockStatistics);

      const response = await request(app)
        .get('/api/v1/workflow/coordinator/status')
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.coordinator).toEqual(mockCoordinatorStatus);
      expect(response.body.data.statistics).toEqual(mockStatistics);
      expect(response.body.data.timestamp).toBeDefined();
    });
  });

  describe('GET /api/v1/workflow/metrics', () => {
    
    it('应该返回执行指标', async () => {
      const mockMetrics = {
        totalExecutions: 100,
        successCount: 85,
        failureCount: 15
      };

      const mockHealthScore = 87.5;
      const mockRealtimeMetrics = {
        activeExecutions: 3,
        queueSize: 5
      };

      (metricsCollector.getMetrics as jest.Mock).mockReturnValue(mockMetrics);
      (metricsCollector.getHealthScore as jest.Mock).mockReturnValue(mockHealthScore);
      (metricsCollector.getRealTimeMetrics as jest.Mock).mockReturnValue(mockRealtimeMetrics);

      const response = await request(app)
        .get('/api/v1/workflow/metrics')
        .query({ timeWindow: '24h', includeDetails: false })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.metrics).toEqual(mockMetrics);
      expect(response.body.data.healthScore).toBe(mockHealthScore);
      expect(response.body.data.realtime).toEqual(mockRealtimeMetrics);
    });

    it('应该支持详细信息查询', async () => {
      const mockPerformanceTrend = { trend: 'up' };
      const mockExecutorComparison = { comparison: 'data' };

      (metricsCollector.getMetrics as jest.Mock).mockReturnValue({});
      (metricsCollector.getHealthScore as jest.Mock).mockReturnValue(90);
      (metricsCollector.getRealTimeMetrics as jest.Mock).mockReturnValue({});
      (metricsCollector.getPerformanceTrend as jest.Mock).mockReturnValue(mockPerformanceTrend);
      (metricsCollector.getExecutorPerformanceComparison as jest.Mock).mockReturnValue(mockExecutorComparison);

      const response = await request(app)
        .get('/api/v1/workflow/metrics')
        .query({ includeDetails: true })
        .expect(200);

      expect(response.body.data.performanceTrend).toEqual(mockPerformanceTrend);
      expect(response.body.data.executorComparison).toEqual(mockExecutorComparison);
    });
  });

  describe('GET /api/v1/workflow/logs', () => {
    
    it('应该返回执行日志', async () => {
      const mockLogResult = {
        logs: [
          {
            level: 'INFO',
            message: '工作流开始执行',
            timestamp: new Date(),
            source: 'WorkflowCoordinator'
          }
        ],
        totalCount: 1,
        hasMore: false,
        aggregations: {
          byLevel: new Map([['INFO', 1]]),
          bySource: new Map([['WorkflowCoordinator', 1]])
        }
      };

      const mockLogStats = {
        totalLogs: 1,
        levelDistribution: new Map([['INFO', 1]]),
        topErrors: []
      };

      (logAggregator.queryLogs as jest.Mock).mockReturnValue(mockLogResult);
      (logAggregator.getLogStatistics as jest.Mock).mockReturnValue(mockLogStats);

      const response = await request(app)
        .get('/api/v1/workflow/logs')
        .query({ sessionId: 'session_001', level: 'INFO' })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.logs).toEqual(mockLogResult.logs);
      expect(response.body.data.pagination.total).toBe(1);
      expect(response.body.data.statistics.totalLogs).toBe(1);
    });
  });

  describe('GET /api/v1/workflow/logs/export', () => {
    
    it('应该导出执行日志', async () => {
      const mockExportResult = {
        data: '{"level":"INFO","message":"test log"}',
        contentType: 'application/json',
        filename: 'workflow_logs_20240101.json'
      };

      (logAggregator.exportLogs as jest.Mock).mockResolvedValue(mockExportResult);

      const response = await request(app)
        .get('/api/v1/workflow/logs/export')
        .query({ format: 'json', sessionId: 'session_001' });

      expect(response.headers['content-type']).toBe(mockExportResult.contentType);
      expect(response.headers['content-disposition']).toContain(mockExportResult.filename);
      expect(response.text).toBe(mockExportResult.data);
    });
  });

  describe('GET /api/v1/workflow/execution/:sessionId/tracking', () => {
    
    it('应该返回执行跟踪信息', async () => {
      const sessionId = 'wf_session_12345_abc';
      const mockSession = {
        sessionId,
        executionId: 'exec_12345',
        workflowId: 'workflow_001',
        status: 'RUNNING',
        startTime: new Date(),
        endTime: null,
        currentStep: 2,
        totalSteps: 3,
        errors: []
      };

      const mockStepState = {
        status: 'COMPLETED',
        startTime: new Date(),
        endTime: new Date(),
        duration: 1000,
        retryCount: 0,
        error: null,
        logs: [{ level: 'INFO', message: '步骤执行成功' }]
      };

      (workflowCoordinator.getExecutionSession as jest.Mock).mockReturnValue(mockSession);
      (executionStateTracker.getExecutionState as jest.Mock).mockResolvedValue(mockStepState);

      const response = await request(app)
        .get(`/api/v1/workflow/execution/${sessionId}/tracking`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.sessionId).toBe(sessionId);
      expect(response.body.data.steps).toHaveLength(2); // currentStep = 2
      expect(response.body.data.summary.totalSteps).toBe(3);
    });

    it('应该处理会话不存在的情况', async () => {
      const sessionId = 'non-existent-session';
      (workflowCoordinator.getExecutionSession as jest.Mock).mockReturnValue(null);

      const response = await request(app)
        .get(`/api/v1/workflow/execution/${sessionId}/tracking`)
        .expect(404);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe('执行会话不存在');
    });
  });

  describe('错误处理', () => {
    
    it('应该处理无效的会话ID参数', async () => {
      const response = await request(app)
        .get('/api/v1/workflow/execution/')
        .expect(404); // Express路由不匹配
    });

    it('应该处理服务异常', async () => {
      (workflowCoordinator.getCoordinatorStatus as jest.Mock).mockImplementation(() => {
        throw new Error('服务内部错误');
      });

      const response = await request(app)
        .get('/api/v1/workflow/coordinator/status')
        .expect(500);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe('获取协调器状态失败');
    });
  });

  describe('认证和授权', () => {
    
    it('应该在没有认证的情况下拒绝访问', async () => {
      // 临时禁用认证mock来测试未认证场景
      jest.doMock('@/middleware/auth.middleware', () => ({
        authMiddleware: (req: any, res: any, next: any) => {
          res.status(401).json({ success: false, message: '未认证' });
        }
      }));

      // 这里需要重新导入路由来应用新的mock
      // 在实际测试中可能需要更复杂的设置
    });
  });
});

describe('工作流执行器API集成测试', () => {
  
  it('应该完成完整的工作流执行生命周期', async () => {
    // 这是一个端到端的集成测试示例
    const mockSessionId = 'integration_test_session';
    
    // 1. 启动工作流
    (workflowCoordinator.startWorkflowExecution as jest.Mock).mockResolvedValue(mockSessionId);
    (workflowCoordinator.getExecutionSession as jest.Mock).mockReturnValue({
      sessionId: mockSessionId,
      status: 'STARTING',
      totalSteps: 3,
      currentStep: 0
    });

    const startResponse = await request(app)
      .post('/api/v1/workflow/execute')
      .send({
        workflowId: '550e8400-e29b-41d4-a716-************',
        context: { test: true }
      })
      .expect(200);

    expect(startResponse.body.data.sessionId).toBe(mockSessionId);

    // 2. 检查状态
    (workflowCoordinator.getExecutionSession as jest.Mock).mockReturnValue({
      sessionId: mockSessionId,
      status: 'RUNNING',
      totalSteps: 3,
      currentStep: 1
    });

    const statusResponse = await request(app)
      .get(`/api/v1/workflow/execution/${mockSessionId}`)
      .expect(200);

    expect(statusResponse.body.data.status).toBe('RUNNING');

    // 3. 暂停执行
    (workflowCoordinator.pauseWorkflowExecution as jest.Mock).mockResolvedValue(undefined);

    await request(app)
      .post('/api/v1/workflow/execution/pause')
      .send({ sessionId: mockSessionId })
      .expect(200);

    // 4. 恢复执行
    (workflowCoordinator.resumeWorkflowExecution as jest.Mock).mockResolvedValue(undefined);

    await request(app)
      .post('/api/v1/workflow/execution/resume')
      .send({ sessionId: mockSessionId })
      .expect(200);

    // 5. 最终停止
    (workflowCoordinator.stopWorkflowExecution as jest.Mock).mockResolvedValue(undefined);

    await request(app)
      .post('/api/v1/workflow/execution/stop')
      .send({ sessionId: mockSessionId })
      .expect(200);
  });
});

// 性能测试示例
describe('工作流执行器API性能测试', () => {
  
  it('应该在并发请求下保持响应性能', async () => {
    const concurrentRequests = 10;
    const promises = [];

    (workflowCoordinator.startWorkflowExecution as jest.Mock).mockImplementation(
      () => Promise.resolve(`session_${Math.random()}`)
    );
    (workflowCoordinator.getExecutionSession as jest.Mock).mockReturnValue({
      sessionId: 'test',
      status: 'RUNNING'
    });

    for (let i = 0; i < concurrentRequests; i++) {
      const promise = request(app)
        .post('/api/v1/workflow/execute')
        .send({
          workflowId: '550e8400-e29b-41d4-a716-************',
          context: { iteration: i }
        });
      promises.push(promise);
    }

    const startTime = Date.now();
    const responses = await Promise.all(promises);
    const duration = Date.now() - startTime;

    // 验证所有请求都成功
    responses.forEach(response => {
      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
    });

    // 验证响应时间合理（例如小于5秒）
    expect(duration).toBeLessThan(5000);
    
    console.log(`并发${concurrentRequests}个请求用时: ${duration}ms`);
  });
});