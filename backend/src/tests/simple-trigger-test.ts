import { triggerManager } from '../services/trigger-manager.service'
import { eventTrigger } from '../services/triggers/event-trigger.service'
import { cronTrigger } from '../services/triggers/cron-trigger.service'
import { conditionalTrigger } from '../services/triggers/conditional-trigger.service'
import { webhookTrigger } from '../services/triggers/webhook-trigger.service'

/**
 * 触发器系统基础功能测试
 */
async function runBasicTests() {
  console.log('🧪 开始触发器系统基础测试...\n')

  let testsPassed = 0
  let testsTotal = 0

  try {
    // 等待服务初始化
    console.log('⏳ 等待服务初始化...')
    await new Promise(resolve => setTimeout(resolve, 2000))

    // 测试1: 获取系统统计
    testsTotal++
    console.log('1. 📊 测试获取系统统计...')
    try {
      const stats = await triggerManager.getTriggerServiceStats()
      if (stats && stats.eventTrigger && stats.cronTrigger && stats.conditionalTrigger && stats.webhookTrigger) {
        console.log('   ✅ 系统统计获取成功')
        console.log(`   - 事件触发器支持 ${stats.eventTrigger.supportedEventTypes.length} 种事件类型`)
        console.log(`   - 定时触发器当前有 ${stats.cronTrigger.statistics.totalJobs} 个任务`)
        console.log(`   - 条件触发器当前有 ${stats.conditionalTrigger.allTriggers.length} 个触发器`)
        console.log(`   - Webhook触发器当前有 ${stats.webhookTrigger.allWebhooks.length} 个端点`)
        testsPassed++
      } else {
        console.log('   ❌ 系统统计数据不完整')
      }
    } catch (error) {
      console.log(`   ❌ 系统统计获取失败: ${error}`)
    }
    console.log()

    // 测试2: 事件触发
    testsTotal++
    console.log('2. 📡 测试事件触发...')
    try {
      await triggerManager.triggerEvent('TEST_INTEGRATION', {
        message: 'Basic integration test',
        timestamp: new Date(),
        testId: 'basic-001'
      }, 'basic-test')
      
      const eventStats = eventTrigger.getEventStatistics()
      console.log('   ✅ 事件触发成功')
      console.log(`   - 当前事件统计: ${JSON.stringify(eventStats)}`)
      testsPassed++
    } catch (error) {
      console.log(`   ❌ 事件触发失败: ${error}`)
    }
    console.log()

    // 测试3: 定时任务创建和手动执行
    testsTotal++
    console.log('3. ⏰ 测试定时任务...')
    try {
      const testJobId = 'basic-test-job'
      
      await cronTrigger.scheduleJob(testJobId, {
        schedule: '0 0 1 1 *', // 年度执行，不会自动触发
        enabled: false,
        description: '基础测试任务',
        handler: async () => {
          return { 
            success: true, 
            message: '基础测试任务执行成功',
            timestamp: new Date() 
          }
        }
      })

      // 手动执行
      const execution = await cronTrigger.runJobNow(testJobId)
      
      if (execution.status === 'SUCCESS') {
        console.log('   ✅ 定时任务测试成功')
        console.log(`   - 执行状态: ${execution.status}`)
        console.log(`   - 执行时长: ${execution.duration}ms`)
        testsPassed++
      } else {
        console.log(`   ❌ 定时任务执行失败: ${execution.status}`)
      }

      // 清理
      await cronTrigger.unscheduleJob(testJobId)
    } catch (error) {
      console.log(`   ❌ 定时任务测试失败: ${error}`)
    }
    console.log()

    // 测试4: 条件触发器
    testsTotal++
    console.log('4. 🔍 测试条件触发器...')
    try {
      const testTriggerId = 'basic-test-condition'
      
      await conditionalTrigger.createConditionalTrigger(testTriggerId, {
        conditions: [
          {
            field: 'cpu.usage',
            operator: 'exists',
            value: null,
            source: 'metric',
            description: '测试CPU存在性条件'
          }
        ],
        operator: 'AND',
        checkInterval: 300,
        enabled: false,
        description: '基础测试条件触发器'
      })

      const evaluation = await conditionalTrigger.forceEvaluation(testTriggerId)
      
      if (evaluation && evaluation.status === 'COMPLETED') {
        console.log('   ✅ 条件触发器测试成功')
        console.log(`   - 评估状态: ${evaluation.status}`)
        console.log(`   - 条件结果: ${evaluation.results.length > 0 ? evaluation.results[0]?.result : '无结果'}`)
        testsPassed++
      } else {
        console.log(`   ❌ 条件触发器评估失败: ${evaluation?.status || '无响应'}`)
      }

      // 清理
      await conditionalTrigger.removeTrigger(testTriggerId)
    } catch (error) {
      console.log(`   ❌ 条件触发器测试失败: ${error}`)
    }
    console.log()

    // 测试5: Webhook触发器
    testsTotal++
    console.log('5. 🔗 测试Webhook触发器...')
    try {
      const testWebhookId = 'basic-test-webhook'
      
      await webhookTrigger.registerWebhook(testWebhookId, {
        endpoint: '/webhooks/basic-test',
        method: 'POST',
        enabled: true,
        description: '基础测试Webhook端点',
        rateLimit: 1000
      })

      // 模拟Webhook请求
      const response = await webhookTrigger.handleWebhookRequest(
        '/webhooks/basic-test',
        'POST',
        { 'content-type': 'application/json' },
        { test: true, message: 'basic test webhook' },
        '127.0.0.1'
      )

      if (response.success) {
        console.log('   ✅ Webhook触发器测试成功')
        console.log(`   - 响应状态: ${response.success ? '成功' : '失败'}`)
        console.log(`   - 响应消息: ${response.message}`)
        testsPassed++
      } else {
        console.log(`   ❌ Webhook请求处理失败: ${response.message}`)
      }

      // 清理
      await webhookTrigger.removeWebhook(testWebhookId)
    } catch (error) {
      console.log(`   ❌ Webhook触发器测试失败: ${error}`)
    }
    console.log()

    // 测试结果汇总
    console.log('🏁 测试结果汇总:')
    console.log(`   总测试数: ${testsTotal}`)
    console.log(`   通过测试: ${testsPassed}`)
    console.log(`   失败测试: ${testsTotal - testsPassed}`)
    console.log(`   通过率: ${((testsPassed / testsTotal) * 100).toFixed(1)}%`)

    if (testsPassed === testsTotal) {
      console.log('\n🎉 所有基础测试都通过了！触发器系统集成成功！')
      return true
    } else {
      console.log('\n⚠️ 部分测试失败，请检查系统配置')
      return false
    }

  } catch (error) {
    console.error('\n❌ 测试过程中发生错误:', error)
    return false
  }
}

// 运行测试
if (require.main === module) {
  runBasicTests()
    .then((success) => {
      process.exit(success ? 0 : 1)
    })
    .catch((error) => {
      console.error('测试执行失败:', error)
      process.exit(1)
    })
}

export { runBasicTests }