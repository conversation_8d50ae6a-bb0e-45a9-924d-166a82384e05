import { beforeAll, afterAll } from '@jest/globals'

// 设置测试超时时间
jest.setTimeout(30000)

// 全局测试设置
beforeAll(async () => {
  console.log('🧪 启动测试环境...')
  
  // 设置测试环境变量
  process.env.NODE_ENV = 'test'
  process.env.DATABASE_URL = process.env.TEST_DATABASE_URL || process.env.DATABASE_URL
  process.env.REDIS_URL = process.env.TEST_REDIS_URL || process.env.REDIS_URL
  
  // 禁用不必要的日志
  console.log = jest.fn()
  console.error = jest.fn()
  console.warn = jest.fn()
})

// 全局测试清理
afterAll(async () => {
  console.log('🧹 清理测试环境...')
  
  // 恢复console
  jest.restoreAllMocks()
  
  // 等待异步操作完成
  await new Promise(resolve => setTimeout(resolve, 1000))
})