/**
 * ActionExecutor系统单元测试
 */

import { v4 as uuidv4 } from 'uuid';
import { WorkflowStepType } from '@prisma/client';
import { HttpActionExecutor } from '../services/actions/http-action-executor';
import { DatabaseActionExecutor } from '../services/actions/database-action-executor';
import { NotificationActionExecutor } from '../services/actions/notification-action-executor';
import { BusinessActionExecutor } from '../services/actions/business-action-executor';
import { ConditionalActionExecutor } from '../services/actions/conditional-action-executor';
import { ActionExecutorManager } from '../services/action-executor-manager.service';
import { ExecutionContextManager } from '../services/execution-context-manager.service';
import type { WorkflowStep, ExecutionContext } from '../types/action-executor.types';

describe('ActionExecutor系统测试', () => {
  let executorManager: ActionExecutorManager;
  let contextManager: ExecutionContextManager;
  let testContext: ExecutionContext;

  beforeAll(async () => {
    // 初始化管理器
    executorManager = ActionExecutorManager.getInstance();
    contextManager = ExecutionContextManager.getInstance();

    // 注册执行器
    executorManager.registerExecutor(new HttpActionExecutor());
    executorManager.registerExecutor(new DatabaseActionExecutor());
    executorManager.registerExecutor(new NotificationActionExecutor());
    executorManager.registerExecutor(new BusinessActionExecutor());
    executorManager.registerExecutor(new ConditionalActionExecutor());

    // 初始化执行器
    await executorManager.initialize();
  });

  beforeEach(async () => {
    // 为每个测试创建新的执行上下文
    const executionId = `test_${uuidv4()}`;
    const workflowId = `workflow_${uuidv4()}`;
    
    testContext = await contextManager.createContext(
      executionId,
      workflowId,
      1,
      { testMode: true }
    );
  });

  afterEach(async () => {
    // 清理测试上下文
    await contextManager.cleanupContext(testContext.executionId);
  });

  afterAll(async () => {
    // 清理资源
    await executorManager.cleanup();
    await contextManager.cleanup();
  });

  describe('HTTP执行器测试', () => {
    test('应该成功执行GET请求', async () => {
      const step: WorkflowStep = {
        index: 1,
        name: '测试GET请求',
        type: WorkflowStepType.HTTP_REQUEST,
        config: {
          method: 'GET',
          url: 'https://httpbin.org/get',
          timeout: 10000
        }
      };

      const result = await executorManager.executeStep(step, testContext);
      
      expect(result.success).toBe(true);
      expect(result.data?.statusCode).toBe(200);
      expect(result.executionTime).toBeDefined();
    });

    test('应该正确处理HTTP错误', async () => {
      const step: WorkflowStep = {
        index: 1,
        name: '测试HTTP错误',
        type: WorkflowStepType.HTTP_REQUEST,
        config: {
          method: 'GET',
          url: 'https://httpbin.org/status/404',
          timeout: 5000
        }
      };

      const result = await executorManager.executeStep(step, testContext);
      
      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
      expect(result.error?.code).toContain('HTTP');
    });

    test('应该正确验证HTTP配置', async () => {
      const step: WorkflowStep = {
        index: 1,
        name: '测试配置验证',
        type: WorkflowStepType.HTTP_REQUEST,
        config: {
          method: 'INVALID',
          url: 'not-a-url'
        }
      };

      const validation = await executorManager.validateStep(step);
      
      expect(validation.valid).toBe(false);
      expect(validation.errors.length).toBeGreaterThan(0);
    });
  });

  describe('数据库执行器测试', () => {
    test('应该正确验证SQL查询配置', async () => {
      const step: WorkflowStep = {
        index: 1,
        name: '测试SQL查询',
        type: WorkflowStepType.DATABASE_QUERY,
        config: {
          operation: 'SELECT',
          query: 'SELECT 1 as test',
          timeout: 5000
        }
      };

      const validation = await executorManager.validateStep(step);
      
      expect(validation.valid).toBe(true);
      expect(validation.errors.length).toBe(0);
    });

    test('应该检测SQL注入风险', async () => {
      const step: WorkflowStep = {
        index: 1,
        name: '测试SQL注入检测',
        type: WorkflowStepType.DATABASE_QUERY,
        config: {
          operation: 'SELECT',
          query: 'SELECT * FROM users; DROP TABLE users;',
          timeout: 5000
        }
      };

      const validation = await executorManager.validateStep(step);
      
      expect(validation.valid).toBe(false);
      expect(validation.errors.some(e => e.includes('SQL注入'))).toBe(true);
    });
  });

  describe('通知执行器测试', () => {
    test('应该正确验证通知配置', async () => {
      const step: WorkflowStep = {
        index: 1,
        name: '测试通知配置',
        type: WorkflowStepType.NOTIFICATION,
        config: {
          type: 'email',
          message: 'Test notification message',
          recipients: ['<EMAIL>'],
          subject: 'Test Subject'
        }
      };

      const validation = await executorManager.validateStep(step);
      
      expect(validation.valid).toBe(true);
      expect(validation.errors.length).toBe(0);
    });

    test('应该正确执行通知发送（模拟）', async () => {
      const step: WorkflowStep = {
        index: 1,
        name: '测试通知发送',
        type: WorkflowStepType.NOTIFICATION,
        config: {
          type: 'email',
          message: 'Test notification',
          recipients: ['<EMAIL>']
        }
      };

      const result = await executorManager.executeStep(step, testContext);
      
      expect(result.success).toBe(true);
      expect(result.data?.type).toBe('email');
      expect(result.data?.successCount).toBe(1);
    });
  });

  describe('业务逻辑执行器测试', () => {
    test('应该正确验证业务动作配置', async () => {
      const step: WorkflowStep = {
        index: 1,
        name: '测试业务动作',
        type: WorkflowStepType.BUSINESS_LOGIC,
        config: {
          action: 'create_service_ticket',
          parameters: {
            title: 'Test Ticket',
            customerId: 'test-customer'
          }
        }
      };

      const validation = await executorManager.validateStep(step);
      
      expect(validation.valid).toBe(true);
      expect(validation.errors.length).toBe(0);
    });

    test('应该检测不支持的业务动作', async () => {
      const step: WorkflowStep = {
        index: 1,
        name: '测试不支持的动作',
        type: WorkflowStepType.BUSINESS_LOGIC,
        config: {
          action: 'invalid_action'
        }
      };

      const validation = await executorManager.validateStep(step);
      
      expect(validation.valid).toBe(false);
      expect(validation.errors.some(e => e.includes('不支持的业务动作'))).toBe(true);
    });
  });

  describe('条件执行器测试', () => {
    test('应该正确执行简单条件判断', async () => {
      const step: WorkflowStep = {
        index: 1,
        name: '测试条件判断',
        type: WorkflowStepType.CONDITIONAL,
        config: {
          condition: {
            operator: 'equals',
            left: '${testValue}',
            right: 'expected'
          },
          trueAction: {
            type: 'continue',
            message: '条件为真'
          },
          falseAction: {
            type: 'continue',
            message: '条件为假'
          }
        }
      };

      // 更新测试上下文变量
      testContext.variables.testValue = 'expected';

      const result = await executorManager.executeStep(step, testContext);
      
      expect(result.success).toBe(true);
      expect(result.data?.conditionResult).toBe(true);
      expect(result.data?.branchExecuted).toBe('true');
    });

    test('应该正确执行复合条件判断', async () => {
      const step: WorkflowStep = {
        index: 1,
        name: '测试复合条件',
        type: WorkflowStepType.CONDITIONAL,
        config: {
          condition: {
            operator: 'and',
            conditions: [
              {
                operator: 'exists',
                left: '${userId}'
              },
              {
                operator: 'equals',
                left: '${userType}',
                right: 'admin'
              }
            ]
          },
          trueAction: {
            type: 'continue',
            message: '用户是管理员且存在'
          },
          falseAction: {
            type: 'skip',
            message: '用户不是管理员或不存在'
          }
        }
      };

      // 设置测试变量
      testContext.variables.userId = 'user123';
      testContext.variables.userType = 'admin';

      const result = await executorManager.executeStep(step, testContext);
      
      expect(result.success).toBe(true);
      expect(result.data?.conditionResult).toBe(true);
      expect(result.data?.branchResult.action).toBe('continue');
    });
  });

  describe('执行器管理器测试', () => {
    test('应该列出所有注册的执行器', () => {
      const executors = executorManager.listExecutors();
      
      expect(executors.length).toBe(5);
      expect(executors.map(e => e.type)).toContain(WorkflowStepType.HTTP_REQUEST);
      expect(executors.map(e => e.type)).toContain(WorkflowStepType.DATABASE_QUERY);
      expect(executors.map(e => e.type)).toContain(WorkflowStepType.NOTIFICATION);
      expect(executors.map(e => e.type)).toContain(WorkflowStepType.BUSINESS_LOGIC);
      expect(executors.map(e => e.type)).toContain(WorkflowStepType.CONDITIONAL);
    });

    test('应该执行健康检查', async () => {
      const healthStatus = await executorManager.healthCheck();
      
      expect(healthStatus.size).toBe(5);
      
      // 所有执行器应该健康
      for (const [type, isHealthy] of healthStatus) {
        expect(isHealthy).toBe(true);
      }
    });

    test('应该获取系统统计信息', () => {
      const stats = executorManager.getSystemStats();
      
      expect(stats.totalExecutors).toBe(5);
      expect(stats.isInitialized).toBe(true);
      expect(stats.executors).toHaveLength(5);
      expect(stats.metrics).toBeDefined();
    });
  });

  describe('错误处理测试', () => {
    test('应该处理不存在的执行器类型', async () => {
      const step: WorkflowStep = {
        index: 1,
        name: '测试不存在的执行器',
        type: 'INVALID_TYPE' as WorkflowStepType,
        config: {}
      };

      const result = await executorManager.executeStep(step, testContext);
      
      expect(result.success).toBe(false);
      expect(result.error?.code).toBe('EXECUTOR_NOT_FOUND');
    });

    test('应该处理执行超时', async () => {
      const step: WorkflowStep = {
        index: 1,
        name: '测试超时',
        type: WorkflowStepType.HTTP_REQUEST,
        config: {
          method: 'GET',
          url: 'https://httpbin.org/delay/5',
          timeout: 1000 // 1秒超时
        }
      };

      const result = await executorManager.executeStep(step, testContext);
      
      expect(result.success).toBe(false);
      expect(result.error?.type).toBe('TIMEOUT_ERROR');
    }, 10000);
  });

  describe('变量解析测试', () => {
    test('应该正确解析上下文变量', async () => {
      const step: WorkflowStep = {
        index: 1,
        name: '测试变量解析',
        type: WorkflowStepType.CONDITIONAL,
        config: {
          condition: {
            operator: 'equals',
            left: '${customerLevel}',
            right: 'VIP'
          },
          trueAction: {
            type: 'continue',
            message: '客户等级是VIP'
          },
          falseAction: {
            type: 'continue',
            message: '客户等级不是VIP'
          }
        }
      };

      // 设置变量
      testContext.variables.customerLevel = 'VIP';

      const result = await executorManager.executeStep(step, testContext);
      
      expect(result.success).toBe(true);
      expect(result.data?.conditionResult).toBe(true);
    });
  });
});

// Jest配置
export default {
  testEnvironment: 'node',
  roots: ['<rootDir>/src'],
  testMatch: ['**/__tests__/**/*.ts', '**/?(*.)+(spec|test).ts'],
  transform: {
    '^.+\\.ts$': 'ts-jest'
  },
  collectCoverageFrom: [
    'src/**/*.ts',
    '!src/**/*.d.ts',
    '!src/tests/**',
    '!src/scripts/**'
  ],
  coverageDirectory: 'coverage',
  coverageReporters: ['text', 'lcov', 'html']
};