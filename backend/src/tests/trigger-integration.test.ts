import { triggerManager } from '@/services/trigger-manager.service'
import { eventTrigger } from '@/services/triggers/event-trigger.service'
import { cronTrigger } from '@/services/triggers/cron-trigger.service'
import { conditionalTrigger } from '@/services/triggers/conditional-trigger.service'
import { webhookTrigger } from '@/services/triggers/webhook-trigger.service'

/**
 * 简化的触发器系统集成测试
 */
async function runTriggerIntegrationTest() {
  console.log('🧪 开始触发器系统集成测试...\n')

  try {
    // 等待服务初始化
    console.log('⏳ 等待服务初始化...')
    await new Promise(resolve => setTimeout(resolve, 3000))

    // 1. 测试系统统计
    console.log('📊 测试系统统计...')
    const stats = await triggerManager.getTriggerServiceStats()
    console.log(`✅ 系统统计获取成功:`)
    console.log(`  - 事件触发器: ${stats.eventTrigger.supportedEventTypes.length} 种事件类型`)
    console.log(`  - 定时触发器: ${stats.cronTrigger.statistics.totalJobs} 个任务`)
    console.log(`  - 条件触发器: ${stats.conditionalTrigger.allTriggers.length} 个触发器`)
    console.log(`  - Webhook触发器: ${stats.webhookTrigger.allWebhooks.length} 个端点\n`)

    // 2. 测试事件触发
    console.log('📡 测试事件触发器...')
    await triggerManager.triggerEvent('TEST_EVENT', {
      message: 'Integration test event',
      timestamp: new Date()
    }, 'integration-test')
    
    const eventStats = eventTrigger.getEventStatistics()
    console.log(`✅ 事件触发成功, 当前事件总数: ${eventStats['totalEvents'] || 0}\n`)

    // 3. 测试定时任务
    console.log('⏰ 测试定时触发器...')
    await cronTrigger.scheduleJob('integration-test-job', {
      schedule: '0 0 1 1 *', // 年度执行（不会真正执行）
      enabled: false,
      description: '集成测试任务',
      handler: async () => {
        return { test: 'success', timestamp: new Date() }
      }
    })

    // 手动执行测试任务
    const execution = await cronTrigger.runJobNow('integration-test-job')
    console.log(`✅ 定时任务测试成功: 状态=${execution.status}, 耗时=${execution.duration}ms\n`)

    // 4. 测试条件触发器
    console.log('🔍 测试条件触发器...')
    await conditionalTrigger.createConditionalTrigger('integration-test-condition', {
      conditions: [
        {
          field: 'test_value',
          operator: 'exists',
          value: null,
          source: 'metric',
          description: '测试条件'
        }
      ],
      operator: 'AND',
      checkInterval: 60,
      enabled: false,
      description: '集成测试条件触发器'
    })

    const conditionEvaluation = await conditionalTrigger.forceEvaluation('integration-test-condition')
    console.log(`✅ 条件触发器测试成功: 评估状态=${conditionEvaluation?.status}\n`)

    // 5. 测试Webhook触发器
    console.log('🔗 测试Webhook触发器...')
    await webhookTrigger.registerWebhook('integration-test-webhook', {
      endpoint: '/webhooks/integration-test',
      method: 'POST',
      enabled: true,
      description: '集成测试Webhook',
      rateLimit: 1000
    })

    // 测试Webhook请求处理
    const webhookResponse = await webhookTrigger.handleWebhookRequest(
      '/webhooks/integration-test',
      'POST',
      { 'content-type': 'application/json' },
      { test: true, message: 'integration test' },
      '127.0.0.1'
    )
    console.log(`✅ Webhook触发器测试成功: 状态=${webhookResponse.success ? '成功' : '失败'}\n`)

    // 6. 清理测试资源
    console.log('🧹 清理测试资源...')
    await cronTrigger.unscheduleJob('integration-test-job')
    await conditionalTrigger.removeTrigger('integration-test-condition')
    await webhookTrigger.removeWebhook('integration-test-webhook')
    console.log('✅ 测试资源清理完成\n')

    console.log('🎉 触发器系统集成测试全部通过！')
    return true

  } catch (error) {
    console.error('❌ 触发器系统集成测试失败:', error)
    return false
  }
}

// 运行测试
if (require.main === module) {
  runTriggerIntegrationTest()
    .then((success) => {
      process.exit(success ? 0 : 1)
    })
    .catch((error) => {
      console.error('测试运行失败:', error)
      process.exit(1)
    })
}

export { runTriggerIntegrationTest }