import { triggerManager } from '@/services/trigger-manager.service'
import { eventTrigger } from '@/services/triggers/event-trigger.service'
import { cronTrigger } from '@/services/triggers/cron-trigger.service'
import { conditionalTrigger } from '@/services/triggers/conditional-trigger.service'
import { webhookTrigger } from '@/services/triggers/webhook-trigger.service'
import { workflowEngine } from '@/services/workflow-engine.service'

/**
 * 触发器系统集成测试
 * 测试所有触发器类型的协调工作和系统集成
 */
describe('Trigger System Integration Tests', () => {
  
  beforeAll(async () => {
    // 确保所有服务都已初始化
    console.log('🧪 初始化触发器系统测试环境...')
  })

  afterAll(async () => {
    // 清理测试环境
    await triggerManager.stopAll()
    console.log('🧹 触发器系统测试环境清理完成')
  })

  describe('TriggerManager Integration', () => {
    
    test('should get comprehensive trigger service stats', async () => {
      const stats = await triggerManager.getTriggerServiceStats()
      
      expect(stats).toHaveProperty('eventTrigger')
      expect(stats).toHaveProperty('cronTrigger') 
      expect(stats).toHaveProperty('conditionalTrigger')
      expect(stats).toHaveProperty('webhookTrigger')
      
      expect(stats.eventTrigger.supportedEventTypes).toBeInstanceOf(Array)
      expect(stats.cronTrigger.statistics).toBeDefined()
      expect(stats.conditionalTrigger.allTriggers).toBeInstanceOf(Array)
      expect(stats.webhookTrigger.allWebhooks).toBeInstanceOf(Array)
      
      console.log('📊 触发器系统统计信息:', JSON.stringify(stats, null, 2))
    })

    test('should handle cross-trigger coordination', async () => {
      // 测试触发器之间的协调工作
      const testEvent = {
        type: 'test.integration',
        data: { message: 'Cross-trigger test' },
        source: 'integration-test'
      }

      // 手动触发事件
      await triggerManager.triggerEvent(testEvent.type, testEvent.data, testEvent.source)
      
      // 验证事件被正确处理
      const eventStats = eventTrigger.getEventStatistics()
      expect(eventStats.totalEvents).toBeGreaterThan(0)
    })

  })

  describe('Event Trigger Integration', () => {
    
    test('should handle system events correctly', async () => {
      const supportedEvents = eventTrigger.getSupportedEventTypes()
      expect(supportedEvents).toContain('SERVICE_CREATED')
      expect(supportedEvents).toContain('USER_LOGIN')
      expect(supportedEvents).toContain('SLA_VIOLATION')
      
      // 测试事件触发
      await eventTrigger.triggerEvent('SERVICE_CREATED', {
        serviceId: 'test-service-001',
        customerId: 'customer-001',
        priority: 'HIGH'
      }, 'integration-test')

      const recentEvents = eventTrigger.getRecentEvents('SERVICE_CREATED', 5)
      expect(recentEvents.length).toBeGreaterThan(0)
    })

    test('should process event filters correctly', async () => {
      // 注册带过滤器的事件监听器
      eventTrigger.registerEventFilter('high-priority-filter', (data) => {
        return data.priority === 'HIGH'
      })

      await eventTrigger.triggerEvent('SERVICE_UPDATED', {
        serviceId: 'test-service-002', 
        priority: 'LOW'
      }, 'filter-test')

      await eventTrigger.triggerEvent('SERVICE_UPDATED', {
        serviceId: 'test-service-003',
        priority: 'HIGH' 
      }, 'filter-test')

      // 验证过滤器工作正常
      const stats = eventTrigger.getEventStatistics()
      expect(stats.filteredEvents).toBeGreaterThanOrEqual(0)
    })

  })

  describe('Cron Trigger Integration', () => {
    
    test('should validate cron expressions correctly', async () => {
      const validExpressions = [
        '* * * * *',        // 每分钟
        '0 * * * *',        // 每小时
        '0 9 * * *',        // 每天9点
        '0 9 * * 1-5',      // 工作日9点
        '*/5 * * * *'       // 每5分钟
      ]

      const invalidExpressions = [
        '60 * * * *',       // 无效分钟
        '* 25 * * *',       // 无效小时
        'invalid',          // 完全无效
        '* * * * * *'       // 太多字段
      ]

      validExpressions.forEach(expr => {
        expect(cronTrigger.constructor.validateCronExpression(expr)).toBe(true)
      })

      invalidExpressions.forEach(expr => {
        expect(cronTrigger.constructor.validateCronExpression(expr)).toBe(false)
      })
    })

    test('should parse cron expressions to human readable format', async () => {
      const testCases = [
        { cron: '* * * * *', expected: '每分钟' },
        { cron: '0 * * * *', expected: '每小时' },
        { cron: '0 0 * * *', expected: '每天' },
        { cron: '0 0 * * 0', expected: '每周日' }
      ]

      testCases.forEach(({ cron, expected }) => {
        const parsed = cronTrigger.constructor.parseCronExpression(cron)
        expect(parsed).toBe(expected)
      })
    })

    test('should schedule and execute jobs correctly', async () => {
      const testJobId = 'integration-test-job'
      
      // 创建测试任务
      await cronTrigger.scheduleJob(testJobId, {
        schedule: '*/1 * * * *', // 每分钟（测试用）
        description: '集成测试任务',
        enabled: false, // 先不启用
        handler: async () => {
          console.log('🧪 测试任务执行')
          return { success: true, timestamp: new Date() }
        }
      })

      // 验证任务已创建
      const jobInfo = cronTrigger.getJobInfo(testJobId)
      expect(jobInfo).toBeDefined()
      expect(jobInfo!.config.schedule).toBe('*/1 * * * *')

      // 手动执行任务
      const execution = await cronTrigger.runJobNow(testJobId)
      expect(execution.status).toBe('SUCCESS')
      expect(execution.result).toHaveProperty('success', true)

      // 清理
      await cronTrigger.unscheduleJob(testJobId)
    })

    test('should track job statistics correctly', async () => {
      const stats = cronTrigger.getJobStatistics()
      
      expect(stats).toHaveProperty('totalJobs')
      expect(stats).toHaveProperty('runningJobs')
      expect(stats).toHaveProperty('totalExecutions')
      expect(stats).toHaveProperty('overallSuccessRate')
      
      console.log('📈 定时任务统计:', stats)
    })

  })

  describe('Conditional Trigger Integration', () => {
    
    test('should create and evaluate conditional triggers', async () => {
      const testTriggerId = 'test-conditional-trigger'
      
      await conditionalTrigger.createConditionalTrigger(testTriggerId, {
        conditions: [
          {
            field: 'test_value',
            operator: 'gt',
            value: 50,
            source: 'variable',
            description: '测试变量大于50'
          }
        ],
        operator: 'AND',
        checkInterval: 10, // 10秒检查间隔（测试用）
        enabled: false, // 先不启用
        description: '测试条件触发器'
      })

      // 验证触发器已创建
      const triggerInfo = conditionalTrigger.getTriggerInfo(testTriggerId)
      expect(triggerInfo).toBeDefined()
      expect(triggerInfo!.trigger.config.conditions).toHaveLength(1)

      // 手动评估条件
      const evaluation = await conditionalTrigger.forceEvaluation(testTriggerId)
      expect(evaluation).toBeDefined()
      expect(evaluation!.status).toBe('COMPLETED')

      // 清理
      await conditionalTrigger.removeTrigger(testTriggerId)
    })

    test('should handle different data sources', async () => {
      const testTriggerId = 'data-source-test'
      
      await conditionalTrigger.createConditionalTrigger(testTriggerId, {
        conditions: [
          {
            field: 'cpu.usage',
            operator: 'lt',
            value: 100, // 应该总是满足
            source: 'metric',
            description: 'CPU使用率测试'
          }
        ],
        operator: 'AND',
        checkInterval: 5,
        enabled: false,
        description: '数据源测试'
      })

      // 强制评估
      const evaluation = await conditionalTrigger.forceEvaluation(testTriggerId)
      expect(evaluation?.results).toHaveLength(1)
      expect(evaluation?.results[0].actualValue).toBeDefined()

      // 清理
      await conditionalTrigger.removeTrigger(testTriggerId)
    })

    test('should handle complex condition logic', async () => {
      const testTriggerId = 'complex-logic-test'
      
      await conditionalTrigger.createConditionalTrigger(testTriggerId, {
        conditions: [
          {
            field: 'value1',
            operator: 'gt',
            value: 10,
            source: 'metric',
            description: '条件1'
          },
          {
            field: 'value2', 
            operator: 'lt',
            value: 100,
            source: 'metric',
            description: '条件2'
          }
        ],
        operator: 'AND', // 两个条件都必须满足
        checkInterval: 5,
        enabled: false,
        description: '复杂逻辑测试'
      })

      const evaluation = await conditionalTrigger.forceEvaluation(testTriggerId)
      expect(evaluation?.results).toHaveLength(2)
      
      // 测试OR逻辑
      await conditionalTrigger.removeTrigger(testTriggerId)
      
      await conditionalTrigger.createConditionalTrigger(testTriggerId + '-or', {
        conditions: [
          {
            field: 'impossible_value',
            operator: 'gt', 
            value: 9999999,
            source: 'metric',
            description: '不可能满足的条件'
          },
          {
            field: 'cpu.usage',
            operator: 'exists',
            value: null,
            source: 'metric', 
            description: '总是满足的条件'
          }
        ],
        operator: 'OR', // 只需一个满足
        checkInterval: 5,
        enabled: false,
        description: 'OR逻辑测试'
      })

      const orEvaluation = await conditionalTrigger.forceEvaluation(testTriggerId + '-or')
      expect(orEvaluation?.finalResult).toBe(true)

      // 清理
      await conditionalTrigger.removeTrigger(testTriggerId + '-or')
    })

  })

  describe('Webhook Trigger Integration', () => {
    
    test('should register and manage webhooks', async () => {
      const testWebhookId = 'test-integration-webhook'
      
      await webhookTrigger.registerWebhook(testWebhookId, {
        endpoint: '/webhooks/integration-test',
        method: 'POST',
        authentication: {
          type: 'token',
          token: 'test-secret-token'
        },
        enabled: true,
        description: '集成测试Webhook',
        rateLimit: 1000
      })

      // 验证Webhook已注册
      const webhookInfo = webhookTrigger.getWebhookInfo(testWebhookId)
      expect(webhookInfo).toBeDefined()
      expect(webhookInfo!.webhook.config.endpoint).toBe('/webhooks/integration-test')

      // 获取所有Webhook
      const allWebhooks = webhookTrigger.getAllWebhooks()
      const testWebhook = allWebhooks.find(w => w.webhook.id === testWebhookId)
      expect(testWebhook).toBeDefined()

      // 清理
      await webhookTrigger.removeWebhook(testWebhookId)
    })

    test('should handle webhook requests with authentication', async () => {
      const testWebhookId = 'auth-test-webhook'
      
      await webhookTrigger.registerWebhook(testWebhookId, {
        endpoint: '/webhooks/auth-test',
        method: 'POST',
        authentication: {
          type: 'token',
          token: 'secret-auth-token'
        },
        enabled: true,
        description: '认证测试Webhook'
      })

      // 测试正确认证
      const validResponse = await webhookTrigger.handleWebhookRequest(
        '/webhooks/auth-test',
        'POST',
        { 'x-webhook-secret': 'secret-auth-token' },
        { test: true },
        '127.0.0.1'
      )

      expect(validResponse.success).toBe(true)

      // 测试错误认证
      const invalidResponse = await webhookTrigger.handleWebhookRequest(
        '/webhooks/auth-test',
        'POST', 
        { 'x-webhook-secret': 'wrong-token' },
        { test: true },
        '127.0.0.1'
      )

      expect(invalidResponse.success).toBe(false)
      expect(invalidResponse.status).toBe(401)

      // 清理
      await webhookTrigger.removeWebhook(testWebhookId)
    })

    test('should apply request filters correctly', async () => {
      const testWebhookId = 'filter-test-webhook'
      
      await webhookTrigger.registerWebhook(testWebhookId, {
        endpoint: '/webhooks/filter-test',
        method: 'POST',
        filters: {
          headers: {
            'x-event-type': 'important'
          },
          body: {
            'priority': 'high'
          }
        },
        enabled: true,
        description: '过滤器测试Webhook'
      })

      // 测试通过过滤器
      const validResponse = await webhookTrigger.handleWebhookRequest(
        '/webhooks/filter-test',
        'POST',
        { 'x-event-type': 'important' },
        { priority: 'high', data: 'test' },
        '127.0.0.1'
      )

      expect(validResponse.success).toBe(true)

      // 测试被过滤器拦截
      const filteredResponse = await webhookTrigger.handleWebhookRequest(
        '/webhooks/filter-test', 
        'POST',
        { 'x-event-type': 'unimportant' },
        { priority: 'low' },
        '127.0.0.1'
      )

      expect(filteredResponse.success).toBe(true) // 仍然成功，但被过滤
      expect(filteredResponse.data?.filtered).toBe(true)

      // 清理
      await webhookTrigger.removeWebhook(testWebhookId)
    })

  })

  describe('Cross-Service Integration', () => {
    
    test('should integrate with workflow engine', async () => {
      // 测试触发器与工作流引擎的集成
      // 注意：这需要有实际的工作流定义
      console.log('🔄 测试工作流引擎集成...')
      
      // 这里我们主要测试触发器能够调用工作流引擎的接口
      // 实际的工作流执行需要数据库中有工作流定义
      
      const testEvent = {
        type: 'workflow.test',
        data: { workflowId: 'test-workflow', params: { test: true } }
      }

      await eventTrigger.triggerEvent(testEvent.type, testEvent.data, 'workflow-integration-test')
      
      // 验证事件已被记录
      const recentEvents = eventTrigger.getRecentEvents('workflow.test', 1)
      expect(recentEvents.length).toBeGreaterThan(0)
    })

    test('should handle error propagation correctly', async () => {
      // 测试错误在不同触发器服务之间的传播
      console.log('🚨 测试错误处理和传播...')

      // 创建一个会失败的定时任务
      const errorJobId = 'error-test-job'
      
      await cronTrigger.scheduleJob(errorJobId, {
        schedule: '0 0 1 1 *', // 年度执行（避免在测试中真正执行）
        enabled: false,
        handler: async () => {
          throw new Error('测试错误处理')
        }
      })

      // 手动执行并期望失败
      const execution = await cronTrigger.runJobNow(errorJobId)
      expect(execution.status).toBe('FAILED')
      expect(execution.error).toContain('测试错误处理')

      // 清理
      await cronTrigger.unscheduleJob(errorJobId)
    })

    test('should maintain performance under load', async () => {
      console.log('⚡ 测试系统负载性能...')
      
      // 测试大量事件触发的性能
      const startTime = Date.now()
      const eventCount = 100

      const promises = []
      for (let i = 0; i < eventCount; i++) {
        promises.push(
          eventTrigger.triggerEvent('PERFORMANCE_TEST', {
            id: i,
            timestamp: new Date()
          }, 'load-test')
        )
      }

      await Promise.all(promises)
      const endTime = Date.now()
      const duration = endTime - startTime

      console.log(`📊 处理${eventCount}个事件用时: ${duration}ms, 平均: ${duration/eventCount}ms/事件`)
      
      // 性能应该在合理范围内（每个事件处理时间不应超过10ms）
      expect(duration / eventCount).toBeLessThan(10)

      // 验证所有事件都被处理
      const stats = eventTrigger.getEventStatistics()
      expect(stats.totalEvents).toBeGreaterThanOrEqual(eventCount)
    })

  })

  describe('Data Persistence and Recovery', () => {
    
    test('should handle service restart gracefully', async () => {
      console.log('🔄 测试服务重启和恢复...')
      
      // 创建一些触发器状态
      await eventTrigger.triggerEvent('RESTART_TEST', { before: 'restart' }, 'restart-test')
      
      const beforeStats = eventTrigger.getEventStatistics()
      
      // 模拟重启（停止并重新初始化服务）
      // 注意：真实的重启测试需要更复杂的设置
      console.log('📊 重启前统计:', beforeStats)
      
      // 验证重启后数据完整性
      const afterStats = eventTrigger.getEventStatistics()
      console.log('📊 重启后统计:', afterStats)
      
      // 在真实环境中，这里应该验证持久化数据的恢复
      expect(afterStats).toBeDefined()
    })

  })

})

/**
 * 性能基准测试
 */
describe('Performance Benchmarks', () => {
  
  test('event trigger throughput', async () => {
    const iterations = 1000
    const startTime = Date.now()

    for (let i = 0; i < iterations; i++) {
      await eventTrigger.triggerEvent('BENCHMARK_EVENT', { iteration: i }, 'benchmark')
    }

    const endTime = Date.now()
    const throughput = iterations / (endTime - startTime) * 1000 // events per second

    console.log(`⚡ 事件触发器吞吐量: ${throughput.toFixed(2)} events/s`)
    expect(throughput).toBeGreaterThan(100) // 至少100 events/s
  })

  test('conditional trigger evaluation speed', async () => {
    const testTriggerId = 'benchmark-conditional'
    
    await conditionalTrigger.createConditionalTrigger(testTriggerId, {
      conditions: [
        {
          field: 'cpu.usage',
          operator: 'lt',
          value: 100,
          source: 'metric',
          description: 'CPU测试'
        }
      ],
      operator: 'AND',
      checkInterval: 1,
      enabled: false
    })

    const iterations = 100
    const startTime = Date.now()

    for (let i = 0; i < iterations; i++) {
      await conditionalTrigger.forceEvaluation(testTriggerId)
    }

    const endTime = Date.now()
    const avgTime = (endTime - startTime) / iterations

    console.log(`⚡ 条件评估平均时间: ${avgTime.toFixed(2)}ms`)
    expect(avgTime).toBeLessThan(50) // 平均评估时间应少于50ms

    await conditionalTrigger.removeTrigger(testTriggerId)
  })

})