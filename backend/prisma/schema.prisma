// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

// 用户表
model User {
  id        String   @id @default(cuid())
  username  String   @unique
  email     String   @unique
  password  String
  fullName  String?  @map("full_name")
  phone     String?
  avatar    String?
  department String?
  status    UserStatus @default(ACTIVE)
  lastLoginAt DateTime? @map("last_login_at")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // 关联
  userRoles        UserRole[]
  createdCustomers Customer[]         @relation("CustomerCreatedBy")
  createdArchives  ProjectArchive[]   @relation("ArchiveCreatedBy")
  assignedServices Service[]          @relation("ServiceAssignedTo")
  createdServices  Service[]          @relation("ServiceCreatedBy")
  workLogs         ServiceWorkLog[]
  uploads          ServiceAttachment[]
  comments         ServiceComment[]
  operationLogs    OperationLog[]
  serviceOperationHistory ServiceOperationHistory[]
  acknowledgedAlerts Alert[]          @relation("AlertAcknowledgedBy")
  resolvedAlerts   Alert[]            @relation("AlertResolvedBy")
  systemEvents     SystemEvent[]      @relation("SystemEventUser")
  systemConfigUpdates SystemConfig[]   @relation
  systemConfigHistory SystemConfigHistory[]
  createdPermissionTemplates PermissionTemplate[] @relation("PermissionTemplateCreatedBy")
  updatedPermissionTemplates PermissionTemplate[] @relation("PermissionTemplateUpdatedBy")
  permissionTemplateHistory PermissionTemplateHistory[] @relation("PermissionTemplateHistoryChangedBy")
  permissionTemplateUsage PermissionTemplateUsage[] @relation("PermissionTemplateUsageAppliedBy")
  
  // 用户活动分析关联
  userSessions UserSession[]
  userActivityStats UserActivityStats[]
  userBehaviorPatterns UserBehaviorPattern[]
  userActivityAnomalies UserActivityAnomaly[]
  resolvedAnomalies UserActivityAnomaly[] @relation("AnomalyResolvedBy")
  userFeatureUsage UserFeatureUsage[]
  
  // 外部API密钥关联 - MVP新增
  createdApiKeys ApiKey[] @relation("ApiKeyCreatedBy")
  
  // 邮件模板关联
  createdEmailTemplates EmailTemplate[] @relation("EmailTemplateCreatedBy")
  updatedEmailTemplates EmailTemplate[] @relation("EmailTemplateUpdatedBy")

  // AI服务关联
  aiAnalysisRequests AIAnalysisRequest[] @relation("AIAnalysisRequest")
  aiFeedback AIFeedback[] @relation("AIFeedback")
  createdPromptTemplates AIPromptTemplate[] @relation("AIPromptTemplateCreator")
  updatedPromptTemplates AIPromptTemplate[] @relation("AIPromptTemplateUpdater")

  // 工作流自动化关联
  createdWorkflowDefinitions WorkflowDefinition[] @relation("WorkflowDefinitionCreatedBy")
  updatedWorkflowDefinitions WorkflowDefinition[] @relation("WorkflowDefinitionUpdatedBy")
  triggeredWorkflowExecutions WorkflowExecution[] @relation("WorkflowExecutionTriggeredBy")
  createdWorkflowTemplates WorkflowTemplate[] @relation("WorkflowTemplateCreatedBy")
  updatedWorkflowTemplates WorkflowTemplate[] @relation("WorkflowTemplateUpdatedBy")

  @@map("users")
}

// 角色表
model Role {
  id          String   @id @default(cuid())
  name        String   @unique
  description String?
  permissions Json     // 存储权限数组
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  // 关联
  userRoles UserRole[]
  permissionTemplateUsage PermissionTemplateUsage[] @relation("PermissionTemplateUsageRole")

  @@map("roles")
}

// 用户角色关联表
model UserRole {
  userId String
  roleId String
  assignedAt DateTime @default(now()) @map("assigned_at")

  // 关联
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
  role Role @relation(fields: [roleId], references: [id], onDelete: Cascade)

  @@id([userId, roleId])
  @@map("user_roles")
}

// 客户表
model Customer {
  id            String        @id @default(cuid())
  name          String
  code          String        @unique // 客户编码
  company       String?
  industry      String?
  type          CustomerType  @default(ENTERPRISE) // 客户类型
  level         CustomerLevel @default(STANDARD)   // 客户等级
  contactPerson String?       @map("contact_person")
  contactPhone  String?       @map("contact_phone") // 联系电话
  contactEmail  String?       @map("contact_email") // 联系邮箱
  address       String?
  description   String?       @db.Text
  isVip         Boolean       @default(false) @map("is_vip") // 是否VIP
  createdBy     String        @map("created_by")
  createdAt     DateTime      @default(now()) @map("created_at")
  updatedAt     DateTime      @updatedAt @map("updated_at")

  // 关联
  createdByUser User              @relation("CustomerCreatedBy", fields: [createdBy], references: [id])
  contacts      CustomerContact[]
  archives      ProjectArchive[]

  @@map("customers")
}

// 客户联系人表
model CustomerContact {
  id         String   @id @default(cuid())
  customerId String   @map("customer_id")
  name       String
  position   String?
  email      String?
  phone      String?
  isPrimary  Boolean  @default(false) @map("is_primary")
  createdAt  DateTime @default(now()) @map("created_at")
  updatedAt  DateTime @updatedAt @map("updated_at")

  // 关联
  customer Customer @relation(fields: [customerId], references: [id], onDelete: Cascade)

  @@map("customer_contacts")
}

// 项目档案表 (简化后的项目信息，主要用于运维服务对象)
model ProjectArchive {
  id              String              @id @default(cuid())
  customerId      String              @map("customer_id")
  name            String
  description     String?             @db.Text
  technology      String?             // 技术栈
  environment     String?             // 部署环境
  version         String?             // 当前版本
  deploymentDate  DateTime?           @map("deployment_date") // 部署日期
  status          ArchiveStatus       @default(ACTIVE) // 档案状态
  createdBy       String              @map("created_by")
  createdAt       DateTime            @default(now()) @map("created_at")
  updatedAt       DateTime            @updatedAt @map("updated_at")

  // 关联
  customer        Customer              @relation(fields: [customerId], references: [id])
  createdByUser   User                  @relation("ArchiveCreatedBy", fields: [createdBy], references: [id])
  configurations  ProjectConfiguration[]
  services        Service[]

  @@map("project_archives")
}

// 项目配置表 (运维配置信息)
model ProjectConfiguration {
  id             String         @id @default(cuid())
  archiveId      String         @map("archive_id")
  configType     ConfigType     @map("config_type")
  title          String         // 配置项标题
  configData     Json           @map("config_data") // 存储配置数据
  encryptedFields Json?         @map("encrypted_fields") // 存储需要加密的字段名
  description    String?        // 配置说明
  isActive       Boolean        @default(true) @map("is_active")
  lastUpdated    DateTime?      @map("last_updated") // 最后更新时间
  createdAt      DateTime       @default(now()) @map("created_at")
  updatedAt      DateTime       @updatedAt @map("updated_at")

  // 关联
  archive ProjectArchive @relation(fields: [archiveId], references: [id], onDelete: Cascade)

  @@map("project_configurations")
}

// SLA模板表
model SlaTemplate {
  id             String   @id @default(cuid())
  name           String
  description    String?  @db.Text
  responseTime   Int      @map("response_time") // 响应时间(分钟)
  resolutionTime Int      @map("resolution_time") // 解决时间(小时)
  availability   Float    @default(99.9) // 可用性百分比
  createdAt      DateTime @default(now()) @map("created_at")
  updatedAt      DateTime @updatedAt @map("updated_at")

  // 关联
  services Service[]

  @@map("sla_templates")
}

// 运维服务记录表 (核心功能)
model Service {
  id                    String        @id @default(cuid())
  archiveId             String        @map("archive_id") // 关联项目档案
  slaTemplateId         String?       @map("sla_template_id")
  ticketNumber          String        @unique @map("ticket_number") // 工单号
  title                 String
  description           String        @db.Text
  category              ServiceCategory @default(MAINTENANCE) // 服务类别
  priority              Priority      @default(MEDIUM)
  status                ServiceStatus @default(PENDING)
  assignedTo            String?       @map("assigned_to")
  customerContact       String?       @map("customer_contact") // 客户联系人
  
  // 外部调用相关字段 - MVP新增
  source                ServiceSource @default(INTERNAL) @map("source") // 工单来源
  externalSystemId      String?       @map("external_system_id") // 外部系统标识
  externalUserId        String?       @map("external_user_id") // 外部用户ID  
  externalAccount       String?       @map("external_account") // 外部账号
  
  startTime             DateTime?     @map("start_time")
  endTime               DateTime?     @map("end_time")
  actualResponseTime    Int?          @map("actual_response_time") // 实际响应时间(分钟)
  actualResolutionTime  Int?          @map("actual_resolution_time") // 实际解决时间(小时)
  estimatedHours        Float?        @map("estimated_hours") // 预估工时
  actualHours           Float?        @map("actual_hours") // 实际工时
  resolution            String?       @db.Text // 解决方案
  customerFeedback      String?       @db.Text @map("customer_feedback") // 客户反馈
  satisfaction          Int?          // 客户满意度(1-5)
  tags                  Json?         // 标签
  firstResponseAt       DateTime?     @map("first_response_at") // 首次响应时间
  createdBy             String        @map("created_by")
  createdAt             DateTime      @default(now()) @map("created_at")
  updatedAt             DateTime      @updatedAt @map("updated_at")

  // 关联
  archive       ProjectArchive @relation(fields: [archiveId], references: [id])
  slaTemplate   SlaTemplate?   @relation(fields: [slaTemplateId], references: [id])
  assignedUser  User?          @relation("ServiceAssignedTo", fields: [assignedTo], references: [id])
  createdByUser User           @relation("ServiceCreatedBy", fields: [createdBy], references: [id])
  workLogs      ServiceWorkLog[]
  attachments   ServiceAttachment[]
  comments      ServiceComment[]
  operationHistory ServiceOperationHistory[]

  @@map("services")
}

// 服务工作日志表
model ServiceWorkLog {
  id          String   @id @default(cuid())
  serviceId   String   @map("service_id")
  userId      String   @map("user_id")
  description String   @db.Text
  workHours   Float    @map("work_hours") // 工作时长(小时)
  workDate    DateTime @map("work_date")
  category    WorkCategory @default(MAINTENANCE)
  createdAt   DateTime @default(now()) @map("created_at")

  // 关联
  service Service @relation(fields: [serviceId], references: [id], onDelete: Cascade)
  user    User    @relation(fields: [userId], references: [id])

  @@map("service_work_logs")
}

// 服务附件表
model ServiceAttachment {
  id           String   @id @default(cuid())
  serviceId    String   @map("service_id")
  filename     String
  originalName String   @map("original_name")
  filePath     String   @map("file_path")
  fileSize     Int      @map("file_size")
  mimeType     String   @map("mime_type")
  uploadedBy   String   @map("uploaded_by")
  uploadedAt   DateTime @default(now()) @map("uploaded_at")

  // 关联
  service  Service @relation(fields: [serviceId], references: [id], onDelete: Cascade)
  uploader User    @relation(fields: [uploadedBy], references: [id])

  @@map("service_attachments")
}

// 服务评论表
model ServiceComment {
  id         String   @id @default(cuid())
  serviceId  String   @map("service_id")
  content    String   @db.Text
  isInternal Boolean  @default(false) @map("is_internal") // 是否内部评论
  authorId   String   @map("author_id")
  createdAt  DateTime @default(now()) @map("created_at")
  updatedAt  DateTime @updatedAt @map("updated_at")

  // 关联
  service Service @relation(fields: [serviceId], references: [id], onDelete: Cascade)
  author  User    @relation(fields: [authorId], references: [id])

  @@map("service_comments")
}

// 通知模板表
model NotificationTemplate {
  id        String           @id @default(cuid())
  name      String
  type      NotificationType
  subject   String
  content   String           @db.Text
  variables Json?            // 可用变量
  createdAt DateTime         @default(now()) @map("created_at")
  updatedAt DateTime         @updatedAt @map("updated_at")

  // 关联
  notifications Notification[]

  @@map("notification_templates")
}

// 通知记录表
model Notification {
  id         String           @id @default(cuid())
  templateId String?          @map("template_id")
  recipient  String           // 接收者邮箱或手机号
  type       NotificationType
  subject    String
  content    String           @db.Text
  status     NotificationStatus @default(PENDING)
  sentAt     DateTime?        @map("sent_at")
  createdAt  DateTime         @default(now()) @map("created_at")

  // 关联
  template NotificationTemplate? @relation(fields: [templateId], references: [id])

  @@map("notifications")
}

// 服务操作历史表
model ServiceOperationHistory {
  id          String                   @id @default(cuid())
  serviceId   String                   @map("service_id")
  type        ServiceOperationType     
  description String                   
  fromValue   String?                  @map("from_value")   // 变更前的值
  toValue     String?                  @map("to_value")     // 变更后的值
  note        String?                  @db.Text             // 操作备注
  userId      String                   @map("user_id")      // 操作人ID
  createdAt   DateTime                 @default(now()) @map("created_at")

  // 关联
  service Service @relation(fields: [serviceId], references: [id], onDelete: Cascade)
  user    User    @relation(fields: [userId], references: [id])

  @@map("service_operation_history")
}

// 操作日志表
model OperationLog {
  id        String   @id @default(cuid())
  userId    String   @map("user_id")
  action    String   // 操作类型
  resource  String   // 操作资源
  resourceId String? @map("resource_id")
  details   Json?    // 操作详情
  ipAddress String?  @map("ip_address")
  userAgent String?  @map("user_agent")
  createdAt DateTime @default(now()) @map("created_at")

  // 关联
  user User @relation(fields: [userId], references: [id])

  @@map("operation_logs")
}

// API密钥管理表 - 外部调用MVP
model ApiKey {
  id          String      @id @default(cuid())
  name        String      // 密钥名称
  keyValue    String      @unique @map("key_value") // API密钥值
  systemId    String      @map("system_id") // 外部系统标识
  description String?     // 描述
  status      ApiKeyStatus @default(ACTIVE) // 状态
  expiresAt   DateTime?   @map("expires_at") // 过期时间
  lastUsedAt  DateTime?   @map("last_used_at") // 最后使用时间
  usageCount  Int         @default(0) @map("usage_count") // 使用次数
  createdBy   String      @map("created_by") // 创建人
  createdAt   DateTime    @default(now()) @map("created_at")
  updatedAt   DateTime    @updatedAt @map("updated_at")

  // 关联
  createdByUser User      @relation("ApiKeyCreatedBy", fields: [createdBy], references: [id])

  @@map("api_keys")
}

// 枚举定义
enum UserStatus {
  ACTIVE
  INACTIVE
  BANNED
}

enum CustomerType {
  ENTERPRISE    // 企业客户
  INDIVIDUAL    // 个人客户
  GOVERNMENT    // 政府机构
  NONPROFIT     // 非营利组织
}

enum CustomerLevel {
  BASIC
  STANDARD
  PREMIUM
  ENTERPRISE
}

enum ArchiveStatus {
  ACTIVE
  MAINTENANCE
  DEPRECATED
  ARCHIVED
}

enum ServiceCategory {
  MAINTENANCE
  SUPPORT
  UPGRADE
  BUGFIX
  CONSULTING
  MONITORING
}

enum Priority {
  LOW
  MEDIUM
  HIGH
  URGENT
}

enum ConfigType {
  SERVER
  DATABASE
  VPN
  ACCOUNT
  ENVIRONMENT
  OTHER
}

enum ServiceStatus {
  PENDING
  IN_PROGRESS
  WAITING_CUSTOMER
  RESOLVED
  CLOSED
  OPEN
}

enum NotificationType {
  EMAIL
  SMS
  SYSTEM
}

enum NotificationStatus {
  PENDING
  SENT
  FAILED
  CANCELLED
}

enum WorkCategory {
  ANALYSIS
  IMPLEMENTATION
  TESTING
  DOCUMENTATION
  COMMUNICATION
  MAINTENANCE
  SUPPORT
  OTHER
}

enum ServiceOperationType {
  CREATE          // 创建工单
  UPDATE          // 更新信息
  STATUS_CHANGE   // 状态变更
  TRANSFER        // 工单转交
  ASSIGNMENT      // 工单分配
  COMMENT         // 添加评论
  ATTACHMENT      // 添加附件
  WORK_LOG        // 添加工作日志
}

// 外部调用相关枚举 - MVP新增
enum ServiceSource {
  INTERNAL        // 内部创建
  EXTERNAL        // 外部API创建
}

enum ApiKeyStatus {
  ACTIVE          // 活跃
  INACTIVE        // 已禁用
  REVOKED         // 已撤销
  EXPIRED         // 已过期
}

// 告警规则表
model AlertRule {
  id                   String                @id @default(cuid())
  name                 String                @unique
  description          String?               @db.Text
  metricType           AlertMetricType       @map("metric_type")
  condition            AlertCondition        
  threshold            Float                 
  duration             Int                   // 持续时间（秒）
  severity             AlertSeverity         
  enabled              Boolean               @default(true)
  notificationChannels Json                  @map("notification_channels") // 存储通知渠道数组
  createdAt            DateTime              @default(now()) @map("created_at")
  updatedAt            DateTime              @updatedAt @map("updated_at")

  // 关联
  alerts Alert[]

  @@map("alert_rules")
}

// 告警记录表
model Alert {
  id              String          @id @default(cuid())
  ruleId          String          @map("rule_id")
  severity        AlertSeverity   
  status          AlertStatus     @default(PENDING)
  message         String          @db.Text
  metricValue     Float?          @map("metric_value") // 触发时的实际值
  triggeredAt     DateTime        @default(now()) @map("triggered_at")
  acknowledgedAt  DateTime?       @map("acknowledged_at")
  acknowledgedBy  String?         @map("acknowledged_by")
  resolvedAt      DateTime?       @map("resolved_at")
  resolvedBy      String?         @map("resolved_by")
  notificationsSent Json?         @map("notifications_sent") // 已发送的通知记录

  // 关联
  rule              AlertRule     @relation(fields: [ruleId], references: [id], onDelete: Cascade)
  acknowledgedByUser User?        @relation("AlertAcknowledgedBy", fields: [acknowledgedBy], references: [id])
  resolvedByUser     User?        @relation("AlertResolvedBy", fields: [resolvedBy], references: [id])

  @@map("alerts")
}

enum AlertMetricType {
  CPU
  MEMORY
  DISK
  NETWORK
  SERVICE
  DATABASE
  REDIS
}

enum AlertCondition {
  GT    // >
  LT    // <
  GTE   // >=
  LTE   // <=
  EQ    // =
  NEQ   // !=
}

enum AlertSeverity {
  LOW
  MEDIUM
  HIGH
  CRITICAL
}

enum AlertStatus {
  PENDING
  ACKNOWLEDGED
  RESOLVED
  CANCELLED
}

// 系统配置表
model SystemConfig {
  id          String              @id @default(cuid())
  category    SystemConfigCategory
  key         String              // 配置键
  value       Json                // 配置值（支持各种数据类型）
  description String?             // 配置描述
  dataType    SystemConfigDataType @default(STRING) @map("data_type")
  isEncrypted Boolean             @default(false) @map("is_encrypted") // 是否加密存储
  isSystem    Boolean             @default(false) @map("is_system") // 是否系统配置（不可删除）
  isPublic    Boolean             @default(false) @map("is_public") // 是否公开（前端可见）
  validationRule Json?            @map("validation_rule") // 验证规则
  defaultValue   Json?            @map("default_value") // 默认值
  displayOrder   Int?             @map("display_order") // 显示顺序
  updatedBy      String?          @map("updated_by") // 最后更新人
  createdAt      DateTime         @default(now()) @map("created_at")
  updatedAt      DateTime         @updatedAt @map("updated_at")

  // 关联
  updatedByUser  User?            @relation(fields: [updatedBy], references: [id])
  configHistory  SystemConfigHistory[]

  @@unique([key]) // 配置键全局唯一
  @@map("system_configs")
}

// 系统配置变更历史表
model SystemConfigHistory {
  id           String   @id @default(cuid())
  configId     String   @map("config_id")
  oldValue     Json?    @map("old_value")
  newValue     Json     @map("new_value")
  changeReason String?  @map("change_reason") // 变更原因
  changedBy    String   @map("changed_by")
  createdAt    DateTime @default(now()) @map("created_at")

  // 关联
  config       SystemConfig @relation(fields: [configId], references: [id], onDelete: Cascade)
  changedByUser User        @relation(fields: [changedBy], references: [id])

  @@map("system_config_history")
}

enum SystemConfigCategory {
  GENERAL      // 基本配置
  SECURITY     // 安全配置
  EMAIL        // 邮件配置
  SMS          // 短信配置
  NOTIFICATION // 通知配置
  STORAGE      // 存储配置
  BACKUP       // 备份配置
  SYSTEM       // 系统配置
  INTEGRATION  // 集成配置
  CUSTOM       // 自定义配置
}

enum SystemConfigDataType {
  STRING
  NUMBER
  BOOLEAN
  JSON
  EMAIL
  URL
  PASSWORD
  TEXTAREA
  SELECT
  MULTI_SELECT
}

// 权限模板表
model PermissionTemplate {
  id              String                    @id @default(cuid())
  name            String                    @unique           // 模板名称
  description     String?                   @db.Text          // 模板描述
  permissions     Json                                        // 权限数组，JSON存储
  category        PermissionTemplateCategory @default(CUSTOM) // 模板分类
  isDefault       Boolean                   @default(false) @map("is_default")  // 是否为默认模板
  isSystem        Boolean                   @default(false) @map("is_system")   // 是否为系统模板（不可删除）
  version         String                    @default("1.0")   // 模板版本
  metadata        Json?                                       // 元数据信息
  createdBy       String                    @map("created_by") // 创建人
  updatedBy       String?                   @map("updated_by") // 最后修改人
  createdAt       DateTime                  @default(now()) @map("created_at")
  updatedAt       DateTime                  @updatedAt @map("updated_at")

  // 关联
  createdByUser   User                      @relation("PermissionTemplateCreatedBy", fields: [createdBy], references: [id])
  updatedByUser   User?                     @relation("PermissionTemplateUpdatedBy", fields: [updatedBy], references: [id])
  templateHistory PermissionTemplateHistory[]
  templateUsage   PermissionTemplateUsage[]

  @@map("permission_templates")
}

// 权限模板历史记录表
model PermissionTemplateHistory {
  id           String   @id @default(cuid())
  templateId   String   @map("template_id")
  action       PermissionTemplateAction      // 操作类型
  oldData      Json?    @map("old_data")      // 变更前数据
  newData      Json?    @map("new_data")      // 变更后数据
  changeReason String?  @map("change_reason") // 变更原因
  changedBy    String   @map("changed_by")
  createdAt    DateTime @default(now()) @map("created_at")

  // 关联
  template     PermissionTemplate @relation(fields: [templateId], references: [id], onDelete: Cascade)
  changedByUser User              @relation("PermissionTemplateHistoryChangedBy", fields: [changedBy], references: [id])

  @@map("permission_template_history")
}

// 权限模板使用记录表
model PermissionTemplateUsage {
  id         String                       @id @default(cuid())
  templateId String                       @map("template_id")
  roleId     String                       @map("role_id")
  appliedBy  String                       @map("applied_by")  // 应用人
  appliedAt  DateTime                     @default(now()) @map("applied_at")
  status     PermissionTemplateUsageStatus @default(ACTIVE)
  note       String?                      @db.Text           // 应用备注

  // 关联
  template   PermissionTemplate @relation(fields: [templateId], references: [id], onDelete: Cascade)
  role       Role              @relation("PermissionTemplateUsageRole", fields: [roleId], references: [id], onDelete: Cascade)
  appliedByUser User           @relation("PermissionTemplateUsageAppliedBy", fields: [appliedBy], references: [id])

  @@unique([templateId, roleId]) // 同一个模板对同一个角色只能有一条记录
  @@map("permission_template_usage")
}

// 权限模板分类枚举
enum PermissionTemplateCategory {
  SYSTEM      // 系统管理类
  BUSINESS    // 业务管理类
  SERVICE     // 服务运维类
  READONLY    // 只读类
  CUSTOM      // 自定义类
}

// 权限模板操作类型枚举
enum PermissionTemplateAction {
  CREATE      // 创建
  UPDATE      // 更新
  DELETE      // 删除
  APPLY       // 应用到角色
  EXPORT      // 导出
  IMPORT      // 导入
  COPY        // 复制
}

// 权限模板使用状态枚举
enum PermissionTemplateUsageStatus {
  ACTIVE      // 生效中
  INACTIVE    // 未生效
  SUPERSEDED  // 已被替换
}

// ========== 监控相关模型 ==========

// 系统事件日志表
model SystemEvent {
  id         String   @id @default(cuid())
  level      String   // 事件级别: info, warn, error, critical
  type       String   // 事件类型: system, service, performance, security
  message    String   @db.Text // 事件消息
  details    Json?    // 事件详细信息
  source     String?  // 事件源
  resolved   Boolean  @default(false) // 是否已解决
  resolvedBy String?  @map("resolved_by") // 解决人
  createdAt  DateTime @default(now()) @map("created_at")
  resolvedAt DateTime? @map("resolved_at")
  
  // 关联
  resolvedByUser User? @relation("SystemEventUser", fields: [resolvedBy], references: [id])
  
  @@index([level, type])
  @@index([createdAt])
  @@map("system_events")
}

// 系统指标历史表（可选，用于长期存储）
model SystemMetrics {
  id          String   @id @default(cuid())
  timestamp   DateTime @default(now())
  metricType  String   @map("metric_type") // CPU, MEMORY, DISK, NETWORK等
  value       Float    // 指标值
  unit        String   // 单位
  tags        Json?    // 标签信息，用于分组和过滤
  
  @@index([metricType, timestamp])
  @@index([timestamp])
  @@map("system_metrics")
}

// 性能基准表
model PerformanceBenchmark {
  id          String   @id @default(cuid())
  category    String   // 分类: CPU, Memory, Disk, Network等
  metric      String   // 指标名称
  baseline    Float    // 基准值
  target      Float    // 目标值
  description String?  @db.Text // 描述
  enabled     Boolean  @default(true)
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")
  
  @@unique([category, metric])
  @@map("performance_benchmarks")
}

// 监控配置表
model MonitoringConfig {
  id            String   @id @default(cuid())
  category      String   // 配置分类
  key          String   // 配置键
  value        Json     // 配置值
  description  String?  @db.Text // 描述
  enabled      Boolean  @default(true)
  updatedBy    String?  @map("updated_by")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")
  
  @@unique([category, key])
  @@map("monitoring_config")
}

// 告警阈值表
model AlertThreshold {
  id          String   @id @default(cuid())
  metricType  String   @map("metric_type") // 指标类型
  name        String   // 阈值名称
  warning     Float    // 警告阈值
  critical    Float    // 严重阈值
  duration    Int      // 持续时间（秒）
  enabled     Boolean  @default(true)
  description String?  @db.Text
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")
  
  @@unique([metricType, name])
  @@map("alert_thresholds")
}

// 服务可用性统计表
model ServiceAvailability {
  id          String   @id @default(cuid())
  serviceName String   @map("service_name")
  date        DateTime @db.Date // 统计日期
  uptime      Float    // 正常运行时间百分比
  downtime    Float    // 宕机时间百分比
  incidents   Int      @default(0) // 故障次数
  mttr        Float?   // 平均恢复时间（分钟）
  
  @@unique([serviceName, date])
  @@index([serviceName, date])
  @@map("service_availability")
}

// 智能分析结果表
model IntelligentAnalysisResult {
  id            String   @id @default(cuid())
  analysisType  String   @map("analysis_type") // 分析类型: anomaly, prediction, recommendation
  result        Json     // 分析结果
  confidence    Float    // 置信度
  status        String   @default("active") // 状态: active, expired, resolved
  validUntil    DateTime? @map("valid_until") // 有效期
  createdAt     DateTime @default(now()) @map("created_at")
  
  @@index([analysisType, status])
  @@index([createdAt])
  @@map("intelligent_analysis_results")
}

// ========== 用户活动分析相关模型 ==========

// 用户会话表
model UserSession {
  id               String    @id @default(cuid())
  userId           String    @map("user_id")
  sessionToken     String    @unique @map("session_token")
  ipAddress        String?   @map("ip_address") @db.VarChar(45)
  userAgent        String?   @map("user_agent") @db.Text
  location         Json?     // 地理位置信息
  deviceInfo       Json?     @map("device_info") // 设备信息
  isActive         Boolean   @default(true) @map("is_active")
  lastActivity     DateTime  @default(now()) @map("last_activity")
  loginTime        DateTime  @default(now()) @map("login_time")
  logoutTime       DateTime? @map("logout_time")
  durationMinutes  Int?      @map("duration_minutes")
  activityScore    Float?    @default(0) @map("activity_score")
  createdAt        DateTime  @default(now()) @map("created_at")
  updatedAt        DateTime  @updatedAt @map("updated_at")

  // 关联
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([isActive])
  @@index([lastActivity])
  @@map("user_sessions")
}

// 用户活动统计表
model UserActivityStats {
  id                    String    @id @default(cuid())
  userId                String    @map("user_id")
  date                  DateTime  @db.Date
  loginCount            Int       @default(0) @map("login_count")
  sessionCount          Int       @default(0) @map("session_count")
  totalDurationMinutes  Int       @default(0) @map("total_duration_minutes")
  operationCount        Int       @default(0) @map("operation_count")
  pageViews             Int       @default(0) @map("page_views")
  activityScore         Float     @default(0) @map("activity_score")
  peakActivityHour      Int?      @map("peak_activity_hour")
  featuresUsed          Json?     @map("features_used")
  lastActivity          DateTime? @map("last_activity")
  createdAt             DateTime  @default(now()) @map("created_at")
  updatedAt             DateTime  @updatedAt @map("updated_at")

  // 关联
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, date])
  @@index([date])
  @@index([activityScore])
  @@map("user_activity_stats")
}

// 用户行为模式表
model UserBehaviorPattern {
  id             String                  @id @default(cuid())
  userId         String                  @map("user_id")
  patternType    UserBehaviorPatternType @map("pattern_type")
  patternData    Json                    @map("pattern_data")
  confidenceScore Float                  @default(0) @map("confidence_score")
  detectedAt     DateTime                @default(now()) @map("detected_at")
  lastUpdated    DateTime                @default(now()) @updatedAt @map("last_updated")
  status         UserBehaviorPatternStatus @default(ACTIVE)
  metadata       Json?

  // 关联
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, patternType])
  @@index([patternType])
  @@index([status])
  @@map("user_behavior_patterns")
}

// 用户活动异常表
model UserActivityAnomaly {
  id             String                   @id @default(cuid())
  userId         String                   @map("user_id")
  anomalyType    UserActivityAnomalyType  @map("anomaly_type")
  description    String                   @db.Text
  severity       UserActivityAnomalySeverity @default(MEDIUM)
  detectedValue  Json                     @map("detected_value")
  normalRange    Json?                    @map("normal_range")
  confidenceScore Float                   @default(0) @map("confidence_score")
  isResolved     Boolean                  @default(false) @map("is_resolved")
  resolvedAt     DateTime?                @map("resolved_at")
  resolvedBy     String?                  @map("resolved_by")
  notes          String?                  @db.Text
  detectedAt     DateTime                 @default(now()) @map("detected_at")

  // 关联
  user        User  @relation(fields: [userId], references: [id], onDelete: Cascade)
  resolvedByUser User? @relation("AnomalyResolvedBy", fields: [resolvedBy], references: [id])

  @@index([userId])
  @@index([anomalyType])
  @@index([severity])
  @@index([isResolved])
  @@map("user_activity_anomalies")
}

// 用户功能使用统计表
model UserFeatureUsage {
  id            String   @id @default(cuid())
  userId        String   @map("user_id")
  featureName   String   @map("feature_name")
  usageCount    Int      @default(0) @map("usage_count")
  lastUsed      DateTime @default(now()) @map("last_used")
  date          DateTime @db.Date
  avgTimeSpent  Float?   @default(0) @map("avg_time_spent")
  createdAt     DateTime @default(now()) @map("created_at")
  updatedAt     DateTime @updatedAt @map("updated_at")

  // 关联
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, featureName, date])
  @@index([featureName])
  @@index([date])
  @@map("user_feature_usage")
}

// 用户行为模式类型枚举
enum UserBehaviorPatternType {
  LOGIN_TIME          // 登录时间模式
  ACTIVITY_DURATION   // 活动时长模式
  FEATURE_USAGE       // 功能使用模式
  OPERATION_FREQUENCY // 操作频率模式
}

// 用户行为模式状态枚举
enum UserBehaviorPatternStatus {
  ACTIVE   // 活跃模式
  INACTIVE // 非活跃模式
  ANOMALY  // 异常模式
}

// 用户活动异常类型枚举
enum UserActivityAnomalyType {
  UNUSUAL_TIME        // 异常时间
  EXCESSIVE_OPERATIONS // 过度操作
  ABNORMAL_LOCATION   // 异常位置
  SUSPICIOUS_BEHAVIOR // 可疑行为
}

// 用户活动异常严重程度枚举
enum UserActivityAnomalySeverity {
  LOW      // 低风险
  MEDIUM   // 中风险
  HIGH     // 高风险
  CRITICAL // 严重风险
}

// ========== 邮件模板管理相关模型 ==========

// 邮件模板表
model EmailTemplate {
  id          String                @id @default(cuid())
  name        String                // 模板名称
  type        EmailTemplateType     // 模板类型
  category    EmailTemplateCategory @default(SYSTEM) // 模板分类
  subject     String                // 邮件主题
  content     String                @db.LongText // 邮件内容
  description String?               @db.Text // 模板描述
  variables   Json?                 // 可用变量数组
  enabled     Boolean               @default(true) // 是否启用
  isSystem    Boolean               @default(false) @map("is_system") // 是否系统模板
  version     String                @default("1.0") // 模板版本
  metadata    Json?                 // 元数据
  createdBy   String                @map("created_by")
  updatedBy   String?               @map("updated_by")
  createdAt   DateTime              @default(now()) @map("created_at")
  updatedAt   DateTime              @updatedAt @map("updated_at")

  // 关联
  createdByUser User  @relation("EmailTemplateCreatedBy", fields: [createdBy], references: [id])
  updatedByUser User? @relation("EmailTemplateUpdatedBy", fields: [updatedBy], references: [id])
  
  // 邮件发送记录关联
  emailLogs EmailLog[]

  @@unique([name, type]) // 同类型下模板名称唯一
  @@map("email_templates")
}

// 邮件发送日志表
model EmailLog {
  id           String            @id @default(cuid())
  templateId   String?           @map("template_id")
  recipient    String            // 收件人邮箱
  subject      String            // 邮件主题
  content      String            @db.LongText // 邮件内容
  status       EmailLogStatus    @default(PENDING) // 发送状态
  errorMessage String?           @map("error_message") @db.Text // 错误信息
  sentAt       DateTime?         @map("sent_at") // 发送时间
  deliveredAt  DateTime?         @map("delivered_at") // 送达时间
  openedAt     DateTime?         @map("opened_at") // 打开时间
  metadata     Json?             // 元数据（如变量替换信息）
  retryCount   Int               @default(0) @map("retry_count") // 重试次数
  createdAt    DateTime          @default(now()) @map("created_at")
  updatedAt    DateTime          @updatedAt @map("updated_at")

  // 关联
  template EmailTemplate? @relation(fields: [templateId], references: [id])

  @@index([recipient])
  @@index([status])
  @@index([createdAt])
  @@map("email_logs")
}

// 邮件模板类型枚举
enum EmailTemplateType {
  ALERT               // 系统告警
  MAINTENANCE         // 系统维护
  SERVICE_CREATED     // 服务单创建
  SERVICE_ASSIGNED    // 服务单分配
  SERVICE_RESOLVED    // 服务单解决
  SERVICE_CLOSED      // 服务单关闭
  USER_WELCOME        // 用户欢迎
  PASSWORD_RESET      // 密码重置
  ACCOUNT_LOCKED      // 账号锁定
  BACKUP_SUCCESS      // 备份成功
  BACKUP_FAILED       // 备份失败
  CUSTOM              // 自定义
}

// 邮件模板分类枚举
enum EmailTemplateCategory {
  SYSTEM              // 系统类
  BUSINESS            // 业务类
  NOTIFICATION        // 通知类
  SECURITY            // 安全类
}

// 邮件发送状态枚举
enum EmailLogStatus {
  PENDING             // 待发送
  SENDING             // 发送中
  SENT                // 已发送
  DELIVERED           // 已送达
  OPENED              // 已打开
  FAILED              // 发送失败
  CANCELLED           // 已取消
}

// Enum for Task Status
enum TaskStatus {
  RUNNING
  SUCCESS
  FAILED
}

// Model for tracking scheduled task executions
model TaskExecution {
  id         String     @id @default(cuid())
  taskName   String     @map("task_name")
  status     TaskStatus @default(RUNNING)
  startedAt  DateTime   @default(now()) @map("started_at")
  endedAt    DateTime?  @map("ended_at")
  duration   Int? // Duration in milliseconds
  output     String?    @db.Text
  error      String?    @db.Text
  createdAt  DateTime   @default(now()) @map("created_at")
  updatedAt  DateTime   @updatedAt @map("updated_at")

  @@index([taskName])
  @@index([status])
  @@index([createdAt])
  @@map("task_executions")
}

// ========== AI服务相关模型 ==========

// AI配置模型 - 仅全局配置
model AIConfiguration {
  id            String   @id @default(cuid())
  provider      String   // openai, anthropic, gemini等
  model         String   // 具体模型名称
  apiKey        String?  @map("api_key") // 加密存储的API密钥
  temperature   Float    @default(0.3)
  maxTokens     Int      @default(1000) @map("max_tokens")
  timeout       Int      @default(30000)
  mode          String   @default("USER_TRIGGERED") // DISABLED, USER_TRIGGERED, AGGRESSIVE
  enabledFields Json     @default("{\"title\":true,\"category\":true,\"priority\":true,\"slaTemplate\":true}") @map("enabled_fields")
  autoFillThreshold Float @default(0.8) @map("auto_fill_threshold")
  isActive      Boolean  @default(true) @map("is_active")
  createdAt     DateTime @default(now()) @map("created_at")
  updatedAt     DateTime @updatedAt @map("updated_at")

  @@map("ai_configurations")
}

// AI分析请求记录
model AIAnalysisRequest {
  id            String   @id @default(cuid())
  requestId     String   @unique @map("request_id") // 用于前端追踪
  userId        String   @map("user_id")
  description   String   @db.Text
  contextData   Json?    @map("context_data") // 上下文数据
  provider      String   // 使用的AI提供商
  model         String   // 使用的模型
  prompt        String   @db.Text // 实际发送的提示词
  response      Json?    // AI响应结果
  suggestions   Json?    // 解析后的建议
  processingTime Int?    @map("processing_time") // 处理时间(ms)
  success       Boolean  @default(false)
  error         String?  // 错误信息
  createdAt     DateTime @default(now()) @map("created_at")

  // 关联用户
  user User @relation("AIAnalysisRequest", fields: [userId], references: [id])

  // 关联反馈
  feedback AIFeedback[]

  @@map("ai_analysis_requests")
}

// AI反馈模型
model AIFeedback {
  id          String   @id @default(cuid())
  requestId   String   @map("request_id") // 关联分析请求
  userId      String   @map("user_id")
  rating      Int      // 1-5星评分
  helpful     Boolean? // 是否有帮助
  adopted     Json?    // 采用了哪些建议
  comments    String?  // 用户评论
  createdAt   DateTime @default(now()) @map("created_at")

  // 关联分析请求和用户
  analysisRequest AIAnalysisRequest @relation(fields: [requestId], references: [requestId])
  user           User              @relation("AIFeedback", fields: [userId], references: [id])

  @@map("ai_feedback")
}

// AI提示词模板
model AIPromptTemplate {
  id          String   @id @default(cuid())
  name        String   @unique
  category    String   // ticket_analysis, classification等
  template    String   @db.Text
  variables   Json?    // 模板变量定义
  provider    String?  // 特定提供商的模板
  version     String   @default("1.0")
  isActive    Boolean  @default(true) @map("is_active")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")
  createdBy   String   @map("created_by")
  updatedBy   String?  @map("updated_by")

  // 关联创建者和更新者
  creator User  @relation("AIPromptTemplateCreator", fields: [createdBy], references: [id])
  updater User? @relation("AIPromptTemplateUpdater", fields: [updatedBy], references: [id])

  @@map("ai_prompt_templates")
}

// ========== 工作流自动化相关模型 ==========

// 工作流定义表
model WorkflowDefinition {
  id              String             @id @default(cuid())
  name            String             @unique // 工作流名称
  description     String?            @db.Text // 工作流描述
  category        WorkflowCategory   @default(SERVICE_AUTOMATION) // 工作流类别
  version         String             @default("1.0") // 版本号
  isActive        Boolean            @default(true) @map("is_active") // 是否激活
  isTemplate      Boolean            @default(false) @map("is_template") // 是否为模板
  priority        WorkflowPriority   @default(MEDIUM) // 优先级
  
  // 工作流配置
  triggerConfig   Json               @map("trigger_config") // 触发器配置
  stepsConfig     Json               @map("steps_config") // 步骤配置
  conditions      Json?              // 条件配置
  variables       Json?              // 变量配置
  settings        Json?              // 其他设置
  
  // 元数据
  tags            Json?              // 标签
  metadata        Json?              // 元数据
  
  // 统计信息
  executionCount  Int                @default(0) @map("execution_count") // 执行次数
  successCount    Int                @default(0) @map("success_count") // 成功次数
  failureCount    Int                @default(0) @map("failure_count") // 失败次数
  lastExecutedAt  DateTime?          @map("last_executed_at") // 最后执行时间
  avgExecutionTime Float?            @map("avg_execution_time") // 平均执行时间(秒)
  
  // 审计信息
  createdBy       String             @map("created_by") // 创建人
  updatedBy       String?            @map("updated_by") // 更新人
  createdAt       DateTime           @default(now()) @map("created_at")
  updatedAt       DateTime           @updatedAt @map("updated_at")

  // 关联
  createdByUser   User               @relation("WorkflowDefinitionCreatedBy", fields: [createdBy], references: [id])
  updatedByUser   User?              @relation("WorkflowDefinitionUpdatedBy", fields: [updatedBy], references: [id])
  executions      WorkflowExecution[]
  
  @@map("workflow_definitions")
}

// 工作流执行表
model WorkflowExecution {
  id              String                    @id @default(cuid())
  workflowId      String                    @map("workflow_id") // 工作流定义ID
  executionId     String                    @unique @map("execution_id") // 执行唯一标识
  status          WorkflowExecutionStatus   @default(PENDING) // 执行状态
  priority        WorkflowPriority          @default(MEDIUM) // 执行优先级
  
  // 触发信息
  triggerType     WorkflowTriggerType       @map("trigger_type") // 触发类型
  triggerData     Json?                     @map("trigger_data") // 触发数据
  triggeredBy     String?                   @map("triggered_by") // 触发人
  
  // 执行信息
  currentStep     Int                       @default(0) @map("current_step") // 当前步骤
  totalSteps      Int                       @map("total_steps") // 总步骤数
  executionData   Json?                     @map("execution_data") // 执行数据
  variables       Json?                     // 执行变量
  
  // 时间信息
  startedAt       DateTime?                 @map("started_at") // 开始时间
  completedAt     DateTime?                 @map("completed_at") // 完成时间
  scheduledAt     DateTime?                 @map("scheduled_at") // 计划执行时间
  timeoutAt       DateTime?                 @map("timeout_at") // 超时时间
  
  // 结果信息
  result          Json?                     // 执行结果
  error           String?                   @db.Text // 错误信息
  logs            Json?                     // 执行日志
  
  // 性能信息
  executionTime   Float?                    @map("execution_time") // 执行时间(秒)
  resourceUsage   Json?                     @map("resource_usage") // 资源使用情况
  
  createdAt       DateTime                  @default(now()) @map("created_at")
  updatedAt       DateTime                  @updatedAt @map("updated_at")

  // 关联
  workflowDefinition WorkflowDefinition     @relation(fields: [workflowId], references: [id])
  triggeredByUser    User?                  @relation("WorkflowExecutionTriggeredBy", fields: [triggeredBy], references: [id])
  steps              WorkflowExecutionStep[]
  
  @@index([workflowId])
  @@index([status])
  @@index([triggerType])
  @@index([createdAt])
  @@map("workflow_executions")
}

// 工作流执行步骤表
model WorkflowExecutionStep {
  id                String                      @id @default(cuid())
  executionId       String                      @map("execution_id") // 执行ID
  stepIndex         Int                         @map("step_index") // 步骤索引
  stepName          String                      @map("step_name") // 步骤名称
  stepType          WorkflowStepType            @map("step_type") // 步骤类型
  status            WorkflowStepStatus          @default(PENDING) // 步骤状态
  
  // 步骤配置
  config            Json                        // 步骤配置
  input             Json?                       // 输入数据
  output            Json?                       // 输出数据
  
  // 执行信息
  startedAt         DateTime?                   @map("started_at") // 开始时间
  completedAt       DateTime?                   @map("completed_at") // 完成时间
  executionTime     Float?                      @map("execution_time") // 执行时间(秒)
  
  // 结果信息
  result            Json?                       // 步骤结果
  error             String?                     @db.Text // 错误信息
  logs              Json?                       // 步骤日志
  retryCount        Int                         @default(0) @map("retry_count") // 重试次数
  
  createdAt         DateTime                    @default(now()) @map("created_at")
  updatedAt         DateTime                    @updatedAt @map("updated_at")

  // 关联
  execution         WorkflowExecution           @relation(fields: [executionId], references: [id], onDelete: Cascade)
  
  @@unique([executionId, stepIndex])
  @@index([executionId])
  @@index([status])
  @@map("workflow_execution_steps")
}

// 工作流模板表
model WorkflowTemplate {
  id              String                  @id @default(cuid())
  name            String                  @unique // 模板名称
  description     String?                 @db.Text // 模板描述
  category        WorkflowCategory        @default(SERVICE_AUTOMATION) // 模板类别
  tags            Json?                   // 标签
  
  // 模板配置
  templateConfig  Json                    @map("template_config") // 模板配置
  variables       Json?                   // 变量定义
  requirements    Json?                   // 使用要求
  
  // 使用统计
  usageCount      Int                     @default(0) @map("usage_count") // 使用次数
  rating          Float?                  // 评分
  
  // 状态信息
  isOfficial      Boolean                 @default(false) @map("is_official") // 是否官方模板
  isActive        Boolean                 @default(true) @map("is_active") // 是否激活
  
  // 审计信息
  createdBy       String                  @map("created_by") // 创建人
  updatedBy       String?                 @map("updated_by") // 更新人
  createdAt       DateTime                @default(now()) @map("created_at")
  updatedAt       DateTime                @updatedAt @map("updated_at")

  // 关联
  createdByUser   User                    @relation("WorkflowTemplateCreatedBy", fields: [createdBy], references: [id])
  updatedByUser   User?                   @relation("WorkflowTemplateUpdatedBy", fields: [updatedBy], references: [id])
  
  @@map("workflow_templates")
}

// 工作流类别枚举
enum WorkflowCategory {
  SERVICE_AUTOMATION    // 服务工单自动化
  SLA_MONITORING       // SLA监控
  ALERT_PROCESSING     // 告警处理
  BACKUP_AUTOMATION    // 备份自动化
  MAINTENANCE          // 维护任务
  APPROVAL_PROCESS     // 审批流程
  NOTIFICATION         // 通知流程
  DATA_PROCESSING      // 数据处理
  INTEGRATION          // 系统集成
  CUSTOM               // 自定义
}

// 工作流优先级枚举
enum WorkflowPriority {
  LOW                  // 低优先级
  MEDIUM               // 中优先级
  HIGH                 // 高优先级
  URGENT               // 紧急
  CRITICAL             // 关键
}

// 工作流执行状态枚举
enum WorkflowExecutionStatus {
  PENDING              // 等待中
  RUNNING              // 执行中
  PAUSED               // 已暂停
  COMPLETED            // 已完成
  FAILED               // 执行失败
  CANCELLED            // 已取消
  TIMEOUT              // 超时
  SKIPPED              // 已跳过
}

// 工作流触发器类型枚举
enum WorkflowTriggerType {
  MANUAL               // 手动触发
  SCHEDULED            // 定时触发
  EVENT                // 事件触发
  WEBHOOK              // Webhook触发
  API                  // API触发
  CONDITION            // 条件触发
}

// 工作流步骤类型枚举
enum WorkflowStepType {
  ACTION               // 动作步骤
  CONDITION            // 条件步骤
  APPROVAL             // 审批步骤
  NOTIFICATION         // 通知步骤
  DELAY                // 延迟步骤
  PARALLEL             // 并行步骤
  LOOP                 // 循环步骤
  SUBPROCESS           // 子流程步骤
  SCRIPT               // 脚本步骤
  HTTP_REQUEST         // HTTP请求步骤
  DATABASE_OPERATION   // 数据库操作步骤
  FILE_OPERATION       // 文件操作步骤
}

// 工作流步骤状态枚举
enum WorkflowStepStatus {
  PENDING              // 等待中
  RUNNING              // 执行中
  COMPLETED            // 已完成
  FAILED               // 执行失败
  SKIPPED              // 已跳过
  RETRYING             // 重试中
}
