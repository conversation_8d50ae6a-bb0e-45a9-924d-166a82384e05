import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function seedAIData() {
  console.log('开始创建AI相关示例数据...')

  try {
    // 1. 创建AI配置
    console.log('创建AI配置...')
    const aiConfigs = await Promise.all([
      prisma.aIConfiguration.upsert({
        where: { id: 'ai-config-1' },
        update: {},
        create: {
          id: 'ai-config-1',
          provider: 'openai',
          model: 'gpt-4',
          apiKey: 'sk-test-key-openai-encrypted',
          temperature: 0.3,
          maxTokens: 4000,
          timeout: 30000,
          mode: 'USER_TRIGGERED',
          enabledFields: {
            title: true,
            category: true,
            priority: true,
            slaTemplate: true
          },
          autoFillThreshold: 0.8,
          isActive: true
        }
      }),
      prisma.aIConfiguration.upsert({
        where: { id: 'ai-config-2' },
        update: {},
        create: {
          id: 'ai-config-2',
          provider: 'anthropic',
          model: 'claude-3-sonnet',
          apiKey: 'sk-test-key-anthropic-encrypted',
          temperature: 0.5,
          maxTokens: 4000,
          timeout: 30000,
          mode: 'USER_TRIGGERED',
          enabledFields: {
            title: true,
            category: true,
            priority: true,
            slaTemplate: false
          },
          autoFillThreshold: 0.7,
          isActive: true
        }
      }),
      prisma.aIConfiguration.upsert({
        where: { id: 'ai-config-3' },
        update: {},
        create: {
          id: 'ai-config-3',
          provider: 'gemini',
          model: 'gemini-pro',
          apiKey: 'test-key-gemini-encrypted',
          temperature: 0.7,
          maxTokens: 2048,
          timeout: 25000,
          mode: 'DISABLED',
          enabledFields: {
            title: true,
            category: false,
            priority: true,
            slaTemplate: false
          },
          autoFillThreshold: 0.6,
          isActive: false
        }
      })
    ])

    console.log(`创建了 ${aiConfigs.length} 个AI配置`)

    // 2. 获取管理员用户ID
    const adminUser = await prisma.user.findFirst({
      where: { username: 'admin' }
    })

    if (!adminUser) {
      throw new Error('未找到管理员用户，请先运行基础数据种子')
    }

    // 3. 创建提示词模板
    console.log('创建提示词模板...')
    const promptTemplates = await Promise.all([
      prisma.aIPromptTemplate.upsert({
        where: { name: '工单分析模板' },
        update: {},
        create: {
          name: '工单分析模板',
          category: 'ticket_analysis',
          template: `请分析以下工单内容：

工单描述：{{description}}
客户信息：{{customerInfo}}
历史记录：{{historyPattern}}

请提供以下分析结果：
1. 问题分类（从以下选择：技术支持、客户服务、产品问题、账户问题、其他）
2. 优先级建议（HIGH/MEDIUM/LOW）
3. 预估解决时间
4. 建议的解决方案
5. 需要的技能标签

请以JSON格式返回结果：
{
  "category": "分类",
  "priority": "优先级",
  "estimatedTime": "预估时间（小时）",
  "suggestedActions": ["建议1", "建议2"],
  "requiredSkills": ["技能1", "技能2"],
  "confidence": 0.85
}`,
          variables: ["description", "customerInfo", "historyPattern"],
          provider: 'openai',
          version: '1.0',
          isActive: true,
          createdBy: adminUser.id
        }
      }),
      prisma.aIPromptTemplate.upsert({
        where: { name: '分类建议模板' },
        update: {},
        create: {
          name: '分类建议模板',
          category: 'classification',
          template: `根据工单描述进行分类：

工单描述：{{description}}

请从以下类别中选择最合适的分类：
- 技术支持：系统故障、功能问题、性能问题
- 客户服务：账户问题、计费问题、使用咨询
- 产品问题：功能缺陷、改进建议、新功能请求
- 安全问题：数据安全、访问权限、安全漏洞

返回JSON格式：
{
  "category": "分类名称",
  "subcategory": "子分类",
  "confidence": 0.9,
  "reasoning": "分类理由"
}`,
          variables: ["description"],
          provider: 'anthropic',
          version: '1.0',
          isActive: true,
          createdBy: adminUser.id
        }
      }),
      prisma.aIPromptTemplate.upsert({
        where: { name: '优先级评估模板' },
        update: {},
        create: {
          name: '优先级评估模板',
          category: 'priority_assessment',
          template: `评估工单优先级：

工单描述：{{description}}
客户等级：{{customerTier}}
影响范围：{{impactScope}}

评估标准：
- HIGH：系统宕机、数据丢失、安全问题、VIP客户紧急问题
- MEDIUM：功能异常、性能问题、一般客户重要问题
- LOW：使用咨询、功能建议、非紧急问题

返回JSON格式：
{
  "priority": "HIGH/MEDIUM/LOW",
  "reasoning": "评估理由",
  "slaHours": 24,
  "escalationRequired": false,
  "confidence": 0.8
}`,
          variables: ["description", "customerTier", "impactScope"],
          provider: 'openai',
          version: '1.0',
          isActive: true,
          createdBy: adminUser.id
        }
      })
    ])

    console.log(`创建了 ${promptTemplates.length} 个提示词模板`)

    // 4. 创建一些示例分析请求
    console.log('创建示例分析请求...')
    const analysisRequests = await Promise.all([
      prisma.aIAnalysisRequest.create({
        data: {
          requestId: 'req_' + Date.now() + '_1',
          userId: adminUser.id,
          description: '用户反馈系统登录缓慢，页面加载时间超过10秒，影响正常使用',
          contextData: {
            customerTier: 'VIP',
            userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            impactScope: 'multiple_users'
          },
          provider: 'openai',
          model: 'gpt-4',
          prompt: '请分析以下工单内容...',
          response: {
            category: '技术支持',
            priority: 'HIGH',
            estimatedTime: '4小时',
            suggestedActions: ['检查数据库连接池', '优化查询性能', '检查服务器负载'],
            requiredSkills: ['数据库优化', '性能调优'],
            confidence: 0.92
          },
          suggestions: {
            title: '系统登录性能问题',
            category: '技术支持',
            priority: 'HIGH',
            slaTemplate: 'VIP客户4小时响应'
          },
          processingTime: 1250,
          success: true
        }
      }),
      prisma.aIAnalysisRequest.create({
        data: {
          requestId: 'req_' + Date.now() + '_2',
          userId: adminUser.id,
          description: '客户投诉产品功能不完善，希望增加批量导出功能',
          contextData: {
            customerTier: 'Standard',
            userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)',
            impactScope: 'single_user'
          },
          provider: 'anthropic',
          model: 'claude-3-sonnet',
          prompt: '根据工单描述进行分类...',
          response: {
            category: '产品问题',
            subcategory: '新功能请求',
            confidence: 0.88,
            reasoning: '客户明确提出功能改进需求'
          },
          suggestions: {
            title: '批量导出功能需求',
            category: '产品问题',
            priority: 'MEDIUM'
          },
          processingTime: 980,
          success: true
        }
      }),
      prisma.aIAnalysisRequest.create({
        data: {
          requestId: 'req_' + Date.now() + '_3',
          userId: adminUser.id,
          description: '系统出现500错误，无法访问',
          contextData: {
            customerTier: 'Enterprise',
            errorCode: '500',
            timestamp: new Date().toISOString()
          },
          provider: 'openai',
          model: 'gpt-4',
          prompt: '评估工单优先级...',
          response: null,
          suggestions: null,
          processingTime: null,
          success: false,
          error: 'API调用超时'
        }
      })
    ])

    console.log(`创建了 ${analysisRequests.length} 个分析请求记录`)

    console.log('AI示例数据创建完成！')

  } catch (error) {
    console.error('创建AI示例数据失败:', error)
    throw error
  } finally {
    await prisma.$disconnect()
  }
}

// 如果直接运行此文件
if (require.main === module) {
  seedAIData()
    .then(() => {
      console.log('AI数据种子执行完成')
      process.exit(0)
    })
    .catch((error) => {
      console.error('AI数据种子执行失败:', error)
      process.exit(1)
    })
}

export { seedAIData }
