/*
  Warnings:

  - A unique constraint covering the columns `[code]` on the table `customers` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `code` to the `customers` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable - 分步骤添加字段
-- 1. 先添加可为空的字段
ALTER TABLE `customers` ADD COLUMN `contact_email` VARCHAR(191) NULL,
    ADD COLUMN `contact_phone` VARCHAR(191) NULL,
    ADD COLUMN `is_vip` BOOLEAN NOT NULL DEFAULT false,
    ADD COLUMN `type` ENUM('ENTERPRISE', 'INDIVIDUAL', 'GOVERNMENT', 'NONPROFIT') NOT NULL DEFAULT 'ENTERPRISE';

-- 2. 添加code字段，先允许为空
ALTER TABLE `customers` ADD COLUMN `code` VARCHAR(191) NULL;

-- 3. 为现有记录生成code值
SET @row_number = 0;
UPDATE `customers` SET `code` = CONCAT('CUST', LPAD((@row_number := @row_number + 1), 4, '0')) WHERE `code` IS NULL ORDER BY created_at;

-- 4. 将code字段设为NOT NULL
ALTER TABLE `customers` MODIFY COLUMN `code` VARCHAR(191) NOT NULL;

-- CreateIndex
CREATE UNIQUE INDEX `customers_code_key` ON `customers`(`code`);
