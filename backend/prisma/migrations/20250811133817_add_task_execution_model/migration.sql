-- CreateTable
CREATE TABLE `ai_configurations` (
    `id` VARCHAR(191) NOT NULL,
    `provider` VARCHAR(191) NOT NULL,
    `model` VARCHAR(191) NOT NULL,
    `api_key` VARCHAR(191) NULL,
    `temperature` DOUBLE NOT NULL DEFAULT 0.3,
    `max_tokens` INTEGER NOT NULL DEFAULT 1000,
    `timeout` INTEGER NOT NULL DEFAULT 30000,
    `mode` VARCHAR(191) NOT NULL DEFAULT 'USER_TRIGGERED',
    `enabled_fields` JSON NOT NULL,
    `auto_fill_threshold` DOUBLE NOT NULL DEFAULT 0.8,
    `is_active` BOOLEAN NOT NULL DEFAULT true,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `ai_analysis_requests` (
    `id` VARCHAR(191) NOT NULL,
    `request_id` VARCHAR(191) NOT NULL,
    `user_id` VARCHAR(191) NOT NULL,
    `description` TEXT NOT NULL,
    `context_data` JSON NULL,
    `provider` VARCHAR(191) NOT NULL,
    `model` VARCHAR(191) NOT NULL,
    `prompt` TEXT NOT NULL,
    `response` JSON NULL,
    `suggestions` JSON NULL,
    `processing_time` INTEGER NULL,
    `success` BOOLEAN NOT NULL DEFAULT false,
    `error` VARCHAR(191) NULL,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),

    UNIQUE INDEX `ai_analysis_requests_request_id_key`(`request_id`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `ai_feedback` (
    `id` VARCHAR(191) NOT NULL,
    `request_id` VARCHAR(191) NOT NULL,
    `user_id` VARCHAR(191) NOT NULL,
    `rating` INTEGER NOT NULL,
    `helpful` BOOLEAN NULL,
    `adopted` JSON NULL,
    `comments` VARCHAR(191) NULL,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `ai_prompt_templates` (
    `id` VARCHAR(191) NOT NULL,
    `name` VARCHAR(191) NOT NULL,
    `category` VARCHAR(191) NOT NULL,
    `template` TEXT NOT NULL,
    `variables` JSON NULL,
    `provider` VARCHAR(191) NULL,
    `version` VARCHAR(191) NOT NULL DEFAULT '1.0',
    `is_active` BOOLEAN NOT NULL DEFAULT true,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,
    `created_by` VARCHAR(191) NOT NULL,
    `updated_by` VARCHAR(191) NULL,

    UNIQUE INDEX `ai_prompt_templates_name_key`(`name`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `workflow_definitions` (
    `id` VARCHAR(191) NOT NULL,
    `name` VARCHAR(191) NOT NULL,
    `description` TEXT NULL,
    `category` ENUM('SERVICE_AUTOMATION', 'SLA_MONITORING', 'ALERT_PROCESSING', 'BACKUP_AUTOMATION', 'MAINTENANCE', 'APPROVAL_PROCESS', 'NOTIFICATION', 'DATA_PROCESSING', 'INTEGRATION', 'CUSTOM') NOT NULL DEFAULT 'SERVICE_AUTOMATION',
    `version` VARCHAR(191) NOT NULL DEFAULT '1.0',
    `is_active` BOOLEAN NOT NULL DEFAULT true,
    `is_template` BOOLEAN NOT NULL DEFAULT false,
    `priority` ENUM('LOW', 'MEDIUM', 'HIGH', 'URGENT', 'CRITICAL') NOT NULL DEFAULT 'MEDIUM',
    `trigger_config` JSON NOT NULL,
    `steps_config` JSON NOT NULL,
    `conditions` JSON NULL,
    `variables` JSON NULL,
    `settings` JSON NULL,
    `tags` JSON NULL,
    `metadata` JSON NULL,
    `execution_count` INTEGER NOT NULL DEFAULT 0,
    `success_count` INTEGER NOT NULL DEFAULT 0,
    `failure_count` INTEGER NOT NULL DEFAULT 0,
    `last_executed_at` DATETIME(3) NULL,
    `avg_execution_time` DOUBLE NULL,
    `created_by` VARCHAR(191) NOT NULL,
    `updated_by` VARCHAR(191) NULL,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,

    UNIQUE INDEX `workflow_definitions_name_key`(`name`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `workflow_executions` (
    `id` VARCHAR(191) NOT NULL,
    `workflow_id` VARCHAR(191) NOT NULL,
    `execution_id` VARCHAR(191) NOT NULL,
    `status` ENUM('PENDING', 'RUNNING', 'PAUSED', 'COMPLETED', 'FAILED', 'CANCELLED', 'TIMEOUT', 'SKIPPED') NOT NULL DEFAULT 'PENDING',
    `priority` ENUM('LOW', 'MEDIUM', 'HIGH', 'URGENT', 'CRITICAL') NOT NULL DEFAULT 'MEDIUM',
    `trigger_type` ENUM('MANUAL', 'SCHEDULED', 'EVENT', 'WEBHOOK', 'API', 'CONDITION') NOT NULL,
    `trigger_data` JSON NULL,
    `triggered_by` VARCHAR(191) NULL,
    `current_step` INTEGER NOT NULL DEFAULT 0,
    `total_steps` INTEGER NOT NULL,
    `execution_data` JSON NULL,
    `variables` JSON NULL,
    `started_at` DATETIME(3) NULL,
    `completed_at` DATETIME(3) NULL,
    `scheduled_at` DATETIME(3) NULL,
    `timeout_at` DATETIME(3) NULL,
    `result` JSON NULL,
    `error` TEXT NULL,
    `logs` JSON NULL,
    `execution_time` DOUBLE NULL,
    `resource_usage` JSON NULL,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,

    UNIQUE INDEX `workflow_executions_execution_id_key`(`execution_id`),
    INDEX `workflow_executions_workflow_id_idx`(`workflow_id`),
    INDEX `workflow_executions_status_idx`(`status`),
    INDEX `workflow_executions_trigger_type_idx`(`trigger_type`),
    INDEX `workflow_executions_created_at_idx`(`created_at`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `workflow_execution_steps` (
    `id` VARCHAR(191) NOT NULL,
    `execution_id` VARCHAR(191) NOT NULL,
    `step_index` INTEGER NOT NULL,
    `step_name` VARCHAR(191) NOT NULL,
    `step_type` ENUM('ACTION', 'CONDITION', 'APPROVAL', 'NOTIFICATION', 'DELAY', 'PARALLEL', 'LOOP', 'SUBPROCESS', 'SCRIPT', 'HTTP_REQUEST', 'DATABASE_OPERATION', 'FILE_OPERATION') NOT NULL,
    `status` ENUM('PENDING', 'RUNNING', 'COMPLETED', 'FAILED', 'SKIPPED', 'RETRYING') NOT NULL DEFAULT 'PENDING',
    `config` JSON NOT NULL,
    `input` JSON NULL,
    `output` JSON NULL,
    `started_at` DATETIME(3) NULL,
    `completed_at` DATETIME(3) NULL,
    `execution_time` DOUBLE NULL,
    `result` JSON NULL,
    `error` TEXT NULL,
    `logs` JSON NULL,
    `retry_count` INTEGER NOT NULL DEFAULT 0,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,

    INDEX `workflow_execution_steps_execution_id_idx`(`execution_id`),
    INDEX `workflow_execution_steps_status_idx`(`status`),
    UNIQUE INDEX `workflow_execution_steps_execution_id_step_index_key`(`execution_id`, `step_index`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `workflow_templates` (
    `id` VARCHAR(191) NOT NULL,
    `name` VARCHAR(191) NOT NULL,
    `description` TEXT NULL,
    `category` ENUM('SERVICE_AUTOMATION', 'SLA_MONITORING', 'ALERT_PROCESSING', 'BACKUP_AUTOMATION', 'MAINTENANCE', 'APPROVAL_PROCESS', 'NOTIFICATION', 'DATA_PROCESSING', 'INTEGRATION', 'CUSTOM') NOT NULL DEFAULT 'SERVICE_AUTOMATION',
    `tags` JSON NULL,
    `template_config` JSON NOT NULL,
    `variables` JSON NULL,
    `requirements` JSON NULL,
    `usage_count` INTEGER NOT NULL DEFAULT 0,
    `rating` DOUBLE NULL,
    `is_official` BOOLEAN NOT NULL DEFAULT false,
    `is_active` BOOLEAN NOT NULL DEFAULT true,
    `created_by` VARCHAR(191) NOT NULL,
    `updated_by` VARCHAR(191) NULL,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL,

    UNIQUE INDEX `workflow_templates_name_key`(`name`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `ai_analysis_requests` ADD CONSTRAINT `ai_analysis_requests_user_id_fkey` FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `ai_feedback` ADD CONSTRAINT `ai_feedback_request_id_fkey` FOREIGN KEY (`request_id`) REFERENCES `ai_analysis_requests`(`request_id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `ai_feedback` ADD CONSTRAINT `ai_feedback_user_id_fkey` FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `ai_prompt_templates` ADD CONSTRAINT `ai_prompt_templates_created_by_fkey` FOREIGN KEY (`created_by`) REFERENCES `users`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `ai_prompt_templates` ADD CONSTRAINT `ai_prompt_templates_updated_by_fkey` FOREIGN KEY (`updated_by`) REFERENCES `users`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `workflow_definitions` ADD CONSTRAINT `workflow_definitions_created_by_fkey` FOREIGN KEY (`created_by`) REFERENCES `users`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `workflow_definitions` ADD CONSTRAINT `workflow_definitions_updated_by_fkey` FOREIGN KEY (`updated_by`) REFERENCES `users`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `workflow_executions` ADD CONSTRAINT `workflow_executions_workflow_id_fkey` FOREIGN KEY (`workflow_id`) REFERENCES `workflow_definitions`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `workflow_executions` ADD CONSTRAINT `workflow_executions_triggered_by_fkey` FOREIGN KEY (`triggered_by`) REFERENCES `users`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `workflow_execution_steps` ADD CONSTRAINT `workflow_execution_steps_execution_id_fkey` FOREIGN KEY (`execution_id`) REFERENCES `workflow_executions`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `workflow_templates` ADD CONSTRAINT `workflow_templates_created_by_fkey` FOREIGN KEY (`created_by`) REFERENCES `users`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `workflow_templates` ADD CONSTRAINT `workflow_templates_updated_by_fkey` FOREIGN KEY (`updated_by`) REFERENCES `users`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;
