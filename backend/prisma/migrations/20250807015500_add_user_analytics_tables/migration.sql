-- CreateTable
CREATE TABLE `user_sessions` (
    `id` VARCHAR(191) NOT NULL,
    `user_id` VARCHAR(191) NOT NULL,
    `session_token` VARCHAR(191) NOT NULL,
    `ip_address` VARCHAR(45) NULL,
    `user_agent` TEXT NULL,
    `location` <PERSON>SON NULL,
    `device_info` JSON NULL,
    `is_active` BOOLEAN NOT NULL DEFAULT true,
    `last_activity` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `login_time` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `logout_time` DATETIME(3) NULL,
    `duration_minutes` INTEGER NULL,
    `activity_score` FLOAT NULL DEFAULT 0,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),

    UNIQUE INDEX `user_sessions_session_token_key`(`session_token`),
    INDEX `idx_user_sessions_user_id`(`user_id`),
    INDEX `idx_user_sessions_is_active`(`is_active`),
    INDEX `idx_user_sessions_last_activity`(`last_activity`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `user_activity_stats` (
    `id` VARCHAR(191) NOT NULL,
    `user_id` VARCHAR(191) NOT NULL,
    `date` DATE NOT NULL,
    `login_count` INTEGER NOT NULL DEFAULT 0,
    `session_count` INTEGER NOT NULL DEFAULT 0,
    `total_duration_minutes` INTEGER NOT NULL DEFAULT 0,
    `operation_count` INTEGER NOT NULL DEFAULT 0,
    `page_views` INTEGER NOT NULL DEFAULT 0,
    `activity_score` FLOAT NOT NULL DEFAULT 0,
    `peak_activity_hour` INTEGER NULL,
    `features_used` JSON NULL,
    `last_activity` DATETIME(3) NULL,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),

    UNIQUE INDEX `user_activity_stats_user_id_date_key`(`user_id`, `date`),
    INDEX `idx_user_activity_stats_date`(`date`),
    INDEX `idx_user_activity_stats_activity_score`(`activity_score`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `user_behavior_patterns` (
    `id` VARCHAR(191) NOT NULL,
    `user_id` VARCHAR(191) NOT NULL,
    `pattern_type` ENUM('LOGIN_TIME', 'ACTIVITY_DURATION', 'FEATURE_USAGE', 'OPERATION_FREQUENCY') NOT NULL,
    `pattern_data` JSON NOT NULL,
    `confidence_score` FLOAT NOT NULL DEFAULT 0,
    `detected_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `last_updated` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
    `status` ENUM('ACTIVE', 'INACTIVE', 'ANOMALY') NOT NULL DEFAULT 'ACTIVE',
    `metadata` JSON NULL,

    UNIQUE INDEX `user_behavior_patterns_user_id_pattern_type_key`(`user_id`, `pattern_type`),
    INDEX `idx_user_behavior_patterns_pattern_type`(`pattern_type`),
    INDEX `idx_user_behavior_patterns_status`(`status`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `user_activity_anomalies` (
    `id` VARCHAR(191) NOT NULL,
    `user_id` VARCHAR(191) NOT NULL,
    `anomaly_type` ENUM('UNUSUAL_TIME', 'EXCESSIVE_OPERATIONS', 'ABNORMAL_LOCATION', 'SUSPICIOUS_BEHAVIOR') NOT NULL,
    `description` TEXT NOT NULL,
    `severity` ENUM('LOW', 'MEDIUM', 'HIGH', 'CRITICAL') NOT NULL DEFAULT 'MEDIUM',
    `detected_value` JSON NOT NULL,
    `normal_range` JSON NULL,
    `confidence_score` FLOAT NOT NULL DEFAULT 0,
    `is_resolved` BOOLEAN NOT NULL DEFAULT false,
    `resolved_at` DATETIME(3) NULL,
    `resolved_by` VARCHAR(191) NULL,
    `notes` TEXT NULL,
    `detected_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),

    INDEX `idx_user_activity_anomalies_user_id`(`user_id`),
    INDEX `idx_user_activity_anomalies_anomaly_type`(`anomaly_type`),
    INDEX `idx_user_activity_anomalies_severity`(`severity`),
    INDEX `idx_user_activity_anomalies_is_resolved`(`is_resolved`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `user_feature_usage` (
    `id` VARCHAR(191) NOT NULL,
    `user_id` VARCHAR(191) NOT NULL,
    `feature_name` VARCHAR(191) NOT NULL,
    `usage_count` INTEGER NOT NULL DEFAULT 0,
    `last_used` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `date` DATE NOT NULL,
    `avg_time_spent` FLOAT NULL DEFAULT 0,
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updated_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),

    UNIQUE INDEX `user_feature_usage_user_id_feature_name_date_key`(`user_id`, `feature_name`, `date`),
    INDEX `idx_user_feature_usage_feature_name`(`feature_name`),
    INDEX `idx_user_feature_usage_date`(`date`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `user_sessions` ADD CONSTRAINT `user_sessions_user_id_fkey` FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `user_activity_stats` ADD CONSTRAINT `user_activity_stats_user_id_fkey` FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `user_behavior_patterns` ADD CONSTRAINT `user_behavior_patterns_user_id_fkey` FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `user_activity_anomalies` ADD CONSTRAINT `user_activity_anomalies_user_id_fkey` FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `user_activity_anomalies` ADD CONSTRAINT `user_activity_anomalies_resolved_by_fkey` FOREIGN KEY (`resolved_by`) REFERENCES `users`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `user_feature_usage` ADD CONSTRAINT `user_feature_usage_user_id_fkey` FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;