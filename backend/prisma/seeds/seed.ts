import { PrismaClient } from "@prisma/client";
import { hashPassword } from "../../src/utils/crypto.util";

const prisma = new PrismaClient();

async function main() {
  console.log("🌱 开始播种数据...");

  // 创建默认角色
  const adminRole = await prisma.role.upsert({
    where: { name: "admin" },
    update: {},
    create: {
      name: "admin",
      description: "系统管理员",
      permissions: JSON.stringify([
        "admin:all",
        "user:read",
        "user:write",
        "role:read",
        "role:write",
        "audit:read",
        "customer:read",
        "customer:write",
        "archive:read",
        "archive:write",
        "service:read",
        "service:write",
        "config:read",
        "config:write",
      ]),
    },
  });

  const engineerRole = await prisma.role.upsert({
    where: { name: "engineer" },
    update: {},
    create: {
      name: "engineer",
      description: "运维工程师",
      permissions: JSON.stringify([
        "customer:read",
        "archive:read",
        "archive:write",
        "service:read",
        "service:write",
        "config:read",
        "config:write",
        "role:read",
      ]),
    },
  });

  const customerServiceRole = await prisma.role.upsert({
    where: { name: "customer_service" },
    update: {},
    create: {
      name: "customer_service",
      description: "客户服务",
      permissions: JSON.stringify([
        "customer:read",
        "customer:write",
        "archive:read",
        "service:read",
        "service:write",
      ]),
    },
  });

  console.log("✅ 角色创建完成");

  // 创建默认管理员用户
  const hashedPassword = await hashPassword("admin123");

  const adminUser = await prisma.user.upsert({
    where: { username: "admin" },
    update: {},
    create: {
      username: "admin",
      email: "<EMAIL>",
      password: hashedPassword,
      fullName: "系统管理员",
      status: "ACTIVE",
    },
  });

  // 分配管理员角色
  await prisma.userRole.upsert({
    where: {
      userId_roleId: {
        userId: adminUser.id,
        roleId: adminRole.id,
      },
    },
    update: {},
    create: {
      userId: adminUser.id,
      roleId: adminRole.id,
    },
  });

  console.log("✅ 管理员用户创建完成");

  // 创建默认演示 API Key（供外部SDK演示使用）
  // 与 sdk/demo/index.html 的默认值保持一致
  const demoApiKeyValue = "ak_demo1234567890abcdef";
  await prisma.apiKey.upsert({
    where: { keyValue: demoApiKeyValue },
    update: {},
    create: {
      name: "Demo Key",
      keyValue: demoApiKeyValue,
      systemId: "demo-system",
      description: "SDK 演示用 API Key",
      status: "ACTIVE",
      createdBy: adminUser.id,
    },
  });
  console.log("✅ 演示 API Key 创建完成: ak_demo1234567890abcdef");

  // 创建示例客户
  const customer1 = await prisma.customer.upsert({
    where: { code: "CUST-DEMO-001" },
    update: {},
    create: {
      name: "示例科技有限公司",
      code: "CUST-DEMO-001",
      company: "示例科技有限公司",
      industry: "互联网",
      level: "STANDARD",
      contactPerson: "张三",
      contactEmail: "<EMAIL>",
      contactPhone: "13800138000",
      address: "北京市朝阳区示例大厦",
      description: "一家专注于互联网技术的公司",
      createdBy: adminUser.id,
    },
  });

  // 创建客户联系人
  await prisma.customerContact.create({
    data: {
      customerId: customer1.id,
      name: "张三",
      position: "技术总监",
      email: "<EMAIL>",
      phone: "13800138000",
      isPrimary: true,
    },
  });

  await prisma.customerContact.create({
    data: {
      customerId: customer1.id,
      name: "李四",
      position: "运维经理",
      email: "<EMAIL>",
      phone: "13800138001",
      isPrimary: false,
    },
  });

  console.log("✅ 示例客户创建完成");

  // 创建示例项目档案
  const archive1 = await prisma.projectArchive.create({
    data: {
      customerId: customer1.id,
      name: "企业官网系统",
      description: "公司官方网站，包含产品展示、新闻资讯、联系我们等功能",
      technology: "React.js + Node.js + MySQL",
      environment: "阿里云 ECS + RDS",
      version: "v2.1.0",
      deploymentDate: new Date("2024-01-15"),
      status: "ACTIVE",
      createdBy: adminUser.id,
    },
  });

  // 创建项目配置
  await prisma.projectConfiguration.create({
    data: {
      archiveId: archive1.id,
      configType: "SERVER",
      title: "生产服务器配置",
      configData: JSON.stringify({
        host: "47.xxx.xxx.xxx",
        port: 22,
        username: "root",
        password: "encrypted_password_here",
      }),
      encryptedFields: JSON.stringify(["password"]),
      description: "生产环境服务器SSH连接配置",
      isActive: true,
    },
  });

  await prisma.projectConfiguration.create({
    data: {
      archiveId: archive1.id,
      configType: "DATABASE",
      title: "生产数据库配置",
      configData: JSON.stringify({
        host: "rm-xxxxx.mysql.rds.aliyuncs.com",
        port: 3306,
        database: "company_website",
        username: "admin",
        password: "encrypted_db_password_here",
      }),
      encryptedFields: JSON.stringify(["password"]),
      description: "生产环境MySQL数据库连接配置",
      isActive: true,
    },
  });

  console.log("✅ 示例项目档案创建完成");

  // 创建SLA模板
  const standardSla = await prisma.slaTemplate.create({
    data: {
      name: "标准SLA",
      description: "标准客户的服务等级协议",
      responseTime: 60, // 1小时响应
      resolutionTime: 24, // 24小时解决
      availability: 99.5,
    },
  });

  const premiumSla = await prisma.slaTemplate.create({
    data: {
      name: "高级SLA",
      description: "高级客户的服务等级协议",
      responseTime: 30, // 30分钟响应
      resolutionTime: 8, // 8小时解决
      availability: 99.9,
    },
  });

  console.log("✅ SLA模板创建完成");

  // 创建示例服务工单
  const service1 = await prisma.service.upsert({
    where: { ticketNumber: "OPS-20240101-001" },
    update: {},
    create: {
      archiveId: archive1.id,
      slaTemplateId: standardSla.id,
      ticketNumber: "OPS-20240101-001",
      title: "网站首页加载缓慢问题",
      description: "用户反馈网站首页加载时间过长，影响用户体验，需要优化性能。",
      category: "SUPPORT",
      priority: "HIGH",
      status: "PENDING",
      customerContact: "张三",
      estimatedHours: 4.0,
      createdBy: adminUser.id,
    },
  });

  // 创建工作日志 - 跳过如果已存在
  try {
    await prisma.serviceWorkLog.create({
      data: {
        serviceId: service1.id,
        userId: adminUser.id,
        description: "分析网站性能瓶颈，发现数据库查询效率较低",
        workHours: 2.0,
        workDate: new Date(),
        category: "ANALYSIS",
      },
    });
  } catch (error) {
    // 忽略重复创建错误
  }

  // 创建服务评论 - 跳过如果已存在
  try {
    await prisma.serviceComment.create({
      data: {
        serviceId: service1.id,
        content:
          "已经定位到问题，主要是数据库查询没有使用索引，计划优化SQL语句。",
        isInternal: true,
        authorId: adminUser.id,
      },
    });
  } catch (error) {
    // 忽略重复创建错误
  }

  console.log("✅ 示例服务工单创建完成");

  // 创建通知模板
  await prisma.notificationTemplate.create({
    data: {
      name: "工单创建通知",
      type: "EMAIL",
      subject: "新工单创建 - {{ticketNumber}}",
      content: `
        您好 {{customerName}}，

        您的服务工单已成功创建：

        工单号：{{ticketNumber}}
        标题：{{title}}
        优先级：{{priority}}
        创建时间：{{createdAt}}

        我们将尽快处理您的请求。

        运维服务团队
      `,
      variables: JSON.stringify([
        "customerName",
        "ticketNumber",
        "title",
        "priority",
        "createdAt",
      ]),
    },
  });

  await prisma.notificationTemplate.create({
    data: {
      name: "工单完成通知",
      type: "EMAIL",
      subject: "工单已完成 - {{ticketNumber}}",
      content: `
        您好 {{customerName}}，

        您的服务工单已完成处理：

        工单号：{{ticketNumber}}
        标题：{{title}}
        处理结果：{{resolution}}
        完成时间：{{completedAt}}

        如有问题，请及时与我们联系。

        运维服务团队
      `,
      variables: JSON.stringify([
        "customerName",
        "ticketNumber",
        "title",
        "resolution",
        "completedAt",
      ]),
    },
  });

  console.log("✅ 通知模板创建完成");

  // 初始化系统配置
  const systemConfigs = [
    // 基本配置
    {
      category: 'GENERAL',
      key: 'SITE_NAME',
      value: '运维服务管理系统',
      description: '网站名称',
      dataType: 'STRING',
      isSystem: true,
      isPublic: true,
      displayOrder: 1
    },
    {
      category: 'GENERAL',
      key: 'SITE_DESCRIPTION', 
      value: '专业的运维服务管理平台',
      description: '网站描述',
      dataType: 'STRING',
      isSystem: true,
      isPublic: true,
      displayOrder: 2
    },
    {
      category: 'GENERAL',
      key: 'COMPANY_NAME',
      value: '多协云科技有限公司',
      description: '公司名称',
      dataType: 'STRING',
      isSystem: true,
      isPublic: true,
      displayOrder: 3
    },
    {
      category: 'GENERAL',
      key: 'DEFAULT_TIMEZONE',
      value: 'Asia/Shanghai',
      description: '默认时区',
      dataType: 'STRING',
      isSystem: true,
      isPublic: true,
      displayOrder: 4
    },
    {
      category: 'GENERAL',
      key: 'DEFAULT_LANGUAGE',
      value: 'zh-CN',
      description: '默认语言',
      dataType: 'STRING',
      isSystem: true,
      isPublic: true,
      displayOrder: 5
    },
    
    // 安全配置
    {
      category: 'SECURITY',
      key: 'PASSWORD_MIN_LENGTH',
      value: 8,
      description: '密码最小长度',
      dataType: 'NUMBER',
      isSystem: true,
      isPublic: false,
      displayOrder: 1
    },
    {
      category: 'SECURITY',
      key: 'PASSWORD_REQUIRE_UPPERCASE',
      value: true,
      description: '密码是否需要大写字母',
      dataType: 'BOOLEAN',
      isSystem: true,
      isPublic: false,
      displayOrder: 2
    },
    {
      category: 'SECURITY',
      key: 'MAX_LOGIN_ATTEMPTS',
      value: 5,
      description: '最大登录尝试次数',
      dataType: 'NUMBER',
      isSystem: true,
      isPublic: false,
      displayOrder: 3
    },
    {
      category: 'SECURITY',
      key: 'SESSION_TIMEOUT',
      value: 7200,
      description: '会话超时时间(秒)',
      dataType: 'NUMBER',
      isSystem: true,
      isPublic: false,
      displayOrder: 4
    },
    
    // 邮件配置
    {
      category: 'EMAIL',
      key: 'EMAIL_ENABLED',
      value: false,
      description: '是否启用邮件发送',
      dataType: 'BOOLEAN',
      isSystem: true,
      isPublic: false,
      displayOrder: 1
    },
    {
      category: 'EMAIL',
      key: 'SMTP_HOST',
      value: '',
      description: 'SMTP服务器地址',
      dataType: 'STRING',
      isSystem: true,
      isPublic: false,
      displayOrder: 2
    },
    {
      category: 'EMAIL',
      key: 'SMTP_PORT',
      value: 587,
      description: 'SMTP端口',
      dataType: 'NUMBER',
      isSystem: true,
      isPublic: false,
      displayOrder: 3
    },
    {
      category: 'EMAIL',
      key: 'SMTP_SECURE',
      value: false,
      description: '是否使用SSL',
      dataType: 'BOOLEAN',
      isSystem: true,
      isPublic: false,
      displayOrder: 4
    },
    {
      category: 'EMAIL',
      key: 'SMTP_USER',
      value: '',
      description: 'SMTP用户名',
      dataType: 'STRING',
      isSystem: true,
      isPublic: false,
      displayOrder: 5
    },
    {
      category: 'EMAIL',
      key: 'SMTP_PASS',
      value: '',
      description: 'SMTP密码',
      dataType: 'PASSWORD',
      isSystem: true,
      isPublic: false,
      isEncrypted: true,
      displayOrder: 6
    },
    {
      category: 'EMAIL',
      key: 'SENDER_NAME',
      value: '运维服务管理系统',
      description: '发件人名称',
      dataType: 'STRING',
      isSystem: true,
      isPublic: false,
      displayOrder: 7
    },
    {
      category: 'EMAIL',
      key: 'SENDER_EMAIL',
      value: '',
      description: '发件人邮箱',
      dataType: 'EMAIL',
      isSystem: true,
      isPublic: false,
      displayOrder: 8
    },
    
    // 短信配置
    {
      category: 'SMS',
      key: 'SMS_ENABLED',
      value: false,
      description: '是否启用短信发送',
      dataType: 'BOOLEAN',
      isSystem: true,
      isPublic: false,
      displayOrder: 1
    },
    {
      category: 'SMS',
      key: 'ALI_SMS_ACCESS_KEY_ID',
      value: '',
      description: '阿里云AccessKey ID',
      dataType: 'STRING',
      isSystem: true,
      isPublic: false,
      displayOrder: 2
    },
    {
      category: 'SMS',
      key: 'ALI_SMS_ACCESS_KEY_SECRET',
      value: '',
      description: '阿里云AccessKey Secret',
      dataType: 'PASSWORD',
      isSystem: true,
      isPublic: false,
      isEncrypted: true,
      displayOrder: 3
    },
    {
      category: 'SMS',
      key: 'ALI_SMS_REGION',
      value: 'cn-hangzhou',
      description: '阿里云短信服务区域',
      dataType: 'STRING',
      isSystem: true,
      isPublic: false,
      displayOrder: 4
    },
    {
      category: 'SMS',
      key: 'ALI_SMS_SIGN_NAME',
      value: '',
      description: '短信签名',
      dataType: 'STRING',
      isSystem: true,
      isPublic: false,
      displayOrder: 5
    },
    
    // 存储配置
    {
      category: 'STORAGE',
      key: 'UPLOAD_MAX_SIZE',
      value: 10485760, // 10MB
      description: '文件上传最大大小(字节)',
      dataType: 'NUMBER',
      isSystem: true,
      isPublic: true,
      displayOrder: 1
    },
    {
      category: 'STORAGE',
      key: 'UPLOAD_ALLOWED_TYPES',
      value: ['image/*', 'application/pdf', 'text/*', 'application/msword', 'application/vnd.openxmlformats-officedocument.*'],
      description: '允许上传的文件类型',
      dataType: 'JSON',
      isSystem: true,
      isPublic: true,
      displayOrder: 2
    },
    {
      category: 'STORAGE',
      key: 'STORAGE_PATH',
      value: './uploads',
      description: '文件存储路径',
      dataType: 'STRING',
      isSystem: true,
      isPublic: false,
      displayOrder: 3
    },
    
    // 系统配置
    {
      category: 'SYSTEM',
      key: 'SYSTEM_MAINTENANCE_MODE',
      value: false,
      description: '系统维护模式',
      dataType: 'BOOLEAN',
      isSystem: true,
      isPublic: true,
      displayOrder: 1
    },
    {
      category: 'SYSTEM',
      key: 'MAINTENANCE_MESSAGE',
      value: '系统正在维护中，请稍后再试',
      description: '维护模式提示信息',
      dataType: 'TEXTAREA',
      isSystem: true,
      isPublic: true,
      displayOrder: 2
    },
    {
      category: 'SYSTEM',
      key: 'DEBUG_MODE',
      value: false,
      description: '调试模式',
      dataType: 'BOOLEAN',
      isSystem: true,
      isPublic: false,
      displayOrder: 3
    },
    {
      category: 'SYSTEM',
      key: 'LOG_LEVEL',
      value: 'info',
      description: '日志级别',
      dataType: 'SELECT',
      isSystem: true,
      isPublic: false,
      validationRule: {
        options: [
          { label: 'Error', value: 'error' },
          { label: 'Warn', value: 'warn' },
          { label: 'Info', value: 'info' },
          { label: 'Debug', value: 'debug' }
        ]
      },
      displayOrder: 4
    },
    {
      category: 'SYSTEM',
      key: 'CACHE_TTL',
      value: 300,
      description: '缓存过期时间(秒)',
      dataType: 'NUMBER',
      isSystem: true,
      isPublic: false,
      displayOrder: 5
    }
  ];

  // 创建系统配置
  for (const config of systemConfigs) {
    await prisma.systemConfig.upsert({
      where: {
        key: config.key
      },
      update: {},
      create: {
        ...config,
        defaultValue: config.value,
        updatedBy: adminUser.id
      } as any
    });
  }

  console.log("✅ 系统配置初始化完成");

  // 初始化系统事件示例数据（仅在为空时创建，便于前端日志模块演示）
  try {
    const existingEvents = await prisma.systemEvent.count();
    if (existingEvents === 0) {
      await prisma.systemEvent.createMany({
        data: [
          {
            level: 'info',
            type: 'system',
            message: '系统启动完成，服务已就绪',
            details: { source: 'bootstrap' } as any,
          },
          {
            level: 'warn',
            type: 'performance',
            message: 'CPU 使用率短时上升，已接近预警阈值',
            details: { cpu: 78 } as any,
          },
          {
            level: 'error',
            type: 'service',
            message: '外部接口请求失败，重试已触发',
            details: { service: 'third-party-api', retry: true } as any,
          },
        ],
      });
      console.log('✅ 系统事件示例创建完成');
    }
  } catch (e) {
    console.warn('⚠️ 创建系统事件示例失败（可能是表未迁移）:', e instanceof Error ? e.message : e);
  }

  // 6. 创建邮件模板
  try {
    const adminUser = await prisma.user.findFirst({ where: { username: 'admin' } });
    if (adminUser) {
      console.log('⏳ 创建邮件模板...');
      
      const emailTemplatesData = [
        {
          name: '系统告警通知',
          type: 'ALERT',
          category: 'SYSTEM',
          subject: '【系统告警】{{alertLevel}} - {{alertTitle}}',
          content: `
<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
  <h2 style="color: #dc3545;">🚨 系统告警通知</h2>
  <div style="background: #f8d7da; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #dc3545;">
    <p><strong>告警级别：</strong><span style="color: #dc3545;">{{alertLevel}}</span></p>
    <p><strong>告警标题：</strong>{{alertTitle}}</p>
    <p><strong>告警内容：</strong>{{alertMessage}}</p>
    <p><strong>发生时间：</strong>{{timestamp}}</p>
    <p><strong>影响范围：</strong>{{affectedSystem}}</p>
  </div>
  <p style="color: #dc3545; font-weight: bold;">请立即检查系统状态并采取必要措施！</p>
  <hr style="border: none; border-top: 1px solid #dee2e6; margin: 20px 0;">
  <p style="color: #6c757d; font-size: 12px;">此邮件由运维服务管理系统自动发送 | {{timestamp}}</p>
</div>`,
          description: '系统检测到异常情况时发送的告警通知邮件',
          variables: ['alertLevel', 'alertTitle', 'alertMessage', 'timestamp', 'affectedSystem'],
          enabled: true,
          isSystem: true,
          createdBy: adminUser.id,
          updatedBy: adminUser.id,
        },
        {
          name: '服务单创建通知',
          type: 'SERVICE_CREATED',
          category: 'BUSINESS',
          subject: '【新服务单】{{serviceTitle}} - {{serviceNumber}}',
          content: `
<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
  <h2 style="color: #28a745;">📋 新服务单创建</h2>
  <div style="background: #d4edda; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #28a745;">
    <p><strong>服务单号：</strong>{{serviceNumber}}</p>
    <p><strong>服务标题：</strong>{{serviceTitle}}</p>
    <p><strong>优先级：</strong><span style="color: {{priorityColor}};">{{priority}}</span></p>
    <p><strong>客户：</strong>{{customerName}}</p>
    <p><strong>创建时间：</strong>{{createdAt}}</p>
    <p><strong>预计完成：</strong>{{expectedCompletion}}</p>
  </div>
  <div style="background: #f8f9fa; padding: 15px; border-radius: 8px;">
    <h4 style="margin-top: 0;">服务描述：</h4>
    <p>{{serviceDescription}}</p>
  </div>
  <p>请及时处理该服务单，如有疑问请联系客户或项目负责人。</p>
  <div style="text-align: center; margin: 20px 0;">
    <a href="{{serviceUrl}}" style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">查看服务单详情</a>
  </div>
  <hr style="border: none; border-top: 1px solid #dee2e6; margin: 20px 0;">
  <p style="color: #6c757d; font-size: 12px;">此邮件由运维服务管理系统自动发送 | {{timestamp}}</p>
</div>`,
          description: '创建新服务单时发送给相关人员的通知邮件',
          variables: ['serviceNumber', 'serviceTitle', 'priority', 'priorityColor', 'customerName', 'createdAt', 'expectedCompletion', 'serviceDescription', 'serviceUrl', 'timestamp'],
          enabled: true,
          isSystem: true,
          createdBy: adminUser.id,
          updatedBy: adminUser.id,
        },
        {
          name: '服务单解决通知',
          type: 'SERVICE_RESOLVED',
          category: 'BUSINESS',
          subject: '【服务单已解决】{{serviceTitle}} - {{serviceNumber}}',
          content: `
<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
  <h2 style="color: #28a745;">✅ 服务单已解决</h2>
  <div style="background: #d4edda; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #28a745;">
    <p><strong>服务单号：</strong>{{serviceNumber}}</p>
    <p><strong>服务标题：</strong>{{serviceTitle}}</p>
    <p><strong>解决时间：</strong>{{resolvedAt}}</p>
    <p><strong>处理人员：</strong>{{assigneeName}}</p>
    <p><strong>耗时：</strong>{{duration}}</p>
  </div>
  <div style="background: #f8f9fa; padding: 15px; border-radius: 8px;">
    <h4 style="margin-top: 0;">解决方案：</h4>
    <p>{{solution}}</p>
  </div>
  <p>该服务单已经成功解决，如有任何问题请及时联系我们。</p>
  <div style="text-align: center; margin: 20px 0;">
    <a href="{{serviceUrl}}" style="background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">查看解决详情</a>
  </div>
  <hr style="border: none; border-top: 1px solid #dee2e6; margin: 20px 0;">
  <p style="color: #6c757d; font-size: 12px;">此邮件由运维服务管理系统自动发送 | {{timestamp}}</p>
</div>`,
          description: '服务单解决后发送给客户的通知邮件',
          variables: ['serviceNumber', 'serviceTitle', 'resolvedAt', 'assigneeName', 'duration', 'solution', 'serviceUrl', 'timestamp'],
          enabled: true,
          isSystem: true,
          createdBy: adminUser.id,
          updatedBy: adminUser.id,
        },
        {
          name: '系统维护通知',
          type: 'MAINTENANCE',
          category: 'SYSTEM',
          subject: '【系统维护】计划维护通知 - {{maintenanceDate}}',
          content: `
<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
  <h2 style="color: #ffc107;">🔧 系统维护通知</h2>
  <div style="background: #fff3cd; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #ffc107;">
    <p><strong>维护时间：</strong>{{maintenanceDate}} {{maintenanceTime}}</p>
    <p><strong>预计时长：</strong>{{estimatedDuration}}</p>
    <p><strong>维护范围：</strong>{{maintenanceScope}}</p>
    <p><strong>影响程度：</strong><span style="color: {{impactColor}};">{{impact}}</span></p>
  </div>
  <div style="background: #f8f9fa; padding: 15px; border-radius: 8px;">
    <h4 style="margin-top: 0;">维护内容：</h4>
    <p>{{maintenanceContent}}</p>
  </div>
  <div style="background: #e2e3e5; padding: 15px; border-radius: 8px; margin: 15px 0;">
    <h4 style="margin-top: 0; color: #495057;">注意事项：</h4>
    <ul>
      <li>维护期间可能出现服务中断或访问异常</li>
      <li>请提前保存重要数据</li>
      <li>如有紧急情况，请联系技术支持</li>
      <li>维护完成后将发送通知邮件</li>
    </ul>
  </div>
  <p>感谢您的理解与配合！</p>
  <hr style="border: none; border-top: 1px solid #dee2e6; margin: 20px 0;">
  <p style="color: #6c757d; font-size: 12px;">此邮件由运维服务管理系统自动发送 | {{timestamp}}</p>
</div>`,
          description: '系统计划维护时发送给用户的通知邮件',
          variables: ['maintenanceDate', 'maintenanceTime', 'estimatedDuration', 'maintenanceScope', 'impact', 'impactColor', 'maintenanceContent', 'timestamp'],
          enabled: true,
          isSystem: true,
          createdBy: adminUser.id,
          updatedBy: adminUser.id,
        }
      ];

      for (const templateData of emailTemplatesData) {
        await prisma.emailTemplate.upsert({
          where: { 
            name_type: {
              name: templateData.name,
              type: templateData.type as any
            }
          },
          update: {},
          create: templateData as any
        });
      }
      
      console.log('✅ 邮件模板创建完成');
    }
  } catch (e) {
    console.warn('⚠️ 创建邮件模板失败（可能是表未迁移）:', e instanceof Error ? e.message : e);
  }

  // 7. 初始化邮件配置
  try {
    const adminUser = await prisma.user.findFirst({ where: { username: 'admin' } });
    if (adminUser) {
      console.log('⏳ 初始化邮件配置...');
      
      const emailConfigs = [
        {
          key: 'SMTP_HOST',
          value: 'smtp.gmail.com',
          description: 'SMTP服务器地址',
          category: 'EMAIL',
          dataType: 'STRING',
          isEncrypted: false,
          isPublic: false,
          isSystem: true,
          updatedBy: adminUser.id,
        },
        {
          key: 'SMTP_PORT',
          value: 587,
          description: 'SMTP服务器端口',
          category: 'EMAIL',
          dataType: 'NUMBER',
          isEncrypted: false,
          isPublic: false,
          isSystem: true,
          updatedBy: adminUser.id,
        },
        {
          key: 'SMTP_SECURE',
          value: false,
          description: 'SMTP是否使用SSL',
          category: 'EMAIL',
          dataType: 'BOOLEAN',
          isEncrypted: false,
          isPublic: false,
          isSystem: true,
          updatedBy: adminUser.id,
        },
        {
          key: 'SMTP_USER',
          value: '',
          description: 'SMTP用户名/邮箱',
          category: 'EMAIL',
          dataType: 'EMAIL',
          isEncrypted: false,
          isPublic: false,
          isSystem: true,
          updatedBy: adminUser.id,
        },
        {
          key: 'SMTP_PASS',
          value: '',
          description: 'SMTP密码/应用专用密码',
          category: 'EMAIL',
          dataType: 'PASSWORD',
          isEncrypted: true,
          isPublic: false,
          isSystem: true,
          updatedBy: adminUser.id,
        },
        {
          key: 'SENDER_NAME',
          value: '运维服务管理系统',
          description: '发件人名称',
          category: 'EMAIL',
          dataType: 'STRING',
          isEncrypted: false,
          isPublic: false,
          isSystem: true,
          updatedBy: adminUser.id,
        },
        {
          key: 'SENDER_EMAIL',
          value: '',
          description: '发件人邮箱',
          category: 'EMAIL',
          dataType: 'EMAIL',
          isEncrypted: false,
          isPublic: false,
          isSystem: true,
          updatedBy: adminUser.id,
        },
        {
          key: 'EMAIL_ENABLED',
          value: false,
          description: '是否启用邮件功能',
          category: 'EMAIL',
          dataType: 'BOOLEAN',
          isEncrypted: false,
          isPublic: false,
          isSystem: true,
          updatedBy: adminUser.id,
        }
      ];

      for (const configData of emailConfigs) {
        await prisma.systemConfig.upsert({
          where: { key: configData.key },
          update: {},
          create: configData as any
        });
      }
      
      console.log('✅ 邮件配置初始化完成');
    }
  } catch (e) {
    console.warn('⚠️ 初始化邮件配置失败:', e instanceof Error ? e.message : e);
  }

  console.log("🎉 数据播种完成！");
  console.log(`
    默认管理员账号:
    用户名: admin
    密码: admin123
    邮箱: <EMAIL>
    
    📧 邮件配置说明:
    - 请在系统配置页面中配置真实的SMTP信息
    - 配置完成后邮件功能将自动启用
    - Gmail用户需要使用应用专用密码
  `);
}

main()
  .catch((e) => {
    console.error("❌ 播种数据时出错:", e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
