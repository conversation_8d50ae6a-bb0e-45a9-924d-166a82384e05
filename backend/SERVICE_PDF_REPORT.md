# 工单PDF报告导出功能

## 🎯 功能概述

专业的单工单PDF报告导出系统，遵循ITIL 4标准，为每个工单生成详细的服务报告文档。

## 📋 报告内容

### 1. 报告封面
- 公司Logo和品牌标识
- 工单编号和标题
- 生成时间和版本信息

### 2. 工单概览
- 基本信息（标题、状态、优先级、类别）
- 时间信息（创建、完成时间）
- 人员信息（负责工程师、创建人）
- 问题描述和解决方案（富文本渲染）

### 3. 客户信息
- 客户基本信息
- 联系人详情
- 客户等级

### 4. SLA绩效指标
- 响应时间达成情况
- 解决时间达成情况
- 风险等级评估
- 进度条可视化

### 5. 统计概要
- 总工时统计
- 沟通记录数量
- 相关附件统计
- 客户满意度评分

### 6. 详细处理时间轴
- 工单生命周期完整记录
- 时间节点可视化
- 操作人员信息

### 7. 工作日志详情
- 详细工作记录
- 工时统计
- 工程师工作内容

### 8. 沟通记录
- 客户沟通历史
- 内部备注记录
- 时间排序展示

### 9. 相关附件清单
- 附件列表
- 上传时间和人员
- 文件大小信息

### 10. 客户反馈
- 客户反馈内容
- 满意度评分
- 意见建议

## 🔧 技术实现

### 后端架构
```
ServiceReportService
├── generateServiceReport()     // 主入口函数
├── collectServiceData()        // 数据收集
├── renderTemplate()           // 模板渲染
├── generatePDF()              // PDF生成
└── buildTimeline()            // 时间轴构建
```

### 技术栈
- **Puppeteer**: PDF生成引擎
- **Handlebars**: HTML模板引擎
- **Moment.js**: 时间格式化
- **Prisma**: 数据库ORM

### PDF模板特点
- 企业级视觉设计
- A4页面优化
- 专业色彩搭配
- 清晰的信息层级
- 富文本安全渲染

## 🚀 使用方法

### API调用
```
GET /api/v1/services/{id}/export/pdf
Authorization: Bearer {token}
```

### 前端集成
在工单详情页面点击"导出PDF"按钮，系统会：
1. 验证用户权限
2. 收集工单完整数据
3. 生成专业PDF报告
4. 自动下载到本地

### 权限控制
- 需要 `service:read` 权限
- 只有相关人员可导出（创建人、负责人、管理员）
- 导出操作会记录审计日志

## 📄 文件输出

### 文件命名格式
```
service-report-{工单编号}-{日期}.pdf
例如: service-report-OPS-2025-001-2025-01-18.pdf
```

### PDF特性
- A4标准尺寸
- 高质量打印
- 页眉页脚信息
- 页码显示
- 公司品牌标识

## 🛡️ 安全特性

### 内容安全
- HTML内容安全清理
- XSS攻击防护
- 危险标签过滤

### 权限验证
- 身份认证检查
- 权限级别验证
- 操作日志记录

### 数据保护
- 敏感信息脱敏
- 访问控制严格
- 审计追踪完整

## 🎨 定制选项

### 公司品牌
在 `ServiceReportService` 中修改公司信息：
```typescript
const company = {
  name: '您的公司名称',
  logo: '/assets/your-logo.png',
  address: '公司地址',
  phone: '联系电话',
  website: '公司网站'
}
```

### 模板样式
修改 `service-report.hbs` 模板文件的CSS样式：
- 颜色主题
- 字体设置
- 布局结构
- 页面边距

### 内容配置
根据需要调整报告章节：
- 显示/隐藏特定内容
- 调整信息详细程度
- 自定义统计指标

## 📊 性能优化

### 生成速度
- 异步处理，不阻塞用户操作
- 数据查询优化
- 模板缓存机制

### 资源管理
- PDF文件压缩
- 浏览器资源清理
- 内存使用优化

### 错误处理
- 完整的错误捕获
- 用户友好的错误提示
- 详细的日志记录

## 🔮 扩展计划

### 功能扩展
- [ ] 多语言支持
- [ ] 批量导出功能
- [ ] 自定义模板选择
- [ ] Excel格式导出
- [ ] 邮件自动发送

### 性能优化
- [ ] PDF生成缓存
- [ ] 模板预编译
- [ ] 图表动态生成
- [ ] 分页大数据处理

## 📞 技术支持

如有问题或建议，请联系技术团队：
- 邮箱：<EMAIL>
- 文档：查看系统帮助文档
- 日志：检查后端服务日志

---

*该功能基于ITIL 4服务管理最佳实践设计，确保输出的报告符合行业标准和企业规范。*
