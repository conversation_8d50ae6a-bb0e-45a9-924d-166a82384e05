{"name": "ops-management-backend", "version": "1.0.0", "description": "运维服务管理系统后端API", "main": "dist/index.js", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "type-check": "tsc --noEmit", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:reset": "prisma migrate reset", "db:seed": "tsx prisma/seeds/seed.ts", "db:studio": "prisma studio"}, "keywords": ["ops", "management", "api", "node.js"], "author": "", "license": "ISC", "dependencies": {"@prisma/client": "^5.16.1", "@types/bcrypt": "^6.0.0", "@types/exceljs": "^1.3.2", "@types/node-cron": "^3.0.11", "@types/xlsx": "^0.0.35", "bcrypt": "^6.0.0", "bcryptjs": "^2.4.3", "canvas": "^3.1.2", "chart.js": "^4.5.0", "chartjs-node-canvas": "^5.0.0", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.4.5", "exceljs": "^4.4.0", "express": "^4.19.2", "express-rate-limit": "^7.3.1", "handlebars": "^4.7.8", "helmet": "^7.1.0", "html-to-text": "^9.0.5", "ioredis": "^5.4.1", "js-yaml": "^4.1.0", "jsonwebtoken": "^9.0.2", "moment": "^2.30.1", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "node-cron": "^4.2.1", "node-os-utils": "^1.3.7", "node-system-stats": "^2.0.5", "nodemailer": "^6.9.14", "puppeteer": "^24.16.0", "redis": "^5.8.0", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "systeminformation": "^5.27.7", "ws": "^8.17.1", "xlsx": "^0.18.5", "zod": "^3.23.8"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cookie-parser": "^1.4.9", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.12", "@types/js-yaml": "^4.0.9", "@types/jsonwebtoken": "^9.0.6", "@types/morgan": "^1.9.9", "@types/multer": "^1.4.11", "@types/node": "^20.14.9", "@types/nodemailer": "^6.4.15", "@types/supertest": "^6.0.3", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.6", "@types/uuid": "^10.0.0", "@types/ws": "^8.5.10", "@typescript-eslint/eslint-plugin": "^7.15.0", "@typescript-eslint/parser": "^7.15.0", "eslint": "^8.57.0", "jest": "^29.7.0", "prisma": "^5.16.1", "supertest": "^7.1.4", "ts-jest": "^29.1.5", "tsx": "^4.16.0", "typescript": "^5.5.2"}, "prisma": {"seed": "tsx prisma/seeds/seed.ts"}}