-- MySQL dump 10.13  Distrib 8.0.27, for macos11 (x86_64)
--
-- Host: 1********    Database: ops_management
-- ------------------------------------------------------
-- Server version	8.4.6

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `_prisma_migrations`
--

DROP TABLE IF EXISTS `_prisma_migrations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `_prisma_migrations` (
  `id` varchar(36) COLLATE utf8mb4_unicode_ci NOT NULL,
  `checksum` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL,
  `finished_at` datetime(3) DEFAULT NULL,
  `migration_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `logs` text COLLATE utf8mb4_unicode_ci,
  `rolled_back_at` datetime(3) DEFAULT NULL,
  `started_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `applied_steps_count` int unsigned NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `_prisma_migrations`
--

LOCK TABLES `_prisma_migrations` WRITE;
/*!40000 ALTER TABLE `_prisma_migrations` DISABLE KEYS */;
INSERT INTO `_prisma_migrations` VALUES ('04c42e41-4ace-432f-8d8a-190fe7e87a59','2c869da40941ff91ee4d6798bcda8b4d6e391d9dc5284ab2700c783e2fdacd41','2025-08-10 04:32:58.849','20250810043258_add_task_execution_model',NULL,NULL,'2025-08-10 04:32:58.727',1),('139e929f-c3f2-4869-a7fb-ea7d5905661a','302163d715bff1b63ab69ee2fee24445a5ddad966379b4ccb3958e95cb9f9b66','2025-08-09 03:42:17.461','20250807012426_add_system_config_management',NULL,NULL,'2025-08-09 03:42:17.300',1),('49525039-dd73-40eb-bb57-b107252e05f8','659b2030222f4509a487be54ba59f74da82ef44e2c5cdaa022116cd14c38028b','2025-08-09 03:42:17.188','20250803034526_add_customer_fields',NULL,NULL,'2025-08-09 03:42:17.113',1),('7c1febad-8433-40e8-a591-fb72549da050','32cc8128a7b3f24fc2636ec46f3661940f244da1d6fb20e2154c1d76da80e92e','2025-08-09 03:42:17.111','20250803015203_init',NULL,NULL,'2025-08-09 03:42:15.793',1),('7f241380-d91b-45fc-9ade-0459a429bc8b','c4bacbe79be01b13aa9a9f660b6294f7ce1769f346452212ba80fd9350829c5e','2025-08-09 05:29:33.983','20250809052933_add_email_templates',NULL,NULL,'2025-08-09 05:29:33.858',1),('a7515301-7602-4ee3-90e5-8a3bff1445e2','db06c443ace45d10a4ca02b05f53cf11507891bbbe6ee92c10306d40e340b70d','2025-08-09 03:42:17.212','20250803134504_remove_customer_email_phone_fields',NULL,NULL,'2025-08-09 03:42:17.190',1),('b3bdd339-1a5f-4e51-80d9-d39a7d8ed94a','6558573929c12ed04aa558b5aa6dc2fe8278df202909b45baecfb72b72015744','2025-08-09 03:42:17.299','20250805152636_add_service_operation_history',NULL,NULL,'2025-08-09 03:42:17.213',1),('c31c09f6-8c19-49c9-a05f-459006d21d9c','c55450779fdd17563f79ffe35ff29a12289d3ed0ee5f21d946968d128cf68522','2025-08-09 03:44:12.196','20250809034411_add_system_events',NULL,NULL,'2025-08-09 03:44:11.486',1),('d8b8e43b-60f1-48b8-840c-fc8599f2c901','3d75168400bd01d9b8c71bc03dec602adabc8b7893fc9010c773c44452c137c3','2025-08-09 03:42:17.659','20250807015500_add_user_analytics_tables',NULL,NULL,'2025-08-09 03:42:17.462',1);
/*!40000 ALTER TABLE `_prisma_migrations` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ai_analysis_requests`
--

DROP TABLE IF EXISTS `ai_analysis_requests`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `ai_analysis_requests` (
  `id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `request_id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `user_id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `context_data` json DEFAULT NULL,
  `provider` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `model` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `prompt` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `response` json DEFAULT NULL,
  `suggestions` json DEFAULT NULL,
  `processing_time` int DEFAULT NULL,
  `success` tinyint(1) NOT NULL DEFAULT '0',
  `error` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  PRIMARY KEY (`id`),
  UNIQUE KEY `ai_analysis_requests_request_id_key` (`request_id`),
  KEY `ai_analysis_requests_user_id_fkey` (`user_id`),
  CONSTRAINT `ai_analysis_requests_user_id_fkey` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ai_analysis_requests`
--

LOCK TABLES `ai_analysis_requests` WRITE;
/*!40000 ALTER TABLE `ai_analysis_requests` DISABLE KEYS */;
INSERT INTO `ai_analysis_requests` VALUES ('cme5io2id0007kijjs6787aji','req_1754820249778_2','cme3pnoma0003r8cva4fu9log','客户投诉产品功能不完善，希望增加批量导出功能','{\"userAgent\": \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)\", \"impactScope\": \"single_user\", \"customerTier\": \"Standard\"}','anthropic','claude-3-sonnet','根据工单描述进行分类...','{\"category\": \"产品问题\", \"reasoning\": \"客户明确提出功能改进需求\", \"confidence\": 0.88, \"subcategory\": \"新功能请求\"}','{\"title\": \"批量导出功能需求\", \"category\": \"产品问题\", \"priority\": \"MEDIUM\"}',980,1,NULL,'2025-08-10 10:04:09.781'),('cme5io2id000akijj108fgkp5','req_1754820249778_3','cme3pnoma0003r8cva4fu9log','系统出现500错误，无法访问','{\"errorCode\": \"500\", \"timestamp\": \"2025-08-10T10:04:09.778Z\", \"customerTier\": \"Enterprise\"}','openai','gpt-4','评估工单优先级...','null','null',NULL,0,'API调用超时','2025-08-10 10:04:09.781'),('cme5io2id000bkijjicbbet4g','req_1754820249777_1','cme3pnoma0003r8cva4fu9log','用户反馈系统登录缓慢，页面加载时间超过10秒，影响正常使用','{\"userAgent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36\", \"impactScope\": \"multiple_users\", \"customerTier\": \"VIP\"}','openai','gpt-4','请分析以下工单内容...','{\"category\": \"技术支持\", \"priority\": \"HIGH\", \"confidence\": 0.92, \"estimatedTime\": \"4小时\", \"requiredSkills\": [\"数据库优化\", \"性能调优\"], \"suggestedActions\": [\"检查数据库连接池\", \"优化查询性能\", \"检查服务器负载\"]}','{\"title\": \"系统登录性能问题\", \"category\": \"技术支持\", \"priority\": \"HIGH\", \"slaTemplate\": \"VIP客户4小时响应\"}',1250,1,NULL,'2025-08-10 10:04:09.781'),('cme5iyv980001wposy6bilgpq','ai_1754820752539_rcdiqlh','cme3pnoma0003r8cva4fu9log','多租户支持','{\"customerId\": \"cme3pnon80007r8cvn6kummym\", \"customerType\": \"ENTERPRISE\"}','unknown','unknown','','null','null',1055,0,'AI API错误: 401 Unauthorized','2025-08-10 10:12:33.595'),('cme5j45wc0006wpos73wxdpp7','ai_1754820999915_zd3xpl2','cme3pnoma0003r8cva4fu9log','用户反馈系统登录缓慢，页面加载时间超过10秒，影响正常使用','{\"industry\": \"technology\", \"customerId\": \"test-customer\", \"customerType\": \"VIP\"}','unknown','unknown','','null','null',751,0,'AI API错误: 401 Unauthorized','2025-08-10 10:16:40.668'),('cme5j5zpo0001b2nkxvemtaiu','ai_1754821084255_va4fjv3','cme3pnoma0003r8cva4fu9log','系统出现严重故障，数据库连接异常，用户无法登录，需要紧急处理','{\"industry\": \"finance\", \"customerId\": \"vip-customer-001\", \"customerType\": \"VIP\"}','unknown','unknown','','null','null',1704,0,'AI API错误: 401 Unauthorized','2025-08-10 10:18:05.962'),('cme5j6avx0005b2nk3y3tzwe0','ai_1754821099479_51kbh54','cme3pnoma0003r8cva4fu9log','希望咨询一下系统升级的最佳实践和建议方案','{\"industry\": \"retail\", \"customerId\": \"standard-customer-001\", \"customerType\": \"Standard\"}','unknown','unknown','','null','null',965,0,'AI API错误: 401 Unauthorized','2025-08-10 10:18:20.445'),('cme5l000g0007hpp9rh4k9ajl','ai_1754824164812_z6ovuac','cme3pnoma0003r8cva4fu9log','测试AI分析功能，这是一个紧急的系统故障问题','{}','unknown','unknown','','null','null',831,0,'AI API错误: 401 Unauthorized','2025-08-10 11:09:25.647'),('cme5l1dxv000bhpp9zcioat6o','ai_1754824229372_phmoyl7','cme3pnoma0003r8cva4fu9log','Cursor 的启动速度和响应速度基本上是和 vscode 一个级别的。但字节的产品经理和程序员们似乎为了自己的 KPI，自己多加了一些特效或者多套了一层壳。然而，这些人显然没有微软的水平，因而做出来的东西基本上和常见的 electron 应用是一样卡的，占用内存也显著多于 vscode/cursor。卡一点，在一些轻量化的日常应用中还好，但在编程中就难以忍受了。','{\"customerId\": \"cme3pnon80007r8cvn6kummym\", \"customerType\": \"ENTERPRISE\"}','unknown','unknown','','null','null',981,0,'AI API错误: 401 Unauthorized','2025-08-10 11:10:30.355'),('cme5ldqj7000ghpp9c4nfzuuw','ai_1754824805120_d3o0ykw','cme3pnoma0003r8cva4fu9log','Cursor 的启动速度和响应速度基本上是和 vscode 一个级别的。但字节的产品经理和程序员们似乎为了自己的 KPI，自己多加了一些特效或者多套了一层壳。然而，这些人显然没有微软的水平，因而做出来的东西基本上和常见的 electron 应用是一样卡的，占用内存也显著多于 vscode/cursor。卡一点，在一些轻量化的日常应用中还好，但在编程中就难以忍受了。','{\"customerId\": \"cme3pnon80007r8cvn6kummym\", \"customerType\": \"ENTERPRISE\"}','unknown','unknown','','null','null',1426,0,'AI API错误: 401 Unauthorized','2025-08-10 11:20:06.548'),('cme5lk8an000khpp9tmzebdla','ai_1754825108771_ypek0ao','cme3pnoma0003r8cva4fu9log','但字节的前端和产品经理没理解什么叫做尽可能的集约空间，如何有效地在尽可能小的屏幕下塞更多内容。Trae 的每个元素与元素、窗口与窗口之间都做了大量的留白，浪费了非常多的、本可以用来展示内容的像素空间。在 Trae 上，笔记本基本上只能开一个代码窗口和一个 AI 对话窗口，这严重降低了编程体验。\n\n作者：胡一鸣\n链接：https://www.zhihu.com/question/12511871108/answer/1919458711082086766\n来源：知乎\n著作权归作者所有。商业转载请联系作者获得授权，非商业转载请注明出处。','{\"customerId\": \"cme3pnon80007r8cvn6kummym\", \"customerType\": \"ENTERPRISE\"}','unknown','unknown','','null','null',731,0,'AI API错误: 401 Unauthorized','2025-08-10 11:25:09.503'),('cme5m49in0001ig6ycypeoqbq','ai_1754826043788_dxf3yx6','cme3pnoma0003r8cva4fu9log','通过设置 getValueLength 属性可以自定义计算字符串长度。搭配 maxLength 和 minLength 可以支持 emoji 长度按照可见长度计算。\n传入 getValueLength 时，Semi 内部做了什么：','{\"customerId\": \"cme3pnon80007r8cvn6kummym\", \"customerType\": \"ENTERPRISE\"}','unknown','unknown','','null','null',415,0,'AI API错误: 401 Unauthorized','2025-08-10 11:40:44.206'),('cme5m97vw0001yigvn0h6lit3','ai_1754826275119_b95c461','cme3pnoma0003r8cva4fu9log','通过设置 getValueLength 属性可以自定义计算字符串长度。搭配 maxLength 和 minLength 可以支持 emoji 长度按照可见长度计算。\n传入 getValueLength 时，Semi 内部做了什么：','{\"customerId\": \"cme3pnon80007r8cvn6kummym\", \"customerType\": \"ENTERPRISE\"}','unknown','unknown','','null','null',250,0,'AI API错误: 401 Unauthorized','2025-08-10 11:44:35.370'),('cme5mc6ee000134lewivl8jt7','ai_1754826406144_xwwnxtu','cme3pnoma0003r8cva4fu9log','通过设置 getValueLength 属性可以自定义计算字符串长度。搭配 maxLength 和 minLength 可以支持 emoji 长度按照可见长度计算。\n传入 getValueLength 时，Semi 内部做了什么：','{\"customerId\": \"cme3pnon80007r8cvn6kummym\", \"customerType\": \"ENTERPRISE\"}','openai','deepseek-ai/DeepSeek-R1-Distill-Qwen-7B','作为专业的IT运维工单分析助手，请分析以下工单内容并返回JSON格式的分类建议。\n\n工单描述：通过设置 getValueLength 属性可以自定义计算字符串长度。搭配 maxLength 和 minLength 可以支持 emoji 长度按照可见长度计算。\n传入 getValueLength 时，Semi 内部做了什么：\n客户信息：ENTERPRISE - 未知\n历史模式：无历史模式\n\n请返回以下格式的JSON响应：\n{\n  \"title\": {\"suggested\": \"标题\", \"confidence\": 数字},\n  \"category\": {\"suggested\": \"类别\", \"confidence\": 数字, \"reasoning\": \"理由\"},\n  \"priority\": {\"suggested\": \"优先级\", \"confidence\": 数字, \"reasoning\": \"理由\"},\n  \"slaTemplate\": {\"suggested\": \"SLA模板\", \"confidence\": 数字, \"reasoning\": \"理由\"}\n}\n\n类别选项：MAINTENANCE, SUPPORT, UPGRADE, BUGFIX, CONSULTING, MONITORING\n优先级选项：LOW, MEDIUM, HIGH, URGENT','{\"id\": \"019893cdf9cfd9303b4524c81f143bae\", \"model\": \"deepseek-ai/DeepSeek-R1-Distill-Qwen-7B\", \"usage\": {\"total_tokens\": 443, \"prompt_tokens\": 261, \"completion_tokens\": 182}, \"object\": \"chat.completion\", \"choices\": [{\"index\": 0, \"message\": {\"role\": \"assistant\", \"content\": \"{\\n\\n  \\\"title\\\": {\\\"suggested\\\": \\\"通过设置 getValueLength 属性自定义字符串长度计算\\\", \\\"confidence\\\": 95},\\n  \\\"category\\\": {\\\"suggested\\\": \\\"SUPPORT\\\", \\\"confidence\\\": 90, \\\"reasoning\\\": \\\"该工单涉及对字符串长度的自定义计算，可能与字符串处理功能相关，属于支持类问题。\\\"},\\n  \\\"priority\\\": {\\\"suggested\\\": \\\"MEDIUM\\\", \\\"confidence\\\": 85, \\\"reasoning\\\": \\\"该工单涉及字符串处理功能的调整，可能会影响用户体验或系统性能，属于中等优先级。\\\"},\\n  \\\"slaTemplate\\\": {\\\"suggested\\\": \\\"字符串处理功能维护\\\", \\\"confidence\\\": 90, \\\"reasoning\\\": \\\"该工单可能涉及字符串处理功能的维护或优化，建议归类到字符串处理功能维护的SLA模板下。\\\"}\\n}\"}, \"finish_reason\": \"stop\"}], \"created\": 1754826406, \"system_fingerprint\": \"\"}','[{\"field\": \"title\", \"reasoning\": \"基于内容分析生成的标题建议\", \"suggested\": \"通过设置 getValueLength 属性自定义字符串长度计算\", \"confidence\": 95}, {\"field\": \"category\", \"reasoning\": \"该工单涉及对字符串长度的自定义计算，可能与字符串处理功能相关，属于支持类问题。\", \"suggested\": \"SUPPORT\", \"confidence\": 90}, {\"field\": \"priority\", \"reasoning\": \"该工单涉及字符串处理功能的调整，可能会影响用户体验或系统性能，属于中等优先级。\", \"suggested\": \"MEDIUM\", \"confidence\": 85}, {\"field\": \"slaTemplate\", \"reasoning\": \"该工单可能涉及字符串处理功能的维护或优化，建议归类到字符串处理功能维护的SLA模板下。\", \"suggested\": \"字符串处理功能维护\", \"confidence\": 90}]',7267,1,NULL,'2025-08-10 11:46:53.414'),('cme5mfws9000534le6wqil5ne','ai_1754826580084_rynxsnz','cme3pnoma0003r8cva4fu9log','系统中有没有数据，联系售后给你看看后台','{\"customerId\": \"cme3pnon80007r8cvn6kummym\", \"customerType\": \"ENTERPRISE\"}','openai','deepseek-ai/DeepSeek-R1-Distill-Qwen-7B','作为专业的IT运维工单分析助手，请分析以下工单内容并返回JSON格式的分类建议。\n\n工单描述：系统中有没有数据，联系售后给你看看后台\n客户信息：ENTERPRISE - 未知\n历史模式：无历史模式\n\n请返回以下格式的JSON响应：\n{\n  \"title\": {\"suggested\": \"标题\", \"confidence\": 数字},\n  \"category\": {\"suggested\": \"类别\", \"confidence\": 数字, \"reasoning\": \"理由\"},\n  \"priority\": {\"suggested\": \"优先级\", \"confidence\": 数字, \"reasoning\": \"理由\"},\n  \"slaTemplate\": {\"suggested\": \"SLA模板\", \"confidence\": 数字, \"reasoning\": \"理由\"}\n}\n\n类别选项：MAINTENANCE, SUPPORT, UPGRADE, BUGFIX, CONSULTING, MONITORING\n优先级选项：LOW, MEDIUM, HIGH, URGENT','{\"id\": \"019893d0a100b74be8543a8c7ae5cdad\", \"model\": \"deepseek-ai/DeepSeek-R1-Distill-Qwen-7B\", \"usage\": {\"total_tokens\": 386, \"prompt_tokens\": 226, \"completion_tokens\": 160}, \"object\": \"chat.completion\", \"choices\": [{\"index\": 0, \"message\": {\"role\": \"assistant\", \"content\": \"{\\n\\n  \\\"title\\\": {\\\"suggested\\\": \\\"系统数据查询\\\", \\\"confidence\\\": 0.8},\\n  \\\"category\\\": {\\\"suggested\\\": \\\"SUPPORT\\\", \\\"confidence\\\": 0.9, \\\"reasoning\\\": \\\"用户希望了解系统数据是否存在，这属于支持类问题，需要联系售后进行详细检查.\\\"},\\n  \\\"priority\\\": {\\\"suggested\\\": \\\"MEDIUM\\\", \\\"confidence\\\": 0.7, \\\"reasoning\\\": \\\"用户可能需要立即了解系统状态，但问题本身不需要立即解决，因此中等优先级.\\\"},\\n  \\\"slaTemplate\\\": {\\\"suggested\\\": \\\"常规技术支持服务\\\", \\\"confidence\\\": 0.8, \\\"reasoning\\\": \\\"用户需要技术支持，但问题不涉及紧急情况，因此适用常规技术支持服务模板.\\\"}\\n}\"}, \"finish_reason\": \"stop\"}], \"created\": 1754826580, \"system_fingerprint\": \"\"}','[{\"field\": \"title\", \"reasoning\": \"基于内容分析生成的标题建议\", \"suggested\": \"系统数据查询\", \"confidence\": 0.8}, {\"field\": \"category\", \"reasoning\": \"用户希望了解系统数据是否存在，这属于支持类问题，需要联系售后进行详细检查.\", \"suggested\": \"SUPPORT\", \"confidence\": 0.9}, {\"field\": \"priority\", \"reasoning\": \"用户可能需要立即了解系统状态，但问题本身不需要立即解决，因此中等优先级.\", \"suggested\": \"MEDIUM\", \"confidence\": 0.7}, {\"field\": \"slaTemplate\", \"reasoning\": \"用户需要技术支持，但问题不涉及紧急情况，因此适用常规技术支持服务模板.\", \"suggested\": \"常规技术支持服务\", \"confidence\": 0.8}]',7490,1,NULL,'2025-08-10 11:49:47.576'),('cme5miq2r000112bdzjr22nva','ai_1754826718589_9g9b6xt','cme3pnoma0003r8cva4fu9log','非技术人员，想靠这个编辑器开发商用软件，我觉得就更不可能了。对于开发人员来说，又有点鸡肋，甚至会耽误事。我觉得让 AI 去解释代码，生成代码片段，提出优化建议还是可以的','{\"customerId\": \"cme3pnon80007r8cvn6kummym\", \"customerType\": \"ENTERPRISE\"}','unknown','unknown','','null','null',254,0,'AI API错误: 401 Unauthorized','2025-08-10 11:51:58.845'),('cme5mke230001ar4qkexjci2i','ai_1754826784920_1xondr8','cme3pnoma0003r8cva4fu9log','作为开发人员，我尝试后觉得并不好用。生成的东西有 bug ，有时候还有编译错误，需要花时间去调整，而且生成后的 ui 并不是很好看，想让它继续调整细节很费劲，有时候看回复是听懂了，但是实际上没效果。反正，我是不得不暂时先放弃了。','{\"customerId\": \"cme3pnon80007r8cvn6kummym\", \"customerType\": \"ENTERPRISE\"}','openai','deepseek-ai/DeepSeek-R1-Distill-Qwen-7B','作为专业的IT运维工单分析助手，请分析以下工单内容并返回JSON格式的分类建议。\n\n工单描述：作为开发人员，我尝试后觉得并不好用。生成的东西有 bug ，有时候还有编译错误，需要花时间去调整，而且生成后的 ui 并不是很好看，想让它继续调整细节很费劲，有时候看回复是听懂了，但是实际上没效果。反正，我是不得不暂时先放弃了。\n客户信息：ENTERPRISE - 未知\n历史模式：无历史模式\n\n请返回以下格式的JSON响应：\n{\n  \"title\": {\"suggested\": \"标题\", \"confidence\": 数字},\n  \"category\": {\"suggested\": \"类别\", \"confidence\": 数字, \"reasoning\": \"理由\"},\n  \"priority\": {\"suggested\": \"优先级\", \"confidence\": 数字, \"reasoning\": \"理由\"},\n  \"slaTemplate\": {\"suggested\": \"SLA模板\", \"confidence\": 数字, \"reasoning\": \"理由\"}\n}\n\n类别选项：MAINTENANCE, SUPPORT, UPGRADE, BUGFIX, CONSULTING, MONITORING\n优先级选项：LOW, MEDIUM, HIGH, URGENT','{\"id\": \"019893d3c16ad798c77142daed01f1a5\", \"model\": \"deepseek-ai/DeepSeek-R1-Distill-Qwen-7B\", \"usage\": {\"total_tokens\": 548, \"prompt_tokens\": 284, \"completion_tokens\": 264}, \"object\": \"chat.completion\", \"choices\": [{\"index\": 0, \"message\": {\"role\": \"assistant\", \"content\": \"{\\n\\n  \\\"title\\\": {\\\"suggested\\\": \\\"开发人员使用生成工具的体验反馈\\\", \\\"confidence\\\": 95},\\n  \\\"category\\\": {\\\"suggested\\\": \\\"BUGFIX\\\", \\\"confidence\\\": 90, \\\"reasoning\\\": \\\"用户反馈中提到了生成的工具存在bug和编译错误，这些问题直接影响了使用体验，属于功能上的缺陷需要修复。同时用户也提到了UI的美观性问题，虽然UI问题可能属于功能范畴，但用户明确表示需要调整细节，因此将UI问题归类为BUGFIX更为合适。\\\"},\\n  \\\"priority\\\": {\\\"suggested\\\": \\\"MEDIUM\\\", \\\"confidence\\\": 80, \\\"reasoning\\\": \\\"用户明确表示需要暂时放弃使用该工具，说明问题影响了他们的工作效率和体验，但问题不是特别紧急，需要进一步的修复和优化。\\\"},\\n  \\\"slaTemplate\\\": {\\\"suggested\\\": \\\"立即修复已知bug并提供UI优化的计划\\\", \\\"confidence\\\": 85, \\\"reasoning\\\": \\\"根据用户反馈，首先需要修复生成工具的bug和编译错误，确保用户能够顺利使用。同时，UI的美观性问题也需要尽快解决，以提升用户体验。因此，SLA模板应包括立即修复bug和UI优化的计划。\\\"}\\n}\"}, \"finish_reason\": \"stop\"}], \"created\": 1754826785, \"system_fingerprint\": \"\"}','[{\"field\": \"title\", \"reasoning\": \"基于内容分析生成的标题建议\", \"suggested\": \"开发人员使用生成工具的体验反馈\", \"confidence\": 95}, {\"field\": \"category\", \"reasoning\": \"用户反馈中提到了生成的工具存在bug和编译错误，这些问题直接影响了使用体验，属于功能上的缺陷需要修复。同时用户也提到了UI的美观性问题，虽然UI问题可能属于功能范畴，但用户明确表示需要调整细节，因此将UI问题归类为BUGFIX更为合适。\", \"suggested\": \"BUGFIX\", \"confidence\": 90}, {\"field\": \"priority\", \"reasoning\": \"用户明确表示需要暂时放弃使用该工具，说明问题影响了他们的工作效率和体验，但问题不是特别紧急，需要进一步的修复和优化。\", \"suggested\": \"MEDIUM\", \"confidence\": 80}, {\"field\": \"slaTemplate\", \"reasoning\": \"根据用户反馈，首先需要修复生成工具的bug和编译错误，确保用户能够顺利使用。同时，UI的美观性问题也需要尽快解决，以提升用户体验。因此，SLA模板应包括立即修复bug和UI优化的计划。\", \"suggested\": \"立即修复已知bug并提供UI优化的计划\", \"confidence\": 85}]',11661,1,NULL,'2025-08-10 11:53:16.585'),('cme5mo3qu0007ar4qqgcvtw24','ai_1754826964781_wczakz1','cme3pnoma0003r8cva4fu9log','本地开发驱动完成后推送到线上环境进行验证，出问题返回修改、打包、再推送验证，反反复复，浪费时间','{\"customerId\": \"cme3pnon80007r8cvn6kummym\", \"customerType\": \"ENTERPRISE\"}','openai','deepseek-ai/DeepSeek-R1-Distill-Qwen-7B','作为专业的IT运维工单分析助手，请分析以下工单内容并返回JSON格式的分类建议。\n\n工单描述：本地开发驱动完成后推送到线上环境进行验证，出问题返回修改、打包、再推送验证，反反复复，浪费时间\n客户信息：ENTERPRISE - 未知\n历史模式：无历史模式\n\n请返回以下格式的JSON响应：\n{\n  \"title\": {\"suggested\": \"标题\", \"confidence\": 数字},\n  \"category\": {\"suggested\": \"类别\", \"confidence\": 数字, \"reasoning\": \"理由\"},\n  \"priority\": {\"suggested\": \"优先级\", \"confidence\": 数字, \"reasoning\": \"理由\"},\n  \"slaTemplate\": {\"suggested\": \"SLA模板\", \"confidence\": 数字, \"reasoning\": \"理由\"}\n}\n\n类别选项：MAINTENANCE, SUPPORT, UPGRADE, BUGFIX, CONSULTING, MONITORING\n优先级选项：LOW, MEDIUM, HIGH, URGENT','{\"id\": \"019893d67ff5b1f3f187f0ded817cac6\", \"model\": \"deepseek-ai/DeepSeek-R1-Distill-Qwen-7B\", \"usage\": {\"total_tokens\": 444, \"prompt_tokens\": 244, \"completion_tokens\": 200}, \"object\": \"chat.completion\", \"choices\": [{\"index\": 0, \"message\": {\"role\": \"assistant\", \"content\": \"{\\n\\n  \\\"title\\\": {\\\"suggested\\\": \\\"本地开发驱动验证流程反复，影响线上环境验证效率\\\", \\\"confidence\\\": 95},\\n  \\\"category\\\": {\\\"suggested\\\": \\\"BUGFIX\\\", \\\"confidence\\\": 95, \\\"reasoning\\\": \\\"该工单描述了在本地开发驱动完成后，推送到线上环境进行验证时出现的问题，需要修改、打包后再推送验证，导致反反复复，浪费时间。这表明问题与软件修复或错误修复相关，属于BUGFIX类别。\\\"},\\n  \\\"priority\\\": {\\\"suggested\\\": \\\"MEDIUM\\\", \\\"confidence\\\": 90, \\\"reasoning\\\": \\\"虽然问题影响了线上环境验证效率，但问题本身是软件修复问题，属于中等优先级。\\\"},\\n  \\\"slaTemplate\\\": {\\\"suggested\\\": \\\"修复验证流程，提高线上环境验证效率\\\", \\\"confidence\\\": 90, \\\"reasoning\\\": \\\"修复验证流程，提高线上环境验证效率。\\\"}\\n}\"}, \"finish_reason\": \"stop\"}], \"created\": 1754826965, \"system_fingerprint\": \"\"}','[{\"field\": \"title\", \"reasoning\": \"基于内容分析生成的标题建议\", \"suggested\": \"本地开发驱动验证流程反复，影响线上环境验证效率\", \"confidence\": 95}, {\"field\": \"category\", \"reasoning\": \"该工单描述了在本地开发驱动完成后，推送到线上环境进行验证时出现的问题，需要修改、打包后再推送验证，导致反反复复，浪费时间。这表明问题与软件修复或错误修复相关，属于BUGFIX类别。\", \"suggested\": \"BUGFIX\", \"confidence\": 95}, {\"field\": \"priority\", \"reasoning\": \"虽然问题影响了线上环境验证效率，但问题本身是软件修复问题，属于中等优先级。\", \"suggested\": \"MEDIUM\", \"confidence\": 90}, {\"field\": \"slaTemplate\", \"reasoning\": \"修复验证流程，提高线上环境验证效率。\", \"suggested\": \"修复验证流程，提高线上环境验证效率\", \"confidence\": 90}]',5061,1,NULL,'2025-08-10 11:56:09.846');
/*!40000 ALTER TABLE `ai_analysis_requests` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ai_configurations`
--

DROP TABLE IF EXISTS `ai_configurations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `ai_configurations` (
  `id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `provider` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `model` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `api_key` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `temperature` double NOT NULL DEFAULT '0.3',
  `max_tokens` int NOT NULL DEFAULT '1000',
  `timeout` int NOT NULL DEFAULT '30000',
  `mode` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'USER_TRIGGERED',
  `enabled_fields` json NOT NULL,
  `auto_fill_threshold` double NOT NULL DEFAULT '0.8',
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `created_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `updated_at` datetime(3) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ai_configurations`
--

LOCK TABLES `ai_configurations` WRITE;
/*!40000 ALTER TABLE `ai_configurations` DISABLE KEYS */;
INSERT INTO `ai_configurations` VALUES ('ai-config-1','openai','gpt-4','sk-lopnegzdjiyoykifpfthtkkpqenppwkdbzeeqgqigtkbwnvb',0.3,4000,30000,'USER_TRIGGERED','{\"title\": true, \"category\": true, \"priority\": true, \"slaTemplate\": true}',0.8,0,'2025-08-10 10:04:09.689','2025-08-10 10:04:09.689'),('ai-config-2','anthropic','claude-3-sonnet','sk-test-key-anthropic-encrypted',0.5,4000,30000,'USER_TRIGGERED','{\"title\": true, \"category\": true, \"priority\": true, \"slaTemplate\": false}',0.7,0,'2025-08-10 10:04:09.689','2025-08-10 10:04:09.689'),('ai-config-3','gemini','gemini-pro','test-key-gemini-encrypted',0.7,2048,25000,'DISABLED','{\"title\": true, \"category\": false, \"priority\": true, \"slaTemplate\": false}',0.6,0,'2025-08-10 10:04:09.689','2025-08-10 10:04:09.689'),('cme59i40g0000u0ptg32h0hk3','openai','deepseek-ai/DeepSeek-R1-Distill-Qwen-7B','sk-lopnegzdjiyoykifpfthtkkpqenppwkdbzeeqgqigtkbwnvb',0.3,1000,30000,'USER_TRIGGERED','{\"title\": true, \"category\": true, \"priority\": true, \"slaTemplate\": true}',0.8,1,'2025-08-10 05:47:35.247','2025-08-10 05:47:35.247');
/*!40000 ALTER TABLE `ai_configurations` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ai_feedback`
--

DROP TABLE IF EXISTS `ai_feedback`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `ai_feedback` (
  `id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `request_id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `user_id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `rating` int NOT NULL,
  `helpful` tinyint(1) DEFAULT NULL,
  `adopted` json DEFAULT NULL,
  `comments` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  PRIMARY KEY (`id`),
  KEY `ai_feedback_request_id_fkey` (`request_id`),
  KEY `ai_feedback_user_id_fkey` (`user_id`),
  CONSTRAINT `ai_feedback_request_id_fkey` FOREIGN KEY (`request_id`) REFERENCES `ai_analysis_requests` (`request_id`) ON DELETE RESTRICT ON UPDATE CASCADE,
  CONSTRAINT `ai_feedback_user_id_fkey` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ai_feedback`
--

LOCK TABLES `ai_feedback` WRITE;
/*!40000 ALTER TABLE `ai_feedback` DISABLE KEYS */;
/*!40000 ALTER TABLE `ai_feedback` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `ai_prompt_templates`
--

DROP TABLE IF EXISTS `ai_prompt_templates`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `ai_prompt_templates` (
  `id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `category` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `template` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `variables` json DEFAULT NULL,
  `provider` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `version` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '1.0',
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `created_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `updated_at` datetime(3) NOT NULL,
  `created_by` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `updated_by` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `ai_prompt_templates_name_key` (`name`),
  KEY `ai_prompt_templates_created_by_fkey` (`created_by`),
  KEY `ai_prompt_templates_updated_by_fkey` (`updated_by`),
  CONSTRAINT `ai_prompt_templates_created_by_fkey` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE,
  CONSTRAINT `ai_prompt_templates_updated_by_fkey` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ai_prompt_templates`
--

LOCK TABLES `ai_prompt_templates` WRITE;
/*!40000 ALTER TABLE `ai_prompt_templates` DISABLE KEYS */;
INSERT INTO `ai_prompt_templates` VALUES ('cme59i42t0002u0pt13s98dmy','工单分析通用模板','ticket_analysis','你是一个专业的IT运维工单智能分析助手。请仔细分析以下工单描述，并基于语义理解和上下文判断，返回准确的分类建议。\n\n## 工单信息\n**描述内容：** {description}\n**客户类型：** {customerType}\n**行业领域：** {industry}\n**历史模式：** {historyPattern}\n\n## 分析任务\n请分析工单的真实意图和需求，返回标准JSON格式的结果：\n\n```json\n{\n  \"title\": {\n    \"suggested\": \"简洁清晰的工单标题(不超过50字)\",\n    \"confidence\": 85\n  },\n  \"category\": {\n    \"suggested\": \"服务类别代码\",\n    \"confidence\": 90,\n    \"reasoning\": \"详细的分类理由和判断依据\"\n  },\n  \"priority\": {\n    \"suggested\": \"优先级代码\",\n    \"confidence\": 80,\n    \"reasoning\": \"优先级判断的具体理由\"\n  },\n  \"slaTemplate\": {\n    \"suggested\": \"推荐的SLA模板\",\n    \"confidence\": 75,\n    \"reasoning\": \"SLA模板选择理由\"\n  }\n}\n```\n\n## 分类标准\n**服务类别：**\n- MAINTENANCE: 系统维护、定期保养\n- SUPPORT: 技术支持、问题咨询\n- UPGRADE: 系统升级、功能增强\n- BUGFIX: 故障修复、问题解决\n- CONSULTING: 技术咨询、方案建议\n- MONITORING: 监控告警、性能分析\n\n**优先级：**\n- LOW: 一般需求，不影响业务\n- MEDIUM: 中等重要，有一定影响\n- HIGH: 重要紧急，影响业务运行\n- URGENT: 极其紧急，严重影响或中断业务\n\n请确保返回的JSON格式正确，置信度为0-100的整数。','[\"description\", \"customerType\", \"industry\", \"historyPattern\"]',NULL,'1.0',1,'2025-08-10 05:47:35.333','2025-08-10 05:47:35.333','cme3pnoma0003r8cva4fu9log',NULL),('cme59i43b0004u0ptijbpduv0','OpenAI专用工单分析模板','ticket_analysis','作为专业的IT运维工单分析助手，请分析以下工单内容并返回JSON格式的分类建议。\n\n工单描述：{description}\n客户信息：{customerType} - {industry}\n历史模式：{historyPattern}\n\n请返回以下格式的JSON响应：\n{\n  \"title\": {\"suggested\": \"标题\", \"confidence\": 数字},\n  \"category\": {\"suggested\": \"类别\", \"confidence\": 数字, \"reasoning\": \"理由\"},\n  \"priority\": {\"suggested\": \"优先级\", \"confidence\": 数字, \"reasoning\": \"理由\"},\n  \"slaTemplate\": {\"suggested\": \"SLA模板\", \"confidence\": 数字, \"reasoning\": \"理由\"}\n}\n\n类别选项：MAINTENANCE, SUPPORT, UPGRADE, BUGFIX, CONSULTING, MONITORING\n优先级选项：LOW, MEDIUM, HIGH, URGENT','[\"description\", \"customerType\", \"industry\", \"historyPattern\"]','openai','1.0',1,'2025-08-10 05:47:35.351','2025-08-10 05:47:35.351','cme3pnoma0003r8cva4fu9log',NULL),('cme59i43i0006u0pttpqtqnvn','Claude专用工单分析模板','ticket_analysis','我需要你作为IT运维专家分析工单内容。\n\n工单详情：\n- 描述：{description}\n- 客户类型：{customerType}\n- 行业：{industry}\n- 历史模式：{historyPattern}\n\n请提供JSON格式的分析结果，包含标题建议、类别分类、优先级评估和SLA模板推荐。\n\n格式要求：\n{\n  \"title\": {\"suggested\": \"建议标题\", \"confidence\": 置信度},\n  \"category\": {\"suggested\": \"MAINTENANCE|SUPPORT|UPGRADE|BUGFIX|CONSULTING|MONITORING\", \"confidence\": 置信度, \"reasoning\": \"分类理由\"},\n  \"priority\": {\"suggested\": \"LOW|MEDIUM|HIGH|URGENT\", \"confidence\": 置信度, \"reasoning\": \"优先级理由\"},\n  \"slaTemplate\": {\"suggested\": \"SLA模板名称\", \"confidence\": 置信度, \"reasoning\": \"选择理由\"}\n}','[\"description\", \"customerType\", \"industry\", \"historyPattern\"]','anthropic','1.0',1,'2025-08-10 05:47:35.358','2025-08-10 05:47:35.358','cme3pnoma0003r8cva4fu9log',NULL),('cme5io2hx0003kijjmym4wdb9','工单分析模板','ticket_analysis','请分析以下工单内容：\n\n工单描述：{{description}}\n客户信息：{{customerInfo}}\n历史记录：{{historyPattern}}\n\n请提供以下分析结果：\n1. 问题分类（从以下选择：技术支持、客户服务、产品问题、账户问题、其他）\n2. 优先级建议（HIGH/MEDIUM/LOW）\n3. 预估解决时间\n4. 建议的解决方案\n5. 需要的技能标签\n\n请以JSON格式返回结果：\n{\n  \"category\": \"分类\",\n  \"priority\": \"优先级\",\n  \"estimatedTime\": \"预估时间（小时）\",\n  \"suggestedActions\": [\"建议1\", \"建议2\"],\n  \"requiredSkills\": [\"技能1\", \"技能2\"],\n  \"confidence\": 0.85\n}','[\"description\", \"customerInfo\", \"historyPattern\"]','openai','1.0',1,'2025-08-10 10:04:09.763','2025-08-10 10:04:09.763','cme3pnoma0003r8cva4fu9log',NULL),('cme5io2hx0004kijjgkfw01vh','分类建议模板','classification','根据工单描述进行分类：\n\n工单描述：{{description}}\n\n请从以下类别中选择最合适的分类：\n- 技术支持：系统故障、功能问题、性能问题\n- 客户服务：账户问题、计费问题、使用咨询\n- 产品问题：功能缺陷、改进建议、新功能请求\n- 安全问题：数据安全、访问权限、安全漏洞\n\n返回JSON格式：\n{\n  \"category\": \"分类名称\",\n  \"subcategory\": \"子分类\",\n  \"confidence\": 0.9,\n  \"reasoning\": \"分类理由\"\n}','[\"description\"]','anthropic','1.0',1,'2025-08-10 10:04:09.763','2025-08-10 10:04:09.763','cme3pnoma0003r8cva4fu9log',NULL),('cme5io2hy0005kijjggiegazc','优先级评估模板','priority_assessment','评估工单优先级：\n\n工单描述：{{description}}\n客户等级：{{customerTier}}\n影响范围：{{impactScope}}\n\n评估标准：\n- HIGH：系统宕机、数据丢失、安全问题、VIP客户紧急问题\n- MEDIUM：功能异常、性能问题、一般客户重要问题\n- LOW：使用咨询、功能建议、非紧急问题\n\n返回JSON格式：\n{\n  \"priority\": \"HIGH/MEDIUM/LOW\",\n  \"reasoning\": \"评估理由\",\n  \"slaHours\": 24,\n  \"escalationRequired\": false,\n  \"confidence\": 0.8\n}','[\"description\", \"customerTier\", \"impactScope\"]','openai','1.0',1,'2025-08-10 10:04:09.763','2025-08-10 10:04:09.763','cme3pnoma0003r8cva4fu9log',NULL);
/*!40000 ALTER TABLE `ai_prompt_templates` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `alert_rules`
--

DROP TABLE IF EXISTS `alert_rules`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `alert_rules` (
  `id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `metric_type` enum('CPU','MEMORY','DISK','NETWORK','SERVICE','DATABASE','REDIS') COLLATE utf8mb4_unicode_ci NOT NULL,
  `condition` enum('GT','LT','GTE','LTE','EQ','NEQ') COLLATE utf8mb4_unicode_ci NOT NULL,
  `threshold` double NOT NULL,
  `duration` int NOT NULL,
  `severity` enum('LOW','MEDIUM','HIGH','CRITICAL') COLLATE utf8mb4_unicode_ci NOT NULL,
  `enabled` tinyint(1) NOT NULL DEFAULT '1',
  `notification_channels` json NOT NULL,
  `created_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `updated_at` datetime(3) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `alert_rules_name_key` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `alert_rules`
--

LOCK TABLES `alert_rules` WRITE;
/*!40000 ALTER TABLE `alert_rules` DISABLE KEYS */;
/*!40000 ALTER TABLE `alert_rules` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `alert_thresholds`
--

DROP TABLE IF EXISTS `alert_thresholds`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `alert_thresholds` (
  `id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `metric_type` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `warning` double NOT NULL,
  `critical` double NOT NULL,
  `duration` int NOT NULL,
  `enabled` tinyint(1) NOT NULL DEFAULT '1',
  `description` text COLLATE utf8mb4_unicode_ci,
  `created_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `updated_at` datetime(3) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `alert_thresholds_metric_type_name_key` (`metric_type`,`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `alert_thresholds`
--

LOCK TABLES `alert_thresholds` WRITE;
/*!40000 ALTER TABLE `alert_thresholds` DISABLE KEYS */;
/*!40000 ALTER TABLE `alert_thresholds` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `alerts`
--

DROP TABLE IF EXISTS `alerts`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `alerts` (
  `id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `rule_id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `severity` enum('LOW','MEDIUM','HIGH','CRITICAL') COLLATE utf8mb4_unicode_ci NOT NULL,
  `status` enum('PENDING','ACKNOWLEDGED','RESOLVED','CANCELLED') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'PENDING',
  `message` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `metric_value` double DEFAULT NULL,
  `triggered_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `acknowledged_at` datetime(3) DEFAULT NULL,
  `acknowledged_by` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `resolved_at` datetime(3) DEFAULT NULL,
  `resolved_by` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `notifications_sent` json DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `alerts_rule_id_fkey` (`rule_id`),
  KEY `alerts_acknowledged_by_fkey` (`acknowledged_by`),
  KEY `alerts_resolved_by_fkey` (`resolved_by`),
  CONSTRAINT `alerts_acknowledged_by_fkey` FOREIGN KEY (`acknowledged_by`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
  CONSTRAINT `alerts_resolved_by_fkey` FOREIGN KEY (`resolved_by`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
  CONSTRAINT `alerts_rule_id_fkey` FOREIGN KEY (`rule_id`) REFERENCES `alert_rules` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `alerts`
--

LOCK TABLES `alerts` WRITE;
/*!40000 ALTER TABLE `alerts` DISABLE KEYS */;
/*!40000 ALTER TABLE `alerts` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `api_keys`
--

DROP TABLE IF EXISTS `api_keys`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `api_keys` (
  `id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `key_value` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `system_id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `status` enum('ACTIVE','INACTIVE','REVOKED','EXPIRED') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'ACTIVE',
  `expires_at` datetime(3) DEFAULT NULL,
  `last_used_at` datetime(3) DEFAULT NULL,
  `usage_count` int NOT NULL DEFAULT '0',
  `created_by` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `updated_at` datetime(3) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `api_keys_key_value_key` (`key_value`),
  KEY `api_keys_created_by_fkey` (`created_by`),
  CONSTRAINT `api_keys_created_by_fkey` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `api_keys`
--

LOCK TABLES `api_keys` WRITE;
/*!40000 ALTER TABLE `api_keys` DISABLE KEYS */;
INSERT INTO `api_keys` VALUES ('cme3pnomw0005r8cvyu27f9h4','Demo Key','ak_demo1234567890abcdef','demo-system','SDK 演示用 API Key','ACTIVE',NULL,NULL,0,'cme3pnoma0003r8cva4fu9log','2025-08-09 03:44:16.760','2025-08-09 03:44:16.760');
/*!40000 ALTER TABLE `api_keys` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `customer_contacts`
--

DROP TABLE IF EXISTS `customer_contacts`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `customer_contacts` (
  `id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `customer_id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `position` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `email` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `phone` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `is_primary` tinyint(1) NOT NULL DEFAULT '0',
  `created_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `updated_at` datetime(3) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `customer_contacts_customer_id_fkey` (`customer_id`),
  CONSTRAINT `customer_contacts_customer_id_fkey` FOREIGN KEY (`customer_id`) REFERENCES `customers` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `customer_contacts`
--

LOCK TABLES `customer_contacts` WRITE;
/*!40000 ALTER TABLE `customer_contacts` DISABLE KEYS */;
INSERT INTO `customer_contacts` VALUES ('cme3pnong0009r8cv9vdf1jme','cme3pnon80007r8cvn6kummym','张三','技术总监','<EMAIL>','13800138000',1,'2025-08-09 03:44:16.780','2025-08-09 03:44:16.780'),('cme3pnons000br8cvhgzzoyyb','cme3pnon80007r8cvn6kummym','李四','运维经理','<EMAIL>','13800138001',0,'2025-08-09 03:44:16.792','2025-08-09 03:44:16.792'),('cme3pnq0p000995aj6p149rpy','cme3pnon80007r8cvn6kummym','张三','技术总监','<EMAIL>','13800138000',1,'2025-08-09 03:44:18.553','2025-08-09 03:44:18.553'),('cme3pnq11000b95ajr5f560yl','cme3pnon80007r8cvn6kummym','李四','运维经理','<EMAIL>','13800138001',0,'2025-08-09 03:44:18.565','2025-08-09 03:44:18.565'),('cme3tlre70009133g65zma4j5','cme3pnon80007r8cvn6kummym','张三','技术总监','<EMAIL>','13800138000',1,'2025-08-09 05:34:45.487','2025-08-09 05:34:45.487'),('cme3tlrey000b133gc1zlhnb1','cme3pnon80007r8cvn6kummym','李四','运维经理','<EMAIL>','13800138001',0,'2025-08-09 05:34:45.514','2025-08-09 05:34:45.514'),('cme3u3zdr0009du4b9ebpcqo4','cme3pnon80007r8cvn6kummym','张三','技术总监','<EMAIL>','13800138000',1,'2025-08-09 05:48:55.647','2025-08-09 05:48:55.647'),('cme3u3zea000bdu4bptgedjmm','cme3pnon80007r8cvn6kummym','李四','运维经理','<EMAIL>','13800138001',0,'2025-08-09 05:48:55.666','2025-08-09 05:48:55.666');
/*!40000 ALTER TABLE `customer_contacts` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `customers`
--

DROP TABLE IF EXISTS `customers`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `customers` (
  `id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `company` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `industry` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `level` enum('BASIC','STANDARD','PREMIUM','ENTERPRISE') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'STANDARD',
  `contact_person` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `address` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `created_by` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `updated_at` datetime(3) NOT NULL,
  `contact_email` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `contact_phone` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `is_vip` tinyint(1) NOT NULL DEFAULT '0',
  `type` enum('ENTERPRISE','INDIVIDUAL','GOVERNMENT','NONPROFIT') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'ENTERPRISE',
  `code` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `customers_code_key` (`code`),
  KEY `customers_created_by_fkey` (`created_by`),
  CONSTRAINT `customers_created_by_fkey` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `customers`
--

LOCK TABLES `customers` WRITE;
/*!40000 ALTER TABLE `customers` DISABLE KEYS */;
INSERT INTO `customers` VALUES ('cme3pnon80007r8cvn6kummym','示例科技有限公司','示例科技有限公司','互联网','STANDARD','张三','北京市朝阳区示例大厦','一家专注于互联网技术的公司','cme3pnoma0003r8cva4fu9log','2025-08-09 03:44:16.772','2025-08-09 03:44:16.772','<EMAIL>','13800138000',0,'ENTERPRISE','CUST-DEMO-001');
/*!40000 ALTER TABLE `customers` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `email_logs`
--

DROP TABLE IF EXISTS `email_logs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `email_logs` (
  `id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `template_id` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `recipient` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `subject` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `content` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `status` enum('PENDING','SENDING','SENT','DELIVERED','OPENED','FAILED','CANCELLED') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'PENDING',
  `error_message` text COLLATE utf8mb4_unicode_ci,
  `sent_at` datetime(3) DEFAULT NULL,
  `delivered_at` datetime(3) DEFAULT NULL,
  `opened_at` datetime(3) DEFAULT NULL,
  `metadata` json DEFAULT NULL,
  `retry_count` int NOT NULL DEFAULT '0',
  `created_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `updated_at` datetime(3) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `email_logs_recipient_idx` (`recipient`),
  KEY `email_logs_status_idx` (`status`),
  KEY `email_logs_created_at_idx` (`created_at`),
  KEY `email_logs_template_id_fkey` (`template_id`),
  CONSTRAINT `email_logs_template_id_fkey` FOREIGN KEY (`template_id`) REFERENCES `email_templates` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `email_logs`
--

LOCK TABLES `email_logs` WRITE;
/*!40000 ALTER TABLE `email_logs` DISABLE KEYS */;
INSERT INTO `email_logs` VALUES ('cme3uspgw0001uz7tm2lo5l60','cme3tlrj3002h133gdvobs2oa','<EMAIL>','【系统告警】高 - 系统CPU使用率过高','\n<div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;\">\n  <h2 style=\"color: #dc3545;\">🚨 系统告警通知</h2>\n  <div style=\"background: #f8d7da; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #dc3545;\">\n    <p><strong>告警级别：</strong><span style=\"color: #dc3545;\">高</span></p>\n    <p><strong>告警标题：</strong>系统CPU使用率过高</p>\n    <p><strong>告警内容：</strong>CPU使用率达到85%，请及时处理</p>\n    <p><strong>发生时间：</strong>8/9/2025, 2:08:09 PM</p>\n    <p><strong>影响范围：</strong>示例affectedSystem</p>\n  </div>\n  <p style=\"color: #dc3545; font-weight: bold;\">请立即检查系统状态并采取必要措施！</p>\n  <hr style=\"border: none; border-top: 1px solid #dee2e6; margin: 20px 0;\">\n  <p style=\"color: #6c757d; font-size: 12px;\">此邮件由运维服务管理系统自动发送 | 8/9/2025, 2:08:09 PM</p>\n</div>','SENT',NULL,'2025-08-09 06:08:10.265',NULL,NULL,'{\"variables\": {\"timestamp\": \"8/9/2025, 2:08:09 PM\", \"alertLevel\": \"高\", \"alertTitle\": \"系统CPU使用率过高\", \"alertMessage\": \"CPU使用率达到85%，请及时处理\", \"affectedSystem\": \"示例affectedSystem\"}, \"configSource\": \"database\", \"templateName\": \"系统告警通知\", \"templateType\": \"ALERT\"}',0,'2025-08-09 06:08:09.197','2025-08-09 06:08:10.267');
/*!40000 ALTER TABLE `email_logs` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `email_templates`
--

DROP TABLE IF EXISTS `email_templates`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `email_templates` (
  `id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `type` enum('ALERT','MAINTENANCE','SERVICE_CREATED','SERVICE_ASSIGNED','SERVICE_RESOLVED','SERVICE_CLOSED','USER_WELCOME','PASSWORD_RESET','ACCOUNT_LOCKED','BACKUP_SUCCESS','BACKUP_FAILED','CUSTOM') COLLATE utf8mb4_unicode_ci NOT NULL,
  `category` enum('SYSTEM','BUSINESS','NOTIFICATION','SECURITY') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'SYSTEM',
  `subject` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `content` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `variables` json DEFAULT NULL,
  `enabled` tinyint(1) NOT NULL DEFAULT '1',
  `is_system` tinyint(1) NOT NULL DEFAULT '0',
  `version` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '1.0',
  `metadata` json DEFAULT NULL,
  `created_by` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `updated_by` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `updated_at` datetime(3) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `email_templates_name_type_key` (`name`,`type`),
  KEY `email_templates_created_by_fkey` (`created_by`),
  KEY `email_templates_updated_by_fkey` (`updated_by`),
  CONSTRAINT `email_templates_created_by_fkey` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE,
  CONSTRAINT `email_templates_updated_by_fkey` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `email_templates`
--

LOCK TABLES `email_templates` WRITE;
/*!40000 ALTER TABLE `email_templates` DISABLE KEYS */;
INSERT INTO `email_templates` VALUES ('cme3tlrj3002h133gdvobs2oa','系统告警通知','ALERT','SYSTEM','【系统告警】{{alertLevel}} - {{alertTitle}}','\n<div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;\">\n  <h2 style=\"color: #dc3545;\">🚨 系统告警通知</h2>\n  <div style=\"background: #f8d7da; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #dc3545;\">\n    <p><strong>告警级别：</strong><span style=\"color: #dc3545;\">{{alertLevel}}</span></p>\n    <p><strong>告警标题：</strong>{{alertTitle}}</p>\n    <p><strong>告警内容：</strong>{{alertMessage}}</p>\n    <p><strong>发生时间：</strong>{{timestamp}}</p>\n    <p><strong>影响范围：</strong>{{affectedSystem}}</p>\n  </div>\n  <p style=\"color: #dc3545; font-weight: bold;\">请立即检查系统状态并采取必要措施！</p>\n  <hr style=\"border: none; border-top: 1px solid #dee2e6; margin: 20px 0;\">\n  <p style=\"color: #6c757d; font-size: 12px;\">此邮件由运维服务管理系统自动发送 | {{timestamp}}</p>\n</div>','系统检测到异常情况时发送的告警通知邮件','[\"alertLevel\", \"alertTitle\", \"alertMessage\", \"timestamp\", \"affectedSystem\"]',1,1,'1.0',NULL,'cme3pnoma0003r8cva4fu9log','cme3pnoma0003r8cva4fu9log','2025-08-09 05:34:45.663','2025-08-09 05:34:45.663'),('cme3tlrj9002j133gm20eubbo','服务单创建通知','SERVICE_CREATED','BUSINESS','【新服务单】{{serviceTitle}} - {{serviceNumber}}','\n<div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;\">\n  <h2 style=\"color: #28a745;\">📋 新服务单创建</h2>\n  <div style=\"background: #d4edda; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #28a745;\">\n    <p><strong>服务单号：</strong>{{serviceNumber}}</p>\n    <p><strong>服务标题：</strong>{{serviceTitle}}</p>\n    <p><strong>优先级：</strong><span style=\"color: {{priorityColor}};\">{{priority}}</span></p>\n    <p><strong>客户：</strong>{{customerName}}</p>\n    <p><strong>创建时间：</strong>{{createdAt}}</p>\n    <p><strong>预计完成：</strong>{{expectedCompletion}}</p>\n  </div>\n  <div style=\"background: #f8f9fa; padding: 15px; border-radius: 8px;\">\n    <h4 style=\"margin-top: 0;\">服务描述：</h4>\n    <p>{{serviceDescription}}</p>\n  </div>\n  <p>请及时处理该服务单，如有疑问请联系客户或项目负责人。</p>\n  <div style=\"text-align: center; margin: 20px 0;\">\n    <a href=\"{{serviceUrl}}\" style=\"background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;\">查看服务单详情</a>\n  </div>\n  <hr style=\"border: none; border-top: 1px solid #dee2e6; margin: 20px 0;\">\n  <p style=\"color: #6c757d; font-size: 12px;\">此邮件由运维服务管理系统自动发送 | {{timestamp}}</p>\n</div>','创建新服务单时发送给相关人员的通知邮件','[\"serviceNumber\", \"serviceTitle\", \"priority\", \"priorityColor\", \"customerName\", \"createdAt\", \"expectedCompletion\", \"serviceDescription\", \"serviceUrl\", \"timestamp\"]',1,1,'1.0',NULL,'cme3pnoma0003r8cva4fu9log','cme3pnoma0003r8cva4fu9log','2025-08-09 05:34:45.669','2025-08-09 05:34:45.669'),('cme3tlrje002l133gk2j9pe35','服务单解决通知','SERVICE_RESOLVED','BUSINESS','【服务单已解决】{{serviceTitle}} - {{serviceNumber}}','\n<div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;\">\n  <h2 style=\"color: #28a745;\">✅ 服务单已解决</h2>\n  <div style=\"background: #d4edda; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #28a745;\">\n    <p><strong>服务单号：</strong>{{serviceNumber}}</p>\n    <p><strong>服务标题：</strong>{{serviceTitle}}</p>\n    <p><strong>解决时间：</strong>{{resolvedAt}}</p>\n    <p><strong>处理人员：</strong>{{assigneeName}}</p>\n    <p><strong>耗时：</strong>{{duration}}</p>\n  </div>\n  <div style=\"background: #f8f9fa; padding: 15px; border-radius: 8px;\">\n    <h4 style=\"margin-top: 0;\">解决方案：</h4>\n    <p>{{solution}}</p>\n  </div>\n  <p>该服务单已经成功解决，如有任何问题请及时联系我们。</p>\n  <div style=\"text-align: center; margin: 20px 0;\">\n    <a href=\"{{serviceUrl}}\" style=\"background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;\">查看解决详情</a>\n  </div>\n  <hr style=\"border: none; border-top: 1px solid #dee2e6; margin: 20px 0;\">\n  <p style=\"color: #6c757d; font-size: 12px;\">此邮件由运维服务管理系统自动发送 | {{timestamp}}</p>\n</div>','服务单解决后发送给客户的通知邮件','[\"serviceNumber\", \"serviceTitle\", \"resolvedAt\", \"assigneeName\", \"duration\", \"solution\", \"serviceUrl\", \"timestamp\"]',1,1,'1.0',NULL,'cme3pnoma0003r8cva4fu9log','cme3pnoma0003r8cva4fu9log','2025-08-09 05:34:45.675','2025-08-09 05:34:45.675'),('cme3tlrji002n133gh5ze4gm9','系统维护通知','MAINTENANCE','SYSTEM','【系统维护】计划维护通知 - {{maintenanceDate}}','\n<div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;\">\n  <h2 style=\"color: #ffc107;\">🔧 系统维护通知</h2>\n  <div style=\"background: #fff3cd; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #ffc107;\">\n    <p><strong>维护时间：</strong>{{maintenanceDate}} {{maintenanceTime}}</p>\n    <p><strong>预计时长：</strong>{{estimatedDuration}}</p>\n    <p><strong>维护范围：</strong>{{maintenanceScope}}</p>\n    <p><strong>影响程度：</strong><span style=\"color: {{impactColor}};\">{{impact}}</span></p>\n  </div>\n  <div style=\"background: #f8f9fa; padding: 15px; border-radius: 8px;\">\n    <h4 style=\"margin-top: 0;\">维护内容：</h4>\n    <p>{{maintenanceContent}}</p>\n  </div>\n  <div style=\"background: #e2e3e5; padding: 15px; border-radius: 8px; margin: 15px 0;\">\n    <h4 style=\"margin-top: 0; color: #495057;\">注意事项：</h4>\n    <ul>\n      <li>维护期间可能出现服务中断或访问异常</li>\n      <li>请提前保存重要数据</li>\n      <li>如有紧急情况，请联系技术支持</li>\n      <li>维护完成后将发送通知邮件</li>\n    </ul>\n  </div>\n  <p>感谢您的理解与配合！</p>\n  <hr style=\"border: none; border-top: 1px solid #dee2e6; margin: 20px 0;\">\n  <p style=\"color: #6c757d; font-size: 12px;\">此邮件由运维服务管理系统自动发送 | {{timestamp}}</p>\n</div>','系统计划维护时发送给用户的通知邮件','[\"maintenanceDate\", \"maintenanceTime\", \"estimatedDuration\", \"maintenanceScope\", \"impact\", \"impactColor\", \"maintenanceContent\", \"timestamp\"]',1,1,'1.0',NULL,'cme3pnoma0003r8cva4fu9log','cme3pnoma0003r8cva4fu9log','2025-08-09 05:34:45.678','2025-08-09 05:34:45.678');
/*!40000 ALTER TABLE `email_templates` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `intelligent_analysis_results`
--

DROP TABLE IF EXISTS `intelligent_analysis_results`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `intelligent_analysis_results` (
  `id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `analysis_type` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `result` json NOT NULL,
  `confidence` double NOT NULL,
  `status` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'active',
  `valid_until` datetime(3) DEFAULT NULL,
  `created_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  PRIMARY KEY (`id`),
  KEY `intelligent_analysis_results_analysis_type_status_idx` (`analysis_type`,`status`),
  KEY `intelligent_analysis_results_created_at_idx` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `intelligent_analysis_results`
--

LOCK TABLES `intelligent_analysis_results` WRITE;
/*!40000 ALTER TABLE `intelligent_analysis_results` DISABLE KEYS */;
/*!40000 ALTER TABLE `intelligent_analysis_results` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `monitoring_config`
--

DROP TABLE IF EXISTS `monitoring_config`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `monitoring_config` (
  `id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `category` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `key` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `value` json NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `enabled` tinyint(1) NOT NULL DEFAULT '1',
  `updated_by` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `updated_at` datetime(3) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `monitoring_config_category_key_key` (`category`,`key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `monitoring_config`
--

LOCK TABLES `monitoring_config` WRITE;
/*!40000 ALTER TABLE `monitoring_config` DISABLE KEYS */;
/*!40000 ALTER TABLE `monitoring_config` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `notification_templates`
--

DROP TABLE IF EXISTS `notification_templates`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `notification_templates` (
  `id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `type` enum('EMAIL','SMS','SYSTEM') COLLATE utf8mb4_unicode_ci NOT NULL,
  `subject` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `content` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `variables` json DEFAULT NULL,
  `created_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `updated_at` datetime(3) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `notification_templates`
--

LOCK TABLES `notification_templates` WRITE;
/*!40000 ALTER TABLE `notification_templates` DISABLE KEYS */;
INSERT INTO `notification_templates` VALUES ('cme3pnopj000qr8cvh7tkpwca','工单创建通知','EMAIL','新工单创建 - {{ticketNumber}}','\n        您好 {{customerName}}，\n\n        您的服务工单已成功创建：\n\n        工单号：{{ticketNumber}}\n        标题：{{title}}\n        优先级：{{priority}}\n        创建时间：{{createdAt}}\n\n        我们将尽快处理您的请求。\n\n        运维服务团队\n      ','\"[\\\"customerName\\\",\\\"ticketNumber\\\",\\\"title\\\",\\\"priority\\\",\\\"createdAt\\\"]\"','2025-08-09 03:44:16.855','2025-08-09 03:44:16.855'),('cme3pnopp000rr8cvoy8hpegm','工单完成通知','EMAIL','工单已完成 - {{ticketNumber}}','\n        您好 {{customerName}}，\n\n        您的服务工单已完成处理：\n\n        工单号：{{ticketNumber}}\n        标题：{{title}}\n        处理结果：{{resolution}}\n        完成时间：{{completedAt}}\n\n        如有问题，请及时与我们联系。\n\n        运维服务团队\n      ','\"[\\\"customerName\\\",\\\"ticketNumber\\\",\\\"title\\\",\\\"resolution\\\",\\\"completedAt\\\"]\"','2025-08-09 03:44:16.861','2025-08-09 03:44:16.861'),('cme3pnq2o000q95aj5xk9e1uv','工单创建通知','EMAIL','新工单创建 - {{ticketNumber}}','\n        您好 {{customerName}}，\n\n        您的服务工单已成功创建：\n\n        工单号：{{ticketNumber}}\n        标题：{{title}}\n        优先级：{{priority}}\n        创建时间：{{createdAt}}\n\n        我们将尽快处理您的请求。\n\n        运维服务团队\n      ','\"[\\\"customerName\\\",\\\"ticketNumber\\\",\\\"title\\\",\\\"priority\\\",\\\"createdAt\\\"]\"','2025-08-09 03:44:18.625','2025-08-09 03:44:18.625'),('cme3pnq2t000r95aj9wamv94l','工单完成通知','EMAIL','工单已完成 - {{ticketNumber}}','\n        您好 {{customerName}}，\n\n        您的服务工单已完成处理：\n\n        工单号：{{ticketNumber}}\n        标题：{{title}}\n        处理结果：{{resolution}}\n        完成时间：{{completedAt}}\n\n        如有问题，请及时与我们联系。\n\n        运维服务团队\n      ','\"[\\\"customerName\\\",\\\"ticketNumber\\\",\\\"title\\\",\\\"resolution\\\",\\\"completedAt\\\"]\"','2025-08-09 03:44:18.630','2025-08-09 03:44:18.630'),('cme3tlrgo000q133gg8vqgt0i','工单创建通知','EMAIL','新工单创建 - {{ticketNumber}}','\n        您好 {{customerName}}，\n\n        您的服务工单已成功创建：\n\n        工单号：{{ticketNumber}}\n        标题：{{title}}\n        优先级：{{priority}}\n        创建时间：{{createdAt}}\n\n        我们将尽快处理您的请求。\n\n        运维服务团队\n      ','\"[\\\"customerName\\\",\\\"ticketNumber\\\",\\\"title\\\",\\\"priority\\\",\\\"createdAt\\\"]\"','2025-08-09 05:34:45.577','2025-08-09 05:34:45.577'),('cme3tlrgu000r133gg12e1isv','工单完成通知','EMAIL','工单已完成 - {{ticketNumber}}','\n        您好 {{customerName}}，\n\n        您的服务工单已完成处理：\n\n        工单号：{{ticketNumber}}\n        标题：{{title}}\n        处理结果：{{resolution}}\n        完成时间：{{completedAt}}\n\n        如有问题，请及时与我们联系。\n\n        运维服务团队\n      ','\"[\\\"customerName\\\",\\\"ticketNumber\\\",\\\"title\\\",\\\"resolution\\\",\\\"completedAt\\\"]\"','2025-08-09 05:34:45.582','2025-08-09 05:34:45.582'),('cme3u3zg6000qdu4bwixtd4kt','工单创建通知','EMAIL','新工单创建 - {{ticketNumber}}','\n        您好 {{customerName}}，\n\n        您的服务工单已成功创建：\n\n        工单号：{{ticketNumber}}\n        标题：{{title}}\n        优先级：{{priority}}\n        创建时间：{{createdAt}}\n\n        我们将尽快处理您的请求。\n\n        运维服务团队\n      ','\"[\\\"customerName\\\",\\\"ticketNumber\\\",\\\"title\\\",\\\"priority\\\",\\\"createdAt\\\"]\"','2025-08-09 05:48:55.734','2025-08-09 05:48:55.734'),('cme3u3zge000rdu4bne384gqp','工单完成通知','EMAIL','工单已完成 - {{ticketNumber}}','\n        您好 {{customerName}}，\n\n        您的服务工单已完成处理：\n\n        工单号：{{ticketNumber}}\n        标题：{{title}}\n        处理结果：{{resolution}}\n        完成时间：{{completedAt}}\n\n        如有问题，请及时与我们联系。\n\n        运维服务团队\n      ','\"[\\\"customerName\\\",\\\"ticketNumber\\\",\\\"title\\\",\\\"resolution\\\",\\\"completedAt\\\"]\"','2025-08-09 05:48:55.742','2025-08-09 05:48:55.742');
/*!40000 ALTER TABLE `notification_templates` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `notifications`
--

DROP TABLE IF EXISTS `notifications`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `notifications` (
  `id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `template_id` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `recipient` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `type` enum('EMAIL','SMS','SYSTEM') COLLATE utf8mb4_unicode_ci NOT NULL,
  `subject` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `content` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `status` enum('PENDING','SENT','FAILED','CANCELLED') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'PENDING',
  `sent_at` datetime(3) DEFAULT NULL,
  `created_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  PRIMARY KEY (`id`),
  KEY `notifications_template_id_fkey` (`template_id`),
  CONSTRAINT `notifications_template_id_fkey` FOREIGN KEY (`template_id`) REFERENCES `notification_templates` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `notifications`
--

LOCK TABLES `notifications` WRITE;
/*!40000 ALTER TABLE `notifications` DISABLE KEYS */;
INSERT INTO `notifications` VALUES ('cme3sd2cd000sikh7hf2efg6e',NULL,'<EMAIL>','SYSTEM','SLA响应时间违约告警','工单号: OPS-20240101-001\n工单标题: 网站首页加载缓慢问题\n客户: 示例科技有限公司\nSLA状态: SLA响应时间违约告警\n\n响应时间状态:\n- 已用时间: 75分钟\n- 剩余时间: -15分钟\n- 完成度: 125%\n- 是否违约: 是\n\n解决时间状态:\n- 已用时间: 1小时  \n- 剩余时间: 23小时\n- 完成度: 4.17%\n- 是否违约: 否\n\n风险等级: CRITICAL','SENT',NULL,'2025-08-09 05:00:00.158'),('cme3tfn92000012zb94ooikll',NULL,'<EMAIL>','SYSTEM','SLA响应时间违约告警','工单号: OPS-20240101-001\n工单标题: 网站首页加载缓慢问题\n客户: 示例科技有限公司\nSLA状态: SLA响应时间违约告警\n\n响应时间状态:\n- 已用时间: 105分钟\n- 剩余时间: -45分钟\n- 完成度: 175%\n- 是否违约: 是\n\n解决时间状态:\n- 已用时间: 1小时  \n- 剩余时间: 23小时\n- 完成度: 4.17%\n- 是否违约: 否\n\n风险等级: CRITICAL','SENT',NULL,'2025-08-09 05:30:00.182'),('cme3ui83f000010gtrfx5kf2l',NULL,'<EMAIL>','SYSTEM','SLA响应时间违约告警','工单号: OPS-20240101-001\n工单标题: 网站首页加载缓慢问题\n客户: 示例科技有限公司\nSLA状态: SLA响应时间违约告警\n\n响应时间状态:\n- 已用时间: 135分钟\n- 剩余时间: -75分钟\n- 完成度: 225%\n- 是否违约: 是\n\n解决时间状态:\n- 已用时间: 2小时  \n- 剩余时间: 22小时\n- 完成度: 8.33%\n- 是否违约: 否\n\n风险等级: CRITICAL','SENT',NULL,'2025-08-09 06:00:00.123'),('cme3vksxf000iuz7trcmj08te',NULL,'<EMAIL>','SYSTEM','SLA响应时间违约告警','工单号: OPS-20240101-001\n工单标题: 网站首页加载缓慢问题\n客户: 示例科技有限公司\nSLA状态: SLA响应时间违约告警\n\n响应时间状态:\n- 已用时间: 165分钟\n- 剩余时间: -105分钟\n- 完成度: 275%\n- 是否违约: 是\n\n解决时间状态:\n- 已用时间: 2小时  \n- 剩余时间: 22小时\n- 完成度: 8.33%\n- 是否违约: 否\n\n风险等级: CRITICAL','SENT',NULL,'2025-08-09 06:30:00.051'),('cme3wndtr000puz7t0m2bue4h',NULL,'<EMAIL>','SYSTEM','SLA响应时间违约告警','工单号: OPS-20240101-001\n工单标题: 网站首页加载缓慢问题\n客户: 示例科技有限公司\nSLA状态: SLA响应时间违约告警\n\n响应时间状态:\n- 已用时间: 195分钟\n- 剩余时间: -135分钟\n- 完成度: 325%\n- 是否违约: 是\n\n解决时间状态:\n- 已用时间: 3小时  \n- 剩余时间: 21小时\n- 完成度: 12.5%\n- 是否违约: 否\n\n风险等级: CRITICAL','SENT',NULL,'2025-08-09 07:00:00.063'),('cme5mt1gh000dar4qbs5pw4t1',NULL,'<EMAIL>','SYSTEM','SLA响应时间违约告警','工单号: OPS-20250810-052\n工单标题: 但字节的前端和产品经理没理解什么叫做尽可能的集约空间，如何有效地在尽可能小的屏幕下塞更多内容。...\n客户: 示例科技有限公司\nSLA状态: SLA响应时间违约告警\n\n响应时间状态:\n- 已用时间: 32分钟\n- 剩余时间: -2分钟\n- 完成度: 106.67%\n- 是否违约: 是\n\n解决时间状态:\n- 已用时间: 0小时  \n- 剩余时间: 8小时\n- 完成度: 0%\n- 是否违约: 否\n\n风险等级: CRITICAL','SENT',NULL,'2025-08-10 12:00:00.162'),('cme6m66tb000assavd92k9ajg',NULL,'<EMAIL>','SYSTEM','SLA严重违约告警','工单号: OPS-20250810-052\n工单标题: 但字节的前端和产品经理没理解什么叫做尽可能的集约空间，如何有效地在尽可能小的屏幕下塞更多内容。...\n客户: 示例科技有限公司\nSLA状态: SLA严重违约告警\n\n响应时间状态:\n- 已用时间: 1022分钟\n- 剩余时间: -992分钟\n- 完成度: 3406.67%\n- 是否违约: 是\n\n解决时间状态:\n- 已用时间: 17小时  \n- 剩余时间: -9小时\n- 完成度: 212.5%\n- 是否违约: 是\n\n风险等级: CRITICAL','SENT',NULL,'2025-08-11 04:30:00.191'),('cme6obcjy000aj9eggzllmsau',NULL,'<EMAIL>','SYSTEM','SLA严重违约告警','工单号: OPS-20250810-052\n工单标题: 但字节的前端和产品经理没理解什么叫做尽可能的集约空间，如何有效地在尽可能小的屏幕下塞更多内容。...\n客户: 示例科技有限公司\nSLA状态: SLA严重违约告警\n\n响应时间状态:\n- 已用时间: 1082分钟\n- 剩余时间: -1052分钟\n- 完成度: 3606.67%\n- 是否违约: 是\n\n解决时间状态:\n- 已用时间: 18小时  \n- 剩余时间: -10小时\n- 完成度: 225%\n- 是否违约: 是\n\n风险等级: CRITICAL','SENT',NULL,'2025-08-11 05:30:00.142'),('cme6pdxhg000ej9egl1ausetl',NULL,'<EMAIL>','SYSTEM','SLA严重违约告警','工单号: OPS-20250810-052\n工单标题: 但字节的前端和产品经理没理解什么叫做尽可能的集约空间，如何有效地在尽可能小的屏幕下塞更多内容。...\n客户: 示例科技有限公司\nSLA状态: SLA严重违约告警\n\n响应时间状态:\n- 已用时间: 1112分钟\n- 剩余时间: -1082分钟\n- 完成度: 3706.67%\n- 是否违约: 是\n\n解决时间状态:\n- 已用时间: 18小时  \n- 剩余时间: -10小时\n- 完成度: 225%\n- 是否违约: 是\n\n风险等级: CRITICAL','SENT',NULL,'2025-08-11 06:00:00.196'),('cme6qgicq000ij9eg69nj2ocs',NULL,'<EMAIL>','SYSTEM','SLA严重违约告警','工单号: OPS-20250810-052\n工单标题: 但字节的前端和产品经理没理解什么叫做尽可能的集约空间，如何有效地在尽可能小的屏幕下塞更多内容。...\n客户: 示例科技有限公司\nSLA状态: SLA严重违约告警\n\n响应时间状态:\n- 已用时间: 1142分钟\n- 剩余时间: -1112分钟\n- 完成度: 3806.67%\n- 是否违约: 是\n\n解决时间状态:\n- 已用时间: 19小时  \n- 剩余时间: -11小时\n- 完成度: 237.5%\n- 是否违约: 是\n\n风险等级: CRITICAL','SENT',NULL,'2025-08-11 06:30:00.170'),('cme6rj390000mj9egxedne0my',NULL,'<EMAIL>','SYSTEM','SLA严重违约告警','工单号: OPS-20250810-052\n工单标题: 但字节的前端和产品经理没理解什么叫做尽可能的集约空间，如何有效地在尽可能小的屏幕下塞更多内容。...\n客户: 示例科技有限公司\nSLA状态: SLA严重违约告警\n\n响应时间状态:\n- 已用时间: 1172分钟\n- 剩余时间: -1142分钟\n- 完成度: 3906.67%\n- 是否违约: 是\n\n解决时间状态:\n- 已用时间: 19小时  \n- 剩余时间: -11小时\n- 完成度: 237.5%\n- 是否违约: 是\n\n风险等级: CRITICAL','SENT',NULL,'2025-08-11 07:00:00.181'),('cme6slo4u000yj9eg0wo0vz78',NULL,'<EMAIL>','SYSTEM','SLA严重违约告警','工单号: OPS-20250810-052\n工单标题: 但字节的前端和产品经理没理解什么叫做尽可能的集约空间，如何有效地在尽可能小的屏幕下塞更多内容。...\n客户: 示例科技有限公司\nSLA状态: SLA严重违约告警\n\n响应时间状态:\n- 已用时间: 1202分钟\n- 剩余时间: -1172分钟\n- 完成度: 4006.67%\n- 是否违约: 是\n\n解决时间状态:\n- 已用时间: 20小时  \n- 剩余时间: -12小时\n- 完成度: 250%\n- 是否违约: 是\n\n风险等级: CRITICAL','SENT',NULL,'2025-08-11 07:30:00.174'),('cme6to90f0012j9egyfq6ct0t',NULL,'<EMAIL>','SYSTEM','SLA严重违约告警','工单号: OPS-20250810-052\n工单标题: 但字节的前端和产品经理没理解什么叫做尽可能的集约空间，如何有效地在尽可能小的屏幕下塞更多内容。...\n客户: 示例科技有限公司\nSLA状态: SLA严重违约告警\n\n响应时间状态:\n- 已用时间: 1232分钟\n- 剩余时间: -1202分钟\n- 完成度: 4106.67%\n- 是否违约: 是\n\n解决时间状态:\n- 已用时间: 20小时  \n- 剩余时间: -12小时\n- 完成度: 250%\n- 是否违约: 是\n\n风险等级: CRITICAL','SENT',NULL,'2025-08-11 08:00:00.160'),('cme6uqtxk0017j9egk32k6ls9',NULL,'<EMAIL>','SYSTEM','SLA严重违约告警','工单号: OPS-20250810-052\n工单标题: 但字节的前端和产品经理没理解什么叫做尽可能的集约空间，如何有效地在尽可能小的屏幕下塞更多内容。...\n客户: 示例科技有限公司\nSLA状态: SLA严重违约告警\n\n响应时间状态:\n- 已用时间: 1262分钟\n- 剩余时间: -1232分钟\n- 完成度: 4206.67%\n- 是否违约: 是\n\n解决时间状态:\n- 已用时间: 21小时  \n- 剩余时间: -13小时\n- 完成度: 262.5%\n- 是否违约: 是\n\n风险等级: CRITICAL','SENT',NULL,'2025-08-11 08:30:00.200'),('cme6vtes0001bj9eg7bzgjwyw',NULL,'<EMAIL>','SYSTEM','SLA严重违约告警','工单号: OPS-20250810-052\n工单标题: 但字节的前端和产品经理没理解什么叫做尽可能的集约空间，如何有效地在尽可能小的屏幕下塞更多内容。...\n客户: 示例科技有限公司\nSLA状态: SLA严重违约告警\n\n响应时间状态:\n- 已用时间: 1292分钟\n- 剩余时间: -1262分钟\n- 完成度: 4306.67%\n- 是否违约: 是\n\n解决时间状态:\n- 已用时间: 21小时  \n- 剩余时间: -13小时\n- 完成度: 262.5%\n- 是否违约: 是\n\n风险等级: CRITICAL','SENT',NULL,'2025-08-11 09:00:00.144'),('cme703qbs000a6clm3h1uf7n5',NULL,'<EMAIL>','SYSTEM','SLA严重违约告警','工单号: OPS-20250810-052\n工单标题: 但字节的前端和产品经理没理解什么叫做尽可能的集约空间，如何有效地在尽可能小的屏幕下塞更多内容。...\n客户: 示例科技有限公司\nSLA状态: SLA严重违约告警\n\n响应时间状态:\n- 已用时间: 1412分钟\n- 剩余时间: -1382分钟\n- 完成度: 4706.67%\n- 是否违约: 是\n\n解决时间状态:\n- 已用时间: 23小时  \n- 剩余时间: -15小时\n- 完成度: 287.5%\n- 是否违约: 是\n\n风险等级: CRITICAL','SENT',NULL,'2025-08-11 11:00:00.136'),('cme728w61000e6clmycfehd5f',NULL,'<EMAIL>','SYSTEM','SLA严重违约告警','工单号: OPS-20250810-052\n工单标题: 但字节的前端和产品经理没理解什么叫做尽可能的集约空间，如何有效地在尽可能小的屏幕下塞更多内容。...\n客户: 示例科技有限公司\nSLA状态: SLA严重违约告警\n\n响应时间状态:\n- 已用时间: 1472分钟\n- 剩余时间: -1442分钟\n- 完成度: 4906.67%\n- 是否违约: 是\n\n解决时间状态:\n- 已用时间: 24小时  \n- 剩余时间: -16小时\n- 完成度: 300%\n- 是否违约: 是\n\n风险等级: CRITICAL','SENT',NULL,'2025-08-11 12:00:00.218'),('cme73bgyl000i6clmakx1rwbj',NULL,'<EMAIL>','SYSTEM','SLA严重违约告警','工单号: OPS-20250810-052\n工单标题: 但字节的前端和产品经理没理解什么叫做尽可能的集约空间，如何有效地在尽可能小的屏幕下塞更多内容。...\n客户: 示例科技有限公司\nSLA状态: SLA严重违约告警\n\n响应时间状态:\n- 已用时间: 1502分钟\n- 剩余时间: -1472分钟\n- 完成度: 5006.67%\n- 是否违约: 是\n\n解决时间状态:\n- 已用时间: 25小时  \n- 剩余时间: -17小时\n- 完成度: 312.5%\n- 是否违约: 是\n\n风险等级: CRITICAL','SENT',NULL,'2025-08-11 12:30:00.093'),('cme74e1ww000bf0voc1lju9iy',NULL,'<EMAIL>','SYSTEM','SLA严重违约告警','工单号: OPS-20250810-052\n工单标题: 但字节的前端和产品经理没理解什么叫做尽可能的集约空间，如何有效地在尽可能小的屏幕下塞更多内容。...\n客户: 示例科技有限公司\nSLA状态: SLA严重违约告警\n\n响应时间状态:\n- 已用时间: 1532分钟\n- 剩余时间: -1502分钟\n- 完成度: 5106.67%\n- 是否违约: 是\n\n解决时间状态:\n- 已用时间: 25小时  \n- 剩余时间: -17小时\n- 完成度: 312.5%\n- 是否违约: 是\n\n风险等级: CRITICAL','SENT',NULL,'2025-08-11 13:00:00.176'),('cme75gmqm000ff0vocgemus1t',NULL,'<EMAIL>','SYSTEM','SLA严重违约告警','工单号: OPS-20250810-052\n工单标题: 但字节的前端和产品经理没理解什么叫做尽可能的集约空间，如何有效地在尽可能小的屏幕下塞更多内容。...\n客户: 示例科技有限公司\nSLA状态: SLA严重违约告警\n\n响应时间状态:\n- 已用时间: 1562分钟\n- 剩余时间: -1532分钟\n- 完成度: 5206.67%\n- 是否违约: 是\n\n解决时间状态:\n- 已用时间: 26小时  \n- 剩余时间: -18小时\n- 完成度: 325%\n- 是否违约: 是\n\n风险等级: CRITICAL','SENT',NULL,'2025-08-11 13:30:00.095');
/*!40000 ALTER TABLE `notifications` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `operation_logs`
--

DROP TABLE IF EXISTS `operation_logs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `operation_logs` (
  `id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `user_id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `action` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `resource` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `resource_id` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `details` json DEFAULT NULL,
  `ip_address` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `user_agent` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  PRIMARY KEY (`id`),
  KEY `operation_logs_user_id_fkey` (`user_id`),
  CONSTRAINT `operation_logs_user_id_fkey` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `operation_logs`
--

LOCK TABLES `operation_logs` WRITE;
/*!40000 ALTER TABLE `operation_logs` DISABLE KEYS */;
INSERT INTO `operation_logs` VALUES ('cme3rrw6c0007ikh7sm8ub3zl','cme3pnoma0003r8cva4fu9log','VIEW','USER_ANALYTICS',NULL,'\"{\\\"type\\\":\\\"activity_summary\\\",\\\"dateRange\\\":{}}\"','::1',NULL,'2025-08-09 04:43:32.388'),('cme3rv9b0000bikh7mnd836o1','cme3pnoma0003r8cva4fu9log','VIEW','USER_ANALYTICS',NULL,'\"{\\\"type\\\":\\\"activity_summary\\\",\\\"dateRange\\\":{}}\"','::1',NULL,'2025-08-09 04:46:09.371'),('cme3ry7s1000dikh74i1asd4u','cme3pnoma0003r8cva4fu9log','VIEW','USER_ANALYTICS',NULL,'\"{\\\"type\\\":\\\"activity_summary\\\",\\\"dateRange\\\":{}}\"','::1',NULL,'2025-08-09 04:48:27.340'),('cme3ryuzp000fikh7yje0ap9k','cme3pnoma0003r8cva4fu9log','VIEW','USER_ANALYTICS',NULL,'\"{\\\"type\\\":\\\"activity_summary\\\",\\\"dateRange\\\":{}}\"','::1',NULL,'2025-08-09 04:48:57.445'),('cme3s7nua000hikh7lhz9l9wh','cme3pnoma0003r8cva4fu9log','VIEW','USER_ANALYTICS',NULL,'\"{\\\"type\\\":\\\"activity_summary\\\",\\\"dateRange\\\":{}}\"','::1',NULL,'2025-08-09 04:55:48.082'),('cme3v04h90003uz7tua3czick','cme3pnoma0003r8cva4fu9log','VIEW','USER_ANALYTICS',NULL,'\"{\\\"type\\\":\\\"activity_summary\\\",\\\"dateRange\\\":{}}\"','::1',NULL,'2025-08-09 06:13:55.245'),('cme3v0fyt0005uz7txcz2vupd','cme3pnoma0003r8cva4fu9log','VIEW','USER_ANALYTICS',NULL,'\"{\\\"type\\\":\\\"activity_summary\\\",\\\"dateRange\\\":{}}\"','::1',NULL,'2025-08-09 06:14:10.133'),('cme3v0rgr0007uz7tuk5vot8o','cme3pnoma0003r8cva4fu9log','VIEW','USER_ANALYTICS',NULL,'\"{\\\"type\\\":\\\"activity_summary\\\",\\\"dateRange\\\":{}}\"','::1',NULL,'2025-08-09 06:14:25.024'),('cme3v0uqe0009uz7t8axfhkft','cme3pnoma0003r8cva4fu9log','VIEW','USER_ANALYTICS',NULL,'\"{\\\"type\\\":\\\"activity_summary\\\",\\\"dateRange\\\":{}}\"','::1',NULL,'2025-08-09 06:14:29.271'),('cme3v1hx8000buz7t4ftjzt3y','cme3pnoma0003r8cva4fu9log','VIEW','USER_ANALYTICS',NULL,'\"{\\\"type\\\":\\\"activity_summary\\\",\\\"dateRange\\\":{}}\"','::1',NULL,'2025-08-09 06:14:59.320'),('cme3v2550000duz7trs6hu4tx','cme3pnoma0003r8cva4fu9log','VIEW','USER_ANALYTICS',NULL,'\"{\\\"type\\\":\\\"activity_summary\\\",\\\"dateRange\\\":{}}\"','::1',NULL,'2025-08-09 06:15:29.402'),('cme3v2trk000fuz7tr0d8k7x8','cme3pnoma0003r8cva4fu9log','VIEW','USER_ANALYTICS',NULL,'\"{\\\"type\\\":\\\"activity_summary\\\",\\\"dateRange\\\":{}}\"','::1',NULL,'2025-08-09 06:16:01.328'),('cme3v3i36000huz7tzwwej0f0','cme3pnoma0003r8cva4fu9log','VIEW','USER_ANALYTICS',NULL,'\"{\\\"type\\\":\\\"activity_summary\\\",\\\"dateRange\\\":{}}\"','::1',NULL,'2025-08-09 06:16:32.841'),('cme3y8ql40007krebj6aegjqi','cme3pnoma0003r8cva4fu9log','VIEW','USER_ANALYTICS',NULL,'\"{\\\"type\\\":\\\"activity_summary\\\",\\\"dateRange\\\":{}}\"','::1',NULL,'2025-08-09 07:44:35.992'),('cme3y99km0009kreb07q28d2b','cme3pnoma0003r8cva4fu9log','VIEW','USER_ANALYTICS',NULL,'\"{\\\"type\\\":\\\"activity_summary\\\",\\\"dateRange\\\":{}}\"','::1',NULL,'2025-08-09 07:45:00.598'),('cme3yzx7i0001su18i9zw20em','cme3pnoma0003r8cva4fu9log','EXPORT','SERVICE_REPORT','cme3pnooz000lr8cv7wgz7k8z','{\"format\": \"PDF\", \"filename\": \"service-report-OPS-20240101-001-2025-08-09.pdf\", \"ticketNumber\": \"OPS-20240101-001\"}','::1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-08-09 08:05:44.283'),('cme3zfyal0001b1kx1vs9yvmj','cme3pnoma0003r8cva4fu9log','EXPORT','SERVICE_REPORT','cme3pnooz000lr8cv7wgz7k8z','{\"format\": \"PDF\", \"filename\": \"service-report-OPS-20240101-001-2025-08-09.pdf\", \"ticketNumber\": \"OPS-20240101-001\"}','::1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-08-09 08:18:12.185'),('cme3zwy2n0001ppi2xsigbqin','cme3pnoma0003r8cva4fu9log','EXPORT','SERVICE_REPORT','cme3pnooz000lr8cv7wgz7k8z','{\"format\": \"PDF\", \"filename\": \"service-report-OPS-20240101-001-2025-08-09.pdf\", \"ticketNumber\": \"OPS-20240101-001\"}','::1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-08-09 08:31:25.053'),('cme40ab3g0003ppi2nkgusdnf','cme3pnoma0003r8cva4fu9log','EXPORT','SERVICE_REPORT','cme3xte5l0001krebg11wi77j','{\"format\": \"PDF\", \"filename\": \"service-report-OPS-20250809-376-2025-08-09.pdf\", \"ticketNumber\": \"OPS-20250809-376\"}','::1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-08-09 08:41:48.459'),('cme40annz0005ppi2ui9jycle','cme3pnoma0003r8cva4fu9log','EXPORT','SERVICE_REPORT','cme3xte5l0001krebg11wi77j','{\"format\": \"PDF\", \"filename\": \"service-report-OPS-20250809-376-2025-08-09.pdf\", \"ticketNumber\": \"OPS-20250809-376\"}','::1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-08-09 08:42:04.751'),('cme41zzlk0007ppi2gpzy1qkg','cme3pnoma0003r8cva4fu9log','LOGOUT','AUTH',NULL,'null','::1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-08-09 09:29:46.231'),('cme42018l0009ppi2pnsd7bov','cme3pnoma0003r8cva4fu9log','LOGOUT','AUTH',NULL,'null','::1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-08-09 09:29:48.357'),('cme4209i7000dppi22qs55rwy','cme3pnoma0003r8cva4fu9log','LOGIN','AUTH',NULL,'null','::1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-08-09 09:29:59.071'),('cme44wsvb0005q7ieszxp56xw','cme3pnoma0003r8cva4fu9log','VIEW','USER_ANALYTICS',NULL,'\"{\\\"type\\\":\\\"activity_summary\\\",\\\"dateRange\\\":{}}\"','::1',NULL,'2025-08-09 10:51:16.392'),('cme46jklh0007q7ie5voyvqcs','cme3pnoma0003r8cva4fu9log','LOGOUT','AUTH',NULL,'null','::1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-08-09 11:36:58.373'),('cme46yial000bq7ie4yfvwxlu','cme3pnoma0003r8cva4fu9log','LOGIN','AUTH',NULL,'null','::1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-08-09 11:48:35.229'),('cme4znmx7000dq7ie688ki3el','cme3pnoma0003r8cva4fu9log','VIEW','USER_ANALYTICS',NULL,'\"{\\\"type\\\":\\\"activity_summary\\\",\\\"dateRange\\\":{}}\"','::1',NULL,'2025-08-10 01:11:56.875'),('cme51j8t4000fq7ieft6rjr24','cme3pnoma0003r8cva4fu9log','VIEW','USER_ANALYTICS',NULL,'\"{\\\"type\\\":\\\"activity_summary\\\",\\\"dateRange\\\":{}}\"','::1',NULL,'2025-08-10 02:04:31.191'),('cme51mt63000hq7ieri4owoe8','cme3pnoma0003r8cva4fu9log','VIEW','USER_ANALYTICS',NULL,'\"{\\\"type\\\":\\\"activity_summary\\\",\\\"dateRange\\\":{}}\"','::1',NULL,'2025-08-10 02:07:17.547'),('cme51nxoa000jq7ie69i7i64z','cme3pnoma0003r8cva4fu9log','VIEW','USER_ANALYTICS',NULL,'\"{\\\"type\\\":\\\"activity_summary\\\",\\\"dateRange\\\":{}}\"','::1',NULL,'2025-08-10 02:08:10.043'),('cme51psjm000lq7iengi1i3p8','cme3pnoma0003r8cva4fu9log','VIEW','USER_ANALYTICS',NULL,'\"{\\\"type\\\":\\\"activity_summary\\\",\\\"dateRange\\\":{}}\"','::1',NULL,'2025-08-10 02:09:36.706'),('cme51r2vc000nq7iekff9utbd','cme3pnoma0003r8cva4fu9log','VIEW','USER_ANALYTICS',NULL,'\"{\\\"type\\\":\\\"activity_summary\\\",\\\"dateRange\\\":{}}\"','::1',NULL,'2025-08-10 02:10:36.739'),('cme51rss2000pq7iegoz1r55f','cme3pnoma0003r8cva4fu9log','VIEW','USER_ANALYTICS',NULL,'\"{\\\"type\\\":\\\"activity_summary\\\",\\\"dateRange\\\":{}}\"','::1',NULL,'2025-08-10 02:11:10.299'),('cme51s0t0000rq7ieg8zeq8y4','cme3pnoma0003r8cva4fu9log','VIEW','USER_ANALYTICS',NULL,'\"{\\\"type\\\":\\\"activity_summary\\\",\\\"dateRange\\\":{}}\"','::1',NULL,'2025-08-10 02:11:20.724'),('cme51s396000tq7iepdrumxlx','cme3pnoma0003r8cva4fu9log','VIEW','USER_ANALYTICS',NULL,'\"{\\\"type\\\":\\\"activity_summary\\\",\\\"dateRange\\\":{}}\"','::1',NULL,'2025-08-10 02:11:23.898'),('cme51s8or000vq7ie5udufcjg','cme3pnoma0003r8cva4fu9log','VIEW','USER_ANALYTICS',NULL,'\"{\\\"type\\\":\\\"activity_summary\\\",\\\"dateRange\\\":{}}\"','::1',NULL,'2025-08-10 02:11:30.940'),('cme51scch000xq7iefubjd9it','cme3pnoma0003r8cva4fu9log','VIEW','USER_ANALYTICS',NULL,'\"{\\\"type\\\":\\\"activity_summary\\\",\\\"dateRange\\\":{}}\"','::1',NULL,'2025-08-10 02:11:35.680'),('cme51skec000zq7ies8kg7kzu','cme3pnoma0003r8cva4fu9log','VIEW','USER_ANALYTICS',NULL,'\"{\\\"type\\\":\\\"activity_summary\\\",\\\"dateRange\\\":{}}\"','::1',NULL,'2025-08-10 02:11:46.111'),('cme51srhd0011q7iey1xlp7pf','cme3pnoma0003r8cva4fu9log','VIEW','USER_ANALYTICS',NULL,'\"{\\\"type\\\":\\\"activity_summary\\\",\\\"dateRange\\\":{}}\"','::1',NULL,'2025-08-10 02:11:55.297'),('cme51t0f40013q7ienox9n3m2','cme3pnoma0003r8cva4fu9log','VIEW','USER_ANALYTICS',NULL,'\"{\\\"type\\\":\\\"activity_summary\\\",\\\"dateRange\\\":{}}\"','::1',NULL,'2025-08-10 02:12:06.863'),('cme51t73o0015q7ie7004kki6','cme3pnoma0003r8cva4fu9log','VIEW','USER_ANALYTICS',NULL,'\"{\\\"type\\\":\\\"activity_summary\\\",\\\"dateRange\\\":{}}\"','::1',NULL,'2025-08-10 02:12:15.540'),('cme51uhh60017q7ieodjqocv6','cme3pnoma0003r8cva4fu9log','VIEW','USER_ANALYTICS',NULL,'\"{\\\"type\\\":\\\"activity_summary\\\",\\\"dateRange\\\":{}}\"','::1',NULL,'2025-08-10 02:13:15.638'),('cme51umo60019q7iemd5ynskr','cme3pnoma0003r8cva4fu9log','VIEW','USER_ANALYTICS',NULL,'\"{\\\"type\\\":\\\"activity_summary\\\",\\\"dateRange\\\":{}}\"','::1',NULL,'2025-08-10 02:13:22.337'),('cme51vl21001bq7ieqza7aw94','cme3pnoma0003r8cva4fu9log','VIEW','USER_ANALYTICS',NULL,'\"{\\\"type\\\":\\\"activity_summary\\\",\\\"dateRange\\\":{}}\"','::1',NULL,'2025-08-10 02:14:06.933'),('cme51vyng001dq7iekaoeh42s','cme3pnoma0003r8cva4fu9log','VIEW','USER_ANALYTICS',NULL,'\"{\\\"type\\\":\\\"activity_summary\\\",\\\"dateRange\\\":{}}\"','::1',NULL,'2025-08-10 02:14:24.556'),('cme51wby9001fq7iejnp6r0mc','cme3pnoma0003r8cva4fu9log','VIEW','USER_ANALYTICS',NULL,'\"{\\\"type\\\":\\\"activity_summary\\\",\\\"dateRange\\\":{}}\"','::1',NULL,'2025-08-10 02:14:41.793'),('cme51xmaw001hq7ieyy5aaeaq','cme3pnoma0003r8cva4fu9log','VIEW','USER_ANALYTICS',NULL,'\"{\\\"type\\\":\\\"activity_summary\\\",\\\"dateRange\\\":{}}\"','::1',NULL,'2025-08-10 02:15:41.862'),('cme51ywmx001jq7ie382apcvk','cme3pnoma0003r8cva4fu9log','VIEW','USER_ANALYTICS',NULL,'\"{\\\"type\\\":\\\"activity_summary\\\",\\\"dateRange\\\":{}}\"','::1',NULL,'2025-08-10 02:16:41.912'),('cme520kul001lq7ie4xpdr73i','cme3pnoma0003r8cva4fu9log','VIEW','USER_ANALYTICS',NULL,'\"{\\\"type\\\":\\\"activity_summary\\\",\\\"dateRange\\\":{}}\"','::1',NULL,'2025-08-10 02:17:59.942'),('cme520ndr001nq7ieqzpctb7m','cme3pnoma0003r8cva4fu9log','VIEW','USER_ANALYTICS',NULL,'\"{\\\"type\\\":\\\"activity_summary\\\",\\\"dateRange\\\":{}}\"','::1',NULL,'2025-08-10 02:18:03.231'),('cme521ga3001pq7iekn30n3fd','cme3pnoma0003r8cva4fu9log','VIEW','USER_ANALYTICS',NULL,'\"{\\\"type\\\":\\\"activity_summary\\\",\\\"dateRange\\\":{}}\"','::1',NULL,'2025-08-10 02:18:40.681'),('cme5228do001rq7ierqkce63a','cme3pnoma0003r8cva4fu9log','VIEW','USER_ANALYTICS',NULL,'\"{\\\"type\\\":\\\"activity_summary\\\",\\\"dateRange\\\":{}}\"','::1',NULL,'2025-08-10 02:19:17.085'),('cme522b1l001tq7ie0d0pso7f','cme3pnoma0003r8cva4fu9log','VIEW','USER_ANALYTICS',NULL,'\"{\\\"type\\\":\\\"activity_summary\\\",\\\"dateRange\\\":{}}\"','::1',NULL,'2025-08-10 02:19:20.553'),('cme522tv5001vq7ienv0k9q1l','cme3pnoma0003r8cva4fu9log','VIEW','USER_ANALYTICS',NULL,'\"{\\\"type\\\":\\\"activity_summary\\\",\\\"dateRange\\\":{}}\"','::1',NULL,'2025-08-10 02:19:44.945'),('cme523hwf001xq7ie17sxd7ej','cme3pnoma0003r8cva4fu9log','VIEW','USER_ANALYTICS',NULL,'\"{\\\"type\\\":\\\"activity_summary\\\",\\\"dateRange\\\":{}}\"','::1',NULL,'2025-08-10 02:20:16.095'),('cme524t1b001zq7iehx66negu','cme3pnoma0003r8cva4fu9log','VIEW','USER_ANALYTICS',NULL,'\"{\\\"type\\\":\\\"activity_summary\\\",\\\"dateRange\\\":{}}\"','::1',NULL,'2025-08-10 02:21:17.183'),('cme5263e20021q7iebajt5f40','cme3pnoma0003r8cva4fu9log','VIEW','USER_ANALYTICS',NULL,'\"{\\\"type\\\":\\\"activity_summary\\\",\\\"dateRange\\\":{}}\"','::1',NULL,'2025-08-10 02:22:17.257'),('cme52709d0023q7ied3e2jlg9','cme3pnoma0003r8cva4fu9log','VIEW','USER_ANALYTICS',NULL,'\"{\\\"type\\\":\\\"activity_summary\\\",\\\"dateRange\\\":{}}\"','::1',NULL,'2025-08-10 02:22:59.857'),('cme528am60025q7iefnt4ercp','cme3pnoma0003r8cva4fu9log','VIEW','USER_ANALYTICS',NULL,'\"{\\\"type\\\":\\\"activity_summary\\\",\\\"dateRange\\\":{}}\"','::1',NULL,'2025-08-10 02:23:59.927'),('cme529ljh0027q7iefahmd8x9','cme3pnoma0003r8cva4fu9log','VIEW','USER_ANALYTICS',NULL,'\"{\\\"type\\\":\\\"activity_summary\\\",\\\"dateRange\\\":{}}\"','::1',NULL,'2025-08-10 02:25:00.749'),('cme52awdl0029q7ie2fgthiuc','cme3pnoma0003r8cva4fu9log','VIEW','USER_ANALYTICS',NULL,'\"{\\\"type\\\":\\\"activity_summary\\\",\\\"dateRange\\\":{}}\"','::1',NULL,'2025-08-10 02:26:01.450'),('cme52c6pk002bq7ieyfudex4k','cme3pnoma0003r8cva4fu9log','VIEW','USER_ANALYTICS',NULL,'\"{\\\"type\\\":\\\"activity_summary\\\",\\\"dateRange\\\":{}}\"','::1',NULL,'2025-08-10 02:27:01.495'),('cme52dh3l002dq7iecmtlpb7n','cme3pnoma0003r8cva4fu9log','VIEW','USER_ANALYTICS',NULL,'\"{\\\"type\\\":\\\"activity_summary\\\",\\\"dateRange\\\":{}}\"','::1',NULL,'2025-08-10 02:28:01.560'),('cme52erfs002fq7iei8325465','cme3pnoma0003r8cva4fu9log','VIEW','USER_ANALYTICS',NULL,'\"{\\\"type\\\":\\\"activity_summary\\\",\\\"dateRange\\\":{}}\"','::1',NULL,'2025-08-10 02:29:01.672'),('cme52g1tr002hq7iek9vs70wf','cme3pnoma0003r8cva4fu9log','VIEW','USER_ANALYTICS',NULL,'\"{\\\"type\\\":\\\"activity_summary\\\",\\\"dateRange\\\":{}}\"','::1',NULL,'2025-08-10 02:30:01.791'),('cme52hc85002jq7ieayp0fe5g','cme3pnoma0003r8cva4fu9log','VIEW','USER_ANALYTICS',NULL,'\"{\\\"type\\\":\\\"activity_summary\\\",\\\"dateRange\\\":{}}\"','::1',NULL,'2025-08-10 02:31:01.885'),('cme52io62002lq7iexu4slnj1','cme3pnoma0003r8cva4fu9log','VIEW','USER_ANALYTICS',NULL,'\"{\\\"type\\\":\\\"activity_summary\\\",\\\"dateRange\\\":{}}\"','::1',NULL,'2025-08-10 02:32:04.059'),('cme52jykb002nq7ienh2iw0tm','cme3pnoma0003r8cva4fu9log','VIEW','USER_ANALYTICS',NULL,'\"{\\\"type\\\":\\\"activity_summary\\\",\\\"dateRange\\\":{}}\"','::1',NULL,'2025-08-10 02:33:04.180'),('cme52l8ye002pq7iesn6494wl','cme3pnoma0003r8cva4fu9log','VIEW','USER_ANALYTICS',NULL,'\"{\\\"type\\\":\\\"activity_summary\\\",\\\"dateRange\\\":{}}\"','::1',NULL,'2025-08-10 02:34:04.275'),('cme52mjc2002rq7iecjdk54cy','cme3pnoma0003r8cva4fu9log','VIEW','USER_ANALYTICS',NULL,'\"{\\\"type\\\":\\\"activity_summary\\\",\\\"dateRange\\\":{}}\"','::1',NULL,'2025-08-10 02:35:04.418'),('cme531lpi002tq7ie832ywn1g','cme3pnoma0003r8cva4fu9log','VIEW','USER_ANALYTICS',NULL,'\"{\\\"type\\\":\\\"activity_summary\\\",\\\"dateRange\\\":{}}\"','::1',NULL,'2025-08-10 02:46:47.334'),('cme531uz9002vq7ie60y3itxo','cme3pnoma0003r8cva4fu9log','VIEW','USER_ANALYTICS',NULL,'\"{\\\"type\\\":\\\"activity_summary\\\",\\\"dateRange\\\":{}}\"','::1',NULL,'2025-08-10 02:46:59.347'),('cme531w8e002xq7ie7mv5xj6q','cme3pnoma0003r8cva4fu9log','VIEW','USER_ANALYTICS',NULL,'\"{\\\"type\\\":\\\"activity_summary\\\",\\\"dateRange\\\":{}}\"','::1',NULL,'2025-08-10 02:47:00.974'),('cme531wjc002zq7ievr6nfrjg','cme3pnoma0003r8cva4fu9log','VIEW','USER_ANALYTICS',NULL,'\"{\\\"type\\\":\\\"activity_summary\\\",\\\"dateRange\\\":{}}\"','::1',NULL,'2025-08-10 02:47:01.369'),('cme5336uj0031q7ieaw2iobh1','cme3pnoma0003r8cva4fu9log','VIEW','USER_ANALYTICS',NULL,'\"{\\\"type\\\":\\\"activity_summary\\\",\\\"dateRange\\\":{}}\"','::1',NULL,'2025-08-10 02:48:01.387'),('cme534h6j0033q7iezkmbmyda','cme3pnoma0003r8cva4fu9log','VIEW','USER_ANALYTICS',NULL,'\"{\\\"type\\\":\\\"activity_summary\\\",\\\"dateRange\\\":{}}\"','::1',NULL,'2025-08-10 02:49:01.425'),('cme535riy0035q7ie16km3p4v','cme3pnoma0003r8cva4fu9log','VIEW','USER_ANALYTICS',NULL,'\"{\\\"type\\\":\\\"activity_summary\\\",\\\"dateRange\\\":{}}\"','::1',NULL,'2025-08-10 02:50:01.495'),('cme5371v20037q7ievype4xvj','cme3pnoma0003r8cva4fu9log','VIEW','USER_ANALYTICS',NULL,'\"{\\\"type\\\":\\\"activity_summary\\\",\\\"dateRange\\\":{}}\"','::1',NULL,'2025-08-10 02:51:01.550'),('cme538c7x0039q7iecptc3926','cme3pnoma0003r8cva4fu9log','VIEW','USER_ANALYTICS',NULL,'\"{\\\"type\\\":\\\"activity_summary\\\",\\\"dateRange\\\":{}}\"','::1',NULL,'2025-08-10 02:52:01.629'),('cme539mlt003bq7ie8aaxp12g','cme3pnoma0003r8cva4fu9log','VIEW','USER_ANALYTICS',NULL,'\"{\\\"type\\\":\\\"activity_summary\\\",\\\"dateRange\\\":{}}\"','::1',NULL,'2025-08-10 02:53:01.737'),('cme53awxw003dq7ieyxntke7f','cme3pnoma0003r8cva4fu9log','VIEW','USER_ANALYTICS',NULL,'\"{\\\"type\\\":\\\"activity_summary\\\",\\\"dateRange\\\":{}}\"','::1',NULL,'2025-08-10 02:54:01.796'),('cme53c7an003fq7ieztcrj9e4','cme3pnoma0003r8cva4fu9log','VIEW','USER_ANALYTICS',NULL,'\"{\\\"type\\\":\\\"activity_summary\\\",\\\"dateRange\\\":{}}\"','::1',NULL,'2025-08-10 02:55:01.860'),('cme53dhmo003hq7iey9zid3m2','cme3pnoma0003r8cva4fu9log','VIEW','USER_ANALYTICS',NULL,'\"{\\\"type\\\":\\\"activity_summary\\\",\\\"dateRange\\\":{}}\"','::1',NULL,'2025-08-10 02:56:01.920'),('cme53erzg003jq7iekho0p6pm','cme3pnoma0003r8cva4fu9log','VIEW','USER_ANALYTICS',NULL,'\"{\\\"type\\\":\\\"activity_summary\\\",\\\"dateRange\\\":{}}\"','::1',NULL,'2025-08-10 02:57:01.996'),('cme53g2cs003lq7iep10b8efc','cme3pnoma0003r8cva4fu9log','VIEW','USER_ANALYTICS',NULL,'\"{\\\"type\\\":\\\"activity_summary\\\",\\\"dateRange\\\":{}}\"','::1',NULL,'2025-08-10 02:58:02.051'),('cme53hcpn003nq7iepnc5b9dw','cme3pnoma0003r8cva4fu9log','VIEW','USER_ANALYTICS',NULL,'\"{\\\"type\\\":\\\"activity_summary\\\",\\\"dateRange\\\":{}}\"','::1',NULL,'2025-08-10 02:59:02.169'),('cme53in2i003pq7ie5q9785gg','cme3pnoma0003r8cva4fu9log','VIEW','USER_ANALYTICS',NULL,'\"{\\\"type\\\":\\\"activity_summary\\\",\\\"dateRange\\\":{}}\"','::1',NULL,'2025-08-10 03:00:02.249'),('cme53jlah003rq7ie1eeatgb2','cme3pnoma0003r8cva4fu9log','VIEW','USER_ANALYTICS',NULL,'\"{\\\"type\\\":\\\"activity_summary\\\",\\\"dateRange\\\":{}}\"','::1',NULL,'2025-08-10 03:00:46.602'),('cme53kvmj003tq7iepn54vzvp','cme3pnoma0003r8cva4fu9log','VIEW','USER_ANALYTICS',NULL,'\"{\\\"type\\\":\\\"activity_summary\\\",\\\"dateRange\\\":{}}\"','::1',NULL,'2025-08-10 03:01:46.648'),('cme53m5yu003vq7ie112hzijs','cme3pnoma0003r8cva4fu9log','VIEW','USER_ANALYTICS',NULL,'\"{\\\"type\\\":\\\"activity_summary\\\",\\\"dateRange\\\":{}}\"','::1',NULL,'2025-08-10 03:02:46.710'),('cme5d56l10005dy4etjwfhw5a','cme3pnoma0003r8cva4fu9log','LOGIN','AUTH',NULL,'null','::ffff:1********','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-08-10 07:29:30.517'),('cme5d56la0007dy4eu4vaj6uw','cme3pnoma0003r8cva4fu9log','LOGIN','AUTH',NULL,'null','::ffff:1********','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-08-10 07:29:30.527'),('cme5d56ni000bdy4epwzan6wa','cme3pnoma0003r8cva4fu9log','LOGIN','AUTH',NULL,'null','::ffff:1********','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-08-10 07:29:30.606'),('cme5iyv9z0003wposk50cixad','cme3pnoma0003r8cva4fu9log','AI_ANALYZE','TICKET_CONTENT',NULL,'\"{\\\"descriptionLength\\\":5,\\\"hasContext\\\":true,\\\"success\\\":true}\"','::1',NULL,'2025-08-10 10:12:33.624'),('cme5j45x10008wposlpiqgp2b','cme3pnoma0003r8cva4fu9log','AI_ANALYZE','TICKET_CONTENT',NULL,'\"{\\\"descriptionLength\\\":29,\\\"hasContext\\\":true,\\\"success\\\":true}\"','::1',NULL,'2025-08-10 10:16:40.693'),('cme5j5zqp0003b2nkinxvif5z','cme3pnoma0003r8cva4fu9log','AI_ANALYZE','TICKET_CONTENT',NULL,'\"{\\\"descriptionLength\\\":30,\\\"hasContext\\\":true,\\\"success\\\":true}\"','::1',NULL,'2025-08-10 10:18:06.002'),('cme5j6awa0007b2nkrjdx2l5j','cme3pnoma0003r8cva4fu9log','AI_ANALYZE','TICKET_CONTENT',NULL,'\"{\\\"descriptionLength\\\":20,\\\"hasContext\\\":true,\\\"success\\\":true}\"','::1',NULL,'2025-08-10 10:18:20.458'),('cme5jytv60005hpp9sxs8wv82','cme3pnoma0003r8cva4fu9log','LOGIN','AUTH',NULL,'null','::1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-08-10 10:40:31.410'),('cme5l001i0009hpp9tifnypq5','cme3pnoma0003r8cva4fu9log','AI_ANALYZE','TICKET_CONTENT',NULL,'\"{\\\"descriptionLength\\\":22,\\\"hasContext\\\":true,\\\"success\\\":true}\"','::1',NULL,'2025-08-10 11:09:25.686'),('cme5l1dyo000dhpp90p4o3rv8','cme3pnoma0003r8cva4fu9log','AI_ANALYZE','TICKET_CONTENT',NULL,'\"{\\\"descriptionLength\\\":183,\\\"hasContext\\\":true,\\\"success\\\":true}\"','::1',NULL,'2025-08-10 11:10:30.384'),('cme5ldqjo000ihpp9evc9liv2','cme3pnoma0003r8cva4fu9log','AI_ANALYZE','TICKET_CONTENT',NULL,'\"{\\\"descriptionLength\\\":183,\\\"hasContext\\\":true,\\\"success\\\":true}\"','::1',NULL,'2025-08-10 11:20:06.564'),('cme5lk8b5000mhpp9gu9hjszk','cme3pnoma0003r8cva4fu9log','AI_ANALYZE','TICKET_CONTENT',NULL,'\"{\\\"descriptionLength\\\":269,\\\"hasContext\\\":true,\\\"success\\\":true}\"','::1',NULL,'2025-08-10 11:25:09.521'),('cme5m49k00003ig6yq6itdrny','cme3pnoma0003r8cva4fu9log','AI_ANALYZE','TICKET_CONTENT',NULL,'\"{\\\"descriptionLength\\\":115,\\\"hasContext\\\":true,\\\"success\\\":true}\"','::1',NULL,'2025-08-10 11:40:44.257'),('cme5m97x60003yigvebf2ww5e','cme3pnoma0003r8cva4fu9log','AI_ANALYZE','TICKET_CONTENT',NULL,'\"{\\\"descriptionLength\\\":115,\\\"hasContext\\\":true,\\\"success\\\":true}\"','::1',NULL,'2025-08-10 11:44:35.419'),('cme5mc6fr000334leu9wf4uvh','cme3pnoma0003r8cva4fu9log','AI_ANALYZE','TICKET_CONTENT',NULL,'\"{\\\"descriptionLength\\\":115,\\\"hasContext\\\":true,\\\"success\\\":true}\"','::1',NULL,'2025-08-10 11:46:53.463'),('cme5mfwsu000734led13xp8nj','cme3pnoma0003r8cva4fu9log','AI_ANALYZE','TICKET_CONTENT',NULL,'\"{\\\"descriptionLength\\\":19,\\\"hasContext\\\":true,\\\"success\\\":true}\"','::1',NULL,'2025-08-10 11:49:47.598'),('cme5miq3n000312bd2ec232j6','cme3pnoma0003r8cva4fu9log','AI_ANALYZE','TICKET_CONTENT',NULL,'\"{\\\"descriptionLength\\\":84,\\\"hasContext\\\":true,\\\"success\\\":true}\"','::1',NULL,'2025-08-10 11:51:58.883'),('cme5mke3h0003ar4qe9rmwzo9','cme3pnoma0003r8cva4fu9log','AI_ANALYZE','TICKET_CONTENT',NULL,'\"{\\\"descriptionLength\\\":114,\\\"hasContext\\\":true,\\\"success\\\":true}\"','::1',NULL,'2025-08-10 11:53:16.637'),('cme5ml0pg0005ar4qaivm23cu','cme3pnoma0003r8cva4fu9log','AI_ANALYZE','TICKET_CONTENT',NULL,'\"{\\\"descriptionLength\\\":117,\\\"hasContext\\\":true,\\\"success\\\":true}\"','::1',NULL,'2025-08-10 11:53:45.940'),('cme5mo3sa0009ar4qyalzw6sx','cme3pnoma0003r8cva4fu9log','AI_ANALYZE','TICKET_CONTENT',NULL,'\"{\\\"descriptionLength\\\":47,\\\"hasContext\\\":true,\\\"success\\\":true}\"','::1',NULL,'2025-08-10 11:56:09.898'),('cme6gm74i000crm1yrexsegme','cme3pnoma0003r8cva4fu9log','LOGIN','AUTH',NULL,'null','::1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','2025-08-11 01:54:29.394'),('cme6nm1170003rux3psmk3dmj','cme3pnoma0003r8cva4fu9log','LOGIN','AUTH',NULL,'null','::1','curl/8.7.1','2025-08-11 05:10:18.811'),('cme6nsdah00044c7g7ds8xfzs','cme3pnoma0003r8cva4fu9log','LOGIN','AUTH',NULL,'null','::1','curl/8.7.1','2025-08-11 05:15:14.633');
/*!40000 ALTER TABLE `operation_logs` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `performance_benchmarks`
--

DROP TABLE IF EXISTS `performance_benchmarks`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `performance_benchmarks` (
  `id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `category` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `metric` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `baseline` double NOT NULL,
  `target` double NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `enabled` tinyint(1) NOT NULL DEFAULT '1',
  `created_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `updated_at` datetime(3) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `performance_benchmarks_category_metric_key` (`category`,`metric`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `performance_benchmarks`
--

LOCK TABLES `performance_benchmarks` WRITE;
/*!40000 ALTER TABLE `performance_benchmarks` DISABLE KEYS */;
/*!40000 ALTER TABLE `performance_benchmarks` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `permission_template_history`
--

DROP TABLE IF EXISTS `permission_template_history`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `permission_template_history` (
  `id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `template_id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `action` enum('CREATE','UPDATE','DELETE','APPLY','EXPORT','IMPORT','COPY') COLLATE utf8mb4_unicode_ci NOT NULL,
  `old_data` json DEFAULT NULL,
  `new_data` json DEFAULT NULL,
  `change_reason` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `changed_by` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  PRIMARY KEY (`id`),
  KEY `permission_template_history_template_id_fkey` (`template_id`),
  KEY `permission_template_history_changed_by_fkey` (`changed_by`),
  CONSTRAINT `permission_template_history_changed_by_fkey` FOREIGN KEY (`changed_by`) REFERENCES `users` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE,
  CONSTRAINT `permission_template_history_template_id_fkey` FOREIGN KEY (`template_id`) REFERENCES `permission_templates` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `permission_template_history`
--

LOCK TABLES `permission_template_history` WRITE;
/*!40000 ALTER TABLE `permission_template_history` DISABLE KEYS */;
/*!40000 ALTER TABLE `permission_template_history` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `permission_template_usage`
--

DROP TABLE IF EXISTS `permission_template_usage`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `permission_template_usage` (
  `id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `template_id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `role_id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `applied_by` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `applied_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `status` enum('ACTIVE','INACTIVE','SUPERSEDED') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'ACTIVE',
  `note` text COLLATE utf8mb4_unicode_ci,
  PRIMARY KEY (`id`),
  UNIQUE KEY `permission_template_usage_template_id_role_id_key` (`template_id`,`role_id`),
  KEY `permission_template_usage_role_id_fkey` (`role_id`),
  KEY `permission_template_usage_applied_by_fkey` (`applied_by`),
  CONSTRAINT `permission_template_usage_applied_by_fkey` FOREIGN KEY (`applied_by`) REFERENCES `users` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE,
  CONSTRAINT `permission_template_usage_role_id_fkey` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `permission_template_usage_template_id_fkey` FOREIGN KEY (`template_id`) REFERENCES `permission_templates` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `permission_template_usage`
--

LOCK TABLES `permission_template_usage` WRITE;
/*!40000 ALTER TABLE `permission_template_usage` DISABLE KEYS */;
/*!40000 ALTER TABLE `permission_template_usage` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `permission_templates`
--

DROP TABLE IF EXISTS `permission_templates`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `permission_templates` (
  `id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `permissions` json NOT NULL,
  `category` enum('SYSTEM','BUSINESS','SERVICE','READONLY','CUSTOM') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'CUSTOM',
  `is_default` tinyint(1) NOT NULL DEFAULT '0',
  `is_system` tinyint(1) NOT NULL DEFAULT '0',
  `version` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '1.0',
  `metadata` json DEFAULT NULL,
  `created_by` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `updated_by` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `updated_at` datetime(3) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `permission_templates_name_key` (`name`),
  KEY `permission_templates_created_by_fkey` (`created_by`),
  KEY `permission_templates_updated_by_fkey` (`updated_by`),
  CONSTRAINT `permission_templates_created_by_fkey` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE,
  CONSTRAINT `permission_templates_updated_by_fkey` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `permission_templates`
--

LOCK TABLES `permission_templates` WRITE;
/*!40000 ALTER TABLE `permission_templates` DISABLE KEYS */;
/*!40000 ALTER TABLE `permission_templates` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `project_archives`
--

DROP TABLE IF EXISTS `project_archives`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `project_archives` (
  `id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `customer_id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `technology` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `environment` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `version` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `deployment_date` datetime(3) DEFAULT NULL,
  `status` enum('ACTIVE','MAINTENANCE','DEPRECATED','ARCHIVED') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'ACTIVE',
  `created_by` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `updated_at` datetime(3) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `project_archives_customer_id_fkey` (`customer_id`),
  KEY `project_archives_created_by_fkey` (`created_by`),
  CONSTRAINT `project_archives_created_by_fkey` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE,
  CONSTRAINT `project_archives_customer_id_fkey` FOREIGN KEY (`customer_id`) REFERENCES `customers` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `project_archives`
--

LOCK TABLES `project_archives` WRITE;
/*!40000 ALTER TABLE `project_archives` DISABLE KEYS */;
INSERT INTO `project_archives` VALUES ('cme3pnoo2000dr8cvegiklnp2','cme3pnon80007r8cvn6kummym','企业官网系统','公司官方网站，包含产品展示、新闻资讯、联系我们等功能','React.js + Node.js + MySQL','阿里云 ECS + RDS','v2.1.0','2024-01-15 00:00:00.000','ACTIVE','cme3pnoma0003r8cva4fu9log','2025-08-09 03:44:16.802','2025-08-09 03:44:16.802'),('cme3pnq17000d95aj95ml8w7y','cme3pnon80007r8cvn6kummym','企业官网系统','公司官方网站，包含产品展示、新闻资讯、联系我们等功能','React.js + Node.js + MySQL','阿里云 ECS + RDS','v2.1.0','2024-01-15 00:00:00.000','ACTIVE','cme3pnoma0003r8cva4fu9log','2025-08-09 03:44:18.571','2025-08-09 03:44:18.571'),('cme3tlrf7000d133gcbf0a2tc','cme3pnon80007r8cvn6kummym','企业官网系统','公司官方网站，包含产品展示、新闻资讯、联系我们等功能','React.js + Node.js + MySQL','阿里云 ECS + RDS','v2.1.0','2024-01-15 00:00:00.000','ACTIVE','cme3pnoma0003r8cva4fu9log','2025-08-09 05:34:45.524','2025-08-09 05:34:45.524'),('cme3u3zej000ddu4bo103ds32','cme3pnon80007r8cvn6kummym','企业官网系统','公司官方网站，包含产品展示、新闻资讯、联系我们等功能','React.js + Node.js + MySQL','阿里云 ECS + RDS','v2.1.0','2024-01-15 00:00:00.000','ACTIVE','cme3pnoma0003r8cva4fu9log','2025-08-09 05:48:55.675','2025-08-09 05:48:55.675');
/*!40000 ALTER TABLE `project_archives` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `project_configurations`
--

DROP TABLE IF EXISTS `project_configurations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `project_configurations` (
  `id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `archive_id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `config_type` enum('SERVER','DATABASE','VPN','ACCOUNT','ENVIRONMENT','OTHER') COLLATE utf8mb4_unicode_ci NOT NULL,
  `title` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `config_data` json NOT NULL,
  `encrypted_fields` json DEFAULT NULL,
  `description` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `last_updated` datetime(3) DEFAULT NULL,
  `created_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `updated_at` datetime(3) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `project_configurations_archive_id_fkey` (`archive_id`),
  CONSTRAINT `project_configurations_archive_id_fkey` FOREIGN KEY (`archive_id`) REFERENCES `project_archives` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `project_configurations`
--

LOCK TABLES `project_configurations` WRITE;
/*!40000 ALTER TABLE `project_configurations` DISABLE KEYS */;
INSERT INTO `project_configurations` VALUES ('cme3pnooa000fr8cv1i2u3nex','cme3pnoo2000dr8cvegiklnp2','SERVER','生产服务器配置','\"{\\\"host\\\":\\\"47.xxx.xxx.xxx\\\",\\\"port\\\":22,\\\"username\\\":\\\"root\\\",\\\"password\\\":\\\"encrypted_password_here\\\"}\"','\"[\\\"password\\\"]\"','生产环境服务器SSH连接配置',1,NULL,'2025-08-09 03:44:16.810','2025-08-09 03:44:16.810'),('cme3pnoog000hr8cvze84szpr','cme3pnoo2000dr8cvegiklnp2','DATABASE','生产数据库配置','\"{\\\"host\\\":\\\"rm-xxxxx.mysql.rds.aliyuncs.com\\\",\\\"port\\\":3306,\\\"database\\\":\\\"company_website\\\",\\\"username\\\":\\\"admin\\\",\\\"password\\\":\\\"encrypted_db_password_here\\\"}\"','\"[\\\"password\\\"]\"','生产环境MySQL数据库连接配置',1,NULL,'2025-08-09 03:44:16.817','2025-08-09 03:44:16.817'),('cme3pnq1f000f95ajjy6i49db','cme3pnq17000d95aj95ml8w7y','SERVER','生产服务器配置','\"{\\\"host\\\":\\\"47.xxx.xxx.xxx\\\",\\\"port\\\":22,\\\"username\\\":\\\"root\\\",\\\"password\\\":\\\"encrypted_password_here\\\"}\"','\"[\\\"password\\\"]\"','生产环境服务器SSH连接配置',1,NULL,'2025-08-09 03:44:18.580','2025-08-09 03:44:18.580'),('cme3pnq1m000h95ajo3yxtkmo','cme3pnq17000d95aj95ml8w7y','DATABASE','生产数据库配置','\"{\\\"host\\\":\\\"rm-xxxxx.mysql.rds.aliyuncs.com\\\",\\\"port\\\":3306,\\\"database\\\":\\\"company_website\\\",\\\"username\\\":\\\"admin\\\",\\\"password\\\":\\\"encrypted_db_password_here\\\"}\"','\"[\\\"password\\\"]\"','生产环境MySQL数据库连接配置',1,NULL,'2025-08-09 03:44:18.586','2025-08-09 03:44:18.586'),('cme3tlrfi000f133gnyi8a5vl','cme3tlrf7000d133gcbf0a2tc','SERVER','生产服务器配置','\"{\\\"host\\\":\\\"47.xxx.xxx.xxx\\\",\\\"port\\\":22,\\\"username\\\":\\\"root\\\",\\\"password\\\":\\\"encrypted_password_here\\\"}\"','\"[\\\"password\\\"]\"','生产环境服务器SSH连接配置',1,NULL,'2025-08-09 05:34:45.534','2025-08-09 05:34:45.534'),('cme3tlrft000h133g9ns63diy','cme3tlrf7000d133gcbf0a2tc','DATABASE','生产数据库配置','\"{\\\"host\\\":\\\"rm-xxxxx.mysql.rds.aliyuncs.com\\\",\\\"port\\\":3306,\\\"database\\\":\\\"company_website\\\",\\\"username\\\":\\\"admin\\\",\\\"password\\\":\\\"encrypted_db_password_here\\\"}\"','\"[\\\"password\\\"]\"','生产环境MySQL数据库连接配置',1,NULL,'2025-08-09 05:34:45.546','2025-08-09 05:34:45.546'),('cme3u3zew000fdu4bxyvk6evb','cme3u3zej000ddu4bo103ds32','SERVER','生产服务器配置','\"{\\\"host\\\":\\\"47.xxx.xxx.xxx\\\",\\\"port\\\":22,\\\"username\\\":\\\"root\\\",\\\"password\\\":\\\"encrypted_password_here\\\"}\"','\"[\\\"password\\\"]\"','生产环境服务器SSH连接配置',1,NULL,'2025-08-09 05:48:55.688','2025-08-09 05:48:55.688'),('cme3u3zf4000hdu4b1zt9mr0a','cme3u3zej000ddu4bo103ds32','DATABASE','生产数据库配置','\"{\\\"host\\\":\\\"rm-xxxxx.mysql.rds.aliyuncs.com\\\",\\\"port\\\":3306,\\\"database\\\":\\\"company_website\\\",\\\"username\\\":\\\"admin\\\",\\\"password\\\":\\\"encrypted_db_password_here\\\"}\"','\"[\\\"password\\\"]\"','生产环境MySQL数据库连接配置',1,NULL,'2025-08-09 05:48:55.696','2025-08-09 05:48:55.696');
/*!40000 ALTER TABLE `project_configurations` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `roles`
--

DROP TABLE IF EXISTS `roles`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `roles` (
  `id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `permissions` json NOT NULL,
  `created_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `updated_at` datetime(3) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `roles_name_key` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `roles`
--

LOCK TABLES `roles` WRITE;
/*!40000 ALTER TABLE `roles` DISABLE KEYS */;
INSERT INTO `roles` VALUES ('cme3pnnzi0000r8cvmmxia6oz','admin','系统管理员','\"[\\\"admin:all\\\",\\\"user:read\\\",\\\"user:write\\\",\\\"role:read\\\",\\\"role:write\\\",\\\"audit:read\\\",\\\"customer:read\\\",\\\"customer:write\\\",\\\"archive:read\\\",\\\"archive:write\\\",\\\"service:read\\\",\\\"service:write\\\",\\\"config:read\\\",\\\"config:write\\\"]\"','2025-08-09 03:44:15.917','2025-08-09 03:44:15.917'),('cme3pno0c0001r8cv13bhbgdo','engineer','运维工程师','\"[\\\"customer:read\\\",\\\"archive:read\\\",\\\"archive:write\\\",\\\"service:read\\\",\\\"service:write\\\",\\\"config:read\\\",\\\"config:write\\\",\\\"role:read\\\"]\"','2025-08-09 03:44:15.948','2025-08-09 03:44:15.948'),('cme3pno0h0002r8cv3iokwcz8','customer_service','客户服务','\"[\\\"customer:read\\\",\\\"customer:write\\\",\\\"archive:read\\\",\\\"service:read\\\",\\\"service:write\\\"]\"','2025-08-09 03:44:15.953','2025-08-09 03:44:15.953');
/*!40000 ALTER TABLE `roles` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `service_attachments`
--

DROP TABLE IF EXISTS `service_attachments`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `service_attachments` (
  `id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `service_id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `filename` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `original_name` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `file_path` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `file_size` int NOT NULL,
  `mime_type` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `uploaded_by` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `uploaded_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  PRIMARY KEY (`id`),
  KEY `service_attachments_service_id_fkey` (`service_id`),
  KEY `service_attachments_uploaded_by_fkey` (`uploaded_by`),
  CONSTRAINT `service_attachments_service_id_fkey` FOREIGN KEY (`service_id`) REFERENCES `services` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `service_attachments_uploaded_by_fkey` FOREIGN KEY (`uploaded_by`) REFERENCES `users` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `service_attachments`
--

LOCK TABLES `service_attachments` WRITE;
/*!40000 ALTER TABLE `service_attachments` DISABLE KEYS */;
INSERT INTO `service_attachments` VALUES ('cme3rs99n0009ikh75iccy2mp','cme3pnooz000lr8cv7wgz7k8z','da71fecb1b051bf6f38edad6174c1759','SCR-20250802-kkyb.png','uploads/service-attachments/da71fecb1b051bf6f38edad6174c1759',10321,'image/png','cme3pnoma0003r8cva4fu9log','2025-08-09 04:43:49.355');
/*!40000 ALTER TABLE `service_attachments` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `service_availability`
--

DROP TABLE IF EXISTS `service_availability`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `service_availability` (
  `id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `service_name` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `date` date NOT NULL,
  `uptime` double NOT NULL,
  `downtime` double NOT NULL,
  `incidents` int NOT NULL DEFAULT '0',
  `mttr` double DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `service_availability_service_name_date_key` (`service_name`,`date`),
  KEY `service_availability_service_name_date_idx` (`service_name`,`date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `service_availability`
--

LOCK TABLES `service_availability` WRITE;
/*!40000 ALTER TABLE `service_availability` DISABLE KEYS */;
/*!40000 ALTER TABLE `service_availability` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `service_comments`
--

DROP TABLE IF EXISTS `service_comments`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `service_comments` (
  `id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `service_id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `content` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `is_internal` tinyint(1) NOT NULL DEFAULT '0',
  `author_id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `updated_at` datetime(3) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `service_comments_service_id_fkey` (`service_id`),
  KEY `service_comments_author_id_fkey` (`author_id`),
  CONSTRAINT `service_comments_author_id_fkey` FOREIGN KEY (`author_id`) REFERENCES `users` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE,
  CONSTRAINT `service_comments_service_id_fkey` FOREIGN KEY (`service_id`) REFERENCES `services` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `service_comments`
--

LOCK TABLES `service_comments` WRITE;
/*!40000 ALTER TABLE `service_comments` DISABLE KEYS */;
INSERT INTO `service_comments` VALUES ('cme3pnopd000pr8cvxew693oe','cme3pnooz000lr8cv7wgz7k8z','已经定位到问题，主要是数据库查询没有使用索引，计划优化SQL语句。',1,'cme3pnoma0003r8cva4fu9log','2025-08-09 03:44:16.850','2025-08-09 03:44:16.850'),('cme3pnq2d000p95ajljlp05t4','cme3pnooz000lr8cv7wgz7k8z','已经定位到问题，主要是数据库查询没有使用索引，计划优化SQL语句。',1,'cme3pnoma0003r8cva4fu9log','2025-08-09 03:44:18.613','2025-08-09 03:44:18.613'),('cme3tlrgj000p133gq7w8gc1s','cme3pnooz000lr8cv7wgz7k8z','已经定位到问题，主要是数据库查询没有使用索引，计划优化SQL语句。',1,'cme3pnoma0003r8cva4fu9log','2025-08-09 05:34:45.571','2025-08-09 05:34:45.571'),('cme3u3zfy000pdu4b1f10eiu2','cme3pnooz000lr8cv7wgz7k8z','已经定位到问题，主要是数据库查询没有使用索引，计划优化SQL语句。',1,'cme3pnoma0003r8cva4fu9log','2025-08-09 05:48:55.726','2025-08-09 05:48:55.726');
/*!40000 ALTER TABLE `service_comments` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `service_operation_history`
--

DROP TABLE IF EXISTS `service_operation_history`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `service_operation_history` (
  `id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `service_id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `type` enum('CREATE','UPDATE','STATUS_CHANGE','TRANSFER','ASSIGNMENT','COMMENT','ATTACHMENT','WORK_LOG') COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `from_value` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `to_value` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `note` text COLLATE utf8mb4_unicode_ci,
  `user_id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  PRIMARY KEY (`id`),
  KEY `service_operation_history_service_id_fkey` (`service_id`),
  KEY `service_operation_history_user_id_fkey` (`user_id`),
  CONSTRAINT `service_operation_history_service_id_fkey` FOREIGN KEY (`service_id`) REFERENCES `services` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `service_operation_history_user_id_fkey` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `service_operation_history`
--

LOCK TABLES `service_operation_history` WRITE;
/*!40000 ALTER TABLE `service_operation_history` DISABLE KEYS */;
INSERT INTO `service_operation_history` VALUES ('cme3vyxvr000muz7t3akxpczo','cme3pnooz000lr8cv7wgz7k8z','WORK_LOG','添加工作日志 (1小时)',NULL,NULL,NULL,'cme3pnoma0003r8cva4fu9log','2025-08-09 06:40:59.655'),('cme3wg0bq000ouz7toz7iuk12','cme3pnooz000lr8cv7wgz7k8z','STATUS_CHANGE','状态变更','PENDING','IN_PROGRESS',NULL,'cme3pnoma0003r8cva4fu9log','2025-08-09 06:54:15.974'),('cme3xggjg000ruz7t89vrbjpb','cme3pnooz000lr8cv7wgz7k8z','STATUS_CHANGE','状态变更','IN_PROGRESS','WAITING_CUSTOMER',NULL,'cme3pnoma0003r8cva4fu9log','2025-08-09 07:22:36.602'),('cme3xh1x2000tuz7tq5dws579','cme3pnooz000lr8cv7wgz7k8z','STATUS_CHANGE','状态变更','WAITING_CUSTOMER','RESOLVED',NULL,'cme3pnoma0003r8cva4fu9log','2025-08-09 07:23:04.310'),('cme3xhay0000vuz7tcrrlz7kr','cme3pnooz000lr8cv7wgz7k8z','STATUS_CHANGE','状态变更','RESOLVED','CLOSED',NULL,'cme3pnoma0003r8cva4fu9log','2025-08-09 07:23:16.009'),('cme3xte6q0003krebcg8wjcj0','cme3xte5l0001krebg11wi77j','CREATE','创建工单',NULL,NULL,'工单已创建','cme3pnoma0003r8cva4fu9log','2025-08-09 07:32:40.083'),('cme3xtz8s0005krebzdc02orb','cme3xte5l0001krebg11wi77j','STATUS_CHANGE','状态变更','PENDING','IN_PROGRESS',NULL,'cme3pnoma0003r8cva4fu9log','2025-08-09 07:33:07.373'),('cme44t0vq0001q7ieodc2ravk','cme3xte5l0001krebg11wi77j','STATUS_CHANGE','状态变更','IN_PROGRESS','RESOLVED',NULL,'cme3pnoma0003r8cva4fu9log','2025-08-09 10:48:20.148'),('cme44ta9j0003q7ie00a8x2l6','cme3xte5l0001krebg11wi77j','STATUS_CHANGE','状态变更','RESOLVED','CLOSED',NULL,'cme3pnoma0003r8cva4fu9log','2025-08-09 10:48:32.312'),('cme5lndds000qhpp95jcrjhdg','cme5lndd2000ohpp9x0j2kkv6','CREATE','创建工单',NULL,NULL,'工单已创建','cme3pnoma0003r8cva4fu9log','2025-08-10 11:27:36.065');
/*!40000 ALTER TABLE `service_operation_history` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `service_work_logs`
--

DROP TABLE IF EXISTS `service_work_logs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `service_work_logs` (
  `id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `service_id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `user_id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `work_hours` double NOT NULL,
  `work_date` datetime(3) NOT NULL,
  `category` enum('ANALYSIS','IMPLEMENTATION','TESTING','DOCUMENTATION','COMMUNICATION','MAINTENANCE','SUPPORT','OTHER') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'MAINTENANCE',
  `created_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  PRIMARY KEY (`id`),
  KEY `service_work_logs_service_id_fkey` (`service_id`),
  KEY `service_work_logs_user_id_fkey` (`user_id`),
  CONSTRAINT `service_work_logs_service_id_fkey` FOREIGN KEY (`service_id`) REFERENCES `services` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `service_work_logs_user_id_fkey` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `service_work_logs`
--

LOCK TABLES `service_work_logs` WRITE;
/*!40000 ALTER TABLE `service_work_logs` DISABLE KEYS */;
INSERT INTO `service_work_logs` VALUES ('cme3pnop8000nr8cvjc4t5sdy','cme3pnooz000lr8cv7wgz7k8z','cme3pnoma0003r8cva4fu9log','分析网站性能瓶颈，发现数据库查询效率较低',2,'2025-08-09 03:44:16.843','ANALYSIS','2025-08-09 03:44:16.844'),('cme3pnq28000n95aj0wx23d9g','cme3pnooz000lr8cv7wgz7k8z','cme3pnoma0003r8cva4fu9log','分析网站性能瓶颈，发现数据库查询效率较低',2,'2025-08-09 03:44:18.607','ANALYSIS','2025-08-09 03:44:18.608'),('cme3tlrgd000n133gqjfqo9ce','cme3pnooz000lr8cv7wgz7k8z','cme3pnoma0003r8cva4fu9log','分析网站性能瓶颈，发现数据库查询效率较低',2,'2025-08-09 05:34:45.565','ANALYSIS','2025-08-09 05:34:45.566'),('cme3u3zfs000ndu4bemqfvt42','cme3pnooz000lr8cv7wgz7k8z','cme3pnoma0003r8cva4fu9log','分析网站性能瓶颈，发现数据库查询效率较低',2,'2025-08-09 05:48:55.719','ANALYSIS','2025-08-09 05:48:55.720'),('cme3vyxup000kuz7tghr19p9n','cme3pnooz000lr8cv7wgz7k8z','cme3pnoma0003r8cva4fu9log','<h3 id=\"priority-of-configuration-parameters\" class=\"anchor anchorWithHideOnScrollNavbar_WYt5\">Priority of configuration parameters</h3>\n<p>If the configuration parameters are duplicated in the URL, Properties, the&nbsp;<code>priority</code>&nbsp;of the parameters, from highest to lowest, are as follows:</p>\n<ol>\n<li>JDBC URL parameters, as described above, can be specified in the parameters of the JDBC URL.</li>\n<li>Properties connProps</li>\n</ol>',1,'2025-08-09 06:40:37.115','MAINTENANCE','2025-08-09 06:40:59.617');
/*!40000 ALTER TABLE `service_work_logs` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `services`
--

DROP TABLE IF EXISTS `services`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `services` (
  `id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `archive_id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `sla_template_id` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `ticket_number` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `title` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `category` enum('MAINTENANCE','SUPPORT','UPGRADE','BUGFIX','CONSULTING','MONITORING') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'MAINTENANCE',
  `priority` enum('LOW','MEDIUM','HIGH','URGENT') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'MEDIUM',
  `status` enum('PENDING','IN_PROGRESS','WAITING_CUSTOMER','RESOLVED','CLOSED','OPEN') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'PENDING',
  `assigned_to` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `customer_contact` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `start_time` datetime(3) DEFAULT NULL,
  `end_time` datetime(3) DEFAULT NULL,
  `actual_response_time` int DEFAULT NULL,
  `actual_resolution_time` int DEFAULT NULL,
  `estimated_hours` double DEFAULT NULL,
  `actual_hours` double DEFAULT NULL,
  `resolution` text COLLATE utf8mb4_unicode_ci,
  `customer_feedback` text COLLATE utf8mb4_unicode_ci,
  `satisfaction` int DEFAULT NULL,
  `tags` json DEFAULT NULL,
  `first_response_at` datetime(3) DEFAULT NULL,
  `created_by` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `updated_at` datetime(3) NOT NULL,
  `external_account` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `external_system_id` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `external_user_id` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `source` enum('INTERNAL','EXTERNAL') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'INTERNAL',
  PRIMARY KEY (`id`),
  UNIQUE KEY `services_ticket_number_key` (`ticket_number`),
  KEY `services_archive_id_fkey` (`archive_id`),
  KEY `services_sla_template_id_fkey` (`sla_template_id`),
  KEY `services_assigned_to_fkey` (`assigned_to`),
  KEY `services_created_by_fkey` (`created_by`),
  CONSTRAINT `services_archive_id_fkey` FOREIGN KEY (`archive_id`) REFERENCES `project_archives` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE,
  CONSTRAINT `services_assigned_to_fkey` FOREIGN KEY (`assigned_to`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
  CONSTRAINT `services_created_by_fkey` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE,
  CONSTRAINT `services_sla_template_id_fkey` FOREIGN KEY (`sla_template_id`) REFERENCES `sla_templates` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `services`
--

LOCK TABLES `services` WRITE;
/*!40000 ALTER TABLE `services` DISABLE KEYS */;
INSERT INTO `services` VALUES ('cme3pnooz000lr8cv7wgz7k8z','cme3pnoo2000dr8cvegiklnp2','cme3pnoon000ir8cvrsmednwg','OPS-20240101-001','网站首页加载缓慢问题','用户反馈网站首页加载时间过长，影响用户体验，需要优化性能。','SUPPORT','HIGH','CLOSED',NULL,'张三',NULL,'2025-08-09 07:23:04.282',218,NULL,4,9,NULL,NULL,NULL,NULL,'2025-08-09 07:23:15.998','cme3pnoma0003r8cva4fu9log','2025-08-09 03:44:16.835','2025-08-09 07:31:11.643',NULL,NULL,NULL,'INTERNAL'),('cme3xte5l0001krebg11wi77j','cme3u3zej000ddu4bo103ds32','cme3pnoov000jr8cvs5jcuoym','OPS-20250809-376','网站首页打不开','页面空白打不开','BUGFIX','URGENT','CLOSED',NULL,NULL,'2025-08-09 07:32:40.040','2025-08-09 10:48:20.075',0,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'2025-08-09 07:33:07.326','cme3pnoma0003r8cva4fu9log','2025-08-09 07:32:40.040','2025-08-09 10:48:32.278',NULL,NULL,NULL,'INTERNAL'),('cme5lndd2000ohpp9x0j2kkv6','cme3u3zej000ddu4bo103ds32','cme3u3zfh000jdu4b7exti3ky','OPS-20250810-052','但字节的前端和产品经理没理解什么叫做尽可能的集约空间，如何有效地在尽可能小的屏幕下塞更多内容。...','但字节的前端和产品经理没理解什么叫做尽可能的集约空间，如何有效地在尽可能小的屏幕下塞更多内容。Trae 的每个元素与元素、窗口与窗口之间都做了大量的留白，浪费了非常多的、本可以用来展示内容的像素空间。在 Trae 上，笔记本基本上只能开一个代码窗口和一个 AI 对话窗口，这严重降低了编程体验。\n\n作者：胡一鸣\n链接：https://www.zhihu.com/question/12511871108/answer/1919458711082086766\n来源：知乎\n著作权归作者所有。商业转载请联系作者获得授权，非商业转载请注明出处。','SUPPORT','URGENT','PENDING',NULL,NULL,'2025-08-10 11:27:36.036',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,'cme3pnoma0003r8cva4fu9log','2025-08-10 11:27:36.038','2025-08-10 11:27:36.038',NULL,NULL,NULL,'INTERNAL');
/*!40000 ALTER TABLE `services` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sla_templates`
--

DROP TABLE IF EXISTS `sla_templates`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sla_templates` (
  `id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `response_time` int NOT NULL,
  `resolution_time` int NOT NULL,
  `availability` double NOT NULL DEFAULT '99.9',
  `created_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `updated_at` datetime(3) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sla_templates`
--

LOCK TABLES `sla_templates` WRITE;
/*!40000 ALTER TABLE `sla_templates` DISABLE KEYS */;
INSERT INTO `sla_templates` VALUES ('cme3pnoon000ir8cvrsmednwg','标准SLA','标准客户的服务等级协议',60,24,99.5,'2025-08-09 03:44:16.823','2025-08-09 03:44:16.823'),('cme3pnoov000jr8cvs5jcuoym','高级SLA','高级客户的服务等级协议',30,8,99.9,'2025-08-09 03:44:16.832','2025-08-09 03:44:16.832'),('cme3pnq1r000i95ajhzmi4fm4','标准SLA','标准客户的服务等级协议',60,24,99.5,'2025-08-09 03:44:18.592','2025-08-09 03:44:18.592'),('cme3pnq1x000j95ajwoxd9e7v','高级SLA','高级客户的服务等级协议',30,8,99.9,'2025-08-09 03:44:18.597','2025-08-09 03:44:18.597'),('cme3tlrfz000i133gerpajymy','标准SLA','标准客户的服务等级协议',60,24,99.5,'2025-08-09 05:34:45.551','2025-08-09 05:34:45.551'),('cme3tlrg5000j133gitkz122b','高级SLA','高级客户的服务等级协议',30,8,99.9,'2025-08-09 05:34:45.558','2025-08-09 05:34:45.558'),('cme3u3zfb000idu4b2ydvlkbb','标准SLA','标准客户的服务等级协议',60,24,99.5,'2025-08-09 05:48:55.704','2025-08-09 05:48:55.704'),('cme3u3zfh000jdu4b7exti3ky','高级SLA','高级客户的服务等级协议',30,8,99.9,'2025-08-09 05:48:55.710','2025-08-09 05:48:55.710');
/*!40000 ALTER TABLE `sla_templates` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `system_config_history`
--

DROP TABLE IF EXISTS `system_config_history`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `system_config_history` (
  `id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `config_id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `old_value` json DEFAULT NULL,
  `new_value` json NOT NULL,
  `change_reason` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `changed_by` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  PRIMARY KEY (`id`),
  KEY `system_config_history_config_id_fkey` (`config_id`),
  KEY `system_config_history_changed_by_fkey` (`changed_by`),
  CONSTRAINT `system_config_history_changed_by_fkey` FOREIGN KEY (`changed_by`) REFERENCES `users` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE,
  CONSTRAINT `system_config_history_config_id_fkey` FOREIGN KEY (`config_id`) REFERENCES `system_configs` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `system_config_history`
--

LOCK TABLES `system_config_history` WRITE;
/*!40000 ALTER TABLE `system_config_history` DISABLE KEYS */;
INSERT INTO `system_config_history` VALUES ('cme3qtkoh0001ikh77xuw6xlw','cme3pnopw000vr8cvxyrpmuba','\"专业的运维服务管理平台\"','\"专业的运维服务管理平台描述\"','更新配置','cme3pnoma0003r8cva4fu9log','2025-08-09 04:16:51.184'),('cme3qvb7y0003ikh7qasooi5y','cme3pnoqm001br8cvx2x4q9wa','false','true','更新配置','cme3pnoma0003r8cva4fu9log','2025-08-09 04:18:12.238'),('cme3rcs3i0005ikh7396znrc2','cme3pnor0001lr8cv0jwuj89r','\"\"','\"057157507bab901d0c5358e56054e1ee:da95b69191dd9a156497c83d13fc0c8a\"','更新配置','cme3pnoma0003r8cva4fu9log','2025-08-09 04:31:47.263'),('cme3s9863000jikh75u0y4ij4','cme3pnoqp001dr8cvb1w4p068','\"\"','\"smtp.163.com\"','更新配置','cme3pnoma0003r8cva4fu9log','2025-08-09 04:57:01.083'),('cme3s9hnr000likh7ke1223p6','cme3pnoqu001hr8cv1temwgq3','false','true','更新配置','cme3pnoma0003r8cva4fu9log','2025-08-09 04:57:13.383'),('cme3s9zga000nikh7u4bv8ynt','cme3pnoqy001jr8cvtsxscvnb','\"\"','\"<EMAIL>\"','更新配置','cme3pnoma0003r8cva4fu9log','2025-08-09 04:57:36.443'),('cme3sag92000pikh7w1i5ur8y','cme3pnor0001lr8cv0jwuj89r','\"057157507bab901d0c5358e56054e1ee:da95b69191dd9a156497c83d13fc0c8a\"','\"3358143fe5cad1c3841a2768c6da89c4:a8d9c81d901a7d4646b7654d9203c7f4d03ff6545476139c53d44708efbad921\"','更新配置','cme3pnoma0003r8cva4fu9log','2025-08-09 04:57:58.214'),('cme3saow9000rikh7qk33da6a','cme3pnor5001pr8cv763vsr60','\"\"','\"<EMAIL>\"','更新配置','cme3pnoma0003r8cva4fu9log','2025-08-09 04:58:09.418');
/*!40000 ALTER TABLE `system_config_history` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `system_configs`
--

DROP TABLE IF EXISTS `system_configs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `system_configs` (
  `id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `category` enum('GENERAL','SECURITY','EMAIL','SMS','NOTIFICATION','STORAGE','BACKUP','SYSTEM','INTEGRATION','CUSTOM') COLLATE utf8mb4_unicode_ci NOT NULL,
  `key` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `value` json NOT NULL,
  `description` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `data_type` enum('STRING','NUMBER','BOOLEAN','JSON','EMAIL','URL','PASSWORD','TEXTAREA','SELECT','MULTI_SELECT') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'STRING',
  `is_encrypted` tinyint(1) NOT NULL DEFAULT '0',
  `is_system` tinyint(1) NOT NULL DEFAULT '0',
  `is_public` tinyint(1) NOT NULL DEFAULT '0',
  `validation_rule` json DEFAULT NULL,
  `default_value` json DEFAULT NULL,
  `display_order` int DEFAULT NULL,
  `updated_by` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `updated_at` datetime(3) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `system_configs_key_key` (`key`),
  KEY `system_configs_updated_by_fkey` (`updated_by`),
  CONSTRAINT `system_configs_updated_by_fkey` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `system_configs`
--

LOCK TABLES `system_configs` WRITE;
/*!40000 ALTER TABLE `system_configs` DISABLE KEYS */;
INSERT INTO `system_configs` VALUES ('cme3pnops000tr8cvadhf4x4w','GENERAL','SITE_NAME','\"运维服务管理系统\"','网站名称','STRING',0,1,1,NULL,'\"运维服务管理系统\"',1,'cme3pnoma0003r8cva4fu9log','2025-08-09 03:44:16.864','2025-08-09 03:44:16.864'),('cme3pnopw000vr8cvxyrpmuba','GENERAL','SITE_DESCRIPTION','\"专业的运维服务管理平台描述\"','网站描述','STRING',0,1,1,NULL,'\"专业的运维服务管理平台\"',2,'cme3pnoma0003r8cva4fu9log','2025-08-09 03:44:16.868','2025-08-09 04:16:51.118'),('cme3pnoq0000xr8cvs1pv7oq5','GENERAL','COMPANY_NAME','\"多协云科技有限公司\"','公司名称','STRING',0,1,1,NULL,'\"多协云科技有限公司\"',3,'cme3pnoma0003r8cva4fu9log','2025-08-09 03:44:16.873','2025-08-09 03:44:16.873'),('cme3pnoq4000zr8cv6epl73g9','GENERAL','DEFAULT_TIMEZONE','\"Asia/Shanghai\"','默认时区','STRING',0,1,1,NULL,'\"Asia/Shanghai\"',4,'cme3pnoma0003r8cva4fu9log','2025-08-09 03:44:16.876','2025-08-09 03:44:16.876'),('cme3pnoq60011r8cv425bd6xt','GENERAL','DEFAULT_LANGUAGE','\"zh-CN\"','默认语言','STRING',0,1,1,NULL,'\"zh-CN\"',5,'cme3pnoma0003r8cva4fu9log','2025-08-09 03:44:16.879','2025-08-09 03:44:16.879'),('cme3pnoq90013r8cva03m6i5e','SECURITY','PASSWORD_MIN_LENGTH','8','密码最小长度','NUMBER',0,1,0,NULL,'8',1,'cme3pnoma0003r8cva4fu9log','2025-08-09 03:44:16.881','2025-08-09 03:44:16.881'),('cme3pnoqc0015r8cvvpt9ez2j','SECURITY','PASSWORD_REQUIRE_UPPERCASE','true','密码是否需要大写字母','BOOLEAN',0,1,0,NULL,'true',2,'cme3pnoma0003r8cva4fu9log','2025-08-09 03:44:16.884','2025-08-09 03:44:16.884'),('cme3pnoqh0017r8cv1h5rk4n3','SECURITY','MAX_LOGIN_ATTEMPTS','5','最大登录尝试次数','NUMBER',0,1,0,NULL,'5',3,'cme3pnoma0003r8cva4fu9log','2025-08-09 03:44:16.889','2025-08-09 03:44:16.889'),('cme3pnoqk0019r8cvm7g9300j','SECURITY','SESSION_TIMEOUT','7200','会话超时时间(秒)','NUMBER',0,1,0,NULL,'7200',4,'cme3pnoma0003r8cva4fu9log','2025-08-09 03:44:16.892','2025-08-09 03:44:16.892'),('cme3pnoqm001br8cvx2x4q9wa','EMAIL','EMAIL_ENABLED','true','是否启用邮件发送','BOOLEAN',0,1,0,NULL,'false',1,'cme3pnoma0003r8cva4fu9log','2025-08-09 03:44:16.895','2025-08-09 04:18:12.217'),('cme3pnoqp001dr8cvb1w4p068','EMAIL','SMTP_HOST','\"smtp.163.com\"','SMTP服务器地址','STRING',0,1,0,NULL,'\"\"',2,'cme3pnoma0003r8cva4fu9log','2025-08-09 03:44:16.897','2025-08-09 04:57:01.054'),('cme3pnoqs001fr8cv2niv8yhj','EMAIL','SMTP_PORT','587','SMTP端口','NUMBER',0,1,0,NULL,'587',3,'cme3pnoma0003r8cva4fu9log','2025-08-09 03:44:16.900','2025-08-09 03:44:16.900'),('cme3pnoqu001hr8cv1temwgq3','EMAIL','SMTP_SECURE','true','是否使用SSL','BOOLEAN',0,1,0,NULL,'false',4,'cme3pnoma0003r8cva4fu9log','2025-08-09 03:44:16.903','2025-08-09 04:57:13.373'),('cme3pnoqy001jr8cvtsxscvnb','EMAIL','SMTP_USER','\"<EMAIL>\"','SMTP用户名','STRING',0,1,0,NULL,'\"\"',5,'cme3pnoma0003r8cva4fu9log','2025-08-09 03:44:16.906','2025-08-09 04:57:36.418'),('cme3pnor0001lr8cv0jwuj89r','EMAIL','SMTP_PASS','\"3358143fe5cad1c3841a2768c6da89c4:a8d9c81d901a7d4646b7654d9203c7f4d03ff6545476139c53d44708efbad921\"','SMTP密码','PASSWORD',1,1,0,NULL,'\"\"',6,'cme3pnoma0003r8cva4fu9log','2025-08-09 03:44:16.909','2025-08-09 04:57:58.197'),('cme3pnor3001nr8cvmxtam9bk','EMAIL','SENDER_NAME','\"运维服务管理系统\"','发件人名称','STRING',0,1,0,NULL,'\"运维服务管理系统\"',7,'cme3pnoma0003r8cva4fu9log','2025-08-09 03:44:16.911','2025-08-09 03:44:16.911'),('cme3pnor5001pr8cv763vsr60','EMAIL','SENDER_EMAIL','\"<EMAIL>\"','发件人邮箱','EMAIL',0,1,0,NULL,'\"\"',8,'cme3pnoma0003r8cva4fu9log','2025-08-09 03:44:16.914','2025-08-09 04:58:09.399'),('cme3pnor8001rr8cv8mkg4o7d','SMS','SMS_ENABLED','false','是否启用短信发送','BOOLEAN',0,1,0,NULL,'false',1,'cme3pnoma0003r8cva4fu9log','2025-08-09 03:44:16.916','2025-08-09 03:44:16.916'),('cme3pnorb001tr8cvghafx4ud','SMS','ALI_SMS_ACCESS_KEY_ID','\"\"','阿里云AccessKey ID','STRING',0,1,0,NULL,'\"\"',2,'cme3pnoma0003r8cva4fu9log','2025-08-09 03:44:16.919','2025-08-09 03:44:16.919'),('cme3pnore001vr8cv9kxryred','SMS','ALI_SMS_ACCESS_KEY_SECRET','\"\"','阿里云AccessKey Secret','PASSWORD',1,1,0,NULL,'\"\"',3,'cme3pnoma0003r8cva4fu9log','2025-08-09 03:44:16.923','2025-08-09 03:44:16.923'),('cme3pnorh001xr8cvn3g322b6','SMS','ALI_SMS_REGION','\"cn-hangzhou\"','阿里云短信服务区域','STRING',0,1,0,NULL,'\"cn-hangzhou\"',4,'cme3pnoma0003r8cva4fu9log','2025-08-09 03:44:16.925','2025-08-09 03:44:16.925'),('cme3pnork001zr8cvqjgr9z7a','SMS','ALI_SMS_SIGN_NAME','\"\"','短信签名','STRING',0,1,0,NULL,'\"\"',5,'cme3pnoma0003r8cva4fu9log','2025-08-09 03:44:16.928','2025-08-09 03:44:16.928'),('cme3pnorn0021r8cvcl0orkq6','STORAGE','UPLOAD_MAX_SIZE','10485760','文件上传最大大小(字节)','NUMBER',0,1,1,NULL,'10485760',1,'cme3pnoma0003r8cva4fu9log','2025-08-09 03:44:16.931','2025-08-09 03:44:16.931'),('cme3pnorq0023r8cvn3qfnyic','STORAGE','UPLOAD_ALLOWED_TYPES','[\"image/*\", \"application/pdf\", \"text/*\", \"application/msword\", \"application/vnd.openxmlformats-officedocument.*\"]','允许上传的文件类型','JSON',0,1,1,NULL,'[\"image/*\", \"application/pdf\", \"text/*\", \"application/msword\", \"application/vnd.openxmlformats-officedocument.*\"]',2,'cme3pnoma0003r8cva4fu9log','2025-08-09 03:44:16.934','2025-08-09 03:44:16.934'),('cme3pnoru0025r8cvn00j2flj','STORAGE','STORAGE_PATH','\"./uploads\"','文件存储路径','STRING',0,1,0,NULL,'\"./uploads\"',3,'cme3pnoma0003r8cva4fu9log','2025-08-09 03:44:16.938','2025-08-09 03:44:16.938'),('cme3pnorw0027r8cvm38bjzl1','SYSTEM','SYSTEM_MAINTENANCE_MODE','false','系统维护模式','BOOLEAN',0,1,1,NULL,'false',1,'cme3pnoma0003r8cva4fu9log','2025-08-09 03:44:16.940','2025-08-09 03:44:16.940'),('cme3pnory0029r8cv0pr0t9hk','SYSTEM','MAINTENANCE_MESSAGE','\"系统正在维护中，请稍后再试\"','维护模式提示信息','TEXTAREA',0,1,1,NULL,'\"系统正在维护中，请稍后再试\"',2,'cme3pnoma0003r8cva4fu9log','2025-08-09 03:44:16.943','2025-08-09 03:44:16.943'),('cme3pnos1002br8cvkwfsa6ri','SYSTEM','DEBUG_MODE','false','调试模式','BOOLEAN',0,1,0,NULL,'false',3,'cme3pnoma0003r8cva4fu9log','2025-08-09 03:44:16.945','2025-08-09 03:44:16.945'),('cme3pnos3002dr8cv94ptf16a','SYSTEM','LOG_LEVEL','\"info\"','日志级别','SELECT',0,1,0,'{\"options\": [{\"label\": \"Error\", \"value\": \"error\"}, {\"label\": \"Warn\", \"value\": \"warn\"}, {\"label\": \"Info\", \"value\": \"info\"}, {\"label\": \"Debug\", \"value\": \"debug\"}]}','\"info\"',4,'cme3pnoma0003r8cva4fu9log','2025-08-09 03:44:16.947','2025-08-09 03:44:16.947'),('cme3pnos6002fr8cvv7203d7n','SYSTEM','CACHE_TTL','300','缓存过期时间(秒)','NUMBER',0,1,0,NULL,'300',5,'cme3pnoma0003r8cva4fu9log','2025-08-09 03:44:16.950','2025-08-09 03:44:16.950');
/*!40000 ALTER TABLE `system_configs` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `system_events`
--

DROP TABLE IF EXISTS `system_events`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `system_events` (
  `id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `level` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `type` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `message` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `details` json DEFAULT NULL,
  `source` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `resolved` tinyint(1) NOT NULL DEFAULT '0',
  `resolved_by` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `resolved_at` datetime(3) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `system_events_level_type_idx` (`level`,`type`),
  KEY `system_events_created_at_idx` (`created_at`),
  KEY `system_events_resolved_by_fkey` (`resolved_by`),
  CONSTRAINT `system_events_resolved_by_fkey` FOREIGN KEY (`resolved_by`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `system_events`
--

LOCK TABLES `system_events` WRITE;
/*!40000 ALTER TABLE `system_events` DISABLE KEYS */;
INSERT INTO `system_events` VALUES ('cme3pnosg002gr8cvfjo9nxqw','info','system','系统启动完成，服务已就绪','{\"source\": \"bootstrap\"}',NULL,0,NULL,'2025-08-09 03:44:16.959',NULL),('cme3pnosg002hr8cv19a7mlfk','warn','performance','CPU 使用率短时上升，已接近预警阈值','{\"cpu\": 78}',NULL,0,NULL,'2025-08-09 03:44:16.959',NULL),('cme3pnosh002ir8cv8tl7k9mt','error','service','外部接口请求失败，重试已触发','{\"retry\": true, \"service\": \"third-party-api\"}',NULL,0,NULL,'2025-08-09 03:44:16.959',NULL);
/*!40000 ALTER TABLE `system_events` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `system_metrics`
--

DROP TABLE IF EXISTS `system_metrics`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `system_metrics` (
  `id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `timestamp` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `metric_type` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `value` double NOT NULL,
  `unit` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `tags` json DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `system_metrics_metric_type_timestamp_idx` (`metric_type`,`timestamp`),
  KEY `system_metrics_timestamp_idx` (`timestamp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `system_metrics`
--

LOCK TABLES `system_metrics` WRITE;
/*!40000 ALTER TABLE `system_metrics` DISABLE KEYS */;
/*!40000 ALTER TABLE `system_metrics` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `task_executions`
--

DROP TABLE IF EXISTS `task_executions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `task_executions` (
  `id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `task_name` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `status` enum('RUNNING','SUCCESS','FAILED') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'RUNNING',
  `started_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `ended_at` datetime(3) DEFAULT NULL,
  `duration` int DEFAULT NULL,
  `output` text COLLATE utf8mb4_unicode_ci,
  `error` text COLLATE utf8mb4_unicode_ci,
  `created_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `updated_at` datetime(3) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `task_executions_task_name_idx` (`task_name`),
  KEY `task_executions_status_idx` (`status`),
  KEY `task_executions_created_at_idx` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `task_executions`
--

LOCK TABLES `task_executions` WRITE;
/*!40000 ALTER TABLE `task_executions` DISABLE KEYS */;
INSERT INTO `task_executions` VALUES ('cme579mhm0000gubkrr1w361v','system-health','SUCCESS','2025-08-10 04:45:00.052','2025-08-10 04:45:00.135',84,NULL,NULL,'2025-08-10 04:45:00.056','2025-08-10 04:45:00.137'),('cme57swxs0001gubk96d8nt2r','sla-alert','SUCCESS','2025-08-10 05:00:00.061','2025-08-10 05:00:00.102',42,NULL,NULL,'2025-08-10 05:00:00.061','2025-08-10 05:00:00.103'),('cme57swxu0002gubkair6uedz','system-health','SUCCESS','2025-08-10 05:00:00.056','2025-08-10 05:00:00.097',40,NULL,NULL,'2025-08-10 05:00:00.060','2025-08-10 05:00:00.097'),('cme58c7df0000gb9r3do4au8g','system-health','SUCCESS','2025-08-10 05:15:00.047','2025-08-10 05:15:00.173',129,NULL,NULL,'2025-08-10 05:15:00.050','2025-08-10 05:15:00.176'),('cme59y2qq000014ovqmluyi62','sla-alert','SUCCESS','2025-08-10 06:00:00.068','2025-08-10 06:00:00.398',331,NULL,NULL,'2025-08-10 06:00:00.073','2025-08-10 06:00:00.399'),('cme59y2xk000114oveo47ja7z','system-health','SUCCESS','2025-08-10 06:00:00.072','2025-08-10 06:00:00.396',324,NULL,NULL,'2025-08-10 06:00:00.073','2025-08-10 06:00:00.398'),('cme5ahd4i0000udyx6b0qs8av','system-health','SUCCESS','2025-08-10 06:15:00.014','2025-08-10 06:15:00.058',44,NULL,NULL,'2025-08-10 06:15:00.018','2025-08-10 06:15:00.060'),('cme5d5tdf000cdy4e5oec1kxj','sla-alert','SUCCESS','2025-08-10 07:30:00.050','2025-08-10 07:30:00.074',24,NULL,NULL,'2025-08-10 07:30:00.051','2025-08-10 07:30:00.075'),('cme5d5tdg000ddy4er0qipqpo','system-health','SUCCESS','2025-08-10 07:30:00.051','2025-08-10 07:30:00.073',22,NULL,NULL,'2025-08-10 07:30:00.052','2025-08-10 07:30:00.073'),('cme5dp3su000edy4e5i1ursxo','system-health','SUCCESS','2025-08-10 07:45:00.028','2025-08-10 07:45:00.081',53,NULL,NULL,'2025-08-10 07:45:00.029','2025-08-10 07:45:00.082'),('cme5e8e8y0000lkw9wvefvina','sla-alert','SUCCESS','2025-08-10 08:00:00.033','2025-08-10 08:00:00.123',91,NULL,NULL,'2025-08-10 08:00:00.034','2025-08-10 08:00:00.124'),('cme5e8e900001lkw9ezn74ns0','system-health','SUCCESS','2025-08-10 08:00:00.034','2025-08-10 08:00:00.126',91,NULL,NULL,'2025-08-10 08:00:00.034','2025-08-10 08:00:00.127'),('cme5e8e920002lkw9wagmoryr','overdue-tickets','FAILED','2025-08-10 08:00:00.034','2025-08-10 08:00:00.119',79,NULL,'PrismaClientValidationError: \n[31mInvalid [1m`prisma.service.findMany()`[22m invocation in[39m\n[4m/Users/<USER>/source/duoxieyun/ops-management-system/backend/src/services/scheduler.service.ts:473:50[24m\n\n  [2m[90m470[39m [36mprivate[39m [36mstatic[39m [36masync[39m [36mnotifyOverdueTickets[39m[34m([39m[34m)[39m [34m{[39m[22m\n  [2m[90m471[39m   [36mconst[39m yesterday = [36mnew[39m Date[34m([39mDate[34m.[39m[36mnow[39m[34m([39m[34m)[39m - [36m24[39m * [36m60[39m * [36m60[39m * [36m1000[39m[34m)[39m[22m\n  [2m[90m472[39m   [22m\n[1m[31m→[39m[22m [2m[90m473[39m   [36mconst[39m overdueServices = [36mawait[39m prisma[34m.[39mservice[34m.[39m[36mfindMany[39m[34m([39m[22m{\n          where: {\n            status: {\n              in: [\n                \"OPEN\",\n                \"IN_PROGRESS\",\n                \"PENDING\"\n              ]\n            },\n            createdAt: {\n              lt: new Date(\"2025-08-09T08:00:00.078Z\")\n            }\n          },\n          include: {\n            customer: true,\n            [31massignedTo[39m: true,\n            [31m~~~~~~~~~~[39m\n        [32m?[39m   [32marchive[39m[32m?[39m[32m: [39m[32mtrue[39m,\n        [32m?[39m   [32mslaTemplate[39m[32m?[39m[32m: [39m[32mtrue[39m,\n        [32m?[39m   [32massignedUser[39m[32m?[39m[32m: [39m[32mtrue[39m,\n        [32m?[39m   [32mcreatedByUser[39m[32m?[39m[32m: [39m[32mtrue[39m,\n        [32m?[39m   [32mworkLogs[39m[32m?[39m[32m: [39m[32mtrue[39m,\n        [32m?[39m   [32mattachments[39m[32m?[39m[32m: [39m[32mtrue[39m,\n        [32m?[39m   [32mcomments[39m[32m?[39m[32m: [39m[32mtrue[39m,\n        [32m?[39m   [32moperationHistory[39m[32m?[39m[32m: [39m[32mtrue[39m\n          }\n        }[2m)[22m\n\nInvalid scalar field [31m`assignedTo`[39m for [1minclude[22m statement on model [1mService[22m. Available options are listed in [32mgreen[39m.\nNote that [1minclude[22m statements only accept relation fields.\n    at wn (/Users/<USER>/source/duoxieyun/ops-management-system/backend/node_modules/@prisma/client/runtime/library.js:29:1363)\n    at e.throwValidationError (/Users/<USER>/source/duoxieyun/ops-management-system/backend/node_modules/@prisma/client/runtime/library.js:29:10206)\n    at rd (/Users/<USER>/source/duoxieyun/ops-management-system/backend/node_modules/@prisma/client/runtime/library.js:29:6772)\n    at td (/Users/<USER>/source/duoxieyun/ops-management-system/backend/node_modules/@prisma/client/runtime/library.js:29:6525)\n    at ed (/Users/<USER>/source/duoxieyun/ops-management-system/backend/node_modules/@prisma/client/runtime/library.js:29:6411)\n    at mr (/Users/<USER>/source/duoxieyun/ops-management-system/backend/node_modules/@prisma/client/runtime/library.js:29:6055)\n    at vn (/Users/<USER>/source/duoxieyun/ops-management-system/backend/node_modules/@prisma/client/runtime/library.js:29:5902)\n    at /Users/<USER>/source/duoxieyun/ops-management-system/backend/node_modules/@prisma/client/runtime/library.js:130:10186\n    at Object.runInChildSpan (/Users/<USER>/source/duoxieyun/ops-management-system/backend/node_modules/@prisma/client/runtime/library.js:121:1509)\n    at t._executeRequest (/Users/<USER>/source/duoxieyun/ops-management-system/backend/node_modules/@prisma/client/runtime/library.js:130:10165)\n    at Na (/Users/<USER>/source/duoxieyun/ops-management-system/backend/node_modules/@prisma/client/runtime/library.js:31:9955)\n    at l (/Users/<USER>/source/duoxieyun/ops-management-system/backend/node_modules/@prisma/client/runtime/library.js:130:9639)\n    at /Users/<USER>/source/duoxieyun/ops-management-system/backend/node_modules/@prisma/client/runtime/library.js:130:9933\n    at AsyncResource.runInAsyncScope (node:async_hooks:206:9)\n    at /Users/<USER>/source/duoxieyun/ops-management-system/backend/node_modules/@prisma/client/runtime/library.js:130:9913\n    at Object.runInChildSpan (/Users/<USER>/source/duoxieyun/ops-management-system/backend/node_modules/@prisma/client/runtime/library.js:121:1509)','2025-08-10 08:00:00.035','2025-08-10 08:00:00.121'),('cme5eroq80003lkw948gphrxi','system-health','SUCCESS','2025-08-10 08:15:00.078','2025-08-10 08:15:00.115',37,NULL,NULL,'2025-08-10 08:15:00.079','2025-08-10 08:15:00.116'),('cme5faz4w0004lkw9g08912zr','system-health','SUCCESS','2025-08-10 08:30:00.027','2025-08-10 08:30:00.071',45,NULL,NULL,'2025-08-10 08:30:00.032','2025-08-10 08:30:00.072'),('cme5faz4y0005lkw9eks8dw8q','sla-alert','SUCCESS','2025-08-10 08:30:00.032','2025-08-10 08:30:00.077',45,NULL,NULL,'2025-08-10 08:30:00.033','2025-08-10 08:30:00.078'),('cme5gdk1i00001202zsgukbc0','sla-alert','SUCCESS','2025-08-10 09:00:00.051','2025-08-10 09:00:00.107',57,NULL,NULL,'2025-08-10 09:00:00.052','2025-08-10 09:00:00.108'),('cme5gdk1i000112020t259ruz','system-health','SUCCESS','2025-08-10 09:00:00.053','2025-08-10 09:00:00.101',48,NULL,NULL,'2025-08-10 09:00:00.054','2025-08-10 09:00:00.102'),('cme5gwui2000212026lg4b0k8','system-health','SUCCESS','2025-08-10 09:15:00.071','2025-08-10 09:15:00.161',90,NULL,NULL,'2025-08-10 09:15:00.073','2025-08-10 09:15:00.162'),('cme5hg4xo0000vq2qvpgdmzrk','sla-alert','SUCCESS','2025-08-10 09:30:00.058','2025-08-10 09:30:00.116',58,NULL,NULL,'2025-08-10 09:30:00.059','2025-08-10 09:30:00.117'),('cme5hg4y50001vq2qdga5sc0y','system-health','SUCCESS','2025-08-10 09:30:00.056','2025-08-10 09:30:00.114',58,NULL,NULL,'2025-08-10 09:30:00.059','2025-08-10 09:30:00.115'),('cme5hzfd60002vq2qho2ef0hc','system-health','SUCCESS','2025-08-10 09:45:00.040','2025-08-10 09:45:00.128',88,NULL,NULL,'2025-08-10 09:45:00.042','2025-08-10 09:45:00.129'),('cme5iipsi0003vq2qy8poqep3','sla-alert','SUCCESS','2025-08-10 10:00:00.017','2025-08-10 10:00:00.086',70,NULL,NULL,'2025-08-10 10:00:00.018','2025-08-10 10:00:00.087'),('cme5iiptv0004vq2qtoa5n91a','system-health','SUCCESS','2025-08-10 10:00:00.018','2025-08-10 10:00:00.080',62,NULL,NULL,'2025-08-10 10:00:00.019','2025-08-10 10:00:00.081'),('cme5j209p0004wpos9an4xbzy','system-health','SUCCESS','2025-08-10 10:15:00.048','2025-08-10 10:15:00.132',85,NULL,NULL,'2025-08-10 10:15:00.050','2025-08-10 10:15:00.133'),('cme5jlaoy0000hpp9l78x1qha','system-health','SUCCESS','2025-08-10 10:30:00.032','2025-08-10 10:30:00.091',59,NULL,NULL,'2025-08-10 10:30:00.033','2025-08-10 10:30:00.092'),('cme5jlape0001hpp9r8l3q09s','sla-alert','SUCCESS','2025-08-10 10:30:00.033','2025-08-10 10:30:00.094',61,NULL,NULL,'2025-08-10 10:30:00.034','2025-08-10 10:30:00.095'),('cme5l760x000ehpp9ok7fi399','system-health','SUCCESS','2025-08-10 11:15:00.032','2025-08-10 11:15:00.079',47,NULL,NULL,'2025-08-10 11:15:00.033','2025-08-10 11:15:00.080'),('cme5lqgh1000rhpp9kuf0grjh','system-health','SUCCESS','2025-08-10 11:30:00.036','2025-08-10 11:30:00.078',43,NULL,NULL,'2025-08-10 11:30:00.037','2025-08-10 11:30:00.078'),('cme5lqgh3000shpp9e67weoq8','sla-alert','SUCCESS','2025-08-10 11:30:00.037','2025-08-10 11:30:00.088',51,NULL,NULL,'2025-08-10 11:30:00.038','2025-08-10 11:30:00.089'),('cme5m9qx40004yigvaavq3lnz','system-health','SUCCESS','2025-08-10 11:45:00.039','2025-08-10 11:45:00.071',32,NULL,NULL,'2025-08-10 11:45:00.040','2025-08-10 11:45:00.072'),('cme5mt1e0000aar4qs9qowcd0','overdue-tickets','FAILED','2025-08-10 12:00:00.070','2025-08-10 12:00:00.140',65,NULL,'PrismaClientValidationError: \n[31mInvalid [1m`prisma.service.findMany()`[22m invocation in[39m\n[4m/Users/<USER>/source/duoxieyun/ops-management-system/backend/src/services/scheduler.service.ts:473:50[24m\n\n  [2m[90m470[39m [36mprivate[39m [36mstatic[39m [36masync[39m [36mnotifyOverdueTickets[39m[34m([39m[34m)[39m [34m{[39m[22m\n  [2m[90m471[39m   [36mconst[39m yesterday = [36mnew[39m Date[34m([39mDate[34m.[39m[36mnow[39m[34m([39m[34m)[39m - [36m24[39m * [36m60[39m * [36m60[39m * [36m1000[39m[34m)[39m[22m\n  [2m[90m472[39m   [22m\n[1m[31m→[39m[22m [2m[90m473[39m   [36mconst[39m overdueServices = [36mawait[39m prisma[34m.[39mservice[34m.[39m[36mfindMany[39m[34m([39m[22m{\n          where: {\n            status: {\n              in: [\n                \"OPEN\",\n                \"IN_PROGRESS\",\n                \"PENDING\"\n              ]\n            },\n            createdAt: {\n              lt: new Date(\"2025-08-09T12:00:00.105Z\")\n            }\n          },\n          include: {\n            customer: true,\n            [31massignedTo[39m: true,\n            [31m~~~~~~~~~~[39m\n        [32m?[39m   [32marchive[39m[32m?[39m[32m: [39m[32mtrue[39m,\n        [32m?[39m   [32mslaTemplate[39m[32m?[39m[32m: [39m[32mtrue[39m,\n        [32m?[39m   [32massignedUser[39m[32m?[39m[32m: [39m[32mtrue[39m,\n        [32m?[39m   [32mcreatedByUser[39m[32m?[39m[32m: [39m[32mtrue[39m,\n        [32m?[39m   [32mworkLogs[39m[32m?[39m[32m: [39m[32mtrue[39m,\n        [32m?[39m   [32mattachments[39m[32m?[39m[32m: [39m[32mtrue[39m,\n        [32m?[39m   [32mcomments[39m[32m?[39m[32m: [39m[32mtrue[39m,\n        [32m?[39m   [32moperationHistory[39m[32m?[39m[32m: [39m[32mtrue[39m\n          }\n        }[2m)[22m\n\nInvalid scalar field [31m`assignedTo`[39m for [1minclude[22m statement on model [1mService[22m. Available options are listed in [32mgreen[39m.\nNote that [1minclude[22m statements only accept relation fields.\n    at wn (/Users/<USER>/source/duoxieyun/ops-management-system/backend/node_modules/@prisma/client/runtime/library.js:29:1363)\n    at e.throwValidationError (/Users/<USER>/source/duoxieyun/ops-management-system/backend/node_modules/@prisma/client/runtime/library.js:29:10206)\n    at rd (/Users/<USER>/source/duoxieyun/ops-management-system/backend/node_modules/@prisma/client/runtime/library.js:29:6772)\n    at td (/Users/<USER>/source/duoxieyun/ops-management-system/backend/node_modules/@prisma/client/runtime/library.js:29:6525)\n    at ed (/Users/<USER>/source/duoxieyun/ops-management-system/backend/node_modules/@prisma/client/runtime/library.js:29:6411)\n    at mr (/Users/<USER>/source/duoxieyun/ops-management-system/backend/node_modules/@prisma/client/runtime/library.js:29:6055)\n    at vn (/Users/<USER>/source/duoxieyun/ops-management-system/backend/node_modules/@prisma/client/runtime/library.js:29:5902)\n    at /Users/<USER>/source/duoxieyun/ops-management-system/backend/node_modules/@prisma/client/runtime/library.js:130:10186\n    at Object.runInChildSpan (/Users/<USER>/source/duoxieyun/ops-management-system/backend/node_modules/@prisma/client/runtime/library.js:121:1509)\n    at t._executeRequest (/Users/<USER>/source/duoxieyun/ops-management-system/backend/node_modules/@prisma/client/runtime/library.js:130:10165)\n    at Na (/Users/<USER>/source/duoxieyun/ops-management-system/backend/node_modules/@prisma/client/runtime/library.js:31:9955)\n    at l (/Users/<USER>/source/duoxieyun/ops-management-system/backend/node_modules/@prisma/client/runtime/library.js:130:9639)\n    at /Users/<USER>/source/duoxieyun/ops-management-system/backend/node_modules/@prisma/client/runtime/library.js:130:9933\n    at AsyncResource.runInAsyncScope (node:async_hooks:206:9)\n    at /Users/<USER>/source/duoxieyun/ops-management-system/backend/node_modules/@prisma/client/runtime/library.js:130:9913\n    at Object.runInChildSpan (/Users/<USER>/source/duoxieyun/ops-management-system/backend/node_modules/@prisma/client/runtime/library.js:121:1509)','2025-08-10 12:00:00.070','2025-08-10 12:00:00.141'),('cme5mt1e0000bar4qirmrcheg','sla-alert','SUCCESS','2025-08-10 12:00:00.069','2025-08-10 12:00:00.167',99,NULL,NULL,'2025-08-10 12:00:00.070','2025-08-10 12:00:00.168'),('cme5mt1e0000car4qwr49y2vg','system-health','SUCCESS','2025-08-10 12:00:00.070','2025-08-10 12:00:00.140',70,NULL,NULL,'2025-08-10 12:00:00.072','2025-08-10 12:00:00.141'),('cme6g9zsl0000rm1y9e9vu7pv','system-health','SUCCESS','2025-08-11 01:45:00.016','2025-08-11 01:45:00.086',70,NULL,NULL,'2025-08-11 01:45:00.019','2025-08-11 01:45:00.088'),('cme6m66q90008ssavrvr9qmtp','system-health','SUCCESS','2025-08-11 04:30:00.079','2025-08-11 04:30:00.172',93,NULL,NULL,'2025-08-11 04:30:00.080','2025-08-11 04:30:00.173'),('cme6m66q90009ssavss19s9dr','sla-alert','SUCCESS','2025-08-11 04:30:00.078','2025-08-11 04:30:00.196',118,NULL,NULL,'2025-08-11 04:30:00.080','2025-08-11 04:30:00.196'),('cme6mph6c000bssavq97wikze','system-health','SUCCESS','2025-08-11 04:45:00.079','2025-08-11 04:45:00.154',75,NULL,NULL,'2025-08-11 04:45:00.081','2025-08-11 04:45:00.155'),('cme6ns20q00004c7gto8v294i','system-health','SUCCESS','2025-08-11 05:15:00.023','2025-08-11 05:15:00.103',80,NULL,NULL,'2025-08-11 05:15:00.026','2025-08-11 05:15:00.105'),('cme6obchm0008j9egihokv9z1','sla-alert','SUCCESS','2025-08-11 05:30:00.055','2025-08-11 05:30:00.147',93,NULL,NULL,'2025-08-11 05:30:00.056','2025-08-11 05:30:00.148'),('cme6obchn0009j9eg64fxkeaw','system-health','SUCCESS','2025-08-11 05:30:00.057','2025-08-11 05:30:00.115',58,NULL,NULL,'2025-08-11 05:30:00.058','2025-08-11 05:30:00.116'),('cme6oumz4000bj9egb1optgg7','system-health','SUCCESS','2025-08-11 05:45:00.108','2025-08-11 05:45:00.140',32,NULL,NULL,'2025-08-11 05:45:00.110','2025-08-11 05:45:00.141'),('cme6pdxei000cj9eguj0pmfbh','sla-alert','SUCCESS','2025-08-11 06:00:00.087','2025-08-11 06:00:00.212',125,NULL,NULL,'2025-08-11 06:00:00.088','2025-08-11 06:00:00.213'),('cme6pdxej000dj9egkdcybccl','system-health','SUCCESS','2025-08-11 06:00:00.085','2025-08-11 06:00:00.150',64,NULL,NULL,'2025-08-11 06:00:00.087','2025-08-11 06:00:00.150'),('cme6px7tm000fj9egmwqxkj18','system-health','SUCCESS','2025-08-11 06:15:00.055','2025-08-11 06:15:00.110',55,NULL,NULL,'2025-08-11 06:15:00.057','2025-08-11 06:15:00.112'),('cme6qgiah000gj9egw0z1cx3b','system-health','SUCCESS','2025-08-11 06:30:00.084','2025-08-11 06:30:00.153',69,NULL,NULL,'2025-08-11 06:30:00.086','2025-08-11 06:30:00.154'),('cme6qgiai000hj9egaibcl81e','sla-alert','SUCCESS','2025-08-11 06:30:00.087','2025-08-11 06:30:00.179',92,NULL,NULL,'2025-08-11 06:30:00.088','2025-08-11 06:30:00.180'),('cme6qzsqh000jj9eg2gfn4nnp','system-health','SUCCESS','2025-08-11 06:45:00.085','2025-08-11 06:45:00.129',45,NULL,NULL,'2025-08-11 06:45:00.087','2025-08-11 06:45:00.130'),('cme6rj36x000kj9eg11hhhd1v','system-health','SUCCESS','2025-08-11 07:00:00.095','2025-08-11 07:00:00.149',55,NULL,NULL,'2025-08-11 07:00:00.101','2025-08-11 07:00:00.150'),('cme6rj36y000lj9egry1026io','sla-alert','SUCCESS','2025-08-11 07:00:00.099','2025-08-11 07:00:00.192',93,NULL,NULL,'2025-08-11 07:00:00.101','2025-08-11 07:00:00.193'),('cme6s2dm9000nj9eg1ljmrl70','system-health','SUCCESS','2025-08-11 07:15:00.076','2025-08-11 07:15:00.166',90,NULL,NULL,'2025-08-11 07:15:00.078','2025-08-11 07:15:00.167'),('cme6slo2n000wj9eggpdz4cwq','sla-alert','SUCCESS','2025-08-11 07:30:00.092','2025-08-11 07:30:00.180',88,NULL,NULL,'2025-08-11 07:30:00.093','2025-08-11 07:30:00.181'),('cme6slo2o000xj9eghvqkhqaw','system-health','SUCCESS','2025-08-11 07:30:00.090','2025-08-11 07:30:00.149',59,NULL,NULL,'2025-08-11 07:30:00.093','2025-08-11 07:30:00.150'),('cme6t4yiv000zj9eg5t4cvzuf','system-health','SUCCESS','2025-08-11 07:45:00.092','2025-08-11 07:45:00.185',94,NULL,NULL,'2025-08-11 07:45:00.100','2025-08-11 07:45:00.186'),('cme6to8yy0010j9eg6uca7sm6','system-health','SUCCESS','2025-08-11 08:00:00.089','2025-08-11 08:00:00.135',46,NULL,NULL,'2025-08-11 08:00:00.093','2025-08-11 08:00:00.136'),('cme6to8zm0011j9egczm76ok6','sla-alert','SUCCESS','2025-08-11 08:00:00.092','2025-08-11 08:00:00.167',75,NULL,NULL,'2025-08-11 08:00:00.093','2025-08-11 08:00:00.168'),('cme6to91u0013j9eg267670ef','overdue-tickets','SUCCESS','2025-08-11 08:00:00.209','2025-08-11 08:00:00.226',17,NULL,NULL,'2025-08-11 08:00:00.210','2025-08-11 08:00:00.227'),('cme6u7jer0014j9eg8blb6gxb','system-health','SUCCESS','2025-08-11 08:15:00.093','2025-08-11 08:15:00.178',85,NULL,NULL,'2025-08-11 08:15:00.095','2025-08-11 08:15:00.179'),('cme6uqtui0015j9egenc15jcb','system-health','SUCCESS','2025-08-11 08:30:00.086','2025-08-11 08:30:00.173',88,NULL,NULL,'2025-08-11 08:30:00.089','2025-08-11 08:30:00.174'),('cme6uqtv90016j9eg78zwvwu5','sla-alert','SUCCESS','2025-08-11 08:30:00.088','2025-08-11 08:30:00.207',119,NULL,NULL,'2025-08-11 08:30:00.089','2025-08-11 08:30:00.208'),('cme6va4af0018j9egn77ymuqk','system-health','SUCCESS','2025-08-11 08:45:00.077','2025-08-11 08:45:00.171',94,NULL,NULL,'2025-08-11 08:45:00.080','2025-08-11 08:45:00.172'),('cme6vteq80019j9eg7y9513ha','sla-alert','SUCCESS','2025-08-11 09:00:00.069','2025-08-11 09:00:00.148',80,NULL,NULL,'2025-08-11 09:00:00.073','2025-08-11 09:00:00.149'),('cme6vteqi001aj9egmvywq4re','system-health','SUCCESS','2025-08-11 09:00:00.073','2025-08-11 09:00:00.118',45,NULL,NULL,'2025-08-11 09:00:00.074','2025-08-11 09:00:00.119'),('cme6wcp62001cj9eghtug6udu','system-health','SUCCESS','2025-08-11 09:15:00.066','2025-08-11 09:15:00.142',76,NULL,NULL,'2025-08-11 09:15:00.069','2025-08-11 09:15:00.143'),('cme6zkftt00001r3mgpsxixj7','system-health','SUCCESS','2025-08-11 10:45:00.064','2025-08-11 10:45:00.144',81,NULL,NULL,'2025-08-11 10:45:00.065','2025-08-11 10:45:00.145'),('cme703q9b00086clmi0alybl4','sla-alert','SUCCESS','2025-08-11 11:00:00.046','2025-08-11 11:00:00.156',109,NULL,NULL,'2025-08-11 11:00:00.047','2025-08-11 11:00:00.157'),('cme703q9c00096clmu5bjfh8a','system-health','SUCCESS','2025-08-11 11:00:00.047','2025-08-11 11:00:00.072',25,NULL,NULL,'2025-08-11 11:00:00.048','2025-08-11 11:00:00.072'),('cme728w2k000b6clmuj1jpj87','system-health','SUCCESS','2025-08-11 12:00:00.088','2025-08-11 12:00:00.191',104,NULL,NULL,'2025-08-11 12:00:00.092','2025-08-11 12:00:00.192'),('cme728w31000c6clm57lh4pyq','sla-alert','SUCCESS','2025-08-11 12:00:00.091','2025-08-11 12:00:00.233',142,NULL,NULL,'2025-08-11 12:00:00.092','2025-08-11 12:00:00.234'),('cme728w4g000d6clmcb3gbdje','overdue-tickets','SUCCESS','2025-08-11 12:00:00.158','2025-08-11 12:00:00.199',41,NULL,NULL,'2025-08-11 12:00:00.159','2025-08-11 12:00:00.200'),('cme72s6gr000f6clmxgl997oe','system-health','SUCCESS','2025-08-11 12:15:00.023','2025-08-11 12:15:00.056',33,NULL,NULL,'2025-08-11 12:15:00.026','2025-08-11 12:15:00.057'),('cme73bgwy000g6clm92v1z99i','sla-alert','SUCCESS','2025-08-11 12:30:00.029','2025-08-11 12:30:00.099',71,NULL,NULL,'2025-08-11 12:30:00.032','2025-08-11 12:30:00.100'),('cme73bgxa000h6clmplq6amql','system-health','SUCCESS','2025-08-11 12:30:00.043','2025-08-11 12:30:00.071',28,NULL,NULL,'2025-08-11 12:30:00.044','2025-08-11 12:30:00.072'),('cme73ure80008f0vogv6iii9l','system-health','SUCCESS','2025-08-11 12:45:00.075','2025-08-11 12:45:00.133',59,NULL,NULL,'2025-08-11 12:45:00.077','2025-08-11 12:45:00.134'),('cme74e1up0009f0vozka24dh7','system-health','SUCCESS','2025-08-11 13:00:00.090','2025-08-11 13:00:00.153',63,NULL,NULL,'2025-08-11 13:00:00.093','2025-08-11 13:00:00.154'),('cme74e1uq000af0vot112e2qk','sla-alert','SUCCESS','2025-08-11 13:00:00.096','2025-08-11 13:00:00.180',84,NULL,NULL,'2025-08-11 13:00:00.097','2025-08-11 13:00:00.180'),('cme74xcaj000cf0vo56hoftdx','system-health','SUCCESS','2025-08-11 13:15:00.081','2025-08-11 13:15:00.134',53,NULL,NULL,'2025-08-11 13:15:00.085','2025-08-11 13:15:00.135'),('cme75gmox000df0vo7wem916d','sla-alert','SUCCESS','2025-08-11 13:30:00.029','2025-08-11 13:30:00.101',72,NULL,NULL,'2025-08-11 13:30:00.031','2025-08-11 13:30:00.102'),('cme75gmoy000ef0voksvczq8k','system-health','SUCCESS','2025-08-11 13:30:00.033','2025-08-11 13:30:00.076',43,NULL,NULL,'2025-08-11 13:30:00.033','2025-08-11 13:30:00.076');
/*!40000 ALTER TABLE `task_executions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `user_activity_anomalies`
--

DROP TABLE IF EXISTS `user_activity_anomalies`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_activity_anomalies` (
  `id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `user_id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `anomaly_type` enum('UNUSUAL_TIME','EXCESSIVE_OPERATIONS','ABNORMAL_LOCATION','SUSPICIOUS_BEHAVIOR') COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `severity` enum('LOW','MEDIUM','HIGH','CRITICAL') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'MEDIUM',
  `detected_value` json NOT NULL,
  `normal_range` json DEFAULT NULL,
  `confidence_score` double NOT NULL DEFAULT '0',
  `is_resolved` tinyint(1) NOT NULL DEFAULT '0',
  `resolved_at` datetime(3) DEFAULT NULL,
  `resolved_by` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `notes` text COLLATE utf8mb4_unicode_ci,
  `detected_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  PRIMARY KEY (`id`),
  KEY `user_activity_anomalies_user_id_idx` (`user_id`),
  KEY `user_activity_anomalies_anomaly_type_idx` (`anomaly_type`),
  KEY `user_activity_anomalies_severity_idx` (`severity`),
  KEY `user_activity_anomalies_is_resolved_idx` (`is_resolved`),
  KEY `user_activity_anomalies_resolved_by_fkey` (`resolved_by`),
  CONSTRAINT `user_activity_anomalies_resolved_by_fkey` FOREIGN KEY (`resolved_by`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
  CONSTRAINT `user_activity_anomalies_user_id_fkey` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `user_activity_anomalies`
--

LOCK TABLES `user_activity_anomalies` WRITE;
/*!40000 ALTER TABLE `user_activity_anomalies` DISABLE KEYS */;
/*!40000 ALTER TABLE `user_activity_anomalies` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `user_activity_stats`
--

DROP TABLE IF EXISTS `user_activity_stats`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_activity_stats` (
  `id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `user_id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `date` date NOT NULL,
  `login_count` int NOT NULL DEFAULT '0',
  `session_count` int NOT NULL DEFAULT '0',
  `total_duration_minutes` int NOT NULL DEFAULT '0',
  `operation_count` int NOT NULL DEFAULT '0',
  `page_views` int NOT NULL DEFAULT '0',
  `activity_score` double NOT NULL DEFAULT '0',
  `peak_activity_hour` int DEFAULT NULL,
  `features_used` json DEFAULT NULL,
  `last_activity` datetime(3) DEFAULT NULL,
  `created_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `updated_at` datetime(3) NOT NULL ON UPDATE CURRENT_TIMESTAMP(3),
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_activity_stats_user_id_date_key` (`user_id`,`date`),
  KEY `user_activity_stats_date_idx` (`date`),
  KEY `user_activity_stats_activity_score_idx` (`activity_score`),
  CONSTRAINT `user_activity_stats_user_id_fkey` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `user_activity_stats`
--

LOCK TABLES `user_activity_stats` WRITE;
/*!40000 ALTER TABLE `user_activity_stats` DISABLE KEYS */;
/*!40000 ALTER TABLE `user_activity_stats` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `user_behavior_patterns`
--

DROP TABLE IF EXISTS `user_behavior_patterns`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_behavior_patterns` (
  `id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `user_id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `pattern_type` enum('LOGIN_TIME','ACTIVITY_DURATION','FEATURE_USAGE','OPERATION_FREQUENCY') COLLATE utf8mb4_unicode_ci NOT NULL,
  `pattern_data` json NOT NULL,
  `confidence_score` double NOT NULL DEFAULT '0',
  `detected_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `last_updated` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
  `status` enum('ACTIVE','INACTIVE','ANOMALY') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'ACTIVE',
  `metadata` json DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_behavior_patterns_user_id_pattern_type_key` (`user_id`,`pattern_type`),
  KEY `user_behavior_patterns_pattern_type_idx` (`pattern_type`),
  KEY `user_behavior_patterns_status_idx` (`status`),
  CONSTRAINT `user_behavior_patterns_user_id_fkey` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `user_behavior_patterns`
--

LOCK TABLES `user_behavior_patterns` WRITE;
/*!40000 ALTER TABLE `user_behavior_patterns` DISABLE KEYS */;
/*!40000 ALTER TABLE `user_behavior_patterns` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `user_feature_usage`
--

DROP TABLE IF EXISTS `user_feature_usage`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_feature_usage` (
  `id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `user_id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `feature_name` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `usage_count` int NOT NULL DEFAULT '0',
  `last_used` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `date` date NOT NULL,
  `avg_time_spent` double DEFAULT '0',
  `created_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `updated_at` datetime(3) NOT NULL ON UPDATE CURRENT_TIMESTAMP(3),
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_feature_usage_user_id_feature_name_date_key` (`user_id`,`feature_name`,`date`),
  KEY `user_feature_usage_feature_name_idx` (`feature_name`),
  KEY `user_feature_usage_date_idx` (`date`),
  CONSTRAINT `user_feature_usage_user_id_fkey` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `user_feature_usage`
--

LOCK TABLES `user_feature_usage` WRITE;
/*!40000 ALTER TABLE `user_feature_usage` DISABLE KEYS */;
/*!40000 ALTER TABLE `user_feature_usage` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `user_roles`
--

DROP TABLE IF EXISTS `user_roles`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_roles` (
  `userId` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `roleId` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `assigned_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  PRIMARY KEY (`userId`,`roleId`),
  KEY `user_roles_roleId_fkey` (`roleId`),
  CONSTRAINT `user_roles_roleId_fkey` FOREIGN KEY (`roleId`) REFERENCES `roles` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `user_roles_userId_fkey` FOREIGN KEY (`userId`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `user_roles`
--

LOCK TABLES `user_roles` WRITE;
/*!40000 ALTER TABLE `user_roles` DISABLE KEYS */;
INSERT INTO `user_roles` VALUES ('cme3pnoma0003r8cva4fu9log','cme3pnnzi0000r8cvmmxia6oz','2025-08-09 03:44:16.750');
/*!40000 ALTER TABLE `user_roles` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `user_sessions`
--

DROP TABLE IF EXISTS `user_sessions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_sessions` (
  `id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `user_id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `session_token` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `ip_address` varchar(45) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `user_agent` text COLLATE utf8mb4_unicode_ci,
  `location` json DEFAULT NULL,
  `device_info` json DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `last_activity` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `login_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `logout_time` datetime(3) DEFAULT NULL,
  `duration_minutes` int DEFAULT NULL,
  `activity_score` double DEFAULT '0',
  `created_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `updated_at` datetime(3) NOT NULL ON UPDATE CURRENT_TIMESTAMP(3),
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_sessions_session_token_key` (`session_token`),
  KEY `user_sessions_user_id_idx` (`user_id`),
  KEY `user_sessions_is_active_idx` (`is_active`),
  KEY `user_sessions_last_activity_idx` (`last_activity`),
  CONSTRAINT `user_sessions_user_id_fkey` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `user_sessions`
--

LOCK TABLES `user_sessions` WRITE;
/*!40000 ALTER TABLE `user_sessions` DISABLE KEYS */;
/*!40000 ALTER TABLE `user_sessions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `users`
--

DROP TABLE IF EXISTS `users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `users` (
  `id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `username` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `email` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `password` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `full_name` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `phone` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `avatar` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `department` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `status` enum('ACTIVE','INACTIVE','BANNED') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'ACTIVE',
  `last_login_at` datetime(3) DEFAULT NULL,
  `created_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `updated_at` datetime(3) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `users_username_key` (`username`),
  UNIQUE KEY `users_email_key` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `users`
--

LOCK TABLES `users` WRITE;
/*!40000 ALTER TABLE `users` DISABLE KEYS */;
INSERT INTO `users` VALUES ('cme3pnoma0003r8cva4fu9log','admin','<EMAIL>','$2a$12$b1BCgoS.ZC7lRy70oeRPYeI/ZR2mfXs6/F/dlvLV2MRGOmEy5AilW','系统管理员',NULL,NULL,NULL,'ACTIVE',NULL,'2025-08-09 03:44:16.738','2025-08-09 03:44:16.738');
/*!40000 ALTER TABLE `users` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `workflow_definitions`
--

DROP TABLE IF EXISTS `workflow_definitions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `workflow_definitions` (
  `id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `category` enum('SERVICE_AUTOMATION','SLA_MONITORING','ALERT_PROCESSING','BACKUP_AUTOMATION','MAINTENANCE','APPROVAL_PROCESS','NOTIFICATION','DATA_PROCESSING','INTEGRATION','CUSTOM') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'SERVICE_AUTOMATION',
  `version` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '1.0',
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `is_template` tinyint(1) NOT NULL DEFAULT '0',
  `priority` enum('LOW','MEDIUM','HIGH','URGENT','CRITICAL') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'MEDIUM',
  `trigger_config` json NOT NULL,
  `steps_config` json NOT NULL,
  `conditions` json DEFAULT NULL,
  `variables` json DEFAULT NULL,
  `settings` json DEFAULT NULL,
  `tags` json DEFAULT NULL,
  `metadata` json DEFAULT NULL,
  `execution_count` int NOT NULL DEFAULT '0',
  `success_count` int NOT NULL DEFAULT '0',
  `failure_count` int NOT NULL DEFAULT '0',
  `last_executed_at` datetime(3) DEFAULT NULL,
  `avg_execution_time` double DEFAULT NULL,
  `created_by` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `updated_by` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `updated_at` datetime(3) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `workflow_definitions_name_key` (`name`),
  KEY `workflow_definitions_created_by_fkey` (`created_by`),
  KEY `workflow_definitions_updated_by_fkey` (`updated_by`),
  CONSTRAINT `workflow_definitions_created_by_fkey` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE,
  CONSTRAINT `workflow_definitions_updated_by_fkey` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `workflow_definitions`
--

LOCK TABLES `workflow_definitions` WRITE;
/*!40000 ALTER TABLE `workflow_definitions` DISABLE KEYS */;
INSERT INTO `workflow_definitions` VALUES ('cme6nmk7a0005rux3ukrgrt5m','测试工作流','这是一个测试工作流','SERVICE_AUTOMATION','1.0',1,0,'MEDIUM','{\"type\": \"MANUAL\", \"config\": {}}','{\"steps\": [{\"name\": \"发送通知\", \"type\": \"NOTIFICATION\", \"index\": 0, \"config\": {\"data\": {\"message\": \"Hello World\"}, \"template\": \"test\", \"recipients\": [\"admin\"]}}], \"trigger\": {\"type\": \"MANUAL\", \"config\": {}}, \"settings\": {\"timeout\": 300, \"errorHandling\": \"stop\"}, \"variables\": {}}',NULL,'{}','{\"timeout\": 300, \"errorHandling\": \"stop\"}','[\"测试\", \"演示\"]','{\"stepCount\": 1, \"hasApprovals\": false, \"hasConditions\": false, \"estimatedDuration\": 1}',1,1,0,'2025-08-11 05:11:03.280',NULL,'cme3pnoma0003r8cva4fu9log',NULL,'2025-08-11 05:10:43.652','2025-08-11 05:11:03.281');
/*!40000 ALTER TABLE `workflow_definitions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `workflow_execution_steps`
--

DROP TABLE IF EXISTS `workflow_execution_steps`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `workflow_execution_steps` (
  `id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `execution_id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `step_index` int NOT NULL,
  `step_name` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `step_type` enum('ACTION','CONDITION','APPROVAL','NOTIFICATION','DELAY','PARALLEL','LOOP','SUBPROCESS','SCRIPT','HTTP_REQUEST','DATABASE_OPERATION','FILE_OPERATION') COLLATE utf8mb4_unicode_ci NOT NULL,
  `status` enum('PENDING','RUNNING','COMPLETED','FAILED','SKIPPED','RETRYING') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'PENDING',
  `config` json NOT NULL,
  `input` json DEFAULT NULL,
  `output` json DEFAULT NULL,
  `started_at` datetime(3) DEFAULT NULL,
  `completed_at` datetime(3) DEFAULT NULL,
  `execution_time` double DEFAULT NULL,
  `result` json DEFAULT NULL,
  `error` text COLLATE utf8mb4_unicode_ci,
  `logs` json DEFAULT NULL,
  `retry_count` int NOT NULL DEFAULT '0',
  `created_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `updated_at` datetime(3) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `workflow_execution_steps_execution_id_step_index_key` (`execution_id`,`step_index`),
  KEY `workflow_execution_steps_execution_id_idx` (`execution_id`),
  KEY `workflow_execution_steps_status_idx` (`status`),
  CONSTRAINT `workflow_execution_steps_execution_id_fkey` FOREIGN KEY (`execution_id`) REFERENCES `workflow_executions` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `workflow_execution_steps`
--

LOCK TABLES `workflow_execution_steps` WRITE;
/*!40000 ALTER TABLE `workflow_execution_steps` DISABLE KEYS */;
INSERT INTO `workflow_execution_steps` VALUES ('cme6nmzbp000hrux399lo8l7y','cme6nmz9m000frux35vlcbv4z',0,'发送通知','NOTIFICATION','COMPLETED','{\"data\": {\"message\": \"Hello World\"}, \"template\": \"test\", \"recipients\": [\"admin\"]}',NULL,'{\"template\": \"test\", \"recipients\": [\"admin\"], \"notificationData\": {\"message\": \"Hello World\"}}','2025-08-11 05:11:03.253','2025-08-11 05:11:03.258',0.005,NULL,NULL,NULL,0,'2025-08-11 05:11:03.253','2025-08-11 05:11:03.261');
/*!40000 ALTER TABLE `workflow_execution_steps` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `workflow_executions`
--

DROP TABLE IF EXISTS `workflow_executions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `workflow_executions` (
  `id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `workflow_id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `execution_id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `status` enum('PENDING','RUNNING','PAUSED','COMPLETED','FAILED','CANCELLED','TIMEOUT','SKIPPED') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'PENDING',
  `priority` enum('LOW','MEDIUM','HIGH','URGENT','CRITICAL') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'MEDIUM',
  `trigger_type` enum('MANUAL','SCHEDULED','EVENT','WEBHOOK','API','CONDITION') COLLATE utf8mb4_unicode_ci NOT NULL,
  `trigger_data` json DEFAULT NULL,
  `triggered_by` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `current_step` int NOT NULL DEFAULT '0',
  `total_steps` int NOT NULL,
  `execution_data` json DEFAULT NULL,
  `variables` json DEFAULT NULL,
  `started_at` datetime(3) DEFAULT NULL,
  `completed_at` datetime(3) DEFAULT NULL,
  `scheduled_at` datetime(3) DEFAULT NULL,
  `timeout_at` datetime(3) DEFAULT NULL,
  `result` json DEFAULT NULL,
  `error` text COLLATE utf8mb4_unicode_ci,
  `logs` json DEFAULT NULL,
  `execution_time` double DEFAULT NULL,
  `resource_usage` json DEFAULT NULL,
  `created_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `updated_at` datetime(3) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `workflow_executions_execution_id_key` (`execution_id`),
  KEY `workflow_executions_workflow_id_idx` (`workflow_id`),
  KEY `workflow_executions_status_idx` (`status`),
  KEY `workflow_executions_trigger_type_idx` (`trigger_type`),
  KEY `workflow_executions_created_at_idx` (`created_at`),
  KEY `workflow_executions_triggered_by_fkey` (`triggered_by`),
  CONSTRAINT `workflow_executions_triggered_by_fkey` FOREIGN KEY (`triggered_by`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
  CONSTRAINT `workflow_executions_workflow_id_fkey` FOREIGN KEY (`workflow_id`) REFERENCES `workflow_definitions` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `workflow_executions`
--

LOCK TABLES `workflow_executions` WRITE;
/*!40000 ALTER TABLE `workflow_executions` DISABLE KEYS */;
INSERT INTO `workflow_executions` VALUES ('cme6nmz9m000frux35vlcbv4z','cme6nmk7a0005rux3ukrgrt5m','exec_1754889063178_j76hn5phe','COMPLETED','MEDIUM','MANUAL','{\"testMessage\": \"Hello from workflow trigger\"}','cme3pnoma0003r8cva4fu9log',1,1,NULL,'{\"template\": \"test\", \"recipients\": [\"admin\"], \"notificationData\": {\"message\": \"Hello World\"}}','2025-08-11 05:11:03.227','2025-08-11 05:11:03.276','2025-08-11 05:11:03.178','2025-08-11 05:16:03.178',NULL,NULL,NULL,0.049,NULL,'2025-08-11 05:11:03.179','2025-08-11 05:11:03.276');
/*!40000 ALTER TABLE `workflow_executions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `workflow_templates`
--

DROP TABLE IF EXISTS `workflow_templates`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `workflow_templates` (
  `id` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `category` enum('SERVICE_AUTOMATION','SLA_MONITORING','ALERT_PROCESSING','BACKUP_AUTOMATION','MAINTENANCE','APPROVAL_PROCESS','NOTIFICATION','DATA_PROCESSING','INTEGRATION','CUSTOM') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'SERVICE_AUTOMATION',
  `tags` json DEFAULT NULL,
  `template_config` json NOT NULL,
  `variables` json DEFAULT NULL,
  `requirements` json DEFAULT NULL,
  `usage_count` int NOT NULL DEFAULT '0',
  `rating` double DEFAULT NULL,
  `is_official` tinyint(1) NOT NULL DEFAULT '0',
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `created_by` varchar(191) COLLATE utf8mb4_unicode_ci NOT NULL,
  `updated_by` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `updated_at` datetime(3) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `workflow_templates_name_key` (`name`),
  KEY `workflow_templates_created_by_fkey` (`created_by`),
  KEY `workflow_templates_updated_by_fkey` (`updated_by`),
  CONSTRAINT `workflow_templates_created_by_fkey` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE,
  CONSTRAINT `workflow_templates_updated_by_fkey` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `workflow_templates`
--

LOCK TABLES `workflow_templates` WRITE;
/*!40000 ALTER TABLE `workflow_templates` DISABLE KEYS */;
/*!40000 ALTER TABLE `workflow_templates` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Dumping routines for database 'ops_management'
--
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-08-11 21:38:08
