/**
 * API 客户端
 */

import { CreateServiceRequest, ApiResponse, Service, PaginatedResponse } from './types'

export class ApiClient {
  private apiKey: string
  private apiUrl: string

  constructor(apiKey: string, apiUrl: string) {
    this.apiKey = apiKey
    this.apiUrl = apiUrl.replace(/\/$/, '') // 移除末尾斜杠
  }

  private async request<T>(endpoint: string, options: RequestInit = {}): Promise<ApiResponse<T>> {
    const url = `${this.apiUrl}${endpoint}`
    
    const defaultHeaders = {
      'Content-Type': 'application/json',
      'X-API-Key': this.apiKey
    }

    try {
      const response = await fetch(url, {
        mode: 'cors', // 显式启用CORS
        ...options,
        headers: {
          ...defaultHeaders,
          ...options.headers
        }
      })

      const data: ApiResponse<T> = await response.json()
      
      if (!response.ok) {
        console.error('API请求失败:', data)
      }

      return data
    } catch (error: any) {
      console.error('网络请求失败:', error)
      
      // 检查是否是CORS错误
      if (error.name === 'TypeError' && error.message.includes('Failed to fetch')) {
        return {
          success: false,
          message: 'CORS错误：无法连接到API服务器。请确保服务器配置了正确的CORS策略。',
          code: 'CORS_ERROR'
        }
      }
      
      // 检查是否是网络错误
      if (error.name === 'TypeError' || error.message.includes('NetworkError')) {
        return {
          success: false,
          message: '网络连接失败，请检查网络连接和API地址',
          code: 'NETWORK_ERROR'
        }
      }

      return {
        success: false,
        message: `请求失败: ${error.message}`,
        code: 'REQUEST_ERROR'
      }
    }
  }

  /**
   * 创建工单
   */
  async createService(data: CreateServiceRequest): Promise<ApiResponse<Service>> {
    return this.request<Service>('/api/v1/external/services', {
      method: 'POST',
      body: JSON.stringify(data)
    })
  }

  /**
   * 获取工单列表
   */
  async getServices(params: {
    page?: number
    limit?: number
    status?: string
    priority?: string
    externalUserId?: string
  } = {}): Promise<PaginatedResponse<Service>> {
    const searchParams = new URLSearchParams()
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined) {
        searchParams.set(key, String(value))
      }
    })

    const query = searchParams.toString()
    const endpoint = `/api/v1/external/services${query ? '?' + query : ''}`
    
    return this.request<PaginatedResponse<Service>['data']>(endpoint)
  }

  /**
   * 获取工单详情
   */
  async getService(id: string): Promise<ApiResponse<Service>> {
    return this.request<Service>(`/api/v1/external/services/${id}`)
  }

  /**
   * 添加工单评论
   */
  async addComment(serviceId: string, content: string): Promise<ApiResponse<any>> {
    return this.request(`/api/v1/external/services/${serviceId}/comments`, {
      method: 'POST',
      body: JSON.stringify({ content })
    })
  }

  /**
   * 检查API连接
   */
  async checkConnection(): Promise<boolean> {
    try {
      const response = await this.getServices({ page: 1, limit: 1 })
      return response.success
    } catch (error) {
      console.error('连接检查失败:', error)
      return false
    }
  }
}