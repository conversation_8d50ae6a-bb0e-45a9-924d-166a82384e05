/**
 * 运维管理系统 JavaScript SDK
 * 最小MVP版本
 */

import { OpsSdkConfig, SdkEvent, EventListener } from './types'
import { ApiClient } from './api'
import { UI } from './ui'

class OpsManagementSDK {
  private config?: OpsSdkConfig
  private apiClient?: ApiClient
  private ui?: UI
  private initialized = false

  /**
   * 初始化SDK
   */
  init(config: OpsSdkConfig): void {
    if (this.initialized) {
      console.warn('SDK已经初始化')
      return
    }

    // 验证配置
    if (!this.validateConfig(config)) {
      throw new Error('SDK配置无效')
    }

    this.config = config
    this.apiClient = new ApiClient(config.apiKey, config.apiUrl)
    this.ui = new UI(config, this.apiClient)

    // 初始化UI
    this.ui.init()
    
    this.initialized = true
    console.log('运维管理SDK初始化完成')

    // 检查API连接
    this.checkConnection()
  }

  /**
   * 验证配置
   */
  private validateConfig(config: OpsSdkConfig): boolean {
    if (!config.apiKey) {
      console.error('API Key 不能为空')
      return false
    }

    if (!config.apiUrl) {
      console.error('API URL 不能为空')
      return false
    }

    try {
      new URL(config.apiUrl)
    } catch {
      console.error('API URL 格式无效')
      return false
    }

    return true
  }

  /**
   * 检查API连接
   */
  private async checkConnection(): Promise<void> {
    if (!this.apiClient) return

    try {
      const isConnected = await this.apiClient.checkConnection()
      if (isConnected) {
        console.log('API连接正常')
        this.emit('ready')
      } else {
        console.warn('API连接失败，请检查配置')
        this.emit('error', { message: 'API连接失败' })
      }
    } catch (error) {
      console.error('连接检查失败:', error)
      this.emit('error', { message: '连接检查失败', error })
    }
  }

  /**
   * 显示工单创建弹窗
   */
  show(): void {
    if (!this.initialized) {
      console.error('SDK未初始化')
      return
    }

    // 触发点击浮动图标事件
    const icon = document.getElementById('ops-sdk-icon')
    if (icon) {
      icon.click()
    }
  }

  /**
   * 隐藏工单创建弹窗
   */
  hide(): void {
    if (!this.initialized) {
      console.error('SDK未初始化')
      return
    }

    const modal = document.getElementById('ops-sdk-modal')
    if (modal) {
      modal.style.display = 'none'
    }
  }

  /**
   * 获取工单列表
   */
  async getServices(params?: {
    page?: number
    limit?: number
    status?: string
    priority?: string
  }) {
    if (!this.apiClient) {
      throw new Error('SDK未初始化')
    }

    return this.apiClient.getServices({
      ...params,
      externalUserId: this.config?.externalUserId
    })
  }

  /**
   * 获取工单详情
   */
  async getService(id: string) {
    if (!this.apiClient) {
      throw new Error('SDK未初始化')
    }

    return this.apiClient.getService(id)
  }

  /**
   * 添加工单评论
   */
  async addComment(serviceId: string, content: string) {
    if (!this.apiClient) {
      throw new Error('SDK未初始化')
    }

    return this.apiClient.addComment(serviceId, content)
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<OpsSdkConfig>): void {
    if (!this.initialized || !this.config) {
      console.error('SDK未初始化')
      return
    }

    // 合并配置
    this.config = {
      ...this.config,
      ...newConfig,
      ui: {
        ...this.config.ui,
        ...newConfig.ui
      },
      features: {
        ...this.config.features,
        ...newConfig.features
      }
    }

    // 重新初始化UI（如果需要）
    console.log('配置已更新')
  }

  /**
   * 添加事件监听器
   */
  on(event: SdkEvent, callback: EventListener): void {
    if (!this.ui) {
      console.error('SDK未初始化')
      return
    }

    this.ui.on(event, callback)
  }

  /**
   * 移除事件监听器
   */
  off(event: SdkEvent, callback?: EventListener): void {
    if (!this.ui) {
      console.error('SDK未初始化')
      return
    }

    this.ui.off(event, callback)
  }

  /**
   * 触发事件
   */
  private emit(event: SdkEvent, data?: any): void {
    if (!this.ui) return
    this.ui.emit(event, data)
  }

  /**
   * 销毁SDK
   */
  destroy(): void {
    if (!this.initialized) return

    if (this.ui) {
      this.ui.destroy()
    }

    this.config = undefined
    this.apiClient = undefined
    this.ui = undefined
    this.initialized = false

    console.log('SDK已销毁')
  }

  /**
   * 获取版本信息
   */
  get version(): string {
    return '1.0.0'
  }

  /**
   * 获取初始化状态
   */
  get isInitialized(): boolean {
    return this.initialized
  }
}

// 创建全局实例
const opsSDK = new OpsManagementSDK()

// 导出默认实例
export default opsSDK

// 也可以导出类供创建多个实例
export { OpsManagementSDK }