/**
 * UI 组件和样式管理
 */

import { OpsSdkConfig, PositionStyle, CreateServiceRequest } from './types'
import { ApiClient } from './api'

export class UI {
  private config: OpsSdkConfig
  private apiClient: ApiClient
  private container?: HTMLElement
  private modal?: HTMLElement
  private eventListeners: { [key: string]: Function[] } = {}

  constructor(config: OpsSdkConfig, apiClient: ApiClient) {
    this.config = config
    this.apiClient = apiClient
  }

  /**
   * 初始化UI
   */
  init(): void {
    this.createContainer()
    this.createFloatingIcon()
    this.bindEvents()
  }

  /**
   * 创建容器
   */
  private createContainer(): void {
    this.container = document.createElement('div')
    this.container.id = 'ops-sdk-container'
    this.container.style.cssText = `
      position: fixed;
      z-index: 9999;
      font-family: ${this.config.ui?.fonts?.family || '-apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif'};
      font-size: ${this.config.ui?.fonts?.size || '14px'};
    `
    
    // 设置位置
    const position = this.getPositionStyle(this.config.position || 'bottom-right')
    Object.assign(this.container.style, position)
    
    document.body.appendChild(this.container)
  }

  /**
   * 创建浮动图标
   */
  private createFloatingIcon(): void {
    if (!this.config.features?.showIcon) return

    const icon = document.createElement('div')
    icon.id = 'ops-sdk-icon'
    icon.innerHTML = this.config.ui?.logo || this.getDefaultIcon()
    icon.title = this.config.ui?.title || '提交工单'
    
    icon.style.cssText = `
      width: 60px;
      height: 60px;
      border-radius: 50%;
      background: ${this.config.ui?.colors?.primary || '#1890ff'};
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      box-shadow: 0 4px 12px rgba(0,0,0,0.15);
      transition: all 0.3s ease;
      font-size: 24px;
    `

    // 悬停效果
    icon.addEventListener('mouseenter', () => {
      icon.style.transform = 'scale(1.1)'
      icon.style.boxShadow = '0 6px 16px rgba(0,0,0,0.2)'
    })

    icon.addEventListener('mouseleave', () => {
      icon.style.transform = 'scale(1)'
      icon.style.boxShadow = '0 4px 12px rgba(0,0,0,0.15)'
    })

    this.container!.appendChild(icon)
  }

  /**
   * 创建工单提交弹窗
   */
  private createModal(): void {
    this.modal = document.createElement('div')
    this.modal.id = 'ops-sdk-modal'
    this.modal.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.5);
      z-index: 10000;
      display: flex;
      align-items: center;
      justify-content: center;
    `

    const modalContent = document.createElement('div')
    modalContent.style.cssText = `
      background: white;
      border-radius: 8px;
      width: 90%;
      max-width: 500px;
      max-height: 80vh;
      overflow-y: auto;
      box-shadow: 0 8px 32px rgba(0,0,0,0.2);
    `

    modalContent.innerHTML = this.getModalHTML()
    this.modal.appendChild(modalContent)
    document.body.appendChild(this.modal)

    this.bindModalEvents()
  }

  /**
   * 获取弹窗HTML
   */
  private getModalHTML(): string {
    return `
      <div style="padding: 24px;">
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
          <h3 style="margin: 0; color: ${this.config.ui?.colors?.primary || '#1890ff'};">
            ${this.config.ui?.title || '提交工单'}
          </h3>
          <button id="ops-sdk-close" style="background: none; border: none; font-size: 24px; cursor: pointer; color: #999;">×</button>
        </div>
        
        ${this.config.ui?.description ? `<p style="margin-bottom: 20px; color: #666;">${this.config.ui.description}</p>` : ''}
        
        <form id="ops-sdk-form">
          <div style="margin-bottom: 16px;">
            <label style="display: block; margin-bottom: 4px; font-weight: 500;">工单标题 *</label>
            <input type="text" name="title" required 
                   style="width: 100%; padding: 8px 12px; border: 1px solid #d9d9d9; border-radius: 4px; font-size: 14px;">
          </div>
          
          <div style="margin-bottom: 16px;">
            <label style="display: block; margin-bottom: 4px; font-weight: 500;">问题描述 *</label>
            <textarea name="description" required rows="4"
                      style="width: 100%; padding: 8px 12px; border: 1px solid #d9d9d9; border-radius: 4px; font-size: 14px; resize: vertical;"></textarea>
          </div>
          
          <div style="display: flex; gap: 12px; margin-bottom: 16px;">
            <div style="flex: 1;">
              <label style="display: block; margin-bottom: 4px; font-weight: 500;">类别</label>
              <select name="category" style="width: 100%; padding: 8px 12px; border: 1px solid #d9d9d9; border-radius: 4px; font-size: 14px;">
                <option value="SUPPORT">技术支持</option>
                <option value="BUGFIX">问题修复</option>
                <option value="MAINTENANCE">维护</option>
                <option value="UPGRADE">升级</option>
                <option value="CONSULTING">咨询</option>
                <option value="MONITORING">监控</option>
              </select>
            </div>
            
            <div style="flex: 1;">
              <label style="display: block; margin-bottom: 4px; font-weight: 500;">优先级</label>
              <select name="priority" style="width: 100%; padding: 8px 12px; border: 1px solid #d9d9d9; border-radius: 4px; font-size: 14px;">
                <option value="MEDIUM">中等</option>
                <option value="LOW">低</option>
                <option value="HIGH">高</option>
                <option value="URGENT">紧急</option>
              </select>
            </div>
          </div>
          
          <div style="margin-bottom: 20px;">
            <label style="display: block; margin-bottom: 4px; font-weight: 500;">联系人</label>
            <input type="text" name="customerContact" 
                   style="width: 100%; padding: 8px 12px; border: 1px solid #d9d9d9; border-radius: 4px; font-size: 14px;">
          </div>
          
          <div id="ops-sdk-error" style="display: none; padding: 8px 12px; background: #fff2f0; border: 1px solid #ffccc7; border-radius: 4px; color: #f5222d; margin-bottom: 16px;"></div>
          
          <div style="display: flex; gap: 12px; justify-content: flex-end;">
            <button type="button" id="ops-sdk-cancel" 
                    style="padding: 8px 16px; border: 1px solid #d9d9d9; background: white; border-radius: 4px; cursor: pointer;">
              取消
            </button>
            <button type="submit" id="ops-sdk-submit"
                    style="padding: 8px 16px; border: none; background: ${this.config.ui?.colors?.primary || '#1890ff'}; color: white; border-radius: 4px; cursor: pointer;">
              提交工单
            </button>
          </div>
        </form>
      </div>
    `
  }

  /**
   * 绑定事件
   */
  private bindEvents(): void {
    // 点击浮动图标打开弹窗
    const icon = document.getElementById('ops-sdk-icon')
    if (icon) {
      icon.addEventListener('click', () => {
        this.showModal()
      })
    }
  }

  /**
   * 绑定弹窗事件
   */
  private bindModalEvents(): void {
    if (!this.modal) return

    // 关闭按钮
    const closeBtn = document.getElementById('ops-sdk-close')
    if (closeBtn) {
      closeBtn.addEventListener('click', () => {
        this.hideModal()
      })
    }

    // 取消按钮
    const cancelBtn = document.getElementById('ops-sdk-cancel')
    if (cancelBtn) {
      cancelBtn.addEventListener('click', () => {
        this.hideModal()
      })
    }

    // 点击背景关闭
    this.modal.addEventListener('click', (e) => {
      if (e.target === this.modal) {
        this.hideModal()
      }
    })

    // 表单提交
    const form = document.getElementById('ops-sdk-form') as HTMLFormElement
    if (form) {
      form.addEventListener('submit', (e) => {
        e.preventDefault()
        this.handleSubmit(form)
      })
    }
  }

  /**
   * 处理表单提交
   */
  private async handleSubmit(form: HTMLFormElement): Promise<void> {
    const formData = new FormData(form)
    const errorDiv = document.getElementById('ops-sdk-error')
    const submitBtn = document.getElementById('ops-sdk-submit') as HTMLButtonElement

    // 显示加载状态
    submitBtn.textContent = '提交中...'
    submitBtn.disabled = true

    try {
      // 构建请求数据
      const request: CreateServiceRequest = {
        archiveId: this.config.archiveId || 'default-archive', // 从配置中获取项目档案ID
        title: formData.get('title') as string,
        description: formData.get('description') as string,
        category: formData.get('category') as any || 'SUPPORT',
        priority: formData.get('priority') as any || 'MEDIUM',
        externalUserId: this.config.externalUserId,
        externalAccount: this.config.externalAccount,
        customerContact: formData.get('customerContact') as string
      }

      const response = await this.apiClient.createService(request)

      if (response.success) {
        this.emit('service-created', response.data)
        this.showSuccess('工单提交成功！工单号：' + response.data?.ticketNumber)
        this.hideModal()
      } else {
        // 根据错误代码显示不同的错误消息
        let errorMessage = response.message || '工单提交失败'
        if (response.code === 'CORS_ERROR') {
          errorMessage = '连接失败：请联系管理员检查API服务器CORS配置'
        } else if (response.code === 'INVALID_API_KEY') {
          errorMessage = 'API密钥无效，请检查配置'
        } else if (response.code === 'NETWORK_ERROR') {
          errorMessage = '网络连接失败，请检查网络连接'
        }
        this.showError(errorMessage)
      }
    } catch (error) {
      console.error('提交工单失败:', error)
      this.showError('网络错误，请稍后重试')
    } finally {
      // 恢复按钮状态
      submitBtn.textContent = '提交工单'
      submitBtn.disabled = false
    }
  }

  /**
   * 显示错误信息
   */
  private showError(message: string): void {
    const errorDiv = document.getElementById('ops-sdk-error')
    if (errorDiv) {
      errorDiv.textContent = message
      errorDiv.style.display = 'block'
    }
  }

  /**
   * 显示成功信息
   */
  private showSuccess(message: string): void {
    // 创建成功提示
    const toast = document.createElement('div')
    toast.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: #f6ffed;
      border: 1px solid #b7eb8f;
      color: #52c41a;
      padding: 12px 16px;
      border-radius: 4px;
      z-index: 10001;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    `
    toast.textContent = message
    document.body.appendChild(toast)

    // 3秒后自动移除
    setTimeout(() => {
      if (toast.parentNode) {
        toast.parentNode.removeChild(toast)
      }
    }, 3000)
  }

  /**
   * 显示弹窗
   */
  private showModal(): void {
    if (!this.modal) {
      this.createModal()
    }
    this.modal!.style.display = 'flex'
    this.emit('modal-open')
  }

  /**
   * 隐藏弹窗
   */
  private hideModal(): void {
    if (this.modal) {
      this.modal.style.display = 'none'
    }
    this.emit('modal-close')
  }

  /**
   * 获取位置样式
   */
  private getPositionStyle(position: string): PositionStyle {
    const positions: { [key: string]: PositionStyle } = {
      'bottom-right': { bottom: '20px', right: '20px' },
      'bottom-left': { bottom: '20px', left: '20px' },
      'top-right': { top: '20px', right: '20px' },
      'top-left': { top: '20px', left: '20px' },
      'bottom-center': { bottom: '20px', left: '50%', transform: 'translateX(-50%)' },
      'top-center': { top: '20px', left: '50%', transform: 'translateX(-50%)' },
      'center-right': { right: '20px', top: '50%', transform: 'translateY(-50%)' },
      'center-left': { left: '20px', top: '50%', transform: 'translateY(-50%)' },
      'center': { top: '50%', left: '50%', transform: 'translate(-50%, -50%)' }
    }
    return positions[position] || positions['bottom-right']
  }

  /**
   * 获取默认图标
   */
  private getDefaultIcon(): string {
    return `
      <svg width="32" height="32" viewBox="0 0 24 24" fill="currentColor">
        <path d="M20 6h-2.18c.11-.31.18-.65.18-1a2.996 2.996 0 0 0-5.5-1.65l-.5.67-.5-.68C10.96 2.54 10 2 9 2 7.34 2 6 3.34 6 5c0 .35.07.69.18 1H4c-1.11 0-1.99.89-1.99 2L2 19c0 1.11.89 2 2 2h16c1.11 0 2-.89 2-2V8c0-1.11-.89-2-2-2zm-5-2c.55 0 1 .45 1 1s-.45 1-1 1-1-.45-1-1 .45-1 1-1zM9 4c.55 0 1 .45 1 1s-.45 1-1 1-1-.45-1-1 .45-1 1-1z"/>
      </svg>
    `
  }

  /**
   * 添加事件监听器
   */
  on(event: string, callback: Function): void {
    if (!this.eventListeners[event]) {
      this.eventListeners[event] = []
    }
    this.eventListeners[event].push(callback)
  }

  /**
   * 移除事件监听器
   */
  off(event: string, callback?: Function): void {
    if (!this.eventListeners[event]) return
    
    if (callback) {
      const index = this.eventListeners[event].indexOf(callback)
      if (index > -1) {
        this.eventListeners[event].splice(index, 1)
      }
    } else {
      this.eventListeners[event] = []
    }
  }

  /**
   * 触发事件
   */
  emit(event: string, data?: any): void {
    if (!this.eventListeners[event]) return
    
    this.eventListeners[event].forEach(callback => {
      try {
        callback(data)
      } catch (error) {
        console.error('事件回调执行失败:', error)
      }
    })
  }

  /**
   * 销毁UI
   */
  destroy(): void {
    if (this.container && this.container.parentNode) {
      this.container.parentNode.removeChild(this.container)
    }
    if (this.modal && this.modal.parentNode) {
      this.modal.parentNode.removeChild(this.modal)
    }
    this.eventListeners = {}
  }
}