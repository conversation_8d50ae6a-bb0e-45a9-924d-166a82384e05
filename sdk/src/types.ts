/**
 * SDK 类型定义
 */

// SDK配置接口
export interface OpsSdkConfig {
  // 基础配置
  apiKey: string
  apiUrl: string
  archiveId?: string // 项目档案ID（必需，用于创建工单）
  externalSystemId?: string
  externalUserId?: string
  externalAccount?: string

  // UI配置
  ui?: {
    colors?: {
      primary?: string
      secondary?: string
      background?: string
      text?: string
    }
    fonts?: {
      family?: string
      size?: string
    }
    logo?: string
    title?: string
    description?: string
  }

  // 位置配置
  position?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left' | 
            'bottom-center' | 'top-center' | 'center-right' | 'center-left' | 'center'

  // 功能配置
  features?: {
    showIcon?: boolean
    showBadge?: boolean
    enableSound?: boolean
  }
}

// 工单创建请求接口
export interface CreateServiceRequest {
  archiveId: string
  title: string
  description: string
  category?: 'MAINTENANCE' | 'SUPPORT' | 'UPGRADE' | 'BUGFIX' | 'CONSULTING' | 'MONITORING'
  priority?: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT'
  externalUserId?: string
  externalAccount?: string
  customerContact?: string
  estimatedHours?: number
  tags?: string[]
}

// 工单接口
export interface Service {
  id: string
  ticketNumber: string
  title: string
  description: string
  category: string
  priority: string
  status: string
  source: string
  externalSystemId?: string
  externalUserId?: string
  externalAccount?: string
  customerContact?: string
  estimatedHours?: number
  actualHours?: number
  tags?: string[]
  createdAt: string
  updatedAt: string
  archive: {
    id: string
    name: string
    customer: {
      id: string
      name: string
      code: string
    }
  }
  assignedUser?: {
    id: string
    username: string
    fullName: string
  }
}

// API响应接口
export interface ApiResponse<T = any> {
  success: boolean
  message?: string
  data?: T
  errors?: { field: string; message: string }[]
  code?: string
}

// 分页响应接口
export interface PaginatedResponse<T> extends ApiResponse<{
  services: T[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}> {}

// 事件类型
export type SdkEvent = 'ready' | 'service-created' | 'service-updated' | 'error' | 'close'

// 事件监听器
export type EventListener = (data?: any) => void

// 浮动图标位置样式
export interface PositionStyle {
  top?: string
  bottom?: string
  left?: string
  right?: string
  transform?: string
}