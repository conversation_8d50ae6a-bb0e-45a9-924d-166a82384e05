<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>运维管理SDK演示</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        h1 {
            color: #1890ff;
            text-align: center;
            margin-bottom: 30px;
        }
        .section {
            margin-bottom: 30px;
        }
        .section h3 {
            color: #333;
            margin-bottom: 15px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }
        input, select, textarea {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            font-size: 14px;
        }
        button {
            padding: 10px 20px;
            background: #1890ff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background: #40a9ff;
        }
        button.secondary {
            background: #f5f5f5;
            color: #666;
        }
        button.secondary:hover {
            background: #e6f7ff;
        }
        .status {
            padding: 12px;
            border-radius: 4px;
            margin-top: 15px;
        }
        .status.success {
            background: #f6ffed;
            border: 1px solid #b7eb8f;
            color: #52c41a;
        }
        .status.error {
            background: #fff2f0;
            border: 1px solid #ffccc7;
            color: #f5222d;
        }
        .status.info {
            background: #e6f7ff;
            border: 1px solid #91d5ff;
            color: #1890ff;
        }
        .log {
            background: #fafafa;
            border: 1px solid #e8e8e8;
            border-radius: 4px;
            padding: 15px;
            max-height: 200px;
            overflow-y: auto;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>运维管理系统 JavaScript SDK 演示</h1>
        
        <div class="section">
            <h3>1. SDK配置</h3>
            <div class="form-group">
                <label>API Key:</label>
                <input type="text" id="apiKey" value="ak_demo1234567890abcdef" placeholder="输入您的API Key（由系统生成）">
            </div>
            <div class="form-group">
                <label>API URL:</label>
                <input type="text" id="apiUrl" value="http://localhost:3001/api" placeholder="输入API地址">
            </div>
            <div class="form-group">
                <label>项目档案ID:</label>
                <input type="text" id="archiveId" value="project-archive-id" placeholder="项目档案ID（必需）">
            </div>
            <div class="form-group">
                <label>外部系统ID:</label>
                <input type="text" id="externalSystemId" value="demo-system" placeholder="外部系统标识">
            </div>
            <div class="form-group">
                <label>外部用户ID:</label>
                <input type="text" id="externalUserId" value="user123" placeholder="外部用户ID">
            </div>
            <div class="form-group">
                <label>浮动图标位置:</label>
                <select id="position">
                    <option value="bottom-right">右下角</option>
                    <option value="bottom-left">左下角</option>
                    <option value="top-right">右上角</option>
                    <option value="top-left">左上角</option>
                    <option value="bottom-center">底部中央</option>
                    <option value="top-center">顶部中央</option>
                    <option value="center">居中</option>
                </select>
            </div>
            <div class="form-group">
                <label>主题颜色:</label>
                <input type="color" id="primaryColor" value="#1890ff">
            </div>
            <button onclick="initSDK()">初始化SDK</button>
            <button onclick="destroySDK()" class="secondary">销毁SDK</button>
        </div>

        <div class="section">
            <h3>2. SDK控制</h3>
            <button onclick="showModal()">显示工单弹窗</button>
            <button onclick="hideModal()" class="secondary">隐藏工单弹窗</button>
            <button onclick="getServices()" class="secondary">获取工单列表</button>
        </div>

        <div class="section">
            <h3>3. 状态信息</h3>
            <div id="status"></div>
        </div>

        <div class="section">
            <h3>4. 事件日志</h3>
            <button onclick="clearLog()" class="secondary">清除日志</button>
            <div id="log" class="log"></div>
        </div>
    </div>

    <!-- 包含构建后的SDK -->
    <script src="../dist/ops-sdk.js"></script>
    
    <script>
        let sdk = null;

        // 日志函数
        function log(message, type = 'info') {
            const logElement = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}\n`;
            logElement.textContent += logEntry;
            logElement.scrollTop = logElement.scrollHeight;

            // 更新状态
            updateStatus(message, type);
        }

        // 更新状态
        function updateStatus(message, type = 'info') {
            const statusElement = document.getElementById('status');
            statusElement.className = `status ${type}`;
            statusElement.textContent = message;
        }

        // 初始化SDK
        function initSDK() {
            try {
                if (sdk) {
                    sdk.destroy();
                }

                const config = {
                    apiKey: document.getElementById('apiKey').value,
                    apiUrl: document.getElementById('apiUrl').value,
                    archiveId: document.getElementById('archiveId').value,
                    externalSystemId: document.getElementById('externalSystemId').value,
                    externalUserId: document.getElementById('externalUserId').value,
                    externalAccount: 'demo-account',
                    position: document.getElementById('position').value,
                    ui: {
                        colors: {
                            primary: document.getElementById('primaryColor').value
                        },
                        title: '提交运维工单',
                        description: '遇到问题？立即提交工单，我们的技术团队将快速为您解决。'
                    },
                    features: {
                        showIcon: true,
                        showBadge: false,
                        enableSound: false
                    }
                };

                // 使用全局SDK实例
                sdk = window.OpsSDK;
                sdk.init(config);
                console.log('config', config);

                // 添加事件监听器
                sdk.on('ready', () => {
                    log('SDK就绪', 'success');
                });

                sdk.on('service-created', (service) => {
                    log(`工单创建成功: ${service.ticketNumber}`, 'success');
                });

                sdk.on('error', (error) => {
                    log(`错误: ${error.message}`, 'error');
                });

                sdk.on('modal-open', () => {
                    log('工单弹窗已打开', 'info');
                });

                sdk.on('modal-close', () => {
                    log('工单弹窗已关闭', 'info');
                });

                log('正在初始化SDK...', 'info');

            } catch (error) {
                log(`初始化失败: ${error.message}`, 'error');
            }
        }

        // 销毁SDK
        function destroySDK() {
            if (sdk) {
                sdk.destroy();
                sdk = null;
                log('SDK已销毁', 'info');
            }
        }

        // 显示弹窗
        function showModal() {
            if (sdk) {
                sdk.show();
            } else {
                log('请先初始化SDK', 'error');
            }
        }

        // 隐藏弹窗
        function hideModal() {
            if (sdk) {
                sdk.hide();
            } else {
                log('请先初始化SDK', 'error');
            }
        }

        // 获取工单列表
        async function getServices() {
            if (!sdk) {
                log('请先初始化SDK', 'error');
                return;
            }

            try {
                log('正在获取工单列表...', 'info');
                const response = await sdk.getServices({ page: 1, limit: 5 });
                
                if (response.success) {
                    log(`获取到 ${response.data.services.length} 个工单`, 'success');
                    response.data.services.forEach((service, index) => {
                        log(`${index + 1}. ${service.ticketNumber}: ${service.title}`, 'info');
                    });
                } else {
                    log(`获取失败: ${response.message}`, 'error');
                }
            } catch (error) {
                log(`获取失败: ${error.message}`, 'error');
            }
        }

        // 清除日志
        function clearLog() {
            document.getElementById('log').textContent = '';
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', () => {
            log('演示页面已加载', 'info');
            updateStatus('请配置SDK参数后点击初始化', 'info');
        });
    </script>
</body>
</html>