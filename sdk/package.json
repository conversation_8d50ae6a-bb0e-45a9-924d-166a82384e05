{"name": "ops-management-sdk", "version": "1.0.0", "description": "运维管理系统 JavaScript SDK - 最小MVP", "main": "dist/ops-sdk.js", "types": "dist/ops-sdk.d.ts", "scripts": {"build": "webpack --mode production", "dev": "webpack --mode development --watch", "serve": "http-server demo -p 3003"}, "keywords": ["ops", "management", "service", "ticket", "sdk", "javascript"], "author": "Ops Management Team", "license": "MIT", "devDependencies": {"@types/node": "^20.10.0", "http-server": "^14.1.1", "ts-loader": "^9.5.0", "typescript": "^5.3.0", "webpack": "^5.89.0", "webpack-cli": "^5.1.4"}, "files": ["dist/", "README.md"]}