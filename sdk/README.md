# 运维管理系统 JavaScript SDK

## 概述

运维管理系统 JavaScript SDK 是一个轻量级的前端集成工具，允许外部网站快速集成运维工单提交功能。用户可以通过浮动图标轻松提交工单，无需跳转到其他页面。

## 最小MVP特性

- ✅ **浮动图标**: 支持9种位置配置
- ✅ **工单创建**: 简洁的弹窗表单
- ✅ **API Key验证**: 安全的外部调用
- ✅ **自定义主题**: 颜色、字体、Logo配置  
- ✅ **事件监听**: 完整的事件系统
- ✅ **响应式设计**: 移动端友好

## 快速开始

### 1. 引入SDK

```html
<script src="path/to/ops-sdk.js"></script>
```

### 2. 初始化配置

```javascript
// 使用全局实例
OpsSDK.init({
  apiKey: 'ak_demo1234567890abcdef',
  apiUrl: 'http://localhost:3001/api',
  archiveId: 'project-archive-id', // 必需：项目档案ID
  externalSystemId: 'your-system-id',
  externalUserId: 'user-123',
  
  // UI配置
  position: 'bottom-right',
  ui: {
    colors: {
      primary: '#1890ff'
    },
    title: '提交工单',
    description: '遇到问题？立即联系我们！'
  }
});
```

### 3. 事件监听

```javascript
OpsSDK.on('ready', () => {
  console.log('SDK初始化完成');
});

OpsSDK.on('service-created', (service) => {
  console.log('工单创建成功:', service.ticketNumber);
});

OpsSDK.on('error', (error) => {
  console.error('发生错误:', error.message);
});
```

## 配置选项

### 基础配置

| 参数 | 类型 | 必须 | 描述 |
|-----|------|------|------|
| `apiKey` | string | ✅ | API密钥 |
| `apiUrl` | string | ✅ | API基础地址 |
| `archiveId` | string | ✅ | 项目档案ID（用于创建工单） |
| `externalSystemId` | string | - | 外部系统标识 |
| `externalUserId` | string | - | 外部用户ID |
| `externalAccount` | string | - | 外部账号 |

### UI配置

| 参数 | 类型 | 默认值 | 描述 |
|-----|------|--------|------|
| `position` | string | 'bottom-right' | 浮动图标位置 |
| `ui.colors.primary` | string | '#1890ff' | 主题色 |
| `ui.title` | string | '提交工单' | 弹窗标题 |
| `ui.description` | string | - | 弹窗描述 |
| `ui.logo` | string | - | 自定义Logo HTML |

### 位置选项

- `bottom-right` - 右下角（默认）
- `bottom-left` - 左下角
- `top-right` - 右上角
- `top-left` - 左上角
- `bottom-center` - 底部中央
- `top-center` - 顶部中央
- `center-right` - 右侧中央
- `center-left` - 左侧中央
- `center` - 屏幕中央

## API方法

### 控制方法

```javascript
// 显示工单弹窗
OpsSDK.show();

// 隐藏工单弹窗
OpsSDK.hide();

// 销毁SDK
OpsSDK.destroy();
```

### 数据方法

```javascript
// 获取工单列表
const response = await OpsSDK.getServices({
  page: 1,
  limit: 10,
  status: 'PENDING'
});

// 获取工单详情
const service = await OpsSDK.getService('service-id');

// 添加工单评论
await OpsSDK.addComment('service-id', '这是一个评论');
```

## 事件系统

SDK支持以下事件：

- `ready` - SDK初始化完成
- `service-created` - 工单创建成功
- `service-updated` - 工单状态更新
- `error` - 发生错误
- `close` - 弹窗关闭

```javascript
// 添加事件监听器
OpsSDK.on('service-created', (service) => {
  alert(`工单 ${service.ticketNumber} 创建成功！`);
});

// 移除事件监听器
OpsSDK.off('service-created', callback);
```

## 工单数据结构

```javascript
{
  id: "service-id",
  ticketNumber: "TK202412080001",
  title: "工单标题",
  description: "工单描述",
  category: "SUPPORT",
  priority: "MEDIUM",
  status: "PENDING",
  source: "EXTERNAL",
  externalSystemId: "your-system",
  externalUserId: "user-123",
  createdAt: "2024-12-08T10:30:00Z",
  archive: {
    id: "archive-id",
    name: "项目名称",
    customer: {
      name: "客户名称",
      code: "CUSTOMER001"
    }
  }
}
```

## 演示示例

运行演示：

```bash
cd sdk
npm install
npm run build
npm run serve
# 访问 http://localhost:3003
```

## 后端集成

### 1. 创建API Key

通过运维管理系统后台创建API Key：

```bash
POST /api/v1/api-keys
{
  "name": "演示系统",
  "systemId": "demo-system",
  "description": "演示系统集成"
}
```

### 2. 外部API端点

SDK使用以下API端点：

- `POST /api/external/services` - 创建工单
- `GET /api/external/services` - 获取工单列表
- `GET /api/external/services/:id` - 获取工单详情
- `POST /api/external/services/:id/comments` - 添加评论

### 3. API Key验证

所有请求需要在请求头中包含：

```
X-API-Key: your-api-key-here
```

## 自定义样式

```javascript
OpsSDK.init({
  // ... 其他配置
  ui: {
    colors: {
      primary: '#ff6b35',    // 主色调
      secondary: '#f7931e',  // 次要色调
      background: '#ffffff', // 背景色
      text: '#333333'        // 文字色
    },
    fonts: {
      family: 'PingFang SC, Microsoft YaHei',
      size: '14px'
    }
  }
});
```

## 错误处理

```javascript
OpsSDK.on('error', (error) => {
  switch(error.code) {
    case 'INVALID_API_KEY':
      console.log('API Key无效');
      break;
    case 'NETWORK_ERROR':
      console.log('网络连接失败');
      break;
    case 'RATE_LIMIT_EXCEEDED':
      console.log('请求频率超过限制');
      break;
    default:
      console.log('未知错误:', error.message);
  }
});
```

## 浏览器兼容性

- Chrome 60+
- Firefox 60+
- Safari 11+
- Edge 79+
- 移动端浏览器

## CORS跨域解决方案

SDK已经内置了CORS跨域支持，后端API自动为外部调用配置了宽松的CORS策略：

### 后端CORS配置
- 外部API路径 `/api/external/*` 允许任何域名访问
- 通过API Key验证确保安全性
- 支持的请求方法：GET, POST, PUT, DELETE, OPTIONS
- 允许的请求头：Content-Type, X-API-Key, Authorization

### 客户端处理
```javascript
// SDK会自动处理CORS错误
OpsSDK.on('error', (error) => {
  if (error.code === 'CORS_ERROR') {
    console.log('CORS配置错误，请联系管理员');
  }
});
```

### 常见CORS问题

1. **请求被阻止**: 确保API服务器运行在正确端口
2. **预检请求失败**: 检查API服务器是否支持OPTIONS请求
3. **API Key验证失败**: 确保在请求头中正确设置X-API-Key

### 生产环境建议

生产环境中，建议在CORS配置中指定具体的域名白名单：

```javascript
// 后端CORS配置示例
app.use('/api/external', cors({
  origin: [
    'https://your-website.com',
    'https://app.your-domain.com'
  ],
  credentials: false
}));
```

## 版本信息

当前版本: **1.0.0** (最小MVP)

获取版本信息：
```javascript
console.log(OpsSDK.version); // "1.0.0"
console.log(OpsSDK.isInitialized); // true/false
```

## 技术支持

如需技术支持，请联系运维管理系统团队或查看API文档。

## 更新日志

### v1.0.0 (2024-12-08)
- ✅ 初始版本发布
- ✅ 基础工单创建功能
- ✅ 浮动图标和弹窗UI
- ✅ API Key验证机制
- ✅ 事件系统支持
- ✅ 演示页面和文档