import { defineConfig } from "@rsbuild/core";
import { pluginReact } from "@rsbuild/plugin-react";
import { pluginTypeCheck } from "@rsbuild/plugin-type-check";
import { SemiRspackPlugin } from "@douyinfe/semi-rspack-plugin";
import { pluginSass } from "@rsbuild/plugin-sass";

const port = 3000; // 前端端口

export default defineConfig({
  plugins: [
    pluginReact(),
    pluginSass({ sassLoaderOptions: { quietDeps: true } }),
    pluginTypeCheck({
      forkTsChecker: {
        typescript: {
          memoryLimit: 4096,
        },
      },
    }),
  ],

  server: {
    port,
    host: "localhost",
    proxy: {
      "/api": {
        target: "http://localhost:3001", // 后端运行在3001端口
        changeOrigin: true,
        secure: false,
        logLevel: "debug",
        onProxyReq: (proxyReq, req, _res) => {
          console.log(
            `[Proxy] ${req.method} ${req.url} -> ${proxyReq.getHeader("host")}${proxyReq.path}`
          );
        },
        onError: (err, req, _res) => {
          console.error(`[Proxy Error] ${req.method} ${req.url}:`, err.message);
        },
      },
      "/ws": {
        target: "ws://localhost:3001",
        ws: true,
        changeOrigin: true,
        secure: false,
        logLevel: "debug",
        onProxyReq: (proxyReq, req, _res) => {
          console.log(
            `[WebSocket Proxy] ${req.method} ${req.url} -> ${proxyReq.getHeader("host")}${proxyReq.path}`
          );
        },
        onError: (err, req, _res) => {
          console.error(`[WebSocket Proxy Error] ${req.method} ${req.url}:`, err.message);
        },
      },
    },
  },

  dev: {
    assetPrefix: `http://localhost:${port}`,
    client: {
      port: `${port}`,
      host: "localhost",
      protocol: "ws",
    },
  },

  html: {
    title: "运维服务管理系统",
    template: "./src/assets/template.html",
    favicon: "./src/assets/images/logo.svg",
    crossorigin: "anonymous",
  },

  source: {
    entry: {
      index: "./src/index.tsx",
    },
    alias: {
      "@": "./src",
      "~": "./src",
    },
  },

  output: {
    cleanDistPath: true,
    polyfill: "entry",
    target: "web",
    distPath: {
      root: "dist",
      js: "static/js",
      css: "static/css",
      svg: "static/svg",
      font: "static/font",
      image: "static/image",
      media: "static/media",
    },
  },

  performance: {
    chunkSplit: {
      strategy: "split-by-experience",
      forceSplitting: ["react", "react-dom", "react-router-dom", "zustand"],
    },
    bundleAnalyze: process.env.BUNDLE_ANALYZE
      ? {
          analyzerMode: "server",
          openAnalyzer: true,
        }
      : undefined,
  },

  tools: {
    postcss: (config, { addPlugins }) => {
      addPlugins([require("tailwindcss/nesting"), require("tailwindcss"), require("autoprefixer")]);
    },
    rspack: (config, { appendPlugins }) => {
      appendPlugins([
        new SemiRspackPlugin({
          theme: "@douyinfe/semi-theme-default",
        }),
      ]);
      config.ignoreWarnings = [...(config.ignoreWarnings || []), /semi-foundation/];
      config.infrastructureLogging = { level: "verbose" };
      config.stats = "verbose";
      // 更强的源码调试体验
      config.devtool = "eval-cheap-module-source-map";
    },
  },
});
