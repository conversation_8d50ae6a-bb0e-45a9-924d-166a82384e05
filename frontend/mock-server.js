const express = require("express");
const cors = require("cors");

const app = express();
app.use(cors());
app.use(express.json());

// 模拟的API响应数据
const mockData = {
  serviceStats: {
    success: true,
    data: {
      totalServices: 156,
      recentServices: 23,
      avgResolutionTime: 4.2,
      statusDistribution: [
        { status: "PENDING", count: 23 },
        { status: "IN_PROGRESS", count: 42 },
        { status: "WAITING_CUSTOMER", count: 18 },
        { status: "RESOLVED", count: 58 },
        { status: "CLOSED", count: 15 },
      ],
      categoryDistribution: [
        { category: "MAINTENANCE", count: 45 },
        { category: "SUPPORT", count: 67 },
        { category: "UPGRADE", count: 28 },
        { category: "BUGFIX", count: 16 },
      ],
      priorityDistribution: [
        { priority: "LOW", count: 32 },
        { priority: "MEDIUM", count: 89 },
        { priority: "HIGH", count: 28 },
        { priority: "URGENT", count: 7 },
      ],
    },
    message: "Service stats retrieved successfully",
  },

  customerStats: {
    success: true,
    data: {
      pagination: {
        total: 127,
      },
    },
    message: "Customer data retrieved",
  },

  projectStats: {
    success: true,
    data: {
      pagination: {
        total: 45,
      },
    },
    message: "Project data retrieved",
  },

  systemStatus: {
    success: true,
    data: {
      status: "healthy",
      uptime: 99.9,
      timestamp: new Date().toISOString(),
    },
    message: "System status retrieved",
  },

  systemResources: {
    success: true,
    data: {
      cpu: { usage: 32.5, total: 100 },
      memory: { usage: 68.2, total: 100 },
      disk: { usage: 45.8, total: 100 },
    },
    message: "System resources retrieved",
  },

  databaseStatus: {
    success: true,
    data: {
      isConnected: true,
      responseTime: 15,
    },
    message: "Database status retrieved",
  },

  redisStatus: {
    success: true,
    data: {
      isConnected: true,
      responseTime: 8,
    },
    message: "Redis status retrieved",
  },
};

// API路由
app.get("/api/v1/services/stats", (req, res) => {
  setTimeout(() => {
    res.json(mockData.serviceStats);
  }, 200); // 模拟200ms延迟
});

app.get("/api/v1/customers", (req, res) => {
  setTimeout(() => {
    res.json(mockData.customerStats);
  }, 150);
});

app.get("/api/v1/archives", (req, res) => {
  setTimeout(() => {
    res.json(mockData.projectStats);
  }, 150);
});

app.get("/api/v1/system/status", (req, res) => {
  // 随机化系统状态以测试实时更新
  const statuses = ["healthy", "warning", "critical"];
  const randomStatus = statuses[Math.floor(Math.random() * statuses.length)];

  res.json({
    success: true,
    data: {
      status: randomStatus,
      uptime: 99.9,
      timestamp: new Date().toISOString(),
    },
    message: "System status retrieved",
  });
});

app.get("/api/v1/system/resources", (req, res) => {
  // 随机化资源使用率以测试实时更新
  res.json({
    success: true,
    data: {
      cpu: { usage: Math.random() * 100, total: 100 },
      memory: { usage: Math.random() * 100, total: 100 },
      disk: { usage: Math.random() * 100, total: 100 },
    },
    message: "System resources retrieved",
  });
});

app.get("/api/v1/system/database", (req, res) => {
  res.json(mockData.databaseStatus);
});

app.get("/api/v1/system/redis", (req, res) => {
  res.json(mockData.redisStatus);
});

// 404处理
app.use((req, res) => {
  res.status(404).json({
    success: false,
    message: `API endpoint not found: ${req.method} ${req.originalUrl}`,
    data: null,
  });
});

const PORT = 3002;
app.listen(PORT, () => {
  console.log(`🚀 Mock API server running on http://localhost:${PORT}`);
  console.log(`📊 Dashboard APIs available:`);
  console.log(`   GET http://localhost:${PORT}/api/v1/services/stats`);
  console.log(`   GET http://localhost:${PORT}/api/v1/system/status`);
  console.log(`   GET http://localhost:${PORT}/api/v1/system/resources`);
});
