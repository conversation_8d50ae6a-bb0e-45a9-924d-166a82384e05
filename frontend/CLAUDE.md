# 运维服务管理系统 - 前端项目文档

## 项目概述

基于 React + TypeScript 的运维服务管理系统前端，采用 Rsbuild 构建工具，提供现代化的用户界面和优秀的用户体验。

## 技术栈约束

### 核心框架

- **前端框架**: React 18 + TypeScript
- **构建工具**: Rsbuild (基于 Rspack)
- **路由**: React Router v6
- **包管理器**: npm

### UI 框架 - **重要约束**

- **UI 组件库**: @douyinfe/semi-ui (Semi Design)
- **图标库**: @douyinfe/semi-icons
- **样式方案**: Semi Design + Tailwind CSS
- **禁止使用**: Ant Design、Element UI、其他组件库

### 状态管理

- **全局状态**: Zustand
- **服务端状态**: TanStack Query (React Query)
- **本地状态**: React Hooks (useState, useReducer)

### 路由和导航

- **路由**: React Router v6
- **路由配置**: 集中式路由配置
- **权限路由**: 基于组件的路由守卫 (AuthGuard, PermissionGuard)

### 数据处理

- **HTTP 客户端**: 自定义 request 工具 (基于 fetch)
- **表单验证**: 内置验证 + Semi Design 表单验证
- **日期处理**: dayjs
- **数据格式化**: 自定义工具函数

### 开发规范

#### 组件开发规范

```typescript
// 使用 Semi Design 组件
import { Button, Table, Modal } from "@douyinfe/semi-ui";
import { IconPlus, IconDelete } from "@douyinfe/semi-icons";

// 组件命名：PascalCase
const UserManagePage: React.FC = () => {
  // ...
};

export default UserManagePage;

// 自定义 Hook 命名：camelCase，以 use 开头
export const useUserManagement = () => {
  // ...
};
```

#### 样式开发规范

#### 常见错误用法
正确用法：`<Row gutter={16}>`
错误用法：`<Row gutter={[16, 16]}>`

```tsx
const ComponentExample: React.FC = () => {
  return (
    <div>
      {/* 优先使用 Semi Design 组件的内置样式 */}
      <Button theme="solid" type="primary">主要按钮</Button>

      {/* 使用 Tailwind CSS 进行布局和间距 */}
      <div className="flex flex-col space-y-4 p-6">
        {/* 内容 */}
      </div>
    </div>
  );
};

/* 全局样式文件 (src/assets/styles/index.css) */
.custom-component {
  /* 仅在必要时使用自定义样式 */
}
```

#### TypeScript 规范

```typescript
// 严格类型定义
interface User {
  id: string;
  username: string;
  email: string;
  fullName: string;
  role: Role;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

// API 响应类型
interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
}

// 组件 Props 类型
interface UserFormProps {
  user?: User | null;
  onSuccess: () => void;
  onCancel: () => void;
}
```

### 代码格式化

prettier 配置文件在 .prettierrc.json 文件中，请遵循该配置文件进行代码格式化。

### 代码规范

请遵循 eslint 配置文件 .eslintrc.json 中的规则进行代码规范。

### 项目结构

```
frontend/
├── src/
│   ├── assets/              # 静态资源
│   │   ├── images/          # 图片资源
│   │   ├── styles/          # 全局样式
│   │   └── template.html    # HTML 模板
│   ├── components/          # React 组件
│   │   ├── common/          # 通用组件
│   │   ├── admin/           # 管理后台组件
│   │   ├── dashboard/       # 仪表板组件
│   │   ├── guards/          # 路由守卫组件
│   │   ├── layout/          # 布局组件
│   │   └── services/        # 服务工单组件
│   ├── hooks/               # 自定义 React Hooks
│   ├── pages/               # 页面组件
│   │   ├── auth/            # 认证相关页面
│   │   ├── customers/       # 客户管理
│   │   ├── projects/        # 项目档案
│   │   ├── services/        # 服务工单
│   │   └── admin/           # 系统管理
│   ├── routes/              # 路由配置
│   ├── services/            # API 服务层
│   ├── stores/              # Zustand 状态管理
│   ├── types/               # TypeScript 类型定义
│   ├── utils/               # 工具函数
│   ├── App.tsx              # 根组件
│   └── index.tsx            # 应用入口
├── rsbuild.config.ts        # Rsbuild 配置
├── tailwind.config.js       # Tailwind 配置
└── tsconfig.json            # TypeScript 配置
```

### API 服务层规范

```typescript
// services/user.ts
import { get, post, put, del } from "@/utils/request";
import type { User, CreateUserData, UpdateUserData } from "@/types";

class UserService {
  async getUsers(params?: UserListParams): Promise<UserListResponse> {
    return get("/api/v1/admin/users", { params });
  }

  async createUser(data: CreateUserData): Promise<ApiResponse<User>> {
    return post("/api/v1/admin/users", data);
  }

  // ... 其他方法
}

export const userService = new UserService();
```

### 组件开发最佳实践

#### 1. Semi Design 组件使用

```tsx
const UserManagePage: React.FC = () => {
  const [visible, setVisible] = useState(false);

  return (
    <div>
      {/* 表格 */}
      <Table
        dataSource={users}
        columns={columns}
        loading={loading}
        pagination={paginationConfig}
        rowKey="id"
      />

      {/* 表单 */}
      <Form getFormApi={api => setFormApi(api)} labelPosition="top">
        <Form.Input field="username" label="用户名" placeholder="请输入用户名" />
      </Form>

      {/* 弹窗 */}
      <Modal
        visible={visible}
        title="编辑用户"
        footer={null}
        width="600px"
        onCancel={() => setVisible(false)}
      >
        <UserForm onSuccess={handleSuccess} onCancel={handleCancel} />
      </Modal>
    </div>
  );
};
```

#### 2. 状态管理

```typescript
// stores/user.ts (Zustand)
interface UserStore {
  users: User[];
  loading: boolean;
  fetchUsers: (params?: UserListParams) => Promise<void>;
}

export const useUserStore = create<UserStore>((set, get) => ({
  users: [],
  loading: false,

  fetchUsers: async (params?: UserListParams) => {
    set({ loading: true });
    try {
      const response = await userService.getUsers(params);
      set({ users: response.data.users });
    } finally {
      set({ loading: false });
    }
  },
}));
```

#### 3. 自定义 Hook

```typescript
// hooks/usePermissions.ts
export const usePermissions = () => {
  const { user } = useAuthStore();

  const hasPermission = useCallback(
    (permission: string): boolean => {
      return user?.permissions?.includes(permission) || false;
    },
    [user]
  );

  const hasRole = useCallback(
    (role: string): boolean => {
      return user?.role?.name === role;
    },
    [user]
  );

  return {
    hasPermission,
    hasRole,
  };
};
```

### 性能优化规范

1. **懒加载**: 使用 `React.lazy` 进行组件懒加载
2. **虚拟滚动**: 大数据量列表使用 Semi Design 的虚拟滚动
3. **缓存策略**: 合理使用 TanStack Query 的缓存机制
4. **打包优化**: 使用 Rsbuild 的 `chunkSplit` 进行代码分割

### 开发命令

```bash
# 开发环境
npm run dev

# 构建生产版本
npm run build

# 预览生产版本
npm run preview

# 代码检查
npm run lint

# 类型检查
npm run type-check

# 格式化代码
npm run format

# 打包分析
npm run analyze

# 运行测试
npm run test
```

### 环境配置

Rsbuild 支持通过 `.env` 文件配置环境变量，变量名需要以 `PUBLIC_` 开头才能在客户端使用：

```bash
# .env.local
PUBLIC_API_BASE_URL=http://localhost:3001
PUBLIC_APP_NAME=运维服务管理系统
```

### 重要约束提醒

1. **严禁使用 Ant Design**: 项目已统一使用 Semi Design，不得混用其他UI框架
2. **TypeScript 严格模式**: 必须保持严格的类型检查
3. **组件复用**: 优先复用现有组件，避免重复开发
4. **性能优先**: 关注首屏加载速度和用户体验
5. **移动端适配**: 所有页面必须支持响应式设计

### 常用 Semi Design 组件映射

| 功能     | Semi Design 组件      | 说明               |
| -------- | --------------------- | ------------------ |
| 按钮     | Button                | 支持多种主题和大小 |
| 表格     | Table                 | 功能完整的数据表格 |
| 表单     | Form, Form.Item       | 表单组件套件       |
| 输入框   | Input, Input.TextArea | 文本输入组件       |
| 选择器   | Select, Cascader      | 下拉选择组件       |
| 日期选择 | DatePicker            | 日期时间选择       |
| 弹窗     | Modal                 | 对话框组件         |
| 消息通知 | Toast, Notification   | 消息提示组件       |
| 导航     | Navigation            | 导航菜单组件       |
| 布局     | Layout                | 页面布局组件       |
| 加载     | Spin                  | 加载指示器         |
| 分页     | Pagination            | 分页组件           |
| 上传     | Upload                | 文件上传组件       |

### 代码质量保证

- **ESLint**: 代码质量检查
- **Prettier**: 代码格式化
- **TypeScript**: 类型安全
- **Husky**: Git 钩子
- **Commitlint**: 提交消息规范

遵循以上约束和规范，确保项目的一致性和可维护性。
