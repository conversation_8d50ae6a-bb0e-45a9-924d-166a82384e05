/** @type {import('tailwindcss').Config} */
module.exports = {
  content: ["./src/**/*.{js,ts,jsx,tsx}", "./src/assets/template.html"],
  darkMode: "class",
  corePlugins: {
    preflight: false,
  },
  // theme: {
  //   extend: {
  //     colors: {
  //       primary: {
  //         50: 'var(--semi-color-primary-0)',
  //         100: 'var(--semi-color-primary-1)',
  //         200: 'var(--semi-color-primary-2)',
  //         300: 'var(--semi-color-primary-3)',
  //         400: 'var(--semi-color-primary-4)',
  //         500: 'var(--semi-color-primary-5)',
  //         600: 'var(--semi-color-primary-6)',
  //         700: 'var(--semi-color-primary-7)',
  //         800: 'var(--semi-color-primary-8)',
  //         900: 'var(--semi-color-primary-9)',
  //       },
  //       // Brand colors
  //       brand: {
  //         primary: 'var(--ops-brand-primary)',
  //         secondary: 'var(--ops-brand-secondary)',
  //       },

  //       // Functional colors
  //       success: 'var(--ops-success)',
  //       warning: 'var(--ops-warning)',
  //       error: 'var(--ops-error)',
  //       info: 'var(--ops-info)',

  //       // Foreground colors (text)
  //       'ops-fg': {
  //         'hglt-plus': 'var(--ops-fg-hglt-plus)',
  //         hglt: 'var(--ops-fg-hglt)',
  //         plus: 'var(--ops-fg-plus)',
  //         primary: 'var(--ops-fg-primary)',
  //         secondary: 'var(--ops-fg-secondary)',
  //         dim: 'var(--ops-fg-dim)',
  //       },

  //       // Background colors
  //       'ops-bg': {
  //         max: 'var(--ops-bg-max)',
  //         plus: 'var(--ops-bg-plus)',
  //         primary: 'var(--ops-bg-primary)',
  //         secondary: 'var(--ops-bg-secondary)',
  //       },

  //       // Middleground colors
  //       'ops-mg': {
  //         'hglt-plus': 'var(--ops-mg-hglt-plus)',
  //         primary: 'var(--ops-mg-primary)',
  //         card: 'var(--ops-mg-card)',
  //       },

  //       // Border colors
  //       'ops-border': {
  //         primary: 'var(--ops-border-primary)',
  //         secondary: 'var(--ops-border-secondary)',
  //         focus: 'var(--ops-border-focus)',
  //       },
  //     },
  //     fontFamily: {
  //       sans: ['Inter', 'ui-sans-serif', 'system-ui', '-apple-system', 'sans-serif'],
  //       mono: ['JetBrains Mono', 'ui-monospace', 'Monaco', 'Cascadia Code', 'monospace'],
  //     },
  //     spacing: {
  //       18: '4.5rem',
  //       88: '22rem',
  //     },
  //     maxWidth: {
  //       '8xl': '88rem',
  //       '9xl': '96rem',
  //     },
  //     animation: {
  //       'fade-in': 'fadeIn 0.2s ease-in-out',
  //       'fade-out': 'fadeOut 0.2s ease-in-out',
  //       'slide-in': 'slideIn 0.3s ease-out',
  //       'slide-out': 'slideOut 0.3s ease-out',
  //       'bounce-subtle': 'bounceSubtle 0.6s ease-in-out',
  //     },
  //     keyframes: {
  //       fadeIn: {
  //         '0%': { opacity: '0' },
  //         '100%': { opacity: '1' },
  //       },
  //       fadeOut: {
  //         '0%': { opacity: '1' },
  //         '100%': { opacity: '0' },
  //       },
  //       slideIn: {
  //         '0%': { transform: 'translateX(-100%)' },
  //         '100%': { transform: 'translateX(0)' },
  //       },
  //       slideOut: {
  //         '0%': { transform: 'translateX(0)' },
  //         '100%': { transform: 'translateX(-100%)' },
  //       },
  //       bounceSubtle: {
  //         '0%, 20%, 53%, 80%, 100%': { transform: 'translate3d(0,0,0)' },
  //         '40%, 43%': { transform: 'translate3d(0, -8px, 0)' },
  //         '70%': { transform: 'translate3d(0, -4px, 0)' },
  //         '90%': { transform: 'translate3d(0, -2px, 0)' },
  //       },
  //     },
  //     boxShadow: {
  //       soft: '0 2px 15px -3px rgba(0, 0, 0, 0.07), 0 10px 20px -2px rgba(0, 0, 0, 0.04)',
  //       medium: '0 4px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 30px -5px rgba(0, 0, 0, 0.05)',
  //       strong: '0 10px 40px -10px rgba(0, 0, 0, 0.15), 0 20px 50px -10px rgba(0, 0, 0, 0.1)',
  //     },
  //   },
  // },
  plugins: [],
};
