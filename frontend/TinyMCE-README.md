# TinyMCE 本地安装说明

本项目使用本地部署的 TinyMCE 富文本编辑器，避免了 API Key 验证问题。

## 安装 TinyMCE

### 自动安装（推荐）

运行安装脚本：

```bash
cd frontend
./scripts/install-tinymce.sh
```

### 手动安装

1. 创建 public 目录：

```bash
mkdir -p frontend/public
```

2. 下载 TinyMCE 社区版：

```bash
cd frontend/public
curl -L -o tinymce.zip "https://download.tiny.cloud/tinymce/community/tinymce_7.6.0.zip"
```

3. 解压并清理：

```bash
unzip tinymce.zip
rm tinymce.zip
```

## 目录结构

安装完成后，目录结构如下：

```
frontend/
├── public/
│   └── tinymce/
│       └── js/
│           └── tinymce/
│               ├── tinymce.min.js  # 主文件
│               ├── plugins/        # 插件目录
│               ├── skins/          # 皮肤目录
│               ├── themes/         # 主题目录
│               └── ...
└── ...
```

## 验证安装

启动开发服务器后，访问任何包含富文本编辑器的页面，应该能看到 TinyMCE 编辑器正常加载，不再显示 API Key 错误。

## 注意事项

1. **版本控制**：`frontend/public/tinymce/` 目录已添加到 `.gitignore`，不会提交到版本控制
2. **团队协作**：新的团队成员需要运行安装脚本来获取 TinyMCE 文件
3. **部署**：生产环境部署时需要确保 TinyMCE 文件正确部署到 public 目录

## 配置

富文本编辑器组件位于：`src/components/common/RichTextEditor.tsx`

关键配置：

- `tinymceScriptSrc="/tinymce/js/tinymce/tinymce.min.js"` - 指向本地 TinyMCE 文件
- 无需 API Key 或许可证配置
- 支持的插件：advlist, autolink, lists, link, charmap, preview, anchor, searchreplace, visualblocks, code, fullscreen, insertdatetime, table, help, wordcount

## 许可证

TinyMCE 社区版采用 LGPL 许可证，可免费用于开源和商业项目。
