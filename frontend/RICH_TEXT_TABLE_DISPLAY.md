# 表格中富文本内容显示方案

## 功能概述

`SimpleRichTextDisplay` 组件专为表格和列表场景设计，提供两种显示模式：

1. **简单模式**：直接显示截断的纯文本
2. **Modal 模式**：显示固定长度 + "更多"按钮，点击打开 Modal 查看完整富文本

## 使用方式

### 基本用法（简单模式）

```tsx
import { SimpleRichTextDisplay } from "@/components/common";

<SimpleRichTextDisplay content={htmlContent} maxLength={100} placeholder="暂无内容" />;
```

### Modal 模式（推荐用于表格）

```tsx
import { SimpleRichTextDisplay } from "@/components/common";

<SimpleRichTextDisplay
  content={htmlContent}
  maxLength={80}
  showMoreModal={true}
  modalTitle="详细内容"
  placeholder="暂无内容"
/>;
```

## 属性说明

| 属性            | 类型                  | 默认值       | 说明                        |
| --------------- | --------------------- | ------------ | --------------------------- |
| `content`       | `string`              | -            | HTML 富文本内容             |
| `maxLength`     | `number`              | `100`        | 最大显示字符数              |
| `showMoreModal` | `boolean`             | `false`      | 是否启用 Modal 查看完整内容 |
| `modalTitle`    | `string`              | `'详细内容'` | Modal 标题                  |
| `placeholder`   | `string`              | `'暂无内容'` | 空内容占位符                |
| `className`     | `string`              | -            | 自定义样式类名              |
| `style`         | `React.CSSProperties` | -            | 自定义样式                  |

## 显示效果

### 简单模式

```
这是一段很长的富文本内容，包含各种格式...
```

### Modal 模式

```
这是一段很长的富文本内容，包含各种格式... [⋯]
```

点击 `[⋯]` 按钮后，会打开 Modal 显示完整的富文本内容（使用 `RichTextDisplay` 组件）。

## 安全特性

- ✅ **表格预览**：将 HTML 转换为纯文本，移除所有标签
- ✅ **Modal 详情**：使用 `RichTextDisplay` 安全渲染完整 HTML
- ✅ **HTML 实体解码**：正确处理 `&lt;`, `&gt;`, `&nbsp;` 等
- ✅ **XSS 防护**：预览模式无 XSS 风险，详情模式使用 TinyMCE 安全引擎

## 当前应用场景

### 1. 工作日志表格

- **位置**：`ServiceDetailSideSheet.tsx` - 工作日志 Tab
- **配置**：`maxLength={80}`, `showMoreModal={true}`
- **效果**：显示工作内容前 80 个字符，点击查看完整内容

### 2. 评论时间线

- **位置**：`ServiceDetailSideSheet.tsx` - 评论 Tab
- **配置**：`maxLength={150}`, `showMoreModal={true}`
- **效果**：显示评论前 150 个字符，点击查看完整内容

## 性能优势

| 特性     | 表格预览          | Modal 详情                  |
| -------- | ----------------- | --------------------------- |
| 渲染速度 | ✅ 极快（纯文本） | ⚠️ 中等（按需加载 TinyMCE） |
| 内存占用 | ✅ 最小           | ⚠️ 中等（仅打开时）         |
| 首屏加载 | ✅ 无影响         | ✅ 无影响（懒加载）         |
| 用户体验 | ✅ 即时显示       | ✅ 按需查看完整内容         |

## 最佳实践

1. **表格列表**：使用 Modal 模式，`maxLength` 设置为 60-100
2. **卡片预览**：使用 Modal 模式，`maxLength` 设置为 100-200
3. **简单场景**：使用简单模式，直接显示截断文本
4. **移动端**：减少 `maxLength`，优化显示效果

## 示例代码

### 工作日志表格列

```tsx
{
  title: '工作内容',
  dataIndex: 'description',
  key: 'description',
  render: (text: string) => (
    <SimpleRichTextDisplay
      content={text}
      placeholder="暂无内容"
      maxLength={80}
      showMoreModal={true}
      modalTitle="工作内容详情"
    />
  ),
}
```

### 评论列表项

```tsx
<SimpleRichTextDisplay
  content={comment.content}
  placeholder="暂无内容"
  maxLength={150}
  showMoreModal={true}
  modalTitle="评论详情"
/>
```

这样的设计既保证了表格的性能和美观，又提供了查看完整内容的便捷方式。
