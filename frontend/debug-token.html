<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Token调试工具</title>
    <style>
      body {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif;
        margin: 20px;
        background: #f5f5f5;
      }
      .container {
        max-width: 800px;
        margin: 0 auto;
        background: white;
        padding: 30px;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }
      .token-info {
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 4px;
        padding: 15px;
        margin: 15px 0;
      }
      .error {
        background: #fff5f5;
        border-color: #fed7d7;
        color: #c53030;
      }
      .success {
        background: #f0fff4;
        border-color: #9ae6b4;
        color: #2d7d32;
      }
      button {
        padding: 10px 20px;
        background: #1890ff;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        margin-right: 10px;
        margin-bottom: 10px;
      }
      button:hover {
        background: #40a9ff;
      }
      pre {
        background: #2d3748;
        color: #e2e8f0;
        padding: 15px;
        border-radius: 4px;
        overflow-x: auto;
        white-space: pre-wrap;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h1>🔍 Token调试工具</h1>

      <div class="section">
        <h3>检查当前认证状态</h3>
        <button onclick="checkToken()">检查Token</button>
        <button onclick="checkAuthStore()">检查AuthStore</button>
        <button onclick="testRequest()">测试认证请求</button>
        <button onclick="clearStorage()">清除存储</button>
      </div>

      <div id="result"></div>
    </div>

    <script>
      function log(message, type = "info") {
        const resultDiv = document.getElementById("result");
        const className = type === "error" ? "error" : type === "success" ? "success" : "";

        const logEntry = `
                <div class="token-info ${className}">
                    <strong>[${new Date().toLocaleTimeString()}]</strong> ${message}
                </div>
            `;

        resultDiv.innerHTML += logEntry;
        resultDiv.scrollTop = resultDiv.scrollHeight;
      }

      function checkToken() {
        log("🔍 开始检查Token...", "info");

        try {
          // 检查localStorage
          const authStorage = localStorage.getItem("auth-storage");
          if (authStorage) {
            const parsed = JSON.parse(authStorage);
            const token = parsed.state?.token;

            if (token) {
              log(`✅ 找到localStorage中的token: ${token.substring(0, 20)}...`, "success");

              // 解析JWT payload
              try {
                const payload = JSON.parse(atob(token.split(".")[1]));
                log(`📊 Token信息: <pre>${JSON.stringify(payload, null, 2)}</pre>`, "info");

                // 检查过期时间
                const now = Math.floor(Date.now() / 1000);
                if (payload.exp < now) {
                  log("⚠️ Token已过期！", "error");
                } else {
                  const remaining = payload.exp - now;
                  log(`✅ Token有效，剩余 ${Math.floor(remaining / 60)} 分钟`, "success");
                }
              } catch (e) {
                log(`❌ 无法解析Token payload: ${e.message}`, "error");
              }
            } else {
              log("❌ localStorage中没有找到token", "error");
            }

            log(`📄 完整auth-storage内容: <pre>${JSON.stringify(parsed, null, 2)}</pre>`, "info");
          } else {
            log("❌ localStorage中没有auth-storage", "error");
          }

          // 检查内存store
          if (window.__AUTH_STORE__) {
            const state = window.__AUTH_STORE__.getState();
            log(`💾 内存AuthStore状态: <pre>${JSON.stringify(state, null, 2)}</pre>`, "info");
          } else {
            log("❌ 内存中没有__AUTH_STORE__", "error");
          }
        } catch (error) {
          log(`❌ 检查Token时出错: ${error.message}`, "error");
        }
      }

      function checkAuthStore() {
        log("🔍 检查AuthStore状态...", "info");

        if (typeof window !== "undefined") {
          // 检查Zustand store
          Object.keys(localStorage).forEach(key => {
            if (key.includes("auth")) {
              const value = localStorage.getItem(key);
              log(`🗝️ 存储键 "${key}": <pre>${value}</pre>`, "info");
            }
          });
        }
      }

      async function testRequest() {
        log("🌐 测试认证请求...", "info");

        try {
          // 获取token
          const authStorage = localStorage.getItem("auth-storage");
          let token = null;

          if (authStorage) {
            const parsed = JSON.parse(authStorage);
            token = parsed.state?.token;
          }

          if (!token) {
            log("❌ 没有找到token，无法测试请求", "error");
            return;
          }

          // 测试请求
          const response = await fetch("http://localhost:3001/api/v1/auth/me", {
            method: "GET",
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
            },
          });

          const data = await response.json();

          if (response.ok) {
            log(`✅ 认证请求成功: <pre>${JSON.stringify(data, null, 2)}</pre>`, "success");
          } else {
            log(
              `❌ 认证请求失败 (${response.status}): <pre>${JSON.stringify(data, null, 2)}</pre>`,
              "error"
            );
          }
        } catch (error) {
          log(`❌ 测试请求时出错: ${error.message}`, "error");
        }
      }

      function clearStorage() {
        log("🗑️ 清除所有认证存储...", "info");

        // 清除localStorage
        Object.keys(localStorage).forEach(key => {
          if (key.includes("auth")) {
            localStorage.removeItem(key);
            log(`✅ 已清除: ${key}`, "success");
          }
        });

        // 清除内存store
        if (window.__AUTH_STORE__) {
          delete window.__AUTH_STORE__;
          log("✅ 已清除内存AuthStore", "success");
        }

        log("🔄 请重新登录", "info");
      }

      // 页面加载时自动检查
      document.addEventListener("DOMContentLoaded", () => {
        log("🚀 Token调试工具已加载", "info");
        checkToken();
      });
    </script>
  </body>
</html>
