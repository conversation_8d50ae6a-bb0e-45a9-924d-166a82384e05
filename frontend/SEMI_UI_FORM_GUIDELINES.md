# Semi-UI 表单组件使用规范

## 📋 概述

本文档规范了项目中Semi-UI Form组件的正确使用方法，避免实例化过深和性能问题。

## ⚠️ 常见问题与解决方案

### 1. 实例化过深问题

显式指定 Form 组件的泛型类型：将 <Form> 改为 <Form<CustomerFormData>>
更新 formRef 的类型：将 useRef<Form>(null) 改为 useRef<Form<CustomerFormData>>(null)
这样可以避免 TypeScript 进行过深的类型推断，从而解决"类型实例化过深，且可能无限"的错误。

### 2. 表单重置问题

**❌ 错误用法：**

```jsx
// 手动管理表单状态
const resetForm = () => {
  setFormData({
    name: "",
    email: "",
  });
};
```

**✅ 正确用法：**

```jsx
// 使用Form的formApi重置表单
const handleReset = () => {
  formRef.current?.formApi.setValues(DEFAULT_FORM_VALUES);
};

// 或清空所有字段
const handleClear = () => {
  formRef.current?.formApi.setValues({});
};
```

### 3. 表单验证规范

**✅ 推荐做法：**

```jsx
<Form.Input
  field='email'
  label='邮箱'
  rules={[
    { required: true, message: '请输入邮箱' },
    { type: 'email', message: '请输入有效的邮箱地址' }
  ]}
/>

<Form.Select
  field='type'
  label='类型'
  optionList={TYPE_OPTIONS}  // 使用常量
  rules={[{ required: true, message: '请选择类型' }]}
/>
```

## 🛠️ 最佳实践

### 1. 表单配置

```jsx
// ✅ 推荐的Form配置
<Form
  ref={formRef}
  layout="vertical" // 垂直布局，适合大部分场景
  onSubmit={handleSubmit} // 提交处理
  initValues={DEFAULT_VALUES} // 使用常量初始值
  style={{ padding: 0 }} // 根据需要设置样式
/>
```

### 2. 选项数据管理

```jsx
// ✅ 在组件外部定义选项常量
const CUSTOMER_TYPE_OPTIONS = [
  { label: "企业客户", value: "ENTERPRISE" },
  { label: "个人客户", value: "INDIVIDUAL" },
  { label: "政府机构", value: "GOVERNMENT" },
];

// ✅ 在组件中使用
<Form.Select field="type" optionList={CUSTOMER_TYPE_OPTIONS} />;
```

### 3. 回调函数优化

```jsx
// ✅ 使用useCallback优化性能
const handleSubmit = useCallback(
  async values => {
    setLoading(true);
    try {
      const response = await apiCall(values);
      if (response.success) {
        Toast.success("操作成功");
        formRef.current?.formApi.setValues(DEFAULT_VALUES);
        onSuccess?.();
      }
    } catch (error) {
      Toast.error(error.message);
    } finally {
      setLoading(false);
    }
  },
  [onSuccess]
);
```

### 4. 表单布局

```jsx
// ✅ 推荐的布局结构
<Form ref={formRef} layout="vertical">
  {/* 分组信息 */}
  <div style={{ marginBottom: "24px" }}>
    <Title heading={5}>基本信息</Title>
    <Row gutter={[16, 16]}>
      <Col span={12}>
        <Form.Input field="name" label="名称" />
      </Col>
      <Col span={12}>
        <Form.Input field="code" label="编码" />
      </Col>
    </Row>
  </div>

  <Divider />

  {/* 其他分组 */}
  <div style={{ marginBottom: "24px" }}>
    <Title heading={5}>联系信息</Title>
    <Form.Input field="phone" label="电话" />
    <Form.Input field="email" label="邮箱" />
  </div>

  {/* 操作按钮 */}
  <div
    style={{
      display: "flex",
      justifyContent: "flex-end",
      gap: "12px",
      marginTop: "32px",
      paddingTop: "24px",
      borderTop: "1px solid var(--semi-color-border)",
    }}
  >
    <Button onClick={onCancel}>取消</Button>
    <Button htmlType="submit" type="primary" loading={loading}>
      提交
    </Button>
  </div>
</Form>
```

## 🚫 禁用模式

### 不要同时使用的配置

```jsx
// ❌ 不要同时使用
<Form
  labelPosition='left'
  layout='vertical'  // 冲突：垂直布局不需要labelPosition
/>

// ❌ 不要在render中创建对象
<Form
  initValues={{
    name: '',    // 每次render都会创建新对象
    email: ''
  }}
/>

// ❌ 不要混用状态管理
const [formData, setFormData] = useState({});
<Form
  initValues={formData}  // 与Form内部状态管理冲突
  onValuesChange={setFormData}  // 造成循环更新
/>
```

## 📊 性能优化

### 1. 避免不必要的重渲染

```jsx
// ✅ 使用React.memo包装表单组件
export const CustomerCreateModal = React.memo(({ visible, onCancel, onSuccess }) => {
  // 组件逻辑
});

// ✅ 使用useCallback缓存回调函数
const handleSubmit = useCallback(
  values => {
    // 提交逻辑
  },
  [dependencies]
);
```

### 2. 大型表单优化

```jsx
// ✅ 大型表单分步骤或分组件
const FormStep1 = React.memo(() => (
  <div>
    <Form.Input field="name" />
    <Form.Input field="code" />
  </div>
));

const FormStep2 = React.memo(() => (
  <div>
    <Form.Input field="phone" />
    <Form.Input field="email" />
  </div>
));
```

## 🔍 调试技巧

### 1. 开发环境检查

```jsx
// ✅ 添加调试信息
useEffect(() => {
  if (process.env.NODE_ENV === "development") {
    console.log("Form render count:", ++renderCount.current);
  }
}, []);
```

### 2. 表单状态监控

```jsx
// ✅ 监控表单值变化
<Form
  onValuesChange={(values, changedValues) => {
    console.log("Form values changed:", changedValues);
  }}
/>
```

## 📝 代码检查清单

在提交代码前，请检查以下项目：

- [ ] 是否使用了formRef而不是状态管理
- [ ] initValues是否使用常量而不是每次创建新对象
- [ ] 是否避免了labelPosition和layout的冲突
- [ ] 回调函数是否使用了useCallback优化
- [ ] 选项数据是否定义为常量
- [ ] 表单重置是否使用formRef.current?.formApi.setValues()
- [ ] 是否避免了不必要的依赖项导致重渲染

## 🎯 示例模板

```jsx
import React, { useState, useRef, useCallback } from "react";
import { Modal, Form, Button, Toast } from "@douyinfe/semi-ui";

// 常量定义
const DEFAULT_VALUES = {
  name: "",
  email: "",
  type: "default",
};

const TYPE_OPTIONS = [
  { label: "类型1", value: "type1" },
  { label: "类型2", value: "type2" },
];

// 表单组件
export const MyFormModal = React.memo(({ visible, onCancel, onSuccess }) => {
  const [loading, setLoading] = useState(false);
  const formRef = useRef < Form > null;

  const handleSubmit = useCallback(
    async values => {
      setLoading(true);
      try {
        // API调用
        const response = await submitData(values);
        if (response.success) {
          Toast.success("操作成功");
          formRef.current?.reset();
          onSuccess?.();
        }
      } catch (error) {
        Toast.error(error.message);
      } finally {
        setLoading(false);
      }
    },
    [onSuccess]
  );

  const handleCancel = useCallback(() => {
    formRef.current?.formApi.setValues(DEFAULT_VALUES);
    onCancel?.();
  }, [onCancel]);

  return (
    <Modal visible={visible} onCancel={handleCancel} footer={null}>
      <Form ref={formRef} layout="vertical" initValues={DEFAULT_VALUES} onSubmit={handleSubmit}>
        <Form.Input field="name" label="名称" required />
        <Form.Input field="email" label="邮箱" type="email" required />
        <Form.Select field="type" label="类型" optionList={TYPE_OPTIONS} required />

        <div style={{ display: "flex", justifyContent: "flex-end", gap: "12px" }}>
          <Button onClick={handleCancel}>取消</Button>
          <Button htmlType="submit" type="primary" loading={loading}>
            提交
          </Button>
        </div>
      </Form>
    </Modal>
  );
});
```

---

**注意**: 遵循这些规范可以避免99%的Semi-UI Form相关问题，提升应用性能和用户体验。
