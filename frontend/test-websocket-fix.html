<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>WebSocket 连接修复测试</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        max-width: 800px;
        margin: 0 auto;
        padding: 20px;
      }
      .status {
        padding: 10px;
        margin: 10px 0;
        border-radius: 5px;
        font-weight: bold;
      }
      .connected {
        background-color: #d4edda;
        color: #155724;
      }
      .disconnected {
        background-color: #f8d7da;
        color: #721c24;
      }
      .connecting {
        background-color: #fff3cd;
        color: #856404;
      }
      .error {
        background-color: #f8d7da;
        color: #721c24;
      }
      .log {
        background-color: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 5px;
        padding: 10px;
        height: 300px;
        overflow-y: auto;
        font-family: monospace;
        font-size: 12px;
      }
      .log-entry {
        margin: 2px 0;
        padding: 2px 0;
      }
      .log-info {
        color: #0066cc;
      }
      .log-success {
        color: #28a745;
      }
      .log-error {
        color: #dc3545;
      }
      .log-warning {
        color: #ffc107;
      }
      button {
        padding: 10px 20px;
        margin: 5px;
        border: none;
        border-radius: 5px;
        cursor: pointer;
        background-color: #007bff;
        color: white;
      }
      button:hover {
        background-color: #0056b3;
      }
      button:disabled {
        background-color: #6c757d;
        cursor: not-allowed;
      }
    </style>
  </head>
  <body>
    <h1>WebSocket 连接修复测试</h1>

    <div id="status" class="status disconnected">未连接</div>

    <div>
      <button id="connectBtn" onclick="connectWebSocket()">连接 WebSocket</button>
      <button id="disconnectBtn" onclick="disconnectWebSocket()" disabled>断开连接</button>
      <button id="testBtn" onclick="testMessage()" disabled>发送测试消息</button>
      <button id="clearBtn" onclick="clearLog()">清空日志</button>
    </div>

    <h3>连接信息</h3>
    <div>
      <strong>连接URL:</strong> <span id="wsUrl">ws://localhost:3000/ws</span><br />
      <strong>Token:</strong> <span id="tokenInfo">未获取</span><br />
      <strong>连接状态:</strong> <span id="connectionState">未连接</span>
    </div>

    <h3>日志</h3>
    <div id="log" class="log"></div>

    <script>
      let ws = null;
      let reconnectAttempts = 0;
      const maxReconnectAttempts = 3;

      function log(message, type = "info") {
        const logDiv = document.getElementById("log");
        const entry = document.createElement("div");
        entry.className = `log-entry log-${type}`;
        entry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
        logDiv.appendChild(entry);
        logDiv.scrollTop = logDiv.scrollHeight;
      }

      function clearLog() {
        document.getElementById("log").innerHTML = "";
      }

      function updateStatus(status, className) {
        const statusDiv = document.getElementById("status");
        statusDiv.textContent = status;
        statusDiv.className = `status ${className}`;
        document.getElementById("connectionState").textContent = status;
      }

      function updateButtons(connected) {
        document.getElementById("connectBtn").disabled = connected;
        document.getElementById("disconnectBtn").disabled = !connected;
        document.getElementById("testBtn").disabled = !connected;
      }

      function getAuthToken() {
        try {
          const authStorage = localStorage.getItem("auth-storage");
          if (authStorage) {
            const parsed = JSON.parse(authStorage);
            return parsed.state?.token || null;
          }
        } catch (error) {
          log("获取token失败: " + error.message, "error");
        }
        return null;
      }

      function connectWebSocket() {
        if (ws && ws.readyState === WebSocket.OPEN) {
          log("WebSocket已经连接", "warning");
          return;
        }

        const token = getAuthToken();
        const wsUrl = token ? `ws://localhost:3000/ws?token=${token}` : "ws://localhost:3000/ws";

        document.getElementById("wsUrl").textContent = wsUrl;
        document.getElementById("tokenInfo").textContent = token ? "已获取" : "未获取";

        updateStatus("正在连接...", "connecting");
        updateButtons(false);

        log(`尝试连接到: ${wsUrl}`, "info");

        try {
          ws = new WebSocket(wsUrl);

          ws.onopen = function () {
            updateStatus("已连接", "connected");
            updateButtons(true);
            reconnectAttempts = 0;
            log("WebSocket连接已建立", "success");

            // 发送订阅消息
            ws.send(
              JSON.stringify({
                type: "subscribe",
                channels: ["global", "user"],
              })
            );
            log("已发送订阅消息", "info");
          };

          ws.onmessage = function (event) {
            try {
              const data = JSON.parse(event.data);
              log(`收到消息: ${JSON.stringify(data, null, 2)}`, "success");
            } catch (e) {
              log(`解析消息失败: ${e.message}`, "error");
              log(`原始消息: ${event.data}`, "warning");
            }
          };

          ws.onerror = function (error) {
            updateStatus("连接错误", "error");
            updateButtons(false);
            log("WebSocket连接错误: " + error, "error");
          };

          ws.onclose = function (event) {
            updateStatus("连接已关闭", "disconnected");
            updateButtons(false);
            log(`WebSocket连接已关闭: 代码=${event.code}, 原因=${event.reason}`, "warning");

            // 自动重连逻辑
            if (reconnectAttempts < maxReconnectAttempts) {
              reconnectAttempts++;
              log(`尝试重连 (${reconnectAttempts}/${maxReconnectAttempts})...`, "info");
              setTimeout(connectWebSocket, 3000);
            } else {
              log("重连次数超过限制，停止重连", "error");
            }
          };
        } catch (e) {
          updateStatus("连接失败", "error");
          updateButtons(false);
          log(`创建WebSocket连接失败: ${e.message}`, "error");
        }
      }

      function disconnectWebSocket() {
        if (ws) {
          ws.close();
          ws = null;
          updateStatus("已断开连接", "disconnected");
          updateButtons(false);
          log("手动断开WebSocket连接", "info");
        }
      }

      function testMessage() {
        if (ws && ws.readyState === WebSocket.OPEN) {
          const testMessage = {
            type: "heartbeat",
            timestamp: Date.now(),
            message: "测试消息",
          };
          ws.send(JSON.stringify(testMessage));
          log("已发送测试消息: " + JSON.stringify(testMessage), "info");
        } else {
          log("WebSocket未连接，无法发送消息", "error");
        }
      }

      // 页面加载时自动尝试连接
      window.onload = function () {
        log("页面加载完成，准备连接WebSocket", "info");
        setTimeout(connectWebSocket, 1000);
      };
    </script>
  </body>
</html>
