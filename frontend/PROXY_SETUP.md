# 前端代理配置说明

## 概述

前端已成功配置代理，可以访问后端 API。代理配置将前端的 `/api` 请求转发到后端服务器。

## 配置详情

### 1. 代理配置 (rsbuild.config.ts)

```typescript
server: {
  proxy: {
    '/api': {
      target: 'http://localhost:3001',  // 后端服务地址
      changeOrigin: true,
      secure: false,
      logLevel: 'debug',               // 启用详细日志
    },
  },
}
```

### 2. API 客户端 (src/utils/request.ts)

- 开发环境：使用代理路径 `/api/v1`
- 生产环境：使用完整 API URL
- 自动添加认证头 (Bearer Token)
- 统一错误处理
- 请求/响应拦截器

### 3. 环境变量配置

```bash
# .env.local
VITE_API_BASE_URL=http://localhost:3001/api/v1
VITE_DEV_PROXY_TARGET=http://localhost:3001
```

## 使用方式

### 1. API 服务调用

```typescript
import { authService } from "@/services";

// 登录
const result = await authService.login({
  username: "admin",
  password: "password123",
});
```

### 2. 直接使用请求工具

```typescript
import { get, post } from "@/utils/request";

// GET 请求
const users = await get("/users");

// POST 请求
const newUser = await post("/users", {
  name: "John Doe",
  email: "<EMAIL>",
});
```

## 测试代理连接

### 1. 浏览器测试

访问 `http://localhost:3000` (前端开发服务器)，在仪表盘页面点击"测试API连接"按钮。

### 2. 开发者工具

打开浏览器开发者工具，查看 Network 标签页：

- 前端请求：`http://localhost:3000/api/v1/xxx`
- 代理转发：`http://localhost:3001/api/v1/xxx`

### 3. 控制台日志

代理配置了详细日志，可以在终端看到代理请求：

```
[HPM] Proxy created: /api -> http://localhost:3001
[Proxy] GET /api/v1/health -> localhost:3001/api/v1/health
```

## 故障排除

### 1. 代理不工作

**检查项目：**

- 后端服务是否在 `http://localhost:3001` 运行
- 后端是否提供了 `/api/v1/health` 健康检查端点
- 防火墙是否阻止了端口访问

**解决方案：**

```bash
# 检查后端服务
curl http://localhost:3001/api/v1/health

# 检查端口占用
lsof -i :3001
```

### 2. CORS 错误

如果直接访问后端 API 出现 CORS 错误，代理配置会解决这个问题。

### 3. 认证问题

确保：

- JWT Token 正确存储在 localStorage
- 请求头包含 `Authorization: Bearer <token>`
- Token 未过期

## API 端点示例

基于后端 API 设计，以下是主要端点：

```
POST /api/v1/auth/login          # 用户登录
GET  /api/v1/auth/me             # 获取当前用户
POST /api/v1/auth/logout         # 用户登出

GET  /api/v1/customers           # 获取客户列表
POST /api/v1/customers           # 创建客户
GET  /api/v1/customers/:id       # 获取客户详情

GET  /api/v1/projects            # 获取项目列表
POST /api/v1/projects            # 创建项目

GET  /api/v1/services            # 获取服务工单列表
POST /api/v1/services            # 创建服务工单
```

## 开发建议

1. **使用服务层**：优先使用 `src/services/` 下的 API 服务，而不是直接调用请求工具
2. **错误处理**：所有 API 调用都应该有适当的错误处理
3. **类型安全**：利用 TypeScript 类型定义确保 API 调用的类型安全
4. **测试优先**：在开发新功能前先测试 API 连接是否正常
