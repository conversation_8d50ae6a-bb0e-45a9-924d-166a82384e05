import { useState, useEffect, useCallback, useRef } from "react";

// 简化的类型定义，避免复杂的导入
export enum ConnectionStatus {
  CONNECTING = "connecting",
  CONNECTED = "connected",
  RECONNECTING = "reconnecting",
  DISCONNECTED = "disconnected",
  ERROR = "error",
}

export enum RealtimeEventType {
  CONNECT = "connect",
  SUBSCRIBE = "subscribe",
  UNSUBSCRIBE = "unsubscribe",
  HEARTBEAT = "heartbeat",
}

export enum RealtimeChannelType {
  GLOBAL = "global",
  USER = "user",
  SYSTEM_MONITOR = "system_monitor",
  SYSTEM_ALERT = "system_alert",
  NOTIFICATION = "notification",
  AUDIT = "audit",
  USER_ACTIVITY = "user_activity",
}

interface WebSocketState {
  status: ConnectionStatus;
  connected: boolean;
  connecting: boolean;
  error: string | null;
  reconnectCount: number;
  lastMessage: any | null;
  stats: {
    messagesReceived: number;
    messagesSent: number;
    reconnectAttempts: number;
  };
}

interface UseWebSocketOptions {
  url?: string;
  autoConnect?: boolean;
  autoReconnect?: boolean;
  maxReconnectAttempts?: number;
  reconnectDelay?: number;
  heartbeatInterval?: number;
  subscriptions?: RealtimeChannelType[];
}

export function useWebSocket(options: UseWebSocketOptions = {}) {
  const {
    url = process.env.NODE_ENV === "development"
      ? "ws://localhost:3001/ws"
      : `ws://${window.location.host}/ws`,
    autoConnect = true,
    autoReconnect = true,
    maxReconnectAttempts = 3,
    reconnectDelay = 5000,
    heartbeatInterval = 60000,
    subscriptions = [RealtimeChannelType.GLOBAL, RealtimeChannelType.USER],
  } = options;

  const [state, setState] = useState<WebSocketState>({
    status: ConnectionStatus.DISCONNECTED,
    connected: false,
    connecting: false,
    error: null,
    reconnectCount: 0,
    lastMessage: null,
    stats: {
      messagesReceived: 0,
      messagesSent: 0,
      reconnectAttempts: 0,
    },
  });

  const eventListeners = useRef(new Map<string, Set<Function>>());
  const channelListeners = useRef(new Map<string, Set<Function>>());
  const ws = useRef<WebSocket | null>(null);
  const reconnectTimer = useRef<NodeJS.Timeout | null>(null);
  const heartbeatTimer = useRef<NodeJS.Timeout | null>(null);
  const isManualClose = useRef(false);
  const reconnectCount = useRef(0);

  const getAuthToken = useCallback((): string | null => {
    if (typeof window === "undefined") return null;

    try {
      // 优先从内存中的AuthStore获取最新token
      if (typeof window !== "undefined" && (window as any).__AUTH_STORE__) {
        const token = (window as any).__AUTH_STORE__.getState?.()?.token;
        if (token) return token;
      }

      // 从localStorage获取
      const authStorage = localStorage.getItem("auth-storage");
      if (authStorage) {
        const parsed = JSON.parse(authStorage);
        return parsed.state?.token || null;
      }
    } catch (error) {
      console.warn("Failed to get auth token:", error);
    }

    return null;
  }, []);

  const connect = useCallback(async (): Promise<void> => {
    // 检查是否已经有连接且状态正常
    if (ws.current?.readyState === WebSocket.OPEN) {
      return;
    }

    // 检查是否正在连接中
    if (ws.current?.readyState === WebSocket.CONNECTING) {
      return;
    }

    setState(prev => ({
      ...prev,
      connecting: true,
      status: ConnectionStatus.CONNECTING,
      error: null,
    }));

    try {
      const token = getAuthToken();
      const wsUrl = token ? `${url}?token=${token}` : url;

      ws.current = new WebSocket(wsUrl);

      ws.current.onopen = () => {
        // 重置重连计数
        reconnectCount.current = 0;

        setState(prev => ({
          ...prev,
          status: ConnectionStatus.CONNECTED,
          connected: true,
          connecting: false,
          error: null,
          reconnectCount: 0,
        }));

        if (subscriptions.length > 0) {
          send({
            type: RealtimeEventType.SUBSCRIBE,
            channels: subscriptions,
          });
        }

        if (heartbeatTimer.current) {
          clearInterval(heartbeatTimer.current);
        }

        heartbeatTimer.current = setInterval(() => {
          send({
            type: RealtimeEventType.HEARTBEAT,
            timestamp: Date.now(),
          });
        }, heartbeatInterval);
      };

      ws.current.onmessage = event => {
        try {
          const message = JSON.parse(event.data);

          setState(prev => ({
            ...prev,
            lastMessage: message,
            stats: {
              ...prev.stats,
              messagesReceived: prev.stats.messagesReceived + 1,
            },
          }));

          // 触发事件监听器
          const listeners = eventListeners.current.get(message.type);
          if (listeners) {
            listeners.forEach(handler => {
              try {
                handler(message);
              } catch (error) {
                console.error("事件处理器执行失败:", error);
              }
            });
          }

          // 触发频道监听器
          if (message.channel) {
            const listeners = channelListeners.current.get(message.channel);
            if (listeners) {
              listeners.forEach(handler => {
                try {
                  handler(message);
                } catch (error) {
                  console.error("频道事件处理器执行失败:", error);
                }
              });
            }
          }
        } catch (error) {
          console.error("解析WebSocket消息失败:", error);
        }
      };

      ws.current.onclose = _event => {
        setState(prev => ({
          ...prev,
          status: ConnectionStatus.DISCONNECTED,
          connected: false,
          connecting: false,
        }));

        if (heartbeatTimer.current) {
          clearInterval(heartbeatTimer.current);
          heartbeatTimer.current = null;
        }

        // 只有在非手动关闭且启用自动重连时才重连
        if (!isManualClose.current && autoReconnect) {
          reconnectCount.current += 1;
          const currentReconnectCount = reconnectCount.current;

          if (currentReconnectCount <= maxReconnectAttempts) {
            setState(prev => ({
              ...prev,
              status: ConnectionStatus.RECONNECTING,
              reconnectCount: currentReconnectCount,
              stats: {
                ...prev.stats,
                reconnectAttempts: prev.stats.reconnectAttempts + 1,
              },
            }));

            // 使用指数退避策略
            const delay = Math.min(reconnectDelay * Math.pow(2, currentReconnectCount - 1), 30000);
            console.log(
              `WebSocket: 尝试重连 (${currentReconnectCount}/${maxReconnectAttempts}), 延迟: ${delay}ms`
            );

            reconnectTimer.current = setTimeout(() => {
              connect();
            }, delay);
          } else {
            setState(prev => ({
              ...prev,
              status: ConnectionStatus.ERROR,
              error: "重连次数超过限制",
            }));
            console.log("WebSocket: 重连次数超过限制，停止重连");
          }
        }
      };

      ws.current.onerror = _event => {
        setState(prev => ({
          ...prev,
          status: ConnectionStatus.ERROR,
          error: "WebSocket连接错误",
        }));
      };
    } catch (error) {
      setState(prev => ({
        ...prev,
        connecting: false,
        status: ConnectionStatus.ERROR,
        error: error instanceof Error ? error.message : "连接失败",
      }));
    }
  }, [
    url,
    getAuthToken,
    subscriptions,
    heartbeatInterval,
    autoReconnect,
    maxReconnectAttempts,
    reconnectDelay,
  ]);

  const disconnect = useCallback((): void => {
    isManualClose.current = true;
    reconnectCount.current = 0; // 重置重连计数

    if (reconnectTimer.current) {
      clearTimeout(reconnectTimer.current);
      reconnectTimer.current = null;
    }

    if (heartbeatTimer.current) {
      clearInterval(heartbeatTimer.current);
      heartbeatTimer.current = null;
    }

    if (ws.current) {
      ws.current.close();
      ws.current = null;
    }

    setState(prev => ({
      ...prev,
      status: ConnectionStatus.DISCONNECTED,
      connected: false,
      connecting: false,
    }));
  }, []);

  const send = useCallback((message: any): boolean => {
    if (ws.current?.readyState === WebSocket.OPEN) {
      ws.current.send(JSON.stringify(message));
      setState(prev => ({
        ...prev,
        stats: {
          ...prev.stats,
          messagesSent: prev.stats.messagesSent + 1,
        },
      }));
      return true;
    }
    return false;
  }, []);

  const reconnect = useCallback((): void => {
    disconnect();
    isManualClose.current = false;
    setTimeout(() => {
      connect();
    }, 1000);
  }, [disconnect, connect]);

  // 便捷的事件监听器
  const onConnect = useCallback((handler: Function) => {
    if (!eventListeners.current.has("connect")) {
      eventListeners.current.set("connect", new Set());
    }
    eventListeners.current.get("connect")!.add(handler);
  }, []);

  const onSystemMetrics = useCallback((handler: Function) => {
    if (!channelListeners.current.has(RealtimeChannelType.SYSTEM_MONITOR)) {
      channelListeners.current.set(RealtimeChannelType.SYSTEM_MONITOR, new Set());
    }
    channelListeners.current.get(RealtimeChannelType.SYSTEM_MONITOR)!.add(handler);
  }, []);

  const onSystemAlert = useCallback((handler: Function) => {
    if (!channelListeners.current.has(RealtimeChannelType.SYSTEM_ALERT)) {
      channelListeners.current.set(RealtimeChannelType.SYSTEM_ALERT, new Set());
    }
    channelListeners.current.get(RealtimeChannelType.SYSTEM_ALERT)!.add(handler);
  }, []);

  const onOnlineUsers = useCallback((handler: Function) => {
    if (!channelListeners.current.has(RealtimeChannelType.USER)) {
      channelListeners.current.set(RealtimeChannelType.USER, new Set());
    }
    channelListeners.current.get(RealtimeChannelType.USER)!.add(handler);
  }, []);

  const onNotification = useCallback((handler: Function) => {
    if (!channelListeners.current.has(RealtimeChannelType.NOTIFICATION)) {
      channelListeners.current.set(RealtimeChannelType.NOTIFICATION, new Set());
    }
    channelListeners.current.get(RealtimeChannelType.NOTIFICATION)!.add(handler);
  }, []);

  const onAuditLog = useCallback((handler: Function) => {
    if (!channelListeners.current.has(RealtimeChannelType.AUDIT)) {
      channelListeners.current.set(RealtimeChannelType.AUDIT, new Set());
    }
    channelListeners.current.get(RealtimeChannelType.AUDIT)!.add(handler);
  }, []);

  const onUserActivity = useCallback((handler: Function) => {
    if (!channelListeners.current.has(RealtimeChannelType.USER_ACTIVITY)) {
      channelListeners.current.set(RealtimeChannelType.USER_ACTIVITY, new Set());
    }
    channelListeners.current.get(RealtimeChannelType.USER_ACTIVITY)!.add(handler);
  }, []);

  // 自动连接
  useEffect(() => {
    let isMounted = true;

    if (autoConnect && isMounted) {
      connect();
    }

    return () => {
      isMounted = false;
      disconnect();
    };
  }, [autoConnect, connect, disconnect]);

  return {
    state,
    connect,
    disconnect,
    send,
    reconnect,
    onConnect,
    onSystemMetrics,
    onSystemAlert,
    onOnlineUsers,
    onNotification,
    onAuditLog,
    onUserActivity,
  };
}
