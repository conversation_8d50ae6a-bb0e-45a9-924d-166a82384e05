import { useCallback, useEffect, useRef, useState } from "react";
import { ConnectionStatus, RealtimeEventType, RealtimeChannelType } from "./useWebSocket";

// 重新导出类型
export { ConnectionStatus, RealtimeEventType, RealtimeChannelType };

interface WebSocketState {
  status: ConnectionStatus;
  connected: boolean;
  connecting: boolean;
  error: string | null;
  reconnectCount: number;
  lastMessage: any | null;
  stats: {
    messagesReceived: number;
    messagesSent: number;
    reconnectAttempts: number;
  };
}

interface WebSocketManagerOptions {
  url?: string;
  autoConnect?: boolean;
  autoReconnect?: boolean;
  maxReconnectAttempts?: number;
  reconnectDelay?: number;
  heartbeatInterval?: number;
}

// 全局WebSocket实例
let globalWebSocket: WebSocket | null = null;
let globalState: WebSocketState = {
  status: ConnectionStatus.DISCONNECTED,
  connected: false,
  connecting: false,
  error: null,
  reconnectCount: 0,
  lastMessage: null,
  stats: {
    messagesReceived: 0,
    messagesSent: 0,
    reconnectAttempts: 0,
  },
};

// 全局事件监听器
const globalEventListeners = new Map<string, Set<Function>>();
const globalChannelListeners = new Map<string, Set<Function>>();

// 全局定时器
let globalReconnectTimer: NodeJS.Timeout | null = null;
let globalHeartbeatTimer: NodeJS.Timeout | null = null;
let globalIsManualClose = false;

// 全局订阅管理
const globalSubscriptions = new Set<RealtimeChannelType>();

// 获取认证token
const getAuthToken = (): string | null => {
  if (typeof window === "undefined") return null;

  try {
    // 优先从内存中的AuthStore获取最新token
    if (typeof window !== "undefined" && (window as any).__AUTH_STORE__) {
      const token = (window as any).__AUTH_STORE__.getState?.()?.token;
      if (token) return token;
    }

    // 从localStorage获取
    const authStorage = localStorage.getItem("auth-storage");
    if (authStorage) {
      const parsed = JSON.parse(authStorage);
      return parsed.state?.token || null;
    }
  } catch (error) {
    console.warn("Failed to get auth token:", error);
  }

  return null;
};

// 连接WebSocket
const connectWebSocket = async (options: WebSocketManagerOptions = {}): Promise<void> => {
  const {
    url = process.env.NODE_ENV === "development"
      ? "ws://localhost:3001/ws"
      : `ws://${window.location.host}/ws`,
    autoReconnect = true,
    maxReconnectAttempts = 3,
    reconnectDelay = 5000,
    heartbeatInterval = 60000,
  } = options;

  if (globalWebSocket?.readyState === WebSocket.OPEN || globalState.connecting) {
    return;
  }

  globalState = {
    ...globalState,
    connecting: true,
    status: ConnectionStatus.CONNECTING,
    error: null,
  };

  try {
    const token = getAuthToken();
    const wsUrl = token ? `${url}?token=${token}` : url;

    globalWebSocket = new WebSocket(wsUrl);

    globalWebSocket.onopen = () => {
      globalState = {
        ...globalState,
        status: ConnectionStatus.CONNECTED,
        connected: true,
        connecting: false,
        error: null,
        reconnectCount: 0,
      };

      // 发送所有订阅的频道
      if (globalSubscriptions.size > 0) {
        sendMessage({
          type: RealtimeEventType.SUBSCRIBE,
          channels: Array.from(globalSubscriptions),
        });
      }

      // 启动心跳
      if (globalHeartbeatTimer) {
        clearInterval(globalHeartbeatTimer);
      }

      globalHeartbeatTimer = setInterval(() => {
        sendMessage({
          type: RealtimeEventType.HEARTBEAT,
          timestamp: Date.now(),
        });
      }, heartbeatInterval);

      console.log("WebSocket: 全局连接已建立");
    };

    globalWebSocket.onmessage = event => {
      try {
        const message = JSON.parse(event.data);
        globalState = {
          ...globalState,
          lastMessage: message,
          stats: {
            ...globalState.stats,
            messagesReceived: globalState.stats.messagesReceived + 1,
          },
        };

        // 触发事件监听器
        const listeners = globalEventListeners.get(message.type);
        if (listeners) {
          listeners.forEach(handler => {
            try {
              handler(message);
            } catch (error) {
              console.error("事件处理器执行失败:", error);
            }
          });
        }

        // 触发频道监听器
        if (message.channel) {
          const channelListeners = globalEventListeners.get(message.channel);
          if (channelListeners) {
            channelListeners.forEach(handler => {
              try {
                handler(message);
              } catch (error) {
                console.error("频道事件处理器执行失败:", error);
              }
            });
          }
        }
      } catch (error) {
        console.error("解析WebSocket消息失败:", error);
      }
    };

    globalWebSocket.onclose = _event => {
      globalState = {
        ...globalState,
        status: ConnectionStatus.DISCONNECTED,
        connected: false,
        connecting: false,
      };

      if (globalHeartbeatTimer) {
        clearInterval(globalHeartbeatTimer);
        globalHeartbeatTimer = null;
      }

      // 自动重连
      if (!globalIsManualClose && autoReconnect) {
        const currentReconnectCount = globalState.reconnectCount + 1;

        if (currentReconnectCount <= maxReconnectAttempts) {
          globalState = {
            ...globalState,
            status: ConnectionStatus.RECONNECTING,
            reconnectCount: currentReconnectCount,
            stats: {
              ...globalState.stats,
              reconnectAttempts: globalState.stats.reconnectAttempts + 1,
            },
          };

          const delay = Math.min(reconnectDelay * Math.pow(2, currentReconnectCount - 1), 30000);
          console.log(
            `WebSocket: 尝试重连 (${currentReconnectCount}/${maxReconnectAttempts}), 延迟: ${delay}ms`
          );

          globalReconnectTimer = setTimeout(() => {
            connectWebSocket(options);
          }, delay);
        } else {
          globalState = {
            ...globalState,
            status: ConnectionStatus.ERROR,
            error: "重连次数超过限制",
          };
          console.log("WebSocket: 重连次数超过限制，停止重连");
        }
      }
    };

    globalWebSocket.onerror = _event => {
      globalState = {
        ...globalState,
        status: ConnectionStatus.ERROR,
        error: "WebSocket连接错误",
      };
    };
  } catch (error) {
    globalState = {
      ...globalState,
      connecting: false,
      status: ConnectionStatus.ERROR,
      error: error instanceof Error ? error.message : "连接失败",
    };
  }
};

// 断开WebSocket连接
const disconnectWebSocket = (): void => {
  globalIsManualClose = true;

  if (globalReconnectTimer) {
    clearTimeout(globalReconnectTimer);
    globalReconnectTimer = null;
  }

  if (globalHeartbeatTimer) {
    clearInterval(globalHeartbeatTimer);
    globalHeartbeatTimer = null;
  }

  if (globalWebSocket) {
    globalWebSocket.close();
    globalWebSocket = null;
  }

  globalState = {
    ...globalState,
    status: ConnectionStatus.DISCONNECTED,
    connected: false,
    connecting: false,
  };
};

// 发送消息
const sendMessage = (message: any): boolean => {
  if (globalWebSocket?.readyState === WebSocket.OPEN) {
    globalWebSocket.send(JSON.stringify(message));
    globalState = {
      ...globalState,
      stats: {
        ...globalState.stats,
        messagesSent: globalState.stats.messagesSent + 1,
      },
    };
    return true;
  }
  return false;
};

// 订阅频道
const subscribeToChannel = (channel: RealtimeChannelType): void => {
  globalSubscriptions.add(channel);
  if (globalState.connected) {
    sendMessage({
      type: RealtimeEventType.SUBSCRIBE,
      channels: [channel],
    });
  }
};

// 取消订阅频道
const unsubscribeFromChannel = (channel: RealtimeChannelType): void => {
  globalSubscriptions.delete(channel);
  if (globalState.connected) {
    sendMessage({
      type: RealtimeEventType.UNSUBSCRIBE,
      channels: [channel],
    });
  }
};

// 添加事件监听器
const addEventListener = (eventType: string, handler: Function): void => {
  if (!globalEventListeners.has(eventType)) {
    globalEventListeners.set(eventType, new Set());
  }
  globalEventListeners.get(eventType)!.add(handler);
};

// 移除事件监听器
const removeEventListener = (eventType: string, handler: Function): void => {
  const listeners = globalEventListeners.get(eventType);
  if (listeners) {
    listeners.delete(handler);
  }
};

// 添加频道监听器
const addChannelListener = (channel: string, handler: Function): void => {
  if (!globalChannelListeners.has(channel)) {
    globalChannelListeners.set(channel, new Set());
  }
  globalChannelListeners.get(channel)!.add(handler);
};

// 移除频道监听器
const removeChannelListener = (channel: string, handler: Function): void => {
  const listeners = globalChannelListeners.get(channel);
  if (listeners) {
    listeners.delete(handler);
  }
};

// React Hook
export function useWebSocketManager(options: WebSocketManagerOptions = {}) {
  const [state, setState] = useState<WebSocketState>(globalState);
  const mountedRef = useRef(true);

  // 同步全局状态
  useEffect(() => {
    const updateState = () => {
      if (mountedRef.current) {
        setState(globalState);
      }
    };

    // 立即更新一次
    updateState();

    // 设置定时器定期同步状态
    const interval = setInterval(updateState, 100);

    return () => {
      clearInterval(interval);
    };
  }, []);

  // 组件卸载时清理
  useEffect(() => {
    return () => {
      mountedRef.current = false;
    };
  }, []);

  const connect = useCallback(() => {
    connectWebSocket(options);
  }, [options]);

  const disconnect = useCallback(() => {
    disconnectWebSocket();
  }, []);

  const send = useCallback((message: any) => {
    return sendMessage(message);
  }, []);

  const subscribe = useCallback((channel: RealtimeChannelType) => {
    subscribeToChannel(channel);
  }, []);

  const unsubscribe = useCallback((channel: RealtimeChannelType) => {
    unsubscribeFromChannel(channel);
  }, []);

  const onConnect = useCallback((handler: Function) => {
    addEventListener("connect", handler);
    return () => removeEventListener("connect", handler);
  }, []);

  const onSystemMetrics = useCallback((handler: Function) => {
    addChannelListener(RealtimeChannelType.SYSTEM_MONITOR, handler);
    return () => removeChannelListener(RealtimeChannelType.SYSTEM_MONITOR, handler);
  }, []);

  const onSystemAlert = useCallback((handler: Function) => {
    addChannelListener(RealtimeChannelType.SYSTEM_ALERT, handler);
    return () => removeChannelListener(RealtimeChannelType.SYSTEM_ALERT, handler);
  }, []);

  const onOnlineUsers = useCallback((handler: Function) => {
    addChannelListener(RealtimeChannelType.USER, handler);
    return () => removeChannelListener(RealtimeChannelType.USER, handler);
  }, []);

  const onNotification = useCallback((handler: Function) => {
    addChannelListener(RealtimeChannelType.NOTIFICATION, handler);
    return () => removeChannelListener(RealtimeChannelType.NOTIFICATION, handler);
  }, []);

  const onAuditLog = useCallback((handler: Function) => {
    addChannelListener(RealtimeChannelType.AUDIT, handler);
    return () => removeChannelListener(RealtimeChannelType.AUDIT, handler);
  }, []);

  const onUserActivity = useCallback((handler: Function) => {
    addChannelListener(RealtimeChannelType.USER_ACTIVITY, handler);
    return () => removeChannelListener(RealtimeChannelType.USER_ACTIVITY, handler);
  }, []);

  // 自动连接
  useEffect(() => {
    if (options.autoConnect !== false) {
      connect();
    }

    return () => {
      // 组件卸载时不自动断开，让其他组件继续使用
    };
  }, [connect, options.autoConnect]);

  return {
    state,
    connect,
    disconnect,
    send,
    subscribe,
    unsubscribe,
    onConnect,
    onSystemMetrics,
    onSystemAlert,
    onOnlineUsers,
    onNotification,
    onAuditLog,
    onUserActivity,
  };
}

// 导出全局函数
export const WebSocketManager = {
  connect: connectWebSocket,
  disconnect: disconnectWebSocket,
  send: sendMessage,
  subscribe: subscribeToChannel,
  unsubscribe: unsubscribeFromChannel,
  getState: () => globalState,
};
