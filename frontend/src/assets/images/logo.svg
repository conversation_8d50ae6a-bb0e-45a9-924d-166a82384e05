<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 64 64" width="64" height="64">
  <defs>
    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1d4ed8;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background circle -->
  <circle cx="32" cy="32" r="30" fill="url(#gradient)" stroke="#1e40af" stroke-width="2"/>
  
  <!-- Chinese character "运" in white -->
  <text x="32" y="42" font-family="serif" font-size="28" font-weight="bold" text-anchor="middle" fill="white">运</text>
  
  <!-- Small gear icon in top-right -->
  <g transform="translate(48, 16) scale(0.6)">
    <circle cx="8" cy="8" r="3" fill="none" stroke="white" stroke-width="1.5"/>
    <path d="M8 1L8 3M8 13L8 15M15 8L13 8M3 8L1 8M13.07 2.93L11.66 4.34M4.34 11.66L2.93 13.07M13.07 13.07L11.66 11.66M4.34 4.34L2.93 2.93" stroke="white" stroke-width="1.5" fill="none"/>
  </g>
</svg>