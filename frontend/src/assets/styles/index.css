/* 导入设计系统 - 放在最前面确保优先级 */
@import "./design-system.css";

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer components {
  .btn-primary {
    @apply bg-blue-600 text-white font-medium px-4 py-2 rounded-md
           transition-colors hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2
           disabled:opacity-50 disabled:cursor-not-allowed;
  }

  .btn-secondary {
    @apply bg-gray-200 text-gray-900 font-medium px-4 py-2 rounded-md 
           transition-colors hover:bg-gray-300 focus:ring-2 focus:ring-gray-500 focus:ring-offset-2
           disabled:opacity-50 disabled:cursor-not-allowed;
  }

  .btn-ghost {
    @apply text-gray-700 font-medium px-4 py-2 rounded-md 
           transition-colors hover:bg-gray-100 focus:ring-2 focus:ring-gray-500 focus:ring-offset-2
           disabled:opacity-50 disabled:cursor-not-allowed;
  }

  .card {
    @apply bg-white shadow-lg border border-gray-200 rounded-lg p-6
           dark:bg-gray-800 dark:border-gray-700;
  }

  .form-input {
    @apply w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm
           focus:ring-2 focus:ring-blue-500 focus:border-transparent
           disabled:bg-gray-50 disabled:text-gray-500
           dark:bg-gray-700 dark:border-gray-600 dark:text-white;
  }

  .form-label {
    @apply block text-sm font-medium text-gray-700 mb-1
           dark:text-gray-300;
  }

  .table-container {
    @apply overflow-x-auto shadow-lg border border-gray-200 rounded-lg
           dark:border-gray-700;
  }

  .page-header {
    @apply mb-6 pb-4 border-b border-gray-200
           dark:border-gray-700;
  }

  .page-title {
    @apply text-2xl font-bold text-gray-900 mb-2
           dark:text-white;
  }

  .page-subtitle {
    @apply text-gray-600
           dark:text-gray-400;
  }
}

@layer utilities {
  .text-shadow {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .gradient-bg {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  }

  .animate-spin-slow {
    animation: spin 3s linear infinite;
  }
}

/* 全局样式优化 - 增强版 */
body {
  margin: 0;
  font-family:
    -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans",
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: var(--ops-bg-tertiary);
  line-height: 1.6;
}

/* 根容器优化 */
#root {
  min-height: 100vh;
  position: relative;
}

/* 布局相关的全局样式 */
.min-h-screen {
  min-height: 100vh;
}

/* 确保Layout组件的正确显示 */
.semi-layout {
  min-height: 100vh;
  background: transparent;
}

.semi-layout-content {
  background: transparent;
}

/* 响应式容器 */
.container-responsive {
  width: 100%;
  margin: 0 auto;
  padding: 0 var(--ops-spacing-md);
}

@media (min-width: 640px) {
  .container-responsive {
    max-width: 640px;
  }
}

@media (min-width: 768px) {
  .container-responsive {
    max-width: 768px;
  }
}

@media (min-width: 1024px) {
  .container-responsive {
    max-width: 1024px;
  }
}

@media (min-width: 1280px) {
  .container-responsive {
    max-width: 1280px;
  }
}

/* 滚动条美化 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
  transition: background 0.3s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: #a1a1a1;
}

/* Firefox滚动条 */
* {
  scrollbar-width: thin;
  scrollbar-color: #c1c1c1 #f1f1f1;
}
