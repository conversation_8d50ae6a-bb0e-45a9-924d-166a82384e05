/* 运维管理系统 - 现代化设计系统 */

/* ===== CSS 变量定义 ===== */
:root {
  /* 主色系 */
  --ops-primary-color: #1890ff;
  --ops-primary-hover: #40a9ff;
  --ops-primary-active: #0050b3;
  --ops-primary-light: #e6f7ff;

  /* 辅助色系 */
  --ops-success-color: #52c41a;
  --ops-success-light: #f6ffed;
  --ops-warning-color: #faad14;
  --ops-warning-light: #fff7e6;
  --ops-error-color: #ff4d4f;
  --ops-error-light: #fff2f0;
  --ops-info-color: #1890ff;
  --ops-info-light: #e6f7ff;

  /* 中性色系 */
  --ops-text-primary: #262626;
  --ops-text-secondary: #595959;
  --ops-text-tertiary: #8c8c8c;
  --ops-text-disabled: #bfbfbf;
  --ops-border-color: #e8e8e8;
  --ops-border-color-light: #f0f0f0;
  --ops-bg-color: #ffffff;
  --ops-bg-secondary: #fafafa;
  --ops-bg-tertiary: #f5f5f5;

  /* 渐变色系 */
  --ops-gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --ops-gradient-success: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
  --ops-gradient-warning: linear-gradient(135deg, #f7971e 0%, #ffd200 100%);
  --ops-gradient-error: linear-gradient(135deg, #ff6b6b 0%, #ff8e8e 100%);
  --ops-gradient-info: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);

  /* 阴影系统 */
  --ops-shadow-1: 0 2px 4px rgba(0, 0, 0, 0.06);
  --ops-shadow-2: 0 4px 8px rgba(0, 0, 0, 0.08);
  --ops-shadow-3: 0 8px 16px rgba(0, 0, 0, 0.1);
  --ops-shadow-4: 0 12px 24px rgba(0, 0, 0, 0.12);
  --ops-shadow-card: 0 2px 8px rgba(0, 0, 0, 0.06);
  --ops-shadow-card-hover: 0 4px 16px rgba(0, 0, 0, 0.12);

  /* 圆角系统 */
  --ops-border-radius-sm: 4px;
  --ops-border-radius-md: 6px;
  --ops-border-radius-lg: 8px;
  --ops-border-radius-xl: 12px;

  /* 间距系统 */
  --ops-spacing-xs: 4px;
  --ops-spacing-sm: 8px;
  --ops-spacing-md: 16px;
  --ops-spacing-lg: 24px;
  --ops-spacing-xl: 32px;
  --ops-spacing-xxl: 48px;

  /* 字体系统 */
  --ops-font-size-xs: 12px;
  --ops-font-size-sm: 14px;
  --ops-font-size-md: 16px;
  --ops-font-size-lg: 18px;
  --ops-font-size-xl: 20px;
  --ops-font-size-xxl: 24px;
  --ops-font-size-xxxl: 32px;

  --ops-line-height-compact: 1.4;
  --ops-line-height-normal: 1.6;
  --ops-line-height-relaxed: 1.8;

  /* Z-Index层级 */
  --ops-z-tooltip: 1050;
  --ops-z-modal: 1040;
  --ops-z-popover: 1030;
  --ops-z-dropdown: 1020;
  --ops-z-header: 1010;
  --ops-z-sidebar: 1000;
}

/* ===== 基础样式重置 ===== */
* {
  box-sizing: border-box;
}

/* 确保与Semi Design兼容的body样式 */
body {
  font-family:
    -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans",
    sans-serif;
  line-height: var(--ops-line-height-normal);
  color: var(--ops-text-primary);
  background-color: var(--ops-bg-secondary);
  margin: 0;
  padding: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 确保与Semi Design的兼容性 */
.semi-layout,
.semi-layout-content {
  background: transparent !important;
}

/* 覆盖Semi Design的一些默认样式以适配设计系统 */
.semi-nav {
  border-right: 1px solid var(--ops-border-color-light) !important;
}

.semi-nav-header {
  padding: var(--ops-spacing-lg) !important;
  border-bottom: 1px solid var(--ops-border-color-light) !important;
}

.semi-nav-item {
  margin: var(--ops-spacing-xs) var(--ops-spacing-sm) !important;
  border-radius: var(--ops-border-radius-md) !important;
}

.semi-nav-item-selected {
  background: var(--ops-primary-light) !important;
  color: var(--ops-primary-color) !important;
}

.semi-nav-item:hover {
  background: var(--ops-bg-tertiary) !important;
}

/* ===== 通用组件样式 ===== */

/* 现代化卡片 */
.ops-card {
  background: var(--ops-bg-color);
  border-radius: var(--ops-border-radius-lg);
  box-shadow: var(--ops-shadow-card);
  border: 1px solid var(--ops-border-color-light);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
}

.ops-card:hover {
  box-shadow: var(--ops-shadow-card-hover);
  transform: translateY(-1px);
}

.ops-card-header {
  padding: var(--ops-spacing-lg);
  border-bottom: 1px solid var(--ops-border-color-light);
  background: var(--ops-bg-color);
}

.ops-card-body {
  padding: var(--ops-spacing-lg);
}

.ops-card-footer {
  padding: var(--ops-spacing-md) var(--ops-spacing-lg);
  border-top: 1px solid var(--ops-border-color-light);
  background: var(--ops-bg-secondary);
}

/* 统计卡片 */
.ops-stats-card {
  background: var(--ops-bg-color);
  border-radius: var(--ops-border-radius-xl);
  padding: var(--ops-spacing-xl);
  box-shadow: var(--ops-shadow-card);
  border: 1px solid var(--ops-border-color-light);
  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.ops-stats-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--ops-shadow-3);
}

.ops-stats-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: var(--ops-primary-color);
  border-radius: 0 var(--ops-border-radius-sm) var(--ops-border-radius-sm) 0;
}

.ops-stats-card.success::before {
  background: var(--ops-success-color);
}

.ops-stats-card.warning::before {
  background: var(--ops-warning-color);
}

.ops-stats-card.error::before {
  background: var(--ops-error-color);
}

.ops-stats-card-title {
  font-size: var(--ops-font-size-sm);
  color: var(--ops-text-secondary);
  font-weight: 500;
  margin-bottom: var(--ops-spacing-sm);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.ops-stats-card-value {
  font-size: var(--ops-font-size-xxxl);
  font-weight: 700;
  color: var(--ops-text-primary);
  margin-bottom: var(--ops-spacing-xs);
  line-height: 1.2;
}

.ops-stats-card-trend {
  font-size: var(--ops-font-size-xs);
  color: var(--ops-text-tertiary);
  display: flex;
  align-items: center;
  gap: var(--ops-spacing-xs);
}

.ops-stats-card-icon {
  position: absolute;
  top: var(--ops-spacing-lg);
  right: var(--ops-spacing-lg);
  width: 48px;
  height: 48px;
  border-radius: var(--ops-border-radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--ops-primary-light);
  color: var(--ops-primary-color);
  opacity: 0.8;
}

/* 渐变统计卡片 */
.ops-gradient-card {
  background: var(--ops-gradient-primary);
  color: white;
  border: none;
  position: relative;
}

.ops-gradient-card.success {
  background: var(--ops-gradient-success);
}

.ops-gradient-card.warning {
  background: var(--ops-gradient-warning);
}

.ops-gradient-card.error {
  background: var(--ops-gradient-error);
}

.ops-gradient-card.info {
  background: var(--ops-gradient-info);
}

.ops-gradient-card::before {
  display: none;
}

.ops-gradient-card .ops-stats-card-title,
.ops-gradient-card .ops-stats-card-value,
.ops-gradient-card .ops-stats-card-trend {
  color: rgba(255, 255, 255, 0.9);
}

.ops-gradient-card .ops-stats-card-title {
  color: rgba(255, 255, 255, 0.8);
}

.ops-gradient-card .ops-stats-card-icon {
  background: rgba(255, 255, 255, 0.2);
  color: rgba(255, 255, 255, 0.9);
}

/* 页面头部 */
.ops-page-header {
  background: var(--ops-bg-color);
  padding: var(--ops-spacing-xl);
  margin-bottom: var(--ops-spacing-xl);
  border-bottom: 1px solid var(--ops-border-color-light);
}

.ops-page-title {
  font-size: var(--ops-font-size-xxxl);
  font-weight: 700;
  color: var(--ops-text-primary);
  margin-bottom: var(--ops-spacing-xs);
  line-height: var(--ops-line-height-compact);
}

.ops-page-subtitle {
  font-size: var(--ops-font-size-md);
  color: var(--ops-text-secondary);
  line-height: var(--ops-line-height-normal);
}

.ops-page-actions {
  display: flex;
  align-items: center;
  gap: var(--ops-spacing-md);
  flex-wrap: wrap;
}

/* 搜索和筛选区域 */
.ops-search-toolbar {
  background: var(--ops-bg-color);
  padding: var(--ops-spacing-lg);
  margin-bottom: var(--ops-spacing-lg);
}

.ops-search-row {
  display: flex;
  flex-wrap: wrap;
  gap: var(--ops-spacing-md);
  align-items: center;
}

/* 批量操作区域 */
.ops-batch-actions {
  background: linear-gradient(135deg, #e6f7ff 0%, #f0f9ff 100%);
  border: 1px solid var(--ops-info-color);
  border-radius: var(--ops-border-radius-lg);
  padding: var(--ops-spacing-lg);
  margin-bottom: var(--ops-spacing-lg);
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: var(--ops-spacing-md);
}

.ops-batch-info {
  display: flex;
  align-items: center;
  gap: var(--ops-spacing-md);
  color: var(--ops-info-color);
  font-weight: 500;
}

.ops-batch-controls {
  display: flex;
  align-items: center;
  gap: var(--ops-spacing-sm);
  flex-wrap: wrap;
}

/* 表格增强 */
.ops-table-container {
  background: var(--ops-bg-color);
  border-radius: var(--ops-border-radius-lg);
  overflow: hidden;
  box-shadow: var(--ops-shadow-card);
  border: 1px solid var(--ops-border-color-light);
}

.ops-table-header {
  background: var(--ops-bg-secondary);
  padding: var(--ops-spacing-lg);
  border-bottom: 1px solid var(--ops-border-color-light);
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: var(--ops-spacing-md);
}

.ops-table-title {
  font-size: var(--ops-font-size-lg);
  font-weight: 600;
  color: var(--ops-text-primary);
}

.ops-table-extra {
  display: flex;
  align-items: center;
  gap: var(--ops-spacing-sm);
}

/* 状态标识 */
.ops-status-indicator {
  display: inline-flex;
  align-items: center;
  gap: var(--ops-spacing-xs);
  padding: var(--ops-spacing-xs) var(--ops-spacing-sm);
  border-radius: var(--ops-border-radius-lg);
  font-size: var(--ops-font-size-xs);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.ops-status-indicator::before {
  content: "";
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: currentColor;
}

.ops-status-indicator.success {
  background: var(--ops-success-light);
  color: var(--ops-success-color);
}

.ops-status-indicator.warning {
  background: var(--ops-warning-light);
  color: var(--ops-warning-color);
}

.ops-status-indicator.error {
  background: var(--ops-error-light);
  color: var(--ops-error-color);
}

.ops-status-indicator.info {
  background: var(--ops-info-light);
  color: var(--ops-info-color);
}

/* 指标进度条 */
.ops-metric-progress {
  margin-bottom: var(--ops-spacing-lg);
}

.ops-metric-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--ops-spacing-sm);
}

.ops-metric-label {
  display: flex;
  align-items: center;
  gap: var(--ops-spacing-xs);
  font-size: var(--ops-font-size-sm);
  color: var(--ops-text-secondary);
  font-weight: 500;
}

.ops-metric-value {
  font-size: var(--ops-font-size-sm);
  font-weight: 600;
  color: var(--ops-text-primary);
}

/* 实时状态指示器 */
.ops-realtime-status {
  display: inline-flex;
  align-items: center;
  gap: var(--ops-spacing-sm);
  padding: var(--ops-spacing-sm) var(--ops-spacing-md);
  border-radius: var(--ops-border-radius-lg);
  font-size: var(--ops-font-size-sm);
  font-weight: 500;
  transition: all 0.3s ease;
}

.ops-realtime-status.connected {
  background: var(--ops-success-light);
  color: var(--ops-success-color);
  border: 1px solid var(--ops-success-color);
}

.ops-realtime-status.connecting {
  background: var(--ops-warning-light);
  color: var(--ops-warning-color);
  border: 1px solid var(--ops-warning-color);
}

.ops-realtime-status.disconnected {
  background: var(--ops-error-light);
  color: var(--ops-error-color);
  border: 1px solid var(--ops-error-color);
}

.ops-realtime-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: currentColor;
}

.ops-realtime-status.connecting .ops-realtime-dot {
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.5;
    transform: scale(1.1);
  }
}

/* 组件状态卡片 */
.ops-component-status {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--ops-spacing-lg);
  border: 1px solid var(--ops-border-color-light);
  border-radius: var(--ops-border-radius-lg);
  background: var(--ops-bg-color);
  transition: all 0.3s ease;
  margin-bottom: var(--ops-spacing-md);
}

.ops-component-status:hover {
  box-shadow: var(--ops-shadow-2);
  border-color: var(--ops-primary-color);
}

.ops-component-info {
  display: flex;
  align-items: center;
  gap: var(--ops-spacing-md);
}

.ops-component-icon {
  width: 40px;
  height: 40px;
  border-radius: var(--ops-border-radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--ops-bg-color);
}

.ops-component-details h4 {
  font-size: var(--ops-font-size-md);
  font-weight: 600;
  color: var(--ops-text-primary);
  margin: 0 0 var(--ops-spacing-xs) 0;
}

.ops-component-details p {
  font-size: var(--ops-font-size-sm);
  color: var(--ops-text-secondary);
  margin: 0;
}

.ops-component-metrics {
  text-align: right;
}

.ops-component-metrics .ops-status-indicator {
  margin-bottom: var(--ops-spacing-xs);
}

.ops-component-metrics .ops-metric-value {
  font-size: var(--ops-font-size-xs);
  color: var(--ops-text-tertiary);
}

/* 告警卡片 */
.ops-alert-card {
  padding: var(--ops-spacing-lg);
  border-radius: var(--ops-border-radius-lg);
  border-left: 4px solid;
  margin-bottom: var(--ops-spacing-md);
  cursor: pointer;
  transition: all 0.3s ease;
  background: var(--ops-bg-color);
}

.ops-alert-card:hover {
  transform: translateX(4px);
  box-shadow: var(--ops-shadow-2);
}

.ops-alert-card.error {
  border-left-color: var(--ops-error-color);
  background: var(--ops-error-light);
}

.ops-alert-card.warning {
  border-left-color: var(--ops-warning-color);
  background: var(--ops-warning-light);
}

.ops-alert-card.info {
  border-left-color: var(--ops-info-color);
  background: var(--ops-info-light);
}

.ops-alert-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: var(--ops-spacing-sm);
}

.ops-alert-title {
  font-size: var(--ops-font-size-sm);
  font-weight: 600;
  color: var(--ops-text-primary);
  margin: 0 0 var(--ops-spacing-xs) 0;
}

.ops-alert-message {
  font-size: var(--ops-font-size-sm);
  color: var(--ops-text-secondary);
  line-height: var(--ops-line-height-normal);
}

.ops-alert-time {
  font-size: var(--ops-font-size-xs);
  color: var(--ops-text-tertiary);
  white-space: nowrap;
}

/* 性能指标卡片 */
.ops-performance-card {
  padding: var(--ops-spacing-lg);
  border-radius: var(--ops-border-radius-lg);
  margin-bottom: var(--ops-spacing-md);
  transition: all 0.3s ease;
}

.ops-performance-card:hover {
  transform: translateY(-2px);
}

.ops-performance-card.requests {
  background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
  border: 1px solid #2196f3;
}

.ops-performance-card.database {
  background: linear-gradient(135deg, #e8f5e8 0%, #f1f8e9 100%);
  border: 1px solid #4caf50;
}

.ops-performance-card.cache {
  background: linear-gradient(135deg, #fff3e0 0%, #fce4ec 100%);
  border: 1px solid #ff9800;
}

.ops-performance-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--ops-spacing-md);
}

.ops-performance-title {
  font-size: var(--ops-font-size-md);
  font-weight: 600;
  color: var(--ops-text-primary);
}

.ops-performance-primary {
  font-size: var(--ops-font-size-lg);
  font-weight: 700;
  color: var(--ops-text-primary);
}

.ops-performance-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--ops-spacing-md);
  margin-top: var(--ops-spacing-md);
}

.ops-performance-metric {
  text-align: center;
}

.ops-performance-metric-label {
  font-size: var(--ops-font-size-xs);
  color: var(--ops-text-secondary);
  margin-bottom: var(--ops-spacing-xs);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.ops-performance-metric-value {
  font-size: var(--ops-font-size-md);
  font-weight: 600;
  color: var(--ops-text-primary);
}

/* 响应式布局 */
@media (max-width: 768px) {
  .ops-stats-card {
    padding: var(--ops-spacing-lg);
  }

  .ops-stats-card-value {
    font-size: var(--ops-font-size-xxl);
  }

  .ops-stats-card-icon {
    width: 36px;
    height: 36px;
    top: var(--ops-spacing-md);
    right: var(--ops-spacing-md);
  }

  .ops-page-title {
    font-size: var(--ops-font-size-xxl);
  }

  .ops-search-row {
    flex-direction: column;
    align-items: stretch;
  }

  .ops-batch-actions {
    flex-direction: column;
    align-items: stretch;
  }

  .ops-performance-grid {
    grid-template-columns: 1fr;
    gap: var(--ops-spacing-sm);
  }

  .ops-component-status {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--ops-spacing-md);
  }

  .ops-component-metrics {
    text-align: left;
    width: 100%;
  }
}

/* 暗色主题 */
@media (prefers-color-scheme: dark) {
  :root {
    --ops-text-primary: #ffffff;
    --ops-text-secondary: #a6a6a6;
    --ops-text-tertiary: #737373;
    --ops-text-disabled: #525252;
    --ops-border-color: #404040;
    --ops-border-color-light: #262626;
    --ops-bg-color: #171717;
    --ops-bg-secondary: #0a0a0a;
    --ops-bg-tertiary: #262626;
  }
}

/* 高对比度支持 */
@media (prefers-contrast: high) {
  :root {
    --ops-border-color: #000000;
    --ops-border-color-light: #666666;
  }

  .ops-card,
  .ops-stats-card,
  .ops-component-status {
    border-width: 2px;
  }
}

/* 动画减少支持 */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }

  .ops-realtime-status.connecting .ops-realtime-dot {
    animation: none;
  }
}

/* 打印样式 */
@media print {
  .ops-page-actions,
  .ops-search-toolbar,
  .ops-batch-actions {
    display: none;
  }

  .ops-card,
  .ops-stats-card {
    box-shadow: none;
    border: 1px solid var(--ops-border-color);
  }
}
