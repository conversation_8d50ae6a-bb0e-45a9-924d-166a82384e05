import React from "react";
import { useAuthStore } from "@/stores";
import { Card, Typography, Empty } from "@douyinfe/semi-ui";
import { IconLock } from "@douyinfe/semi-icons";
import { usePermissionCheck } from "@/components/guards/PermissionGuard";

const { Text } = Typography;

interface PermissionSectionProps {
  children: React.ReactNode;
  permissions?: string | string[];
  roles?: string | string[];
  requireAll?: boolean;
  fallback?: React.ReactNode;
  showFallback?: boolean;
  fallbackTitle?: string;
  fallbackDescription?: string;
  wrapWithCard?: boolean;
  cardProps?: any;
}

export function PermissionSection({
  children,
  permissions,
  roles,
  requireAll = false,
  fallback,
  showFallback = true,
  fallbackTitle = "权限不足",
  fallbackDescription = "您没有权限查看此内容",
  wrapWithCard = false,
  cardProps = {},
}: PermissionSectionProps) {
  const { hasAccess, missingPermissions, missingRoles } = usePermissionCheck(
    permissions,
    roles,
    requireAll
  );

  // 如果有权限，直接显示内容
  if (hasAccess) {
    return <>{children}</>;
  }

  // 如果有自定义fallback，使用自定义的
  if (fallback) {
    return <>{fallback}</>;
  }

  // 如果不显示fallback，返回null
  if (!showFallback) {
    return null;
  }

  // 构建详细的权限信息
  const getDetailedDescription = () => {
    const missing = [];
    if (missingPermissions.length > 0) {
      missing.push(`所需权限: ${missingPermissions.join(", ")}`);
    }
    if (missingRoles.length > 0) {
      missing.push(`所需角色: ${missingRoles.join(", ")}`);
    }

    if (missing.length > 0) {
      return `${fallbackDescription}。${missing.join("；")}`;
    }

    return fallbackDescription;
  };

  const fallbackContent = (
    <div className="text-center py-8">
      <Empty
        image={<IconLock size="large" style={{ color: "var(--semi-color-warning)" }} />}
        title={fallbackTitle}
        description={
          <Text type="secondary" size="small">
            {getDetailedDescription()}
          </Text>
        }
      />
    </div>
  );

  // 如果需要用Card包装
  if (wrapWithCard) {
    return (
      <Card
        style={{
          background: "var(--semi-color-bg-2)",
          borderColor: "var(--semi-color-border)",
        }}
        {...cardProps}
      >
        {fallbackContent}
      </Card>
    );
  }

  return fallbackContent;
}

// 基于角色的条件渲染组件
interface RoleSwitchProps {
  role: string;
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

export function RoleSwitch({ role, children, fallback = null }: RoleSwitchProps) {
  const { hasAccess } = usePermissionCheck(undefined, role);

  if (hasAccess) {
    return <>{children}</>;
  }

  return <>{fallback}</>;
}

// 多角色条件渲染组件
interface MultiRoleSwitchProps {
  cases: Array<{
    roles: string | string[];
    component: React.ReactNode;
    requireAll?: boolean;
  }>;
  fallback?: React.ReactNode;
}

export function MultiRoleSwitch({ cases, fallback = null }: MultiRoleSwitchProps) {
  const { hasAnyRole } = useAuthStore();

  for (const { roles, component, requireAll = false } of cases) {
    const roleArray = Array.isArray(roles) ? roles : [roles];
    const hasAccess = requireAll
      ? roleArray.every(role => hasAnyRole([role]))
      : roleArray.some(role => hasAnyRole([role]));

    if (hasAccess) {
      return <>{component}</>;
    }
  }

  return <>{fallback}</>;
}

// 权限组合渲染组件
interface PermissionCombinationProps {
  cases: Array<{
    permissions?: string | string[];
    roles?: string | string[];
    component: React.ReactNode;
    requireAll?: boolean;
  }>;
  fallback?: React.ReactNode;
}

export function PermissionCombination({ cases, fallback = null }: PermissionCombinationProps) {
  const { hasPermission, hasAnyRole } = useAuthStore();

  for (const { permissions, roles, component, requireAll = false } of cases) {
    let hasAccess = true;

    // 检查权限
    if (permissions) {
      const permArray = Array.isArray(permissions) ? permissions : [permissions];
      const hasPermissionAccess = requireAll
        ? permArray.every(perm => hasPermission(perm))
        : permArray.some(perm => hasPermission(perm));
      hasAccess = hasAccess && hasPermissionAccess;
    }

    // 检查角色
    if (roles && hasAccess) {
      const roleArray = Array.isArray(roles) ? roles : [roles];
      const hasRoleAccess = requireAll
        ? roleArray.every(role => hasAnyRole([role]))
        : roleArray.some(role => hasAnyRole([role]));
      hasAccess = hasAccess && hasRoleAccess;
    }

    if (hasAccess) {
      return <>{component}</>;
    }
  }

  return <>{fallback}</>;
}
