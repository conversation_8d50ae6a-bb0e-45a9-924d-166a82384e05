import React, { useState, useCallback } from "react";
import {
  Modal,
  Button,
  Toast,
  Typography,
  Spin,
  Empty,
  ImagePreview,
  Space,
  Image,
  Progress,
} from "@douyinfe/semi-ui";
import { IconClose, IconDownload, IconFile, IconEyeOpened } from "@douyinfe/semi-icons";
import { Document, Page, pdfjs } from "react-pdf";
import { useFileLoader } from "@/hooks/useFileLoader";

// 配置PDF.js worker
pdfjs.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjs.version}/pdf.worker.min.js`;

const { Text, Title } = Typography;

interface AttachmentData {
  id: string;
  filename: string;
  originalName: string;
  fileSize: number;
  filePath: string;
  uploadedAt: string;
  uploader: {
    username: string;
    fullName?: string;
  };
}

interface AttachmentPreviewProps {
  visible: boolean;
  attachment: AttachmentData | null;
  onClose: () => void;
}

// 文件类型判断
const getFileType = (filename: string): "image" | "pdf" | "document" | "other" => {
  const ext = filename.toLowerCase().split(".").pop() || "";

  if (["jpg", "jpeg", "png", "gif", "webp", "svg", "bmp"].includes(ext)) {
    return "image";
  }

  if (ext === "pdf") {
    return "pdf";
  }

  if (["doc", "docx", "xls", "xlsx", "ppt", "pptx", "txt"].includes(ext)) {
    return "document";
  }

  return "other";
};

// 格式化文件大小
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return "0 B";
  const k = 1024;
  const sizes = ["B", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`;
};

// PDF预览组件
const PDFPreview: React.FC<{ attachmentId: string; originalFileName: string }> = ({
  attachmentId,
  originalFileName,
}) => {
  const [numPages, setNumPages] = useState<number | null>(null);
  const [pageNumber, setPageNumber] = useState(1);
  const [pdfError, setPdfError] = useState<string | null>(null);

  const { blobUrl, loading, error, progress } = useFileLoader(attachmentId, {
    cacheable: true,
    onError: errorMsg => {
      console.error("PDF文件加载失败:", errorMsg);
    },
  });

  const onDocumentLoadSuccess = useCallback(({ numPages }: { numPages: number }) => {
    setNumPages(numPages);
    setPdfError(null);
  }, []);

  const onDocumentLoadError = useCallback((error: Error) => {
    console.error("PDF渲染失败:", error);
    setPdfError("PDF文件渲染失败，可能文件已损坏");
  }, []);

  const changePage = useCallback((offset: number) => {
    setPageNumber(prevPageNumber => prevPageNumber + offset);
  }, []);

  const previousPage = useCallback(() => changePage(-1), [changePage]);
  const nextPage = useCallback(() => changePage(1), [changePage]);

  // 下载处理函数
  const handleDownload = useCallback(() => {
    window.open(`/api/v1/upload/download/${attachmentId}`, "_blank");
  }, [attachmentId]);

  if (loading) {
    return (
      <div
        style={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          height: "400px",
          flexDirection: "column",
          gap: "16px",
        }}
      >
        <Spin size="large" tip="加载PDF文件中..." />
        {progress > 0 && (
          <div style={{ width: "200px" }}>
            <Progress
              percent={progress}
              showInfo
              format={percent => `${Math.round(percent || 0)}%`}
            />
          </div>
        )}
      </div>
    );
  }

  if (error || pdfError) {
    return (
      <div
        style={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          height: "400px",
          flexDirection: "column",
          gap: "16px",
        }}
      >
        <Empty
          image={<IconFile size="extra-large" />}
          title="PDF加载失败"
          description={error || pdfError || "PDF文件加载失败"}
        />
        <Space>
          <Button theme="solid" type="primary" icon={<IconDownload />} onClick={handleDownload}>
            下载文件
          </Button>
        </Space>
      </div>
    );
  }

  if (!blobUrl) {
    return (
      <div
        style={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          height: "400px",
        }}
      >
        <Spin size="large" tip="准备PDF文件..." />
      </div>
    );
  }

  return (
    <div style={{ textAlign: "center" }}>
      <div
        style={{
          marginBottom: "16px",
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
        }}
      >
        <Space>
          <Button disabled={pageNumber <= 1} onClick={previousPage} size="small">
            上一页
          </Button>
          <Text>
            第 {pageNumber} 页{numPages ? `，共 ${numPages} 页` : ""}
          </Text>
          <Button disabled={pageNumber >= (numPages || 1)} onClick={nextPage} size="small">
            下一页
          </Button>
        </Space>
        <Button theme="borderless" icon={<IconDownload />} onClick={handleDownload} size="small">
          下载
        </Button>
      </div>

      <div
        style={{
          border: "1px solid var(--semi-color-border)",
          borderRadius: "4px",
          overflow: "auto",
          maxHeight: "70vh",
          display: "flex",
          justifyContent: "center",
        }}
      >
        <Document
          file={blobUrl}
          onLoadSuccess={onDocumentLoadSuccess}
          onLoadError={onDocumentLoadError}
          loading={<Spin tip="渲染PDF页面..." />}
        >
          <Page
            pageNumber={pageNumber}
            width={Math.min(800, window.innerWidth * 0.7)}
            loading={<Spin />}
          />
        </Document>
      </div>
    </div>
  );
};

// 图片预览组件
const ImagePreviewComponent: React.FC<{ attachmentId: string; filename: string }> = ({
  attachmentId,
  filename,
}) => {
  const [imageError, setImageError] = useState(false);

  const { blobUrl, loading, error, progress } = useFileLoader(attachmentId, {
    cacheable: true,
    onError: errorMsg => {
      console.error("图片加载失败:", errorMsg);
    },
  });

  const handleImageLoad = useCallback(() => {
    setImageError(false);
  }, []);

  const handleImageError = useCallback(() => {
    setImageError(true);
  }, []);

  // 下载处理函数
  const handleDownload = useCallback(() => {
    window.open(`/api/v1/upload/download/${attachmentId}`, "_blank");
  }, [attachmentId]);

  if (loading) {
    return (
      <div
        style={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          height: "400px",
          flexDirection: "column",
          gap: "16px",
        }}
      >
        <Spin size="large" tip="加载图片中..." />
        {progress > 0 && (
          <div style={{ width: "200px" }}>
            <Progress
              percent={progress}
              showInfo
              format={percent => `${Math.round(percent || 0)}%`}
            />
          </div>
        )}
      </div>
    );
  }

  if (error || imageError) {
    return (
      <div
        style={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          height: "400px",
          flexDirection: "column",
          gap: "16px",
        }}
      >
        <Empty
          image={<IconFile size="extra-large" />}
          title="图片加载失败"
          description={error || "图片文件可能已损坏或不存在"}
        />
        <Button theme="solid" type="primary" icon={<IconDownload />} onClick={handleDownload}>
          下载文件
        </Button>
      </div>
    );
  }

  if (!blobUrl) {
    return (
      <div
        style={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          height: "400px",
        }}
      >
        <Spin size="large" tip="准备图片..." />
      </div>
    );
  }

  return (
    <div style={{ textAlign: "center" }}>
      <div
        style={{
          marginBottom: "16px",
          display: "flex",
          justifyContent: "flex-end",
        }}
      >
        <Button theme="borderless" icon={<IconDownload />} onClick={handleDownload} size="small">
          下载
        </Button>
      </div>

      <Image
        src={blobUrl}
        alt={filename}
        style={{
          maxWidth: "100%",
          maxHeight: "70vh",
        }}
        onLoad={handleImageLoad}
        onError={handleImageError}
        preview={{
          src: blobUrl,
        }}
      />
    </div>
  );
};

// 文档预览组件
const DocumentPreview: React.FC<{ attachmentId: string; filename: string }> = ({
  attachmentId,
  filename,
}) => {
  const ext = filename.toLowerCase().split(".").pop() || "";

  // 下载处理函数
  const handleDownload = useCallback(() => {
    window.open(`/api/v1/upload/download/${attachmentId}`, "_blank");
  }, [attachmentId]);

  // 尝试在线预览
  const handleOnlinePreview = useCallback(() => {
    // 尝试在新窗口中打开，某些浏览器支持Office文档在线预览
    window.open(`/api/v1/upload/download/${attachmentId}`, "_blank");
  }, [attachmentId]);

  return (
    <div
      style={{
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        height: "400px",
        flexDirection: "column",
        gap: "16px",
      }}
    >
      <Empty
        image={<IconFile size="extra-large" />}
        title={`${ext.toUpperCase()} 文档`}
        description="此类型文档需要下载后使用对应应用程序打开"
      />
      <Space>
        <Button theme="solid" type="primary" icon={<IconDownload />} onClick={handleDownload}>
          下载文件
        </Button>
        <Button theme="borderless" onClick={handleOnlinePreview}>
          尝试在线预览
        </Button>
      </Space>
    </div>
  );
};

export const AttachmentPreview: React.FC<AttachmentPreviewProps> = ({
  visible,
  attachment,
  onClose,
}) => {
  if (!attachment) return null;

  const fileType = getFileType(attachment.originalName || attachment.filename);

  const renderPreviewContent = () => {
    switch (fileType) {
      case "image":
        return (
          <ImagePreviewComponent
            attachmentId={attachment.id}
            filename={attachment.originalName || attachment.filename}
          />
        );
      case "pdf":
        return (
          <PDFPreview
            attachmentId={attachment.id}
            originalFileName={attachment.originalName || attachment.filename}
          />
        );
      case "document":
        return (
          <DocumentPreview
            attachmentId={attachment.id}
            filename={attachment.originalName || attachment.filename}
          />
        );
      default:
        return (
          <div
            style={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              height: "400px",
              flexDirection: "column",
              gap: "16px",
            }}
          >
            <Empty
              image={<IconFile size="extra-large" />}
              title="文件预览"
              description="此文件类型不支持在线预览，请下载后查看"
            />
            <Button
              theme="solid"
              type="primary"
              icon={<IconDownload />}
              onClick={() => window.open(`/api/v1/upload/download/${attachment.id}`, "_blank")}
            >
              下载文件
            </Button>
          </div>
        );
    }
  };

  return (
    <Modal
      title={
        <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
          <IconEyeOpened />
          <span>附件预览</span>
        </div>
      }
      visible={visible}
      onCancel={onClose}
      footer={null}
      width="90vw"
      style={{ maxWidth: "1200px" }}
      bodyStyle={{ padding: "24px" }}
      closeIcon={<IconClose />}
    >
      <div style={{ marginBottom: "16px" }}>
        <Title heading={5} style={{ marginBottom: "8px" }}>
          {attachment.originalName || attachment.filename}
        </Title>
        <Space>
          <Text type="tertiary" size="small">
            文件大小: {formatFileSize(attachment.fileSize)}
          </Text>
          <Text type="tertiary" size="small">
            上传者: {attachment.uploader.fullName || attachment.uploader.username}
          </Text>
          <Text type="tertiary" size="small">
            上传时间: {new Date(attachment.uploadedAt).toLocaleString("zh-CN")}
          </Text>
        </Space>
      </div>

      {renderPreviewContent()}
    </Modal>
  );
};

export default AttachmentPreview;
