import React from "react";
import { Typography } from "@douyinfe/semi-ui";
import classNames from "classnames";

interface SafeHTMLDisplayProps {
  content?: string;
  className?: string;
  style?: React.CSSProperties;
  placeholder?: string;
}

/**
 * 安全的 HTML 内容显示组件（简化版）
 * 使用白名单过滤，只允许安全的 HTML 标签和属性
 */
export const SafeHTMLDisplay: React.FC<SafeHTMLDisplayProps> = ({
  content,
  className,
  style,
  placeholder = "暂无内容",
}) => {
  // 如果没有内容，显示占位符
  if (!content || content.trim() === "" || content === "<p></p>" || content === "<br>") {
    return (
      <Typography.Text
        type="tertiary"
        style={{ fontStyle: "italic", ...style }}
        className={className}
      >
        {placeholder}
      </Typography.Text>
    );
  }

  // 安全的HTML标签白名单
  const allowedTags = new Set([
    "p",
    "br",
    "strong",
    "b",
    "em",
    "i",
    "u",
    "s",
    "strike",
    "del",
    "h1",
    "h2",
    "h3",
    "h4",
    "h5",
    "h6",
    "ul",
    "ol",
    "li",
    "blockquote",
    "pre",
    "code",
    "a",
    "span",
    "div",
  ]);

  // 安全的属性白名单
  const allowedAttributes = new Set(["href", "target", "title", "alt", "class", "style"]);

  // 简单的HTML清理函数
  const sanitizeHTML = (html: string): string => {
    // 移除script标签和其他危险标签
    html = html.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, "");
    html = html.replace(/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi, "");
    html = html.replace(/<object\b[^<]*(?:(?!<\/object>)<[^<]*)*<\/object>/gi, "");
    html = html.replace(/<embed\b[^<]*(?:(?!<\/embed>)<[^<]*)*<\/embed>/gi, "");
    html = html.replace(/<link\b[^>]*>/gi, "");
    html = html.replace(/<meta\b[^>]*>/gi, "");
    html = html.replace(/<style\b[^<]*(?:(?!<\/style>)<[^<]*)*<\/style>/gi, "");

    // 移除javascript:协议的链接
    html = html.replace(/href\s*=\s*["']javascript:[^"']*["']/gi, 'href="#"');

    // 移除on*事件属性
    html = html.replace(/\s*on\w+\s*=\s*["'][^"']*["']/gi, "");

    return html;
  };

  const safeContent = sanitizeHTML(content);

  return (
    <>
      <div
        className={classNames("safe-html-display", className)}
        style={style}
        dangerouslySetInnerHTML={{ __html: safeContent }}
      />

      <style>{`
        .safe-html-display {
          color: var(--semi-color-text-0);
          line-height: 1.6;
          font-size: 14px;
          font-family: var(--semi-font-family-regular);
        }
        
        .safe-html-display p {
          margin: 0 0 8px 0;
        }
        
        .safe-html-display p:last-child {
          margin-bottom: 0;
        }
        
        .safe-html-display h1,
        .safe-html-display h2,
        .safe-html-display h3,
        .safe-html-display h4,
        .safe-html-display h5,
        .safe-html-display h6 {
          margin: 16px 0 8px 0;
          color: var(--semi-color-text-0);
          font-weight: 600;
        }
        
        .safe-html-display h1:first-child,
        .safe-html-display h2:first-child,
        .safe-html-display h3:first-child,
        .safe-html-display h4:first-child,
        .safe-html-display h5:first-child,
        .safe-html-display h6:first-child {
          margin-top: 0;
        }
        
        .safe-html-display ul,
        .safe-html-display ol {
          margin: 8px 0;
          padding-left: 24px;
        }
        
        .safe-html-display li {
          margin: 4px 0;
        }
        
        .safe-html-display blockquote {
          margin: 16px 0;
          padding: 12px 16px;
          border-left: 4px solid var(--semi-color-primary);
          background: var(--semi-color-bg-1);
          color: var(--semi-color-text-1);
        }
        
        .safe-html-display code {
          background: var(--semi-color-bg-1);
          padding: 2px 4px;
          border-radius: 3px;
          font-family: var(--semi-font-family-mono);
          font-size: 13px;
        }
        
        .safe-html-display pre {
          background: var(--semi-color-bg-1);
          padding: 12px;
          border-radius: 6px;
          overflow-x: auto;
          margin: 16px 0;
        }
        
        .safe-html-display pre code {
          background: none;
          padding: 0;
        }
        
        .safe-html-display a {
          color: var(--semi-color-primary);
          text-decoration: none;
        }
        
        .safe-html-display a:hover {
          text-decoration: underline;
        }
        
        .safe-html-display strong {
          font-weight: 600;
        }
        
        .safe-html-display em {
          font-style: italic;
        }
        
        .safe-html-display u {
          text-decoration: underline;
        }
        
        .safe-html-display s {
          text-decoration: line-through;
        }
      `}</style>
    </>
  );
};

export default SafeHTMLDisplay;
