import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "@douyinfe/semi-ui";
import { IconActivity, IconPause, IconPlay, IconRefresh } from "@douyinfe/semi-icons";

interface RealTimeIndicatorProps {
  isActive: boolean;
  isPaused: boolean;
  hasUpdates: boolean;
  onTogglePause: () => void;
  onRefresh: () => void;
  lastUpdateTime?: Date;
}

export function RealTimeIndicator({
  isActive,
  isPaused,
  hasUpdates,
  onTogglePause,
  onRefresh,
  lastUpdateTime,
}: RealTimeIndicatorProps) {
  const getStatusColor = () => {
    if (isPaused) return "orange";
    if (isActive) return "green";
    return "grey";
  };

  const getStatusText = () => {
    if (isPaused) return "已暂停";
    if (isActive) return "实时同步中";
    return "离线";
  };

  const formatLastUpdateTime = (time?: Date) => {
    if (!time) return "";
    const now = new Date();
    const diff = Math.floor((now.getTime() - time.getTime()) / 1000);

    if (diff < 60) return `${diff}秒前更新`;
    if (diff < 3600) return `${Math.floor(diff / 60)}分钟前更新`;
    return time.toLocaleTimeString("zh-CN", { hour: "2-digit", minute: "2-digit" });
  };

  return (
    <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
      {/* 实时状态指示器 */}
      <Badge
        dot
        style={{
          backgroundColor: hasUpdates ? "#ff6b35" : `var(--semi-color-${getStatusColor()})`,
        }}
      >
        <Tooltip
          content={
            <div>
              <div>{getStatusText()}</div>
              {lastUpdateTime && (
                <div style={{ fontSize: "12px", opacity: 0.8 }}>
                  {formatLastUpdateTime(lastUpdateTime)}
                </div>
              )}
            </div>
          }
        >
          <div style={{ display: "flex", alignItems: "center", gap: "4px" }}>
            <IconActivity
              size="small"
              style={{
                color: hasUpdates ? "#ff6b35" : `var(--semi-color-${getStatusColor()})`,
                animation: isActive && !isPaused ? "pulse 2s infinite" : "none",
              }}
            />
            <span style={{ fontSize: "12px", color: "var(--semi-color-text-1)" }}>实时</span>
          </div>
        </Tooltip>
      </Badge>

      {/* 控制按钮 */}
      <div style={{ display: "flex", gap: "4px" }}>
        <Tooltip content={isPaused ? "恢复自动刷新" : "暂停自动刷新"}>
          <Button
            theme="borderless"
            size="small"
            icon={isPaused ? <IconPlay /> : <IconPause />}
            onClick={onTogglePause}
            style={{
              color: isPaused ? "var(--semi-color-warning)" : "var(--semi-color-text-2)",
            }}
          />
        </Tooltip>

        <Tooltip content="立即刷新">
          <Button
            theme="borderless"
            size="small"
            icon={<IconRefresh />}
            onClick={onRefresh}
            style={{ color: "var(--semi-color-text-2)" }}
          />
        </Tooltip>
      </div>

      <style>{`
        @keyframes pulse {
          0% { opacity: 1; }
          50% { opacity: 0.5; }
          100% { opacity: 1; }
        }
      `}</style>
    </div>
  );
}
