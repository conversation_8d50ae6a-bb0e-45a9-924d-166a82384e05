import React, { useState } from "react";
import { Typo<PERSON>, Mo<PERSON>, Button } from "@douyinfe/semi-ui";
import { IconMore } from "@douyinfe/semi-icons";
import classNames from "classnames";
import { SafeHTMLDisplay } from "./SafeHTMLDisplay";

interface SimpleRichTextDisplayProps {
  content?: string;
  className?: string;
  style?: React.CSSProperties;
  placeholder?: string;
  maxLength?: number; // 最大显示长度
  showMoreModal?: boolean; // 是否使用 Modal 显示更多内容
  modalTitle?: string; // Modal 标题
}

/**
 * 简化的富文本显示组件
 * 适用于表格、列表等需要轻量化显示的场景
 * 会将HTML内容转换为纯文本显示，避免安全问题
 */
export const SimpleRichTextDisplay: React.FC<SimpleRichTextDisplayProps> = ({
  content,
  className,
  style,
  placeholder = "暂无内容",
  maxLength = 100,
  showMoreModal = false,
  modalTitle = "详细内容",
}) => {
  const [modalVisible, setModalVisible] = useState(false);
  // 如果没有内容，显示占位符
  if (!content || content.trim() === "" || content === "<p></p>" || content === "<br>") {
    return (
      <Typography.Text
        type="tertiary"
        style={{ fontStyle: "italic", ...style }}
        className={className}
      >
        {placeholder}
      </Typography.Text>
    );
  }

  // 将HTML转换为纯文本
  const getTextContent = (html: string): string => {
    // 移除HTML标签
    let text = html.replace(/<[^>]*>/g, "");
    // 解码HTML实体
    text = text.replace(/&lt;/g, "<");
    text = text.replace(/&gt;/g, ">");
    text = text.replace(/&amp;/g, "&");
    text = text.replace(/&quot;/g, '"');
    text = text.replace(/&#39;/g, "'");
    text = text.replace(/&nbsp;/g, " ");
    // 移除多余的空白
    text = text.replace(/\s+/g, " ").trim();
    return text;
  };

  const textContent = getTextContent(content);
  const shouldTruncate = textContent.length > maxLength;
  const displayText = shouldTruncate ? `${textContent.substring(0, maxLength)}...` : textContent;

  // 如果内容被截断且使用 Modal 显示
  if (shouldTruncate && showMoreModal) {
    return (
      <>
        <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
          <Typography.Text
            className={classNames("simple-rich-text-display", className)}
            style={style}
          >
            {displayText}
          </Typography.Text>
          <Button
            theme="borderless"
            type="tertiary"
            size="small"
            icon={<IconMore />}
            onClick={() => setModalVisible(true)}
            style={{ padding: "2px", minWidth: "auto" }}
          />
        </div>

        <Modal
          title={modalTitle}
          visible={modalVisible}
          onCancel={() => setModalVisible(false)}
          width={800}
          footer={<Button onClick={() => setModalVisible(false)}>关闭</Button>}
        >
          <div style={{ maxHeight: "60vh", overflow: "auto", padding: "16px 0" }}>
            {/* 调试信息 */}
            {process.env.NODE_ENV === "development" && (
              <div
                style={{
                  background: "#f0f0f0",
                  padding: "8px",
                  marginBottom: "16px",
                  fontSize: "12px",
                  borderRadius: "4px",
                }}
              >
                内容长度: {content?.length || 0}
                <br />
              </div>
            )}
            <SafeHTMLDisplay content={content} />
          </div>
        </Modal>
      </>
    );
  }

  // 默认显示
  return (
    <Typography.Text className={classNames("simple-rich-text-display", className)} style={style}>
      {displayText}
    </Typography.Text>
  );
};

export default SimpleRichTextDisplay;
