import React from "react";
import { But<PERSON>, Tooltip } from "@douyinfe/semi-ui";
import { usePermissionCheck } from "@/components/guards/PermissionGuard";

interface PermissionButtonProps {
  children?: React.ReactNode;
  permissions?: string | string[];
  roles?: string | string[];
  requireAll?: boolean;
  fallback?: React.ReactNode;
  showTooltip?: boolean;
  tooltipContent?: string;
  disabled?: boolean;
  onClick?: (e: React.MouseEvent) => void;
  className?: string;
  style?: React.CSSProperties;
  theme?: "solid" | "borderless" | "light";
  type?: "primary" | "secondary" | "tertiary" | "warning" | "danger";
  size?: "small" | "default" | "large";
  icon?: React.ReactNode;
  loading?: boolean;
  htmlType?: "button" | "submit" | "reset";
}

export function PermissionButton({
  permissions,
  roles,
  requireAll = false,
  fallback = null,
  showTooltip = true,
  tooltipContent,
  children,
  disabled,
  onClick,
  className,
  style,
  theme,
  type,
  size,
  icon,
  loading,
  htmlType,
}: PermissionButtonProps) {
  const { hasAccess, missingPermissions, missingRoles } = usePermissionCheck(
    permissions,
    roles,
    requireAll
  );

  // 如果没有权限，显示fallback或隐藏
  if (!hasAccess) {
    if (fallback) {
      return <>{fallback}</>;
    }
    return null;
  }

  // 构建权限不足的提示信息
  const getTooltipContent = () => {
    if (tooltipContent) {
      return tooltipContent;
    }

    const missing = [];
    if (missingPermissions.length > 0) {
      missing.push(`缺少权限: ${missingPermissions.join(", ")}`);
    }
    if (missingRoles.length > 0) {
      missing.push(`缺少角色: ${missingRoles.join(", ")}`);
    }

    return missing.length > 0 ? missing.join("; ") : "";
  };

  const button = (
    <Button
      disabled={disabled || !hasAccess}
      onClick={onClick}
      className={className}
      style={style}
      theme={theme}
      type={type}
      size={size}
      icon={icon}
      loading={loading}
      htmlType={htmlType}
    >
      {children}
    </Button>
  );

  // 如果没有权限且需要显示提示
  if (!hasAccess && showTooltip) {
    return <Tooltip content={getTooltipContent()}>{button}</Tooltip>;
  }

  return button;
}

// 带权限的Icon Button
interface PermissionIconButtonProps {
  icon: React.ReactNode;
  tooltip?: string;
  permissions?: string | string[];
  roles?: string | string[];
  requireAll?: boolean;
  showTooltip?: boolean;
  onClick?: (e: React.MouseEvent) => void;
  className?: string;
  style?: React.CSSProperties;
}

export function PermissionIconButton({
  icon,
  tooltip,
  permissions,
  roles,
  requireAll = false,
  showTooltip = true,
  onClick,
  className,
  style,
}: PermissionIconButtonProps) {
  const { hasAccess } = usePermissionCheck(permissions, roles, requireAll);

  if (!hasAccess) {
    return null;
  }

  const button = (
    <Button
      theme="borderless"
      size="small"
      icon={icon}
      onClick={onClick}
      className={className}
      style={style}
    />
  );

  if (tooltip && showTooltip) {
    return <Tooltip content={tooltip}>{button}</Tooltip>;
  }

  return button;
}
