import React from "react";
import { Typography } from "@douyinfe/semi-ui";
import RichTextEditor from "./RichTextEditor";

const { Text } = Typography;

interface RichTextFormFieldProps {
  field: string;
  label?: React.ReactNode;
  labelPosition?: "top" | "left" | "inset";
  labelWidth?: string | number;
  labelAlign?: "left" | "right";
  placeholder?: string;
  disabled?: boolean;
  readOnly?: boolean;
  maxLength?: number;
  minLength?: number;
  style?: React.CSSProperties;
  className?: string;
  height?: number;
  showWordCount?: boolean;
  rules?: Array<any>;
  initValue?: string;
  transform?: {
    input?: (value: any) => any;
    output?: (value: any) => any;
  };
  validate?: (value: any, values: Record<string, any>) => string | Promise<string>;
  trigger?: string | Array<string>;
  fieldClassName?: string;
  pure?: boolean;
  noLabel?: boolean;
  extraText?: React.ReactNode;
  extraTextPosition?: "middle" | "bottom";
  helpText?: React.ReactNode;
  convert?: "upper" | "lower";
  stopValidateWithError?: boolean;
  allowEmpty?: boolean;
  onChange?: (content: string) => void;
  onFocus?: (editor: any) => void;
  onBlur?: (editor: any) => void;
}

export const RichTextFormField: React.FC<RichTextFormFieldProps> = props => {
  const {
    field,
    label,
    labelPosition = "top",
    labelWidth,
    labelAlign = "left",
    placeholder,
    disabled = false,
    readOnly = false,
    maxLength,
    minLength,
    style,
    className,
    height = 200,
    showWordCount = true,
    extraText,
    extraTextPosition = "bottom",
    helpText,
    onChange,
    onFocus,
    onBlur,
    ...restProps
  } = props;

  // 获取Semi Design Form的上下文 - 这里需要从Form.Field扩展
  // 由于Semi Design Form的内部实现复杂，我们创建一个独立的组件
  // 在实际使用中，这个组件将通过高阶组件或其他方式与Form集成

  return (
    <div className={`rich-text-form-field ${className || ""}`} style={style}>
      {label && labelPosition === "top" && (
        <div
          style={{
            marginBottom: "8px",
            display: "flex",
            alignItems: "center",
            justifyContent: labelAlign === "right" ? "flex-end" : "flex-start",
          }}
        >
          <Text strong style={{ fontSize: "14px" }}>
            {label}
          </Text>
          {extraText && extraTextPosition === "middle" && (
            <Text type="tertiary" size="small" style={{ marginLeft: "8px" }}>
              {extraText}
            </Text>
          )}
        </div>
      )}

      {label && labelPosition === "left" && (
        <div style={{ display: "flex", alignItems: "flex-start", gap: "12px" }}>
          <div
            style={{
              width: labelWidth || "80px",
              textAlign: labelAlign,
              paddingTop: "8px",
              flexShrink: 0,
            }}
          >
            <Text strong style={{ fontSize: "14px" }}>
              {label}
            </Text>
          </div>
          <div style={{ flex: 1 }}>
            <RichTextEditor
              placeholder={placeholder}
              disabled={disabled}
              readOnly={readOnly}
              maxLength={maxLength}
              minLength={minLength}
              height={height}
              showWordCount={showWordCount}
              onChange={onChange}
              onFocus={onFocus}
              onBlur={onBlur}
              {...restProps}
            />
          </div>
        </div>
      )}

      {(labelPosition === "top" || labelPosition === "inset") && (
        <RichTextEditor
          placeholder={labelPosition === "inset" ? (label as string) : placeholder}
          disabled={disabled}
          readOnly={readOnly}
          maxLength={maxLength}
          minLength={minLength}
          height={height}
          showWordCount={showWordCount}
          onChange={onChange}
          onFocus={onFocus}
          onBlur={onBlur}
          {...restProps}
        />
      )}

      {helpText && (
        <div style={{ marginTop: "4px" }}>
          <Text type="tertiary" size="small">
            {helpText}
          </Text>
        </div>
      )}

      {extraText && extraTextPosition === "bottom" && (
        <div style={{ marginTop: "4px" }}>
          <Text type="tertiary" size="small">
            {extraText}
          </Text>
        </div>
      )}
    </div>
  );
};

export default RichTextFormField;
