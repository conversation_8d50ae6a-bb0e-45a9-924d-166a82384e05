import React, { useCallback } from "react";
import { withField } from "@douyinfe/semi-ui/lib/es/form";
import RichTextEditor from "./RichTextEditor";

interface FormRichTextAreaProps {
  field?: string;
  label?: React.ReactNode;
  placeholder?: string;
  disabled?: boolean;
  readOnly?: boolean;
  maxCount?: number;
  minCount?: number;
  style?: React.CSSProperties;
  className?: string;
  height?: number;
  showWordCount?: boolean;
  rules?: Array<any>;
  value?: string;
  onChange?: (value: string) => void;
  onFocus?: (editor: any) => void;
  onBlur?: (editor: any) => void;
}

const FormRichTextAreaComponent: React.FC<FormRichTextAreaProps> = ({
  value,
  onChange,
  placeholder = "请输入内容...",
  disabled = false,
  readOnly = false,
  maxCount,
  minCount,
  style,
  className,
  height = 200,
  showWordCount = true,
  onFocus,
  onBlur,
  ...props
}) => {
  const handleChange = useCallback(
    (content: string) => {
      onChange?.(content);
    },
    [onChange]
  );

  const handleBlur = useCallback(
    (editor: any) => {
      onBlur?.(editor);
    },
    [onBlur]
  );

  const handleFocus = useCallback(
    (editor: any) => {
      onFocus?.(editor);
    },
    [onFocus]
  );

  return (
    <RichTextEditor
      value={value}
      placeholder={placeholder}
      disabled={disabled}
      readOnly={readOnly}
      maxLength={maxCount}
      minLength={minCount}
      style={style}
      className={className}
      height={height}
      showWordCount={showWordCount}
      onChange={handleChange}
      onFocus={handleFocus}
      onBlur={handleBlur}
    />
  );
};

// 使用withField包装组件以获得Form集成能力
export const FormRichTextArea = withField(FormRichTextAreaComponent, {
  valueKey: "value",
  onKeyChangeFnName: "onChange",
});

export default FormRichTextArea;
