import React from "react";
import { <PERSON><PERSON>, Switch, Select, Space, Typography, Tag } from "@douyinfe/semi-ui";
import { IconRefresh, IconSignal } from "@douyinfe/semi-icons";
import { ConnectionStatus } from "@/composables/useWebSocket";

const { Text } = Typography;

interface SmartDataRefreshProps {
  // WebSocket连接状态
  wsStatus: ConnectionStatus;
  wsError?: string | null;

  // 自动刷新控制
  autoRefresh: boolean;
  onAutoRefreshChange: (enabled: boolean) => void;

  // 刷新间隔控制
  refreshInterval: number;
  onRefreshIntervalChange: (interval: number) => void;

  // 手动刷新
  onManualRefresh: () => void;

  // 刷新间隔选项
  intervalOptions?: { label: string; value: number }[];
}

export default function SmartDataRefresh({
  wsStatus,
  wsError,
  autoRefresh,
  onAutoRefreshChange,
  refreshInterval,
  onRefreshIntervalChange,
  onManualRefresh,
  intervalOptions = [
    { label: "15秒", value: 15 },
    { label: "30秒", value: 30 },
    { label: "60秒", value: 60 },
    { label: "2分钟", value: 120 },
    { label: "5分钟", value: 300 },
  ],
}: SmartDataRefreshProps) {
  // 判断当前数据更新方式
  const isWebSocketConnected = wsStatus === ConnectionStatus.CONNECTED;
  const isWebSocketConnecting =
    wsStatus === ConnectionStatus.CONNECTING || wsStatus === ConnectionStatus.RECONNECTING;
  const isWebSocketError = wsStatus === ConnectionStatus.ERROR;
  const isWebSocketDisconnected = wsStatus === ConnectionStatus.DISCONNECTED;

  // 获取连接状态显示
  const getConnectionStatusDisplay = () => {
    switch (wsStatus) {
      case ConnectionStatus.CONNECTED:
        return {
          color: "green",
          text: "实时连接",
          icon: <IconSignal style={{ color: "#52c41a" }} />,
        };
      case ConnectionStatus.CONNECTING:
      case ConnectionStatus.RECONNECTING:
        return {
          color: "orange",
          text: "连接中...",
          icon: <IconSignal style={{ color: "#faad14" }} />,
        };
      case ConnectionStatus.ERROR:
        return {
          color: "red",
          text: "连接错误",
          icon: <IconSignal style={{ color: "#ff4d4f" }} />,
        };
      case ConnectionStatus.DISCONNECTED:
      default:
        return {
          color: "red",
          text: "连接断开",
          icon: <IconSignal style={{ color: "#ff4d4f" }} />,
        };
    }
  };

  // 获取数据更新方式说明
  const getDataUpdateMethod = () => {
    if (isWebSocketConnected) {
      return "WebSocket实时推送";
    } else if (autoRefresh) {
      return `HTTP轮询 (${refreshInterval}秒间隔)`;
    } else {
      return "手动刷新";
    }
  };

  const connectionStatus = getConnectionStatusDisplay();

  return (
    <div
      style={{
        display: "flex",
        alignItems: "center",
        justifyContent: "space-between",
        padding: "12px 16px",
        backgroundColor: "#fafafa",
        borderRadius: "6px",
        border: "1px solid #e8e8e8",
      }}
    >
      <div style={{ display: "flex", alignItems: "center", gap: "12px" }}>
        {/* 连接状态指示器 */}
        <div
          style={{
            display: "flex",
            alignItems: "center",
            gap: "6px",
            padding: "4px 8px",
            backgroundColor: "#fff",
            borderRadius: "4px",
            border: `1px solid #${connectionStatus.color === "green" ? "52c41a" : connectionStatus.color === "orange" ? "faad14" : "ff4d4f"}`,
          }}
        >
          {connectionStatus.icon}
          <Text
            size="small"
            style={{
              color: `#${connectionStatus.color === "green" ? "52c41a" : connectionStatus.color === "orange" ? "faad14" : "ff4d4f"}`,
            }}
          >
            {connectionStatus.text}
          </Text>
        </div>

        {/* 数据更新方式说明 */}
        <Text size="small" type="secondary">
          数据更新: {getDataUpdateMethod()}
        </Text>

        {/* 错误信息显示 */}
        {wsError && (
          <Tag color="red" size="small">
            错误: {wsError}
          </Tag>
        )}
      </div>

      <div style={{ display: "flex", alignItems: "center", gap: "12px" }}>
        {/* 自动刷新开关 */}
        <div style={{ display: "flex", alignItems: "center", gap: "6px" }}>
          <Text size="small">自动刷新</Text>
          <Switch
            checked={autoRefresh}
            onChange={onAutoRefreshChange}
            disabled={isWebSocketConnected} // WebSocket连接时禁用自动刷新
            size="small"
          />
        </div>

        {/* 刷新间隔选择器 */}
        {autoRefresh && !isWebSocketConnected && (
          <Select
            value={refreshInterval}
            onChange={value => onRefreshIntervalChange(value as number)}
            size="small"
            style={{ width: "80px" }}
            dropdownStyle={{ minWidth: "80px" }}
          >
            {intervalOptions.map(option => (
              <Select.Option key={option.value} value={option.value}>
                {option.label}
              </Select.Option>
            ))}
          </Select>
        )}

        {/* 手动刷新按钮 */}
        <Button
          size="small"
          icon={<IconRefresh />}
          onClick={onManualRefresh}
          loading={isWebSocketConnecting}
        >
          手动刷新
        </Button>
      </div>
    </div>
  );
}
