import React from "react";
import { Tag, Tooltip, Progress } from "@douyinfe/semi-ui";
import { IconClock, IconAlertTriangle, IconTickCircle } from "@douyinfe/semi-icons";

interface SLAStatusIndicatorProps {
  slaTemplate?: {
    id: string;
    name: string;
    responseTime: number; // 响应时间(小时)
    resolutionTime: number; // 解决时间(小时)
  };
  createdAt: string;
  startTime?: string;
  endTime?: string;
  firstResponseAt?: string;
  status: string;
  size?: "small" | "default" | "large";
  showProgress?: boolean;
}

export function SLAStatusIndicator({
  slaTemplate,
  createdAt,
  startTime,
  endTime,
  firstResponseAt,
  status,
  size = "small",
  showProgress = false,
}: SLAStatusIndicatorProps) {
  // 如果没有SLA模板，显示无SLA状态
  if (!slaTemplate) {
    return (
      <Tag color="grey" size={size}>
        无SLA
      </Tag>
    );
  }

  const now = new Date();
  const createdTime = new Date(createdAt);
  const responseDeadline = new Date(
    createdTime.getTime() + slaTemplate.responseTime * 60 * 60 * 1000
  );
  const resolutionDeadline = new Date(
    createdTime.getTime() + slaTemplate.resolutionTime * 60 * 60 * 1000
  );

  // 计算响应SLA状态
  const getResponseStatus = () => {
    if (firstResponseAt) {
      const responseTime = new Date(firstResponseAt);
      return responseTime <= responseDeadline ? "met" : "breached";
    }

    // 如果还没有首次响应
    if (["RESOLVED", "CLOSED"].includes(status)) {
      return "breached"; // 已完成但没有响应记录，视为违约
    }

    return now > responseDeadline ? "breached" : "pending";
  };

  // 计算解决SLA状态
  const getResolutionStatus = () => {
    if (endTime) {
      const resolvedTime = new Date(endTime);
      return resolvedTime <= resolutionDeadline ? "met" : "breached";
    }

    // 如果还没有解决
    if (["RESOLVED", "CLOSED"].includes(status)) {
      return "breached"; // 状态已完成但没有结束时间，视为违约
    }

    return now > resolutionDeadline ? "breached" : "pending";
  };

  const responseStatus = getResponseStatus();
  const resolutionStatus = getResolutionStatus();

  // 计算时间进度
  const getTimeProgress = (deadline: Date) => {
    const total = deadline.getTime() - createdTime.getTime();
    const elapsed = now.getTime() - createdTime.getTime();
    return Math.min(Math.max((elapsed / total) * 100, 0), 100);
  };

  // 获取整体SLA状态
  const getOverallStatus = () => {
    if (responseStatus === "breached" || resolutionStatus === "breached") {
      return { status: "breached", color: "red", text: "SLA违约", icon: IconAlertTriangle };
    }

    if (responseStatus === "met" && resolutionStatus === "met") {
      return { status: "met", color: "green", text: "SLA达标", icon: IconTickCircle };
    }

    // 检查是否临近截止时间
    const responseProgress = getTimeProgress(responseDeadline);
    const resolutionProgress = getTimeProgress(resolutionDeadline);

    if (responseStatus === "pending" && responseProgress > 80) {
      return { status: "warning", color: "orange", text: "SLA预警", icon: IconAlertTriangle };
    }

    if (resolutionStatus === "pending" && resolutionProgress > 80) {
      return { status: "warning", color: "orange", text: "SLA预警", icon: IconAlertTriangle };
    }

    return { status: "normal", color: "blue", text: "SLA正常", icon: IconClock };
  };

  const overallStatus = getOverallStatus();
  const Icon = overallStatus.icon;

  // 格式化剩余时间
  const formatTimeRemaining = (deadline: Date) => {
    const remaining = deadline.getTime() - now.getTime();
    if (remaining <= 0) return "已超时";

    const hours = Math.floor(remaining / (1000 * 60 * 60));
    const minutes = Math.floor((remaining % (1000 * 60 * 60)) / (1000 * 60));

    if (hours > 24) {
      const days = Math.floor(hours / 24);
      return `${days}天${hours % 24}小时`;
    }

    if (hours > 0) {
      return `${hours}小时${minutes}分钟`;
    }

    return `${minutes}分钟`;
  };

  const tooltipContent = (
    <div style={{ maxWidth: "300px" }}>
      <div style={{ fontWeight: "bold", marginBottom: "8px" }}>SLA模板: {slaTemplate.name}</div>

      <div style={{ marginBottom: "6px" }}>
        <strong>响应时间要求:</strong> {slaTemplate.responseTime}小时
        <br />
        <span
          style={{
            color:
              responseStatus === "met"
                ? "#52c41a"
                : responseStatus === "breached"
                  ? "#ff4d4f"
                  : "#1890ff",
          }}
        >
          {responseStatus === "met"
            ? "✓ 已达标"
            : responseStatus === "breached"
              ? "✗ 已违约"
              : `剩余: ${formatTimeRemaining(responseDeadline)}`}
        </span>
      </div>

      <div>
        <strong>解决时间要求:</strong> {slaTemplate.resolutionTime}小时
        <br />
        <span
          style={{
            color:
              resolutionStatus === "met"
                ? "#52c41a"
                : resolutionStatus === "breached"
                  ? "#ff4d4f"
                  : "#1890ff",
          }}
        >
          {resolutionStatus === "met"
            ? "✓ 已达标"
            : resolutionStatus === "breached"
              ? "✗ 已违约"
              : `剩余: ${formatTimeRemaining(resolutionDeadline)}`}
        </span>
      </div>

      {showProgress && (
        <div style={{ marginTop: "8px" }}>
          <div style={{ fontSize: "12px", marginBottom: "4px" }}>解决进度:</div>
          <Progress
            percent={getTimeProgress(resolutionDeadline)}
            size="small"
            stroke={
              resolutionStatus === "breached"
                ? "#ff4d4f"
                : getTimeProgress(resolutionDeadline) > 80
                  ? "#ff7a00"
                  : "#1890ff"
            }
            showInfo={false}
          />
        </div>
      )}
    </div>
  );

  return (
    <Tooltip content={tooltipContent} position="top">
      <Tag color={overallStatus.color as any} size={size} style={{ cursor: "help" }}>
        <Icon size="small" style={{ marginRight: "4px" }} />
        {overallStatus.text}
      </Tag>
    </Tooltip>
  );
}
