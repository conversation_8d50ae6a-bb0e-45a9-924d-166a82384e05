import React from "react";
import { Typography } from "@douyinfe/semi-ui";
import { Editor } from "@tinymce/tinymce-react";
import classNames from "classnames";

interface RichTextDisplayProps {
  content?: string;
  className?: string;
  style?: React.CSSProperties;
  placeholder?: string;
}

export const RichTextDisplay: React.FC<RichTextDisplayProps> = ({
  content,
  className,
  style,
  placeholder = "暂无内容",
}) => {
  // 如果没有内容，显示占位符
  if (!content || content.trim() === "" || content === "<p></p>" || content === "<br>") {
    return (
      <Typography.Text
        type="tertiary"
        style={{ fontStyle: "italic", ...style }}
        className={className}
      >
        {placeholder}
      </Typography.Text>
    );
  }

  // TinyMCE 预览模式配置
  const previewConfig = {
    height: "auto",
    menubar: false,
    toolbar: false,
    statusbar: false,
    content_style: `
      body { 
        font-family: var(--semi-font-family-regular); 
        font-size: 14px; 
        line-height: 1.6; 
        color: var(--semi-color-text-0);
        margin: 0;
        padding: 8px;
        background: transparent;
      }
      p { margin: 0 0 8px 0; }
      p:last-child { margin-bottom: 0; }
      h1, h2, h3, h4, h5, h6 { 
        margin: 16px 0 8px 0; 
        color: var(--semi-color-text-0); 
        font-weight: 600; 
      }
      h1:first-child, h2:first-child, h3:first-child, 
      h4:first-child, h5:first-child, h6:first-child { 
        margin-top: 0; 
      }
      ul, ol { margin: 8px 0; padding-left: 24px; }
      li { margin: 4px 0; }
      blockquote { 
        margin: 16px 0; 
        padding: 12px 16px; 
        border-left: 4px solid var(--semi-color-primary); 
        background: var(--semi-color-bg-1); 
        color: var(--semi-color-text-1); 
      }
      code { 
        background: var(--semi-color-bg-1); 
        padding: 2px 4px; 
        border-radius: 3px; 
        font-family: var(--semi-font-family-mono); 
        font-size: 13px; 
      }
      pre { 
        background: var(--semi-color-bg-1); 
        padding: 12px; 
        border-radius: 6px; 
        overflow-x: auto; 
        margin: 16px 0; 
      }
      pre code { background: none; padding: 0; }
      a { color: var(--semi-color-primary); text-decoration: none; }
      a:hover { text-decoration: underline; }
      strong { font-weight: 600; }
      em { font-style: italic; }
      u { text-decoration: underline; }
      s { text-decoration: line-through; }
    `,
    branding: false,
    skin: false,
    content_css: false,
    plugins: [], // 不加载任何插件，减少体积
    init_instance_callback: (editor: any) => {
      // 确保编辑器完全只读
      editor.mode.set("readonly");

      // 隐藏编辑器边框和其他UI元素
      const container = editor.getContainer();
      if (container) {
        container.style.border = "none";
        container.style.borderRadius = "0";

        // 隐藏可能存在的工具栏、菜单栏等
        const toolbar = container.querySelector(".tox-toolbar");
        const menubar = container.querySelector(".tox-menubar");
        const statusbar = container.querySelector(".tox-statusbar");

        if (toolbar) toolbar.style.display = "none";
        if (menubar) menubar.style.display = "none";
        if (statusbar) statusbar.style.display = "none";
      }
    },
    setup: (editor: any) => {
      // 禁用所有按键事件
      editor.on("keydown keyup keypress", (e: any) => {
        e.preventDefault();
        e.stopPropagation();
        return false;
      });

      // 禁用右键菜单
      editor.on("contextmenu", (e: any) => {
        e.preventDefault();
        return false;
      });

      // 禁用拖拽
      editor.on("dragstart dragend drop", (e: any) => {
        e.preventDefault();
        return false;
      });
    },
  };

  return (
    <>
      <div className={classNames("rich-text-display", className)} style={style}>
        <Editor
          tinymceScriptSrc="/tinymce/js/tinymce/tinymce.min.js"
          value={content}
          init={previewConfig}
          disabled={true}
        />
      </div>

      <style>{`
        .rich-text-display .tox-tinymce {
          border: none !important;
          border-radius: 0 !important;
          box-shadow: none !important;
        }
        
        .rich-text-display .tox-edit-area {
          border: none !important;
        }
        
        .rich-text-display .tox-edit-area__iframe {
          border: none !important;
        }
        
        .rich-text-display .tox-toolbar,
        .rich-text-display .tox-menubar,
        .rich-text-display .tox-statusbar {
          display: none !important;
        }
        
        /* 确保内容样式 */
        .rich-text-display iframe {
          min-height: auto !important;
        }
      `}</style>
    </>
  );
};

export default RichTextDisplay;
