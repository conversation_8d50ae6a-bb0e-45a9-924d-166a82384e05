import React, { useState, useEffect, useRef, useCallback } from "react";
import { Editor } from "@tinymce/tinymce-react";
import { Typography, Toast } from "@douyinfe/semi-ui";
import classNames from "classnames";

const { Text } = Typography;

interface RichTextEditorProps {
  value?: string;
  defaultValue?: string;
  placeholder?: string;
  disabled?: boolean;
  readOnly?: boolean;
  maxLength?: number;
  minLength?: number;
  style?: React.CSSProperties;
  className?: string;
  height?: number;
  showWordCount?: boolean;
  onChange?: (content: string, editor?: any) => void;
  onFocus?: (editor: any) => void;
  onBlur?: (editor: any) => void;
}

// TinyMCE 默认配置
const defaultConfig = {
  height: 200,
  menubar: false,
  plugins: [
    "advlist",
    "autolink",
    "lists",
    "link",
    "charmap",
    "preview",
    "anchor",
    "searchreplace",
    "visualblocks",
    "code",
    "fullscreen",
    "insertdatetime",
    "table",
    "code",
    "help",
    "wordcount",
  ],
  toolbar:
    "undo redo | blocks | " +
    "bold italic forecolor backcolor | alignleft aligncenter " +
    "alignright alignjustify | bullist numlist outdent indent | " +
    "removeformat | help",
  content_style:
    "body { font-family: -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; font-size: 14px; line-height: 1.6; }",
  branding: false,
  statusbar: false,
};

export const RichTextEditor: React.FC<RichTextEditorProps> = ({
  value,
  defaultValue = "",
  placeholder = "请输入内容...",
  disabled = false,
  readOnly = false,
  maxLength,
  minLength,
  style,
  className,
  height = 200,
  showWordCount = false,
  onChange,
  onFocus,
  onBlur,
}) => {
  const [content, setContent] = useState(value || defaultValue);
  const [wordCount, setWordCount] = useState(0);
  const editorRef = useRef<any>(null);

  // 同步外部value变化
  useEffect(() => {
    if (value !== undefined && value !== content) {
      setContent(value);
    }
  }, [value]);

  // 计算字符数（去除HTML标签）
  const calculateWordCount = useCallback((htmlContent: string) => {
    const textContent = htmlContent.replace(/<[^>]*>/g, "");
    return textContent.length;
  }, []);

  // 更新字符数
  useEffect(() => {
    const count = calculateWordCount(content);
    setWordCount(count);
  }, [content, calculateWordCount]);

  const handleChange = useCallback(
    (htmlContent: string, editor: any) => {
      const textLength = calculateWordCount(htmlContent);

      // 检查最大长度限制
      if (maxLength && textLength > maxLength) {
        Toast.warning(`内容长度不能超过 ${maxLength} 个字符`);
        return;
      }

      setContent(htmlContent);
      onChange?.(htmlContent, editor);
    },
    [maxLength, calculateWordCount, onChange]
  );

  const handleInit = useCallback((evt: any, editor: any) => {
    editorRef.current = editor;
  }, []);

  const handleFocus = useCallback(
    (editor: any) => {
      onFocus?.(editor);
    },
    [onFocus]
  );

  const handleBlur = useCallback(
    (editor: any) => {
      // 检查最小长度限制
      if (minLength && wordCount < minLength) {
        Toast.warning(`内容长度不能少于 ${minLength} 个字符`);
      }
      onBlur?.(editor);
    },
    [minLength, wordCount, onBlur]
  );

  // 合并 TinyMCE 配置
  const editorConfig = {
    ...defaultConfig,
    height,
    placeholder,
    toolbar_mode: "wrap" as const,
    setup: (editor: any) => {
      if (readOnly || disabled) {
        editor.mode.set("readonly");
      }
    },
  };

  return (
    <div className={classNames("rich-text-editor", className)} style={style}>
      <div
        style={{
          border: "1px solid var(--semi-color-border)",
          borderRadius: "3px",
          background: disabled ? "var(--semi-color-disabled-bg)" : "var(--semi-color-bg-0)",
        }}
      >
        <Editor
          tinymceScriptSrc="/tinymce/js/tinymce/tinymce.min.js"
          value={content}
          init={editorConfig}
          onEditorChange={handleChange}
          onInit={handleInit}
          onFocus={handleFocus}
          onBlur={handleBlur}
          disabled={disabled}
        />
      </div>

      {showWordCount && (
        <div
          style={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            marginTop: "4px",
          }}
        >
          <div />
          <Text
            type="tertiary"
            size="small"
            style={{
              color:
                maxLength && wordCount > maxLength * 0.9
                  ? "var(--semi-color-warning)"
                  : "var(--semi-color-text-2)",
            }}
          >
            {wordCount}
            {maxLength ? ` / ${maxLength}` : ""}
          </Text>
        </div>
      )}

      <style>{`
        .rich-text-editor .tox-tinymce {
          border: none !important;
          border-radius: 0 !important;
        }
        
        .rich-text-editor .tox-toolbar {
          border-top: none !important;
          border-left: none !important;
          border-right: none !important;
          border-bottom: 1px solid var(--semi-color-border) !important;
          background: var(--semi-color-bg-1) !important;
        }
        
        .rich-text-editor .tox-toolbar__primary {
          background: var(--semi-color-bg-1) !important;
        }
        
        .rich-text-editor .tox-edit-area {
          border: none !important;
        }
        
        .rich-text-editor .tox-edit-area__iframe {
          border: none !important;
        }
        
        .rich-text-editor .tox-tbtn {
          color: var(--semi-color-text-0) !important;
        }
        
        .rich-text-editor .tox-tbtn:hover {
          background: var(--semi-color-hover-bg) !important;
        }
        
        .rich-text-editor .tox-tbtn--enabled {
          background: var(--semi-color-primary-light-default) !important;
        }
        
        .rich-text-editor .tox-toolbar__group {
          border-right: 1px solid var(--semi-color-border) !important;
        }
      `}</style>
    </div>
  );
};

export default RichTextEditor;
