import React, { Component, ReactNode } from "react";
import { Banner } from "@douyinfe/semi-ui";

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void;
}

interface State {
  hasError: boolean;
  error?: Error;
}

class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    // 更新状态以显示错误UI
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    // 记录错误到控制台
    console.error("ErrorBoundary caught an error:", error, errorInfo);

    // 调用自定义错误处理回调
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }
  }

  render() {
    if (this.state.hasError) {
      // 如果提供了自定义fallback，使用它
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // 默认错误UI
      return (
        <Banner
          type="danger"
          description="组件渲染出错，请刷新页面重试"
          closeIcon={null}
          style={{ margin: "8px 0" }}
        />
      );
    }

    return this.props.children;
  }
}

// Hook版本的错误边界（用于函数组件内部错误处理）
export const useErrorHandler = () => {
  const [error, setError] = React.useState<Error | null>(null);

  React.useEffect(() => {
    if (error) {
      console.error("Hook error handler:", error);
      // 在开发环境中抛出错误以便调试
      if (process.env.NODE_ENV === "development") {
        setTimeout(() => {
          throw error;
        });
      }
    }
  }, [error]);

  const handleError = React.useCallback((error: Error) => {
    setError(error);
  }, []);

  const clearError = React.useCallback(() => {
    setError(null);
  }, []);

  return { error, handleError, clearError };
};

export default ErrorBoundary;
