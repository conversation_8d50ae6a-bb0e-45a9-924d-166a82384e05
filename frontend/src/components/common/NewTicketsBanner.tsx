import React, { useEffect, useState } from "react";
import { Banner, Button, Space } from "@douyinfe/semi-ui";
import { IconCalendar, IconClose, IconUserAdd } from "@douyinfe/semi-icons";

interface NewTicketsBannerProps {
  newTicketsCount: number;
  onViewNew: () => void;
  onDismiss: () => void;
  autoHide?: boolean;
  autoHideDelay?: number;
}

export function NewTicketsBanner({
  newTicketsCount,
  onViewNew,
  onDismiss,
  autoHide = true,
  autoHideDelay = 8000, // 8秒后自动隐藏
}: NewTicketsBannerProps) {
  const [visible, setVisible] = useState(false);
  const [isAnimating, setIsAnimating] = useState(false);

  useEffect(() => {
    if (newTicketsCount > 0) {
      setVisible(true);
      setIsAnimating(true);

      // 动画完成后移除动画状态
      const animationTimer = setTimeout(() => {
        setIsAnimating(false);
      }, 500);

      // 自动隐藏
      let autoHideTimer: NodeJS.Timeout;
      if (autoHide) {
        autoHideTimer = setTimeout(() => {
          handleDismiss();
        }, autoHideDelay);
      }

      return () => {
        clearTimeout(animationTimer);
        if (autoHideTimer) clearTimeout(autoHideTimer);
      };
    } else {
      setVisible(false);
    }
  }, [newTicketsCount, autoHide, autoHideDelay]);

  const handleDismiss = () => {
    setVisible(false);
    setTimeout(() => {
      onDismiss();
    }, 300); // 等待退出动画完成
  };

  const handleViewNew = () => {
    onViewNew();
    handleDismiss();
  };

  if (!visible || newTicketsCount === 0) {
    return null;
  }

  return (
    <div
      style={{
        position: "sticky",
        top: 0,
        zIndex: 1000,
        marginBottom: "16px",
        animation: isAnimating ? "slideDownFadeIn 0.5s ease-out" : undefined,
      }}
    >
      <Banner
        fullMode={false}
        type="info"
        bordered
        icon={<IconCalendar />}
        closeIcon={<IconClose />}
        onClose={handleDismiss}
        title={
          <div
            style={{
              display: "flex",
              alignItems: "center",
              justifyContent: "space-between",
              width: "100%",
            }}
          >
            <div>
              <strong>发现 {newTicketsCount} 个新工单</strong>
              <div style={{ fontSize: "12px", opacity: 0.8 }}>
                有新的服务工单需要处理，点击查看最新内容
              </div>
            </div>

            <Space>
              <Button
                theme="solid"
                type="primary"
                size="small"
                icon={<IconUserAdd />}
                onClick={handleViewNew}
              >
                查看新工单
              </Button>
            </Space>
          </div>
        }
        style={{
          background:
            "linear-gradient(90deg, var(--semi-color-primary-light-default) 0%, var(--semi-color-info-light-default) 100%)",
          border: "1px solid var(--semi-color-primary-light-active)",
          borderRadius: "8px",
          boxShadow: "0 2px 8px rgba(0, 0, 0, 0.1)",
        }}
      />

      <style>{`
        @keyframes slideDownFadeIn {
          0% {
            transform: translateY(-100%);
            opacity: 0;
          }
          100% {
            transform: translateY(0);
            opacity: 1;
          }
        }
        
        @keyframes slideUpFadeOut {
          0% {
            transform: translateY(0);
            opacity: 1;
          }
          100% {
            transform: translateY(-100%);
            opacity: 0;
          }
        }
      `}</style>
    </div>
  );
}
