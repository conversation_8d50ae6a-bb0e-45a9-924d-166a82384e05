import React, { useState, useEffect, useRef, forwardRef, useImperativeHandle } from "react";
import {
  IconSignal,
  IconActivity,
  IconUser,
  Icon<PERSON>lock,
  IconAlertTriangle,
  IconTick as <PERSON>con<PERSON><PERSON><PERSON>,
  IconLink as IconConnection,
  IconClose as IconX,
} from "@douyinfe/semi-icons";
import { useGlobalWebSocket } from "@/contexts/WebSocketContext";
import { ConnectionStatus, RealtimeChannelType } from "@/composables/useWebSocket";

interface SystemMetrics {
  cpu: number;
  memory: number;
  disk: number;
  network: number;
}

interface OnlineUser {
  userId: string;
  username: string;
  fullName: string;
  lastActivity: string;
}

interface Alert {
  id: number;
  title: string;
  message: string;
  severity: string;
  timestamp: string;
}

interface Notification {
  id: number;
  title: string;
  message: string;
  type: string;
  persistent?: boolean;
  timestamp: string;
}

interface Activity {
  id: number;
  type: string;
  fullName?: string;
  action?: string;
  resource?: string;
  message?: string;
  timestamp: string;
}

export interface RealtimeMonitorRef {
  refresh: () => void;
  getStatus: () => ConnectionStatus;
}

const RealtimeMonitor = forwardRef<RealtimeMonitorRef>((props, ref) => {
  const [systemMetrics, setSystemMetrics] = useState<SystemMetrics | null>(null);
  const [onlineUsers, setOnlineUsers] = useState<{ count: number; users: OnlineUser[] } | null>(
    null
  );
  const [alerts, setAlerts] = useState<Alert[]>([]);
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [recentActivities, setRecentActivities] = useState<Activity[]>([]);

  // 使用全局WebSocket连接
  const webSocket = useGlobalWebSocket();
  const state = webSocket.state;
  const {
    onConnect,
    onSystemMetrics,
    onSystemAlert,
    onOnlineUsers,
    onNotification,
    onAuditLog,
    onUserActivity,
  } = webSocket;

  // 暴露给父组件的方法
  useImperativeHandle(ref, () => ({
    refresh: () => {
      // 触发WebSocket重新连接或刷新
      onConnect?.(() => {});
    },
    getStatus: () => state.status,
  }));

  // 连接状态计算
  const getConnectionStatusClass = () => {
    switch (state.status) {
      case ConnectionStatus.CONNECTED:
        return "connected";
      case ConnectionStatus.CONNECTING:
      case ConnectionStatus.RECONNECTING:
        return "connecting";
      case ConnectionStatus.ERROR:
        return "error";
      default:
        return "disconnected";
    }
  };

  const getConnectionStatusIcon = () => {
    switch (state.status) {
      case ConnectionStatus.CONNECTED:
        return "IconSignal";
      case ConnectionStatus.CONNECTING:
      case ConnectionStatus.RECONNECTING:
        return "IconRefresh";
      case ConnectionStatus.ERROR:
        return "IconAlertTriangle";
      default:
        return "IconSignalSlash";
    }
  };

  const getConnectionStatusText = () => {
    switch (state.status) {
      case ConnectionStatus.CONNECTED:
        return "已连接";
      case ConnectionStatus.CONNECTING:
        return "连接中...";
      case ConnectionStatus.RECONNECTING:
        return `重连中... (${state.reconnectCount})`;
      case ConnectionStatus.ERROR:
        return state.error || "连接错误";
      default:
        return "未连接";
    }
  };

  // 事件处理
  useEffect(() => {
    onConnect((data: any) => {
      console.log("WebSocket已连接:", data);
    });

    onSystemMetrics((data: any) => {
      setSystemMetrics(data);
    });

    onSystemAlert((data: any) => {
      setAlerts(prev => {
        const newAlert = {
          id: Date.now(),
          ...data,
        };
        const newAlerts = [newAlert, ...prev];
        // 保持最新10条告警
        return newAlerts.slice(0, 10);
      });
    });

    onOnlineUsers((data: any) => {
      setOnlineUsers(data);
    });

    onNotification((data: any) => {
      const notification = {
        id: Date.now(),
        ...data,
        timestamp: new Date().toISOString(),
      };

      setNotifications(prev => [notification, ...prev]);

      // 自动移除非持久通知
      if (!notification.persistent) {
        setTimeout(() => {
          removeNotification(notification.id);
        }, 5000);
      }
    });

    onAuditLog((data: any) => {
      setRecentActivities(prev => {
        const newActivity = {
          id: Date.now(),
          type: "audit",
          ...data,
          timestamp: new Date().toISOString(),
        };
        const newActivities = [newActivity, ...prev];
        // 保持最新20条活动
        return newActivities.slice(0, 20);
      });
    });

    onUserActivity((data: any) => {
      setRecentActivities(prev => {
        const newActivity = {
          id: Date.now(),
          type: "user_activity",
          ...data,
        };
        const newActivities = [newActivity, ...prev];
        // 保持最新20条活动
        return newActivities.slice(0, 20);
      });
    });
  }, [
    onConnect,
    onSystemMetrics,
    onSystemAlert,
    onOnlineUsers,
    onNotification,
    onAuditLog,
    onUserActivity,
  ]);

  // 工具函数
  const getMetricClass = (value: number): string => {
    if (value >= 90) return "critical";
    if (value >= 80) return "warning";
    if (value >= 60) return "caution";
    return "normal";
  };

  const getAlertIcon = (severity: string): string => {
    switch (severity.toLowerCase()) {
      case "critical":
        return "IconAlertTriangle";
      case "high":
        return "IconAlertCircle";
      case "medium":
        return "IconInfoCircle";
      case "low":
      default:
        return "IconBell";
    }
  };

  const getAlertColor = (severity: string): string => {
    switch (severity.toLowerCase()) {
      case "critical":
        return "red";
      case "high":
        return "orange";
      case "medium":
        return "yellow";
      case "low":
      default:
        return "blue";
    }
  };

  const getActivityIcon = (type: string): string => {
    switch (type) {
      case "audit":
        return "IconFileText";
      case "user_activity":
        return "IconUser";
      default:
        return "IconBell";
    }
  };

  const formatActivity = (activity: Activity): string => {
    if (activity.type === "audit") {
      return `${activity.fullName} ${activity.action} ${activity.resource}`;
    } else if (activity.type === "user_activity") {
      return `${activity.fullName} ${activity.action}`;
    }
    return activity.message || "未知活动";
  };

  const formatTime = (timestamp: string): string => {
    const date = new Date(timestamp);
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const minutes = Math.floor(diff / 60000);

    if (minutes < 1) return "刚刚";
    if (minutes < 60) return `${minutes}分钟前`;
    if (minutes < 1440) return `${Math.floor(minutes / 60)}小时前`;
    return date.toLocaleDateString();
  };

  const removeNotification = (id: number): void => {
    setNotifications(prev => prev.filter(n => n.id !== id));
  };

  return (
    <div className="realtime-monitor space-y-6">
      {/* 连接状态指示器 */}
      <div
        className={`connection-status flex items-center justify-between px-4 py-2 rounded-lg text-sm font-medium ${getConnectionStatusClass()}`}
      >
        <div className="flex items-center space-x-2">
          <IconSignal />
          <span>{getConnectionStatusText()}</span>
        </div>
        {state.stats && (
          <div className="connection-stats text-xs opacity-75">
            收: {state.stats.messagesReceived} | 发: {state.stats.messagesSent}
          </div>
        )}
      </div>

      {/* 实时系统指标 */}
      {systemMetrics && (
        <div className="metrics-grid grid grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="metric-card bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm border">
            <div className="metric-header flex items-center space-x-2 text-sm font-medium text-gray-600 dark:text-gray-400">
              <IconActivity />
              <span>CPU使用率</span>
            </div>
            <div className="metric-value text-2xl font-bold mt-2">{systemMetrics.cpu}%</div>
            <div className="metric-bar mt-3 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
              <div
                className={`metric-progress h-full rounded-full transition-all duration-300 ${getMetricClass(systemMetrics.cpu)}`}
                style={{ width: `${systemMetrics.cpu}%` }}
              ></div>
            </div>
          </div>

          <div className="metric-card bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm border">
            <div className="metric-header flex items-center space-x-2 text-sm font-medium text-gray-600 dark:text-gray-400">
              <IconActivity />
              <span>内存使用率</span>
            </div>
            <div className="metric-value text-2xl font-bold mt-2">{systemMetrics.memory}%</div>
            <div className="metric-bar mt-3 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
              <div
                className={`metric-progress h-full rounded-full transition-all duration-300 ${getMetricClass(systemMetrics.memory)}`}
                style={{ width: `${systemMetrics.memory}%` }}
              ></div>
            </div>
          </div>

          <div className="metric-card bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm border">
            <div className="metric-header flex items-center space-x-2 text-sm font-medium text-gray-600 dark:text-gray-400">
              <IconActivity />
              <span>磁盘使用率</span>
            </div>
            <div className="metric-value text-2xl font-bold mt-2">{systemMetrics.disk}%</div>
            <div className="metric-bar mt-3 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
              <div
                className={`metric-progress h-full rounded-full transition-all duration-300 ${getMetricClass(systemMetrics.disk)}`}
                style={{ width: `${systemMetrics.disk}%` }}
              ></div>
            </div>
          </div>

          <div className="metric-card bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm border">
            <div className="metric-header flex items-center space-x-2 text-sm font-medium text-gray-600 dark:text-gray-400">
              <IconConnection />
              <span>网络使用率</span>
            </div>
            <div className="metric-value text-2xl font-bold mt-2">{systemMetrics.network}%</div>
            <div className="metric-bar mt-3 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
              <div
                className={`metric-progress h-full rounded-full transition-all duration-300 ${getMetricClass(systemMetrics.network)}`}
                style={{ width: `${systemMetrics.network}%` }}
              ></div>
            </div>
          </div>
        </div>
      )}

      {/* 在线用户 */}
      {onlineUsers && (
        <div className="online-users bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border">
          <div className="section-header flex items-center space-x-2 text-lg font-semibold mb-4">
            <IconUser />
            <span>在线用户 ({onlineUsers.count})</span>
          </div>
          <div className="users-list space-y-3">
            {onlineUsers.users.slice(0, 5).map(user => (
              <div key={user.userId} className="user-item flex items-center space-x-3">
                <div className="user-avatar w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-medium">
                  {user.fullName?.charAt(0) || user.username?.charAt(0)}
                </div>
                <div className="user-info flex-1">
                  <div className="user-name font-medium text-sm">
                    {user.fullName || user.username}
                  </div>
                  <div className="user-activity text-xs text-gray-500 dark:text-gray-400">
                    {formatTime(user.lastActivity)}
                  </div>
                </div>
                <div className="user-status w-2 h-2 rounded-full bg-green-500"></div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* 系统告警 */}
      {alerts.length > 0 && (
        <div className="system-alerts bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border">
          <div className="section-header flex items-center space-x-2 text-lg font-semibold mb-4">
            <IconAlertTriangle />
            <span>系统告警</span>
          </div>
          <div className="alerts-list space-y-3">
            {alerts.slice(0, 3).map(alert => (
              <div
                key={alert.id}
                className={`alert-item flex items-start space-x-3 p-3 rounded-lg border-l-4 alert-${alert.severity.toLowerCase()}`}
              >
                <div className="alert-icon flex-shrink-0">
                  <IconAlertTriangle className="text-red-500" />
                </div>
                <div className="alert-content flex-1">
                  <div className="alert-title font-medium text-sm">{alert.title}</div>
                  <div className="alert-message text-sm text-gray-600 dark:text-gray-400 mt-1">
                    {alert.message}
                  </div>
                  <div className="alert-time text-xs text-gray-500 dark:text-gray-400 mt-1">
                    {formatTime(alert.timestamp)}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* 最新活动 */}
      {recentActivities.length > 0 && (
        <div className="recent-activities bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border">
          <div className="section-header flex items-center space-x-2 text-lg font-semibold mb-4">
            <IconClock />
            <span>最新活动</span>
          </div>
          <div className="activities-list space-y-3">
            {recentActivities.slice(0, 5).map(activity => (
              <div key={activity.id} className="activity-item flex items-center space-x-3">
                <div className="activity-icon w-8 h-8 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center flex-shrink-0">
                  <IconActivity />
                </div>
                <div className="activity-content flex-1">
                  <div className="activity-text text-sm">{formatActivity(activity)}</div>
                  <div className="activity-time text-xs text-gray-500 dark:text-gray-400">
                    {formatTime(activity.timestamp)}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* 通知区域 */}
      {notifications.length > 0 && (
        <div className="notifications fixed top-4 right-4 space-y-2 z-50">
          {notifications.map(notification => (
            <div
              key={notification.id}
              className={`notification-item flex items-start space-x-3 p-4 rounded-lg shadow-lg border max-w-sm notification-${notification.type}`}
            >
              <div className="notification-content flex-1">
                <div className="notification-title font-medium text-sm">{notification.title}</div>
                <div className="notification-message text-sm opacity-90 mt-1">
                  {notification.message}
                </div>
              </div>
              <button
                className="notification-close flex-shrink-0 p-1 rounded-full hover:bg-black/10 dark:hover:bg-white/10"
                onClick={() => removeNotification(notification.id)}
              >
                <IconX />
              </button>
            </div>
          ))}
        </div>
      )}

      <style>{`
        .connection-status.connected {
          @apply bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200;
        }

        .connection-status.connecting {
          @apply bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200;
        }

        .connection-status.error {
          @apply bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200;
        }

        .connection-status.disconnected {
          @apply bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200;
        }

        .metric-progress.normal {
          @apply bg-green-500;
        }

        .metric-progress.caution {
          @apply bg-blue-500;
        }

        .metric-progress.warning {
          @apply bg-yellow-500;
        }

        .metric-progress.critical {
          @apply bg-red-500;
        }

        .alert-item.alert-critical {
          @apply bg-red-50 border-red-500 dark:bg-red-900/20;
        }

        .alert-item.alert-high {
          @apply bg-orange-50 border-orange-500 dark:bg-orange-900/20;
        }

        .alert-item.alert-medium {
          @apply bg-yellow-50 border-yellow-500 dark:bg-yellow-900/20;
        }

        .alert-item.alert-low {
          @apply bg-blue-50 border-blue-500 dark:bg-blue-900/20;
        }

        .notification-item.notification-success {
          @apply bg-green-100 border-green-500 text-green-800 dark:bg-green-900 dark:text-green-200;
        }

        .notification-item.notification-warning {
          @apply bg-yellow-100 border-yellow-500 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200;
        }

        .notification-item.notification-error {
          @apply bg-red-100 border-red-500 text-red-800 dark:bg-red-900 dark:text-red-200;
        }

        .notification-item.notification-info {
          @apply bg-blue-100 border-blue-500 text-blue-800 dark:bg-blue-900 dark:text-blue-200;
        }
      `}</style>
    </div>
  );
});

export default RealtimeMonitor;
