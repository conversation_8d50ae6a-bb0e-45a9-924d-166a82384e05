import React, { useState, useRef, useCallback } from "react";
import {
  Modal,
  Form,
  Toast,
  Button,
  Row,
  Col,
  Typography,
  Divider,
  Space,
} from "@douyinfe/semi-ui";
import CustomerService from "@/services/customer";
import type { CustomerCreateData } from "@/types";

interface CustomerCreateModalProps {
  visible: boolean;
  onCancel: () => void;
  onSuccess: () => void;
}

interface CustomerFormData extends CustomerCreateData {}

const { Title, Text } = Typography;

const customerTypeOptions = [
  { label: "企业客户", value: "ENTERPRISE" },
  { label: "个人客户", value: "INDIVIDUAL" },
  { label: "政府机构", value: "GOVERNMENT" },
  { label: "非营利组织", value: "NONPROFIT" },
];

const customerLevelOptions = [
  { label: "基础", value: "BASIC" },
  { label: "标准", value: "STANDARD" },
  { label: "高级", value: "PREMIUM" },
  { label: "企业", value: "ENTERPRISE" },
];

// 默认表单值 - 使用常量避免重复创建对象
const DEFAULT_FORM_VALUES: CustomerFormData = {
  name: "",
  code: "",
  type: "ENTERPRISE",
  level: "BASIC",
  industry: "",
  contactPerson: "",
  contactPhone: "",
  contactEmail: "",
  address: "",
  description: "",
  isVip: false,
};

export function CustomerCreateModal({ visible, onCancel, onSuccess }: CustomerCreateModalProps) {
  const [loading, setLoading] = useState(false);
  const formRef = useRef<Form<CustomerFormData>>(null);

  const handleSubmit = useCallback(
    async (values: CustomerFormData) => {
      setLoading(true);
      try {
        console.log("提交的客户数据:", values);
        const response = await CustomerService.createCustomer(values);
        console.log("创建客户响应:", response);

        if (response.success) {
          Toast.success(response.message || "客户创建成功");
          formRef.current?.formApi.setValues(DEFAULT_FORM_VALUES); // 重置为默认值
          onSuccess();
        } else {
          Toast.error(response.message || "创建失败");
        }
      } catch (error: any) {
        console.error("创建客户失败:", error);
        Toast.error(error.message || "创建失败，请稍后重试");
      } finally {
        setLoading(false);
      }
    },
    [onSuccess]
  );

  const handleCancel = useCallback(() => {
    formRef.current?.formApi.setValues(DEFAULT_FORM_VALUES); // 重置为默认值
    onCancel();
  }, [onCancel]);

  return (
    <Modal
      title={
        <div style={{ textAlign: "center", width: "100%" }}>
          <Title heading={4} style={{ margin: 0, color: "var(--semi-color-text-0)" }}>
            新增客户
          </Title>
        </div>
      }
      visible={visible}
      onCancel={handleCancel}
      footer={null}
      width={800}
      style={{ maxWidth: "90vw" }}
      bodyStyle={{ padding: "24px" }}
    >
      <Form<CustomerFormData>
        ref={formRef}
        onSubmit={handleSubmit}
        layout="vertical"
        style={{ padding: 0 }}
        initValues={DEFAULT_FORM_VALUES}
        labelPosition="top"
        labelAlign="left"
      >
        {/* 基本信息 */}
        <div style={{ marginBottom: "32px" }}>
          <div style={{ marginBottom: "20px" }}>
            <Title heading={5} style={{ margin: 0, color: "var(--semi-color-text-0)" }}>
              基本信息
            </Title>
            <Text size="small" style={{ color: "var(--semi-color-text-2)" }}>
              填写客户的基本信息
            </Text>
          </div>

          <Row gutter={[16, 16]}>
            <Col span={12}>
              <Form.Input
                field="name"
                label="客户名称"
                placeholder="请输入客户名称"
                rules={[{ required: true, message: "请输入客户名称" }]}
                style={{ width: "100%" }}
                size="large"
              />
            </Col>
            <Col span={12}>
              <Form.Input
                field="code"
                label="客户编码"
                placeholder="请输入客户编码"
                rules={[{ required: true, message: "请输入客户编码" }]}
                style={{ width: "100%" }}
                size="large"
              />
            </Col>
          </Row>

          <Row gutter={[16, 16]}>
            <Col span={12}>
              <Form.Select
                field="type"
                label="客户类型"
                placeholder="请选择客户类型"
                optionList={customerTypeOptions}
                rules={[{ required: true, message: "请选择客户类型" }]}
                style={{ width: "100%" }}
                size="large"
              />
            </Col>
            <Col span={12}>
              <Form.Select
                field="level"
                label="客户等级"
                placeholder="请选择客户等级"
                optionList={customerLevelOptions}
                rules={[{ required: true, message: "请选择客户等级" }]}
                style={{ width: "100%" }}
                size="large"
              />
            </Col>
          </Row>

          <Row gutter={[16, 16]}>
            <Col span={12}>
              <Form.Input
                field="industry"
                label="所属行业"
                placeholder="请输入所属行业"
                style={{ width: "100%" }}
                size="large"
              />
            </Col>
            <Col span={12}>
              <div style={{ paddingTop: "32px" }}>
                <Form.Checkbox field="isVip" noLabel>
                  <Space>
                    <Text style={{ color: "var(--semi-color-text-0)" }}>VIP客户</Text>
                    <Text size="small" style={{ color: "var(--semi-color-text-2)" }}>
                      标记为重要客户
                    </Text>
                  </Space>
                </Form.Checkbox>
              </div>
            </Col>
          </Row>
        </div>

        <Divider style={{ margin: "32px 0" }} />

        {/* 联系信息 */}
        <div style={{ marginBottom: "32px" }}>
          <div style={{ marginBottom: "20px" }}>
            <Title heading={5} style={{ margin: 0, color: "var(--semi-color-text-0)" }}>
              联系信息
            </Title>
            <Text size="small" style={{ color: "var(--semi-color-text-2)" }}>
              填写客户联系人的相关信息
            </Text>
          </div>

          <Row gutter={[16, 16]}>
            <Col span={8}>
              <Form.Input
                field="contactPerson"
                label="联系人"
                placeholder="请输入联系人姓名"
                style={{ width: "100%" }}
                size="large"
              />
            </Col>
            <Col span={8}>
              <Form.Input
                field="contactPhone"
                label="联系电话"
                placeholder="请输入联系电话"
                style={{ width: "100%" }}
                size="large"
              />
            </Col>
            <Col span={8}>
              <Form.Input
                field="contactEmail"
                label="联系邮箱"
                placeholder="请输入联系邮箱"
                rules={[{ type: "email", message: "请输入有效的邮箱地址" }]}
                style={{ width: "100%" }}
                size="large"
              />
            </Col>
          </Row>
        </div>

        <Divider style={{ margin: "32px 0" }} />

        {/* 其他信息 */}
        <div style={{ marginBottom: "32px" }}>
          <div style={{ marginBottom: "20px" }}>
            <Title heading={5} style={{ margin: 0, color: "var(--semi-color-text-0)" }}>
              其他信息
            </Title>
            <Text size="small" style={{ color: "var(--semi-color-text-2)" }}>
              填写客户的地址和描述信息
            </Text>
          </div>

          <Form.TextArea
            field="address"
            label="客户地址"
            placeholder="请输入客户详细地址"
            rows={3}
            style={{ width: "100%" }}
          />

          <div style={{ marginTop: "16px" }}>
            <Form.TextArea
              field="description"
              label="客户描述"
              placeholder="请输入客户描述信息，如业务范围、特殊需求等"
              rows={4}
              style={{ width: "100%" }}
            />
          </div>
        </div>

        {/* 操作按钮 */}
        <div
          style={{
            display: "flex",
            justifyContent: "flex-end",
            gap: "12px",
            marginTop: "40px",
            paddingTop: "24px",
            borderTop: "1px solid var(--semi-color-border)",
          }}
        >
          <Button
            theme="borderless"
            onClick={handleCancel}
            size="large"
            style={{ minWidth: "80px" }}
          >
            取消
          </Button>
          <Button
            htmlType="submit"
            type="primary"
            loading={loading}
            size="large"
            style={{ minWidth: "100px" }}
          >
            创建客户
          </Button>
        </div>
      </Form>
    </Modal>
  );
}
