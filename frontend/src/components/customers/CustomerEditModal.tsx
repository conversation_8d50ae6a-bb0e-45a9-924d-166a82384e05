import React, { useState, useEffect } from "react";
import { Modal, Form, Toast, Button, Row, Col } from "@douyinfe/semi-ui";
import CustomerService from "@/services/customer";
import type { Customer, CustomerUpdateData } from "@/types";

interface CustomerEditModalProps {
  visible: boolean;
  customer: Customer;
  onCancel: () => void;
  onSuccess: () => void;
}

interface CustomerFormData extends Omit<CustomerUpdateData, "id"> {}

const customerTypeOptions = [
  { label: "企业客户", value: "ENTERPRISE" },
  { label: "个人客户", value: "INDIVIDUAL" },
  { label: "政府机构", value: "GOVERNMENT" },
  { label: "非营利组织", value: "NONPROFIT" },
];

export function CustomerEditModal({
  visible,
  customer,
  onCancel,
  onSuccess,
}: CustomerEditModalProps) {
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState<CustomerFormData>({
    name: "",
    code: "",
    type: "ENTERPRISE",
    industry: "",
    contactPerson: "",
    contactPhone: "",
    contactEmail: "",
    address: "",
    description: "",
    isVip: false,
  });

  // 当客户数据变化时，更新表单
  useEffect(() => {
    if (customer && visible) {
      setFormData({
        name: customer.name,
        code: customer.code,
        type: customer.type,
        industry: customer.industry || "",
        contactPerson: customer.contactPerson,
        contactPhone: customer.contactPhone,
        contactEmail: customer.contactEmail,
        address: customer.address || "",
        description: customer.description || "",
        isVip: customer.isVip,
      });
    }
  }, [customer, visible]);

  const handleSubmit = async (values: CustomerFormData) => {
    setLoading(true);
    try {
      const response = await CustomerService.updateCustomer(customer.id, values);

      if (response.success) {
        Toast.success(response.message || "客户信息更新成功");
        onSuccess();
      } else {
        Toast.error(response.message || "更新失败");
      }
    } catch (error: any) {
      console.error("更新客户失败:", error);
      Toast.error(error.message || "更新失败，请稍后重试");
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    onCancel();
  };

  return (
    <Modal
      title="编辑客户"
      visible={visible}
      onCancel={handleCancel}
      footer={null}
      width={600}
      style={{ maxWidth: "90vw" }}
    >
      <Form
        onSubmit={handleSubmit}
        labelPosition="left"
        labelWidth={80}
        style={{ padding: "20px 0" }}
        initValues={formData}
      >
        <Row gutter={[16, 16]}>
          <Col span={12}>
            <Form.Input
              field="name"
              label="客户名称"
              placeholder="请输入客户名称"
              rules={[{ required: true, message: "请输入客户名称" }]}
            />
          </Col>
          <Col span={12}>
            <Form.Input
              field="code"
              label="客户编码"
              placeholder="请输入客户编码"
              rules={[{ required: true, message: "请输入客户编码" }]}
            />
          </Col>
        </Row>

        <Row gutter={[16, 16]}>
          <Col span={12}>
            <Form.Select
              field="type"
              label="客户类型"
              placeholder="请选择客户类型"
              optionList={customerTypeOptions}
              rules={[{ required: true, message: "请选择客户类型" }]}
            />
          </Col>
          <Col span={12}>
            <Form.Input field="industry" label="所属行业" placeholder="请输入所属行业" />
          </Col>
        </Row>

        <Row gutter={[16, 16]}>
          <Col span={12}>
            <Form.Input
              field="contactPerson"
              label="联系人"
              placeholder="请输入联系人姓名"
              rules={[{ required: true, message: "请输入联系人姓名" }]}
            />
          </Col>
          <Col span={12}>
            <Form.Input
              field="contactPhone"
              label="联系电话"
              placeholder="请输入联系电话"
              rules={[{ required: true, message: "请输入联系电话" }]}
            />
          </Col>
        </Row>

        <Form.Input
          field="contactEmail"
          label="联系邮箱"
          placeholder="请输入联系邮箱"
          rules={[
            { required: true, message: "请输入联系邮箱" },
            { type: "email", message: "请输入有效的邮箱地址" },
          ]}
        />

        <Form.TextArea field="address" label="客户地址" placeholder="请输入客户地址" rows={2} />

        <Form.TextArea field="description" label="客户描述" placeholder="请输入客户描述" rows={3} />

        <Form.Checkbox field="isVip" noLabel>
          VIP客户
        </Form.Checkbox>

        <div
          className="flex justify-end space-x-3 mt-6 pt-4 border-t"
          style={{ borderColor: "var(--semi-color-border)" }}
        >
          <Button theme="borderless" onClick={handleCancel}>
            取消
          </Button>
          <Button htmlType="submit" type="primary" loading={loading}>
            更新
          </Button>
        </div>
      </Form>
    </Modal>
  );
}
