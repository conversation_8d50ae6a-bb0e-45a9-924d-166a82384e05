import React from "react";
import { Button, Tooltip } from "@douyinfe/semi-ui";
import { IconMoon, IconSun } from "@douyinfe/semi-icons";
import { useUIStore } from "@/stores";

export function ThemeToggle() {
  const { theme, setTheme } = useUIStore();

  const toggleTheme = () => {
    const newTheme = theme === "light" ? "dark" : "light";
    setTheme(newTheme);
  };

  const isDark = theme === "dark";
  const tooltipContent = `切换到${isDark ? "浅色" : "深色"}主题`;

  return (
    <Tooltip content={tooltipContent} position="bottom">
      <Button
        theme="borderless"
        type="tertiary"
        icon={isDark ? <IconSun /> : <IconMoon />}
        onClick={toggleTheme}
        size="default"
        style={{
          color: "var(--semi-color-text-1)",
          backgroundColor: "transparent",
        }}
        className="!p-2 hover:!bg-gray-100 dark:hover:!bg-gray-700 transition-colors"
      />
    </Tooltip>
  );
}
