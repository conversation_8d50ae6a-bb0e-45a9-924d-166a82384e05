import React, { useEffect } from "react";
import { Outlet } from "react-router-dom";
import { Layout, BackTop, Space } from "@douyinfe/semi-ui";
import { Sidebar } from "./Sidebar";
import { useNotifications } from "@/hooks/useNotifications";
import {
  IconMail,
  IconCustomerSupport,
  IconGithubLogo,
  IconSetting,
  IconActivity,
  IconGlobe,
} from "@douyinfe/semi-icons";

const { Sider, Content, Footer } = Layout;

export function BasicLayout() {
  // 初始化通知系统
  useNotifications();

  return (
    <div className="min-h-screen" style={{ backgroundColor: "var(--ops-bg-tertiary)" }}>
      <Layout className="min-h-screen">
        {/* 现代化侧边栏（固定高度：视窗高度 - 页脚高度） */}
        <Sider
          style={{
            backgroundColor: "var(--semi-color-bg-1)",
            boxShadow: "var(--ops-shadow-2)",
            zIndex: "var(--ops-z-sidebar)",
            minHeight: "calc(100vh)",
            overflow: "auto",
            borderRight: "1px solid var(--semi-color-border)",
          }}
        >
          <Sidebar />
        </Sider>

        {/* 主内容区域 */}
        <Layout>
          {/* 内容区域 */}
          <Content
            className="flex flex-col"
            style={{
              padding: "var(--ops-spacing-lg)",
              backgroundColor: "var(--ops-bg-tertiary)",
              height: "calc(100vh)",
              overflowY: "auto",
              position: "relative",
            }}
          >
            {/* 内容容器 */}
            <div
              className="flex-1 w-full max-w-full"
              style={{
                backgroundColor: "transparent",
                borderRadius: "var(--ops-border-radius-lg)",
              }}
            >
              <Outlet />
            </div>

            {/* 回到顶部按钮 */}
            <BackTop
              style={{
                backgroundColor: "var(--ops-primary-color)",
                color: "white",
                borderRadius: "var(--ops-border-radius-xl)",
                boxShadow: "var(--ops-shadow-3)",
                border: "none",
              }}
              text="回到顶部"
              visibilityHeight={300}
              duration={300}
            />
            {/* 现代化页脚 */}
            <Footer
              style={{
                backgroundColor: "var(--semi-color-bg-1)",
                borderTop: "1px solid var(--semi-color-border)",
                padding: "var(--ops-spacing-lg) var(--ops-spacing-xl)",
                boxShadow: "0 -2px 8px rgba(0, 0, 0, 0.04)",
              }}
            >
              <div className="flex flex-col md:flex-row justify-between items-center gap-4">
                {/* 左侧：版权信息 */}
                <div className="flex flex-col md:flex-row items-center gap-4">
                  <div className="flex items-center gap-2">
                    <IconActivity
                      size="large"
                      style={{
                        color: "var(--ops-primary-color)",
                        fontSize: "24px",
                      }}
                    />
                    <span
                      className="font-semibold text-lg"
                      style={{ color: "var(--ops-text-primary)" }}
                    >
                      运维管理系统
                    </span>
                  </div>
                  <div className="text-sm" style={{ color: "var(--ops-text-secondary)" }}>
                    © {new Date().getFullYear()} 运维服务管理平台. 保留所有权利.
                  </div>
                </div>

                {/* 右侧：快捷链接和状态 */}
                <div className="flex flex-col md:flex-row items-center gap-6">
                  {/* 系统状态 */}
                  <div className="flex items-center gap-2">
                    <div
                      className="w-2 h-2 rounded-full animate-pulse"
                      style={{ backgroundColor: "var(--ops-success-color)" }}
                    ></div>
                    <span
                      className="text-sm font-medium"
                      style={{ color: "var(--ops-success-color)" }}
                    >
                      系统正常运行
                    </span>
                  </div>

                  {/* 快捷操作 */}
                  <Space spacing="loose">
                    <div
                      className="flex items-center gap-1 px-3 py-1 rounded-md cursor-pointer transition-all duration-200 hover:bg-gray-100"
                      style={{
                        color: "var(--ops-text-secondary)",
                        fontSize: "var(--ops-font-size-sm)",
                      }}
                      onClick={() => window.open("mailto:<EMAIL>")}
                    >
                      <IconMail size="small" />
                      <span>技术支持</span>
                    </div>

                    <div
                      className="flex items-center gap-1 px-3 py-1 rounded-md cursor-pointer transition-all duration-200 hover:bg-gray-100"
                      style={{
                        color: "var(--ops-text-secondary)",
                        fontSize: "var(--ops-font-size-sm)",
                      }}
                    >
                      <IconCustomerSupport size="small" />
                      <span>在线客服</span>
                    </div>

                    <div
                      className="flex items-center gap-1 px-3 py-1 rounded-md cursor-pointer transition-all duration-200 hover:bg-gray-100"
                      style={{
                        color: "var(--ops-text-secondary)",
                        fontSize: "var(--ops-font-size-sm)",
                      }}
                    >
                      <IconGlobe size="small" />
                      <span>帮助文档</span>
                    </div>
                  </Space>
                </div>
              </div>

              {/* 底部信息栏 */}
              <div
                className="mt-4 pt-4 border-t text-center"
                style={{
                  borderColor: "var(--semi-color-border)",
                  color: "var(--ops-text-tertiary)",
                  fontSize: "var(--ops-font-size-xs)",
                }}
              >
                <div className="flex flex-col md:flex-row justify-center items-center gap-4">
                  <span>构建版本: v2.1.0</span>
                  <span className="hidden md:inline">|</span>
                  <span>最后更新: {new Date().toLocaleDateString()}</span>
                  <span className="hidden md:inline">|</span>
                  <span>运行环境: {process.env.NODE_ENV === "production" ? "生产" : "开发"}</span>
                </div>
              </div>
            </Footer>
          </Content>
        </Layout>
      </Layout>
    </div>
  );
}
