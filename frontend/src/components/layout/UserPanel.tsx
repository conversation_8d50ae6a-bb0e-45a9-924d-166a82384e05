import React from "react";
import { Badge, Space, Tooltip } from "@douyinfe/semi-ui";
import { IconPulse } from "@douyinfe/semi-icons";
import { useAuthStore } from "@/stores";
import { useSystemHealth } from "@/hooks/useDashboard";
import { ErrorBoundary } from "@/components/common";
import { UserMenu } from "./UserMenu";

function HeaderContent() {
  const { user } = useAuthStore();
  const { systemHealth } = useSystemHealth();

  // 获取系统健康状态颜色
  const getHealthColor = (health: string) => {
    switch (health) {
      case "healthy":
        return "#52c41a";
      case "warning":
        return "#fa8c16";
      case "critical":
        return "#ff4d4f";
      default:
        return "#d9d9d9";
    }
  };

  return (
    <div
      className="flex items-center justify-between px-2 py-2"
      style={{
        background: "transparent",
        minHeight: "auto",
      }}
    >
      {/* 网络健康状态 */}
      <Tooltip
        content={`网络状态: ${systemHealth === "healthy" ? "正常" : systemHealth === "warning" ? "异常" : "故障"}`}
      >
        <div className="flex items-center justify-start gap-2">
          <IconPulse
            style={{
              color: getHealthColor(systemHealth),
              fontSize: "16px",
            }}
          />
          <span className="text-xs text-gray-600">
            {systemHealth === "healthy" ? "在线" : "离线"}
          </span>
        </div>
      </Tooltip>

      {/* 用户菜单 */}
      {user ? <UserMenu user={user as any} /> : null}
    </div>
  );
}

export function UserPanel() {
  return (
    <ErrorBoundary
      fallback={
        <div
          className="flex items-center justify-center px-2 py-2"
          style={{
            background: "transparent",
            minHeight: "auto",
          }}
        >
          <Space>
            <Badge dot type="danger" />
            <span className="text-xs text-gray-500">离线</span>
          </Space>
        </div>
      }
      onError={(error, errorInfo) => {
        console.error("Header component error:", error, errorInfo);
      }}
    >
      <HeaderContent />
    </ErrorBoundary>
  );
}
