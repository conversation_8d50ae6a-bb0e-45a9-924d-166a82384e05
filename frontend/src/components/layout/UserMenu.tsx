import React, { useState } from "react";
import { Dropdown, Avatar, Typography } from "@douyinfe/semi-ui";
import { IconUser, IconKey, IconExit, IconChevronDown } from "@douyinfe/semi-icons";
import { useNavigate } from "react-router-dom";
import { useAuthStore } from "@/stores";
import { ProfileModal } from "@/components/profile/ProfileModal";
import { PasswordModal } from "@/components/profile/PasswordModal";
import type { User } from "shared/types";

interface UserMenuProps {
  user: User & {
    role: {
      name: string;
      permissions: string[];
    };
  };
}

const { Text } = Typography;

export function UserMenu({ user }: UserMenuProps) {
  const { logout } = useAuthStore();
  const [profileModalVisible, setProfileModalVisible] = useState(false);
  const [passwordModalVisible, setPasswordModalVisible] = useState(false);
  const navigate = useNavigate();

  const handleLogout = async () => {
    await logout();
  };

  const handleMenuClick = (key: string) => {
    switch (key) {
      case "profile":
        setProfileModalVisible(true);
        break;
      case "password":
        setPasswordModalVisible(true);
        break;
      case "logout":
        handleLogout();
        break;
      case "my-api-keys":
        navigate("/me/api-keys");
        break;
      default:
        break;
    }
  };

  const handleProfileSuccess = () => {
    setProfileModalVisible(false);
  };

  const handlePasswordSuccess = () => {
    setPasswordModalVisible(false);
  };

  // 用户头像背景色生成
  const getAvatarColor = (name: string) => {
    const colors = [
      "var(--semi-color-primary)",
      "var(--semi-color-secondary)",
      "var(--semi-color-tertiary)",
      "var(--semi-color-warning)",
      "var(--semi-color-success)",
    ];
    const index = name.charCodeAt(0) % colors.length;
    return colors[index];
  };

  return (
    <>
      <Dropdown
        trigger="click"
        position="topRight"
        showTick={false}
        render={
          <div
            className="bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 py-1 min-w-48"
            style={{
              backgroundColor: "var(--semi-color-bg-2)",
              borderColor: "var(--semi-color-border)",
              boxShadow: "var(--semi-shadow-elevated)",
            }}
          >
            {/* 用户信息头部 */}
            <div className="px-4 py-3 border-b" style={{ borderColor: "var(--semi-color-border)" }}>
              <Text strong style={{ color: "var(--semi-color-text-0)" }}>
                {user.fullName || user.username}
              </Text>
              <br />
              <Text size="small" type="secondary">
                {user.email}
              </Text>
              <br />
              <Text size="small" type="tertiary">
                {user.role?.name || "未知角色"}
              </Text>
            </div>

            {/* 菜单项 */}
            <div className="py-1">
              <div
                className="flex items-center px-4 py-2 text-sm cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                onClick={() => handleMenuClick("profile")}
                style={{ color: "var(--semi-color-text-0)" }}
              >
                <IconUser className="mr-3" />
                个人设置
              </div>

              <div
                className="flex items-center px-4 py-2 text-sm cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                onClick={() => handleMenuClick("password")}
                style={{ color: "var(--semi-color-text-0)" }}
              >
                <IconKey className="mr-3" />
                修改密码
              </div>

              <div
                className="flex items-center px-4 py-2 text-sm cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                onClick={() => handleMenuClick("my-api-keys")}
                style={{ color: "var(--semi-color-text-0)" }}
              >
                <IconKey className="mr-3" />
                我的 API 密钥
              </div>

              <hr className="my-1" style={{ borderColor: "var(--semi-color-border)" }} />

              <div
                className="flex items-center px-4 py-2 text-sm cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                onClick={() => handleMenuClick("logout")}
                style={{ color: "var(--semi-color-danger)" }}
              >
                <IconExit className="mr-3" />
                退出登录
              </div>
            </div>
          </div>
        }
      >
        <div className="flex items-center space-x-2 px-2 py-1 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer transition-colors">
          <Avatar
            size="small"
            style={{
              backgroundColor: getAvatarColor(user.fullName || user.username),
              color: "white",
              fontWeight: 500,
            }}
          >
            {(user.fullName || user.username).charAt(0).toUpperCase()}
          </Avatar>

          <div className="hidden md:block text-left min-w-0">
            <Text
              strong
              size="small"
              ellipsis={{ showTooltip: true }}
              style={{
                color: "var(--semi-color-text-0)",
                fontSize: "13px",
              }}
            >
              {user.fullName || user.username}
            </Text>
            <br />
            <Text
              size="small"
              type="tertiary"
              ellipsis={{ showTooltip: true }}
              style={{ fontSize: "11px" }}
            >
              {user.role?.name || "未知角色"}
            </Text>
          </div>

          <IconChevronDown size="small" style={{ color: "var(--semi-color-text-2)" }} />
        </div>
      </Dropdown>

      {/* 个人设置弹窗 */}
      <ProfileModal
        visible={profileModalVisible}
        onCancel={() => setProfileModalVisible(false)}
        onSuccess={handleProfileSuccess}
      />

      {/* 修改密码弹窗 */}
      <PasswordModal
        visible={passwordModalVisible}
        onCancel={() => setPasswordModalVisible(false)}
        onSuccess={handlePasswordSuccess}
      />
    </>
  );
}
