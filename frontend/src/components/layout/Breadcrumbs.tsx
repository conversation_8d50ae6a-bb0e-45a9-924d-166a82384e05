import React from "react";
import { Link } from "react-router-dom";
import { Breadcrumb } from "@douyinfe/semi-ui";
import { IconChevronRight } from "@douyinfe/semi-icons";
import type { BreadcrumbItem } from "@/types";

interface BreadcrumbsProps {
  items: BreadcrumbItem[];
}

export function Breadcrumbs({ items }: BreadcrumbsProps) {
  if (items.length === 0) return null;

  // 转换为 Semi-UI Breadcrumb 需要的格式
  const breadcrumbItems = items.map((item, index) => ({
    path: item.href || "",
    name: (
      <span className="flex items-center">
        {item.icon && <span className="mr-1">{item.icon}</span>}
        {item.text}
      </span>
    ),
    href: item.href,
    disabled: item.disabled,
    // 最后一项是当前页面，不需要链接
    isLast: index === items.length - 1,
  }));

  return (
    <Breadcrumb
      separator={<IconChevronRight size="small" />}
      className="!text-sm"
      style={{
        color: "var(--semi-color-text-1)",
      }}
    >
      {breadcrumbItems.map((item, index) => (
        <Breadcrumb.Item
          key={index}
          href={item.isLast ? undefined : item.href}
          style={{
            color: item.isLast ? "var(--semi-color-text-0)" : "var(--semi-color-text-2)",
            fontWeight: item.isLast ? 500 : 400,
            opacity: item.disabled ? 0.5 : 1,
          }}
        >
          {item.href && !item.isLast && !item.disabled ? (
            <Link
              to={item.href}
              className="hover:text-blue-600 transition-colors"
              style={{ color: "inherit" }}
            >
              {item.name}
            </Link>
          ) : (
            item.name
          )}
        </Breadcrumb.Item>
      ))}
    </Breadcrumb>
  );
}
