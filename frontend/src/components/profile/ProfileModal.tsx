import React, { useState } from "react";
import { Modal, Form, Button, Toast, Row, Col, Typography } from "@douyinfe/semi-ui";
import { userService, type ProfileUpdateData } from "@/services/user";
import { useAuthStore } from "@/stores";

const { Title } = Typography;

interface ProfileModalProps {
  visible: boolean;
  onCancel: () => void;
  onSuccess: () => void;
}

export function ProfileModal({ visible, onCancel, onSuccess }: ProfileModalProps) {
  const [loading, setLoading] = useState(false);
  const { user: currentUser } = useAuthStore();

  // 更新个人信息
  const handleSubmit = async (values: ProfileUpdateData) => {
    setLoading(true);
    try {
      const response = await userService.updateProfile(values);
      if (response.success) {
        Toast.success("个人信息更新成功");
        onSuccess();
      } else {
        Toast.error(response.message || "更新失败");
      }
    } catch (error: any) {
      console.error("更新个人信息失败:", error);
      Toast.error(error.message || "更新失败，请稍后重试");
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    onCancel();
  };

  return (
    <Modal
      title={
        <div style={{ textAlign: "center", width: "100%" }}>
          <Title heading={4} style={{ margin: 0, color: "var(--semi-color-text-0)" }}>
            个人设置
          </Title>
        </div>
      }
      visible={visible}
      onCancel={handleCancel}
      footer={null}
      width={600}
      style={{ maxWidth: "90vw" }}
      bodyStyle={{ padding: "24px" }}
    >
      <Form<ProfileUpdateData>
        onSubmit={handleSubmit}
        layout="vertical"
        style={{ padding: 0 }}
        initValues={{
          username: currentUser?.username || "",
          email: currentUser?.email || "",
          fullName: currentUser?.fullName || "",
          department: currentUser?.department || "",
          phone: currentUser?.phone || "",
        }}
      >
        <Row gutter={[16, 16]}>
          <Col span={12}>
            <Form.Input
              field="username"
              label="用户名"
              placeholder="请输入用户名"
              rules={[
                { required: true, message: "请输入用户名" },
                { min: 3, max: 50, message: "用户名长度为3-50位" },
                { pattern: /^[a-zA-Z0-9_]+$/, message: "用户名只能包含字母、数字和下划线" },
              ]}
              style={{ width: "100%" }}
            />
          </Col>
          <Col span={12}>
            <Form.Input
              field="email"
              label="邮箱"
              placeholder="请输入邮箱"
              rules={[
                { required: true, message: "请输入邮箱" },
                { type: "email", message: "请输入有效的邮箱地址" },
              ]}
              style={{ width: "100%" }}
            />
          </Col>
        </Row>

        <Row gutter={[16, 16]}>
          <Col span={12}>
            <Form.Input
              field="fullName"
              label="姓名"
              placeholder="请输入姓名"
              rules={[
                { required: true, message: "请输入姓名" },
                { max: 100, message: "姓名长度不能超过100位" },
              ]}
              style={{ width: "100%" }}
            />
          </Col>
          <Col span={12}>
            <Form.Input
              field="phone"
              label="手机号"
              placeholder="请输入手机号"
              rules={[{ pattern: /^1[3-9]\d{9}$/, message: "请输入正确的手机号码" }]}
              style={{ width: "100%" }}
            />
          </Col>
        </Row>

        <Form.Input
          field="department"
          label="部门"
          placeholder="请输入部门"
          style={{ width: "100%" }}
        />

        <div style={{ display: "flex", justifyContent: "center", gap: 16, marginTop: 24 }}>
          <Button onClick={handleCancel} style={{ minWidth: 100 }}>
            取消
          </Button>
          <Button type="primary" htmlType="submit" loading={loading} style={{ minWidth: 120 }}>
            保存
          </Button>
        </div>
      </Form>
    </Modal>
  );
}
