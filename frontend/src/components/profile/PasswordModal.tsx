import React, { useState } from "react";
import { Modal, Form, Button, Toast, Typography } from "@douyinfe/semi-ui";
import { userService, type PasswordChangeData } from "@/services/user";
import { useAuthStore } from "@/stores";

const { Title } = Typography;

interface PasswordModalProps {
  visible: boolean;
  onCancel: () => void;
  onSuccess: () => void;
}

interface PasswordFormData {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

export function PasswordModal({ visible, onCancel, onSuccess }: PasswordModalProps) {
  const [loading, setLoading] = useState(false);
  const { user } = useAuthStore();

  // 修改密码
  const handleSubmit = async (values: PasswordFormData) => {
    if (values.newPassword !== values.confirmPassword) {
      Toast.error("两次输入的新密码不一致");
      return;
    }

    setLoading(true);
    try {
      const passwordData: PasswordChangeData = {
        currentPassword: values.currentPassword,
        newPassword: values.newPassword,
      };

      const response = await userService.changePassword(user?.id || "", passwordData);

      if (response.success) {
        Toast.success("密码修改成功");
        onSuccess();
      } else {
        Toast.error(response.message || "密码修改失败");
      }
    } catch (error: any) {
      console.error("修改密码失败:", error);
      Toast.error(error.message || "密码修改失败，请稍后重试");
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    onCancel();
  };

  return (
    <Modal
      title={
        <div style={{ textAlign: "center", width: "100%" }}>
          <Title heading={4} style={{ margin: 0, color: "var(--semi-color-text-0)" }}>
            修改密码
          </Title>
        </div>
      }
      visible={visible}
      onCancel={handleCancel}
      footer={null}
      width={500}
      style={{ maxWidth: "90vw" }}
      bodyStyle={{ padding: "24px" }}
    >
      <Form onSubmit={handleSubmit} layout="vertical" style={{ padding: 0 }}>
        <Form.Input
          field="currentPassword"
          label="当前密码"
          type="password"
          placeholder="请输入当前密码"
          rules={[{ required: true, message: "请输入当前密码" }]}
          style={{ width: "100%" }}
        />

        <Form.Input
          field="newPassword"
          label="新密码"
          type="password"
          placeholder="请输入新密码"
          rules={[
            { required: true, message: "请输入新密码" },
            { min: 6, max: 50, message: "密码长度为6-50位" },
          ]}
          style={{ width: "100%" }}
        />

        <Form.Input
          field="confirmPassword"
          label="确认新密码"
          type="password"
          placeholder="请再次输入新密码"
          rules={[{ required: true, message: "请确认新密码" }]}
          style={{ width: "100%" }}
        />

        <div style={{ display: "flex", justifyContent: "center", gap: 16, marginTop: 24 }}>
          <Button onClick={handleCancel} style={{ minWidth: 100 }}>
            取消
          </Button>
          <Button type="primary" htmlType="submit" loading={loading} style={{ minWidth: 120 }}>
            确认修改
          </Button>
        </div>
      </Form>
    </Modal>
  );
}
