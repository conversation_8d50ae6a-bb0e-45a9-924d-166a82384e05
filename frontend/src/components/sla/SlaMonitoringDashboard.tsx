import React, { useState, useEffect } from "react";
import {
  Card,
  Row,
  Col,
  Typography,
  Progress,
  Tag,
  List,
  Button,
  Spin,
  Empty,
} from "@douyinfe/semi-ui";
import { IconRefresh, IconTick, IconAlertTriangle } from "@douyinfe/semi-icons";
import { useQuery } from "@tanstack/react-query";
import { slaService } from "@/services/sla";

const { Title, Text } = Typography;

export default function SlaMonitoringDashboard() {
  const [autoRefresh, setAutoRefresh] = useState(true);

  // 获取SLA仪表板数据
  const {
    data: dashboardData,
    isLoading: dashboardLoading,
    refetch: refetchDashboard,
  } = useQuery({
    queryKey: ["sla-dashboard"],
    queryFn: () => slaService.getSlaPerformanceDashboard(),
    refetchInterval: autoRefresh ? 30000 : false, // 30秒自动刷新
    select: response => response.data,
  });

  // 获取风险预警数据
  const {
    data: riskAlerts,
    isLoading: risksLoading,
    refetch: refetchRisks,
  } = useQuery({
    queryKey: ["sla-risks"],
    queryFn: () => slaService.getSlaRiskAlerts({ limit: 10 }),
    refetchInterval: autoRefresh ? 30000 : false,
    select: response => response.data,
  });

  // 获取活跃监控数据
  const {
    data: activeMonitoring,
    isLoading: monitoringLoading,
    refetch: refetchMonitoring,
  } = useQuery({
    queryKey: ["sla-monitoring-active"],
    queryFn: () => slaService.getActiveSlaMonitoring(),
    refetchInterval: autoRefresh ? 30000 : false,
    select: response => response.data,
  });

  const handleRefresh = () => {
    refetchDashboard();
    refetchRisks();
    refetchMonitoring();
  };

  const handleTriggerAlert = async () => {
    try {
      await slaService.triggerSlaAlertCheck();
      handleRefresh();
    } catch (error) {
      console.error("触发SLA检查失败:", error);
    }
  };

  // 获取风险等级颜色
  const getRiskColor = (riskLevel: string) => {
    switch (riskLevel) {
      case "CRITICAL":
        return "red";
      case "HIGH":
        return "orange";
      case "MEDIUM":
        return "yellow";
      case "LOW":
        return "green";
      default:
        return "grey";
    }
  };

  // 获取SLA状态颜色
  const getSlaStatusColor = (status: string) => {
    switch (status) {
      case "WITHIN_SLA":
        return "green";
      case "APPROACHING_BREACH":
        return "orange";
      case "RESPONSE_BREACH":
      case "RESOLUTION_BREACH":
      case "FULL_BREACH":
        return "red";
      default:
        return "grey";
    }
  };

  const isLoading = dashboardLoading || risksLoading || monitoringLoading;

  return (
    <div style={{ padding: "24px" }}>
      <div
        style={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          marginBottom: "24px",
        }}
      >
        <Title heading={3}>SLA监控面板</Title>
        <div>
          <Button
            icon={<IconRefresh />}
            onClick={handleRefresh}
            loading={isLoading}
            style={{ marginRight: "8px" }}
          >
            刷新
          </Button>
          <Button
            icon={<IconAlertTriangle />}
            onClick={handleTriggerAlert}
            type="primary"
            theme="solid"
          >
            手动检查
          </Button>
        </div>
      </div>

      {/* 整体状态概览 */}
      <Row gutter={16} style={{ marginBottom: "24px" }}>
        <Col span={6}>
          <Card style={{ textAlign: "center" }}>
            <Title heading={4} style={{ color: "#1890ff" }}>
              {dashboardData?.performance?.totalActiveServices || 0}
            </Title>
            <Text type="secondary">活跃服务总数</Text>
          </Card>
        </Col>
        <Col span={6}>
          <Card style={{ textAlign: "center" }}>
            <Title heading={4} style={{ color: "#52c41a" }}>
              {dashboardData?.performance?.withinSla || 0}
            </Title>
            <Text type="secondary">SLA范围内</Text>
          </Card>
        </Col>
        <Col span={6}>
          <Card style={{ textAlign: "center" }}>
            <Title heading={4} style={{ color: "#faad14" }}>
              {dashboardData?.performance?.approachingBreach || 0}
            </Title>
            <Text type="secondary">接近违约</Text>
          </Card>
        </Col>
        <Col span={6}>
          <Card style={{ textAlign: "center" }}>
            <Title heading={4} style={{ color: "#f5222d" }}>
              {dashboardData?.performance?.breached || 0}
            </Title>
            <Text type="secondary">已违约</Text>
          </Card>
        </Col>
      </Row>

      {/* SLA合规性指标 */}
      <Row gutter={16} style={{ marginBottom: "24px" }}>
        <Col span={12}>
          <Card title="响应时间合规率">
            <div style={{ textAlign: "center" }}>
              <Progress
                percent={dashboardData?.performance?.averageResponseCompliance || 0}
                type="circle"
                size="large"
                stroke={
                  (dashboardData?.performance?.averageResponseCompliance || 0) >= 90
                    ? "#52c41a"
                    : "#faad14"
                }
              />
              <div style={{ marginTop: "16px" }}>
                <Text type="secondary">
                  平均响应合规率:{" "}
                  {dashboardData?.performance?.averageResponseCompliance?.toFixed(1) || 0}%
                </Text>
              </div>
            </div>
          </Card>
        </Col>
        <Col span={12}>
          <Card title="解决时间合规率">
            <div style={{ textAlign: "center" }}>
              <Progress
                percent={dashboardData?.performance?.averageResolutionCompliance || 0}
                type="circle"
                size="large"
                stroke={
                  (dashboardData?.performance?.averageResolutionCompliance || 0) >= 90
                    ? "#52c41a"
                    : "#faad14"
                }
              />
              <div style={{ marginTop: "16px" }}>
                <Text type="secondary">
                  平均解决合规率:{" "}
                  {dashboardData?.performance?.averageResolutionCompliance?.toFixed(1) || 0}%
                </Text>
              </div>
            </div>
          </Card>
        </Col>
      </Row>

      {/* 风险等级分布和最近警报 */}
      <Row gutter={16} style={{ marginBottom: "24px" }}>
        <Col span={12}>
          <Card title="风险等级分布">
            {dashboardData?.performance?.riskDistribution ? (
              <div>
                {Object.entries(dashboardData.performance.riskDistribution).map(
                  ([level, count]) => (
                    <div key={level} style={{ marginBottom: "12px" }}>
                      <div
                        style={{
                          display: "flex",
                          justifyContent: "space-between",
                          marginBottom: "4px",
                        }}
                      >
                        <Text>{level}</Text>
                        <Text>{count as number}个</Text>
                      </div>
                      <Progress
                        percent={
                          ((count as number) /
                            (dashboardData.performance.totalActiveServices || 1)) *
                          100
                        }
                        size="small"
                        stroke={getRiskColor(level)}
                        showInfo={false}
                      />
                    </div>
                  )
                )}
              </div>
            ) : (
              <Empty description="暂无数据" />
            )}
          </Card>
        </Col>
        <Col span={12}>
          <Card title="最近SLA警报">
            {dashboardData?.recentAlerts && dashboardData.recentAlerts.length > 0 ? (
              <List
                size="small"
                dataSource={dashboardData.recentAlerts}
                renderItem={(alert: any) => (
                  <List.Item>
                    <div>
                      <Text strong>{alert.subject}</Text>
                      <br />
                      <Text type="secondary" size="small">
                        {new Date(alert.createdAt).toLocaleString()}
                      </Text>
                      <br />
                      <Tag color={alert.status === "SENT" ? "green" : "orange"} size="small">
                        {alert.status}
                      </Tag>
                    </div>
                  </List.Item>
                )}
              />
            ) : (
              <Empty description="暂无警报" />
            )}
          </Card>
        </Col>
      </Row>

      {/* 高风险服务预警 */}
      <Card title="高风险服务预警">
        {riskAlerts && riskAlerts.items.length > 0 ? (
          <List
            dataSource={riskAlerts.items.filter(
              (item: any) => item.riskLevel === "CRITICAL" || item.riskLevel === "HIGH"
            )}
            renderItem={(item: any) => (
              <List.Item>
                <div style={{ width: "100%" }}>
                  <div
                    style={{
                      display: "flex",
                      justifyContent: "space-between",
                      alignItems: "center",
                    }}
                  >
                    <div>
                      <Text strong>
                        #{item.service.ticketNumber} - {item.service.title}
                      </Text>
                      <br />
                      <Text type="secondary">
                        响应时间: {item.responseTimeStatus.elapsed}分钟 /{" "}
                        {item.slaTemplate.responseTime}分钟 (
                        {item.responseTimeStatus.percentage.toFixed(1)}%)
                      </Text>
                      <br />
                      <Text type="secondary">
                        解决时间: {item.resolutionTimeStatus.elapsed}小时 /{" "}
                        {item.slaTemplate.resolutionTime}小时 (
                        {item.resolutionTimeStatus.percentage.toFixed(1)}%)
                      </Text>
                    </div>
                    <div>
                      <Tag color={getRiskColor(item.riskLevel)}>{item.riskLevel}</Tag>
                      <br />
                      <Tag color={getSlaStatusColor(item.status)} style={{ marginTop: "4px" }}>
                        {item.status}
                      </Tag>
                    </div>
                  </div>
                  <div style={{ marginTop: "8px" }}>
                    <Progress
                      percent={Math.max(
                        item.responseTimeStatus.percentage,
                        item.resolutionTimeStatus.percentage
                      )}
                      size="small"
                      stroke={getRiskColor(item.riskLevel)}
                    />
                  </div>
                </div>
              </List.Item>
            )}
          />
        ) : (
          <Empty description="当前没有高风险服务" image={<IconTick size="large" />} />
        )}
      </Card>

      {/* 自动刷新控制 */}
      <div style={{ textAlign: "center", marginTop: "24px" }}>
        <Button
          type={autoRefresh ? "primary" : "secondary"}
          onClick={() => setAutoRefresh(!autoRefresh)}
          size="small"
        >
          {autoRefresh ? "关闭自动刷新" : "开启自动刷新"}
        </Button>
        {autoRefresh && (
          <Text type="secondary" size="small" style={{ marginLeft: "8px" }}>
            每30秒自动刷新
          </Text>
        )}
      </div>
    </div>
  );
}
