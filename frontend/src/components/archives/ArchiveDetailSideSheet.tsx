import React, { useState, useEffect } from "react";
import {
  SideSheet,
  Typography,
  Card,
  Tag,
  Row,
  Col,
  Spin,
  Empty,
  Button,
  Descriptions,
  Table,
  Toast,
  Space,
} from "@douyinfe/semi-ui";
import {
  IconClose,
  IconCalendar,
  IconUser,
  IconServer,
  IconCode,
  IconSetting,
  IconTicketCodeStroked,
  IconRefresh,
} from "@douyinfe/semi-icons";
import { get } from "@/utils/request";

const { Title, Text } = Typography;

interface ArchiveDetailSideSheetProps {
  visible: boolean;
  archiveId: string | null;
  onClose: () => void;
}

interface Archive {
  id: string;
  name: string;
  description?: string;
  technology?: string;
  environment?: string;
  version?: string;
  deploymentDate?: string;
  status: "ACTIVE" | "MAINTENANCE" | "DEPRECATED" | "ARCHIVED";
  createdAt: string;
  updatedAt: string;
  customer: {
    id: string;
    name: string;
    company?: string;
    level: string;
  };
  createdByUser?: {
    username: string;
    fullName?: string;
  };
  configurations: Array<{
    id: string;
    configType: string;
    title: string;
    isActive: boolean;
  }>;
  services: Array<{
    id: string;
    ticketNumber: string;
    title: string;
    status: string;
    priority: string;
    createdAt: string;
  }>;
  _count?: {
    services: number;
  };
}

// 状态配置
const statusConfig = {
  ACTIVE: { text: "进行中", color: "green" },
  MAINTENANCE: { text: "维护中", color: "blue" },
  DEPRECATED: { text: "已废弃", color: "red" },
  ARCHIVED: { text: "已归档", color: "grey" },
} as const;

const configTypeConfig = {
  SERVER: { text: "服务器", color: "blue" },
  DATABASE: { text: "数据库", color: "green" },
  VPN: { text: "VPN", color: "purple" },
  ACCOUNT: { text: "账户", color: "orange" },
  ENVIRONMENT: { text: "环境", color: "cyan" },
  OTHER: { text: "其他", color: "grey" },
} as const;

const serviceStatusConfig = {
  PENDING: { text: "待处理", color: "orange" },
  IN_PROGRESS: { text: "处理中", color: "blue" },
  WAITING_CUSTOMER: { text: "等待客户", color: "yellow" },
  RESOLVED: { text: "已解决", color: "green" },
  CLOSED: { text: "已关闭", color: "grey" },
} as const;

const priorityConfig = {
  LOW: { text: "低", color: "grey" },
  MEDIUM: { text: "中", color: "blue" },
  HIGH: { text: "高", color: "orange" },
  URGENT: { text: "紧急", color: "red" },
} as const;

export function ArchiveDetailSideSheet({
  visible,
  archiveId,
  onClose,
}: ArchiveDetailSideSheetProps) {
  const [loading, setLoading] = useState(false);
  const [archive, setArchive] = useState<Archive | null>(null);

  useEffect(() => {
    if (visible && archiveId) {
      loadArchiveDetail();
    }
  }, [visible, archiveId]);

  const loadArchiveDetail = async () => {
    if (!archiveId) return;

    setLoading(true);
    try {
      const response = await get(`/api/v1/archives/${archiveId}`);
      if (response.success) {
        setArchive(response.data);
      } else {
        Toast.error(response.message || "获取档案详情失败");
      }
    } catch (error: any) {
      console.error("获取档案详情失败:", error);
      Toast.error(error.message || "获取档案详情失败");
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = () => {
    loadArchiveDetail();
  };

  // 服务工单表格列定义
  const serviceColumns = [
    {
      title: "工单号",
      dataIndex: "ticketNumber",
      key: "ticketNumber",
      width: 120,
      render: (text: string) => (
        <Text strong style={{ fontFamily: "monospace" }}>
          {text}
        </Text>
      ),
    },
    {
      title: "工单标题",
      dataIndex: "title",
      key: "title",
      render: (text: string) => <Text style={{ color: "var(--semi-color-text-0)" }}>{text}</Text>,
    },
    {
      title: "状态",
      dataIndex: "status",
      key: "status",
      width: 80,
      render: (status: keyof typeof serviceStatusConfig) => (
        <Tag color={serviceStatusConfig[status]?.color || "grey"} size="small">
          {serviceStatusConfig[status]?.text || status}
        </Tag>
      ),
    },
    {
      title: "优先级",
      dataIndex: "priority",
      key: "priority",
      width: 80,
      render: (priority: keyof typeof priorityConfig) => (
        <Tag color={priorityConfig[priority]?.color || "grey"} size="small">
          {priorityConfig[priority]?.text || priority}
        </Tag>
      ),
    },
    {
      title: "创建时间",
      dataIndex: "createdAt",
      key: "createdAt",
      width: 100,
      render: (date: string) => (
        <Text type="tertiary" size="small">
          {new Date(date).toLocaleDateString("zh-CN")}
        </Text>
      ),
    },
  ];

  if (!visible) return null;

  return (
    <SideSheet
      title={
        <div
          style={{
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
            width: "100%",
          }}
        >
          <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
            <IconCode size="large" />
            <span>项目档案详情</span>
          </div>
          <Space>
            <Button
              theme="borderless"
              icon={<IconRefresh />}
              onClick={handleRefresh}
              loading={loading}
              size="small"
            />
            <Button theme="borderless" icon={<IconClose />} onClick={onClose} size="small" />
          </Space>
        </div>
      }
      visible={visible}
      onCancel={onClose}
      width={800}
      placement="right"
      mask={false}
      closable={false}
      bodyStyle={{ padding: "24px" }}
    >
      {loading ? (
        <div
          style={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            height: "200px",
          }}
        >
          <Spin size="large" />
        </div>
      ) : archive ? (
        <div style={{ display: "flex", flexDirection: "column", gap: "24px" }}>
          {/* 基本信息 */}
          <Card
            title={
              <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
                <IconCode />
                <span>基本信息</span>
              </div>
            }
            style={{ background: "var(--semi-color-bg-1)" }}
          >
            <div
              style={{ display: "flex", alignItems: "center", gap: "12px", marginBottom: "16px" }}
            >
              <Title heading={3} style={{ margin: 0, color: "var(--semi-color-text-0)" }}>
                {archive.name}
              </Title>
              <Tag color={statusConfig[archive.status].color} size="large">
                {statusConfig[archive.status].text}
              </Tag>
            </div>

            <Descriptions
              data={[
                {
                  key: "项目名称",
                  value: archive.name,
                },
                {
                  key: "项目描述",
                  value: archive.description || "-",
                },
                {
                  key: "技术栈",
                  value: archive.technology || "-",
                },
                {
                  key: "运行环境",
                  value: archive.environment || "-",
                },
                {
                  key: "版本信息",
                  value: archive.version || "-",
                },
                {
                  key: "部署日期",
                  value: archive.deploymentDate
                    ? new Date(archive.deploymentDate).toLocaleDateString("zh-CN")
                    : "-",
                },
                {
                  key: "创建时间",
                  value: new Date(archive.createdAt).toLocaleString("zh-CN"),
                },
                {
                  key: "更新时间",
                  value: new Date(archive.updatedAt).toLocaleString("zh-CN"),
                },
                {
                  key: "创建人",
                  value: archive.createdByUser?.fullName || archive.createdByUser?.username || "-",
                },
              ]}
              row
              size="small"
            />
          </Card>

          {/* 客户信息 */}
          <Card
            title={
              <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
                <IconUser />
                <span>客户信息</span>
              </div>
            }
            style={{ background: "var(--semi-color-bg-1)" }}
          >
            <Row gutter={[16, 16]}>
              <Col span={12}>
                <div>
                  <Text type="secondary" size="small">
                    客户名称
                  </Text>
                  <div style={{ color: "var(--semi-color-text-0)", fontWeight: 500 }}>
                    {archive.customer.name}
                  </div>
                </div>
              </Col>
              <Col span={12}>
                <div>
                  <Text type="secondary" size="small">
                    公司名称
                  </Text>
                  <div style={{ color: "var(--semi-color-text-0)" }}>
                    {archive.customer.company || "-"}
                  </div>
                </div>
              </Col>
            </Row>
          </Card>

          {/* 配置项 */}
          <Card
            title={
              <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
                <IconSetting />
                <span>配置项 ({archive.configurations.length})</span>
              </div>
            }
            style={{ background: "var(--semi-color-bg-1)" }}
          >
            {archive.configurations.length > 0 ? (
              <div style={{ display: "flex", flexDirection: "column", gap: "12px" }}>
                {archive.configurations.map(config => (
                  <div
                    key={config.id}
                    style={{
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "space-between",
                      padding: "12px",
                      background: "var(--semi-color-bg-2)",
                      borderRadius: "6px",
                      border: "1px solid var(--semi-color-border)",
                    }}
                  >
                    <div style={{ display: "flex", alignItems: "center", gap: "12px" }}>
                      <IconServer size="small" />
                      <div>
                        <Text strong style={{ color: "var(--semi-color-text-0)" }}>
                          {config.title}
                        </Text>
                      </div>
                    </div>
                    <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
                      <Tag
                        color={(configTypeConfig as any)[config.configType]?.color || "grey"}
                        size="small"
                      >
                        {(configTypeConfig as any)[config.configType]?.text || config.configType}
                      </Tag>
                      <Tag color={config.isActive ? "green" : "grey"} size="small">
                        {config.isActive ? "启用" : "禁用"}
                      </Tag>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <Empty
                image={<IconSetting size="extra-large" />}
                title="暂无配置项"
                description="该项目暂未配置任何配置项"
              />
            )}
          </Card>

          {/* 相关服务工单 */}
          <Card
            title={
              <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
                <IconTicketCodeStroked />
                <span>相关服务工单 ({archive._count?.services || archive.services.length})</span>
              </div>
            }
            style={{ background: "var(--semi-color-bg-1)" }}
          >
            {archive.services.length > 0 ? (
              <Table
                columns={serviceColumns}
                dataSource={archive.services}
                rowKey="id"
                pagination={false}
                size="small"
                style={{ background: "var(--semi-color-bg-0)" }}
              />
            ) : (
              <Empty
                image={<IconTicketCodeStroked size="extra-large" />}
                title="暂无服务工单"
                description="该项目暂未有任何服务工单"
              />
            )}
          </Card>
        </div>
      ) : (
        <div style={{ textAlign: "center", padding: "48px" }}>
          <Empty title="档案不存在" description="无法找到指定的项目档案" />
        </div>
      )}
    </SideSheet>
  );
}
