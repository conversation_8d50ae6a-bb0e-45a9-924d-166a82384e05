import React, { useState } from "react";
import {
  Modal,
  Form,
  Input,
  Select,
  TextArea,
  Button,
  Toast,
  Space,
  Row,
  Col,
} from "@douyinfe/semi-ui";
import { IconSave } from "@douyinfe/semi-icons";

interface ArchiveCreateModalProps {
  visible: boolean;
  customerId: string;
  onCancel: () => void;
  onSuccess: () => void;
}

interface ArchiveFormData {
  name: string;
  description: string;
  status: "ACTIVE" | "MAINTENANCE" | "DEPRECATED" | "ARCHIVED";
  projectType: string;
  version: string;
  technicalStack: string;
  deploymentUrl?: string;
  repositoryUrl?: string;
  documentationUrl?: string;
  notes?: string;
}

const statusOptions = [
  { value: "ACTIVE", label: "进行中" },
  { value: "MAINTENANCE", label: "维护中" },
  { value: "DEPRECATED", label: "已废弃" },
  { value: "ARCHIVED", label: "已归档" },
];

const projectTypeOptions = [
  { value: "WEB_APPLICATION", label: "Web应用" },
  { value: "MOBILE_APP", label: "移动应用" },
  { value: "DESKTOP_APP", label: "桌面应用" },
  { value: "API_SERVICE", label: "API服务" },
  { value: "MICROSERVICE", label: "微服务" },
  { value: "WEBSITE", label: "官网" },
  { value: "SYSTEM_INTEGRATION", label: "系统集成" },
  { value: "OTHER", label: "其他" },
];

export const ArchiveCreateModal: React.FC<ArchiveCreateModalProps> = ({
  visible,
  customerId,
  onCancel,
  onSuccess,
}) => {
  const [formApi, setFormApi] = useState<any>(null);
  const [loading, setLoading] = useState(false);

  const handleSubmit = async () => {
    try {
      const values = await formApi?.validate();
      if (!values) return;

      setLoading(true);

      // 构建请求数据
      const requestData: ArchiveFormData & { customerId: string } = {
        ...values,
        customerId,
      };

      // 这里调用API创建项目档案
      // 由于没有实际的API服务，我们模拟一个请求
      console.log("创建项目档案:", requestData);

      // 模拟API请求
      await new Promise(resolve => setTimeout(resolve, 1000));

      Toast.success("项目档案创建成功");
      onSuccess();
    } catch (error: any) {
      console.error("创建项目档案失败:", error);
      Toast.error(error.message || "创建失败，请稍后重试");
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    formApi?.reset();
    onCancel();
  };

  return (
    <Modal
      title="创建项目档案"
      visible={visible}
      width={800}
      onCancel={handleCancel}
      footer={
        <Space>
          <Button onClick={handleCancel} disabled={loading}>
            取消
          </Button>
          <Button
            theme="solid"
            type="primary"
            icon={<IconSave />}
            loading={loading}
            onClick={handleSubmit}
          >
            保存
          </Button>
        </Space>
      }
      closeOnEsc={!loading}
      maskClosable={!loading}
    >
      <Form
        getFormApi={setFormApi}
        labelPosition="top"
        labelAlign="left"
        initValues={{
          status: "ACTIVE",
          projectType: "WEB_APPLICATION",
          version: "1.0.0",
        }}
      >
        <Row gutter={[16, 0]}>
          <Col span={12}>
            <Form.Input
              field="name"
              label="项目名称"
              placeholder="请输入项目名称"
              rules={[
                { required: true, message: "请输入项目名称" },
                { min: 2, max: 100, message: "项目名称长度应在2-100字符之间" },
              ]}
            />
          </Col>
          <Col span={12}>
            <Form.Select
              field="status"
              label="项目状态"
              placeholder="请选择项目状态"
              optionList={statusOptions}
              rules={[{ required: true, message: "请选择项目状态" }]}
            />
          </Col>
        </Row>

        <Row gutter={[16, 0]}>
          <Col span={12}>
            <Form.Select
              field="projectType"
              label="项目类型"
              placeholder="请选择项目类型"
              optionList={projectTypeOptions}
              rules={[{ required: true, message: "请选择项目类型" }]}
            />
          </Col>
          <Col span={12}>
            <Form.Input
              field="version"
              label="版本号"
              placeholder="如：1.0.0"
              rules={[
                { required: true, message: "请输入版本号" },
                { max: 50, message: "版本号不能超过50字符" },
              ]}
            />
          </Col>
        </Row>

        <Form.Input
          field="technicalStack"
          label="技术栈"
          placeholder="如：React, Node.js, MySQL"
          rules={[
            { required: true, message: "请输入技术栈" },
            { max: 200, message: "技术栈描述不能超过200字符" },
          ]}
        />

        <Form.TextArea
          field="description"
          label="项目描述"
          placeholder="请输入项目描述"
          rows={3}
          maxCount={500}
          rules={[
            { required: true, message: "请输入项目描述" },
            { min: 10, max: 500, message: "项目描述长度应在10-500字符之间" },
          ]}
        />

        <Row gutter={[16, 0]}>
          <Col span={12}>
            <Form.Input
              field="deploymentUrl"
              label="部署地址"
              placeholder="https://example.com"
              rules={[
                {
                  type: "url",
                  message: "请输入有效的URL地址",
                },
              ]}
            />
          </Col>
          <Col span={12}>
            <Form.Input
              field="repositoryUrl"
              label="代码仓库"
              placeholder="https://github.com/user/repo"
              rules={[
                {
                  type: "url",
                  message: "请输入有效的URL地址",
                },
              ]}
            />
          </Col>
        </Row>

        <Form.Input
          field="documentationUrl"
          label="文档地址"
          placeholder="https://docs.example.com"
          rules={[
            {
              type: "url",
              message: "请输入有效的URL地址",
            },
          ]}
        />

        <Form.TextArea
          field="notes"
          label="备注信息"
          placeholder="可选的备注信息"
          rows={3}
          maxCount={1000}
        />
      </Form>
    </Modal>
  );
};
