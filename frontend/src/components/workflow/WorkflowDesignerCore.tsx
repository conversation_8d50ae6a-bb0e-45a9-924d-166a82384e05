import React, { useState, useCallback, useRef, useEffect } from 'react';
import { Spin } from '@douyinfe/semi-ui';
import WorkflowCanvas from './WorkflowCanvas';
import WorkflowToolbox, { NodeTemplate } from './WorkflowToolbox';
import type {
  DesignerNode,
  DesignerConnection,
  WorkflowStep,
  Point,
  CanvasEvent,
  CanvasSelection,
  WorkflowStepType
} from '../../types/workflow';
import { generateNodeId } from '../../utils/workflow-helpers';

export interface WorkflowDesignerCoreProps {
  nodes: DesignerNode[];
  connections: DesignerConnection[];
  selectedNodes: string[];
  selectedConnections: string[];
  scale: number;
  offset: Point;
  readonly?: boolean;
  loading?: boolean;
  onNodesChange: (nodes: DesignerNode[]) => void;
  onConnectionsChange: (connections: DesignerConnection[]) => void;
  onSelectionChange: (selection: CanvasSelection) => void;
  onScaleChange: (scale: number) => void;
  onOffsetChange: (offset: Point) => void;
  onCanvasEvent: (event: CanvasEvent) => void;
}

interface DragState {
  isDragging: boolean;
  dragTemplate: NodeTemplate | null;
  dragStartPosition: Point | null;
  currentMousePosition: Point | null;
}

const WorkflowDesignerCore: React.FC<WorkflowDesignerCoreProps> = ({
  nodes,
  connections,
  selectedNodes,
  selectedConnections,
  scale,
  offset,
  readonly = false,
  loading = false,
  onNodesChange,
  onConnectionsChange,
  onSelectionChange,
  onScaleChange,
  onOffsetChange,
  onCanvasEvent
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const canvasRef = useRef<HTMLDivElement>(null);
  const [dragState, setDragState] = useState<DragState>({
    isDragging: false,
    dragTemplate: null,
    dragStartPosition: null,
    currentMousePosition: null
  });

  // 创建新节点的默认步骤
  const createStepFromTemplate = useCallback((template: NodeTemplate): WorkflowStep => {
    return {
      index: nodes.length,
      name: template.name,
      type: template.type as WorkflowStepType,
      description: template.description,
      config: { ...template.defaultConfig },
      timeout: 30000
    };
  }, [nodes.length]);

  // 创建新节点
  const createNodeFromTemplate = useCallback((
    template: NodeTemplate, 
    position: Point
  ): DesignerNode => {
    const step = createStepFromTemplate(template);
    const nodeId = generateNodeId();
    
    return {
      id: nodeId,
      type: template.type as WorkflowStepType,
      position: {
        x: Math.max(0, position.x),
        y: Math.max(0, position.y)
      },
      size: {
        width: 200,
        height: 100
      },
      step,
      data: {
        step,
        isSelected: false,
        isHighlighted: false,
        validation: {
          isValid: true,
          errors: []
        }
      },
      ports: {
        input: [{
          id: `${nodeId}-input`,
          label: 'Input',
          type: 'default'
        }],
        output: [{
          id: `${nodeId}-output`,
          label: 'Output',
          type: 'default'
        }]
      }
    };
  }, [createStepFromTemplate]);

  // 处理工具箱拖拽开始
  const handleNodeDragStart = useCallback((template: NodeTemplate, startPosition: Point) => {
    if (readonly) return;
    
    setDragState({
      isDragging: true,
      dragTemplate: template,
      dragStartPosition: startPosition,
      currentMousePosition: startPosition
    });

    // 添加全局鼠标事件监听
    const handleGlobalMouseMove = (e: MouseEvent) => {
      setDragState(prev => ({
        ...prev,
        currentMousePosition: { x: e.clientX, y: e.clientY }
      }));
    };

    const handleGlobalMouseUp = (e: MouseEvent) => {
      // 检查是否在画布区域释放
      if (canvasRef.current) {
        const canvasRect = canvasRef.current.getBoundingClientRect();
        const isInCanvas = 
          e.clientX >= canvasRect.left &&
          e.clientX <= canvasRect.right &&
          e.clientY >= canvasRect.top &&
          e.clientY <= canvasRect.bottom;

        if (isInCanvas && dragState.dragTemplate) {
          // 计算画布内的相对位置
          const canvasPosition = {
            x: (e.clientX - canvasRect.left - offset.x) / scale,
            y: (e.clientY - canvasRect.top - offset.y) / scale
          };

          // 创建新节点
          const newNode = createNodeFromTemplate(dragState.dragTemplate, canvasPosition);
          onNodesChange([...nodes, newNode]);
          
          // 选中新创建的节点
          onSelectionChange({ nodes: [newNode.id], connections: [] });
          
          // 触发画布事件
          onCanvasEvent({
            type: 'node_moved',
            nodeId: newNode.id,
            position: canvasPosition
          });
        }
      }

      // 清理拖拽状态
      setDragState({
        isDragging: false,
        dragTemplate: null,
        dragStartPosition: null,
        currentMousePosition: null
      });

      // 移除全局事件监听
      document.removeEventListener('mousemove', handleGlobalMouseMove);
      document.removeEventListener('mouseup', handleGlobalMouseUp);
    };

    document.addEventListener('mousemove', handleGlobalMouseMove);
    document.addEventListener('mouseup', handleGlobalMouseUp);
  }, [readonly, dragState.dragTemplate, offset, scale, createNodeFromTemplate, nodes, onNodesChange, onSelectionChange, onCanvasEvent]);

  // 处理节点更新
  const handleNodeUpdate = useCallback((nodeId: string, updates: Partial<DesignerNode>) => {
    const updatedNodes = nodes.map(node => 
      node.id === nodeId ? { ...node, ...updates } : node
    );
    onNodesChange(updatedNodes);
  }, [nodes, onNodesChange]);

  // 处理节点删除
  const handleNodeDelete = useCallback((nodeId: string) => {
    // 删除节点
    const filteredNodes = nodes.filter(node => node.id !== nodeId);
    onNodesChange(filteredNodes);

    // 删除相关连接
    const filteredConnections = connections.filter(
      conn => conn.sourceNodeId !== nodeId && conn.targetNodeId !== nodeId
    );
    onConnectionsChange(filteredConnections);

    // 更新选择状态
    const updatedSelectedNodes = selectedNodes.filter(id => id !== nodeId);
    onSelectionChange({ 
      nodes: updatedSelectedNodes, 
      connections: selectedConnections 
    });
  }, [nodes, connections, selectedNodes, selectedConnections, onNodesChange, onConnectionsChange, onSelectionChange]);

  // 处理连接添加
  const handleConnectionAdd = useCallback((sourceId: string, targetId: string) => {
    const connectionId = `${sourceId}-${targetId}-${Date.now()}`;
    const newConnection: DesignerConnection = {
      id: connectionId,
      sourceNodeId: sourceId,
      targetNodeId: targetId,
      label: undefined
    };

    onConnectionsChange([...connections, newConnection]);
  }, [connections, onConnectionsChange]);

  // 处理连接删除
  const handleConnectionDelete = useCallback((connectionId: string) => {
    const filteredConnections = connections.filter(conn => conn.id !== connectionId);
    onConnectionsChange(filteredConnections);
    
    // 更新选择状态
    const updatedSelectedConnections = selectedConnections.filter(id => id !== connectionId);
    onSelectionChange({ 
      nodes: selectedNodes, 
      connections: updatedSelectedConnections 
    });
  }, [connections, selectedConnections, selectedNodes, onConnectionsChange, onSelectionChange]);

  // 处理鼠标滚轮缩放
  const handleWheel = useCallback((e: React.WheelEvent) => {
    if (e.ctrlKey || e.metaKey) {
      e.preventDefault();
      
      const delta = e.deltaY;
      const scaleFactor = delta > 0 ? 0.9 : 1.1;
      const newScale = Math.min(Math.max(scale * scaleFactor, 0.1), 3);
      
      onScaleChange(newScale);
    }
  }, [scale, onScaleChange]);

  // 处理画布拖拽
  const [isPanning, setIsPanning] = useState(false);
  const [panStart, setPanStart] = useState<Point>({ x: 0, y: 0 });

  const handleCanvasMouseDown = useCallback((e: React.MouseEvent) => {
    if (e.button === 1 || (e.button === 0 && e.metaKey)) { // 中键或Cmd+左键
      e.preventDefault();
      setIsPanning(true);
      setPanStart({ x: e.clientX - offset.x, y: e.clientY - offset.y });
    }
  }, [offset]);

  const handleCanvasMouseMove = useCallback((e: React.MouseEvent) => {
    if (isPanning) {
      const newOffset = {
        x: e.clientX - panStart.x,
        y: e.clientY - panStart.y
      };
      onOffsetChange(newOffset);
    }
  }, [isPanning, panStart, onOffsetChange]);

  const handleCanvasMouseUp = useCallback(() => {
    setIsPanning(false);
  }, []);

  // 键盘快捷键
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (readonly) return;

      // 复制: Cmd/Ctrl + C
      if ((e.metaKey || e.ctrlKey) && e.key === 'c') {
        if (selectedNodes.length > 0) {
          // TODO: 实现复制逻辑
          console.log('Copy nodes:', selectedNodes);
        }
      }
      
      // 粘贴: Cmd/Ctrl + V
      if ((e.metaKey || e.ctrlKey) && e.key === 'v') {
        // TODO: 实现粘贴逻辑
        console.log('Paste nodes');
      }
      
      // 全选: Cmd/Ctrl + A
      if ((e.metaKey || e.ctrlKey) && e.key === 'a') {
        e.preventDefault();
        onSelectionChange({
          nodes: nodes.map(n => n.id),
          connections: connections.map(c => c.id)
        });
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [readonly, selectedNodes, nodes, connections, onSelectionChange]);

  return (
    <div 
      ref={containerRef}
      className="workflow-designer-core"
      style={{
        width: '100%',
        height: '100%',
        display: 'flex',
        position: 'relative',
        overflow: 'hidden'
      }}
    >
      {/* 左侧工具箱 */}
      <div 
        className="workflow-designer-toolbox"
        style={{
          width: '280px',
          height: '100%',
          borderRight: '1px solid #d9d9d9',
          backgroundColor: '#fafafa',
          flexShrink: 0
        }}
      >
        <WorkflowToolbox
          onNodeDragStart={handleNodeDragStart}
          readonly={readonly}
        />
      </div>

      {/* 右侧画布区域 */}
      <div 
        className="workflow-designer-canvas-container"
        style={{
          flex: 1,
          height: '100%',
          position: 'relative',
          overflow: 'hidden'
        }}
      >
        <div
          ref={canvasRef}
          className="workflow-canvas-wrapper"
          style={{
            width: '100%',
            height: '100%',
            cursor: isPanning ? 'grabbing' : dragState.isDragging ? 'copy' : 'default'
          }}
          onWheel={handleWheel}
          onMouseDown={handleCanvasMouseDown}
          onMouseMove={handleCanvasMouseMove}
          onMouseUp={handleCanvasMouseUp}
        >
          {loading ? (
            <div style={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              height: '100%',
              backgroundColor: '#f8f9fa'
            }}>
              <Spin size="large" tip="加载工作流..." />
            </div>
          ) : (
            <WorkflowCanvas
              nodes={nodes}
              connections={connections}
              selectedNodes={selectedNodes}
              selectedConnections={selectedConnections}
              scale={scale}
              offset={offset}
              readonly={readonly}
              onNodeAdd={() => {}} // 通过拖拽处理
              onNodeUpdate={handleNodeUpdate}
              onNodeDelete={handleNodeDelete}
              onConnectionAdd={handleConnectionAdd}
              onConnectionDelete={handleConnectionDelete}
              onSelectionChange={onSelectionChange}
              onCanvasEvent={onCanvasEvent}
            />
          )}
        </div>

        {/* 拖拽预览 */}
        {dragState.isDragging && dragState.dragTemplate && dragState.currentMousePosition && (
          <div
            className="drag-preview"
            style={{
              position: 'fixed',
              left: dragState.currentMousePosition.x - 100,
              top: dragState.currentMousePosition.y - 25,
              width: '200px',
              height: '50px',
              backgroundColor: 'white',
              border: '2px dashed #1890ff',
              borderRadius: '8px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              pointerEvents: 'none',
              zIndex: 9999,
              opacity: 0.8,
              fontSize: '12px',
              fontWeight: 500,
              color: '#1890ff'
            }}
          >
            <span style={{ marginRight: '8px' }}>{dragState.dragTemplate.icon}</span>
            {dragState.dragTemplate.name}
          </div>
        )}

        {/* 操作提示 */}
        {!readonly && (
          <div 
            className="workflow-canvas-hints"
            style={{
              position: 'absolute',
              bottom: '16px',
              right: '16px',
              backgroundColor: 'rgba(0, 0, 0, 0.6)',
              color: 'white',
              padding: '8px 12px',
              borderRadius: '6px',
              fontSize: '11px',
              maxWidth: '300px',
              lineHeight: '1.4'
            }}
          >
            <div>💡 操作提示：</div>
            <div>• 从左侧拖拽组件到画布创建节点</div>
            <div>• 滚轮缩放，中键拖拽移动画布</div>
            <div>• 拖拽节点连接点创建连接线</div>
            <div>• Delete 删除选中的节点/连接</div>
          </div>
        )}

        {/* 缩放控制 */}
        <div 
          className="workflow-zoom-controls"
          style={{
            position: 'absolute',
            top: '16px',
            right: '16px',
            backgroundColor: 'white',
            border: '1px solid #d9d9d9',
            borderRadius: '6px',
            padding: '4px',
            display: 'flex',
            alignItems: 'center',
            gap: '4px',
            fontSize: '12px',
            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)'
          }}
        >
          <button
            onClick={() => onScaleChange(Math.max(scale * 0.9, 0.1))}
            style={{
              border: 'none',
              background: 'none',
              cursor: 'pointer',
              padding: '4px 8px'
            }}
          >
            －
          </button>
          <span style={{ minWidth: '60px', textAlign: 'center' }}>
            {Math.round(scale * 100)}%
          </span>
          <button
            onClick={() => onScaleChange(Math.min(scale * 1.1, 3))}
            style={{
              border: 'none',
              background: 'none',
              cursor: 'pointer',
              padding: '4px 8px'
            }}
          >
            ＋
          </button>
        </div>
      </div>
    </div>
  );
};

export default WorkflowDesignerCore;