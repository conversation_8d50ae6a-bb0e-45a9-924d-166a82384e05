/**
 * 工作流调试面板组件
 * 显示执行状态、变量值和错误信息
 */
import React, { useState, useMemo } from 'react';
import { Card, Tabs, TabPane, Tree, Badge, Tag, Button, Input, Space, Collapse, Empty } from '@douyinfe/semi-ui';
import {
  IconSearch,
  IconRefresh,
  IconEyeOpened,
  IconCode,
  IconAlertTriangle,
  IconInfoCircle,
  IconClock
} from '@douyinfe/semi-icons';
import type { 
  WorkflowExecution, 
  WorkflowExecutionStep, 
  DesignerNode 
} from '../../types/workflow';

export interface WorkflowDebugPanelProps {
  execution?: WorkflowExecution | null;
  nodes: DesignerNode[];
  visible: boolean;
  onClose: () => void;
}

interface VariableInfo {
  name: string;
  value: any;
  type: string;
  source: 'context' | 'step' | 'system';
  lastUpdated?: string;
}

interface LogEntry {
  id: string;
  timestamp: string;
  level: 'info' | 'warning' | 'error' | 'debug';
  message: string;
  stepId?: string;
  stepName?: string;
  details?: any;
}

const WorkflowDebugPanel: React.FC<WorkflowDebugPanelProps> = ({
  execution,
  nodes,
  visible,
  onClose
}) => {
  const [activeTab, setActiveTab] = useState('variables');
  const [searchText, setSearchText] = useState('');
  const [expandedKeys, setExpandedKeys] = useState<string[]>(['system', 'context', 'steps']);

  // 处理变量数据
  const variablesInfo = useMemo(() => {
    if (!execution) return [];

    const variables: VariableInfo[] = [];

    // 系统变量
    variables.push(
      {
        name: 'execution_id',
        value: execution.id,
        type: 'string',
        source: 'system'
      },
      {
        name: 'session_id',
        value: execution.sessionId,
        type: 'string',
        source: 'system'
      },
      {
        name: 'workflow_id',
        value: execution.workflowId,
        type: 'string',
        source: 'system'
      },
      {
        name: 'status',
        value: execution.status,
        type: 'string',
        source: 'system'
      },
      {
        name: 'started_at',
        value: execution.startedAt,
        type: 'date',
        source: 'system'
      }
    );

    if (execution.completedAt) {
      variables.push({
        name: 'completed_at',
        value: execution.completedAt,
        type: 'date',
        source: 'system'
      });
    }

    // 上下文变量
    if (execution.context) {
      Object.entries(execution.context).forEach(([key, value]) => {
        variables.push({
          name: key,
          value,
          type: typeof value,
          source: 'context'
        });
      });
    }

    // 步骤输出变量
    if (execution.variables) {
      Object.entries(execution.variables).forEach(([key, value]) => {
        variables.push({
          name: key,
          value,
          type: typeof value,
          source: 'step'
        });
      });
    }

    return variables;
  }, [execution]);

  // 处理日志数据
  const logEntries = useMemo(() => {
    if (!execution) return [];

    const logs: LogEntry[] = [];

    // 执行开始日志
    logs.push({
      id: 'exec_start',
      timestamp: execution.startedAt,
      level: 'info',
      message: '工作流执行开始',
      details: {
        workflowId: execution.workflowId,
        sessionId: execution.sessionId
      }
    });

    // 步骤执行日志
    execution.steps?.forEach((step, index) => {
      const node = nodes.find(n => n.id === step.stepId);
      const stepName = node?.step.name || `步骤 ${index + 1}`;

      // 步骤开始
      logs.push({
        id: `${step.id}_start`,
        timestamp: step.startTime,
        level: 'info',
        message: `开始执行: ${stepName}`,
        stepId: step.stepId,
        stepName,
        details: {
          stepType: node?.step.type,
          input: step.input
        }
      });

      // 步骤完成或失败
      if (step.endTime) {
        logs.push({
          id: `${step.id}_end`,
          timestamp: step.endTime,
          level: step.status === 'FAILED' ? 'error' : step.status === 'COMPLETED' ? 'info' : 'warning',
          message: step.status === 'FAILED' 
            ? `执行失败: ${stepName}` 
            : step.status === 'COMPLETED'
            ? `执行完成: ${stepName}`
            : `执行状态: ${step.status}`,
          stepId: step.stepId,
          stepName,
          details: {
            status: step.status,
            duration: step.duration,
            output: step.output,
            error: step.error
          }
        });
      }
    });

    // 执行完成日志
    if (execution.completedAt) {
      logs.push({
        id: 'exec_end',
        timestamp: execution.completedAt,
        level: execution.status === 'FAILED' ? 'error' : 'info',
        message: execution.status === 'FAILED' ? '工作流执行失败' : '工作流执行完成',
        details: {
          status: execution.status,
          error: execution.error
        }
      });
    }

    return logs.sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());
  }, [execution, nodes]);

  // 过滤变量
  const filteredVariables = useMemo(() => {
    if (!searchText) return variablesInfo;
    return variablesInfo.filter(variable => 
      variable.name.toLowerCase().includes(searchText.toLowerCase()) ||
      String(variable.value).toLowerCase().includes(searchText.toLowerCase())
    );
  }, [variablesInfo, searchText]);

  // 过滤日志
  const filteredLogs = useMemo(() => {
    if (!searchText) return logEntries;
    return logEntries.filter(log => 
      log.message.toLowerCase().includes(searchText.toLowerCase()) ||
      log.stepName?.toLowerCase().includes(searchText.toLowerCase())
    );
  }, [logEntries, searchText]);

  // 获取日志级别图标
  const getLogLevelIcon = (level: LogEntry['level']) => {
    switch (level) {
      case 'error':
        return <IconAlertTriangle style={{ color: '#ff4d4f' }} />;
      case 'warning':
        return <IconAlertTriangle style={{ color: '#faad14' }} />;
      case 'info':
        return <IconInfo style={{ color: '#1890ff' }} />;
      case 'debug':
        return <IconCode style={{ color: '#52c41a' }} />;
      default:
        return <IconInfo style={{ color: '#d9d9d9' }} />;
    }
  };

  // 获取日志级别颜色
  const getLogLevelColor = (level: LogEntry['level']) => {
    switch (level) {
      case 'error': return '#ff4d4f';
      case 'warning': return '#faad14';
      case 'info': return '#1890ff';
      case 'debug': return '#52c41a';
      default: return '#d9d9d9';
    }
  };

  // 渲染变量值
  const renderVariableValue = (value: any, type: string) => {
    if (value === null || value === undefined) {
      return <span style={{ color: '#999', fontStyle: 'italic' }}>null</span>;
    }

    if (type === 'object') {
      return (
        <pre style={{ 
          fontSize: '12px', 
          backgroundColor: '#f5f5f5', 
          padding: '4px 8px', 
          borderRadius: '3px',
          margin: 0,
          maxHeight: '100px',
          overflow: 'auto',
          whiteSpace: 'pre-wrap',
          wordBreak: 'break-all'
        }}>
          {JSON.stringify(value, null, 2)}
        </pre>
      );
    }

    if (type === 'string' && value.length > 100) {
      return (
        <div>
          <div style={{ fontSize: '12px', fontFamily: 'monospace' }}>
            {value.substring(0, 100)}...
          </div>
          <Button 
            size="small" 
            type="tertiary" 
            style={{ marginTop: '4px' }}
            onClick={() => {
              // 显示完整内容的弹窗
              console.log('Full value:', value);
            }}
          >
            查看完整内容
          </Button>
        </div>
      );
    }

    return (
      <span style={{ 
        fontSize: '12px', 
        fontFamily: 'monospace',
        color: type === 'string' ? '#52c41a' : type === 'number' ? '#1890ff' : '#fa8c16'
      }}>
        {String(value)}
      </span>
    );
  };

  // 渲染变量树
  const renderVariableTree = () => {
    const groupedVariables = {
      system: filteredVariables.filter(v => v.source === 'system'),
      context: filteredVariables.filter(v => v.source === 'context'),
      steps: filteredVariables.filter(v => v.source === 'step')
    };

    return (
      <div>
        {Object.entries(groupedVariables).map(([groupName, vars]) => (
          <Card 
            key={groupName}
            title={
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                <span style={{ textTransform: 'capitalize' }}>
                  {groupName === 'system' ? '系统变量' : 
                   groupName === 'context' ? '上下文变量' : '步骤变量'}
                </span>
                <Badge count={vars.length} style={{ backgroundColor: '#1890ff' }} />
              </div>
            }
            style={{ marginBottom: '16px' }}
            bodyStyle={{ padding: vars.length ? '16px' : '40px' }}
          >
            {vars.length ? (
              <div>
                {vars.map((variable, index) => (
                  <div 
                    key={variable.name}
                    style={{ 
                      paddingBottom: '12px',
                      marginBottom: index < vars.length - 1 ? '12px' : 0,
                      borderBottom: index < vars.length - 1 ? '1px solid #f0f0f0' : 'none'
                    }}
                  >
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '4px' }}>
                      <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                        <code style={{ fontSize: '13px', fontWeight: 500 }}>
                          {variable.name}
                        </code>
                        <Tag size="small" color={
                          variable.type === 'string' ? 'green' :
                          variable.type === 'number' ? 'blue' :
                          variable.type === 'boolean' ? 'orange' :
                          variable.type === 'object' ? 'purple' : 'grey'
                        }>
                          {variable.type}
                        </Tag>
                      </div>
                      {variable.lastUpdated && (
                        <span style={{ fontSize: '11px', color: '#999' }}>
                          {new Date(variable.lastUpdated).toLocaleTimeString()}
                        </span>
                      )}
                    </div>
                    <div>
                      {renderVariableValue(variable.value, variable.type)}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <Empty
                title={`无${groupName === 'system' ? '系统' : groupName === 'context' ? '上下文' : '步骤'}变量`}
                description="暂无数据"
              />
            )}
          </Card>
        ))}
      </div>
    );
  };

  // 渲染执行日志
  const renderExecutionLogs = () => {
    if (!filteredLogs.length) {
      return (
        <Empty
          title="无执行日志"
          description="工作流执行后查看详细日志"
          image={<IconBug style={{ fontSize: '48px', color: '#ccc' }} />}
        />
      );
    }

    return (
      <div style={{ maxHeight: '600px', overflowY: 'auto' }}>
        {filteredLogs.map((log, index) => (
          <div 
            key={log.id}
            style={{ 
              padding: '12px',
              marginBottom: '8px',
              backgroundColor: log.level === 'error' ? '#fff2f0' : 
                             log.level === 'warning' ? '#fffbe6' : '#f0f9ff',
              border: `1px solid ${
                log.level === 'error' ? '#ffccc7' : 
                log.level === 'warning' ? '#ffe58f' : '#91d5ff'
              }`,
              borderRadius: '6px',
              borderLeft: `4px solid ${getLogLevelColor(log.level)}`
            }}
          >
            <div style={{ display: 'flex', alignItems: 'flex-start', gap: '8px' }}>
              <div style={{ marginTop: '2px' }}>
                {getLogLevelIcon(log.level)}
              </div>
              
              <div style={{ flex: 1 }}>
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '4px' }}>
                  <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                    <span style={{ fontSize: '13px', fontWeight: 500 }}>
                      {log.message}
                    </span>
                    {log.stepName && (
                      <Tag size="small" color="grey">
                        {log.stepName}
                      </Tag>
                    )}
                  </div>
                  <span style={{ fontSize: '11px', color: '#999' }}>
                    {new Date(log.timestamp).toLocaleTimeString()}
                  </span>
                </div>
                
                {log.details && (
                  <Collapse ghost>
                    <Panel header="详细信息" itemKey="details">
                      <pre style={{ 
                        fontSize: '11px', 
                        backgroundColor: 'rgba(0,0,0,0.02)', 
                        padding: '8px', 
                        borderRadius: '3px',
                        margin: 0,
                        whiteSpace: 'pre-wrap',
                        wordBreak: 'break-all'
                      }}>
                        {JSON.stringify(log.details, null, 2)}
                      </pre>
                    </Panel>
                  </Collapse>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  };

  // 渲染断点和观察点
  const renderBreakpoints = () => {
    return (
      <div>
        <div style={{ marginBottom: '16px' }}>
          <Button type="primary" size="small">
            添加断点
          </Button>
        </div>
        <Empty
          title="断点功能开发中"
          description="此功能将在后续版本中提供"
          image={<IconClock style={{ fontSize: '48px', color: '#ccc' }} />}
        />
      </div>
    );
  };

  if (!visible) return null;

  return (
    <div style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* 头部工具栏 */}
      <div style={{ 
        padding: '16px',
        borderBottom: '1px solid #d9d9d9',
        backgroundColor: 'white'
      }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <div>
            <h3 style={{ margin: 0, fontSize: '16px', display: 'flex', alignItems: 'center', gap: '8px' }}>
              <IconBug />
              调试面板
            </h3>
            <div style={{ fontSize: '12px', color: '#666', marginTop: '4px' }}>
              {execution ? `会话: ${execution.sessionId}` : '未开始执行'}
            </div>
          </div>
          
          <Space>
            <Input
              prefix={<IconSearch />}
              placeholder="搜索变量或日志"
              value={searchText}
              onChange={setSearchText}
              style={{ width: '200px' }}
              size="small"
            />
            
            <Button 
              icon={<IconRefresh />}
              onClick={() => {
                setSearchText('');
                setActiveTab('variables');
              }}
              size="small"
            >
              重置
            </Button>
          </Space>
        </div>
      </div>

      {/* 标签页内容 */}
      <div style={{ flex: 1, overflow: 'hidden' }}>
        <Tabs 
          activeKey={activeTab} 
          onChange={setActiveTab}
          type="card"
          style={{ height: '100%' }}
          contentStyle={{ height: 'calc(100% - 44px)', overflow: 'auto', padding: '16px' }}
        >
          <TabPane 
            tab={
              <span>
                变量 
                {variablesInfo.length > 0 && (
                  <Badge count={variablesInfo.length} style={{ marginLeft: '8px' }} />
                )}
              </span>
            } 
            itemKey="variables"
          >
            {renderVariableTree()}
          </TabPane>
          
          <TabPane 
            tab={
              <span>
                日志
                {logEntries.length > 0 && (
                  <Badge count={logEntries.length} style={{ marginLeft: '8px' }} />
                )}
              </span>
            } 
            itemKey="logs"
          >
            {renderExecutionLogs()}
          </TabPane>
          
          <TabPane tab="断点" itemKey="breakpoints">
            {renderBreakpoints()}
          </TabPane>
        </Tabs>
      </div>
    </div>
  );
};

export default WorkflowDebugPanel;