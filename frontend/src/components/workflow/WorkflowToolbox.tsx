import React, { useState, useCallback, useRef } from 'react';
import { Input, Collapse, Button, Tooltip, Divider } from '@douyinfe/semi-ui';
import { IconSearch, IconChevronDown, IconChevronRight } from '@douyinfe/semi-icons';
import type { WorkflowStep, Point } from '../../types/workflow';

export interface NodeTemplate {
  id: string;
  category: string;
  type: string;
  name: string;
  description: string;
  icon: string;
  color: string;
  defaultConfig: Record<string, any>;
  inputTypes?: string[];
  outputTypes?: string[];
  tags: string[];
}

export interface WorkflowToolboxProps {
  onNodeDragStart: (template: NodeTemplate, startPosition: Point) => void;
  onTemplateSelect?: (template: NodeTemplate) => void;
  searchQuery?: string;
  onSearchChange?: (query: string) => void;
  readonly?: boolean;
}

// 预定义的节点模板
const NODE_TEMPLATES: NodeTemplate[] = [
  // HTTP 相关
  {
    id: 'http-request',
    category: 'HTTP',
    type: 'HTTP_REQUEST',
    name: 'HTTP 请求',
    description: '发送 HTTP 请求到指定 URL',
    icon: '🌐',
    color: '#52c41a',
    defaultConfig: {
      method: 'GET',
      url: '',
      headers: {},
      timeout: 30000
    },
    outputTypes: ['json', 'text', 'html'],
    tags: ['http', 'api', 'web', 'request']
  },
  {
    id: 'webhook',
    category: 'HTTP',
    type: 'HTTP_REQUEST',
    name: 'Webhook',
    description: '等待 Webhook 回调',
    icon: '🪝',
    color: '#52c41a',
    defaultConfig: {
      path: '/webhook',
      method: 'POST',
      timeout: 300000
    },
    outputTypes: ['json'],
    tags: ['webhook', 'callback', 'trigger']
  },

  // 数据库操作
  {
    id: 'database-query',
    category: '数据库',
    type: 'DATABASE_OPERATION',
    name: '数据库查询',
    description: '执行数据库查询操作',
    icon: '🗄️',
    color: '#1890ff',
    defaultConfig: {
      query: 'SELECT * FROM table',
      parameters: []
    },
    inputTypes: ['json'],
    outputTypes: ['array', 'object'],
    tags: ['database', 'sql', 'query', 'data']
  },
  {
    id: 'database-update',
    category: '数据库',
    type: 'DATABASE_OPERATION',
    name: '数据库更新',
    description: '执行数据库更新操作',
    icon: '💾',
    color: '#1890ff',
    defaultConfig: {
      query: 'UPDATE table SET field = ? WHERE id = ?',
      parameters: []
    },
    inputTypes: ['json'],
    outputTypes: ['number'],
    tags: ['database', 'sql', 'update', 'data']
  },

  // 通知相关
  {
    id: 'email-notification',
    category: '通知',
    type: 'NOTIFICATION',
    name: '邮件通知',
    description: '发送邮件通知',
    icon: '📧',
    color: '#faad14',
    defaultConfig: {
      to: [],
      subject: '',
      template: 'default',
      variables: {}
    },
    inputTypes: ['json'],
    outputTypes: ['boolean'],
    tags: ['email', 'notification', 'alert', 'communication']
  },
  {
    id: 'sms-notification',
    category: '通知',
    type: 'NOTIFICATION',
    name: '短信通知',
    description: '发送短信通知',
    icon: '📱',
    color: '#faad14',
    defaultConfig: {
      phone: '',
      template: 'default',
      variables: {}
    },
    inputTypes: ['json'],
    outputTypes: ['boolean'],
    tags: ['sms', 'notification', 'alert', 'mobile']
  },

  // 流程控制
  {
    id: 'condition',
    category: '流程控制',
    type: 'CONDITION',
    name: '条件分支',
    description: '根据条件选择执行路径',
    icon: '❓',
    color: '#722ed1',
    defaultConfig: {
      expression: 'input.status === "success"',
      truePath: null,
      falsePath: null
    },
    inputTypes: ['any'],
    outputTypes: ['boolean'],
    tags: ['condition', 'branch', 'logic', 'control']
  },
  {
    id: 'loop',
    category: '流程控制',
    type: 'LOOP',
    name: '循环',
    description: '重复执行指定步骤',
    icon: '🔄',
    color: '#eb2f96',
    defaultConfig: {
      type: 'for',
      condition: 'i < 10',
      maxIterations: 100
    },
    inputTypes: ['array', 'number'],
    outputTypes: ['array'],
    tags: ['loop', 'iteration', 'repeat', 'control']
  },
  {
    id: 'delay',
    category: '流程控制',
    type: 'DELAY',
    name: '延时等待',
    description: '等待指定时间后继续执行',
    icon: '⏰',
    color: '#13c2c2',
    defaultConfig: {
      duration: 1000,
      unit: 'ms'
    },
    inputTypes: ['any'],
    outputTypes: ['any'],
    tags: ['delay', 'wait', 'sleep', 'timing']
  },

  // 通用动作
  {
    id: 'action',
    category: '基础动作',
    type: 'ACTION',
    name: '通用动作',
    description: '执行通用动作步骤',
    icon: '⚡',
    color: '#fa541c',
    defaultConfig: {
      actionType: 'custom',
      parameters: {}
    },
    inputTypes: ['any'],
    outputTypes: ['any'],
    tags: ['action', 'custom', 'execute']
  },

  // 人工审批
  {
    id: 'approval',
    category: '流程控制',
    type: 'APPROVAL',
    name: '人工审批',
    description: '等待人工审批后继续',
    icon: '✋',
    color: '#13c2c2',
    defaultConfig: {
      approvers: [],
      requireAll: false,
      timeout: 86400000
    },
    inputTypes: ['any'],
    outputTypes: ['boolean'],
    tags: ['approval', 'manual', 'review']
  },

  // 脚本执行
  {
    id: 'script',
    category: '脚本',
    type: 'SCRIPT',
    name: '脚本执行',
    description: '执行自定义脚本代码',
    icon: '📝',
    color: '#52c41a',
    defaultConfig: {
      language: 'javascript',
      script: '// 在这里编写脚本\nreturn { success: true };',
      timeout: 30000
    },
    inputTypes: ['any'],
    outputTypes: ['any'],
    tags: ['script', 'code', 'custom', 'javascript']
  },

  // 文件操作
  {
    id: 'file-read',
    category: '文件操作',
    type: 'FILE_OPERATION',
    name: '读取文件',
    description: '从文件系统读取文件',
    icon: '📁',
    color: '#fa8c16',
    defaultConfig: {
      operation: 'read',
      path: '',
      encoding: 'utf8'
    },
    outputTypes: ['text', 'binary'],
    tags: ['file', 'read', 'filesystem', 'io']
  },
  {
    id: 'file-write',
    category: '文件操作',
    type: 'FILE_OPERATION',
    name: '写入文件',
    description: '将数据写入文件系统',
    icon: '💾',
    color: '#fa8c16',
    defaultConfig: {
      operation: 'write',
      path: '',
      encoding: 'utf8',
      mode: 'w'
    },
    inputTypes: ['text', 'binary'],
    outputTypes: ['boolean'],
    tags: ['file', 'write', 'filesystem', 'io']
  }
];

const WorkflowToolbox: React.FC<WorkflowToolboxProps> = ({
  onNodeDragStart,
  onTemplateSelect,
  searchQuery = '',
  onSearchChange,
  readonly = false
}) => {
  const [expandedCategories, setExpandedCategories] = useState<string[]>(['HTTP', '通知']);
  const [dragStartPosition, setDragStartPosition] = useState<Point | null>(null);
  const toolboxRef = useRef<HTMLDivElement>(null);

  // 过滤节点模板
  const filteredTemplates = NODE_TEMPLATES.filter(template => {
    if (!searchQuery) return true;
    
    const query = searchQuery.toLowerCase();
    return template.name.toLowerCase().includes(query) ||
           template.description.toLowerCase().includes(query) ||
           template.tags.some(tag => tag.toLowerCase().includes(query)) ||
           template.category.toLowerCase().includes(query);
  });

  // 按分类分组
  const groupedTemplates = filteredTemplates.reduce((groups, template) => {
    const category = template.category;
    if (!groups[category]) {
      groups[category] = [];
    }
    groups[category].push(template);
    return groups;
  }, {} as Record<string, NodeTemplate[]>);

  // 处理分类展开/收起
  const handleCategoryToggle = useCallback((category: string) => {
    setExpandedCategories(prev => 
      prev.includes(category) 
        ? prev.filter(c => c !== category)
        : [...prev, category]
    );
  }, []);

  // 处理模板拖拽开始
  const handleTemplateDragStart = useCallback((e: React.MouseEvent, template: NodeTemplate) => {
    if (readonly) return;
    
    e.preventDefault();
    
    const rect = (e.target as HTMLElement).getBoundingClientRect();
    const startPosition = {
      x: e.clientX - rect.left + rect.width / 2,
      y: e.clientY - rect.top + rect.height / 2
    };
    
    setDragStartPosition(startPosition);
    onNodeDragStart(template, startPosition);
  }, [readonly, onNodeDragStart]);

  // 处理模板点击选择
  const handleTemplateClick = useCallback((template: NodeTemplate) => {
    if (onTemplateSelect) {
      onTemplateSelect(template);
    }
  }, [onTemplateSelect]);

  // 渲染节点模板
  const renderTemplate = useCallback((template: NodeTemplate) => (
    <div
      key={template.id}
      className="workflow-toolbox-item"
      draggable={!readonly}
      onDragStart={(e) => handleTemplateDragStart(e, template)}
      onClick={() => handleTemplateClick(template)}
      style={{
        display: 'flex',
        alignItems: 'center',
        padding: '8px 12px',
        margin: '4px 0',
        backgroundColor: 'white',
        border: '1px solid #d9d9d9',
        borderRadius: '6px',
        cursor: readonly ? 'default' : 'grab',
        transition: 'all 0.2s',
        userSelect: 'none'
      }}
      onMouseEnter={(e) => {
        if (!readonly) {
          (e.target as HTMLElement).style.borderColor = template.color;
          (e.target as HTMLElement).style.boxShadow = `0 2px 8px ${template.color}20`;
        }
      }}
      onMouseLeave={(e) => {
        if (!readonly) {
          (e.target as HTMLElement).style.borderColor = '#d9d9d9';
          (e.target as HTMLElement).style.boxShadow = 'none';
        }
      }}
    >
      <span 
        className="workflow-toolbox-item-icon"
        style={{ 
          fontSize: '16px', 
          marginRight: '8px',
          color: template.color 
        }}
      >
        {template.icon}
      </span>
      
      <div className="workflow-toolbox-item-content" style={{ flex: 1 }}>
        <div 
          className="workflow-toolbox-item-name"
          style={{ 
            fontSize: '13px', 
            fontWeight: 500,
            color: '#262626',
            marginBottom: '2px'
          }}
        >
          {template.name}
        </div>
        <div 
          className="workflow-toolbox-item-description"
          style={{ 
            fontSize: '11px', 
            color: '#8c8c8c',
            lineHeight: '1.2'
          }}
        >
          {template.description}
        </div>
      </div>

      {/* 输入/输出类型指示器 */}
      <div className="workflow-toolbox-item-types" style={{ marginLeft: '8px' }}>
        {template.inputTypes && (
          <Tooltip content={`输入类型: ${template.inputTypes.join(', ')}`}>
            <div 
              style={{
                width: '6px',
                height: '6px',
                borderRadius: '50%',
                backgroundColor: '#1890ff',
                marginBottom: '2px'
              }}
            />
          </Tooltip>
        )}
        {template.outputTypes && (
          <Tooltip content={`输出类型: ${template.outputTypes.join(', ')}`}>
            <div 
              style={{
                width: '6px',
                height: '6px',
                borderRadius: '50%',
                backgroundColor: '#52c41a'
              }}
            />
          </Tooltip>
        )}
      </div>
    </div>
  ), [readonly, handleTemplateDragStart, handleTemplateClick]);

  return (
    <div 
      className="workflow-toolbox"
      ref={toolboxRef}
      style={{
        width: '100%',
        height: '100%',
        backgroundColor: '#fafafa',
        border: '1px solid #d9d9d9',
        borderRadius: '8px',
        overflow: 'hidden',
        display: 'flex',
        flexDirection: 'column'
      }}
    >
      {/* 工具箱头部 */}
      <div 
        className="workflow-toolbox-header"
        style={{
          padding: '16px',
          borderBottom: '1px solid #d9d9d9',
          backgroundColor: 'white'
        }}
      >
        <h4 style={{ margin: '0 0 12px 0', fontSize: '14px', fontWeight: 600 }}>
          组件工具箱
        </h4>
        
        {/* 搜索框 */}
        <Input
          prefix={<IconSearch />}
          placeholder="搜索组件..."
          value={searchQuery}
          onChange={(value) => onSearchChange?.(value)}
          size="small"
          style={{ width: '100%' }}
        />

        {/* 统计信息 */}
        <div style={{ 
          marginTop: '8px', 
          fontSize: '11px', 
          color: '#8c8c8c',
          textAlign: 'center'
        }}>
          共 {filteredTemplates.length} 个组件
        </div>
      </div>

      {/* 工具箱内容 */}
      <div 
        className="workflow-toolbox-content"
        style={{
          flex: 1,
          overflow: 'auto',
          padding: '8px'
        }}
      >
        {Object.keys(groupedTemplates).length === 0 ? (
          <div style={{
            padding: '32px 16px',
            textAlign: 'center',
            color: '#8c8c8c',
            fontSize: '13px'
          }}>
            {searchQuery ? '未找到匹配的组件' : '没有可用组件'}
          </div>
        ) : (
          Object.entries(groupedTemplates).map(([category, templates]) => {
            const isExpanded = expandedCategories.includes(category);
            
            return (
              <div key={category} className="workflow-toolbox-category" style={{ marginBottom: '12px' }}>
                {/* 分类标题 */}
                <div
                  className="workflow-toolbox-category-header"
                  onClick={() => handleCategoryToggle(category)}
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    padding: '6px 8px',
                    backgroundColor: 'white',
                    border: '1px solid #d9d9d9',
                    borderRadius: '4px',
                    cursor: 'pointer',
                    fontSize: '12px',
                    fontWeight: 500,
                    marginBottom: isExpanded ? '8px' : '0'
                  }}
                >
                  {isExpanded ? <IconChevronDown /> : <IconChevronRight />}
                  <span style={{ marginLeft: '4px', flex: 1 }}>{category}</span>
                  <span style={{ 
                    color: '#8c8c8c', 
                    fontSize: '11px',
                    backgroundColor: '#f0f0f0',
                    padding: '1px 6px',
                    borderRadius: '8px'
                  }}>
                    {templates.length}
                  </span>
                </div>

                {/* 分类内容 */}
                {isExpanded && (
                  <div className="workflow-toolbox-category-content">
                    {templates.map(template => renderTemplate(template))}
                  </div>
                )}
              </div>
            );
          })
        )}
      </div>

      {/* 工具箱底部 */}
      {!readonly && (
        <div style={{
          padding: '12px 16px',
          borderTop: '1px solid #d9d9d9',
          backgroundColor: 'white',
          fontSize: '11px',
          color: '#8c8c8c',
          textAlign: 'center'
        }}>
          💡 将组件拖拽到画布上创建节点
        </div>
      )}
    </div>
  );
};

export default WorkflowToolbox;
export { NODE_TEMPLATES };