import React, { useState, useEffect } from 'react';
import { <PERSON>, Button, Empty, Divider, Collapse, Badge } from '@douyinfe/semi-ui';
import { IconSettings, IconChevronRight, IconClose, IconCheck, IconAlertTriangle } from '@douyinfe/semi-icons';
import StepFormFactory from './steps/StepFormFactory';
import type { 
  DesignerNode, 
  WorkflowStep, 
  ValidationResult 
} from '../../types/workflow';

const { Panel } = Collapse;

export interface WorkflowPropertiesPanelProps {
  selectedNodes: string[];
  nodes: DesignerNode[];
  onNodeUpdate: (nodeId: string, updates: Partial<DesignerNode>) => void;
  onClose?: () => void;
  availableVariables?: Array<{
    name: string;
    type: string;
    description: string;
  }>;
  readonly?: boolean;
}

const WorkflowPropertiesPanel: React.FC<WorkflowPropertiesPanelProps> = ({
  selectedNodes,
  nodes,
  onNodeUpdate,
  onClose,
  availableVariables = [],
  readonly = false
}) => {
  const [validation, setValidation] = useState<Record<string, ValidationResult>>({});
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState<Record<string, boolean>>({});

  // 获取选中的节点
  const selectedNode = selectedNodes.length === 1 
    ? nodes.find(node => node.id === selectedNodes[0])
    : null;

  // 处理步骤配置变化
  const handleStepChange = (nodeId: string, updatedStep: WorkflowStep) => {
    const node = nodes.find(n => n.id === nodeId);
    if (!node) return;

    const updates: Partial<DesignerNode> = {
      step: updatedStep,
      data: {
        ...node.data,
        step: updatedStep
      }
    };

    onNodeUpdate(nodeId, updates);
    setHasUnsavedChanges({ ...hasUnsavedChanges, [nodeId]: true });
  };

  // 处理验证结果
  const handleValidation = (nodeId: string, isValid: boolean, errors: string[]) => {
    const validationResult: ValidationResult = {
      isValid,
      errors,
      warnings: []
    };

    setValidation({ ...validation, [nodeId]: validationResult });

    // 更新节点的验证状态
    const node = nodes.find(n => n.id === nodeId);
    if (node) {
      const updates: Partial<DesignerNode> = {
        data: {
          ...node.data,
          validation: {
            isValid,
            errors
          }
        }
      };
      onNodeUpdate(nodeId, updates);
    }
  };

  // 保存配置
  const handleSave = (nodeId: string) => {
    // 这里可以添加保存到服务器的逻辑
    setHasUnsavedChanges({ ...hasUnsavedChanges, [nodeId]: false });
  };

  // 重置配置
  const handleReset = (nodeId: string) => {
    const node = nodes.find(n => n.id === nodeId);
    if (node) {
      // 重置为默认配置
      handleStepChange(nodeId, { ...node.step });
      setHasUnsavedChanges({ ...hasUnsavedChanges, [nodeId]: false });
    }
  };

  // 清理状态
  useEffect(() => {
    const currentNodeIds = selectedNodes;
    const staleIds = Object.keys(hasUnsavedChanges).filter(
      id => !currentNodeIds.includes(id)
    );
    
    if (staleIds.length > 0) {
      const newUnsavedChanges = { ...hasUnsavedChanges };
      const newValidation = { ...validation };
      
      staleIds.forEach(id => {
        delete newUnsavedChanges[id];
        delete newValidation[id];
      });
      
      setHasUnsavedChanges(newUnsavedChanges);
      setValidation(newValidation);
    }
  }, [selectedNodes, hasUnsavedChanges, validation]);

  // 没有选中节点时的状态
  if (selectedNodes.length === 0) {
    return (
      <div className="workflow-properties-panel">
        <Card 
          title={
            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
              <span>
                <IconSettings style={{ marginRight: '8px' }} />
                属性配置
              </span>
              {onClose && (
                <Button
                  icon={<IconClose />}
                  theme="borderless"
                  size="small"
                  onClick={onClose}
                />
              )}
            </div>
          }
          style={{ height: '100%' }}
          bodyStyle={{ 
            display: 'flex', 
            alignItems: 'center', 
            justifyContent: 'center',
            padding: '40px 20px'
          }}
        >
          <Empty
            title="未选中任何步骤"
            description="请点击画布上的节点来配置步骤属性"
            image={<IconSettings style={{ fontSize: '48px', color: '#ccc' }} />}
          />
        </Card>
      </div>
    );
  }

  // 选中多个节点时的状态
  if (selectedNodes.length > 1) {
    return (
      <div className="workflow-properties-panel">
        <Card 
          title={
            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
              <span>
                <IconSettings style={{ marginRight: '8px' }} />
                批量操作
              </span>
              {onClose && (
                <Button
                  icon={<IconClose />}
                  theme="borderless"
                  size="small"
                  onClick={onClose}
                />
              )}
            </div>
          }
          style={{ height: '100%' }}
          bodyStyle={{ padding: '20px' }}
        >
          <div style={{ textAlign: 'center', marginBottom: '20px' }}>
            <Badge count={selectedNodes.length} style={{ backgroundColor: '#1890ff' }}>
              <IconSettings style={{ fontSize: '32px', color: '#1890ff' }} />
            </Badge>
            <div style={{ marginTop: '12px', fontSize: '14px', color: '#666' }}>
              已选中 {selectedNodes.length} 个步骤
            </div>
          </div>

          <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
            <Button 
              type="primary" 
              block 
              disabled={readonly}
              onClick={() => {
                // TODO: 实现批量删除功能
                console.log('批量删除:', selectedNodes);
              }}
            >
              批量删除
            </Button>
            <Button 
              block 
              disabled={readonly}
              onClick={() => {
                // TODO: 实现批量复制功能
                console.log('批量复制:', selectedNodes);
              }}
            >
              批量复制
            </Button>
            <Button 
              block 
              disabled={readonly}
              onClick={() => {
                // TODO: 实现批量配置功能
                console.log('批量配置:', selectedNodes);
              }}
            >
              批量配置
            </Button>
          </div>

          <Divider />

          {/* 选中节点列表 */}
          <div>
            <h5 style={{ margin: '0 0 12px 0', fontSize: '13px', color: '#666' }}>
              选中的步骤:
            </h5>
            <div style={{ maxHeight: '200px', overflowY: 'auto' }}>
              {selectedNodes.map(nodeId => {
                const node = nodes.find(n => n.id === nodeId);
                if (!node) return null;

                const nodeValidation = validation[nodeId];
                const isValid = nodeValidation?.isValid !== false;

                return (
                  <div 
                    key={nodeId}
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'space-between',
                      padding: '8px 12px',
                      marginBottom: '4px',
                      backgroundColor: '#fafafa',
                      borderRadius: '4px',
                      border: isValid ? '1px solid #d9d9d9' : '1px solid #ff4d4f'
                    }}
                  >
                    <div style={{ flex: 1 }}>
                      <div style={{ fontSize: '12px', fontWeight: 500 }}>
                        {node.step.name || `${node.step.type} 步骤`}
                      </div>
                      <div style={{ fontSize: '11px', color: '#666' }}>
                        {node.step.type}
                      </div>
                    </div>
                    <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
                      {hasUnsavedChanges[nodeId] && (
                        <Badge dot style={{ backgroundColor: '#faad14' }} />
                      )}
                      {isValid ? (
                        <IconCheck style={{ color: '#52c41a', fontSize: '12px' }} />
                      ) : (
                        <IconAlertTriangle style={{ color: '#ff4d4f', fontSize: '12px' }} />
                      )}
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </Card>
      </div>
    );
  }

  // 单个节点配置
  if (!selectedNode) return null;

  const nodeValidation = validation[selectedNode.id];
  const hasChanges = hasUnsavedChanges[selectedNode.id] || false;
  const isValid = nodeValidation?.isValid !== false;

  return (
    <div className="workflow-properties-panel">
      <Card
        title={
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <IconSettings style={{ marginRight: '8px' }} />
              <span>步骤配置</span>
              {hasChanges && (
                <Badge dot style={{ backgroundColor: '#faad14', marginLeft: '8px' }} />
              )}
              {!isValid && (
                <IconAlertTriangle style={{ color: '#ff4d4f', marginLeft: '8px', fontSize: '16px' }} />
              )}
            </div>
            {onClose && (
              <Button
                icon={<IconClose />}
                theme="borderless"
                size="small"
                onClick={onClose}
              />
            )}
          </div>
        }
        style={{ height: '100%', display: 'flex', flexDirection: 'column' }}
        bodyStyle={{ flex: 1, overflow: 'auto', padding: 0 }}
        footerLine
        footerStyle={{ padding: '12px 16px' }}
        footer={
          !readonly && (
            <div style={{ display: 'flex', gap: '8px', justifyContent: 'flex-end' }}>
              <Button
                size="small"
                onClick={() => handleReset(selectedNode.id)}
                disabled={!hasChanges}
              >
                重置
              </Button>
              <Button
                type="primary"
                size="small"
                onClick={() => handleSave(selectedNode.id)}
                disabled={!hasChanges || !isValid}
              >
                保存
              </Button>
            </div>
          )
        }
      >
        {/* 节点基本信息 */}
        <div style={{
          padding: '16px',
          borderBottom: '1px solid #f0f0f0',
          backgroundColor: '#fafafa'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', marginBottom: '8px' }}>
            <div style={{
              fontSize: '16px',
              marginRight: '8px',
              color: 'white',
              backgroundColor: '#1890ff',
              width: '24px',
              height: '24px',
              borderRadius: '4px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}>
              ⚙️
            </div>
            <div>
              <div style={{ fontSize: '14px', fontWeight: 500 }}>
                {selectedNode.step.name || `${selectedNode.step.type} 步骤`}
              </div>
              <div style={{ fontSize: '12px', color: '#666' }}>
                {selectedNode.step.type} • 步骤 {selectedNode.step.index}
              </div>
            </div>
          </div>
        </div>

        {/* 验证错误信息 */}
        {!isValid && nodeValidation?.errors && nodeValidation.errors.length > 0 && (
          <div style={{
            padding: '12px 16px',
            backgroundColor: '#fff2f0',
            borderBottom: '1px solid #ffccc7'
          }}>
            <div style={{ color: '#ff4d4f', fontSize: '12px', fontWeight: 500, marginBottom: '4px' }}>
              <IconAlertTriangle style={{ marginRight: '4px' }} />
              配置错误:
            </div>
            {nodeValidation.errors.map((error, index) => (
              <div key={index} style={{ color: '#ff4d4f', fontSize: '12px', marginLeft: '16px' }}>
                • {error}
              </div>
            ))}
          </div>
        )}

        {/* 步骤配置表单 */}
        <div style={{ flex: 1 }}>
          <StepFormFactory
            step={selectedNode.step}
            onChange={(updatedStep) => handleStepChange(selectedNode.id, updatedStep)}
            onValidation={(isValid, errors) => handleValidation(selectedNode.id, isValid, errors)}
            availableVariables={availableVariables}
          />
        </div>
      </Card>
    </div>
  );
};

export default WorkflowPropertiesPanel;