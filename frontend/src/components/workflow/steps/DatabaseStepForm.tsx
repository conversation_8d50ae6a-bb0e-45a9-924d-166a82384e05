import React, { useState } from 'react';
import { Form, Input, Select, Button, Space, Tabs, TabPane, List, Tag, TextArea } from '@douyinfe/semi-ui';
import { IconInfoCircle, IconServer } from '@douyinfe/semi-icons';
import BaseStepForm from './BaseStepForm';
import type { WorkflowStep, StepFormProps } from '../../../types/workflow';
const { Option } = Select;

/**
 * 数据库操作步骤配置表单
 */
const DatabaseStepForm: React.FC<StepFormProps> = ({
  step,
  onChange,
  onValidation,
  availableVariables = []
}) => {
  const [formApi, setFormApi] = useState<any>(null);
  
  // SQL 模板
  const sqlTemplates = {
    SELECT: {
      name: '查询数据',
      sql: 'SELECT * FROM table_name WHERE condition = ?',
      description: '从数据库表中查询数据'
    },
    INSERT: {
      name: '插入数据',
      sql: 'INSERT INTO table_name (column1, column2) VALUES (?, ?)',
      description: '向数据库表中插入新数据'
    },
    UPDATE: {
      name: '更新数据',
      sql: 'UPDATE table_name SET column1 = ? WHERE id = ?',
      description: '更新数据库表中的现有数据'
    },
    DELETE: {
      name: '删除数据',
      sql: 'DELETE FROM table_name WHERE id = ?',
      description: '从数据库表中删除数据'
    },
    COUNT: {
      name: '统计数据',
      sql: 'SELECT COUNT(*) as count FROM table_name WHERE condition = ?',
      description: '统计符合条件的记录数'
    },
    EXISTS: {
      name: '检查存在',
      sql: 'SELECT EXISTS(SELECT 1 FROM table_name WHERE condition = ?) as exists',
      description: '检查是否存在符合条件的记录'
    }
  };

  // 处理配置变化
  const handleConfigChange = (values: any) => {
    const updatedStep: WorkflowStep = {
      ...step,
      config: {
        ...step.config,
        ...values
      }
    };

    // 验证配置
    const errors: string[] = [];
    
    if (!values.query?.trim()) {
      errors.push('请输入SQL查询语句');
    }

    if (!values.connection) {
      errors.push('请选择数据库连接');
    }

    // 验证参数
    if (values.parameters && Array.isArray(values.parameters)) {
      values.parameters.forEach((param: any, index: number) => {
        if (!param.name?.trim()) {
          errors.push(`参数 ${index + 1} 的名称不能为空`);
        }
      });
    }

    onValidation(errors.length === 0, errors);
    onChange(updatedStep);
  };

  // 应用SQL模板
  const applyTemplate = (templateKey: string) => {
    const template = sqlTemplates[templateKey as keyof typeof sqlTemplates];
    if (template && formApi) {
      formApi.setValue('query', template.sql);
      handleConfigChange(formApi.getValues());
    }
  };

  // 解析参数
  const parseParameters = (query: string) => {
    const paramMatches = query.match(/\?/g);
    const namedParamMatches = query.match(/:(\w+)/g);
    
    let params: Array<{ name: string; type: string; description: string }> = [];
    
    if (paramMatches) {
      params = paramMatches.map((_, index) => ({
        name: `param${index + 1}`,
        type: 'string',
        description: `参数 ${index + 1}`
      }));
    }
    
    if (namedParamMatches) {
      params = namedParamMatches.map(match => ({
        name: match.substring(1),
        type: 'string',
        description: `命名参数 ${match}`
      }));
    }
    
    return params;
  };

  return (
    <div className="database-step-form">
      {/* 基础配置 */}
      <BaseStepForm
        step={step}
        onChange={onChange}
        onValidation={onValidation}
        availableVariables={availableVariables}
      />

      {/* 数据库特定配置 */}
      <div style={{ marginTop: '24px', borderTop: '1px solid #d9d9d9', paddingTop: '16px' }}>
        <h4 style={{ margin: '0 0 16px 0', fontSize: '14px', fontWeight: 600 }}>
          <IconServer style={{ marginRight: '8px' }} />
          数据库操作配置
        </h4>

        <Form
          getFormApi={setFormApi}
          initValues={{
            connection: step.config.connection || '',
            query: step.config.query || '',
            parameters: step.config.parameters || [],
            timeout: step.config.timeout || 30000,
            transactional: step.config.transactional || false,
            returnFormat: step.config.returnFormat || 'object'
          }}
          onValueChange={handleConfigChange}
          labelPosition="top"
        >
          <Tabs type="card">
            {/* SQL查询 */}
            <TabPane tab="SQL查询" itemKey="query">
              <div style={{ padding: '16px 0' }}>
                <Form.Select
                  field="connection"
                  label="数据库连接"
                  placeholder="选择数据库连接"
                  rules={[{ required: true, message: '请选择数据库连接' }]}
                >
                  <Option value="primary">主数据库</Option>
                  <Option value="readonly">只读数据库</Option>
                  <Option value="cache">缓存数据库</Option>
                  <Option value="logs">日志数据库</Option>
                </Form.Select>

                {/* SQL模板选择 */}
                <div style={{ marginBottom: '16px' }}>
                  <label style={{ display: 'block', marginBottom: '8px', fontSize: '12px', color: '#666' }}>
                    选择SQL模板:
                  </label>
                  <Space wrap>
                    {Object.entries(sqlTemplates).map(([key, template]) => (
                      <Button
                        key={key}
                        size="small"
                        onClick={() => applyTemplate(key)}
                        style={{ marginBottom: '4px' }}
                      >
                        {template.name}
                      </Button>
                    ))}
                  </Space>
                </div>

                <Form.TextArea
                  field="query"
                  label="SQL查询语句"
                  placeholder="输入SQL查询语句，使用 ? 作为参数占位符"
                  autosize={{ minRows: 6, maxRows: 12 }}
                  rules={[{ required: true, message: '请输入SQL查询语句' }]}
                />

                <div style={{ marginTop: '12px' }}>
                  <label style={{ display: 'block', marginBottom: '8px', fontSize: '12px', color: '#666' }}>
                    <IconInfoCircle style={{ marginRight: '4px' }} />
                    SQL语法提示:
                  </label>
                  <div style={{ fontSize: '11px', color: '#999', lineHeight: '1.4' }}>
                    • 使用 <code>?</code> 作为位置参数占位符<br/>
                    • 使用 <code>:paramName</code> 作为命名参数<br/>
                    • 支持变量替换: <code>${'{variable_name}'}</code><br/>
                    • 建议使用参数化查询防止SQL注入
                  </div>
                </div>
              </div>
            </TabPane>

            {/* 参数配置 */}
            <TabPane tab="参数配置" itemKey="parameters">
              <div style={{ padding: '16px 0' }}>
                {formApi?.getValue('query') && (
                  <div style={{ marginBottom: '16px' }}>
                    <Button
                      onClick={() => {
                        const query = formApi.getValue('query');
                        const detectedParams = parseParameters(query);
                        formApi.setValue('parameters', detectedParams);
                      }}
                      size="small"
                    >
                      自动检测参数
                    </Button>
                  </div>
                )}

                {/* 简化的参数配置 */}
                <div>
                  <div style={{ marginBottom: '16px', fontWeight: 500 }}>查询参数</div>
                  <Form.TextArea
                    field="parameters"
                    placeholder="请输入查询参数，JSON格式，例如：&#10;{&#10;  &quot;userId&quot;: &quot;123&quot;,&#10;  &quot;status&quot;: &quot;active&quot;&#10;}"
                    rows={4}
                    style={{ width: '100%' }}
                  />
                </div>
              </div>
            </TabPane>

            {/* 执行选项 */}
            <TabPane tab="执行选项" itemKey="options">
              <div style={{ padding: '16px 0' }}>
                <Form.InputNumber
                  field="timeout"
                  label="查询超时时间（毫秒）"
                  min={1000}
                  max={300000}
                  step={1000}
                />

                <Form.Switch
                  field="transactional"
                  label="事务执行"
                />

                <Form.Select
                  field="returnFormat"
                  label="返回数据格式"
                >
                  <Option value="object">对象数组 (推荐)</Option>
                  <Option value="array">二维数组</Option>
                  <Option value="raw">原始结果</Option>
                  <Option value="count">仅返回影响行数</Option>
                </Form.Select>
              </div>
            </TabPane>
          </Tabs>
        </Form>

        {/* SQL模板列表 */}
        <div style={{
          marginTop: '16px',
          padding: '12px',
          backgroundColor: '#f0f9ff',
          border: '1px solid #91d5ff',
          borderRadius: '6px'
        }}>
          <div style={{ color: '#1890ff', fontSize: '12px', fontWeight: 500, marginBottom: '8px' }}>
            <IconInfoCircle style={{ marginRight: '4px' }} />
            常用SQL模板:
          </div>
          <List
            dataSource={Object.entries(sqlTemplates)}
            renderItem={([key, template]) => (
              <List.Item style={{ padding: '4px 0' }}>
                <div style={{ width: '100%' }}>
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <div>
                      <Tag color="blue" size="small">{key}</Tag>
                      <span style={{ fontSize: '12px', marginLeft: '8px' }}>{template.name}</span>
                    </div>
                    <Button
                      size="small"
                      type="tertiary"
                      onClick={() => applyTemplate(key)}
                    >
                      使用
                    </Button>
                  </div>
                  <div style={{ fontSize: '11px', color: '#666', marginTop: '4px', fontFamily: 'monospace' }}>
                    {template.sql}
                  </div>
                </div>
              </List.Item>
            )}
          />
        </div>

        {/* 结果变量说明 */}
        <div style={{
          marginTop: '16px',
          padding: '12px',
          backgroundColor: '#f6ffed',
          border: '1px solid #b7eb8f',
          borderRadius: '6px'
        }}>
          <div style={{ color: '#389e0d', fontSize: '12px', fontWeight: 500, marginBottom: '8px' }}>
            <IconInfoCircle style={{ marginRight: '4px' }} />
            返回变量说明:
          </div>
          <div style={{ color: '#52c41a', fontSize: '11px', lineHeight: '1.5' }}>
            • <code>result.data</code> - 查询结果数据<br/>
            • <code>result.rowCount</code> - 影响的行数<br/>
            • <code>result.insertId</code> - 插入操作的自增ID（如果适用）<br/>
            • <code>result.duration</code> - 查询执行时间（毫秒）<br/>
            • <code>result.connection</code> - 使用的数据库连接名称
          </div>
        </div>
      </div>
    </div>
  );
};

export default DatabaseStepForm;