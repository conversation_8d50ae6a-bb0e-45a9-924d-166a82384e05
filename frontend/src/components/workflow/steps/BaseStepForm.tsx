import React, { useState, useEffect } from 'react';
import { Form, Input, Select, InputNumber, Switch, Button, Space, Collapse, Divider, TextArea } from '@douyinfe/semi-ui';
import { IconInfoCircle, IconPlus, IconDelete } from '@douyinfe/semi-icons';
import type { WorkflowStep, StepFormProps } from '../../../types/workflow';
const { Option } = Select;
const { Panel } = Collapse;

/**
 * 基础步骤表单组件
 * 提供所有工作流步骤的通用配置项
 */
const BaseStepForm: React.FC<StepFormProps> = ({
  step,
  onChange,
  onValidation,
  availableVariables = []
}) => {
  const [formApi, setFormApi] = useState<any>(null);
  const [errors, setErrors] = useState<string[]>([]);

  // 验证表单
  const validateForm = (values: any) => {
    const newErrors: string[] = [];

    if (!values.name?.trim()) {
      newErrors.push('步骤名称不能为空');
    }

    if (!values.type) {
      newErrors.push('步骤类型不能为空');
    }

    if (values.timeout && (values.timeout < 1000 || values.timeout > 3600000)) {
      newErrors.push('超时时间必须在1秒到1小时之间');
    }

    setErrors(newErrors);
    onValidation(newErrors.length === 0, newErrors);
    return newErrors.length === 0;
  };

  // 处理表单值变化
  const handleFormChange = (values: any) => {
    const updatedStep: WorkflowStep = {
      ...step,
      name: values.name || step.name,
      description: values.description || step.description,
      timeout: values.timeout || step.timeout,
      config: {
        ...step.config,
        ...values.config
      },
      condition: values.condition ? {
        expression: values.condition.expression || '',
        variables: values.condition.variables || []
      } : undefined,
      retry: values.enableRetry ? {
        maxAttempts: values.retry?.maxAttempts || 3,
        delay: values.retry?.delay || 1000,
        backoffMultiplier: values.retry?.backoffMultiplier || 2
      } : undefined,
      onSuccess: values.onSuccess ? {
        nextStep: values.onSuccess.nextStep,
        variables: values.onSuccess.variables
      } : undefined,
      onFailure: values.onFailure ? {
        nextStep: values.onFailure.nextStep,
        rollback: values.onFailure.rollback || false
      } : undefined
    };

    validateForm(values);
    onChange(updatedStep);
  };

  // 初始化表单值
  const getInitialValues = () => {
    return {
      name: step.name,
      description: step.description || '',
      timeout: step.timeout || 30000,
      config: step.config || {},
      condition: step.condition,
      enableRetry: !!step.retry,
      retry: step.retry || {
        maxAttempts: 3,
        delay: 1000,
        backoffMultiplier: 2
      },
      onSuccess: step.onSuccess,
      onFailure: step.onFailure
    };
  };

  useEffect(() => {
    if (formApi) {
      const values = formApi.getValues();
      validateForm(values);
    }
  }, [step]);

  return (
    <Form
      getFormApi={setFormApi}
      initValues={getInitialValues()}
      onValueChange={handleFormChange}
      labelPosition="top"
      style={{ padding: '16px' }}
    >
      {/* 基本信息 */}
      <div className="step-form-section">
        <h4 style={{ margin: '0 0 16px 0', fontSize: '14px', fontWeight: 600 }}>
          基本信息
        </h4>
        
        <Form.Input
          field="name"
          label="步骤名称"
          placeholder="请输入步骤名称"
          rules={[{ required: true, message: '请输入步骤名称' }]}
        />

        <Form.TextArea
          field="description"
          label="步骤描述"
          placeholder="请输入步骤描述（可选）"
          autosize
          maxCount={200}
        />

        <Form.InputNumber
          field="timeout"
          label="超时时间（毫秒）"
          placeholder="30000"
          min={1000}
          max={3600000}
          step={1000}
          formatter={(value) => `${value}ms`}
          parser={(value) => (value?.replace('ms', '') || '30000')}
        />
      </div>

      <Divider margin="24px" />

      {/* 高级配置 */}
      <Collapse defaultActiveKey={[]}>
        {/* 条件执行 */}
        <Panel header="条件执行" itemKey="condition">
          <div style={{ padding: '12px 0' }}>
            <Form.TextArea
              field="condition.expression"
              label="执行条件（JavaScript表达式）"
              placeholder="例如: input.status === 'success' && input.code === 200"
              autosize={{ minRows: 2, maxRows: 4 }}
            />
            
            <div style={{ marginTop: '12px' }}>
              <label style={{ display: 'block', marginBottom: '8px', fontSize: '12px', color: '#666' }}>
                <IconInfoCircle style={{ marginRight: '4px' }} />
                可用变量: {availableVariables.map(v => v.name).join(', ')}
              </label>
            </div>
          </div>
        </Panel>

        {/* 重试配置 */}
        <Panel header="重试配置" itemKey="retry">
          <div style={{ padding: '12px 0' }}>
            <Form.Switch
              field="enableRetry"
              label="启用重试机制"
            />

            <Form.InputNumber
              field="retry.maxAttempts"
              label="最大重试次数"
              min={1}
              max={10}
              disabled={!formApi?.getValue('enableRetry')}
            />

            <Form.InputNumber
              field="retry.delay"
              label="重试延迟（毫秒）"
              min={100}
              max={60000}
              disabled={!formApi?.getValue('enableRetry')}
            />

            <Form.InputNumber
              field="retry.backoffMultiplier"
              label="退避倍数"
              min={1}
              max={5}
              step={0.1}
              disabled={!formApi?.getValue('enableRetry')}
            />
          </div>
        </Panel>

        {/* 成功处理 */}
        <Panel header="成功处理" itemKey="success">
          <div style={{ padding: '12px 0' }}>
            <Form.InputNumber
              field="onSuccess.nextStep"
              label="成功后跳转到步骤"
              placeholder="留空则继续下一步"
              min={0}
            />

            <Form.Input
              field="onSuccess.variables"
              label="设置变量（JSON格式）"
              placeholder='{"key": "value"}'
            />
          </div>
        </Panel>

        {/* 失败处理 */}
        <Panel header="失败处理" itemKey="failure">
          <div style={{ padding: '12px 0' }}>
            <Form.InputNumber
              field="onFailure.nextStep"
              label="失败后跳转到步骤"
              placeholder="留空则终止执行"
              min={0}
            />

            <Form.Switch
              field="onFailure.rollback"
              label="启用回滚"
            />
          </div>
        </Panel>
      </Collapse>

      {/* 错误提示 */}
      {errors.length > 0 && (
        <div style={{
          marginTop: '16px',
          padding: '12px',
          backgroundColor: '#fff2f0',
          border: '1px solid #ffccc7',
          borderRadius: '6px'
        }}>
          <div style={{ color: '#ff4d4f', fontSize: '12px', fontWeight: 500, marginBottom: '4px' }}>
            配置错误:
          </div>
          {errors.map((error, index) => (
            <div key={index} style={{ color: '#ff4d4f', fontSize: '12px' }}>
              • {error}
            </div>
          ))}
        </div>
      )}

      {/* 可用变量提示 */}
      {availableVariables.length > 0 && (
        <div style={{
          marginTop: '16px',
          padding: '12px',
          backgroundColor: '#f6ffed',
          border: '1px solid #b7eb8f',
          borderRadius: '6px'
        }}>
          <div style={{ color: '#389e0d', fontSize: '12px', fontWeight: 500, marginBottom: '8px' }}>
            <IconInfoCircle style={{ marginRight: '4px' }} />
            可用变量:
          </div>
          {availableVariables.map((variable, index) => (
            <div key={index} style={{ 
              color: '#52c41a', 
              fontSize: '11px', 
              marginBottom: '4px',
              fontFamily: 'monospace',
              backgroundColor: 'rgba(82, 196, 26, 0.1)',
              padding: '2px 6px',
              borderRadius: '3px',
              display: 'inline-block',
              marginRight: '8px'
            }}>
              <code>{variable.name}</code>
              <span style={{ color: '#666', marginLeft: '6px' }}>
                ({variable.type}) {variable.description}
              </span>
            </div>
          ))}
        </div>
      )}
    </Form>
  );
};

export default BaseStepForm;