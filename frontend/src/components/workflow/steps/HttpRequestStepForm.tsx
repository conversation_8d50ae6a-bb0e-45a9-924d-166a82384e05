import React, { useState, useEffect } from 'react';
import { Form, Input, Select, Switch, Button, Space, TagInput, Collapse, Tabs, TabPane } from '@douyinfe/semi-ui';
import { IconPlus, IconDelete, IconInfo } from '@douyinfe/semi-icons';
import BaseStepForm from './BaseStepForm';
import type { WorkflowStep, StepFormProps } from '../../../types/workflow';

const { TextArea } = Input;
const { Option } = Select;
const { Panel } = Collapse;

/**
 * HTTP请求步骤配置表单
 */
const HttpRequestStepForm: React.FC<StepFormProps> = ({
  step,
  onChange,
  onValidation,
  availableVariables = []
}) => {
  const [formApi, setFormApi] = useState<any>(null);
  const [headers, setHeaders] = useState<Array<{ key: string; value: string }>>([]);
  const [queryParams, setQueryParams] = useState<Array<{ key: string; value: string }>>([]);

  // 初始化头部和查询参数
  useEffect(() => {
    if (step.config.headers) {
      const headerArray = Object.entries(step.config.headers).map(([key, value]) => ({
        key,
        value: String(value)
      }));
      setHeaders(headerArray);
    }

    if (step.config.queryParams) {
      const paramArray = Object.entries(step.config.queryParams).map(([key, value]) => ({
        key,
        value: String(value)
      }));
      setQueryParams(paramArray);
    }
  }, [step.config]);

  // 处理表单变化
  const handleConfigChange = (values: any) => {
    const headersObject = headers.reduce((obj, { key, value }) => {
      if (key.trim()) {
        obj[key.trim()] = value;
      }
      return obj;
    }, {} as Record<string, string>);

    const queryParamsObject = queryParams.reduce((obj, { key, value }) => {
      if (key.trim()) {
        obj[key.trim()] = value;
      }
      return obj;
    }, {} as Record<string, string>);

    const updatedStep: WorkflowStep = {
      ...step,
      config: {
        ...step.config,
        ...values,
        headers: headersObject,
        queryParams: queryParamsObject
      }
    };

    // 验证配置
    const errors: string[] = [];
    
    if (!values.url?.trim()) {
      errors.push('请输入请求URL');
    } else {
      try {
        new URL(values.url);
      } catch {
        errors.push('请输入有效的URL格式');
      }
    }

    if (values.timeout && (values.timeout < 1000 || values.timeout > 300000)) {
      errors.push('请求超时时间应在1秒到5分钟之间');
    }

    onValidation(errors.length === 0, errors);
    onChange(updatedStep);
  };

  // 添加头部
  const addHeader = () => {
    setHeaders([...headers, { key: '', value: '' }]);
  };

  // 删除头部
  const removeHeader = (index: number) => {
    const newHeaders = headers.filter((_, i) => i !== index);
    setHeaders(newHeaders);
  };

  // 更新头部
  const updateHeader = (index: number, field: 'key' | 'value', value: string) => {
    const newHeaders = [...headers];
    newHeaders[index][field] = value;
    setHeaders(newHeaders);
    
    if (formApi) {
      handleConfigChange(formApi.getValues());
    }
  };

  // 添加查询参数
  const addQueryParam = () => {
    setQueryParams([...queryParams, { key: '', value: '' }]);
  };

  // 删除查询参数
  const removeQueryParam = (index: number) => {
    const newParams = queryParams.filter((_, i) => i !== index);
    setQueryParams(newParams);
  };

  // 更新查询参数
  const updateQueryParam = (index: number, field: 'key' | 'value', value: string) => {
    const newParams = [...queryParams];
    newParams[index][field] = value;
    setQueryParams(newParams);
    
    if (formApi) {
      handleConfigChange(formApi.getValues());
    }
  };

  // 预设头部模板
  const commonHeaders = [
    { label: 'Content-Type: application/json', key: 'Content-Type', value: 'application/json' },
    { label: 'Content-Type: application/x-www-form-urlencoded', key: 'Content-Type', value: 'application/x-www-form-urlencoded' },
    { label: 'Authorization: Bearer Token', key: 'Authorization', value: 'Bearer ${token}' },
    { label: 'User-Agent', key: 'User-Agent', value: 'WorkflowEngine/1.0' },
    { label: 'Accept: application/json', key: 'Accept', value: 'application/json' }
  ];

  return (
    <div className="http-request-step-form">
      {/* 基础配置 */}
      <BaseStepForm
        step={step}
        onChange={onChange}
        onValidation={onValidation}
        availableVariables={availableVariables}
      />

      {/* HTTP特定配置 */}
      <div style={{ marginTop: '24px', borderTop: '1px solid #d9d9d9', paddingTop: '16px' }}>
        <h4 style={{ margin: '0 0 16px 0', fontSize: '14px', fontWeight: 600 }}>
          HTTP请求配置
        </h4>

        <Form
          getFormApi={setFormApi}
          initValues={{
            method: step.config.method || 'GET',
            url: step.config.url || '',
            body: step.config.body || '',
            timeout: step.config.timeout || 30000,
            followRedirects: step.config.followRedirects !== false,
            validateSSL: step.config.validateSSL !== false,
            retryOnFailure: step.config.retryOnFailure || false,
            expectedStatusCodes: step.config.expectedStatusCodes || [200]
          }}
          onValueChange={handleConfigChange}
          labelPosition="top"
        >
          {/* 基本请求配置 */}
          <Form.Select
            field="method"
            label="请求方法"
            style={{ width: '100%' }}
          >
            <Option value="GET">GET</Option>
            <Option value="POST">POST</Option>
            <Option value="PUT">PUT</Option>
            <Option value="PATCH">PATCH</Option>
            <Option value="DELETE">DELETE</Option>
            <Option value="HEAD">HEAD</Option>
            <Option value="OPTIONS">OPTIONS</Option>
          </Form.Select>

          <Form.Input
            field="url"
            label="请求URL"
            placeholder="https://api.example.com/endpoint"
            rules={[{ required: true, message: '请输入请求URL' }]}
          />

          {/* 选项卡配置 */}
          <Tabs type="card" style={{ marginTop: '16px' }}>
            {/* 请求体 */}
            <TabPane tab="请求体" itemKey="body">
              <div style={{ padding: '16px 0' }}>
                <Form.TextArea
                  field="body"
                  label="请求体内容"
                  placeholder="JSON、表单数据或原始文本"
                  autosize={{ minRows: 4, maxRows: 10 }}
                />
                
                <div style={{ marginTop: '12px' }}>
                  <label style={{ display: 'block', marginBottom: '8px', fontSize: '12px', color: '#666' }}>
                    <IconInfo style={{ marginRight: '4px' }} />
                    支持变量替换，使用 ${'{variable_name}'} 格式
                  </label>
                </div>
              </div>
            </TabPane>

            {/* 请求头 */}
            <TabPane tab="请求头" itemKey="headers">
              <div style={{ padding: '16px 0' }}>
                <div style={{ marginBottom: '16px' }}>
                  <Button 
                    icon={<IconPlus />} 
                    onClick={addHeader}
                    size="small"
                  >
                    添加请求头
                  </Button>
                  
                  <Select
                    placeholder="选择常用请求头"
                    style={{ marginLeft: '12px', width: '300px' }}
                    onSelect={(value) => {
                      const header = commonHeaders.find(h => `${h.key}:${h.value}` === value);
                      if (header) {
                        setHeaders([...headers, header]);
                      }
                    }}
                    size="small"
                  >
                    {commonHeaders.map((header, index) => (
                      <Option key={index} value={`${header.key}:${header.value}`}>
                        {header.label}
                      </Option>
                    ))}
                  </Select>
                </div>

                {headers.map((header, index) => (
                  <div key={index} style={{ display: 'flex', gap: '8px', marginBottom: '8px', alignItems: 'flex-start' }}>
                    <Input
                      placeholder="Header名称"
                      value={header.key}
                      onChange={(value) => updateHeader(index, 'key', value)}
                      style={{ flex: 1 }}
                      size="small"
                    />
                    <Input
                      placeholder="Header值"
                      value={header.value}
                      onChange={(value) => updateHeader(index, 'value', value)}
                      style={{ flex: 2 }}
                      size="small"
                    />
                    <Button
                      icon={<IconDelete />}
                      onClick={() => removeHeader(index)}
                      type="danger"
                      theme="borderless"
                      size="small"
                    />
                  </div>
                ))}
              </div>
            </TabPane>

            {/* 查询参数 */}
            <TabPane tab="查询参数" itemKey="query">
              <div style={{ padding: '16px 0' }}>
                <div style={{ marginBottom: '16px' }}>
                  <Button 
                    icon={<IconPlus />} 
                    onClick={addQueryParam}
                    size="small"
                  >
                    添加查询参数
                  </Button>
                </div>

                {queryParams.map((param, index) => (
                  <div key={index} style={{ display: 'flex', gap: '8px', marginBottom: '8px', alignItems: 'flex-start' }}>
                    <Input
                      placeholder="参数名"
                      value={param.key}
                      onChange={(value) => updateQueryParam(index, 'key', value)}
                      style={{ flex: 1 }}
                      size="small"
                    />
                    <Input
                      placeholder="参数值"
                      value={param.value}
                      onChange={(value) => updateQueryParam(index, 'value', value)}
                      style={{ flex: 1 }}
                      size="small"
                    />
                    <Button
                      icon={<IconDelete />}
                      onClick={() => removeQueryParam(index)}
                      type="danger"
                      theme="borderless"
                      size="small"
                    />
                  </div>
                ))}
              </div>
            </TabPane>

            {/* 高级选项 */}
            <TabPane tab="高级选项" itemKey="advanced">
              <div style={{ padding: '16px 0' }}>
                <Form.InputNumber
                  field="timeout"
                  label="请求超时时间（毫秒）"
                  min={1000}
                  max={300000}
                  step={1000}
                />

                <Form.Switch
                  field="followRedirects"
                  label="跟随重定向"
                />

                <Form.Switch
                  field="validateSSL"
                  label="验证SSL证书"
                />

                <Form.Switch
                  field="retryOnFailure"
                  label="失败时重试"
                />

                <Form.TagInput
                  field="expectedStatusCodes"
                  label="期望的状态码"
                  placeholder="输入状态码，如200, 201, 204"
                  separator=","
                />
              </div>
            </TabPane>
          </Tabs>
        </Form>

        {/* 响应处理提示 */}
        <div style={{
          marginTop: '16px',
          padding: '12px',
          backgroundColor: '#f0f9ff',
          border: '1px solid #91d5ff',
          borderRadius: '6px'
        }}>
          <div style={{ color: '#1890ff', fontSize: '12px', fontWeight: 500, marginBottom: '8px' }}>
            <IconInfo style={{ marginRight: '4px' }} />
            响应处理说明:
          </div>
          <div style={{ color: '#1890ff', fontSize: '11px', lineHeight: '1.5' }}>
            • 响应内容将存储在 <code>response.data</code> 变量中<br/>
            • HTTP状态码存储在 <code>response.status</code> 中<br/>
            • 响应头存储在 <code>response.headers</code> 中<br/>
            • 请求耗时存储在 <code>response.duration</code> 中（毫秒）
          </div>
        </div>
      </div>
    </div>
  );
};

export default HttpRequestStepForm;