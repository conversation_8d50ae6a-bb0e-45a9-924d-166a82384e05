import React from 'react';
import { Empty } from '@douyinfe/semi-ui';
import { IconAlertTriangle } from '@douyinfe/semi-icons';
import BaseStepForm from './BaseStepForm';
import HttpRequestStepForm from './HttpRequestStepForm';
import DatabaseStepForm from './DatabaseStepForm';
import NotificationStepForm from './NotificationStepForm';
import type { WorkflowStep, StepFormProps, WorkflowStepType } from '../../../types/workflow';

/**
 * 步骤表单工厂组件
 * 根据工作流步骤类型返回对应的配置表单
 */
const StepFormFactory: React.FC<StepFormProps> = ({
  step,
  onChange,
  onValidation,
  availableVariables = []
}) => {
  // 根据步骤类型返回对应的表单组件
  const renderStepForm = (stepType: WorkflowStepType) => {
    const formProps = {
      step,
      onChange,
      onValidation,
      availableVariables
    };

    switch (stepType) {
      case 'HTTP_REQUEST':
        return <HttpRequestStepForm {...formProps} />;
        
      case 'DATABASE_OPERATION':
        return <DatabaseStepForm {...formProps} />;
        
      case 'NOTIFICATION':
        return <NotificationStepForm {...formProps} />;
        
      case 'CONDITION':
        return <ConditionStepForm {...formProps} />;
        
      case 'LOOP':
        return <LoopStepForm {...formProps} />;
        
      case 'DELAY':
        return <DelayStepForm {...formProps} />;
        
      case 'SCRIPT':
        return <ScriptStepForm {...formProps} />;
        
      case 'APPROVAL':
        return <ApprovalStepForm {...formProps} />;
        
      case 'FILE_OPERATION':
        return <FileOperationStepForm {...formProps} />;
        
      case 'ACTION':
      case 'PARALLEL':
      case 'SUBPROCESS':
      default:
        // 对于暂未实现的步骤类型，使用基础表单
        return <BaseStepForm {...formProps} />;
    }
  };

  return (
    <div className="step-form-factory">
      {renderStepForm(step.type)}
    </div>
  );
};

// 条件步骤表单组件（简化版）
const ConditionStepForm: React.FC<StepFormProps> = (props) => {
  return (
    <div>
      <BaseStepForm {...props} />
      <div style={{ marginTop: '24px', borderTop: '1px solid #d9d9d9', paddingTop: '16px' }}>
        <h4 style={{ margin: '0 0 16px 0', fontSize: '14px', fontWeight: 600 }}>
          条件判断配置
        </h4>
        <div style={{ 
          padding: '16px', 
          backgroundColor: '#f0f9ff',
          border: '1px solid #91d5ff',
          borderRadius: '6px',
          textAlign: 'center'
        }}>
          <IconAlertTriangle style={{ fontSize: '24px', color: '#1890ff', marginBottom: '8px' }} />
          <div style={{ color: '#1890ff', fontSize: '14px' }}>
            条件步骤配置表单开发中...
          </div>
          <div style={{ color: '#666', fontSize: '12px', marginTop: '4px' }}>
            请使用基础配置中的"条件执行"功能
          </div>
        </div>
      </div>
    </div>
  );
};

// 循环步骤表单组件（简化版）
const LoopStepForm: React.FC<StepFormProps> = (props) => {
  return (
    <div>
      <BaseStepForm {...props} />
      <div style={{ marginTop: '24px', borderTop: '1px solid #d9d9d9', paddingTop: '16px' }}>
        <h4 style={{ margin: '0 0 16px 0', fontSize: '14px', fontWeight: 600 }}>
          循环执行配置
        </h4>
        <div style={{ 
          padding: '16px', 
          backgroundColor: '#fff7e6',
          border: '1px solid #ffd591',
          borderRadius: '6px',
          textAlign: 'center'
        }}>
          <IconAlertTriangle style={{ fontSize: '24px', color: '#fa8c16', marginBottom: '8px' }} />
          <div style={{ color: '#fa8c16', fontSize: '14px' }}>
            循环步骤配置表单开发中...
          </div>
        </div>
      </div>
    </div>
  );
};

// 延时步骤表单组件（简化版）
const DelayStepForm: React.FC<StepFormProps> = (props) => {
  return (
    <div>
      <BaseStepForm {...props} />
      <div style={{ marginTop: '24px', borderTop: '1px solid #d9d9d9', paddingTop: '16px' }}>
        <h4 style={{ margin: '0 0 16px 0', fontSize: '14px', fontWeight: 600 }}>
          延时等待配置
        </h4>
        <div style={{ 
          padding: '16px', 
          backgroundColor: '#f0f9ff',
          border: '1px solid #91d5ff',
          borderRadius: '6px',
          textAlign: 'center'
        }}>
          <IconAlertTriangle style={{ fontSize: '24px', color: '#13c2c2', marginBottom: '8px' }} />
          <div style={{ color: '#13c2c2', fontSize: '14px' }}>
            延时步骤配置表单开发中...
          </div>
        </div>
      </div>
    </div>
  );
};

// 脚本步骤表单组件（简化版）
const ScriptStepForm: React.FC<StepFormProps> = (props) => {
  return (
    <div>
      <BaseStepForm {...props} />
      <div style={{ marginTop: '24px', borderTop: '1px solid #d9d9d9', paddingTop: '16px' }}>
        <h4 style={{ margin: '0 0 16px 0', fontSize: '14px', fontWeight: 600 }}>
          脚本执行配置
        </h4>
        <div style={{ 
          padding: '16px', 
          backgroundColor: '#f6ffed',
          border: '1px solid #b7eb8f',
          borderRadius: '6px',
          textAlign: 'center'
        }}>
          <IconAlertTriangle style={{ fontSize: '24px', color: '#52c41a', marginBottom: '8px' }} />
          <div style={{ color: '#52c41a', fontSize: '14px' }}>
            脚本步骤配置表单开发中...
          </div>
        </div>
      </div>
    </div>
  );
};

// 审批步骤表单组件（简化版）
const ApprovalStepForm: React.FC<StepFormProps> = (props) => {
  return (
    <div>
      <BaseStepForm {...props} />
      <div style={{ marginTop: '24px', borderTop: '1px solid #d9d9d9', paddingTop: '16px' }}>
        <h4 style={{ margin: '0 0 16px 0', fontSize: '14px', fontWeight: 600 }}>
          人工审批配置
        </h4>
        <div style={{ 
          padding: '16px', 
          backgroundColor: '#f0f9ff',
          border: '1px solid #91d5ff',
          borderRadius: '6px',
          textAlign: 'center'
        }}>
          <IconAlertTriangle style={{ fontSize: '24px', color: '#13c2c2', marginBottom: '8px' }} />
          <div style={{ color: '#13c2c2', fontSize: '14px' }}>
            审批步骤配置表单开发中...
          </div>
        </div>
      </div>
    </div>
  );
};

// 文件操作步骤表单组件（简化版）
const FileOperationStepForm: React.FC<StepFormProps> = (props) => {
  return (
    <div>
      <BaseStepForm {...props} />
      <div style={{ marginTop: '24px', borderTop: '1px solid #d9d9d9', paddingTop: '16px' }}>
        <h4 style={{ margin: '0 0 16px 0', fontSize: '14px', fontWeight: 600 }}>
          文件操作配置
        </h4>
        <div style={{ 
          padding: '16px', 
          backgroundColor: '#fff7e6',
          border: '1px solid #ffd591',
          borderRadius: '6px',
          textAlign: 'center'
        }}>
          <IconAlertTriangle style={{ fontSize: '24px', color: '#fa8c16', marginBottom: '8px' }} />
          <div style={{ color: '#fa8c16', fontSize: '14px' }}>
            文件操作步骤配置表单开发中...
          </div>
        </div>
      </div>
    </div>
  );
};

export default StepFormFactory;