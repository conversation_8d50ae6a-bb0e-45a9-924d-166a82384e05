/**
 * 工作流预览面板组件
 * 提供可视化工作流结构和执行路径展示功能
 */
import React, { useState, useEffect, useMemo } from 'react';
import { Card, Button, Space, Tabs, TabPane, Tag, Progress, Tree, Collapse, Badge, Empty } from '@douyinfe/semi-ui';
import { 
  IconPlay, 
  IconPause, 
  IconStop, 
  IconRefresh, 
  IconChevronRight, 
  IconChevronDown,
  IconCheck,
  IconAlertTriangle,
  IconClock,
  IconEyeOpened
} from '@douyinfe/semi-icons';
import type { 
  WorkflowDefinition, 
  DesignerNode, 
  DesignerConnection, 
  WorkflowExecution,
  WorkflowExecutionStep 
} from '../../types/workflow';

const { Panel } = Collapse;

export interface WorkflowPreviewPanelProps {
  workflow?: WorkflowDefinition;
  nodes: DesignerNode[];
  connections: DesignerConnection[];
  visible: boolean;
  onClose: () => void;
  onExecute: () => Promise<WorkflowExecution>;
  onStop?: () => void;
  execution?: WorkflowExecution | null;
}

interface ExecutionPathNode {
  id: string;
  name: string;
  type: string;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'skipped';
  startTime?: string;
  endTime?: string;
  duration?: number;
  output?: any;
  error?: string;
  children?: ExecutionPathNode[];
}

const WorkflowPreviewPanel: React.FC<WorkflowPreviewPanelProps> = ({
  workflow,
  nodes,
  connections,
  visible,
  onClose,
  onExecute,
  onStop,
  execution
}) => {
  const [activeTab, setActiveTab] = useState('structure');
  const [isExecuting, setIsExecuting] = useState(false);
  const [executionSteps, setExecutionSteps] = useState<WorkflowExecutionStep[]>([]);
  const [expandedKeys, setExpandedKeys] = useState<string[]>(['root']);

  // 构建工作流结构树
  const workflowStructure = useMemo(() => {
    if (!nodes.length) return [];

    const buildTree = (): ExecutionPathNode[] => {
      // 找到起始节点
      const startNodes = nodes.filter(node => 
        !connections.some(conn => conn.targetNodeId === node.id)
      );

      const buildNodeTree = (node: DesignerNode): ExecutionPathNode => {
        const children: ExecutionPathNode[] = [];
        
        // 找到该节点的所有出边
        const outgoingConnections = connections.filter(conn => conn.sourceNodeId === node.id);
        
        outgoingConnections.forEach(conn => {
          const targetNode = nodes.find(n => n.id === conn.targetNodeId);
          if (targetNode) {
            children.push(buildNodeTree(targetNode));
          }
        });

        return {
          id: node.id,
          name: node.step.name || `${node.step.type} 步骤`,
          type: node.step.type,
          status: 'pending',
          children: children.length > 0 ? children : undefined
        };
      };

      return startNodes.map(buildNodeTree);
    };

    return buildTree();
  }, [nodes, connections]);

  // 更新执行状态
  useEffect(() => {
    if (execution?.steps) {
      setExecutionSteps(execution.steps);
    }
  }, [execution]);

  // 处理工作流执行
  const handleExecute = async () => {
    setIsExecuting(true);
    try {
      const result = await onExecute();
      console.log('Workflow execution started:', result);
    } catch (error) {
      console.error('Failed to execute workflow:', error);
    } finally {
      setIsExecuting(false);
    }
  };

  // 处理停止执行
  const handleStop = () => {
    if (onStop) {
      onStop();
    }
    setIsExecuting(false);
  };

  // 获取节点执行状态
  const getNodeStatus = (nodeId: string): ExecutionPathNode['status'] => {
    const step = executionSteps.find(s => s.stepId === nodeId);
    if (!step) return 'pending';
    
    switch (step.status) {
      case 'RUNNING': return 'running';
      case 'COMPLETED': return 'completed';
      case 'FAILED': return 'failed';
      case 'SKIPPED': return 'skipped';
      default: return 'pending';
    }
  };

  // 获取状态图标
  const getStatusIcon = (status: ExecutionPathNode['status']) => {
    switch (status) {
      case 'running':
        return <IconClock style={{ color: '#1890ff' }} />;
      case 'completed':
        return <IconCheck style={{ color: '#52c41a' }} />;
      case 'failed':
        return <IconAlertTriangle style={{ color: '#ff4d4f' }} />;
      case 'skipped':
        return <Tag color="grey">跳过</Tag>;
      default:
        return <div style={{ width: '14px', height: '14px', backgroundColor: '#d9d9d9', borderRadius: '50%' }} />;
    }
  };

  // 获取状态颜色
  const getStatusColor = (status: ExecutionPathNode['status']) => {
    switch (status) {
      case 'running': return '#1890ff';
      case 'completed': return '#52c41a';
      case 'failed': return '#ff4d4f';
      case 'skipped': return '#d9d9d9';
      default: return '#d9d9d9';
    }
  };

  // 计算执行进度
  const executionProgress = useMemo(() => {
    if (!executionSteps.length || !nodes.length) return 0;
    
    const completedSteps = executionSteps.filter(step => 
      step.status === 'COMPLETED' || step.status === 'FAILED' || step.status === 'SKIPPED'
    ).length;
    
    return Math.round((completedSteps / nodes.length) * 100);
  }, [executionSteps, nodes.length]);

  // 渲染工作流结构树
  const renderStructureTree = () => {
    if (!workflowStructure.length) {
      return (
        <Empty
          title="无工作流结构"
          description="请先在画布上添加节点并建立连接"
          image={<IconEyeOpened style={{ fontSize: '48px', color: '#ccc' }} />}
        />
      );
    }

    const renderTreeNode = (node: ExecutionPathNode, level = 0): React.ReactNode => {
      const status = getNodeStatus(node.id);
      const step = executionSteps.find(s => s.stepId === node.id);
      
      return (
        <div 
          key={node.id} 
          style={{ 
            marginLeft: level * 20,
            marginBottom: '8px',
            padding: '8px 12px',
            border: '1px solid #f0f0f0',
            borderRadius: '6px',
            backgroundColor: level === 0 ? '#fafafa' : 'white'
          }}
        >
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
              {getStatusIcon(status)}
              <div>
                <div style={{ fontWeight: 500, fontSize: '14px' }}>
                  {node.name}
                </div>
                <div style={{ fontSize: '12px', color: '#666' }}>
                  {node.type}
                  {step?.startTime && (
                    <span style={{ marginLeft: '8px' }}>
                      开始: {new Date(step.startTime).toLocaleTimeString()}
                    </span>
                  )}
                  {step?.endTime && (
                    <span style={{ marginLeft: '8px' }}>
                      结束: {new Date(step.endTime).toLocaleTimeString()}
                    </span>
                  )}
                </div>
              </div>
            </div>
            
            {status === 'running' && (
              <Badge dot style={{ backgroundColor: '#1890ff' }} />
            )}
          </div>
          
          {step?.error && (
            <div style={{ 
              marginTop: '8px', 
              padding: '8px', 
              backgroundColor: '#fff2f0',
              border: '1px solid #ffccc7',
              borderRadius: '4px',
              fontSize: '12px',
              color: '#ff4d4f'
            }}>
              错误: {step.error}
            </div>
          )}
          
          {step?.output && (
            <div style={{ 
              marginTop: '8px', 
              padding: '8px', 
              backgroundColor: '#f6ffed',
              border: '1px solid #b7eb8f',
              borderRadius: '4px',
              fontSize: '12px',
              color: '#52c41a'
            }}>
              输出: {typeof step.output === 'object' ? JSON.stringify(step.output, null, 2) : step.output}
            </div>
          )}
          
          {node.children && node.children.map(child => renderTreeNode(child, level + 1))}
        </div>
      );
    };

    return (
      <div style={{ maxHeight: '400px', overflowY: 'auto' }}>
        {workflowStructure.map(node => renderTreeNode(node))}
      </div>
    );
  };

  // 渲染执行状态
  const renderExecutionStatus = () => {
    if (!execution) {
      return (
        <Empty
          title="未开始执行"
          description="点击执行按钮开始测试工作流"
          image={<IconPlay style={{ fontSize: '48px', color: '#ccc' }} />}
        />
      );
    }

    return (
      <div>
        {/* 执行概览 */}
        <Card 
          title="执行概览" 
          style={{ marginBottom: '16px' }}
          bodyStyle={{ padding: '16px' }}
        >
          <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '12px' }}>
            <span>执行状态:</span>
            <Tag color={execution.status === 'COMPLETED' ? 'green' : 
                      execution.status === 'FAILED' ? 'red' : 
                      execution.status === 'RUNNING' ? 'blue' : 'grey'}>
              {execution.status}
            </Tag>
          </div>
          
          <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '12px' }}>
            <span>会话ID:</span>
            <code style={{ fontSize: '12px' }}>{execution.sessionId}</code>
          </div>
          
          <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '12px' }}>
            <span>开始时间:</span>
            <span>{new Date(execution.startedAt).toLocaleString()}</span>
          </div>
          
          {execution.completedAt && (
            <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '12px' }}>
              <span>完成时间:</span>
              <span>{new Date(execution.completedAt).toLocaleString()}</span>
            </div>
          )}
          
          <div style={{ marginTop: '16px' }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '8px' }}>
              <span>执行进度:</span>
              <span>{executionProgress}%</span>
            </div>
            <Progress 
              percent={executionProgress} 
              stroke={execution.status === 'FAILED' ? '#ff4d4f' : '#52c41a'}
            />
          </div>
        </Card>

        {/* 步骤详情 */}
        <Card title="步骤详情" bodyStyle={{ padding: 0 }}>
          <Collapse defaultActiveKey={['current']} ghost>
            {executionSteps.map((step, index) => {
              const node = nodes.find(n => n.id === step.stepId);
              return (
                <Panel
                  key={step.stepId}
                  header={
                    <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                      {getStatusIcon(step.status.toLowerCase() as ExecutionPathNode['status'])}
                      <span>{node?.step.name || `步骤 ${index + 1}`}</span>
                      <Tag size="small" color="grey">{node?.step.type}</Tag>
                    </div>
                  }
                  itemKey={step.status === 'RUNNING' ? 'current' : step.stepId}
                >
                  <div style={{ padding: '0 16px 16px' }}>
                    <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '8px' }}>
                      <span>状态:</span>
                      <Tag color={getStatusColor(step.status.toLowerCase() as ExecutionPathNode['status'])}>
                        {step.status}
                      </Tag>
                    </div>
                    
                    {step.startTime && (
                      <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '8px' }}>
                        <span>开始时间:</span>
                        <span>{new Date(step.startTime).toLocaleString()}</span>
                      </div>
                    )}
                    
                    {step.endTime && (
                      <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '8px' }}>
                        <span>结束时间:</span>
                        <span>{new Date(step.endTime).toLocaleString()}</span>
                      </div>
                    )}
                    
                    {step.duration && (
                      <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '8px' }}>
                        <span>执行时长:</span>
                        <span>{step.duration}ms</span>
                      </div>
                    )}
                    
                    {step.input && (
                      <div style={{ marginBottom: '8px' }}>
                        <div style={{ marginBottom: '4px', fontWeight: 500 }}>输入数据:</div>
                        <pre style={{ 
                          fontSize: '12px', 
                          backgroundColor: '#f0f9ff', 
                          padding: '8px', 
                          borderRadius: '4px',
                          border: '1px solid #91d5ff',
                          whiteSpace: 'pre-wrap',
                          wordBreak: 'break-all'
                        }}>
                          {typeof step.input === 'object' ? JSON.stringify(step.input, null, 2) : step.input}
                        </pre>
                      </div>
                    )}
                    
                    {step.output && (
                      <div style={{ marginBottom: '8px' }}>
                        <div style={{ marginBottom: '4px', fontWeight: 500 }}>输出数据:</div>
                        <pre style={{ 
                          fontSize: '12px', 
                          backgroundColor: '#f6ffed', 
                          padding: '8px', 
                          borderRadius: '4px',
                          border: '1px solid #b7eb8f',
                          whiteSpace: 'pre-wrap',
                          wordBreak: 'break-all'
                        }}>
                          {typeof step.output === 'object' ? JSON.stringify(step.output, null, 2) : step.output}
                        </pre>
                      </div>
                    )}
                    
                    {step.error && (
                      <div style={{ marginBottom: '8px' }}>
                        <div style={{ marginBottom: '4px', fontWeight: 500, color: '#ff4d4f' }}>错误信息:</div>
                        <pre style={{ 
                          fontSize: '12px', 
                          backgroundColor: '#fff2f0', 
                          padding: '8px', 
                          borderRadius: '4px',
                          border: '1px solid #ffccc7',
                          color: '#ff4d4f',
                          whiteSpace: 'pre-wrap',
                          wordBreak: 'break-all'
                        }}>
                          {step.error}
                        </pre>
                      </div>
                    )}
                  </div>
                </Panel>
              );
            })}
          </Collapse>
        </Card>
      </div>
    );
  };

  // 渲染性能指标
  const renderPerformanceMetrics = () => {
    if (!execution || !executionSteps.length) {
      return (
        <Empty
          title="无性能数据"
          description="执行工作流后查看性能指标"
        />
      );
    }

    const totalDuration = execution.completedAt && execution.startedAt 
      ? new Date(execution.completedAt).getTime() - new Date(execution.startedAt).getTime()
      : null;

    const stepMetrics = executionSteps
      .filter(step => step.duration)
      .sort((a, b) => (b.duration || 0) - (a.duration || 0));

    return (
      <div>
        {/* 总体性能 */}
        <Card title="总体性能" style={{ marginBottom: '16px' }} bodyStyle={{ padding: '16px' }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '12px' }}>
            <span>总执行时间:</span>
            <span>{totalDuration ? `${totalDuration}ms` : '-'}</span>
          </div>
          
          <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '12px' }}>
            <span>步骤数量:</span>
            <span>{executionSteps.length}</span>
          </div>
          
          <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '12px' }}>
            <span>成功步骤:</span>
            <span>{executionSteps.filter(s => s.status === 'COMPLETED').length}</span>
          </div>
          
          <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '12px' }}>
            <span>失败步骤:</span>
            <span>{executionSteps.filter(s => s.status === 'FAILED').length}</span>
          </div>
        </Card>

        {/* 步骤性能排行 */}
        <Card title="步骤性能排行" bodyStyle={{ padding: '16px' }}>
          {stepMetrics.map((step, index) => {
            const node = nodes.find(n => n.id === step.stepId);
            return (
              <div 
                key={step.stepId}
                style={{ 
                  display: 'flex', 
                  justifyContent: 'space-between', 
                  alignItems: 'center',
                  padding: '8px 0',
                  borderBottom: index < stepMetrics.length - 1 ? '1px solid #f0f0f0' : 'none'
                }}
              >
                <div>
                  <div style={{ fontWeight: 500 }}>
                    {node?.step.name || `步骤 ${index + 1}`}
                  </div>
                  <div style={{ fontSize: '12px', color: '#666' }}>
                    {node?.step.type}
                  </div>
                </div>
                <div style={{ textAlign: 'right' }}>
                  <div style={{ fontWeight: 500 }}>
                    {step.duration}ms
                  </div>
                  <div style={{ fontSize: '12px', color: '#666' }}>
                    排名 #{index + 1}
                  </div>
                </div>
              </div>
            );
          })}
        </Card>
      </div>
    );
  };

  if (!visible) return null;

  return (
    <div style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* 头部控制栏 */}
      <div style={{ 
        padding: '16px',
        borderBottom: '1px solid #d9d9d9',
        backgroundColor: 'white'
      }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <div>
            <h3 style={{ margin: 0, fontSize: '16px' }}>
              工作流预览 - {workflow?.name || '未命名工作流'}
            </h3>
            <div style={{ fontSize: '12px', color: '#666', marginTop: '4px' }}>
              节点: {nodes.length} | 连接: {connections.length}
            </div>
          </div>
          
          <Space>
            <Button 
              icon={<IconRefresh />}
              onClick={() => {
                setExecutionSteps([]);
                setActiveTab('structure');
              }}
              size="small"
            >
              重置
            </Button>
            
            {isExecuting ? (
              <Button 
                icon={<IconStop />}
                onClick={handleStop}
                type="danger"
                size="small"
              >
                停止
              </Button>
            ) : (
              <Button 
                icon={<IconPlay />}
                onClick={handleExecute}
                type="primary"
                disabled={!nodes.length}
                size="small"
              >
                执行测试
              </Button>
            )}
          </Space>
        </div>
      </div>

      {/* 标签页内容 */}
      <div style={{ flex: 1, overflow: 'hidden' }}>
        <Tabs 
          activeKey={activeTab} 
          onChange={setActiveTab}
          type="card"
          style={{ height: '100%' }}
          contentStyle={{ height: 'calc(100% - 44px)', overflow: 'auto', padding: '16px' }}
        >
          <TabPane tab="工作流结构" itemKey="structure">
            {renderStructureTree()}
          </TabPane>
          
          <TabPane tab="执行状态" itemKey="execution">
            {renderExecutionStatus()}
          </TabPane>
          
          <TabPane tab="性能指标" itemKey="performance">
            {renderPerformanceMetrics()}
          </TabPane>
        </Tabs>
      </div>
    </div>
  );
};

export default WorkflowPreviewPanel;