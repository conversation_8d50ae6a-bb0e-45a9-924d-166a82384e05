import React from "react";
import {
  Card,
  Typography,
  Progress,
  Tag,
  List,
  Empty,
  Spin,
  Divider,
} from "@douyinfe/semi-ui";
import {
  IconArrowUp,
  IconArrowDown,
  IconAlertTriangle,
  IconTickCircle,
} from "@douyinfe/semi-icons";
import {
  IllustrationConstruction,
  IllustrationConstructionDark,
} from "@douyinfe/semi-illustrations";

const { Title, Text } = Typography;

interface IntelligentAnalysisData {
  score?: number;
  level?: 'excellent' | 'good' | 'warning' | 'critical';
  insights?: Array<{
    category: string;
    title: string;
    description: string;
    severity: 'info' | 'warning' | 'critical';
    confidence: number;
    impact: 'low' | 'medium' | 'high';
  }>;
  recommendations?: Array<{
    id: string;
    category: string;
    title: string;
    description: string;
    priority: 'low' | 'medium' | 'high' | 'urgent';
    difficulty: 'easy' | 'medium' | 'hard';
    estimatedImpact: string;
  }>;
  trends?: Array<{
    metric: string;
    trend: 'upward' | 'downward' | 'stable' | 'cyclical';
    changeRate: number;
  }>;
}

interface IntelligentAnalysisCardProps {
  data?: IntelligentAnalysisData;
  isLoading?: boolean;
  error?: any;
}

const getScoreColor = (score: number) => {
  if (score >= 90) return "green";
  if (score >= 70) return "blue";
  if (score >= 50) return "orange";
  return "red";
};

const getScoreText = (score: number) => {
  if (score >= 90) return "优秀";
  if (score >= 70) return "良好";
  if (score >= 50) return "警告";
  return "严重";
};

const getLevelIcon = (level?: string) => {
  switch (level) {
    case 'excellent':
    case 'good':
      return <IconTickCircle style={{ color: "var(--semi-color-success)" }} />;
    case 'warning':
      return <IconAlertTriangle style={{ color: "var(--semi-color-warning)" }} />;
    case 'critical':
      return <IconAlertTriangle style={{ color: "var(--semi-color-danger)" }} />;
    default:
      return <IconTickCircle style={{ color: "var(--semi-color-success)" }} />;
  }
};

const getTrendIcon = (trend?: string) => {
  switch (trend) {
    case 'upward':
      return <IconArrowUp style={{ color: "var(--semi-color-success)" }} />;
    case 'downward':
      return <IconArrowDown style={{ color: "var(--semi-color-danger)" }} />;
    default:
      return <IconArrowUp style={{ color: "var(--semi-color-info)" }} />;
  }
};

export const IntelligentAnalysisCard: React.FC<IntelligentAnalysisCardProps> = ({
  data,
  isLoading,
  error,
}) => {
  if (isLoading) {
    return (
      <Card title="智能分析" style={{ minHeight: 300 }}>
        <Spin spinning={true}>
          <div style={{ height: 200 }} />
        </Spin>
      </Card>
    );
  }

  if (error) {
    return (
      <Card title="智能分析" style={{ minHeight: 300 }}>
        <Empty
          image={<IllustrationConstruction style={{ width: 150, height: 150 }} />}
          darkModeImage={
            <IllustrationConstructionDark style={{ width: 150, height: 150 }} />
          }
          title="智能分析暂不可用"
          description="系统正在收集数据，请稍后查看"
        />
      </Card>
    );
  }

  if (!data || typeof data.score === 'undefined') {
    return (
      <Card title="智能分析" style={{ minHeight: 300 }}>
        <Empty
          image={<IllustrationConstruction style={{ width: 150, height: 150 }} />}
          darkModeImage={
            <IllustrationConstructionDark style={{ width: 150, height: 150 }} />
          }
          title="暂无分析数据"
          description="系统正在收集数据，请稍后查看"
        />
      </Card>
    );
  }

  return (
    <Card title="系统健康评分" style={{ minHeight: 300 }}>
      <div className="space-y-4">
        <div className="text-center">
          <div className="flex items-center justify-center space-x-2 mb-2">
            {getLevelIcon(data.level)}
            <Title heading={2} className="m-0">
              {data.score}
            </Title>
          </div>
          <Text type="secondary">综合健康评分</Text>
          <div className="mt-2">
            <Tag color={getScoreColor(data.score)}>
              {getScoreText(data.score)}
            </Tag>
          </div>
        </div>

        <Divider />

        {data.insights && data.insights.length > 0 && (
          <div>
            <Text strong>关键洞察</Text>
            <div className="mt-3 space-y-2">
              {data.insights.slice(0, 3).map((insight, index) => (
                <div key={index} className="p-3 border rounded bg-gray-50">
                  <div className="flex items-center justify-between mb-1">
                    <Text strong size="small">{insight.title}</Text>
                    <Tag 
                      color={
                        insight.severity === 'critical' ? 'red' :
                        insight.severity === 'warning' ? 'orange' : 'blue'
                      }
                      size="small"
                    >
                      {insight.category}
                    </Tag>
                  </div>
                  <Text size="small" type="secondary">
                    {insight.description}
                  </Text>
                  <div className="mt-1">
                    <Text size="small" type="tertiary">
                      置信度: {Math.round(insight.confidence * 100)}%
                    </Text>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {data.trends && data.trends.length > 0 && (
          <div>
            <Text strong>趋势分析</Text>
            <div className="mt-3 space-y-2">
              {data.trends.slice(0, 3).map((trend, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    {getTrendIcon(trend.trend)}
                    <Text size="small">{trend.metric}</Text>
                  </div>
                  <div className="text-right">
                    <Text size="small" strong>
                      {trend.changeRate > 0 ? '+' : ''}{trend.changeRate.toFixed(1)}%
                    </Text>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </Card>
  );
};

export const IntelligentRecommendationsCard: React.FC<IntelligentAnalysisCardProps> = ({
  data,
  isLoading,
  error,
}) => {
  if (isLoading) {
    return (
      <Card title="分析建议" style={{ minHeight: 300 }}>
        <Spin spinning={true}>
          <div style={{ height: 200 }} />
        </Spin>
      </Card>
    );
  }

  if (error || !data?.recommendations || data.recommendations.length === 0) {
    return (
      <Card title="分析建议" style={{ minHeight: 300 }}>
        <Empty
          image={<IllustrationConstruction style={{ width: 150, height: 150 }} />}
          darkModeImage={
            <IllustrationConstructionDark style={{ width: 150, height: 150 }} />
          }
          title="暂无分析建议"
          description="系统运行良好，暂无优化建议"
        />
      </Card>
    );
  }

  return (
    <Card title="分析建议" style={{ minHeight: 300 }}>
      <List
        dataSource={data.recommendations.slice(0, 5)}
        renderItem={(item: any) => (
          <List.Item>
            <div className="w-full">
              <div className="flex items-center space-x-2 mb-2">
                <Tag color={
                  item.priority === 'urgent' ? 'red' :
                  item.priority === 'high' ? 'orange' :
                  item.priority === 'medium' ? 'blue' : 'grey'
                }>
                  {item.priority === 'urgent' ? '紧急' :
                   item.priority === 'high' ? '高优先级' :
                   item.priority === 'medium' ? '中优先级' : '低优先级'}
                </Tag>
                <Tag color="grey" size="small">
                  {item.difficulty === 'easy' ? '简单' :
                   item.difficulty === 'medium' ? '中等' : '困难'}
                </Tag>
              </div>
              <Text strong>{item.title}</Text>
              <div className="mt-1">
                <Text type="secondary" size="small">{item.description}</Text>
              </div>
              {item.estimatedImpact && (
                <div className="mt-1">
                  <Text size="small" type="tertiary">
                    预期效果: {item.estimatedImpact}
                  </Text>
                </div>
              )}
            </div>
          </List.Item>
        )}
      />
    </Card>
  );
};
