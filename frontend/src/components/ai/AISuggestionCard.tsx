/**
 * AI建议展示卡片组件
 */

import React from "react";
import { Card, Button, Tag, Space, Typography, Progress, Row, Col } from "@douyinfe/semi-ui";
import {
  IconBolt as IconRobot,
  IconTick as IconCheck,
  IconInfoCircle,
  IconRefresh,
  IconClose,
} from "@douyinfe/semi-icons";
import type { AIAnalysisResponse } from "@/types/ai";

const { Text } = Typography;

interface AISuggestionCardProps {
  analysis: AIAnalysisResponse | null;
  loading?: boolean;
  error?: string | null;
  onAdoptAll?: () => void;
  onAdoptSuggestion?: (field: string, value: string) => void;
  onRetryAnalysis?: () => void;
  onClose?: () => void;
  className?: string;
}

// 字段名映射
const FIELD_LABELS = {
  title: "工单标题",
  category: "服务类别",
  priority: "优先级",
  slaTemplate: "SLA模板",
};

// 服务类别映射
const CATEGORY_LABELS = {
  MAINTENANCE: "维护",
  SUPPORT: "支持",
  UPGRADE: "升级",
  BUGFIX: "Bug修复",
  CONSULTING: "咨询",
  MONITORING: "监控",
};

// 优先级映射
const PRIORITY_LABELS = {
  LOW: "低",
  MEDIUM: "中",
  HIGH: "高",
  URGENT: "紧急",
};

// 获取置信度颜色
const getConfidenceColor = (confidence: number) => {
  if (confidence >= 80) return "green" as const;
  if (confidence >= 60) return "blue" as const;
  if (confidence >= 40) return "orange" as const;
  return "red" as const;
};

// 获取置信度文本
const getConfidenceText = (confidence: number): string => {
  if (confidence >= 80) return "高置信度";
  if (confidence >= 60) return "中等置信度";
  if (confidence >= 40) return "低置信度";
  return "不确定";
};

// 格式化建议值显示
const formatSuggestionValue = (field: string, value: string): string => {
  switch (field) {
    case "category":
      return CATEGORY_LABELS[value as keyof typeof CATEGORY_LABELS] || value;
    case "priority":
      return PRIORITY_LABELS[value as keyof typeof PRIORITY_LABELS] || value;
    default:
      return value;
  }
};

export const AISuggestionCard: React.FC<AISuggestionCardProps> = ({
  analysis,
  loading = false,
  error = null,
  onAdoptAll,
  onAdoptSuggestion,
  onRetryAnalysis,
  onClose,
  className = "",
}) => {
  // 错误状态
  if (error) {
    return (
      <Card
        title={
          <div style={{ display: "flex", justifyContent: "space-between", alignItems: "center" }}>
            <Space>
              <IconRobot style={{ color: "var(--semi-color-danger)" }} />
              <Text strong>AI分析失败</Text>
            </Space>
            <Space>
              {onRetryAnalysis && (
                <Button
                  theme="borderless"
                  icon={<IconRefresh />}
                  size="small"
                  onClick={onRetryAnalysis}
                >
                  重试
                </Button>
              )}
              {onClose && (
                <Button theme="borderless" icon={<IconClose />} size="small" onClick={onClose} />
              )}
            </Space>
          </div>
        }
        className={className}
        style={{
          borderColor: "var(--semi-color-danger)",
          background: "var(--semi-color-danger-light-default)",
        }}
      >
        <div style={{ textAlign: "center", padding: "16px 0" }}>
          <Text type="danger">{error}</Text>
        </div>
      </Card>
    );
  }

  // 加载状态
  if (loading) {
    return (
      <Card
        title={
          <Space>
            <IconRobot style={{ color: "var(--semi-color-primary)" }} />
            <Text strong>AI正在分析...</Text>
          </Space>
        }
        className={className}
        style={{
          borderColor: "var(--semi-color-primary)",
          background: "var(--semi-color-primary-light-default)",
        }}
      >
        <div style={{ textAlign: "center", padding: "24px 0" }}>
          <Progress
            percent={100}
            showInfo={false}
            strokeWidth={6}
            type="circle"
            size="small"
            style={{ marginBottom: 12 }}
          />
          <div>
            <Text type="secondary">正在使用AI分析工单内容...</Text>
          </div>
        </div>
      </Card>
    );
  }

  // 无分析结果
  if (!analysis || !analysis.suggestions.length) {
    return (
      <Card
        title={
          <div style={{ display: "flex", justifyContent: "space-between", alignItems: "center" }}>
            <Space>
              <IconRobot style={{ color: "var(--semi-color-warning)" }} />
              <Text strong>暂无AI建议</Text>
            </Space>
            {onClose && (
              <Button theme="borderless" icon={<IconClose />} size="small" onClick={onClose} />
            )}
          </div>
        }
        className={className}
      >
        <div style={{ textAlign: "center", padding: "16px 0" }}>
          <Text type="tertiary">请输入工单描述以获取AI智能建议</Text>
        </div>
      </Card>
    );
  }

  // 有建议结果
  const { suggestions, overallConfidence, processingTime, warning, metadata } = analysis;

  return (
    <Card
      title={
        <div
          style={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            flexWrap: "wrap",
            gap: "8px",
          }}
        >
          <Space wrap>
            <IconRobot style={{ color: "var(--semi-color-success)" }} />
            <Text strong>AI智能建议</Text>
            <Tag color={getConfidenceColor(overallConfidence)} size="small">
              {getConfidenceText(overallConfidence)} ({overallConfidence}%)
            </Tag>
            {metadata?.aiProvider && (
              <Tag size="small" color="grey">
                {metadata.aiProvider}
              </Tag>
            )}
          </Space>
          <Space>
            {onAdoptAll && (
              <Button
                theme="solid"
                type="primary"
                size="small"
                icon={<IconCheck />}
                onClick={onAdoptAll}
              >
                采用全部
              </Button>
            )}
            {onRetryAnalysis && (
              <Button
                theme="borderless"
                icon={<IconRefresh />}
                size="small"
                onClick={onRetryAnalysis}
              >
                重新分析
              </Button>
            )}
            {onClose && (
              <Button theme="borderless" icon={<IconClose />} size="small" onClick={onClose} />
            )}
          </Space>
        </div>
      }
      className={className}
      style={{
        borderColor: "var(--semi-color-success)",
        background: "var(--semi-color-success-light-default)",
      }}
    >
      {/* 警告信息 */}
      {warning && (
        <div
          style={{
            marginBottom: 16,
            padding: 12,
            backgroundColor: "var(--semi-color-warning-light-default)",
            borderRadius: 6,
            border: "1px solid var(--semi-color-warning-light-active)",
          }}
        >
          <Space>
            <IconInfoCircle style={{ color: "var(--semi-color-warning)" }} />
            <Text type="warning" size="small">
              {warning}
            </Text>
          </Space>
        </div>
      )}

      {/* 建议列表 */}
      <div className="space-y-3">
        {suggestions.map((suggestion, index) => (
          <div
            key={`${suggestion.field}-${index}`}
            style={{
              padding: 12,
              backgroundColor: "var(--semi-color-bg-1)",
              borderRadius: 6,
              border: "1px solid var(--semi-color-border)",
            }}
          >
            <Row gutter={[12, 8]}>
              <Col span={16}>
                <div>
                  <Space>
                    <Text strong size="small">
                      {FIELD_LABELS[suggestion.field as keyof typeof FIELD_LABELS]}
                    </Text>
                    <Tag color={getConfidenceColor(suggestion.confidence)} size="small">
                      {suggestion.confidence}%
                    </Tag>
                  </Space>
                  <div style={{ marginTop: 4 }}>
                    <Text
                      style={{
                        fontSize: "14px",
                        color: "var(--semi-color-text-0)",
                        fontWeight: 500,
                      }}
                    >
                      {formatSuggestionValue(suggestion.field, suggestion.suggested)}
                    </Text>
                  </div>
                  {suggestion.reasoning && (
                    <div style={{ marginTop: 4 }}>
                      <Text type="tertiary" size="small">
                        {suggestion.reasoning}
                      </Text>
                    </div>
                  )}
                </div>
              </Col>
              <Col span={8} style={{ textAlign: "right" }}>
                {onAdoptSuggestion && (
                  <Button
                    theme="light"
                    type="primary"
                    size="small"
                    icon={<IconCheck />}
                    onClick={() => onAdoptSuggestion(suggestion.field, suggestion.suggested)}
                  >
                    采用
                  </Button>
                )}
              </Col>
            </Row>
          </div>
        ))}
      </div>

      {/* 元数据信息 */}
      {(metadata || processingTime) && (
        <div
          style={{
            marginTop: 16,
            paddingTop: 12,
            borderTop: "1px solid var(--semi-color-border)",
          }}
        >
          <Row gutter={[16, 8]}>
            {processingTime && (
              <Col>
                <Text type="tertiary" size="small">
                  分析耗时: {processingTime}ms
                </Text>
              </Col>
            )}
            {metadata?.estimatedHours && (
              <Col>
                <Text type="tertiary" size="small">
                  预估工时: {metadata.estimatedHours}小时
                </Text>
              </Col>
            )}
            {metadata?.tags && metadata.tags.length > 0 && (
              <Col span={24}>
                <Space wrap>
                  <Text type="tertiary" size="small">
                    标签:
                  </Text>
                  {metadata.tags.map(tag => (
                    <Tag key={tag} size="small" color="blue">
                      {tag}
                    </Tag>
                  ))}
                </Space>
              </Col>
            )}
          </Row>
        </div>
      )}
    </Card>
  );
};

export default AISuggestionCard;
