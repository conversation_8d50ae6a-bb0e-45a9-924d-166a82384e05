import React, { useMemo } from "react";
import { Card, Progress, Typography, Badge, Space, Tooltip } from "@douyinfe/semi-ui";
import { IconServer, IconActivity, IconWifi } from "@douyinfe/semi-icons";
import { useSystemHealth } from "@/hooks/useDashboard";

const { Title, Text } = Typography;

interface SystemHealthCardProps {
  className?: string;
}

export const SystemHealthCard: React.FC<SystemHealthCardProps> = ({ className }) => {
  const {
    systemHealth,
    healthScore,
    systemResources,
    databaseStatus,
    redisStatus,
    isLoading,
    error,
  } = useSystemHealth();

  // 健康状态配置
  const healthConfig = useMemo(() => {
    switch (systemHealth) {
      case "healthy":
        return {
          color: "green",
          text: "系统运行正常",
          badgeType: "success" as const,
          icon: <IconActivity style={{ color: "#52c41a" }} />,
        };
      case "warning":
        return {
          color: "orange",
          text: "系统运行异常",
          badgeType: "warning" as const,
          icon: <IconActivity style={{ color: "#fa8c16" }} />,
        };
      case "critical":
        return {
          color: "red",
          text: "系统运行故障",
          badgeType: "danger" as const,
          icon: <IconActivity style={{ color: "#ff4d4f" }} />,
        };
      default:
        return {
          color: "grey",
          text: "系统状态未知",
          badgeType: "tertiary" as const,
          icon: <IconActivity style={{ color: "#d9d9d9" }} />,
        };
    }
  }, [systemHealth]);

  // 资源使用情况配置
  const getResourceColor = (usage: number): string => {
    if (usage >= 90) return "#ff4d4f";
    if (usage >= 70) return "#fa8c16";
    return "#52c41a";
  };

  // 错误状态渲染 - 移到所有Hook调用之后
  if (error) {
    return (
      <Card className={className} title="系统监控" headerExtraContent={<IconServer />}>
        <div className="text-center py-4">
          <Text type="danger">获取系统状态失败</Text>
        </div>
      </Card>
    );
  }

  return (
    <Card
      className={className}
      title="系统监控"
      headerExtraContent={healthConfig.icon}
      loading={isLoading}
      bodyStyle={{ padding: "20px" }}
    >
      {/* 系统健康状态总览 */}
      <div className="mb-6">
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center gap-2">
            <Badge dot type={healthConfig.badgeType} />
            <Text strong>{healthConfig.text}</Text>
          </div>
          <Text type="secondary">健康评分: {healthScore}/100</Text>
        </div>

        <Progress percent={healthScore} stroke={healthConfig.color} showInfo={false} size="large" />
      </div>

      {/* 系统资源使用情况 */}
      {systemResources && (
        <div className="space-y-4 mb-6">
          <Title heading={6} className="!mb-3">
            资源使用情况
          </Title>

          {/* CPU使用率 */}
          <div>
            <div className="flex justify-between items-center mb-1">
              <Text size="small">CPU 使用率</Text>
              <Text size="small" style={{ color: getResourceColor(systemResources.cpu.usage) }}>
                {systemResources.cpu.usage.toFixed(1)}%
              </Text>
            </div>
            <Progress
              percent={systemResources.cpu.usage}
              stroke={getResourceColor(systemResources.cpu.usage)}
              showInfo={false}
              size="small"
            />
          </div>

          {/* 内存使用率 */}
          <div>
            <div className="flex justify-between items-center mb-1">
              <Text size="small">内存使用率</Text>
              <Text size="small" style={{ color: getResourceColor(systemResources.memory.usage) }}>
                {systemResources.memory.usage.toFixed(1)}%
              </Text>
            </div>
            <Progress
              percent={systemResources.memory.usage}
              stroke={getResourceColor(systemResources.memory.usage)}
              showInfo={false}
              size="small"
            />
          </div>

          {/* 磁盘使用率 */}
          <div>
            <div className="flex justify-between items-center mb-1">
              <Text size="small">磁盘使用率</Text>
              <Text size="small" style={{ color: getResourceColor(systemResources.disk.usage) }}>
                {systemResources.disk.usage.toFixed(1)}%
              </Text>
            </div>
            <Progress
              percent={systemResources.disk.usage}
              stroke={getResourceColor(systemResources.disk.usage)}
              showInfo={false}
              size="small"
            />
          </div>
        </div>
      )}

      {/* 服务连接状态 */}
      <div>
        <Title heading={6} className="!mb-3">
          服务连接状态
        </Title>

        <Space wrap>
          {/* 数据库状态 */}
          <Tooltip content={`响应时间: ${databaseStatus?.responseTime || "-"}ms`}>
            <div className="flex items-center gap-1">
              <IconServer
                style={{
                  color: databaseStatus?.isConnected ? "#52c41a" : "#ff4d4f",
                }}
              />
              <Badge dot type={databaseStatus?.isConnected ? "success" : "danger"} />
              <Text size="small">MySQL</Text>
            </div>
          </Tooltip>

          {/* Redis状态 */}
          <Tooltip content={`响应时间: ${redisStatus?.responseTime || "-"}ms`}>
            <div className="flex items-center gap-1">
              <IconServer
                style={{
                  color: redisStatus?.isConnected ? "#52c41a" : "#ff4d4f",
                }}
              />
              <Badge dot type={redisStatus?.isConnected ? "success" : "danger"} />
              <Text size="small">Redis</Text>
            </div>
          </Tooltip>

          {/* 网络状态 */}
          <Tooltip content="网络连接状态">
            <div className="flex items-center gap-1">
              <IconWifi style={{ color: "#52c41a" }} />
              <Badge dot type="success" />
              <Text size="small">网络</Text>
            </div>
          </Tooltip>
        </Space>
      </div>
    </Card>
  );
};
