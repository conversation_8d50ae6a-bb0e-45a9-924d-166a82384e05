import React, { useEffect, useRef } from "react";
import { Card, Typography, Spin } from "@douyinfe/semi-ui";
import * as echarts from "echarts/core";
import { Pie<PERSON>hart } from "echarts/charts";
import { TitleComponent, TooltipComponent, LegendComponent } from "echarts/components";
import { CanvasRenderer } from "echarts/renderers";

// 注册 ECharts 组件
echarts.use([PieChart, TitleComponent, TooltipComponent, LegendComponent, CanvasRenderer]);

const { Title } = Typography;

interface StatusData {
  name: string;
  value: number;
  color: string;
}

interface ServiceStatusChartProps {
  data: StatusData[];
  loading?: boolean;
}

export function ServiceStatusChart({ data, loading = false }: ServiceStatusChartProps) {
  const chartRef = useRef<HTMLDivElement>(null);
  const chartInstance = useRef<echarts.ECharts | null>(null);

  useEffect(() => {
    if (!chartRef.current || loading) return;

    // 初始化图表
    if (!chartInstance.current) {
      chartInstance.current = echarts.init(chartRef.current);
    }

    const option: echarts.EChartsCoreOption = {
      tooltip: {
        trigger: "item",
        formatter: "{a} <br/>{b}: {c} ({d}%)",
        backgroundColor: "var(--semi-color-bg-2)",
        borderColor: "var(--semi-color-border)",
        textStyle: {
          color: "var(--semi-color-text-0)",
        },
      },
      legend: {
        orient: "vertical",
        left: "left",
        top: "center",
        textStyle: {
          color: "var(--semi-color-text-1)",
        },
        itemGap: 12,
      },
      series: [
        {
          name: "工单状态",
          type: "pie",
          radius: ["40%", "70%"],
          center: ["65%", "50%"],
          avoidLabelOverlap: false,
          itemStyle: {
            borderRadius: 8,
            borderColor: "var(--semi-color-bg-0)",
            borderWidth: 2,
          },
          label: {
            show: false,
            position: "center",
          },
          emphasis: {
            label: {
              show: true,
              fontSize: 16,
              fontWeight: "bold",
              color: "var(--semi-color-text-0)",
            },
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: "rgba(0, 0, 0, 0.2)",
            },
          },
          labelLine: {
            show: false,
          },
          data: data.map(item => ({
            value: item.value,
            name: item.name,
            itemStyle: {
              color: item.color,
            },
          })),
        },
      ],
    };

    chartInstance.current.setOption(option);

    // 监听窗口大小变化
    const handleResize = () => {
      chartInstance.current?.resize();
    };
    window.addEventListener("resize", handleResize);

    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, [data, loading]);

  // 清理图表实例
  useEffect(() => {
    return () => {
      if (chartInstance.current) {
        chartInstance.current.dispose();
        chartInstance.current = null;
      }
    };
  }, []);

  return (
    <Card
      style={{
        background: "var(--semi-color-bg-2)",
        borderColor: "var(--semi-color-border)",
      }}
      bodyStyle={{ padding: "24px" }}
    >
      <Title heading={5} className="!mb-4" style={{ color: "var(--semi-color-text-0)" }}>
        工单状态分布
      </Title>

      <div className="relative h-64">
        {loading ? (
          <div className="flex items-center justify-center h-full">
            <Spin size="large" />
          </div>
        ) : (
          <div ref={chartRef} className="w-full h-full" style={{ minHeight: "256px" }} />
        )}
      </div>
    </Card>
  );
}
