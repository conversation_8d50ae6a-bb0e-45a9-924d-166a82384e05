import React from "react";
import { Card, Typography, Empty, Space } from "@douyinfe/semi-ui";
import {
  IconInfoCircle,
  IconAlertTriangle,
  IconAlertCircle,
  IconTickCircle,
  IconBell,
  IconClock,
} from "@douyinfe/semi-icons";

const { Title, Text } = Typography;

interface SystemNotification {
  id: string;
  type: "info" | "warning" | "error" | "success";
  title: string;
  message: string;
  time: string;
}

interface SystemNotificationsProps {
  notifications: SystemNotification[];
  loading?: boolean;
}

const notificationConfig = {
  info: {
    icon: IconInfoCircle,
    color: "var(--semi-color-primary)",
    bgColor: "rgba(59, 130, 246, 0.1)",
  },
  warning: {
    icon: IconAlertTriangle,
    color: "var(--semi-color-warning)",
    bgColor: "rgba(245, 158, 11, 0.1)",
  },
  error: {
    icon: IconAlertCircle,
    color: "var(--semi-color-danger)",
    bgColor: "rgba(239, 68, 68, 0.1)",
  },
  success: {
    icon: IconTickCircle,
    color: "var(--semi-color-success)",
    bgColor: "rgba(16, 185, 129, 0.1)",
  },
};

function formatTime(timeString: string): string {
  const time = new Date(timeString);
  const now = new Date();
  const diffTime = Math.abs(now.getTime() - time.getTime());
  const diffMinutes = Math.floor(diffTime / (1000 * 60));
  const diffHours = Math.floor(diffTime / (1000 * 60 * 60));
  const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));

  if (diffMinutes < 60) {
    return `${diffMinutes} 分钟前`;
  } else if (diffHours < 24) {
    return `${diffHours} 小时前`;
  } else if (diffDays < 7) {
    return `${diffDays} 天前`;
  } else {
    return time.toLocaleDateString("zh-CN", {
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  }
}

export function SystemNotifications({ notifications, loading = false }: SystemNotificationsProps) {
  return (
    <Card
      style={{
        background: "var(--semi-color-bg-2)",
        borderColor: "var(--semi-color-border)",
        height: "400px",
      }}
      bodyStyle={{ padding: "24px", height: "100%", display: "flex", flexDirection: "column" }}
    >
      <Title heading={5} className="!mb-4" style={{ color: "var(--semi-color-text-0)" }}>
        系统通知
      </Title>

      <div className="flex-1 overflow-y-auto">
        {notifications.length === 0 ? (
          <div className="flex items-center justify-center h-full">
            <Empty
              image={<IconBell size="extra-large" />}
              title="暂无通知"
              description="当前没有系统通知"
            />
          </div>
        ) : (
          <div className="space-y-3">
            {notifications.map(notification => {
              const config = notificationConfig[notification.type];
              const IconComponent = config.icon;

              return (
                <div
                  key={notification.id}
                  className="p-3 rounded-lg border transition-all duration-200 hover:shadow-sm"
                  style={{
                    backgroundColor: config.bgColor,
                    borderColor: "var(--semi-color-border)",
                  }}
                >
                  <div className="flex items-start space-x-3">
                    <div className="flex-shrink-0 mt-0.5" style={{ color: config.color }}>
                      <IconComponent size="default" />
                    </div>

                    <div className="flex-1 min-w-0">
                      <Text
                        strong
                        className="block mb-1"
                        style={{ color: "var(--semi-color-text-0)" }}
                      >
                        {notification.title}
                      </Text>

                      <Text
                        type="secondary"
                        size="small"
                        className="block mb-2 leading-relaxed"
                        style={{
                          color: "var(--semi-color-text-1)",
                          lineHeight: "1.4",
                        }}
                      >
                        {notification.message}
                      </Text>

                      <div className="flex items-center">
                        <IconClock size="small" className="mr-1" />
                        <Text
                          type="tertiary"
                          size="small"
                          style={{ color: "var(--semi-color-text-2)" }}
                        >
                          {formatTime(notification.time)}
                        </Text>
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>
    </Card>
  );
}
