import React from "react";
import { VChart } from "@visactor/vchart";
interface DiskUsageDonutProps {
  percent: number; // 0-100
  width?: number;
  height?: number;
}

// 动态加载 VChart，避免在未安装依赖时阻断渲染
export const DiskUsageDonut: React.FC<DiskUsageDonutProps> = ({
  percent,
  width = 140,
  height = 120,
}) => {
  const containerRef = React.useRef<HTMLDivElement | null>(null);
  const chartRef = React.useRef<any>(null);
  const [isReady, setIsReady] = React.useState(false);
  const [failed, setFailed] = React.useState(false);

  React.useEffect(() => {
    let destroyed = false;

    (async () => {
      try {
        if (destroyed) return;

        // 颜色根据百分比变化
        const usage = Math.max(0, Math.min(100, percent));
        const color = usage >= 90 ? "#ff4d4f" : usage >= 70 ? "#fa8c16" : "#10b981";

        const spec: any = {
          type: "pie",
          data: [
            {
              id: "disk",
              values: [
                { type: "used", value: usage },
                { type: "free", value: 100 - usage },
              ],
            },
          ],
          outerRadius: 0.9,
          innerRadius: 0.7,
          categoryField: "type",
          valueField: "value",
          // 关闭默认交互与图例
          legends: { visible: false },
          tooltip: { visible: false },
          label: { visible: false },
          pie: {
            state: {
              hover: { animation: false },
              selected: { animation: false },
            },
            style: {
              fill: (datum: any) => (datum.type === "used" ? color : "#e5e7eb"),
              stroke: "transparent",
            },
          },
          // 中心文本
          title: {
            visible: true,
            text: `${usage.toFixed(0)}%`,
            subtext: "磁盘",
            align: "center",
            verticalAlign: "middle",
            textStyle: { fontSize: 18, fontWeight: 600, fill: "#334155" },
            subtextStyle: { fontSize: 12, fill: "#64748b", dy: 18 },
          },
        };

        if (containerRef.current) {
          // 销毁旧图表
          if (chartRef.current) {
            chartRef.current?.release?.();
          }
          chartRef.current = new VChart(spec, { dom: containerRef.current });
          await chartRef.current.renderAsync?.();
          setIsReady(true);
        }
      } catch (err) {
        // 未安装依赖或渲染失败：使用 ECharts 简易兜底
        setFailed(true);
        try {
          const echarts = await import("echarts");
          if (destroyed || !containerRef.current) return;
          const inst = echarts.init(containerRef.current);
          inst.setOption({
            series: [
              {
                type: "pie",
                radius: ["70%", "90%"],
                avoidLabelOverlap: false,
                label: { show: false },
                labelLine: { show: false },
                data: [
                  { value: Math.max(0, Math.min(100, percent)), name: "used" },
                  { value: 100 - Math.max(0, Math.min(100, percent)), name: "free" },
                ],
                color: [
                  percent >= 90 ? "#ff4d4f" : percent >= 70 ? "#fa8c16" : "#10b981",
                  "#e5e7eb",
                ],
              },
            ],
          });
          chartRef.current = inst;
          setIsReady(true);
        } catch (_) {
          setIsReady(false);
        }
      }
    })();

    return () => {
      destroyed = true;
      if (chartRef.current) {
        // 兼容 VChart 与 ECharts
        chartRef.current?.release?.();
        chartRef.current?.dispose?.();
        chartRef.current = null;
      }
    };
  }, [percent]);

  return (
    <div style={{ width, height, position: "relative" }}>
      <div ref={containerRef} style={{ width: "100%", height: "100%" }} />
      {!isReady && (
        <div
          style={{
            position: "absolute",
            inset: 0,
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            color: "#64748b",
            fontSize: 12,
          }}
        >
          磁盘 {Math.round(percent)}%
        </div>
      )}
    </div>
  );
};

export default DiskUsageDonut;
