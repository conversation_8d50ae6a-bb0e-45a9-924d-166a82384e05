import React from "react";
import { Card, Typography, Tag, Empty, Space } from "@douyinfe/semi-ui";
import { IconTicketCodeStroked, IconCalendar, IconUser } from "@douyinfe/semi-icons";
import { Link } from "react-router-dom";

const { Title, Text } = Typography;

interface ServiceItem {
  id: string;
  title: string;
  status: "pending" | "processing" | "completed" | "closed";
  priority: "low" | "medium" | "high" | "urgent";
  customer: { name: string };
  createdAt: string;
}

interface RecentServicesProps {
  services: ServiceItem[];
  loading?: boolean;
}

const statusConfig = {
  pending: { text: "待处理", color: "orange" },
  processing: { text: "处理中", color: "blue" },
  completed: { text: "已完成", color: "green" },
  closed: { text: "已关闭", color: "grey" },
} as const;

const priorityConfig = {
  low: { text: "低", color: "grey" },
  medium: { text: "中", color: "blue" },
  high: { text: "高", color: "orange" },
  urgent: { text: "紧急", color: "red" },
} as const;

function formatDate(dateString: string): string {
  const date = new Date(dateString);
  const now = new Date();
  const diffTime = Math.abs(now.getTime() - date.getTime());
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

  if (diffDays === 1) {
    return "今天";
  } else if (diffDays === 2) {
    return "昨天";
  } else if (diffDays <= 7) {
    return `${diffDays} 天前`;
  } else {
    return date.toLocaleDateString("zh-CN", {
      month: "short",
      day: "numeric",
    });
  }
}

export function RecentServices({ services, loading = false }: RecentServicesProps) {
  return (
    <Card
      style={{
        background: "var(--semi-color-bg-2)",
        borderColor: "var(--semi-color-border)",
        height: "400px",
      }}
      bodyStyle={{ padding: "24px", height: "100%", display: "flex", flexDirection: "column" }}
    >
      <div className="flex items-center justify-between mb-4">
        <Title heading={5} className="!mb-0" style={{ color: "var(--semi-color-text-0)" }}>
          最新工单
        </Title>
        <Link
          to="/services"
          className="text-sm transition-colors"
          style={{
            color: "var(--semi-color-primary)",
            textDecoration: "none",
          }}
        >
          查看全部
        </Link>
      </div>

      <div className="flex-1 overflow-y-auto">
        {services.length === 0 ? (
          <div className="flex items-center justify-center h-full">
            <Empty
              image={<IconTicketCodeStroked size="extra-large" />}
              title="暂无工单"
              description="还没有最新的工单记录"
            />
          </div>
        ) : (
          <div className="space-y-3">
            {services.map(service => (
              <Link
                key={service.id}
                to={`/services/${service.id}`}
                className="block"
                style={{ textDecoration: "none" }}
              >
                <div
                  className="p-3 rounded-lg border transition-all duration-200 hover:shadow-md"
                  style={{
                    backgroundColor: "var(--semi-color-bg-1)",
                    borderColor: "var(--semi-color-border)",
                  }}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1 min-w-0">
                      <Text
                        strong
                        ellipsis={{ showTooltip: true }}
                        className="block mb-2"
                        style={{ color: "var(--semi-color-text-0)" }}
                      >
                        {service.title}
                      </Text>

                      <Space className="text-xs">
                        <div className="flex items-center">
                          <IconUser size="small" className="mr-1" />
                          <Text type="tertiary" size="small">
                            {service.customer.name}
                          </Text>
                        </div>
                        <div className="flex items-center">
                          <IconCalendar size="small" className="mr-1" />
                          <Text type="tertiary" size="small">
                            {formatDate(service.createdAt)}
                          </Text>
                        </div>
                      </Space>
                    </div>

                    <div className="flex flex-col items-end space-y-1 ml-3">
                      <Tag color={statusConfig[service.status].color} size="small">
                        {statusConfig[service.status].text}
                      </Tag>
                      {service.priority !== "low" && (
                        <Tag color={priorityConfig[service.priority].color} size="small">
                          {priorityConfig[service.priority].text}
                        </Tag>
                      )}
                    </div>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        )}
      </div>
    </Card>
  );
}
