import React, { useMemo } from "react";
import { Card, Typography, Spin, Empty } from "@douyinfe/semi-ui";
import { IconActivity } from "@douyinfe/semi-icons";
import { useTrendData } from "@/hooks/useDashboard";

const { Title, Text } = Typography;

interface TrendChartProps {
  days?: number;
  className?: string;
}

export const TrendChart: React.FC<TrendChartProps> = ({ days = 7, className }) => {
  const { data: trends, isLoading, error } = useTrendData(days);

  // 计算趋势指标
  const trendMetrics = useMemo(() => {
    if (!trends || trends.length === 0) return null;

    const latest = trends[trends.length - 1];
    const previous = trends[trends.length - 2] || trends[0];

    return {
      services: {
        current: latest.services,
        change: latest.services - previous.services,
        changePercent:
          previous.services > 0
            ? (((latest.services - previous.services) / previous.services) * 100).toFixed(1)
            : "0",
      },
      customers: {
        current: latest.customers,
        change: latest.customers - previous.customers,
        changePercent:
          previous.customers > 0
            ? (((latest.customers - previous.customers) / previous.customers) * 100).toFixed(1)
            : "0",
      },
      projects: {
        current: latest.projects,
        change: latest.projects - previous.projects,
        changePercent:
          previous.projects > 0
            ? (((latest.projects - previous.projects) / previous.projects) * 100).toFixed(1)
            : "0",
      },
    };
  }, [trends]);

  // 简单的SVG线图组件
  const SimpleLineChart: React.FC<{
    data: number[];
    color: string;
    width?: number;
    height?: number;
  }> = ({ data, color, width = 300, height = 60 }) => {
    if (data.length === 0) return null;

    const max = Math.max(...data);
    const min = Math.min(...data);
    const range = max - min || 1;

    const points = data
      .map((value, index) => {
        const x = (index / (data.length - 1)) * width;
        const y = height - ((value - min) / range) * height;
        return `${x},${y}`;
      })
      .join(" ");

    return (
      <svg width={width} height={height} className="overflow-visible">
        <polyline
          points={points}
          fill="none"
          stroke={color}
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        {/* 添加点 */}
        {data.map((value, index) => {
          const x = (index / (data.length - 1)) * width;
          const y = height - ((value - min) / range) * height;
          return <circle key={index} cx={x} cy={y} r="3" fill={color} />;
        })}
      </svg>
    );
  };

  if (error) {
    return (
      <Card className={className} title="数据趋势" headerExtraContent={<IconActivity />}>
        <div className="text-center py-8">
          <Text type="danger">获取趋势数据失败</Text>
        </div>
      </Card>
    );
  }

  if (isLoading) {
    return (
      <Card className={className} title="数据趋势" headerExtraContent={<IconActivity />}>
        <div className="text-center py-8">
          <Spin size="large" />
        </div>
      </Card>
    );
  }

  if (!trends || trends.length === 0) {
    return (
      <Card className={className} title="数据趋势" headerExtraContent={<IconActivity />}>
        <Empty description="暂无趋势数据" />
      </Card>
    );
  }

  return (
    <Card
      className={className}
      title={`数据趋势 (最近${days}天)`}
      headerExtraContent={<IconActivity />}
      bodyStyle={{ padding: "20px" }}
    >
      <div className="space-y-6">
        {/* 服务工单趋势 */}
        <div>
          <div className="flex justify-between items-center mb-2">
            <Text strong>服务工单</Text>
            {trendMetrics && (
              <div className="flex items-center gap-1">
                <Text size="small" type="secondary">
                  {trendMetrics.services.current}
                </Text>
                <Text size="small" type={trendMetrics.services.change >= 0 ? "success" : "danger"}>
                  ({trendMetrics.services.change >= 0 ? "+" : ""}
                  {trendMetrics.services.changePercent}%)
                </Text>
              </div>
            )}
          </div>
          <SimpleLineChart data={trends.map(t => t.services)} color="#1890ff" />
        </div>

        {/* 客户数量趋势 */}
        <div>
          <div className="flex justify-between items-center mb-2">
            <Text strong>客户数量</Text>
            {trendMetrics && (
              <div className="flex items-center gap-1">
                <Text size="small" type="secondary">
                  {trendMetrics.customers.current}
                </Text>
                <Text size="small" type={trendMetrics.customers.change >= 0 ? "success" : "danger"}>
                  ({trendMetrics.customers.change >= 0 ? "+" : ""}
                  {trendMetrics.customers.changePercent}%)
                </Text>
              </div>
            )}
          </div>
          <SimpleLineChart data={trends.map(t => t.customers)} color="#52c41a" />
        </div>

        {/* 项目数量趋势 */}
        <div>
          <div className="flex justify-between items-center mb-2">
            <Text strong>项目数量</Text>
            {trendMetrics && (
              <div className="flex items-center gap-1">
                <Text size="small" type="secondary">
                  {trendMetrics.projects.current}
                </Text>
                <Text size="small" type={trendMetrics.projects.change >= 0 ? "success" : "danger"}>
                  ({trendMetrics.projects.change >= 0 ? "+" : ""}
                  {trendMetrics.projects.changePercent}%)
                </Text>
              </div>
            )}
          </div>
          <SimpleLineChart data={trends.map(t => t.projects)} color="#fa8c16" />
        </div>
      </div>

      {/* 时间轴 */}
      <div className="flex justify-between mt-4 px-2">
        <Text size="small" type="tertiary">
          {trends[0]?.date}
        </Text>
        <Text size="small" type="tertiary">
          {trends[trends.length - 1]?.date}
        </Text>
      </div>
    </Card>
  );
};
