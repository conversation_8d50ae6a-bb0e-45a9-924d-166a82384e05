import React, { useEffect, useRef } from "react";
import { Card, Typography, Spin } from "@douyinfe/semi-ui";
import * as echarts from "echarts/core";
import { LineChart } from "echarts/charts";
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
} from "echarts/components";
import { CanvasRenderer } from "echarts/renderers";

// 注册 ECharts 组件
echarts.use([
  LineChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  CanvasRenderer,
]);

const { Title } = Typography;

interface TrendData {
  months: string[];
  newServices: number[];
  completedServices: number[];
}

interface MonthlyTrendChartProps {
  data: TrendData;
  loading?: boolean;
}

export function MonthlyTrendChart({ data, loading = false }: MonthlyTrendChartProps) {
  const chartRef = useRef<HTMLDivElement>(null);
  const chartInstance = useRef<echarts.ECharts | null>(null);

  useEffect(() => {
    if (!chartRef.current || loading) return;

    // 初始化图表
    if (!chartInstance.current) {
      chartInstance.current = echarts.init(chartRef.current);
    }

    const option: echarts.EChartsCoreOption = {
      tooltip: {
        trigger: "axis",
        backgroundColor: "var(--semi-color-bg-2)",
        borderColor: "var(--semi-color-border)",
        textStyle: {
          color: "var(--semi-color-text-0)",
        },
      },
      legend: {
        data: ["新建工单", "完成工单"],
        top: 0,
        textStyle: {
          color: "var(--semi-color-text-1)",
        },
      },
      grid: {
        left: "3%",
        right: "4%",
        bottom: "3%",
        top: "15%",
        containLabel: true,
      },
      xAxis: {
        type: "category",
        boundaryGap: false,
        data: data.months,
        axisLine: {
          lineStyle: {
            color: "var(--semi-color-border)",
          },
        },
        axisLabel: {
          color: "var(--semi-color-text-2)",
        },
      },
      yAxis: {
        type: "value",
        axisLine: {
          lineStyle: {
            color: "var(--semi-color-border)",
          },
        },
        axisLabel: {
          color: "var(--semi-color-text-2)",
        },
        splitLine: {
          lineStyle: {
            color: "var(--semi-color-border)",
            type: "dashed",
          },
        },
      },
      series: [
        {
          name: "新建工单",
          type: "line",
          smooth: true,
          symbol: "circle",
          symbolSize: 6,
          lineStyle: {
            width: 3,
            color: "#3b82f6",
          },
          itemStyle: {
            color: "#3b82f6",
            borderWidth: 2,
            borderColor: "#ffffff",
          },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: "rgba(59, 130, 246, 0.3)",
              },
              {
                offset: 1,
                color: "rgba(59, 130, 246, 0.05)",
              },
            ]),
          },
          data: data.newServices,
        },
        {
          name: "完成工单",
          type: "line",
          smooth: true,
          symbol: "circle",
          symbolSize: 6,
          lineStyle: {
            width: 3,
            color: "#10b981",
          },
          itemStyle: {
            color: "#10b981",
            borderWidth: 2,
            borderColor: "#ffffff",
          },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: "rgba(16, 185, 129, 0.3)",
              },
              {
                offset: 1,
                color: "rgba(16, 185, 129, 0.05)",
              },
            ]),
          },
          data: data.completedServices,
        },
      ],
    };

    chartInstance.current.setOption(option);

    // 监听窗口大小变化
    const handleResize = () => {
      chartInstance.current?.resize();
    };
    window.addEventListener("resize", handleResize);

    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, [data, loading]);

  // 清理图表实例
  useEffect(() => {
    return () => {
      if (chartInstance.current) {
        chartInstance.current.dispose();
        chartInstance.current = null;
      }
    };
  }, []);

  return (
    <Card
      style={{
        background: "var(--semi-color-bg-2)",
        borderColor: "var(--semi-color-border)",
      }}
      bodyStyle={{ padding: "24px" }}
    >
      <Title heading={5} className="!mb-4" style={{ color: "var(--semi-color-text-0)" }}>
        月度工单趋势
      </Title>

      <div className="relative h-64">
        {loading ? (
          <div className="flex items-center justify-center h-full">
            <Spin size="large" />
          </div>
        ) : (
          <div ref={chartRef} className="w-full h-full" style={{ minHeight: "256px" }} />
        )}
      </div>
    </Card>
  );
}
