import React from "react";
import { Card, Typography } from "@douyinfe/semi-ui";

const { Title, Text } = Typography;

interface StatCardProps {
  title: string;
  value: number | string;
  icon: React.ReactNode;
  color: "blue" | "green" | "yellow" | "purple" | "red";
  trend?: {
    value: number;
    isPositive: boolean;
  };
}

const colorMap = {
  blue: {
    bg: "from-blue-500 to-blue-600",
    shadow: "shadow-blue-500/20",
  },
  green: {
    bg: "from-green-500 to-green-600",
    shadow: "shadow-green-500/20",
  },
  yellow: {
    bg: "from-yellow-500 to-yellow-600",
    shadow: "shadow-yellow-500/20",
  },
  purple: {
    bg: "from-purple-500 to-purple-600",
    shadow: "shadow-purple-500/20",
  },
  red: {
    bg: "from-red-500 to-red-600",
    shadow: "shadow-red-500/20",
  },
};

export function StatCard({ title, value, icon, color, trend }: StatCardProps) {
  const colorConfig = colorMap[color];

  return (
    <Card
      className="relative overflow-hidden transition-all duration-300 hover:shadow-lg"
      bodyStyle={{ padding: "24px" }}
      style={{
        background: "var(--semi-color-bg-2)",
        borderColor: "var(--semi-color-border)",
      }}
    >
      <div className="flex items-center">
        <div className="flex-shrink-0">
          <div
            className={`w-12 h-12 rounded-xl bg-gradient-to-br ${colorConfig.bg} ${colorConfig.shadow} shadow-lg flex items-center justify-center transform hover:scale-105 transition-transform duration-200`}
          >
            <div className="text-white text-lg">{icon}</div>
          </div>
        </div>

        <div className="ml-5 flex-1 min-w-0">
          <Text
            type="secondary"
            size="small"
            className="block mb-1"
            style={{ color: "var(--semi-color-text-2)" }}
          >
            {title}
          </Text>

          <div className="flex items-baseline">
            <Title
              heading={3}
              className="!mb-0 !mr-2"
              style={{
                color: "var(--semi-color-text-0)",
                fontSize: "1.75rem",
                fontWeight: 600,
              }}
            >
              {typeof value === "number" ? value.toLocaleString() : value}
            </Title>

            {trend && (
              <div
                className={`flex items-center text-xs px-2 py-1 rounded-full ${
                  trend.isPositive
                    ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
                    : "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"
                }`}
              >
                <span className={`mr-1 ${trend.isPositive ? "↗" : "↘"}`}>
                  {trend.isPositive ? "↗" : "↘"}
                </span>
                {Math.abs(trend.value)}%
              </div>
            )}
          </div>
        </div>
      </div>

      {/* 装饰性背景 */}
      <div
        className={`absolute -top-4 -right-4 w-24 h-24 bg-gradient-to-br ${colorConfig.bg} opacity-5 rounded-full blur-xl`}
      />
    </Card>
  );
}
