/**
 * API Key创建/编辑表单组件
 */

import React, { useState, useRef } from "react";
import { Modal, Form, Input, Select, DatePicker, Button, Toast } from "@douyinfe/semi-ui";
import { IconCopy } from "@douyinfe/semi-icons";
import {
  createApi<PERSON><PERSON>,
  update<PERSON>pi<PERSON><PERSON>,
  createMy<PERSON><PERSON><PERSON><PERSON>,
  updateMyApi<PERSON>ey,
  type CreateApiKeyRequest,
  type UpdateApiKeyRequest,
  type ApiKey,
} from "@/services/api-key";

interface ApiKeyFormProps {
  visible: boolean;
  apiKey?: ApiKey | null;
  onSuccess: () => void;
  onCancel: () => void;
  useMyEndpoints?: boolean;
}

interface ApiKeyCreateResponse {
  success: boolean;
  data?: {
    id: string;
    name: string;
    keyValue: string;
    systemId: string;
    status: string;
    createdAt: string;
  };
  message?: string;
}

const ApiKeyForm: React.FC<ApiKeyFormProps> = ({
  visible,
  apiKey,
  onSuccess,
  onCancel,
  useMyEndpoints = false,
}) => {
  const [loading, setLoading] = useState(false);
  const [generatedKey, setGeneratedKey] = useState<string>("");
  const formRef = useRef<any>();

  const isEdit = !!apiKey;

  const handleSubmit = async (values: any) => {
    setLoading(true);

    try {
      if (isEdit) {
        // 更新API Key
        const updateData: UpdateApiKeyRequest = {
          name: values.name,
          description: values.description,
          status: values.status,
          expiresAt: values.expiresAt ? values.expiresAt.toISOString() : undefined,
        };

        if (useMyEndpoints) {
          await updateMyApiKey(apiKey!.id, updateData);
        } else {
          await updateApiKey(apiKey!.id, updateData);
        }
        Toast.success("API Key更新成功");
      } else {
        // 创建API Key
        const createData: CreateApiKeyRequest = {
          name: values.name,
          systemId: values.systemId,
          description: values.description,
          expiresAt: values.expiresAt ? values.expiresAt.toISOString() : undefined,
        };

        const response = (
          useMyEndpoints ? await createMyApiKey(createData) : await createApiKey(createData)
        ) as ApiKeyCreateResponse;

        if (response.success && response.data) {
          setGeneratedKey(response.data.keyValue);
          Toast.success("API Key创建成功");
        } else {
          Toast.error(response.message || "API Key创建失败");
          return;
        }
      }

      onSuccess();
    } catch (error: any) {
      console.error("API Key操作失败:", error);
      Toast.error(error.message || "API Key操作失败");
    } finally {
      setLoading(false);
    }
  };

  const handleCopyKey = () => {
    if (generatedKey) {
      navigator.clipboard
        .writeText(generatedKey)
        .then(() => {
          Toast.success("API Key已复制到剪贴板");
        })
        .catch(() => {
          Toast.error("复制失败，请手动复制");
        });
    }
  };

  const handleClose = () => {
    setGeneratedKey("");
    formRef.current?.reset();
    onCancel();
  };

  // 如果是刚创建的API Key，显示密钥值
  if (generatedKey) {
    return (
      <Modal
        title="API Key创建成功"
        visible={visible}
        width={600}
        footer={
          <div className="flex justify-end space-x-3">
            <Button onClick={handleClose}>确定</Button>
          </div>
        }
        onCancel={handleClose}
      >
        <div className="space-y-4">
          <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-4">
            <div className="text-sm text-green-800">
              <strong>✅ API Key创建成功！</strong>
            </div>
          </div>

          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
            <div className="text-sm text-yellow-800">
              <strong>⚠️ 安全提示：</strong>请立即复制并安全保存API
              Key，出于安全考虑，它只会显示这一次。
            </div>
          </div>

          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-3">
              🔑 系统生成的API Key
            </label>
            <div className="flex items-center space-x-2">
              <Input
                value={generatedKey}
                readOnly
                className="flex-1 font-mono text-sm bg-gray-50"
                style={{ fontSize: "13px", letterSpacing: "0.5px" }}
                addonAfter={
                  <Button
                    theme="borderless"
                    icon={<IconCopy />}
                    onClick={handleCopyKey}
                    title="复制API Key"
                  />
                }
              />
            </div>
            <div className="text-xs text-gray-500 mt-2">
              格式：ak_[时间戳][随机字符][校验码] - 系统自动生成，确保唯一性和安全性
            </div>
          </div>

          <div className="bg-gray-50 rounded-lg p-4 space-y-2">
            <div className="text-sm font-medium text-gray-700 mb-3">📋 API Key详细信息</div>
            <div className="grid grid-cols-1 gap-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600">密钥名称:</span>
                <span className="font-medium">{formRef.current?.getValue("name")}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">系统标识:</span>
                <span className="font-mono text-blue-600">
                  {formRef.current?.getValue("systemId")}
                </span>
              </div>
              {formRef.current?.getValue("description") && (
                <div className="flex justify-between">
                  <span className="text-gray-600">描述:</span>
                  <span className="text-right max-w-48 truncate">
                    {formRef.current?.getValue("description")}
                  </span>
                </div>
              )}
              <div className="flex justify-between">
                <span className="text-gray-600">创建时间:</span>
                <span>{new Date().toLocaleString()}</span>
              </div>
            </div>
          </div>
        </div>
      </Modal>
    );
  }

  return (
    <Modal
      title={isEdit ? "编辑API Key" : "创建API Key"}
      visible={visible}
      width={600}
      footer={null}
      onCancel={handleClose}
    >
      <Form
        ref={formRef}
        initValues={
          apiKey
            ? {
                name: apiKey.name,
                systemId: apiKey.systemId,
                description: apiKey.description,
                status: apiKey.status,
                expiresAt: apiKey.expiresAt ? new Date(apiKey.expiresAt) : undefined,
              }
            : undefined
        }
        onSubmit={handleSubmit}
        labelPosition="top"
      >
        <Form.Input
          field="name"
          label="密钥名称"
          placeholder="请输入API Key名称"
          rules={[
            { required: true, message: "请输入API Key名称" },
            { max: 100, message: "名称长度不能超过100个字符" },
          ]}
        />

        <Form.Input
          field="systemId"
          label="系统标识"
          placeholder="请输入外部系统标识"
          disabled={isEdit} // 编辑时不允许修改系统ID
          rules={[
            { required: true, message: "请输入系统标识" },
            { max: 50, message: "系统标识长度不能超过50个字符" },
            { pattern: /^[a-zA-Z0-9_-]+$/, message: "系统标识只能包含字母、数字、下划线和连字符" },
          ]}
        />

        <Form.TextArea
          field="description"
          label="描述"
          placeholder="请输入API Key描述（可选）"
          maxCount={500}
          showClear
        />

        {isEdit && (
          <Form.Select
            field="status"
            label="状态"
            placeholder="请选择状态"
            optionList={[
              { label: "活跃", value: "ACTIVE" },
              { label: "禁用", value: "INACTIVE" },
              { label: "撤销", value: "REVOKED" },
            ]}
            rules={[{ required: true, message: "请选择状态" }]}
          />
        )}

        <Form.DatePicker
          field="expiresAt"
          label="过期时间（可选）"
          placeholder="请选择过期时间"
          type="dateTime"
          format="yyyy-MM-dd HH:mm:ss"
          disabledDate={date => (date ? date < new Date() : false)}
        />

        <div className="flex justify-end space-x-3 mt-6">
          <Button onClick={handleClose} disabled={loading}>
            取消
          </Button>
          <Button htmlType="submit" type="primary" loading={loading} theme="solid">
            {isEdit ? "更新" : "创建"}
          </Button>
        </div>
      </Form>
    </Modal>
  );
};

export default ApiKeyForm;
