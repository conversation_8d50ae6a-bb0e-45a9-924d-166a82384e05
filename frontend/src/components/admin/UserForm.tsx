import React, { useEffect } from "react";
import { Form, Select, Button, Toast, Row, Col } from "@douyinfe/semi-ui";

import { useMutation, useQuery } from "@tanstack/react-query";
import { userService, type CreateUserData, type UpdateUserData } from "@/services/user";
import { roleService } from "@/services/role";

interface UserFormProps {
  user?: {
    id: string;
    username: string;
    email: string;
    fullName: string;
    roleId: string;
    department?: string;
    phone?: string;
    isActive: boolean;
  } | null;
  onSuccess: () => void;
  onCancel: () => void;
}

export const UserForm: React.FC<UserFormProps> = ({ user, onSuccess, onCancel }) => {
  const [formApi, setFormApi] = React.useState<any>(null);
  const isEdit = !!user;

  // 获取角色列表
  const { data: rolesData } = useQuery({
    queryKey: ["roles-all"],
    queryFn: () => roleService.getAllRoles(),
  });

  // 获取部门列表
  const { data: departmentsData } = useQuery({
    queryKey: ["departments"],
    queryFn: () => userService.getDepartments(),
  });

  // 创建用户
  const createMutation = useMutation({
    mutationFn: (data: CreateUserData) => userService.createUser(data),
    onSuccess: () => {
      Toast.success("用户创建成功");
      onSuccess();
    },
    onError: (error: Error) => {
      Toast.error(error.message || "创建失败");
    },
  });

  // 更新用户
  const updateMutation = useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateUserData }) =>
      userService.updateUser(id, data),
    onSuccess: () => {
      Toast.success("用户更新成功");
      onSuccess();
    },
    onError: (error: Error) => {
      Toast.error(error.message || "更新失败");
    },
  });

  useEffect(() => {
    if (formApi) {
      if (user) {
        formApi.setValues({
          username: user.username,
          email: user.email,
          fullName: user.fullName,
          roleId: user.roleId,
          department: user.department,
          phone: user.phone,
          isActive: user.isActive,
        });
      } else {
        formApi.reset();
        formApi.setValue("isActive", true);
      }
    }
  }, [user, formApi]);

  const handleSubmit = async (values: Record<string, unknown>) => {
    try {
      if (isEdit && user) {
        await updateMutation.mutateAsync({
          id: user.id,
          data: {
            username: values.username as string,
            email: values.email as string,
            fullName: values.fullName as string,
            roleId: values.roleId as string,
            department: (values.department as string) || undefined,
            phone: (values.phone as string) || undefined,
            isActive: values.isActive as boolean,
          },
        });
      } else {
        await createMutation.mutateAsync({
          username: values.username as string,
          email: values.email as string,
          password: values.password as string,
          fullName: values.fullName as string,
          roleId: values.roleId as string,
          department: (values.department as string) || undefined,
          phone: (values.phone as string) || undefined,
          isActive: values.isActive as boolean,
        });
      }
    } catch (error) {
      // 错误已由mutation自己处理
    }
  };

  return (
    <div className="space-y-6">
      <Form<any>
        getFormApi={api => setFormApi(api)}
        labelPosition="top"
        onSubmit={handleSubmit}
        style={{ padding: "16px 0" }}
      >
        {/* 账号信息 */}
        <div className="mb-4">
          <h4 className="text-base font-medium text-gray-900 mb-3">账号信息</h4>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Input
                field="username"
                label="用户名"
                placeholder="请输入用户名"
                rules={[
                  { required: true, message: "请输入用户名" },
                  { min: 3, message: "用户名长度不能少于3位" },
                  { max: 50, message: "用户名长度不能超过50位" },
                  { pattern: /^[a-zA-Z0-9_]+$/, message: "用户名只能包含字母、数字和下划线" },
                ]}
              />
            </Col>
            <Col span={12}>
              <Form.Input
                field="email"
                label="邮箱"
                placeholder="请输入邮箱"
                rules={[
                  { required: true, message: "请输入邮箱" },
                  { type: "email", message: "请输入正确的邮箱格式" },
                ]}
              />
            </Col>
          </Row>
        </div>
        {!isEdit && (
          <div className="mb-4">
            <Row gutter={16}>
              <Col span={12}>
                <Form.Input
                  field="password"
                  label="密码"
                  mode="password"
                  placeholder="请输入密码（至少6位）"
                  rules={[
                    { required: true, message: "请输入密码" },
                    { min: 6, message: "密码长度不能少于6位" },
                  ]}
                />
              </Col>
            </Row>
          </div>
        )}

        {/* 个人信息 */}
        <div className="mb-4">
          <h4 className="text-base font-medium text-gray-900 mb-3">个人信息</h4>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Input
                field="fullName"
                label="姓名"
                placeholder="请输入姓名"
                rules={[
                  { required: true, message: "请输入姓名" },
                  { max: 100, message: "姓名长度不能超过100位" },
                ]}
              />
            </Col>
            <Col span={12}>
              <Form.Input
                field="phone"
                label="手机号"
                placeholder="请输入手机号"
                rules={[{ pattern: /^1[3-9]\d{9}$/, message: "请输入正确的手机号码" }]}
              />
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Select
                field="department"
                label="部门"
                placeholder="请选择或输入部门"
                filter
                allowCreate
              >
                {departmentsData?.data?.map((dept: string) => (
                  <Select.Option key={dept} value={dept}>
                    {dept}
                  </Select.Option>
                ))}
              </Form.Select>
            </Col>
            <Col span={12}>
              <Form.Switch field="isActive" label="状态" checkedText="启" uncheckedText="禁" />
            </Col>
          </Row>
        </div>

        {/* 权限信息 */}
        <div className="mb-4">
          <h4 className="text-base font-medium text-gray-900 mb-3">权限信息</h4>
          <Row gutter={16}>
            <Col span={24}>
              <Form.Select
                field="roleId"
                label="角色"
                placeholder="请选择角色"
                rules={[{ required: true, message: "请选择角色" }]}
              >
                {rolesData?.data?.map((role: { id: string; name: string; description: string }) => (
                  <Select.Option key={role.id} value={role.id}>
                    {role.name} - {role.description}
                  </Select.Option>
                ))}
              </Form.Select>
            </Col>
          </Row>
        </div>
        <div className="flex justify-end gap-3 mt-6">
          <Button onClick={onCancel} size="large">
            取消
          </Button>
          <Button
            type="primary"
            htmlType="submit"
            loading={createMutation.isPending || updateMutation.isPending}
            size="large"
          >
            {isEdit ? "更新" : "创建"}
          </Button>
        </div>
      </Form>
    </div>
  );
};
