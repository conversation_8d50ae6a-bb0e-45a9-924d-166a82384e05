import React from "react";
import { Card, Tag, Table, Empty, Typography } from "@douyinfe/semi-ui";
import { useQuery } from "@tanstack/react-query";
import { IconTickCircle, IconMinusCircle } from "@douyinfe/semi-icons";
import { roleService, type PermissionGroup } from "@/services/role";
import type { Role } from "@/types";

interface PermissionMatrixProps {
  role: Role;
  readonly?: boolean;
}

export const PermissionMatrix: React.FC<PermissionMatrixProps> = ({ role, readonly = true }) => {
  // 获取权限分组
  const { data: permissionGroups, isLoading } = useQuery({
    queryKey: ["permission-groups"],
    queryFn: () => roleService.getPermissionGroups(),
  });

  if (isLoading) {
    return <div className="text-center py-4">加载权限信息中...</div>;
  }

  // 确保 permissionGroups.data 是数组
  const groups = Array.isArray(permissionGroups?.data) ? permissionGroups.data : [];

  if (groups.length === 0) {
    return <Empty title="暂无权限数据" description="请检查系统配置" />;
  }

  const rolePermissions = new Set(role.permissions);
  const totalPermissions = groups.reduce((sum, group) => sum + (group.permissions?.length || 0), 0);
  const hasPermissions = role.permissions.length;

  // 为表格准备数据
  const tableData = groups.map(group => {
    const permissions = group.permissions || [];
    const groupPermissions = permissions.map(p => p.name);
    const hasPermissionCount = groupPermissions.filter(p => rolePermissions.has(p)).length;

    return {
      key: group.name,
      groupName: group.label,
      description: `${group.name} 权限组`,
      permissions,
      hasCount: hasPermissionCount,
      totalCount: groupPermissions.length,
      coverage: Math.round((hasPermissionCount / groupPermissions.length) * 100),
    };
  });

  const columns = [
    {
      title: "权限组",
      dataIndex: "groupName",
      key: "groupName",
      render: (text: string, record: any) => (
        <div>
          <Typography.Text weight={600}>{text}</Typography.Text>
          <Typography.Text type="quaternary" size="small" className="block">
            {record.hasCount}/{record.totalCount} 个权限
          </Typography.Text>
        </div>
      ),
    },
    {
      title: "覆盖率",
      dataIndex: "coverage",
      key: "coverage",
      render: (coverage: number) => {
        let color = "red";
        if (coverage >= 80) color = "green";
        else if (coverage >= 50) color = "orange";

        return <Tag color={color as any}>{coverage}%</Tag>;
      },
    },
    {
      title: "权限详情",
      dataIndex: "permissions",
      key: "permissions",
      render: (permissions: any[]) => (
        <div className="space-y-1">
          {permissions.map(permission => {
            const hasPermission = rolePermissions.has(permission.name);
            return (
              <div key={permission.name} className="flex items-center space-x-2">
                {hasPermission ? (
                  <IconTickCircle style={{ color: "#52c41a" }} />
                ) : (
                  <IconMinusCircle style={{ color: "#ff4d4f" }} />
                )}
                <Typography.Text type={hasPermission ? "success" : "quaternary"}>
                  {permission.label}
                </Typography.Text>
                <Typography.Text type="quaternary" size="small">
                  ({permission.name})
                </Typography.Text>
              </div>
            );
          })}
        </div>
      ),
    },
  ];

  return (
    <div className="space-y-6">
      {/* 角色概览 */}
      <Card>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="text-center">
            <Typography.Title heading={3} style={{ color: "#1890ff", margin: 0 }}>
              {role.name}
            </Typography.Title>
            <Typography.Text type="quaternary">角色名称</Typography.Text>
          </div>
          <div className="text-center">
            <Typography.Title heading={3} style={{ color: "#52c41a", margin: 0 }}>
              {hasPermissions}
            </Typography.Title>
            <Typography.Text type="quaternary">拥有权限</Typography.Text>
          </div>
          <div className="text-center">
            <Typography.Title heading={3} style={{ color: "#722ed1", margin: 0 }}>
              {Math.round((hasPermissions / totalPermissions) * 100)}%
            </Typography.Title>
            <Typography.Text type="quaternary">权限覆盖率</Typography.Text>
          </div>
        </div>
        <div className="mt-4 p-4 bg-gray-50 rounded">
          <Typography.Text size="small" type="secondary">
            <Typography.Text weight={600}>角色描述：</Typography.Text>
            {role.description}
          </Typography.Text>
        </div>
      </Card>

      {/* 权限详情表格 */}
      <Card title="权限详情">
        <Table
          columns={columns}
          dataSource={tableData}
          pagination={false}
          expandedRowRender={(record: any) => {
            return (
              <div className="pl-4">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                  {record.permissions.map((permission: any) => {
                    const hasPermission = rolePermissions.has(permission.name);
                    return (
                      <div
                        key={permission.name}
                        className={`border rounded p-3 ${
                          hasPermission
                            ? "border-green-200 bg-green-50"
                            : "border-gray-200 bg-gray-50"
                        }`}
                      >
                        <div className="flex items-start space-x-2">
                          {hasPermission ? (
                            <IconTickCircle style={{ color: "#52c41a" }} className="mt-1" />
                          ) : (
                            <IconMinusCircle style={{ color: "#ff4d4f" }} className="mt-1" />
                          )}
                          <div className="flex-1">
                            <Typography.Text
                              weight={600}
                              size="small"
                              type={hasPermission ? "success" : "quaternary"}
                              className="block"
                            >
                              {permission.label}
                            </Typography.Text>
                            <Typography.Text type="quaternary" size="small" className="block mt-1">
                              {permission.description}
                            </Typography.Text>
                            <Typography.Text type="primary" size="small" className="block mt-1">
                              {permission.name}
                            </Typography.Text>
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            );
          }}
        />
      </Card>

      {/* 权限列表汇总 */}
      <Card title="权限列表">
        <div className="space-y-2">
          {role.permissions.map(permission => (
            <Tag key={permission} color="blue" className="mb-1">
              {permission}
            </Tag>
          ))}
          {role.permissions.length === 0 && (
            <Empty title="该角色没有任何权限" description="请联系管理员配置权限" />
          )}
        </div>
      </Card>
    </div>
  );
};
