import React, { useEffect } from "react";
import { Form, Input, Button, Toast, Space } from "@douyinfe/semi-ui";
import { useMutation, useQuery } from "@tanstack/react-query";
import { roleService, type CreateRoleData, type UpdateRoleData } from "@/services/role";
import type { Role } from "@/types";
import { PermissionSelector } from "./PermissionSelector";

interface RoleFormProps {
  role?: Role | null;
  onSuccess: () => void;
  onCancel: () => void;
}

export const RoleForm: React.FC<RoleFormProps> = ({ role, onSuccess, onCancel }) => {
  const [formApi, setFormApi] = React.useState<any>(null);
  const isEdit = !!role;

  // 创建角色
  const createMutation = useMutation({
    mutationFn: (data: CreateRoleData) => roleService.createRole(data),
    onSuccess: () => {
      Toast.success("角色创建成功");
      onSuccess();
    },
    onError: (error: any) => {
      Toast.error(error.message || "创建失败");
    },
  });

  // 更新角色
  const updateMutation = useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateRoleData }) =>
      roleService.updateRole(id, data),
    onSuccess: () => {
      Toast.success("角色更新成功");
      onSuccess();
    },
    onError: (error: any) => {
      Toast.error(error.message || "更新失败");
    },
  });

  useEffect(() => {
    if (formApi) {
      if (role) {
        formApi.setValues({
          name: role.name,
          description: role.description,
          permissions: role.permissions,
        });
      } else {
        formApi.reset();
      }
    }
  }, [role, formApi]);

  const handleSubmit = async (values: any) => {
    // 验证权限
    const permissions = formApi?.getValue("permissions") || [];
    if (!permissions || permissions.length === 0) {
      Toast.error("至少需要选择一个权限");
      return;
    }

    try {
      const submitData = {
        name: values.name,
        description: values.description,
        permissions,
      };

      if (isEdit) {
        await updateMutation.mutateAsync({
          id: role!.id,
          data: submitData,
        });
      } else {
        await createMutation.mutateAsync(submitData);
      }
    } catch (error) {
      // Error handling is done in mutation onError
    }
  };

  const validateRoleName = (_: any, value: string, callback: (error?: string) => void) => {
    if (!value) {
      callback("请输入角色名称");
      return;
    }
    if (!/^[a-zA-Z0-9_-]+$/.test(value)) {
      callback("角色名只能包含字母、数字、下划线和横线");
      return;
    }
    if (value.length < 1 || value.length > 50) {
      callback("角色名长度应在1-50字符之间");
      return;
    }
    callback();
  };

  const validatePermissions = (_: any, value: string[], callback: (error?: string) => void) => {
    if (!value || value.length === 0) {
      callback("至少需要选择一个权限");
      return;
    }
    callback();
  };

  return (
    <Form getFormApi={api => setFormApi(api)} labelPosition="top" onSubmit={handleSubmit}>
      <Form.Input
        field="name"
        label="角色名称"
        placeholder="请输入角色名称（如：admin、editor、viewer）"
        disabled={isEdit && ["admin", "editor", "viewer"].includes(role?.name || "")}
        rules={[{ validator: validateRoleName as any }]}
      />

      <Form.TextArea
        field="description"
        label="角色描述"
        placeholder="请输入角色描述"
        rows={3}
        showCounter
        maxLength={200}
        rules={[
          { required: true, message: "请输入角色描述" },
          { min: 1, max: 200, message: "描述长度应在1-200字符之间" },
        ]}
      />

      <div className="mb-4">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          权限配置 <span className="text-red-500">*</span>
        </label>
        <PermissionSelector
          value={formApi?.getValue("permissions")}
          onChange={value => formApi?.setValue("permissions", value)}
        />
      </div>

      <div className="flex justify-end space-x-2 mt-6">
        <Button onClick={onCancel}>取消</Button>
        <Button
          type="primary"
          htmlType="submit"
          loading={createMutation.isPending || updateMutation.isPending}
        >
          {isEdit ? "更新角色" : "创建角色"}
        </Button>
      </div>
    </Form>
  );
};
