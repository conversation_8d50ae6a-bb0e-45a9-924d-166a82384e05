import React, { useState } from "react";
import {
  Modal,
  Form,
  Input,
  Select,
  TextArea,
  Button,
  Toast,
  Space,
  Row,
  Col,
  Card,
  Typography,
  Switch,
} from "@douyinfe/semi-ui";
import { IconSave, IconEyeOpened, IconEyeClosedSolid } from "@douyinfe/semi-icons";

const { Text } = Typography;

interface ConfigurationCreateModalProps {
  visible: boolean;
  customerId: string;
  onCancel: () => void;
  onSuccess: () => void;
}

interface ConfigurationFormData {
  title: string;
  description: string;
  configType: "SERVER" | "DATABASE" | "VPN" | "ACCOUNT" | "ENVIRONMENT" | "OTHER";
  isActive: boolean;
  configData: Record<string, any>;
}

const configTypeOptions = [
  { value: "SERVER", label: "服务器配置" },
  { value: "DATABASE", label: "数据库配置" },
  { value: "VPN", label: "VPN配置" },
  { value: "ACCOUNT", label: "账户配置" },
  { value: "ENVIRONMENT", label: "环境配置" },
  { value: "OTHER", label: "其他配置" },
];

// 不同配置类型的字段定义
const configTypeFields = {
  SERVER: [
    { field: "host", label: "服务器地址", required: true, type: "input" },
    { field: "port", label: "端口", required: false, type: "input" },
    { field: "username", label: "用户名", required: false, type: "input" },
    { field: "password", label: "密码", required: false, type: "password" },
    { field: "sshKey", label: "SSH密钥", required: false, type: "textarea" },
    { field: "os", label: "操作系统", required: false, type: "input" },
  ],
  DATABASE: [
    { field: "host", label: "数据库地址", required: true, type: "input" },
    { field: "port", label: "端口", required: true, type: "input" },
    { field: "database", label: "数据库名", required: true, type: "input" },
    { field: "username", label: "用户名", required: true, type: "input" },
    { field: "password", label: "密码", required: true, type: "password" },
    { field: "charset", label: "字符集", required: false, type: "input" },
  ],
  VPN: [
    { field: "serverAddress", label: "VPN服务器", required: true, type: "input" },
    { field: "protocol", label: "协议类型", required: false, type: "input" },
    { field: "username", label: "用户名", required: true, type: "input" },
    { field: "password", label: "密码", required: true, type: "password" },
    { field: "certificate", label: "证书", required: false, type: "textarea" },
  ],
  ACCOUNT: [
    { field: "platform", label: "平台名称", required: true, type: "input" },
    { field: "username", label: "用户名", required: true, type: "input" },
    { field: "password", label: "密码", required: true, type: "password" },
    { field: "email", label: "邮箱", required: false, type: "input" },
    { field: "phone", label: "手机号", required: false, type: "input" },
    { field: "apiKey", label: "API密钥", required: false, type: "password" },
  ],
  ENVIRONMENT: [
    { field: "name", label: "环境名称", required: true, type: "input" },
    { field: "domain", label: "域名", required: false, type: "input" },
    { field: "apiUrl", label: "API地址", required: false, type: "input" },
    { field: "version", label: "版本", required: false, type: "input" },
    { field: "branch", label: "分支", required: false, type: "input" },
  ],
  OTHER: [
    { field: "key1", label: "配置项1", required: false, type: "input" },
    { field: "value1", label: "配置值1", required: false, type: "input" },
    { field: "key2", label: "配置项2", required: false, type: "input" },
    { field: "value2", label: "配置值2", required: false, type: "input" },
  ],
};

export const ConfigurationCreateModal: React.FC<ConfigurationCreateModalProps> = ({
  visible,
  customerId,
  onCancel,
  onSuccess,
}) => {
  const [formApi, setFormApi] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [selectedConfigType, setSelectedConfigType] = useState<string>("SERVER");
  const [showPasswords, setShowPasswords] = useState<Record<string, boolean>>({});

  const handleSubmit = async () => {
    try {
      const values = await formApi?.validate();
      if (!values) return;

      setLoading(true);

      // 从表单值中分离基本信息和配置数据
      const { title, description, configType, isActive, ...configData } = values;

      // 构建请求数据
      const requestData: ConfigurationFormData & { customerId: string } = {
        title,
        description,
        configType,
        isActive: isActive ?? true,
        configData,
        customerId,
      };

      // 这里调用API创建配置
      console.log("创建配置:", requestData);

      // 模拟API请求
      await new Promise(resolve => setTimeout(resolve, 1000));

      Toast.success("配置创建成功");
      onSuccess();
    } catch (error: any) {
      console.error("创建配置失败:", error);
      Toast.error(error.message || "创建失败，请稍后重试");
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    formApi?.reset();
    setSelectedConfigType("SERVER");
    setShowPasswords({});
    onCancel();
  };

  const handleConfigTypeChange = (value: string | number | any[] | Record<string, any>) => {
    const configType = String(value);
    setSelectedConfigType(configType);
    // 清空配置数据字段
    const currentValues = formApi?.getValues();
    if (currentValues) {
      // 保留基本信息，清空配置数据
      const basicFields = ["title", "description", "configType", "isActive"];
      const newValues = Object.keys(currentValues)
        .filter(key => basicFields.includes(key))
        .reduce((obj, key) => {
          obj[key] = currentValues[key];
          return obj;
        }, {} as any);
      formApi?.setValues(newValues);
    }
  };

  const togglePasswordVisibility = (field: string) => {
    setShowPasswords(prev => ({
      ...prev,
      [field]: !prev[field],
    }));
  };

  const renderConfigFields = () => {
    const fields = configTypeFields[selectedConfigType as keyof typeof configTypeFields] || [];

    return fields.map((fieldConfig, index) => {
      const isPassword = fieldConfig.type === "password";
      const showPassword = showPasswords[fieldConfig.field];

      if (fieldConfig.type === "textarea") {
        return (
          <Col span={24} key={fieldConfig.field}>
            <Form.TextArea
              field={fieldConfig.field}
              label={fieldConfig.label}
              placeholder={`请输入${fieldConfig.label}`}
              rows={3}
              rules={
                fieldConfig.required
                  ? [{ required: true, message: `请输入${fieldConfig.label}` }]
                  : []
              }
            />
          </Col>
        );
      }

      return (
        <Col span={12} key={fieldConfig.field}>
          <Form.Input
            field={fieldConfig.field}
            label={fieldConfig.label}
            placeholder={`请输入${fieldConfig.label}`}
            type={isPassword && !showPassword ? "password" : "text"}
            suffix={
              isPassword ? (
                <Button
                  theme="borderless"
                  icon={showPassword ? <IconEyeClosedSolid /> : <IconEyeOpened />}
                  size="small"
                  onClick={() => togglePasswordVisibility(fieldConfig.field)}
                />
              ) : undefined
            }
            rules={
              fieldConfig.required
                ? [{ required: true, message: `请输入${fieldConfig.label}` }]
                : []
            }
          />
        </Col>
      );
    });
  };

  return (
    <Modal
      title="创建配置"
      visible={visible}
      width={900}
      onCancel={handleCancel}
      footer={
        <Space>
          <Button onClick={handleCancel} disabled={loading}>
            取消
          </Button>
          <Button
            theme="solid"
            type="primary"
            icon={<IconSave />}
            loading={loading}
            onClick={handleSubmit}
          >
            保存
          </Button>
        </Space>
      }
      closeOnEsc={!loading}
      maskClosable={!loading}
    >
      <Form
        getFormApi={setFormApi}
        labelPosition="top"
        labelAlign="left"
        initValues={{
          configType: "SERVER",
          isActive: true,
        }}
      >
        {/* 基本信息 */}
        <Card title="基本信息" style={{ marginBottom: 16 }}>
          <Row gutter={[16, 0]}>
            <Col span={12}>
              <Form.Input
                field="title"
                label="配置名称"
                placeholder="请输入配置名称"
                rules={[
                  { required: true, message: "请输入配置名称" },
                  { min: 2, max: 100, message: "配置名称长度应在2-100字符之间" },
                ]}
              />
            </Col>
            <Col span={12}>
              <Form.Select
                field="configType"
                label="配置类型"
                placeholder="请选择配置类型"
                optionList={configTypeOptions}
                onChange={handleConfigTypeChange}
                rules={[{ required: true, message: "请选择配置类型" }]}
              />
            </Col>
          </Row>

          <Form.TextArea
            field="description"
            label="配置描述"
            placeholder="请输入配置描述"
            rows={2}
            maxCount={500}
            rules={[
              { required: true, message: "请输入配置描述" },
              { min: 5, max: 500, message: "配置描述长度应在5-500字符之间" },
            ]}
          />

          <Form.Switch field="isActive" label="是否启用" />
        </Card>

        {/* 配置数据 */}
        <Card title="配置数据">
          <Row gutter={[16, 0]}>{renderConfigFields()}</Row>

          {selectedConfigType !== "OTHER" && (
            <div
              style={{ marginTop: 16, padding: 12, backgroundColor: "#f8f9fa", borderRadius: 6 }}
            >
              <Text type="secondary" size="small">
                💡 敏感信息（如密码、密钥）将会自动加密存储，请放心填写
              </Text>
            </div>
          )}
        </Card>
      </Form>
    </Modal>
  );
};
