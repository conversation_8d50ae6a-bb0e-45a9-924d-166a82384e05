import React, { useState } from "react";
import {
  Checkbox,
  Card,
  Collapsible,
  Space,
  Button,
  Input,
  Empty,
  Typography,
} from "@douyinfe/semi-ui";
import { useQuery } from "@tanstack/react-query";
import { IconSearch, IconTick, IconClear } from "@douyinfe/semi-icons";
import { roleService, type PermissionGroup } from "@/services/role";

const { Text } = Typography;

interface PermissionSelectorProps {
  value?: string[];
  onChange?: (value: string[]) => void;
  disabled?: boolean;
}

export const PermissionSelector: React.FC<PermissionSelectorProps> = ({
  value = [],
  onChange,
  disabled = false,
}) => {
  const [searchText, setSearchText] = useState("");
  const [activeKeys, setActiveKeys] = useState<string[]>([]);

  // 获取权限分组
  const { data: permissionGroups, isLoading } = useQuery({
    queryKey: ["permission-groups"],
    queryFn: () => roleService.getPermissionGroups(),
  });

  const handlePermissionChange = (permission: string, checked: boolean) => {
    if (disabled) return;

    let newValue: string[];
    if (checked) {
      newValue = [...value, permission];
    } else {
      newValue = value.filter(p => p !== permission);
    }
    onChange?.(newValue);
  };

  const handleGroupChange = (groupPermissions: string[], checked: boolean) => {
    if (disabled) return;

    let newValue: string[];
    if (checked) {
      // 添加整个组的权限
      const uniquePermissions = new Set([...value, ...groupPermissions]);
      newValue = Array.from(uniquePermissions);
    } else {
      // 移除整个组的权限
      newValue = value.filter(p => !groupPermissions.includes(p));
    }
    onChange?.(newValue);
  };

  const handleSelectAll = () => {
    if (disabled) return;

    const allPermissions = groups.flatMap(group => (group.permissions || []).map(p => p.name));
    onChange?.(allPermissions);
  };

  const handleClearAll = () => {
    if (disabled) return;
    onChange?.([]);
  };

  // 确保 permissionGroups.data 是数组
  const groups = Array.isArray(permissionGroups?.data) ? permissionGroups.data : [];

  // 搜索过滤
  const filteredGroups = groups
    .map(group => ({
      ...group,
      permissions: (group.permissions || []).filter(
        permission =>
          !searchText ||
          permission.name.toLowerCase().includes(searchText.toLowerCase()) ||
          permission.label.toLowerCase().includes(searchText.toLowerCase()) ||
          permission.description.toLowerCase().includes(searchText.toLowerCase())
      ),
    }))
    .filter(group => group.permissions.length > 0);

  // 自动展开有搜索结果的分组
  React.useEffect(() => {
    if (searchText) {
      const keysWithResults = filteredGroups.map(group => group.name);
      setActiveKeys(keysWithResults);
    }
  }, [searchText, filteredGroups]);

  if (isLoading) {
    return <div className="text-center py-4">加载权限列表中...</div>;
  }

  if (groups.length === 0) {
    return <Empty description="暂无权限数据" />;
  }

  const totalPermissions = groups.reduce((sum, group) => sum + (group.permissions?.length || 0), 0);
  const selectedCount = value.length;

  return (
    <div className="space-y-4">
      {/* 搜索和批量操作 */}
      <div className="flex flex-wrap gap-4 items-center">
        <Input
          placeholder="搜索权限..."
          prefix={<IconSearch />}
          value={searchText}
          onChange={(value: string) => setSearchText(value)}
          style={{ width: 300 }}
          showClear
        />
        <Text type="quaternary" size="small">
          已选择 {selectedCount} / {totalPermissions} 个权限
        </Text>
        <Space>
          <Button size="small" icon={<IconTick />} onClick={handleSelectAll} disabled={disabled}>
            全选
          </Button>
          <Button size="small" icon={<IconClear />} onClick={handleClearAll} disabled={disabled}>
            清空
          </Button>
        </Space>
      </div>

      {/* 权限分组 */}
      <div className="space-y-4">
        {filteredGroups.map(group => {
          const groupPermissions = group.permissions.map(p => p.name);
          const selectedInGroup = groupPermissions.filter(p => value.includes(p));
          const isGroupFullySelected = selectedInGroup.length === groupPermissions.length;
          const isGroupPartiallySelected =
            selectedInGroup.length > 0 && selectedInGroup.length < groupPermissions.length;
          const isGroupExpanded = activeKeys.includes(group.name);

          return (
            <Card key={group.name} className="border">
              <Collapsible
                isOpen={isGroupExpanded}
                onOpenChange={(isOpen: boolean) => {
                  if (isOpen) {
                    setActiveKeys([...activeKeys, group.name]);
                  } else {
                    setActiveKeys(activeKeys.filter(key => key !== group.name));
                  }
                }}
              >
                <div>
                  <div className="flex items-center space-x-3">
                    <Checkbox
                      checked={isGroupFullySelected}
                      indeterminate={isGroupPartiallySelected}
                      onChange={e =>
                        handleGroupChange(groupPermissions, e.target?.checked || false)
                      }
                      disabled={disabled}
                    />
                    <div>
                      <Text weight={600}>{group.label}</Text>
                      <Text type="quaternary" size="small" className="ml-2">
                        ({selectedInGroup.length}/{groupPermissions.length})
                      </Text>
                    </div>
                  </div>
                </div>
                <div>
                  <div className="mt-4 pl-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                      {group.permissions.map(permission => (
                        <Card key={permission.name} className="border hover:bg-gray-50">
                          <Checkbox
                            checked={value.includes(permission.name)}
                            onChange={e =>
                              handlePermissionChange(permission.name, e.target?.checked || false)
                            }
                            disabled={disabled}
                          >
                            <div>
                              <Text weight={600} size="small">
                                {permission.label}
                              </Text>
                              <Text type="quaternary" size="small" className="block mt-1">
                                {permission.description}
                              </Text>
                              <Text type="primary" size="small" className="block mt-1">
                                {permission.name}
                              </Text>
                            </div>
                          </Checkbox>
                        </Card>
                      ))}
                    </div>
                  </div>
                </div>
              </Collapsible>
            </Card>
          );
        })}
      </div>

      {filteredGroups.length === 0 && searchText && (
        <Empty title={`未找到包含 "${searchText}" 的权限`} description="请尝试其他关键词" />
      )}
    </div>
  );
};
