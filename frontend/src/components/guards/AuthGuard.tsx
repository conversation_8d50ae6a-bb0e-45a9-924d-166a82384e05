import React, { useEffect, useRef } from "react";
import { Navigate, useLocation } from "react-router-dom";
import { useAuthStore } from "@/stores";
import { PageLoading } from "@/components/common/PageLoading";

interface AuthGuardProps {
  children: React.ReactNode;
}

export function AuthGuard({ children }: AuthGuardProps) {
  const { isAuthenticated, loading, user, token, checkAuth } = useAuthStore();
  const location = useLocation();
  const hasCheckedAuth = useRef(false);
  const lastAuthCheckTime = useRef(0);
  const AUTH_CHECK_COOLDOWN = 2000; // 2秒冷却时间

  useEffect(() => {
    const now = Date.now();

    // 如果已经认证或正在加载，不需要检查
    if (isAuthenticated || loading) {
      return;
    }

    // 如果没有token，直接跳转到登录页，不调用checkAuth
    if (!token) {
      console.log("AuthGuard: 没有token，跳过认证检查");
      return;
    }

    // 冷却时间检查，防止频繁调用
    if (now - lastAuthCheckTime.current < AUTH_CHECK_COOLDOWN) {
      console.log("AuthGuard: 认证检查冷却中，跳过");
      return;
    }

    // 如果已经检查过一次且用户为null，不再重复检查
    if (hasCheckedAuth.current && !user) {
      console.log("AuthGuard: 已检查过且用户为null，跳过重复检查");
      return;
    }

    console.log("AuthGuard: 执行认证检查");
    lastAuthCheckTime.current = now;
    hasCheckedAuth.current = true;

    checkAuth().catch(error => {
      console.error("AuthGuard: 认证检查失败", error);
    });
  }, [isAuthenticated, loading, user, token]); // 移除checkAuth从依赖项

  if (loading) {
    return <PageLoading />;
  }

  if (!isAuthenticated) {
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  return <>{children}</>;
}
