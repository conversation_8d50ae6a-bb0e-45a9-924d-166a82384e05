import React from "react";
import { Navigate, useLocation } from "react-router-dom";
import { useAuthStore } from "@/stores";

interface PermissionGuardProps {
  children: React.ReactNode;
  permissions?: string | string[];
  roles?: string | string[];
  requireAll?: boolean; // 是否需要满足所有权限，默认false（满足任一即可）
  fallback?: React.ReactNode; // 权限不足时的fallback组件
  redirectTo?: string; // 权限不足时重定向的路径
}

export function PermissionGuard({
  children,
  permissions = [],
  roles = [],
  requireAll = false,
  fallback,
  redirectTo = "/403",
}: PermissionGuardProps) {
  const { user, hasPermission, hasRole } = useAuthStore();
  const location = useLocation();

  // 如果用户未登录，这个守卫不处理（由AuthGuard处理）
  if (!user) {
    return <>{children}</>;
  }

  // 转换为数组格式
  const permissionList = Array.isArray(permissions) ? permissions : [permissions].filter(Boolean);
  const roleList = Array.isArray(roles) ? roles : [roles].filter(Boolean);

  // 检查权限
  const checkPermissions = (): boolean => {
    if (permissionList.length === 0 && roleList.length === 0) {
      return true; // 没有权限要求，直接通过
    }

    // 检查角色
    let roleCheck = true;
    if (roleList.length > 0) {
      if (requireAll) {
        roleCheck = roleList.every(role => hasRole(role));
      } else {
        roleCheck = roleList.some(role => hasRole(role));
      }
    }

    // 检查权限
    let permissionCheck = true;
    if (permissionList.length > 0) {
      if (requireAll) {
        permissionCheck = permissionList.every(permission => hasPermission(permission));
      } else {
        permissionCheck = permissionList.some(permission => hasPermission(permission));
      }
    }

    // 如果同时指定了角色和权限，需要都满足
    if (roleList.length > 0 && permissionList.length > 0) {
      return roleCheck && permissionCheck;
    }

    // 只有角色要求或只有权限要求
    return roleCheck && permissionCheck;
  };

  const hasAccess = checkPermissions();

  // 权限检查失败
  if (!hasAccess) {
    // 优先返回自定义fallback
    if (fallback) {
      return <>{fallback}</>;
    }

    // 重定向到指定页面，携带当前路径信息
    return (
      <Navigate
        to={redirectTo}
        state={{
          from: location,
          requiredPermissions: permissionList,
          requiredRoles: roleList,
          currentUser: {
            role: user.role.name,
            permissions: user.role.permissions,
          },
        }}
        replace
      />
    );
  }

  return <>{children}</>;
}

// 高阶组件版本，用于包装组件
export function withPermission(
  Component: React.ComponentType,
  permissions?: string | string[],
  roles?: string | string[],
  options?: {
    requireAll?: boolean;
    fallback?: React.ReactNode;
    redirectTo?: string;
  }
) {
  return function PermissionWrappedComponent(props: any) {
    return (
      <PermissionGuard
        permissions={permissions}
        roles={roles}
        requireAll={options?.requireAll}
        fallback={options?.fallback}
        redirectTo={options?.redirectTo}
      >
        <Component {...props} />
      </PermissionGuard>
    );
  };
}

// Hook版本，用于在组件内部检查权限
export function usePermissionCheck(
  permissions?: string | string[],
  roles?: string | string[],
  requireAll = false
): {
  hasAccess: boolean;
  missingPermissions: string[];
  missingRoles: string[];
} {
  const { user, hasPermission, hasRole } = useAuthStore();

  if (!user) {
    return {
      hasAccess: false,
      missingPermissions: [],
      missingRoles: [],
    };
  }

  const permissionList = Array.isArray(permissions) ? permissions : [permissions].filter(Boolean);
  const roleList = Array.isArray(roles) ? roles : [roles].filter(Boolean);

  // 检查权限并收集缺失的权限
  const missingPermissions = permissionList.filter(
    permission => permission && !hasPermission(permission)
  );
  const missingRoles = roleList.filter(role => role && !hasRole(role));

  let hasAccess = true;

  if (requireAll) {
    // 需要满足所有权限和角色
    hasAccess = missingPermissions.length === 0 && missingRoles.length === 0;
  } else {
    // 满足任一权限或角色即可
    if (permissionList.length > 0 && roleList.length > 0) {
      // 同时有权限和角色要求，需要至少满足各自的一个
      hasAccess =
        missingPermissions.length < permissionList.length && missingRoles.length < roleList.length;
    } else if (permissionList.length > 0) {
      // 只有权限要求
      hasAccess = missingPermissions.length < permissionList.length;
    } else if (roleList.length > 0) {
      // 只有角色要求
      hasAccess = missingRoles.length < roleList.length;
    }
  }

  return {
    hasAccess,
    missingPermissions: missingPermissions.filter((p): p is string => Boolean(p)),
    missingRoles: missingRoles.filter((r): r is string => Boolean(r)),
  };
}
