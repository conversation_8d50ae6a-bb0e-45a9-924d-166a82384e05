import React, { useMemo, useRef, useEffect, useLayoutEffect } from 'react'
import { Card, Typography, Spin, Empty, Row, Col, Progress, Tag, Banner } from '@douyinfe/semi-ui'
import { IconShield, IconActivity, IconAlertTriangle, IconStar } from '@douyinfe/semi-icons'
import * as echarts from 'echarts'
import type { SLAComplianceReport } from '@/services/report.service'

const { Title, Text } = Typography

interface SLAComplianceChartProps {
  data?: SLAComplianceReport
  loading?: boolean
  error?: any
}

export const SLAComplianceChart: React.FC<SLAComplianceChartProps> = ({ data, loading, error }) => {
  const complianceTrendRef = useRef<HTMLDivElement>(null)
  const violationsPriorityRef = useRef<HTMLDivElement>(null)
  const violationsCategoryRef = useRef<HTMLDivElement>(null)

  // ========== 图表配置 ==========
  const chartOptions = useMemo(() => {
    if (!data) return null

    // SLA合规趋势图
    const trendOption = {
      title: {
        text: 'SLA合规性月度趋势',
        left: 'center',
        textStyle: { fontSize: 16, fontWeight: 'bold' }
      },
      tooltip: {
        trigger: 'axis',
        formatter: (params: any) => {
          const item = params[0]
          const monthData = data.monthlyTrend.find(t => t.month === item.axisValue)
          return `${item.axisValue}<br/>合规率: ${item.value}%<br/>违规数: ${monthData?.violations || 0}<br/>总数: ${monthData?.total || 0}`
        }
      },
      xAxis: {
        type: 'category',
        data: data.monthlyTrend.map(t => t.month),
        axisLabel: {
          formatter: (value: string) => {
            const date = new Date(value)
            return `${date.getMonth() + 1}月`
          }
        }
      },
      yAxis: {
        type: 'value',
        min: 0,
        max: 100,
        name: '合规率 (%)',
        axisLabel: {
          formatter: '{value}%'
        }
      },
      series: [{
        name: '合规率',
        type: 'line',
        data: data.monthlyTrend.map(t => t.rate),
        smooth: true,
        symbol: 'circle',
        symbolSize: 8,
        lineStyle: {
          width: 3,
          color: '#52c41a'
        },
        itemStyle: {
          color: '#52c41a'
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [{
              offset: 0, color: 'rgba(82, 196, 26, 0.3)'
            }, {
              offset: 1, color: 'rgba(82, 196, 26, 0.1)'
            }]
          }
        },
        markLine: {
          data: [{
            yAxis: 95,
            name: 'SLA目标线',
            lineStyle: {
              color: '#ff4d4f',
              type: 'dashed'
            }
          }]
        }
      }]
    }

    // 优先级违规分布
    const priorityViolationsData = Object.entries(data.violationsByPriority).map(([priority, info]) => ({
      name: getPriorityText(priority),
      value: info.violations,
      rate: info.rate,
      total: info.total,
      itemStyle: {
        color: getPriorityColor(priority)
      }
    }))

    const priorityOption = {
      title: {
        text: '优先级违规分布',
        left: 'center',
        textStyle: { fontSize: 16, fontWeight: 'bold' }
      },
      tooltip: {
        trigger: 'item',
        formatter: (params: any) => {
          const item = priorityViolationsData.find(d => d.name === params.name)
          return `${params.name}<br/>违规数: ${params.value}<br/>违规率: ${item?.rate.toFixed(1)}%<br/>总数: ${item?.total}`
        }
      },
      xAxis: {
        type: 'category',
        data: priorityViolationsData.map(item => item.name)
      },
      yAxis: {
        type: 'value',
        name: '违规数量'
      },
      series: [{
        name: '违规数量',
        type: 'bar',
        data: priorityViolationsData,
        emphasis: {
          focus: 'series'
        },
        label: {
          show: true,
          position: 'top',
          formatter: (params: any) => {
            const item = priorityViolationsData.find(d => d.name === params.name)
            return `${params.value} (${item?.rate.toFixed(1)}%)`
          }
        }
      }]
    }

    // 服务类别违规分布
    const categoryViolationsData = Object.entries(data.violationsByCategory).map(([category, info]) => ({
      value: info.violations,
      name: getCategoryText(category),
      rate: info.rate,
      total: info.total
    }))

    const categoryOption = {
      title: {
        text: '服务类别违规分布',
        left: 'center',
        textStyle: { fontSize: 16, fontWeight: 'bold' }
      },
      tooltip: {
        trigger: 'item',
        formatter: (params: any) => {
          const item = categoryViolationsData.find(d => d.name === params.name)
          return `${params.name}<br/>违规数: ${params.value}<br/>违规率: ${item?.rate.toFixed(1)}%<br/>总数: ${item?.total}`
        }
      },
      legend: {
        orient: 'vertical',
        left: 'left',
        top: 'middle'
      },
      series: [{
        name: '违规分布',
        type: 'pie',
        radius: ['30%', '70%'],
        center: ['60%', '50%'],
        data: categoryViolationsData,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }]
    }

    return { trendOption, priorityOption, categoryOption }
  }, [data])

  // ========== 图表初始化 ==========
  useLayoutEffect(() => {
    if (!chartOptions || loading) return

    const initChart = (ref: React.RefObject<HTMLDivElement>, option: any) => {
      if (ref.current && option) {
        try {
          const chart = echarts.init(ref.current)
          
          // 验证数据完整性
          if (option.series && Array.isArray(option.series)) {
            option.series.forEach((series: any) => {
              if (series.data && !Array.isArray(series.data)) {
                series.data = []
              }
            })
          }
          
          chart.setOption(option)
          
          // 强制重新计算尺寸
          setTimeout(() => {
            chart.resize()
          }, 50)
          
          const handleResize = () => chart.resize()
          window.addEventListener('resize', handleResize)
          
          return () => {
            window.removeEventListener('resize', handleResize)
            chart.dispose()
          }
        } catch (error) {
          console.error('图表初始化失败:', error)
          return () => {}
        }
      }
      return () => {}
    }

    const cleanup1 = initChart(complianceTrendRef, chartOptions.trendOption)
    const cleanup2 = initChart(violationsPriorityRef, chartOptions.priorityOption)
    const cleanup3 = initChart(violationsCategoryRef, chartOptions.categoryOption)

    return () => {
      cleanup1?.()
      cleanup2?.()
      cleanup3?.()
    }
  }, [chartOptions, loading])

  // ========== 加载和错误状态 ==========
  if (error) {
    return (
      <Card>
        <div className="text-center py-8">
          <Text type="danger">获取SLA合规性数据失败</Text>
        </div>
      </Card>
    )
  }

  if (loading) {
    return (
      <Card>
        <div className="text-center py-8">
          <Spin size="large" />
        </div>
      </Card>
    )
  }

  if (!data) {
    return (
      <Card>
        <Empty description="暂无SLA合规性数据" />
      </Card>
    )
  }

  // ========== 主要渲染 ==========
  return (
    <div className="space-y-6">
      {/* SLA合规性告警 */}
      {data.complianceRate < 95 && (
        <Banner
          type="warning"
          description={`SLA合规性警告: 当前SLA合规率为 ${data.complianceRate.toFixed(1)}%，低于95%目标，请关注服务质量改进`}
          icon={<IconAlertTriangle />}
        />
      )}

      {/* 关键指标 */}
      <Row gutter={16}>
        <Col span={6}>
          <Card>
            <div className="text-center">
              <div className="mb-2">
                <IconShield style={{ fontSize: '24px', color: data.complianceRate >= 95 ? '#52c41a' : data.complianceRate >= 85 ? '#faad14' : '#ff4d4f' }} />
              </div>
              <div className="text-2xl font-bold mb-1" style={{ color: data.complianceRate >= 95 ? '#52c41a' : data.complianceRate >= 85 ? '#faad14' : '#ff4d4f' }}>
                {data.complianceRate.toFixed(1)}%
              </div>
              <div className="text-gray-600 text-sm">总体合规率</div>
            </div>
            <div className="mt-2">
              <Text size="small" type="secondary">
                {data.totalServices - data.slaViolations}/{data.totalServices} 合规
              </Text>
            </div>
          </Card>
        </Col>
        
        <Col span={6}>
          <Card>
            <div className="text-center">
              <div className="mb-2">
                <IconActivity style={{ fontSize: '24px', color: data.responseTimeCompliance.rate >= 95 ? '#52c41a' : '#faad14' }} />
              </div>
              <div className="text-2xl font-bold mb-1" style={{ color: data.responseTimeCompliance.rate >= 95 ? '#52c41a' : '#faad14' }}>
                {data.responseTimeCompliance.rate.toFixed(1)}%
              </div>
              <div className="text-gray-600 text-sm">响应时间合规</div>
            </div>
            <div className="mt-2">
              <Text size="small" type="secondary">
                平均: {data.responseTimeCompliance.avgResponseTime.toFixed(1)}分钟
              </Text>
            </div>
          </Card>
        </Col>
        
        <Col span={6}>
          <Card>
            <div className="text-center">
              <div className="mb-2">
                <IconActivity style={{ fontSize: '24px', color: data.resolutionTimeCompliance.rate >= 95 ? '#52c41a' : '#faad14' }} />
              </div>
              <div className="text-2xl font-bold mb-1" style={{ color: data.resolutionTimeCompliance.rate >= 95 ? '#52c41a' : '#faad14' }}>
                {data.resolutionTimeCompliance.rate.toFixed(1)}%
              </div>
              <div className="text-gray-600 text-sm">解决时间合规</div>
            </div>
            <div className="mt-2">
              <Text size="small" type="secondary">
                平均: {data.resolutionTimeCompliance.avgResolutionTime.toFixed(1)}小时
              </Text>
            </div>
          </Card>
        </Col>
        
        <Col span={6}>
          <Card>
            <div className="text-center">
              <div className="mb-2">
                <IconStar style={{ fontSize: '24px', color: '#ff4d4f' }} />
              </div>
              <div className="text-2xl font-bold mb-1" style={{ color: '#ff4d4f' }}>
                {data.costImpact.totalViolationCost}元
              </div>
              <div className="text-gray-600 text-sm">违规成本</div>
            </div>
            <div className="mt-2">
              <Text size="small" type="secondary">
                可节省: {data.costImpact.potentialSavings}元
              </Text>
            </div>
          </Card>
        </Col>
      </Row>

      {/* 趋势和分布图表 */}
      <Row gutter={16}>
        <Col span={24}>
          <Card>
            <div ref={complianceTrendRef} style={{ width: '100%', height: '400px' }} />
          </Card>
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={12}>
          <Card>
            <div ref={violationsPriorityRef} style={{ width: '100%', height: '350px' }} />
          </Card>
        </Col>
        
        <Col span={12}>
          <Card>
            <div ref={violationsCategoryRef} style={{ width: '100%', height: '350px' }} />
          </Card>
        </Col>
      </Row>

      {/* SLA详细分析 */}
      <Row gutter={16}>
        <Col span={12}>
          <Card title="响应时间详细分析" headerExtraContent={<IconActivity />}>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <Text>合规服务数</Text>
                <Text strong>{data.responseTimeCompliance.compliant}</Text>
              </div>
              
              <div className="flex justify-between items-center">
                <Text>违规服务数</Text>
                <Text type="danger">
                  {data.responseTimeCompliance.total - data.responseTimeCompliance.compliant}
                </Text>
              </div>
              
              <Progress 
                percent={data.responseTimeCompliance.rate}
                stroke={data.responseTimeCompliance.rate >= 95 ? '#52c41a' : '#faad14'}
                format={percent => `${percent?.toFixed(1)}%`}
              />
              
              <div className="bg-gray-50 p-3 rounded">
                <Text size="small" type="secondary">
                  目标: 各优先级响应时间要求内完成首次响应
                </Text>
              </div>
            </div>
          </Card>
        </Col>
        
        <Col span={12}>
          <Card title="解决时间详细分析" headerExtraContent={<IconActivity />}>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <Text>合规服务数</Text>
                <Text strong>{data.resolutionTimeCompliance.compliant}</Text>
              </div>
              
              <div className="flex justify-between items-center">
                <Text>违规服务数</Text>
                <Text type="danger">
                  {data.resolutionTimeCompliance.total - data.resolutionTimeCompliance.compliant}
                </Text>
              </div>
              
              <Progress 
                percent={data.resolutionTimeCompliance.rate}
                stroke={data.resolutionTimeCompliance.rate >= 95 ? '#52c41a' : '#faad14'}
                format={percent => `${percent?.toFixed(1)}%`}
              />
              
              <div className="bg-gray-50 p-3 rounded">
                <Text size="small" type="secondary">
                  目标: 各优先级解决时间要求内完成问题解决
                </Text>
              </div>
            </div>
          </Card>
        </Col>
      </Row>

      {/* 成本影响分析 */}
      {data.costImpact.totalViolationCost > 0 && (
        <Card title="SLA违规成本影响分析" headerExtraContent={<IconStar />}>
          <Row gutter={[16, 16]}>
            <Col span={8}>
              <div className="text-center p-4 bg-red-50 rounded">
                <div className="text-2xl font-bold text-red-600 mb-2">
                  ¥{data.costImpact.totalViolationCost.toLocaleString()}
                </div>
                <div className="text-gray-600">总违规成本</div>
              </div>
            </Col>
            
            <Col span={8}>
              <div className="text-center p-4 bg-orange-50 rounded">
                <div className="text-2xl font-bold text-orange-600 mb-2">
                  ¥{data.costImpact.avgCostPerViolation.toLocaleString()}
                </div>
                <div className="text-gray-600">单次违规成本</div>
              </div>
            </Col>
            
            <Col span={8}>
              <div className="text-center p-4 bg-green-50 rounded">
                <div className="text-2xl font-bold text-green-600 mb-2">
                  ¥{data.costImpact.potentialSavings.toLocaleString()}
                </div>
                <div className="text-gray-600">潜在节省</div>
              </div>
            </Col>
          </Row>
          
          <div className="mt-4 p-4 bg-blue-50 rounded">
            <Text type="secondary">
              💡 建议: 通过改进响应速度和解决效率，预计可节省 
              <Text strong type="primary">
                ¥{data.costImpact.potentialSavings.toLocaleString()}
              </Text> 
              的违规成本
            </Text>
          </div>
        </Card>
      )}
    </div>
  )
}

// ========== 工具函数 ==========

const getPriorityText = (priority: string): string => {
  const priorityMap: Record<string, string> = {
    'LOW': '低',
    'MEDIUM': '中',
    'HIGH': '高',
    'URGENT': '紧急'
  }
  return priorityMap[priority] || priority
}

const getCategoryText = (category: string): string => {
  const categoryMap: Record<string, string> = {
    'MAINTENANCE': '运维',
    'SUPPORT': '支持',
    'UPGRADE': '升级',
    'BUGFIX': '故障修复',
    'CONSULTING': '咨询',
    'MONITORING': '监控'
  }
  return categoryMap[category] || category
}

const getPriorityColor = (priority: string): string => {
  const colorMap: Record<string, string> = {
    'LOW': '#52c41a',
    'MEDIUM': '#faad14',
    'HIGH': '#ff7a45',
    'URGENT': '#ff4d4f'
  }
  return colorMap[priority] || '#1890ff'
}