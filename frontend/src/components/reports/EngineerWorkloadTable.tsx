import React, { useMemo } from 'react'
import { Table, Typography, Tag, Progress, Card, Empty, Spin } from '@douyinfe/semi-ui'
import { IconUser, IconActivity, IconClock } from '@douyinfe/semi-icons'
import type { OperationEfficiencyMetrics } from '@/services/report.service'

const { Text } = Typography

interface EngineerWorkloadTableProps {
  data: OperationEfficiencyMetrics['engineerWorkload']
  loading?: boolean
}

export const EngineerWorkloadTable: React.FC<EngineerWorkloadTableProps> = ({ data, loading }) => {
  // ========== 表格配置 ==========
  const columns = useMemo(() => [
    {
      title: '工程师',
      dataIndex: 'userName',
      key: 'userName',
      render: (text: string, record: any) => (
        <div className="flex items-center space-x-2">
          <div className="w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-bold">
            {text.charAt(0)}
          </div>
          <div>
            <div className="font-medium">{text}</div>
            <div className="text-xs text-gray-500">ID: {record.userId.slice(-6)}</div>
          </div>
        </div>
      ),
      width: 150,
      fixed: 'left' as const
    },
    {
      title: '分配工单',
      dataIndex: 'assignedCount',
      key: 'assignedCount',
      render: (count: number) => (
        <div className="text-center">
          <div className="text-lg font-bold text-blue-600">{count}</div>
          <div className="text-xs text-gray-500">个</div>
        </div>
      ),
      sorter: (a: any, b: any) => a.assignedCount - b.assignedCount,
      width: 100
    },
    {
      title: '完成工单',
      dataIndex: 'completedCount',
      key: 'completedCount',
      render: (count: number) => (
        <div className="text-center">
          <div className="text-lg font-bold text-green-600">{count}</div>
          <div className="text-xs text-gray-500">个</div>
        </div>
      ),
      sorter: (a: any, b: any) => a.completedCount - b.completedCount,
      width: 100
    },
    {
      title: '完成效率',
      dataIndex: 'efficiency',
      key: 'efficiency',
      render: (efficiency: number, record: any) => (
        <div className="space-y-2">
          <div className="flex justify-between items-center">
            <Text size="small">{efficiency.toFixed(1)}%</Text>
            <Tag color={getEfficiencyColor(efficiency) as any}>
              {getEfficiencyLevel(efficiency)}
            </Tag>
          </div>
          <Progress 
            percent={efficiency} 
            showInfo={false}
            size="small"
            stroke={getEfficiencyColor(efficiency)}
          />
        </div>
      ),
      sorter: (a: any, b: any) => a.efficiency - b.efficiency,
      defaultSortOrder: 'descend' as const,
      width: 150
    },
    {
      title: '总工时',
      dataIndex: 'totalWorkHours',
      key: 'totalWorkHours',
      render: (hours: number) => (
        <div className="text-center">
          <div className="text-lg font-bold text-purple-600">{hours.toFixed(1)}</div>
          <div className="text-xs text-gray-500">小时</div>
        </div>
      ),
      sorter: (a: any, b: any) => a.totalWorkHours - b.totalWorkHours,
      width: 100
    },
    {
      title: '平均解决时间',
      dataIndex: 'avgResolutionTime',
      key: 'avgResolutionTime',
      render: (time: number) => (
        <div className="text-center">
          <div className="text-lg font-bold text-orange-600">{time.toFixed(1)}</div>
          <div className="text-xs text-gray-500">小时</div>
          <Tag size="small" color={getResolutionTimeColor(time) as any} className="mt-1">
            {getResolutionTimeLevel(time)}
          </Tag>
        </div>
      ),
      sorter: (a: any, b: any) => a.avgResolutionTime - b.avgResolutionTime,
      width: 130
    },
    {
      title: '工作负载',
      key: 'workload',
      render: (record: any) => {
        const workloadScore = calculateWorkloadScore(record)
        return (
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <Text size="small">负载指数</Text>
              <Text size="small" type={workloadScore > 80 ? 'danger' : workloadScore > 60 ? 'warning' : 'secondary'}>
                {workloadScore.toFixed(0)}
              </Text>
            </div>
            <Progress 
              percent={Math.min(workloadScore, 100)} 
              showInfo={false}
              size="small"
              stroke={workloadScore > 80 ? '#ff4d4f' : workloadScore > 60 ? '#faad14' : '#52c41a'}
            />
            <Tag size="small" color={getWorkloadColor(workloadScore) as any}>
              {getWorkloadLevel(workloadScore)}
            </Tag>
          </div>
        )
      },
      sorter: (a: any, b: any) => calculateWorkloadScore(a) - calculateWorkloadScore(b),
      width: 120
    }
  ], [])

  // ========== 数据统计 ==========
  const stats = useMemo(() => {
    if (!data || data.length === 0) return null

    const totalAssigned = data.reduce((sum, engineer) => sum + engineer.assignedCount, 0)
    const totalCompleted = data.reduce((sum, engineer) => sum + engineer.completedCount, 0)
    const totalWorkHours = data.reduce((sum, engineer) => sum + engineer.totalWorkHours, 0)
    const avgEfficiency = data.reduce((sum, engineer) => sum + engineer.efficiency, 0) / data.length

    return {
      totalEngineers: data.length,
      totalAssigned,
      totalCompleted,
      totalWorkHours,
      avgEfficiency,
      completionRate: totalAssigned > 0 ? (totalCompleted / totalAssigned) * 100 : 0
    }
  }, [data])

  // ========== 加载状态 ==========
  if (loading) {
    return (
      <Card title="工程师工作负载详情" headerExtraContent={<IconUser />}>
        <div className="text-center py-8">
          <Spin size="large" />
        </div>
      </Card>
    )
  }

  if (!data || data.length === 0) {
    return (
      <Card title="工程师工作负载详情" headerExtraContent={<IconUser />}>
        <Empty description="暂无工程师工作负载数据" />
      </Card>
    )
  }

  // ========== 主要渲染 ==========
  return (
    <Card title="工程师工作负载详情" headerExtraContent={<IconUser />}>
      {/* 汇总统计 */}
      {stats && (
        <div className="mb-6 p-4 bg-blue-50 rounded-lg">
          <div className="grid grid-cols-6 gap-4 text-center">
            <div>
              <div className="text-lg font-bold text-blue-600">{stats.totalEngineers}</div>
              <div className="text-xs text-gray-500">活跃工程师</div>
            </div>
            <div>
              <div className="text-lg font-bold text-purple-600">{stats.totalAssigned}</div>
              <div className="text-xs text-gray-500">总分配工单</div>
            </div>
            <div>
              <div className="text-lg font-bold text-green-600">{stats.totalCompleted}</div>
              <div className="text-xs text-gray-500">总完成工单</div>
            </div>
            <div>
              <div className="text-lg font-bold text-orange-600">{stats.totalWorkHours.toFixed(1)}</div>
              <div className="text-xs text-gray-500">总工时(小时)</div>
            </div>
            <div>
              <div className="text-lg font-bold text-yellow-600">{stats.avgEfficiency.toFixed(1)}%</div>
              <div className="text-xs text-gray-500">平均效率</div>
            </div>
            <div>
              <div className="text-lg font-bold text-cyan-600">{stats.completionRate.toFixed(1)}%</div>
              <div className="text-xs text-gray-500">整体完成率</div>
            </div>
          </div>
        </div>
      )}

      {/* 工程师详细表格 */}
      <Table
        dataSource={data}
        columns={columns}
        rowKey="userId"
        pagination={{
          pageSize: 10,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total: number, range: number[]) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
        } as any}
        scroll={{ x: 900 }}
        size="small"
      />
    </Card>
  )
}

// ========== 工具函数 ==========

const getEfficiencyColor = (efficiency: number): string => {
  if (efficiency >= 90) return 'green'
  if (efficiency >= 80) return 'blue'
  if (efficiency >= 70) return 'orange'
  if (efficiency >= 60) return 'yellow'
  return 'red'
}

const getEfficiencyLevel = (efficiency: number): string => {
  if (efficiency >= 90) return '优秀'
  if (efficiency >= 80) return '良好'
  if (efficiency >= 70) return '中等'
  if (efficiency >= 60) return '一般'
  return '待提升'
}

const getResolutionTimeColor = (time: number): string => {
  if (time <= 4) return 'green'
  if (time <= 12) return 'blue'
  if (time <= 24) return 'orange'
  if (time <= 48) return 'yellow'
  return 'red'
}

const getResolutionTimeLevel = (time: number): string => {
  if (time <= 4) return '快速'
  if (time <= 12) return '正常'
  if (time <= 24) return '中等'
  if (time <= 48) return '偏慢'
  return '过慢'
}

const calculateWorkloadScore = (engineer: any): number => {
  // 综合负载指数 = 分配数量(40%) + 工时比重(30%) + 效率倒数(30%)
  const assignedWeight = (engineer.assignedCount / 20) * 40 // 假设20个工单为满负载
  const hoursWeight = (engineer.totalWorkHours / 160) * 30 // 假设160小时/月为满工时
  const efficiencyWeight = (100 - engineer.efficiency) * 0.3 // 效率越低，负载越高
  
  return Math.min(assignedWeight + hoursWeight + efficiencyWeight, 100)
}

const getWorkloadColor = (workload: number): string => {
  if (workload > 80) return 'red'
  if (workload > 60) return 'orange'
  if (workload > 40) return 'blue'
  return 'green'
}

const getWorkloadLevel = (workload: number): string => {
  if (workload > 80) return '超负荷'
  if (workload > 60) return '繁忙'
  if (workload > 40) return '正常'
  return '轻松'
}