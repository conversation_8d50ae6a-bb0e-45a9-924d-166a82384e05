import React, { useMemo, useRef, useEffect, useLayoutEffect } from 'react'
import { Card, Typography, Spin, Empty, Row, Col, Progress, Tag, Rating } from '@douyinfe/semi-ui'
import { IconStar, IconActivity, IconUser, IconComment } from '@douyinfe/semi-icons'
import * as echarts from 'echarts'
import type { CustomerSatisfactionMetrics } from '@/services/report.service'

const { Title, Text } = Typography

interface SatisfactionChartProps {
  data?: CustomerSatisfactionMetrics
  loading?: boolean
  error?: any
}

export const SatisfactionChart: React.FC<SatisfactionChartProps> = ({ data, loading, error }) => {
  const trendChartRef = useRef<HTMLDivElement>(null)
  const ratingChartRef = useRef<HTMLDivElement>(null)
  const categoryChartRef = useRef<HTMLDivElement>(null)

  // ========== 图表配置 ==========
  const chartOptions = useMemo(() => {
    if (!data) return null

    // 确保数据存在且为数组/对象
    const customerSatisfactionTrend = data.customerSatisfactionTrend || []
    const ratingDistribution = data.ratingDistribution || {}
    const satisfactionByCategory = data.satisfactionByCategory || {}
    const topCustomers = data.topCustomers || []

    // 满意度趋势线图
    const trendOption = {
      title: {
        text: '客户满意度趋势',
        left: 'center',
        textStyle: { fontSize: 16, fontWeight: 'bold' }
      },
      tooltip: {
        trigger: 'axis',
        formatter: (params: any) => {
          const item = params[0]
          const trendItem = customerSatisfactionTrend.find(t => t.date === item.axisValue)
          return `${item.axisValue}<br/>满意度: ${item.value}<br/>反馈数: ${trendItem?.count || 0}`
        }
      },
      xAxis: {
        type: 'category',
        data: customerSatisfactionTrend.map(t => t.date),
        axisLabel: {
          formatter: (value: string) => {
            const date = new Date(value)
            return `${date.getMonth() + 1}/${date.getDate()}`
          }
        }
      },
      yAxis: {
        type: 'value',
        min: 1,
        max: 5,
        name: '满意度评分',
        axisLabel: {
          formatter: '{value}星'
        }
      },
      series: [{
        name: '满意度',
        type: 'line',
        data: customerSatisfactionTrend.map(t => t.rating || 0),
        smooth: true,
        symbol: 'circle',
        symbolSize: 6,
        lineStyle: {
          width: 3,
          color: '#faad14'
        },
        itemStyle: {
          color: '#faad14'
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [{
              offset: 0, color: 'rgba(250, 173, 20, 0.3)'
            }, {
              offset: 1, color: 'rgba(250, 173, 20, 0.1)'
            }]
          }
        }
      }]
    }

    // 评分分布柱状图
    const ratingOption = {
      title: {
        text: '评分分布',
        left: 'center',
        textStyle: { fontSize: 16, fontWeight: 'bold' }
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: { type: 'shadow' }
      },
      xAxis: {
        type: 'category',
        data: ['1星', '2星', '3星', '4星', '5星']
      },
      yAxis: {
        type: 'value',
        name: '反馈数量'
      },
      series: [{
        name: '反馈数量',
        type: 'bar',
        data: [1, 2, 3, 4, 5].map(rating => ({
          value: ratingDistribution[rating] || 0,
          itemStyle: {
            color: getRatingColor(rating)
          }
        })),
        emphasis: {
          focus: 'series'
        },
        label: {
          show: true,
          position: 'top'
        }
      }]
    }

    // 分类满意度雷达图
    const categoryData = Object.entries(satisfactionByCategory).map(([category, info]) => ({
      name: getCategoryText(category),
      value: info.avgRating,
      count: info.count
    }))

    const categoryOption = {
      title: {
        text: '分类满意度分析',
        left: 'center',
        textStyle: { fontSize: 16, fontWeight: 'bold' }
      },
      tooltip: {
        trigger: 'item',
        formatter: (params: any) => {
          const item = categoryData.find(d => d.name === params.name)
          return `${params.name}<br/>满意度: ${params.value}<br/>服务数: ${item?.count || 0}`
        }
      },
      radar: {
        indicator: categoryData.map(item => ({
          name: item.name,
          max: 5,
          min: 1
        })),
        center: ['50%', '50%'],
        radius: '70%'
      },
      series: [{
        name: '分类满意度',
        type: 'radar',
        data: [{
          value: categoryData.map(item => item.value),
          name: '满意度评分',
          itemStyle: {
            color: '#faad14'
          },
          areaStyle: {
            color: 'rgba(250, 173, 20, 0.3)'
          }
        }]
      }]
    }

    return { trendOption, ratingOption, categoryOption }
  }, [data])

  // ========== 图表初始化 ==========
  useLayoutEffect(() => {
    if (!chartOptions || loading) return

    const initChart = (ref: React.RefObject<HTMLDivElement>, option: any) => {
      if (ref.current && option) {
        try {
          const chart = echarts.init(ref.current)
          
          // 验证数据完整性
          if (option.series && Array.isArray(option.series)) {
            option.series.forEach((series: any) => {
              if (series.data && !Array.isArray(series.data)) {
                series.data = []
              }
            })
          }
          
          chart.setOption(option)
          
          // 强制重新计算尺寸
          setTimeout(() => {
            chart.resize()
          }, 50)
          
          const handleResize = () => chart.resize()
          window.addEventListener('resize', handleResize)
          
          return () => {
            window.removeEventListener('resize', handleResize)
            chart.dispose()
          }
        } catch (error) {
          console.error('图表初始化失败:', error)
          return () => {}
        }
      }
      return () => {}
    }

    const cleanup1 = initChart(trendChartRef, chartOptions.trendOption)
    const cleanup2 = initChart(ratingChartRef, chartOptions.ratingOption)
    const cleanup3 = initChart(categoryChartRef, chartOptions.categoryOption)

    return () => {
      cleanup1?.()
      cleanup2?.()
      cleanup3?.()
    }
  }, [chartOptions, loading])

  // ========== 加载和错误状态 ==========
  if (error) {
    return (
      <Card>
        <div className="text-center py-8">
          <Text type="danger">获取客户满意度数据失败</Text>
        </div>
      </Card>
    )
  }

  if (loading) {
    return (
      <Card>
        <div className="text-center py-8">
          <Spin size="large" />
        </div>
      </Card>
    )
  }

  if (!data) {
    return (
      <Card>
        <Empty description="暂无客户满意度数据" />
      </Card>
    )
  }

  // ========== 主要渲染 ==========
  return (
    <div className="space-y-6">
      {/* 满意度概览 */}
      <Row gutter={16}>
        <Col span={8}>
          <Card className="text-center">
            <div className="mb-4">
              <Rating 
                value={data.averageRating || 0} 
                disabled 
                allowHalf
              />
            </div>
            <div className="text-3xl font-bold text-yellow-600 mb-2">
              {(data.averageRating || 0).toFixed(1)}
            </div>
            <div className="text-gray-600">平均满意度</div>
            <div className="text-sm text-gray-500 mt-2">
              基于 {data.totalFeedbacks || 0} 条反馈
            </div>
          </Card>
        </Col>
        
        <Col span={8}>
          <Card className="text-center">
            <div className="text-2xl font-bold text-blue-600 mb-2">
              {data.totalFeedbacks || 0}
            </div>
            <div className="text-gray-600">总反馈数</div>
            <div className="mt-3">
              <div className="grid grid-cols-5 gap-1">
                {[5, 4, 3, 2, 1].map(rating => (
                  <div key={rating} className="text-center">
                    <div className="text-xs text-gray-500">{rating}★</div>
                    <div className="text-xs font-medium">
                      {data.ratingDistribution?.[rating] || 0}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </Card>
        </Col>
        
        <Col span={8}>
          <Card className="text-center">
            <div className="text-2xl font-bold text-red-600 mb-2">
              {data.complaintsAnalysis.totalComplaints}
            </div>
            <div className="text-gray-600">投诉总数</div>
            <div className="mt-2">
              <Progress 
                percent={data.complaintsAnalysis.totalComplaints > 0 
                  ? (data.complaintsAnalysis.resolvedComplaints / data.complaintsAnalysis.totalComplaints) * 100 
                  : 0}
                showInfo={false}
                size="small"
                stroke="#52c41a"
              />
              <Text size="small" type="secondary">
                已解决: {data.complaintsAnalysis.resolvedComplaints}
              </Text>
            </div>
          </Card>
        </Col>
      </Row>

      {/* 图表分析 */}
      <Row gutter={16}>
        <Col span={12}>
          <Card>
            <div ref={trendChartRef} style={{ width: '100%', height: '350px' }} />
          </Card>
        </Col>
        
        <Col span={12}>
          <Card>
            <div ref={ratingChartRef} style={{ width: '100%', height: '350px' }} />
          </Card>
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={12}>
          <Card>
            <div ref={categoryChartRef} style={{ width: '100%', height: '350px' }} />
          </Card>
        </Col>
        
        <Col span={12}>
          <Card title="优质客户榜单" headerExtraContent={<IconUser />}>
            <div className="space-y-4">
              {data.topCustomers.slice(0, 5).map((customer, index) => (
                <div key={customer.customerId} className="flex items-center justify-between p-3 bg-gray-50 rounded">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center font-bold">
                      {index + 1}
                    </div>
                    <div>
                      <div className="font-medium">{customer.customerName}</div>
                      <div className="text-sm text-gray-500">
                        {customer.totalServices} 次服务
                      </div>
                    </div>
                  </div>
                  <div className="text-right">
                    <Rating value={customer.avgRating} disabled size="small" />
                    <div className="text-sm text-gray-500">
                      {customer.avgRating.toFixed(1)}分
                    </div>
                  </div>
                </div>
              ))}
              
              {data.topCustomers.length === 0 && (
                <Empty description="暂无客户满意度数据" />
              )}
            </div>
          </Card>
        </Col>
      </Row>

      {/* 投诉分析 */}
      {data.complaintsAnalysis.totalComplaints > 0 && (
        <Card title="投诉分析" headerExtraContent={<IconComment />}>
          <Row gutter={16}>
            <Col span={12}>
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <Text strong>投诉解决率</Text>
                  <Text type="success">
                    {data.complaintsAnalysis.totalComplaints > 0 
                      ? ((data.complaintsAnalysis.resolvedComplaints / data.complaintsAnalysis.totalComplaints) * 100).toFixed(1)
                      : 0}%
                  </Text>
                </div>
                <Progress 
                  percent={data.complaintsAnalysis.totalComplaints > 0 
                    ? (data.complaintsAnalysis.resolvedComplaints / data.complaintsAnalysis.totalComplaints) * 100 
                    : 0}
                  stroke="#52c41a"
                />
                
                <div className="flex justify-between items-center mt-4">
                  <Text strong>平均解决时间</Text>
                  <Text>{data.complaintsAnalysis.avgResolutionTime.toFixed(1)} 小时</Text>
                </div>
              </div>
            </Col>
            
            <Col span={12}>
              <div>
                <Text strong className="block mb-3">常见投诉问题</Text>
                <div className="space-y-2">
                  {data.complaintsAnalysis.commonIssues.slice(0, 5).map((issue, index) => (
                    <div key={index} className="flex justify-between items-center">
                      <Text className="flex-1">{issue.issue}</Text>
                      <div className="flex items-center space-x-2">
                        <Tag>{issue.count}次</Tag>
                        <Text size="small" type="secondary">
                          {issue.percentage.toFixed(1)}%
                        </Text>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </Col>
          </Row>
        </Card>
      )}
    </div>
  )
}

// ========== 工具函数 ==========

const getCategoryText = (category: string): string => {
  const categoryMap: Record<string, string> = {
    'MAINTENANCE': '运维',
    'SUPPORT': '支持',
    'UPGRADE': '升级',
    'BUGFIX': '故障修复',
    'CONSULTING': '咨询',
    'MONITORING': '监控'
  }
  return categoryMap[category] || category
}

const getRatingColor = (rating: number): string => {
  const colorMap: Record<number, string> = {
    1: '#ff4d4f',
    2: '#ff7a45',
    3: '#faad14',
    4: '#52c41a',
    5: '#1890ff'
  }
  return colorMap[rating] || '#1890ff'
}