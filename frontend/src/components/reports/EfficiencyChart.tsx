import React, { useMemo, useRef, useEffect, useLayoutEffect } from 'react'
import { Card, Typography, Spin, Empty, Row, Col, Progress, Tag } from '@douyinfe/semi-ui'
import { IconActivity, IconClock, IconTickCircle, IconUser } from '@douyinfe/semi-icons'
import * as echarts from 'echarts'
import type { OperationEfficiencyMetrics } from '@/services/report.service'

const { Title, Text } = Typography

interface EfficiencyChartProps {
  data?: OperationEfficiencyMetrics
  loading?: boolean
  error?: any
}

export const EfficiencyChart: React.FC<EfficiencyChartProps> = ({ data, loading, error }) => {
  const categoryChartRef = useRef<HTMLDivElement>(null)
  const priorityChartRef = useRef<HTMLDivElement>(null)
  const statusChartRef = useRef<HTMLDivElement>(null)

  // ========== 图表配置 ==========
  const chartOptions = useMemo(() => {
    if (!data) return null

    // 确保数据存在且为对象
    const categoryDistribution = data.categoryDistribution || {}
    const priorityDistribution = data.priorityDistribution || {}
    const statusDistribution = data.statusDistribution || {}

    // 服务类别分布饼图
    const categoryOption = {
      title: {
        text: '服务类别分布',
        left: 'center',
        textStyle: { fontSize: 16, fontWeight: 'bold' }
      },
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      },
      legend: {
        orient: 'vertical',
        left: 'left',
        top: 'middle'
      },
      series: [{
        name: '服务类别',
        type: 'pie',
        radius: ['30%', '70%'],
        center: ['60%', '50%'],
        data: Object.entries(categoryDistribution)
          .filter(([_, value]) => value !== undefined && value !== null)
          .map(([key, value]) => ({
            value: Number(value) || 0,
            name: getCategoryText(key)
          })),
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }]
    }

    // 优先级分布柱状图
    const priorityOption = {
      title: {
        text: '优先级分布',
        left: 'center',
        textStyle: { fontSize: 16, fontWeight: 'bold' }
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: { type: 'shadow' }
      },
      xAxis: {
        type: 'category',
        data: Object.keys(priorityDistribution)
          .filter(key => priorityDistribution[key] !== undefined && priorityDistribution[key] !== null)
          .map(getPriorityText)
      },
      yAxis: {
        type: 'value',
        name: '工单数量'
      },
      series: [{
        name: '工单数量',
        type: 'bar',
        data: Object.entries(priorityDistribution)
          .filter(([_, value]) => value !== undefined && value !== null)
          .map(([key, value]) => ({
            value: Number(value) || 0,
            itemStyle: {
              color: getPriorityColor(key)
            }
          })),
        emphasis: {
          focus: 'series'
        }
      }]
    }

    // 状态分布环形图
    const statusOption = {
      title: {
        text: '状态分布',
        left: 'center',
        textStyle: { fontSize: 16, fontWeight: 'bold' }
      },
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      },
      legend: {
        orient: 'horizontal',
        bottom: 0
      },
      series: [{
        name: '工单状态',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['50%', '40%'],
        data: Object.entries(statusDistribution)
          .filter(([_, value]) => value !== undefined && value !== null)
          .map(([key, value]) => ({
            value: Number(value) || 0,
            name: getStatusText(key),
            itemStyle: {
              color: getStatusColor(key)
            }
          })),
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        },
        label: {
          show: true,
          formatter: '{b}: {c}',
          fontSize: 12
        }
      }]
    }

    return { categoryOption, priorityOption, statusOption }
  }, [data])

  // ========== 图表初始化 ==========
  useLayoutEffect(() => {
    if (!chartOptions || loading) return

    const initChart = (ref: React.RefObject<HTMLDivElement>, option: any) => {
      if (ref.current && option) {
        try {
          const chart = echarts.init(ref.current)
          
          // 验证数据完整性
          if (option.series && Array.isArray(option.series)) {
            option.series.forEach((series: any) => {
              if (series.data && !Array.isArray(series.data)) {
                series.data = []
              }
            })
          }
          
          chart.setOption(option)
          
          // 强制重新计算尺寸
          setTimeout(() => {
            chart.resize()
          }, 50)
          
          // 响应式调整
          const handleResize = () => chart.resize()
          window.addEventListener('resize', handleResize)
          
          return () => {
            window.removeEventListener('resize', handleResize)
            chart.dispose()
          }
        } catch (error) {
          console.error('图表初始化失败:', error)
          return () => {}
        }
      }
      return () => {}
    }

    const cleanup1 = initChart(categoryChartRef, chartOptions.categoryOption)
    const cleanup2 = initChart(priorityChartRef, chartOptions.priorityOption)
    const cleanup3 = initChart(statusChartRef, chartOptions.statusOption)

    return () => {
      cleanup1?.()
      cleanup2?.()
      cleanup3?.()
    }
  }, [chartOptions, loading])

  // ========== 加载和错误状态 ==========
  if (error) {
    return (
      <Card>
        <div className="text-center py-8">
          <Text type="danger">获取运维效率数据失败</Text>
        </div>
      </Card>
    )
  }

  if (loading) {
    return (
      <Card>
        <div className="text-center py-8">
          <Spin size="large" />
        </div>
      </Card>
    )
  }

  if (!data) {
    return (
      <Card>
        <Empty description="暂无运维效率数据" />
      </Card>
    )
  }

  // ========== 主要渲染 ==========
  return (
    <div className="space-y-6">
      {/* 关键指标卡片 */}
      <Row gutter={16}>
        <Col span={6}>
          <Card className="text-center">
            <div className="text-2xl font-bold text-blue-600 mb-2">
              {data.totalServices || 0}
            </div>
            <div className="text-gray-600">总工单数</div>
            <div className="mt-2">
              <Progress 
                percent={data.totalServices ? ((data.completedServices || 0) / data.totalServices) * 100 : 0}
                showInfo={false}
                size="small"
                stroke="#52c41a"
              />
              <Text size="small" type="secondary">
                已完成: {data.completedServices || 0}
              </Text>
            </div>
          </Card>
        </Col>
        
        <Col span={6}>
          <Card className="text-center">
            <div className="text-2xl font-bold text-green-600 mb-2">
              {(data.avgResponseTimeMinutes || 0).toFixed(1)}
            </div>
            <div className="text-gray-600">平均响应时间(分钟)</div>
            <div className="mt-2">
              <Tag color={(data.avgResponseTimeMinutes || 0) <= 60 ? 'green' : (data.avgResponseTimeMinutes || 0) <= 240 ? 'orange' : 'red'}>
                {(data.avgResponseTimeMinutes || 0) <= 60 ? '优秀' : (data.avgResponseTimeMinutes || 0) <= 240 ? '良好' : '需改进'}
              </Tag>
            </div>
          </Card>
        </Col>
        
        <Col span={6}>
          <Card className="text-center">
            <div className="text-2xl font-bold text-orange-600 mb-2">
              {(data.avgResolutionTimeHours || 0).toFixed(1)}
            </div>
            <div className="text-gray-600">平均解决时间(小时)</div>
            <div className="mt-2">
              <Tag color={(data.avgResolutionTimeHours || 0) <= 24 ? 'green' : (data.avgResolutionTimeHours || 0) <= 72 ? 'orange' : 'red'}>
                {(data.avgResolutionTimeHours || 0) <= 24 ? '快速' : (data.avgResolutionTimeHours || 0) <= 72 ? '正常' : '偏慢'}
              </Tag>
            </div>
          </Card>
        </Col>
        
        <Col span={6}>
          <Card className="text-center">
            <div className="text-2xl font-bold text-purple-600 mb-2">
              {(data.slaComplianceRate || 0).toFixed(1)}%
            </div>
            <div className="text-gray-600">SLA合规率</div>
            <div className="mt-2">
              <Progress 
                percent={data.slaComplianceRate || 0}
                showInfo={false}
                size="small"
                stroke={(data.slaComplianceRate || 0) >= 95 ? '#52c41a' : (data.slaComplianceRate || 0) >= 85 ? '#faad14' : '#ff4d4f'}
              />
            </div>
          </Card>
        </Col>
      </Row>

      {/* 分布图表 */}
      <Row gutter={16}>
        <Col span={8}>
          <Card>
            <div ref={categoryChartRef} style={{ width: '100%', height: '300px' }} />
          </Card>
        </Col>
        
        <Col span={8}>
          <Card>
            <div ref={priorityChartRef} style={{ width: '100%', height: '300px' }} />
          </Card>
        </Col>
        
        <Col span={8}>
          <Card>
            <div ref={statusChartRef} style={{ width: '100%', height: '300px' }} />
          </Card>
        </Col>
      </Row>

      {/* 工程师工作负载概览 */}
      <Card title="工程师工作负载概览" headerExtraContent={<IconUser />}>
        <Row gutter={[16, 16]}>
          {(data.engineerWorkload || []).slice(0, 6).map((engineer) => (
            <Col span={8} key={engineer.userId}>
              <Card>
                <div className="flex justify-between items-start mb-3">
                  <div>
                    <Text strong>{engineer.userName}</Text>
                    <div className="text-gray-500 text-sm">
                      效率: {(engineer.efficiency || 0).toFixed(1)}%
                    </div>
                  </div>
                  <Tag color={(engineer.efficiency || 0) >= 80 ? 'green' : (engineer.efficiency || 0) >= 60 ? 'orange' : 'red'}>
                    {(engineer.efficiency || 0) >= 80 ? '高效' : (engineer.efficiency || 0) >= 60 ? '正常' : '待提升'}
                  </Tag>
                </div>
                
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <Text size="small">分配/完成:</Text>
                    <Text size="small">{engineer.assignedCount || 0}/{engineer.completedCount || 0}</Text>
                  </div>
                  <div className="flex justify-between">
                    <Text size="small">工时:</Text>
                    <Text size="small">{(engineer.totalWorkHours || 0).toFixed(1)}h</Text>
                  </div>
                  <div className="flex justify-between">
                    <Text size="small">平均解决:</Text>
                    <Text size="small">{(engineer.avgResolutionTime || 0).toFixed(1)}h</Text>
                  </div>
                </div>
                
                <Progress 
                  percent={engineer.efficiency || 0}
                  showInfo={false}
                  size="small"
                  stroke={(engineer.efficiency || 0) >= 80 ? '#52c41a' : (engineer.efficiency || 0) >= 60 ? '#faad14' : '#ff4d4f'}
                  className="mt-3"
                />
              </Card>
            </Col>
          ))}
        </Row>
        
        {(data.engineerWorkload || []).length > 6 && (
          <div className="text-center mt-4">
            <Text type="secondary">
              显示前6位工程师，共{(data.engineerWorkload || []).length}位
            </Text>
          </div>
        )}
      </Card>
    </div>
  )
}

// ========== 工具函数 ==========

const getCategoryText = (category: string): string => {
  const categoryMap: Record<string, string> = {
    'MAINTENANCE': '运维',
    'SUPPORT': '支持',
    'UPGRADE': '升级',
    'BUGFIX': '故障修复',
    'CONSULTING': '咨询',
    'MONITORING': '监控'
  }
  return categoryMap[category] || category
}

const getPriorityText = (priority: string): string => {
  const priorityMap: Record<string, string> = {
    'LOW': '低',
    'MEDIUM': '中',
    'HIGH': '高',
    'URGENT': '紧急'
  }
  return priorityMap[priority] || priority
}

const getStatusText = (status: string): string => {
  const statusMap: Record<string, string> = {
    'PENDING': '待处理',
    'IN_PROGRESS': '处理中',
    'WAITING_CUSTOMER': '等待客户',
    'RESOLVED': '已解决',
    'CLOSED': '已关闭'
  }
  return statusMap[status] || status
}

const getPriorityColor = (priority: string): string => {
  const colorMap: Record<string, string> = {
    'LOW': '#52c41a',
    'MEDIUM': '#faad14',
    'HIGH': '#ff7a45',
    'URGENT': '#ff4d4f'
  }
  return colorMap[priority] || '#1890ff'
}

const getStatusColor = (status: string): string => {
  const colorMap: Record<string, string> = {
    'PENDING': '#faad14',
    'IN_PROGRESS': '#1890ff',
    'WAITING_CUSTOMER': '#722ed1',
    'RESOLVED': '#52c41a',
    'CLOSED': '#8c8c8c'
  }
  return colorMap[status] || '#1890ff'
}