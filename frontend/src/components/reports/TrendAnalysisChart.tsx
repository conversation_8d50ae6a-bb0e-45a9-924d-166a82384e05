import React, { useMemo, useRef, useEffect, useLayoutEffect } from 'react'
import { Card, Typography, Spin, Empty, Row, Col, Tag, Banner } from '@douyinfe/semi-ui'
import { IconActivity, IconClock, IconAlertTriangle, IconCalendar } from '@douyinfe/semi-icons'
import * as echarts from 'echarts'
import type { ServiceTrendAnalysis } from '@/services/report.service'

const { Title, Text } = Typography

interface TrendAnalysisChartProps {
  data?: ServiceTrendAnalysis
  loading?: boolean
  error?: any
}

export const TrendAnalysisChart: React.FC<TrendAnalysisChartProps> = ({ data, loading, error }) => {
  const volumeTrendRef = useRef<HTMLDivElement>(null)
  const resolutionTrendRef = useRef<HTMLDivElement>(null)
  const categoryTrendsRef = useRef<HTMLDivElement>(null)
  const backlogTrendRef = useRef<HTMLDivElement>(null)

  // ========== 图表配置 ==========
  const chartOptions = useMemo(() => {
    if (!data) return null

    // 服务量趋势图
    const volumeOption = {
      title: {
        text: '服务工单量趋势',
        left: 'center',
        textStyle: { fontSize: 16, fontWeight: 'bold' }
      },
      tooltip: {
        trigger: 'axis',
        formatter: (params: any) => {
          const daily = params[0]
          const cumulative = params[1]
          return `${daily.axisValue}<br/>当日新增: ${daily.value}<br/>累计总数: ${cumulative.value}`
        }
      },
      legend: {
        data: ['当日新增', '累计总数']
      },
      xAxis: {
        type: 'category',
        data: data.serviceVolumeTrend.map(t => {
          const date = new Date(t.date)
          return `${date.getMonth() + 1}/${date.getDate()}`
        })
      },
      yAxis: [
        {
          type: 'value',
          name: '当日新增',
          position: 'left'
        },
        {
          type: 'value',
          name: '累计总数',
          position: 'right'
        }
      ],
      series: [
        {
          name: '当日新增',
          type: 'bar',
          yAxisIndex: 0,
          data: data.serviceVolumeTrend.map(t => t.count),
          itemStyle: {
            color: '#1890ff'
          }
        },
        {
          name: '累计总数',
          type: 'line',
          yAxisIndex: 1,
          data: data.serviceVolumeTrend.map(t => t.cumulative),
          smooth: true,
          lineStyle: {
            color: '#52c41a',
            width: 3
          },
          itemStyle: {
            color: '#52c41a'
          }
        }
      ]
    }

    // 解决时间趋势图
    const resolutionOption = {
      title: {
        text: '解决时间趋势分析',
        left: 'center',
        textStyle: { fontSize: 16, fontWeight: 'bold' }
      },
      tooltip: {
        trigger: 'axis',
        formatter: (params: any) => {
          const avg = params[0]
          const min = params[1]
          const max = params[2]
          return `${avg.axisValue}<br/>平均: ${avg.value}小时<br/>最短: ${min.value}小时<br/>最长: ${max.value}小时`
        }
      },
      legend: {
        data: ['平均时间', '最短时间', '最长时间']
      },
      xAxis: {
        type: 'category',
        data: data.resolutionTimeTrend.map(t => {
          const date = new Date(t.date)
          return `${date.getMonth() + 1}/${date.getDate()}`
        })
      },
      yAxis: {
        type: 'value',
        name: '时间 (小时)'
      },
      series: [
        {
          name: '平均时间',
          type: 'line',
          data: data.resolutionTimeTrend.map(t => t.avgHours),
          smooth: true,
          lineStyle: {
            color: '#1890ff',
            width: 3
          }
        },
        {
          name: '最短时间',
          type: 'line',
          data: data.resolutionTimeTrend.map(t => t.minHours),
          smooth: true,
          lineStyle: {
            color: '#52c41a',
            type: 'dashed'
          }
        },
        {
          name: '最长时间',
          type: 'line',
          data: data.resolutionTimeTrend.map(t => t.maxHours),
          smooth: true,
          lineStyle: {
            color: '#ff4d4f',
            type: 'dashed'
          }
        }
      ]
    }

    // 服务类别趋势堆叠图
    const categoryTrendsData = Object.entries(data.categoryTrends)
    const categoryOption = {
      title: {
        text: '服务类别趋势',
        left: 'center',
        textStyle: { fontSize: 16, fontWeight: 'bold' }
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: { type: 'cross' }
      },
      legend: {
        data: categoryTrendsData.map(([category]) => getCategoryText(category))
      },
      xAxis: {
        type: 'category',
        data: categoryTrendsData[0]?.[1]?.map(t => {
          const date = new Date(t.date)
          return `${date.getMonth() + 1}/${date.getDate()}`
        }) || []
      },
      yAxis: {
        type: 'value',
        name: '工单数量'
      },
      series: categoryTrendsData.map(([category, trends], index) => ({
        name: getCategoryText(category),
        type: 'line',
        stack: 'total',
        data: trends.map(t => t.count),
        smooth: true,
        areaStyle: {},
        emphasis: {
          focus: 'series'
        }
      }))
    }

    return { volumeOption, resolutionOption, categoryOption }
  }, [data])

  // ========== 图表初始化 ==========
  useLayoutEffect(() => {
    if (!chartOptions || loading) return

    const initChart = (ref: React.RefObject<HTMLDivElement>, option: any) => {
      if (ref.current && option) {
        try {
          const chart = echarts.init(ref.current)
          
          // 验证数据完整性
          if (option.series && Array.isArray(option.series)) {
            option.series.forEach((series: any) => {
              if (series.data && !Array.isArray(series.data)) {
                series.data = []
              }
            })
          }
          
          chart.setOption(option)
          
          // 强制重新计算尺寸
          setTimeout(() => {
            chart.resize()
          }, 50)
          
          const handleResize = () => chart.resize()
          window.addEventListener('resize', handleResize)
          
          return () => {
            window.removeEventListener('resize', handleResize)
            chart.dispose()
          }
        } catch (error) {
          console.error('图表初始化失败:', error)
          return () => {}
        }
      }
      return () => {}
    }

    const cleanup1 = initChart(volumeTrendRef, chartOptions.volumeOption)
    const cleanup2 = initChart(resolutionTrendRef, chartOptions.resolutionOption)
    const cleanup3 = initChart(categoryTrendsRef, chartOptions.categoryOption)

    return () => {
      cleanup1?.()
      cleanup2?.()
      cleanup3?.()
    }
  }, [chartOptions, loading])

  // ========== 加载和错误状态 ==========
  if (error) {
    return (
      <Card>
        <div className="text-center py-8">
          <Text type="danger">获取趋势分析数据失败</Text>
        </div>
      </Card>
    )
  }

  if (loading) {
    return (
      <Card>
        <div className="text-center py-8">
          <Spin size="large" />
        </div>
      </Card>
    )
  }

  if (!data) {
    return (
      <Card>
        <Empty description="暂无趋势分析数据" />
      </Card>
    )
  }

  // ========== 主要渲染 ==========
  return (
    <div className="space-y-6">
      {/* 待办事项积压警告 */}
      {data.backlogAnalysis.currentBacklog > 10 && (
        <Banner
          type="warning"
          description={`待办事项积压警告: 当前有 ${data.backlogAnalysis.currentBacklog} 个待处理工单，建议优化工作流程`}
          icon={<IconAlertTriangle />}
        />
      )}

      {/* 服务量趋势 */}
      <Card>
        <div ref={volumeTrendRef} style={{ width: '100%', height: '400px' }} />
      </Card>

      {/* 解决时间趋势 */}
      <Card>
        <div ref={resolutionTrendRef} style={{ width: '100%', height: '400px' }} />
      </Card>

      {/* 类别趋势和积压分析 */}
      <Row gutter={16}>
        <Col span={16}>
          <Card>
            <div ref={categoryTrendsRef} style={{ width: '100%', height: '350px' }} />
          </Card>
        </Col>
        
        <Col span={8}>
          <Card title="积压工单分析" headerExtraContent={<IconActivity />}>
            <div className="space-y-4">
              <div className="text-center p-4 bg-gray-50 rounded">
                <div className="text-3xl font-bold text-blue-600 mb-2">
                  {data.backlogAnalysis.currentBacklog}
                </div>
                <div className="text-gray-600">当前积压工单</div>
              </div>
              
              {data.backlogAnalysis.oldestTicket && (
                <Banner
                  type="warning"
                  description={`最久待处理工单: ${data.backlogAnalysis.oldestTicket.ticketNumber}，已等待 ${data.backlogAnalysis.oldestTicket.daysOld} 天`}
                  icon={<IconAlertTriangle />}
                />
              )}
              
              <div className="space-y-2">
                <div className="flex justify-between">
                  <Text size="small">平均积压规模</Text>
                  <Text size="small">{data.backlogAnalysis.avgBacklogSize}</Text>
                </div>
                
                <div className="bg-blue-50 p-3 rounded">
                  <Text size="small" type="secondary">
                    📈 积压趋势分析建议优化工作分配和优先级管理
                  </Text>
                </div>
              </div>
            </div>
          </Card>
        </Col>
      </Row>

      {/* 时间模式分析 */}
      <Card title="服务时间模式分析" headerExtraContent={<IconCalendar />}>
        <Row gutter={16}>
          <Col span={8}>
            <div className="text-center">
              <Title heading={5}>工作日分布</Title>
              <div className="mt-4">
                <Tag>周一至周五: 业务高峰</Tag>
                <br />
                <Tag color="orange" className="mt-2">周末: 紧急服务</Tag>
              </div>
            </div>
          </Col>
          
          <Col span={8}>
            <div className="text-center">
              <Title heading={5}>时段分布</Title>
              <div className="mt-4">
                <Tag>9:00-18:00: 工作时间</Tag>
                <br />
                <Tag color="red" className="mt-2">18:00-9:00: 应急响应</Tag>
              </div>
            </div>
          </Col>
          
          <Col span={8}>
            <div className="text-center">
              <Title heading={5}>月度模式</Title>
              <div className="mt-4">
                <Tag>月初: 需求集中</Tag>
                <br />
                <Tag color="blue" className="mt-2">月末: 项目交付</Tag>
              </div>
            </div>
          </Col>
        </Row>
        
        <div className="mt-4 p-4 bg-yellow-50 rounded">
          <Text type="secondary">
            💡 模式分析: 根据历史数据分析，建议在业务高峰期增加人员配置，优化资源分配
          </Text>
        </div>
      </Card>
    </div>
  )
}

// ========== 工具函数 ==========

const getCategoryText = (category: string): string => {
  const categoryMap: Record<string, string> = {
    'MAINTENANCE': '运维',
    'SUPPORT': '支持', 
    'UPGRADE': '升级',
    'BUGFIX': '故障修复',
    'CONSULTING': '咨询',
    'MONITORING': '监控'
  }
  return categoryMap[category] || category
}