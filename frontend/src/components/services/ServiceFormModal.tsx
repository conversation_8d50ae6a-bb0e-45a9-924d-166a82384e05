import React, { useEffect, useState } from "react";
import { Modal, Form, Select, Toast, Input, Tag } from "@douyinfe/semi-ui";
import { IconSearch, IconUser } from "@douyinfe/semi-icons";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { serviceService } from "@/services/service";
import { customerService } from "@/services/customer";
import { archiveService } from "@/services/archive";
import { slaService, type SlaTemplate } from "@/services/sla";
import { Service, CreateServiceRequest, UpdateServiceRequest, CustomerContact } from "shared/types";

interface ServiceFormModalProps {
  visible: boolean;
  onClose: () => void;
  service?: Service | null;
}

export default function ServiceFormModal({ visible, onClose, service }: ServiceFormModalProps) {
  const queryClient = useQueryClient();
  const [formApi, setFormApi] = useState<any>();
  const [selectedCustomerId, setSelectedCustomerId] = useState<string | null>(null);
  const [customerSearchQuery, setCustomerSearchQuery] = useState("");
  const [selectedContacts, setSelectedContacts] = useState<CustomerContact[]>([]);
  const [contactSearchQuery, setContactSearchQuery] = useState("");
  const [isContactListVisible, setIsContactListVisible] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<string>("");
  const [selectedPriority, setSelectedPriority] = useState<string>("");

  const { data: customers, isLoading: customersLoading } = useQuery({
    queryKey: ["customers", { limit: 1000 }],
    queryFn: () => customerService.getCustomers({ limit: 1000 }),
    select: response => response.data.items,
  });

  // 获取选中客户的详细信息（包含联系人）
  const { data: selectedCustomer, isLoading: customerDetailsLoading } = useQuery({
    queryKey: ["customer-details", selectedCustomerId],
    queryFn: () => customerService.getCustomer(selectedCustomerId!),
    enabled: !!selectedCustomerId,
    select: response => response.data,
  });

  const { data: archives, isLoading: archivesLoading } = useQuery({
    queryKey: ["archives", { customerId: selectedCustomerId }],
    queryFn: () => archiveService.getArchives({ customerId: selectedCustomerId!, limit: 1000 }),
    enabled: !!selectedCustomerId,
    select: response => {
      // 后端返回的是 archives 字段，不是 items
      const data = response.data as any;
      return data?.archives || data?.items || [];
    },
  });

  // 获取所有可用的SLA模板
  const { data: slaTemplates, isLoading: slaTemplatesLoading } = useQuery({
    queryKey: ["sla-templates"],
    queryFn: () => slaService.getSlaTemplates({ limit: 100 }),
    select: response => response.data?.items || response.data || [],
  });

  // 获取推荐的SLA模板
  const { data: recommendedSlaTemplates } = useQuery({
    queryKey: [
      "recommended-sla-templates",
      {
        customerLevel: selectedCustomer?.level,
        serviceCategory: selectedCategory,
        priority: selectedPriority,
      },
    ],
    queryFn: () =>
      slaService.getRecommendedSlaTemplates({
        customerLevel: selectedCustomer?.level,
        serviceCategory: selectedCategory || undefined,
        priority: selectedPriority || undefined,
      }),
    enabled: !!(selectedCustomer?.level || selectedCategory || selectedPriority),
    select: response => response.data || [],
  });

  const createMutation = useMutation({
    mutationFn: (data: CreateServiceRequest) => serviceService.createService(data),
    onSuccess: () => {
      Toast.success("工单创建成功");
      queryClient.invalidateQueries({ queryKey: ["services"] });
      onClose();
    },
    onError: (err: any) => Toast.error(`创建失败: ${err.message}`),
  });

  const updateMutation = useMutation({
    mutationFn: (data: UpdateServiceRequest) => serviceService.updateService(service!.id, data),
    onSuccess: () => {
      Toast.success("工单更新成功");
      queryClient.invalidateQueries({ queryKey: ["services"] });
      onClose();
    },
    onError: (err: any) => Toast.error(`更新失败: ${err.message}`),
  });

  useEffect(() => {
    if (service && formApi) {
      formApi.setValues(service);
      // 从服务数据中获取客户ID
      if (service.archiveId) {
        // 这里假设我们可以从archive关联中获取customerId
        // 实际情况可能需要额外的API调用来获取archive信息
        setSelectedCustomerId(service.archiveId); // 暂时使用archiveId，可能需要调整
      }
    } else if (formApi) {
      formApi.reset();
      setSelectedCustomerId(null);
      setSelectedContacts([]);
      setCustomerSearchQuery("");
      setContactSearchQuery("");
    }
  }, [service, formApi]);

  // 当客户改变时，清空已选联系人
  useEffect(() => {
    if (selectedCustomerId) {
      setSelectedContacts([]);
      setContactSearchQuery("");
      setIsContactListVisible(false);
    }
  }, [selectedCustomerId]);

  const handleSubmit = (values: any) => {
    // 验证是否选择了联系人
    if (selectedContacts.length === 0) {
      Toast.error("请选择至少一个客户联系人");
      return;
    }

    // 添加选中的联系人到提交数据
    const submitData = {
      ...values,
      customerContacts: selectedContacts.map(contact => contact.id),
    };

    if (service) {
      updateMutation.mutate(submitData);
    } else {
      createMutation.mutate(submitData);
    }
  };

  // 过滤客户列表
  const filteredCustomers =
    customers?.filter(
      customer =>
        customer.name.toLowerCase().includes(customerSearchQuery.toLowerCase()) ||
        (customer.company &&
          customer.company.toLowerCase().includes(customerSearchQuery.toLowerCase()))
    ) || [];

  // 过滤联系人列表
  const availableContacts = (selectedCustomer as any)?.contacts || [];
  const filteredContacts = contactSearchQuery
    ? availableContacts.filter(
        (contact: CustomerContact) =>
          contact.name.toLowerCase().includes(contactSearchQuery.toLowerCase()) ||
          (contact.email && contact.email.toLowerCase().includes(contactSearchQuery.toLowerCase()))
      )
    : availableContacts; // 没有搜索内容时显示所有联系人

  // 处理联系人选择
  const handleContactSelect = (contact: CustomerContact) => {
    if (!selectedContacts.find(c => c.id === contact.id)) {
      setSelectedContacts([...selectedContacts, contact]);
    }
    setContactSearchQuery("");
    setIsContactListVisible(false);
  };

  const handleContactRemove = (contactId: string) => {
    setSelectedContacts(selectedContacts.filter(c => c.id !== contactId));
  };

  // 处理点击联系人容器外部
  const handleContactContainerClick = (event: React.MouseEvent) => {
    event.stopPropagation();
  };

  return (
    <Modal
      title={service ? "编辑工单" : "创建工单"}
      visible={visible}
      onOk={() => formApi.submitForm()}
      onCancel={onClose}
      confirmLoading={createMutation.isPending || updateMutation.isPending}
      width={600}
    >
      <Form<any>
        getFormApi={setFormApi}
        onSubmit={handleSubmit}
        labelPosition="left"
        labelAlign="right"
        labelWidth="100px"
      >
        <Form.Select
          field="customerId"
          label="选择客户"
          placeholder="请先选择客户"
          loading={customersLoading}
          onChange={value => setSelectedCustomerId(value as string)}
          rules={[{ required: true, message: "请选择客户" }]}
          filter
          onSearch={setCustomerSearchQuery}
          searchPlaceholder="搜索客户名称或公司"
        >
          {filteredCustomers.map(c => (
            <Select.Option key={c.id} value={c.id}>
              <div style={{ display: "flex", flexDirection: "column" }}>
                <span style={{ fontWeight: "bold" }}>{c.name}</span>
                {c.company && (
                  <span style={{ fontSize: "12px", color: "var(--semi-color-text-2)" }}>
                    {c.company}
                  </span>
                )}
              </div>
            </Select.Option>
          ))}
        </Form.Select>

        <Form.Select
          field="archiveId"
          label="关联项目"
          placeholder={selectedCustomerId ? "请选择项目档案" : "请先选择客户"}
          loading={archivesLoading}
          disabled={!selectedCustomerId}
          rules={[{ required: true, message: "请选择项目" }]}
        >
          {archives && archives.length > 0
            ? archives.map((archive: any) => (
                <Select.Option key={archive.id} value={archive.id}>
                  {archive.name}
                </Select.Option>
              ))
            : selectedCustomerId &&
              !archivesLoading && (
                <Select.Option key="no-data" value="" disabled>
                  该客户暂无项目档案
                </Select.Option>
              )}
        </Form.Select>

        <Form.Input
          field="title"
          label="标题"
          rules={[{ required: true, message: "请输入标题" }]}
        />

        <Form.TextArea
          field="description"
          label="问题描述"
          rules={[{ required: true, message: "请输入问题描述" }]}
        />

        <Form.Select
          field="category"
          label="服务类别"
          rules={[{ required: true, message: "请选择服务类别" }]}
          onChange={value => setSelectedCategory(value as string)}
        >
          <Select.Option value="MAINTENANCE">日常维护</Select.Option>
          <Select.Option value="SUPPORT">技术支持</Select.Option>
          <Select.Option value="UPGRADE">升级部署</Select.Option>
          <Select.Option value="BUGFIX">故障修复</Select.Option>
          <Select.Option value="CONSULTING">技术咨询</Select.Option>
          <Select.Option value="MONITORING">监控维护</Select.Option>
        </Form.Select>

        <Form.Select
          field="priority"
          label="优先级"
          rules={[{ required: true, message: "请选择优先级" }]}
          onChange={value => setSelectedPriority(value as string)}
        >
          <Select.Option value="LOW">低</Select.Option>
          <Select.Option value="MEDIUM">中</Select.Option>
          <Select.Option value="HIGH">高</Select.Option>
          <Select.Option value="URGENT">紧急</Select.Option>
        </Form.Select>

        {/* SLA模板选择 */}
        <Form.Select
          field="slaTemplateId"
          label="SLA模板"
          placeholder="选择SLA服务等级协议"
          loading={slaTemplatesLoading}
          style={{ padding: "5px 0", height: "50px" }}
          showClear
        >
          {/* 推荐的SLA模板（如果有） */}
          {recommendedSlaTemplates && recommendedSlaTemplates.length > 0 && (
            <Select.OptGroup label="推荐模板">
              {recommendedSlaTemplates.map((template: SlaTemplate) => (
                <Select.Option key={template.id} value={template.id}>
                  <div
                    style={{
                      display: "flex",
                      justifyContent: "space-between",
                      alignItems: "center",
                    }}
                  >
                    <div>
                      <strong>{template.name}</strong>
                      {template.description && (
                        <div style={{ fontSize: "12px", color: "var(--semi-color-text-2)" }}>
                          {template.description}
                        </div>
                      )}
                    </div>
                    <div style={{ fontSize: "12px", color: "var(--semi-color-text-1)" }}>
                      响应: {template.responseTime}分钟 | 解决: {template.resolutionTime}小时
                    </div>
                  </div>
                </Select.Option>
              ))}
            </Select.OptGroup>
          )}

          {/* 所有可用的SLA模板 */}
          <Select.OptGroup
            label={
              recommendedSlaTemplates && recommendedSlaTemplates.length > 0
                ? "其他模板"
                : "可用模板"
            }
          >
            {Array.isArray(slaTemplates) && slaTemplates.length > 0 ? (
              slaTemplates
                .filter(
                  (template: SlaTemplate) =>
                    !recommendedSlaTemplates?.find(rec => rec.id === template.id)
                )
                .map((template: SlaTemplate) => (
                  <Select.Option key={template.id} value={template.id}>
                    <div
                      style={{
                        display: "flex",
                        justifyContent: "space-between",
                        alignItems: "center",
                      }}
                    >
                      <div>
                        <span>{template.name}</span>
                        {template.description && (
                          <div style={{ fontSize: "12px", color: "var(--semi-color-text-2)" }}>
                            {template.description}
                          </div>
                        )}
                      </div>
                      <div style={{ fontSize: "12px", color: "var(--semi-color-text-1)" }}>
                        响应: {template.responseTime}分钟 | 解决: {template.resolutionTime}小时
                      </div>
                    </div>
                  </Select.Option>
                ))
            ) : (
              <Select.Option key="no-sla" value="" disabled>
                暂无可用SLA模板
              </Select.Option>
            )}
          </Select.OptGroup>
        </Form.Select>

        {/* 隐藏字段用于存储选中的联系人ID */}
        <Form.Input
          field="customerContacts"
          noLabel={true}
          style={{ display: "none" }}
          initValue={selectedContacts.map(c => c.id).join(",")}
        />

        {/* 客户联系人选择区域 */}
        <div style={{ marginBottom: "24px" }}>
          <label
            style={{
              display: "block",
              marginBottom: "8px",
              fontWeight: "bold",
              fontSize: "14px",
            }}
          >
            客户联系人 <span style={{ color: "red" }}>*</span>
          </label>

          <div
            style={{
              border: "1px solid var(--semi-color-border)",
              borderRadius: "6px",
              padding: "8px",
              minHeight: "40px",
            }}
          >
            {/* 已选联系人标签 */}
            <div
              style={{
                marginBottom: selectedContacts.length > 0 ? "8px" : "0",
                display: "flex",
                flexWrap: "wrap",
                gap: "4px",
              }}
            >
              {selectedContacts.map(contact => (
                <Tag
                  key={contact.id}
                  closable
                  onClose={() => handleContactRemove(contact.id)}
                  style={{ marginBottom: "4px" }}
                >
                  <IconUser size="small" style={{ marginRight: "4px" }} />
                  {contact.name}
                  {contact.position && ` (${contact.position})`}
                </Tag>
              ))}
            </div>

            {/* 联系人搜索和选择 */}
            {selectedCustomerId && (
              <div>
                <Input
                  prefix={<IconSearch />}
                  placeholder="搜索并选择客户联系人"
                  value={contactSearchQuery}
                  onChange={setContactSearchQuery}
                  onFocus={() => setIsContactListVisible(true)}
                  onBlur={() => {
                    // 延迟隐藏列表，让点击事件能够正常触发
                    setTimeout(() => setIsContactListVisible(false), 200);
                  }}
                  disabled={!selectedCustomerId || customerDetailsLoading}
                  style={{ marginBottom: "4px" }}
                />

                {/* 联系人选项列表 */}
                {isContactListVisible && filteredContacts.length > 0 && (
                  <div
                    onClick={handleContactContainerClick}
                    style={{
                      maxHeight: "120px",
                      overflowY: "auto",
                      border: "1px solid var(--semi-color-border)",
                      borderRadius: "4px",
                      backgroundColor: "var(--semi-color-bg-1)",
                    }}
                  >
                    {filteredContacts.map((contact: CustomerContact) => {
                      const isSelected = selectedContacts.find(c => c.id === contact.id);
                      return (
                        <div
                          key={contact.id}
                          onClick={() => !isSelected && handleContactSelect(contact)}
                          style={{
                            padding: "8px 12px",
                            cursor: isSelected ? "default" : "pointer",
                            backgroundColor: isSelected
                              ? "var(--semi-color-fill-1)"
                              : "transparent",
                            opacity: isSelected ? 0.6 : 1,
                            borderBottom: "1px solid var(--semi-color-border)",
                          }}
                        >
                          <div
                            style={{
                              display: "flex",
                              alignItems: "center",
                              justifyContent: "space-between",
                            }}
                          >
                            <div>
                              <div style={{ fontWeight: "bold" }}>{contact.name}</div>
                              {contact.position && (
                                <div
                                  style={{
                                    fontSize: "12px",
                                    color: "var(--semi-color-text-2)",
                                  }}
                                >
                                  {contact.position}
                                </div>
                              )}
                              {contact.email && (
                                <div
                                  style={{
                                    fontSize: "12px",
                                    color: "var(--semi-color-text-2)",
                                  }}
                                >
                                  {contact.email}
                                </div>
                              )}
                            </div>
                            {contact.isPrimary && (
                              <Tag size="small" color="blue">
                                主要联系人
                              </Tag>
                            )}
                          </div>
                        </div>
                      );
                    })}
                  </div>
                )}

                {/* 当列表显示但没有联系人时的提示 */}
                {isContactListVisible &&
                  !customerDetailsLoading &&
                  availableContacts.length === 0 && (
                    <div
                      style={{
                        padding: "16px",
                        textAlign: "center",
                        color: "var(--semi-color-text-2)",
                        fontSize: "14px",
                        border: "1px solid var(--semi-color-border)",
                        borderRadius: "4px",
                        backgroundColor: "var(--semi-color-bg-1)",
                      }}
                    >
                      该客户暂无联系人信息
                    </div>
                  )}
              </div>
            )}

            {/* 未选择客户时的提示 */}
            {!selectedCustomerId && (
              <div
                style={{
                  padding: "16px",
                  textAlign: "center",
                  color: "var(--semi-color-text-2)",
                  fontSize: "14px",
                }}
              >
                请先选择客户
              </div>
            )}
          </div>
        </div>

        <Form.InputNumber field="estimatedHours" label="预估工时" />
      </Form>
    </Modal>
  );
}
