import React, { useState, useRef, useCallback, useEffect } from "react";
import { Form, Select, TextArea, Button, Toast, Typography, Space } from "@douyinfe/semi-ui";
import { put, get } from "@/utils/request";

const { Text } = Typography;
const { Option } = Select;

interface ServiceTransferFormProps {
  serviceId: string;
  currentAssignee?: {
    id: string;
    username: string;
    fullName?: string;
  } | null;
  onSuccess: () => void;
  onCancel?: () => void;
}

interface TransferFormData {
  assignedTo: string;
  transferNote?: string;
}

interface User {
  id: string;
  username: string;
  fullName?: string;
  email: string;
}

export function ServiceTransferForm({
  serviceId,
  currentAssignee,
  onSuccess,
  onCancel,
}: ServiceTransferFormProps) {
  const formRef = useRef<Form>(null);
  const [submitting, setSubmitting] = useState(false);
  const [loading, setLoading] = useState(false);
  const [users, setUsers] = useState<User[]>([]);

  // 加载可用用户列表
  const loadUsers = useCallback(async () => {
    setLoading(true);
    try {
      const response = await get("/users", {
        status: "ACTIVE",
        limit: 100, // 获取足够多的用户
      });

      if (response.success) {
        setUsers(response.data.users || []);
      } else {
        Toast.error("获取用户列表失败");
      }
    } catch (error: any) {
      console.error("获取用户列表失败:", error);
      Toast.error("获取用户列表失败");
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    loadUsers();
  }, [loadUsers]);

  const handleSubmit = useCallback(
    async (values: TransferFormData) => {
      setSubmitting(true);

      console.log("转交工单开始:", {
        serviceId,
        values,
        currentUrl: window.location.href,
      });

      try {
        const response = await put(`/api/v1/services/${serviceId}`, {
          assignedTo: values.assignedTo,
          transferNote: values.transferNote,
          operationType: "TRANSFER",
          operationNote: values.transferNote,
        });

        console.log("工单转交响应:", response);

        if (response.success) {
          Toast.success("工单转交成功");
          onSuccess();
        } else {
          Toast.error(response.message || "工单转交失败");
        }
      } catch (error: any) {
        console.error("工单转交失败:", error);
        console.error("错误详情:", {
          status: error.status,
          message: error.message,
          stack: error.stack,
        });

        // 处理认证错误
        if (error.status === 401) {
          console.log("认证失败，将跳转到登录页");
          return;
        }

        Toast.error(error.message || "工单转交失败，请稍后重试");
      } finally {
        setSubmitting(false);
        console.log("工单转交结束");
      }
    },
    [serviceId, onSuccess]
  );

  const handleCancel = useCallback(() => {
    onCancel?.();
  }, [onCancel]);

  return (
    <div
      style={{
        padding: "16px",
        background: "var(--semi-color-bg-2)",
        borderRadius: "6px",
        border: "1px solid var(--semi-color-border)",
        marginBottom: "16px",
      }}
    >
      <Form
        ref={formRef}
        initValues={{ assignedTo: currentAssignee?.id || "" }}
        onSubmit={handleSubmit}
        labelPosition="left"
        labelWidth={80}
        labelAlign="left"
      >
        <Form.Select
          field="assignedTo"
          label="转交给"
          placeholder="选择要转交的用户"
          rules={[{ required: true, message: "请选择转交对象" }]}
          loading={loading}
          style={{ width: "100%" }}
        >
          {users.map(user => (
            <Option key={user.id} value={user.id}>
              <div
                style={{ display: "flex", alignItems: "center", justifyContent: "space-between" }}
              >
                <span>{user.fullName || user.username}</span>
                <Text type="tertiary" size="small">
                  {user.email}
                </Text>
              </div>
            </Option>
          ))}
        </Form.Select>

        <Form.TextArea
          field="transferNote"
          label="转交说明"
          placeholder="请输入转交说明（可选）"
          rows={3}
          maxCount={500}
          showClear
        />

        <div
          style={{
            display: "flex",
            justifyContent: "flex-end",
            gap: "8px",
            marginTop: "16px",
            paddingTop: "16px",
            borderTop: "1px solid var(--semi-color-border)",
          }}
        >
          {onCancel && (
            <Button onClick={handleCancel} disabled={submitting} size="small">
              取消
            </Button>
          )}
          <Button theme="solid" type="primary" htmlType="submit" loading={submitting} size="small">
            转交工单
          </Button>
        </div>
      </Form>

      <div style={{ marginTop: "12px" }}>
        <Text type="tertiary" size="small">
          转交说明：工单转交后，新的负责人将收到通知，原负责人仍可查看工单信息
        </Text>
        {currentAssignee && (
          <div style={{ marginTop: "4px" }}>
            <Text type="secondary" size="small">
              当前负责人：{currentAssignee.fullName || currentAssignee.username}
            </Text>
          </div>
        )}
      </div>
    </div>
  );
}
