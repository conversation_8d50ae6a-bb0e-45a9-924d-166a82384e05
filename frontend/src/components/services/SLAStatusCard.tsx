import React from "react";
import { Card, Progress, Tag, Row, Col, Typography, Space } from "@douyinfe/semi-ui";
import { IconClock, IconAlarm, IconTickCircle, IconAlertTriangle } from "@douyinfe/semi-icons";

const { Text, Title } = Typography;

interface SLAStatusCardProps {
  slaTemplate?: {
    id: string;
    name: string;
    responseTime: number;
    resolutionTime: number;
  };
  createdAt: string;
  startTime?: string;
  endTime?: string;
  firstResponseAt?: string;
  status: string;
}

interface SLAStatus {
  responseStatus: "PENDING" | "MET" | "VIOLATED";
  resolutionStatus: "PENDING" | "MET" | "VIOLATED";
  responseProgress: number;
  resolutionProgress: number;
  responseRemaining: number;
  resolutionRemaining: number;
}

export function SLAStatusCard({
  slaTemplate,
  createdAt,
  startTime,
  endTime,
  firstResponseAt,
  status,
}: SLAStatusCardProps) {
  const calculateSLAStatus = (): SLAStatus => {
    if (!slaTemplate) {
      return {
        responseStatus: "PENDING",
        resolutionStatus: "PENDING",
        responseProgress: 0,
        resolutionProgress: 0,
        responseRemaining: 0,
        resolutionRemaining: 0,
      };
    }

    const now = new Date();
    const created = new Date(createdAt);
    const responseDeadline = new Date(
      created.getTime() + slaTemplate.responseTime * 60 * 60 * 1000
    );
    const resolutionDeadline = new Date(
      created.getTime() + slaTemplate.resolutionTime * 60 * 60 * 1000
    );

    // 响应时间计算
    let responseStatus: "PENDING" | "MET" | "VIOLATED" = "PENDING";
    let responseProgress = 0;
    let responseRemaining = 0;

    if (firstResponseAt) {
      const firstResponse = new Date(firstResponseAt);
      responseStatus = firstResponse <= responseDeadline ? "MET" : "VIOLATED";
      responseProgress = 100;
    } else {
      const elapsed = now.getTime() - created.getTime();
      const total = slaTemplate.responseTime * 60 * 60 * 1000;
      responseProgress = Math.min((elapsed / total) * 100, 100);
      responseRemaining = Math.max(responseDeadline.getTime() - now.getTime(), 0);

      if (now > responseDeadline) {
        responseStatus = "VIOLATED";
      }
    }

    // 解决时间计算
    let resolutionStatus: "PENDING" | "MET" | "VIOLATED" = "PENDING";
    let resolutionProgress = 0;
    let resolutionRemaining = 0;

    if (status === "RESOLVED" || status === "CLOSED") {
      const resolved = endTime ? new Date(endTime) : now;
      resolutionStatus = resolved <= resolutionDeadline ? "MET" : "VIOLATED";
      resolutionProgress = 100;
    } else {
      const elapsed = now.getTime() - created.getTime();
      const total = slaTemplate.resolutionTime * 60 * 60 * 1000;
      resolutionProgress = Math.min((elapsed / total) * 100, 100);
      resolutionRemaining = Math.max(resolutionDeadline.getTime() - now.getTime(), 0);

      if (now > resolutionDeadline) {
        resolutionStatus = "VIOLATED";
      }
    }

    return {
      responseStatus,
      resolutionStatus,
      responseProgress,
      resolutionProgress,
      responseRemaining,
      resolutionRemaining,
    };
  };

  const formatTimeRemaining = (milliseconds: number): string => {
    if (milliseconds <= 0) return "已超时";

    const hours = Math.floor(milliseconds / (1000 * 60 * 60));
    const minutes = Math.floor((milliseconds % (1000 * 60 * 60)) / (1000 * 60));

    if (hours > 24) {
      const days = Math.floor(hours / 24);
      const remainingHours = hours % 24;
      return `${days}天${remainingHours}小时`;
    } else if (hours > 0) {
      return `${hours}小时${minutes}分钟`;
    } else {
      return `${minutes}分钟`;
    }
  };

  const getProgressColor = (status: "PENDING" | "MET" | "VIOLATED", progress: number) => {
    if (status === "VIOLATED") return "red";
    if (status === "MET") return "green";
    if (progress > 80) return "orange";
    if (progress > 60) return "yellow";
    return "blue";
  };

  const getStatusIcon = (status: "PENDING" | "MET" | "VIOLATED") => {
    switch (status) {
      case "MET":
        return <IconTickCircle style={{ color: "var(--semi-color-success)" }} />;
      case "VIOLATED":
        return <IconAlertTriangle style={{ color: "var(--semi-color-danger)" }} />;
      default:
        return <IconAlarm style={{ color: "var(--semi-color-warning)" }} />;
    }
  };

  const getStatusText = (status: "PENDING" | "MET" | "VIOLATED") => {
    switch (status) {
      case "MET":
        return "已满足";
      case "VIOLATED":
        return "已违规";
      default:
        return "进行中";
    }
  };

  if (!slaTemplate) {
    return (
      <Card
        title={
          <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
            <IconClock />
            <span>SLA信息</span>
          </div>
        }
        style={{ background: "var(--semi-color-bg-1)" }}
      >
        <Text type="tertiary">该工单未配置SLA模板</Text>
      </Card>
    );
  }

  const slaStatus = calculateSLAStatus();

  return (
    <Card
      title={
        <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
          <IconClock />
          <span>SLA状态监控</span>
          <Tag
            color={
              slaStatus.responseStatus === "VIOLATED" || slaStatus.resolutionStatus === "VIOLATED"
                ? "red"
                : slaStatus.responseProgress > 80 || slaStatus.resolutionProgress > 80
                  ? "orange"
                  : "green"
            }
            size="small"
          >
            {slaTemplate.name}
          </Tag>
        </div>
      }
      style={{
        background: "var(--semi-color-bg-1)",
        border:
          slaStatus.responseStatus === "VIOLATED" || slaStatus.resolutionStatus === "VIOLATED"
            ? "2px solid var(--semi-color-danger)"
            : slaStatus.responseProgress > 80 || slaStatus.resolutionProgress > 80
              ? "2px solid var(--semi-color-warning)"
              : "1px solid var(--semi-color-border)",
      }}
    >
      <Row gutter={[24, 16]}>
        {/* 响应时间SLA */}
        <Col span={12}>
          <div
            style={{ padding: "16px", background: "var(--semi-color-bg-2)", borderRadius: "6px" }}
          >
            <div
              style={{
                display: "flex",
                alignItems: "center",
                justifyContent: "space-between",
                marginBottom: "12px",
              }}
            >
              <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
                {getStatusIcon(slaStatus.responseStatus)}
                <Text strong>响应时间SLA</Text>
              </div>
              <Tag
                color={getProgressColor(slaStatus.responseStatus, slaStatus.responseProgress)}
                size="small"
              >
                {getStatusText(slaStatus.responseStatus)}
              </Tag>
            </div>

            <div style={{ marginBottom: "8px" }}>
              <Text type="secondary" size="small">
                要求: {slaTemplate.responseTime}小时内响应
              </Text>
            </div>

            <Progress
              percent={slaStatus.responseProgress}
              stroke={getProgressColor(slaStatus.responseStatus, slaStatus.responseProgress)}
              size="small"
              showInfo={false}
              style={{ marginBottom: "8px" }}
            />

            <div style={{ display: "flex", justifyContent: "space-between" }}>
              <Text size="small" type="tertiary">
                进度: {Math.round(slaStatus.responseProgress)}%
              </Text>
              {slaStatus.responseStatus === "PENDING" && (
                <Text size="small" type={slaStatus.responseRemaining > 0 ? "secondary" : "danger"}>
                  剩余: {formatTimeRemaining(slaStatus.responseRemaining)}
                </Text>
              )}
            </div>
          </div>
        </Col>

        {/* 解决时间SLA */}
        <Col span={12}>
          <div
            style={{ padding: "16px", background: "var(--semi-color-bg-2)", borderRadius: "6px" }}
          >
            <div
              style={{
                display: "flex",
                alignItems: "center",
                justifyContent: "space-between",
                marginBottom: "12px",
              }}
            >
              <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
                {getStatusIcon(slaStatus.resolutionStatus)}
                <Text strong>解决时间SLA</Text>
              </div>
              <Tag
                color={getProgressColor(slaStatus.resolutionStatus, slaStatus.resolutionProgress)}
                size="small"
              >
                {getStatusText(slaStatus.resolutionStatus)}
              </Tag>
            </div>

            <div style={{ marginBottom: "8px" }}>
              <Text type="secondary" size="small">
                要求: {slaTemplate.resolutionTime}小时内解决
              </Text>
            </div>

            <Progress
              percent={slaStatus.resolutionProgress}
              stroke={getProgressColor(slaStatus.resolutionStatus, slaStatus.resolutionProgress)}
              size="small"
              showInfo={false}
              style={{ marginBottom: "8px" }}
            />

            <div style={{ display: "flex", justifyContent: "space-between" }}>
              <Text size="small" type="tertiary">
                进度: {Math.round(slaStatus.resolutionProgress)}%
              </Text>
              {slaStatus.resolutionStatus === "PENDING" && (
                <Text
                  size="small"
                  type={slaStatus.resolutionRemaining > 0 ? "secondary" : "danger"}
                >
                  剩余: {formatTimeRemaining(slaStatus.resolutionRemaining)}
                </Text>
              )}
            </div>
          </div>
        </Col>
      </Row>

      {/* 总体状态提示 */}
      {(slaStatus.responseStatus === "VIOLATED" || slaStatus.resolutionStatus === "VIOLATED") && (
        <div
          style={{
            marginTop: "16px",
            padding: "12px",
            background: "var(--semi-color-danger-light-default)",
            borderRadius: "6px",
            border: "1px solid var(--semi-color-danger-light-active)",
          }}
        >
          <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
            <IconAlertTriangle style={{ color: "var(--semi-color-danger)" }} />
            <Text strong style={{ color: "var(--semi-color-danger)" }}>
              SLA违规警告
            </Text>
          </div>
          <Text size="small" style={{ color: "var(--semi-color-danger)", marginTop: "4px" }}>
            该工单已违反SLA要求，请尽快处理或升级
          </Text>
        </div>
      )}
    </Card>
  );
}
