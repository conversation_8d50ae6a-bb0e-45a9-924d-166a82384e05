import React, { useState, useEffect, useRef, useCallback } from "react";
import {
  Modal,
  Form,
  Input,
  Select,
  TextArea,
  Button,
  Toast,
  Row,
  Col,
  InputNumber,
  Space,
  Collapsible,
} from "@douyinfe/semi-ui";
import { IconBolt as IconRobot, IconStarStroked as IconMagicWand } from "@douyinfe/semi-icons";
import { get, post } from "@/utils/request";
import { slaService } from "@/services/sla";
import { useAI } from "@/hooks/useAI";
import { AISuggestionCard } from "@/components/ai";

const { Option } = Select;

interface ServiceCreateModalProps {
  visible: boolean;
  customerId: string;
  onCancel: () => void;
  onSuccess: () => void;
}

interface Archive {
  id: string;
  name: string;
  status: string;
}

interface SlaTemplate {
  id: string;
  name: string;
  responseTime: number;
  resolutionTime: number;
}

interface FormData {
  archiveId: string;
  slaTemplateId?: string;
  title: string;
  description: string;
  category: "MAINTENANCE" | "SUPPORT" | "UPGRADE" | "BUGFIX" | "CONSULTING" | "MONITORING";
  priority: "LOW" | "MEDIUM" | "HIGH" | "URGENT";
  customerContact?: string;
  estimatedHours?: number;
}

const DEFAULT_FORM_VALUES: FormData = {
  archiveId: "",
  title: "",
  description: "",
  category: "SUPPORT",
  priority: "MEDIUM",
};

const categoryOptions = [
  { label: "维护", value: "MAINTENANCE" },
  { label: "支持", value: "SUPPORT" },
  { label: "升级", value: "UPGRADE" },
  { label: "Bug修复", value: "BUGFIX" },
  { label: "咨询", value: "CONSULTING" },
  { label: "监控", value: "MONITORING" },
];

const priorityOptions = [
  { label: "低", value: "LOW" },
  { label: "中", value: "MEDIUM" },
  { label: "高", value: "HIGH" },
  { label: "紧急", value: "URGENT" },
];

export function ServiceCreateModal({
  visible,
  customerId,
  onCancel,
  onSuccess,
}: ServiceCreateModalProps) {
  const formRef = useRef<Form>(null);
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [archives, setArchives] = useState<Archive[]>([]);
  const [slaTemplates, setSlaTemplates] = useState<SlaTemplate[]>([]);

  // AI 相关状态
  const [showAISuggestions, setShowAISuggestions] = useState(false);
  const [currentDescription, setCurrentDescription] = useState("");

  // 初始化AI功能
  const ai = useAI({
    userId: "current-user", // TODO: 从用户上下文获取
    contextData: {
      customerId,
      customerType: "ENTERPRISE", // TODO: 从客户信息获取
    },
  });

  // 加载客户的项目档案列表
  const loadArchives = useCallback(async () => {
    if (!customerId) return;

    setLoading(true);
    try {
      const response = await get(`/api/v1/archives/customer/${customerId}`);
      if (response.success) {
        // 过滤出活跃状态的项目档案
        const activeArchives = response.data.filter(
          (archive: Archive) => archive.status === "ACTIVE" || archive.status === "MAINTENANCE"
        );
        setArchives(activeArchives);
      } else {
        console.warn("获取项目档案失败:", response.message);
        setArchives([]);
      }
    } catch (error) {
      console.error("获取项目档案失败:", error);
      setArchives([]);
    } finally {
      setLoading(false);
    }
  }, [customerId]);

  // 加载SLA模板列表
  const loadSlaTemplates = useCallback(async () => {
    try {
      const response = await slaService.getSlaTemplates({ limit: 50 });
      if (response.success && response.data) {
        // 处理分页响应数据结构
        setSlaTemplates(response.data.items || []);
      } else {
        console.warn("获取SLA模板失败:", response.message);
        setSlaTemplates([]);
      }
    } catch (error) {
      console.error("获取SLA模板失败:", error);
      setSlaTemplates([]);
    }
  }, []);

  useEffect(() => {
    if (visible) {
      loadArchives();
      loadSlaTemplates();
    }
  }, [visible, loadArchives, loadSlaTemplates]);

  const handleSubmit = useCallback(
    async (values: FormData) => {
      setSubmitting(true);
      try {
        const response = await post("/api/v1/services", {
          ...values,
          estimatedHours: values.estimatedHours || undefined,
        });

        if (response.success) {
          Toast.success(response.message || "服务工单创建成功");
          formRef.current?.formApi.setValues(DEFAULT_FORM_VALUES);
          onSuccess();
        } else {
          Toast.error(response.message || "创建失败");
        }
      } catch (error: any) {
        console.error("创建服务工单失败:", error);
        Toast.error(error.message || "创建失败，请稍后重试");
      } finally {
        setSubmitting(false);
      }
    },
    [onSuccess]
  );

  const handleCancel = useCallback(() => {
    formRef.current?.formApi.setValues(DEFAULT_FORM_VALUES);
    ai.clearSuggestions();
    setShowAISuggestions(false);
    setCurrentDescription("");
    onCancel();
  }, [onCancel, ai]);

  // 处理描述内容变化
  const handleDescriptionChange = useCallback(
    (value: string) => {
      setCurrentDescription(value);

      // 激进模式：自动触发AI分析
      if (ai.isAggressiveMode && value.trim().length > 10) {
        ai.debouncedAnalyze(value);
      }
    },
    [ai]
  );

  // 手动触发AI分析
  const handleAnalyzeWithAI = useCallback(async () => {
    if (!currentDescription.trim()) {
      Toast.warning("请先输入工单描述内容");
      return;
    }

    setShowAISuggestions(true);
    await ai.analyzeContent(currentDescription);
  }, [currentDescription, ai]);

  // 采用单个AI建议
  const handleAdoptSuggestion = useCallback(
    (field: string, value: string) => {
      const formApi = formRef.current?.formApi;
      if (!formApi) return;

      // 应用建议到表单
      switch (field) {
        case "title":
          formApi.setValue("title", value);
          break;
        case "category":
          formApi.setValue("category", value);
          break;
        case "priority":
          formApi.setValue("priority", value);
          break;
        case "slaTemplate":
          // 根据建议的模板名称查找对应的ID
          const matchedTemplate = slaTemplates.find(
            t => t.name.toLowerCase().includes(value.toLowerCase()) || t.id === value
          );
          if (matchedTemplate) {
            formApi.setValue("slaTemplateId", matchedTemplate.id);
          }
          break;
      }

      ai.adoptSuggestion(field, value);
      Toast.success(
        `已采用${field === "title" ? "标题" : field === "category" ? "类别" : field === "priority" ? "优先级" : "SLA模板"}建议`
      );
    },
    [ai, slaTemplates]
  );

  // 采用所有AI建议
  const handleAdoptAllSuggestions = useCallback(() => {
    const adoptedValues = ai.adoptAllSuggestions();
    const formApi = formRef.current?.formApi;

    if (!formApi || Object.keys(adoptedValues).length === 0) {
      Toast.warning("暂无可采用的建议");
      return;
    }

    let adoptedCount = 0;
    Object.entries(adoptedValues).forEach(([field, value]) => {
      switch (field) {
        case "title":
          formApi.setValue("title", value);
          adoptedCount++;
          break;
        case "category":
          formApi.setValue("category", value);
          adoptedCount++;
          break;
        case "priority":
          formApi.setValue("priority", value);
          adoptedCount++;
          break;
        case "slaTemplate":
          const matchedTemplate = slaTemplates.find(
            t => t.name.toLowerCase().includes(value.toLowerCase()) || t.id === value
          );
          if (matchedTemplate) {
            formApi.setValue("slaTemplateId", matchedTemplate.id);
            adoptedCount++;
          }
          break;
      }
    });

    Toast.success(`已采用 ${adoptedCount} 个AI建议`);
  }, [ai, slaTemplates]);

  return (
    <Modal
      title="新增服务工单"
      visible={visible}
      onCancel={handleCancel}
      width={800}
      footer={null}
      maskClosable={false}
    >
      <Form<FormData>
        ref={formRef}
        initValues={DEFAULT_FORM_VALUES}
        onSubmit={handleSubmit}
        labelPosition="left"
        labelWidth={100}
        labelAlign="left"
        style={{ marginTop: "20px" }}
      >
        <Row gutter={[16, 16]}>
          <Col span={24}>
            <Form.Select
              field="archiveId"
              label="项目档案"
              placeholder="请选择项目档案"
              loading={loading}
              rules={[{ required: true, message: "请选择项目档案" }]}
              style={{ width: "100%" }}
            >
              {archives.map(archive => (
                <Option key={archive.id} value={archive.id}>
                  {archive.name}
                </Option>
              ))}
            </Form.Select>
          </Col>

          <Col span={24}>
            <Form.Input
              field="title"
              label={
                <Space>
                  <span>工单标题</span>
                  {ai.suggestions.find(s => s.field === "title") && (
                    <span style={{ fontSize: "12px", color: "var(--semi-color-success)" }}>
                      🤖 AI建议
                    </span>
                  )}
                </Space>
              }
              placeholder={ai.isAggressiveMode ? "AI将自动生成标题建议" : "请输入工单标题"}
              rules={[
                { required: true, message: "请输入工单标题" },
                { max: 200, message: "标题不能超过200个字符" },
              ]}
            />
          </Col>

          <Col span={24}>
            <div>
              <div
                style={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "space-between",
                  marginBottom: 8,
                }}
              >
                <span className="semi-form-field-label">工单描述</span>
                {/* AI模式指示器 */}
                {!ai.isAIDisabled && (
                  <Space>
                    {ai.isAggressiveMode && (
                      <div
                        style={{
                          display: "flex",
                          alignItems: "center",
                          fontSize: "12px",
                          color: "var(--semi-color-success)",
                        }}
                      >
                        <IconRobot size="small" style={{ marginRight: 4 }} />
                        <span>AI智能模式</span>
                      </div>
                    )}
                    {ai.isUserTriggeredMode && (
                      <Button
                        theme="light"
                        type="primary"
                        size="small"
                        icon={<IconMagicWand />}
                        onClick={handleAnalyzeWithAI}
                        loading={ai.analyzing}
                        disabled={!currentDescription.trim()}
                      >
                        AI智能分析
                      </Button>
                    )}
                  </Space>
                )}
              </div>

              <Form.TextArea
                noLabel
                field="description"
                placeholder="请详细描述问题和需求"
                rows={4}
                maxCount={2000}
                rules={[{ required: true, message: "请输入工单描述" }]}
                onChange={handleDescriptionChange}
              />
            </div>
          </Col>

          {/* AI建议区域 */}
          {!ai.isAIDisabled && (showAISuggestions || ai.isAggressiveMode) && (
            <Col span={24}>
              <Collapsible
                isOpen={showAISuggestions || ai.suggestions.length > 0}
                collapseHeight={0}
              >
                <div style={{ marginTop: 8 }}>
                  <AISuggestionCard
                    analysis={ai.lastAnalysis}
                    loading={ai.analyzing}
                    error={ai.error}
                    onAdoptAll={handleAdoptAllSuggestions}
                    onAdoptSuggestion={handleAdoptSuggestion}
                    onRetryAnalysis={() => ai.analyzeContent(currentDescription)}
                    onClose={() => {
                      setShowAISuggestions(false);
                      ai.clearSuggestions();
                    }}
                  />
                </div>
              </Collapsible>
            </Col>
          )}

          <Col span={12}>
            <Form.Select
              field="category"
              label={
                <Space>
                  <span>服务类别</span>
                  {ai.suggestions.find(s => s.field === "category") && (
                    <span style={{ fontSize: "12px", color: "var(--semi-color-success)" }}>
                      🤖 AI建议
                    </span>
                  )}
                </Space>
              }
              rules={[{ required: true, message: "请选择服务类别" }]}
              style={{ width: "100%" }}
              placeholder={ai.isAggressiveMode ? "AI将自动选择类别" : "请选择服务类别"}
            >
              {categoryOptions.map(option => (
                <Option key={option.value} value={option.value}>
                  {option.label}
                </Option>
              ))}
            </Form.Select>
          </Col>

          <Col span={12}>
            <Form.Select
              field="priority"
              label={
                <Space>
                  <span>优先级</span>
                  {ai.suggestions.find(s => s.field === "priority") && (
                    <span style={{ fontSize: "12px", color: "var(--semi-color-success)" }}>
                      🤖 AI建议
                    </span>
                  )}
                </Space>
              }
              rules={[{ required: true, message: "请选择优先级" }]}
              style={{ width: "100%" }}
              placeholder={ai.isAggressiveMode ? "AI将自动评估优先级" : "请选择优先级"}
            >
              {priorityOptions.map(option => (
                <Option key={option.value} value={option.value}>
                  {option.label}
                </Option>
              ))}
            </Form.Select>
          </Col>

          <Col span={12}>
            <Form.Input
              field="customerContact"
              label="客户联系人"
              placeholder="请输入客户联系人"
              maxLength={100}
            />
          </Col>

          <Col span={12}>
            <Form.InputNumber
              field="estimatedHours"
              label="预估工时"
              placeholder="请输入预估工时"
              suffix="小时"
              min={0}
              max={9999}
              precision={1}
              style={{ width: "100%" }}
            />
          </Col>

          {Array.isArray(slaTemplates) && slaTemplates.length > 0 && (
            <Col span={24}>
              <Form.Select
                field="slaTemplateId"
                label={
                  <Space>
                    <span>SLA模板</span>
                    {ai.suggestions.find(s => s.field === "slaTemplate") && (
                      <span style={{ fontSize: "12px", color: "var(--semi-color-success)" }}>
                        🤖 AI建议
                      </span>
                    )}
                  </Space>
                }
                placeholder={ai.isAggressiveMode ? "AI将自动推荐SLA模板" : "请选择SLA模板（可选）"}
                style={{ width: "100%" }}
              >
                {slaTemplates.map(template => (
                  <Option key={template.id} value={template.id}>
                    {template.name} (响应: {template.responseTime}h, 解决: {template.resolutionTime}
                    h)
                  </Option>
                ))}
              </Form.Select>
            </Col>
          )}
        </Row>

        <div
          style={{
            display: "flex",
            justifyContent: "flex-end",
            gap: "12px",
            marginTop: "24px",
            paddingTop: "24px",
            borderTop: "1px solid var(--semi-color-border)",
          }}
        >
          <Button onClick={handleCancel} disabled={submitting}>
            取消
          </Button>
          <Button theme="solid" type="primary" htmlType="submit" loading={submitting}>
            创建工单
          </Button>
        </div>
      </Form>
    </Modal>
  );
}
