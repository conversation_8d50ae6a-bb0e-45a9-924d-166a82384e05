import React, { useState, useRef, useCallback } from "react";
import {
  Form,
  Input,
  TextArea,
  InputNumber,
  DatePicker,
  Select,
  Button,
  Toast,
  Row,
  Col,
} from "@douyinfe/semi-ui";
import { post } from "@/utils/request";
import { FormRichTextArea } from "@/components/common";
const { Option } = Select;

interface WorkLogCreateFormProps {
  serviceId: string;
  onSuccess: () => void;
  onCancel?: () => void;
}

interface WorkLogFormData {
  description: string;
  workHours: number;
  workDate: Date;
  category:
    | "ANALYSIS"
    | "IMPLEMENTATION"
    | "TESTING"
    | "DOCUMENTATION"
    | "COMMUNICATION"
    | "MAINTENANCE"
    | "SUPPORT"
    | "OTHER";
}

const DEFAULT_FORM_VALUES: WorkLogFormData = {
  description: "",
  workHours: 1,
  workDate: new Date(),
  category: "MAINTENANCE",
};

const categoryOptions = [
  { label: "分析", value: "ANALYSIS" },
  { label: "实施", value: "IMPLEMENTATION" },
  { label: "测试", value: "TESTING" },
  { label: "文档", value: "DOCUMENTATION" },
  { label: "沟通", value: "COMMUNICATION" },
  { label: "维护", value: "MAINTENANCE" },
  { label: "支持", value: "SUPPORT" },
  { label: "其他", value: "OTHER" },
];

export function WorkLogCreateForm({ serviceId, onSuccess, onCancel }: WorkLogCreateFormProps) {
  const formRef = useRef<Form>(null);
  const [submitting, setSubmitting] = useState(false);

  const handleSubmit = useCallback(
    async (values: WorkLogFormData) => {
      setSubmitting(true);

      console.log("提交工作日志开始:", {
        serviceId,
        values,
        currentUrl: window.location.href,
      });

      try {
        const response = await post(`/api/v1/services/${serviceId}/work-logs`, {
          description: values.description,
          workHours: values.workHours,
          workDate: values.workDate.toISOString(),
          category: values.category,
        });

        console.log("工作日志提交响应:", response);

        if (response.success) {
          Toast.success("工作日志添加成功");
          formRef.current?.formApi.setValues(DEFAULT_FORM_VALUES);
          onSuccess();
        } else {
          Toast.error(response.message || "添加工作日志失败");
        }
      } catch (error: any) {
        console.error("添加工作日志失败:", error);
        console.error("错误详情:", {
          status: error.status,
          message: error.message,
          stack: error.stack,
        });

        // 处理认证错误，不显示错误信息，因为会自动跳转登录页
        if (error.status === 401) {
          console.log("认证失败，将跳转到登录页");
          return; // 不显示错误提示，让认证模块处理
        }

        Toast.error(error.message || "添加工作日志失败，请稍后重试");
      } finally {
        setSubmitting(false);
        console.log("工作日志提交结束");
      }
    },
    [serviceId, onSuccess]
  );

  const handleCancel = useCallback(() => {
    formRef.current?.formApi.setValues(DEFAULT_FORM_VALUES);
    onCancel?.();
  }, [onCancel]);

  return (
    <div
      style={{
        padding: "16px",
        background: "var(--semi-color-bg-2)",
        borderRadius: "6px",
        border: "1px solid var(--semi-color-border)",
        marginBottom: "16px",
      }}
    >
      <Form<WorkLogFormData>
        ref={formRef}
        initValues={DEFAULT_FORM_VALUES}
        onSubmit={handleSubmit}
        labelPosition="left"
        labelWidth={80}
        labelAlign="left"
      >
        <Row gutter={[16, 16]}>
          <Col span={24}>
            <FormRichTextArea
              field="description"
              label="工作内容"
              placeholder="请详细描述本次工作内容"
              height={300}
              maxCount={1000}
              showWordCount={true}
              rules={[{ required: true, message: "请输入工作内容" }]}
            />
          </Col>

          <Col span={8}>
            <Form.InputNumber
              field="workHours"
              label="工作时长"
              placeholder="工时"
              suffix="小时"
              min={0.1}
              max={24}
              step={0.5}
              precision={1}
              rules={[
                { required: true, message: "请输入工作时长" },
                { type: "number", min: 0.1, message: "工作时长不能少于0.1小时" },
              ]}
              style={{ width: "100%" }}
            />
          </Col>

          <Col span={8}>
            <Form.DatePicker
              field="workDate"
              label="工作日期"
              placeholder="选择工作日期"
              type="date"
              rules={[{ required: true, message: "请选择工作日期" }]}
              style={{ width: "100%" }}
            />
          </Col>

          <Col span={8}>
            <Form.Select
              field="category"
              label="工作类别"
              placeholder="选择工作类别"
              rules={[{ required: true, message: "请选择工作类别" }]}
              style={{ width: "100%" }}
            >
              {categoryOptions.map(option => (
                <Option key={option.value} value={option.value}>
                  {option.label}
                </Option>
              ))}
            </Form.Select>
          </Col>
        </Row>

        <div
          style={{
            display: "flex",
            justifyContent: "flex-end",
            gap: "8px",
            marginTop: "16px",
            paddingTop: "16px",
            borderTop: "1px solid var(--semi-color-border)",
          }}
        >
          {onCancel && (
            <Button onClick={handleCancel} disabled={submitting} size="small">
              取消
            </Button>
          )}
          <Button theme="solid" type="primary" htmlType="submit" loading={submitting} size="small">
            添加工作日志
          </Button>
        </div>
      </Form>
    </div>
  );
}
