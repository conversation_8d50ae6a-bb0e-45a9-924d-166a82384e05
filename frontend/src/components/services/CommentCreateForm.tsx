import React, { useState, useRef, useCallback } from "react";
import { Form, Input, TextArea, Checkbox, Button, Toast, Typography } from "@douyinfe/semi-ui";
import { post } from "@/utils/request";
import { FormRichTextArea } from "@/components/common";
const { Text } = Typography;

interface CommentCreateFormProps {
  serviceId: string;
  onSuccess: () => void;
  onCancel?: () => void;
}

interface CommentFormData {
  content: string;
  isInternal: boolean;
}

const DEFAULT_FORM_VALUES: CommentFormData = {
  content: "",
  isInternal: false,
};

export function CommentCreateForm({ serviceId, onSuccess, onCancel }: CommentCreateFormProps) {
  const formRef = useRef<Form>(null);
  const [submitting, setSubmitting] = useState(false);

  const handleSubmit = useCallback(
    async (values: CommentFormData) => {
      setSubmitting(true);

      console.log("提交评论开始:", {
        serviceId,
        values,
        currentUrl: window.location.href,
      });

      try {
        const response = await post(`/api/v1/services/${serviceId}/comments`, {
          content: values.content,
          isInternal: values.isInternal,
        });

        console.log("评论提交响应:", response);

        if (response.success) {
          Toast.success("评论添加成功");
          formRef.current?.formApi.setValues(DEFAULT_FORM_VALUES);
          onSuccess();
        } else {
          Toast.error(response.message || "添加评论失败");
        }
      } catch (error: any) {
        console.error("添加评论失败:", error);
        console.error("错误详情:", {
          status: error.status,
          message: error.message,
          stack: error.stack,
        });

        // 处理认证错误，不显示错误信息，因为会自动跳转登录页
        if (error.status === 401) {
          console.log("认证失败，将跳转到登录页");
          return; // 不显示错误提示，让认证模块处理
        }

        Toast.error(error.message || "添加评论失败，请稍后重试");
      } finally {
        setSubmitting(false);
        console.log("评论提交结束");
      }
    },
    [serviceId, onSuccess]
  );

  const handleCancel = useCallback(() => {
    formRef.current?.formApi.setValues(DEFAULT_FORM_VALUES);
    onCancel?.();
  }, [onCancel]);

  return (
    <div
      style={{
        padding: "16px",
        background: "var(--semi-color-bg-2)",
        borderRadius: "6px",
        border: "1px solid var(--semi-color-border)",
        marginBottom: "16px",
      }}
    >
      <Form
        ref={formRef}
        initValues={DEFAULT_FORM_VALUES}
        onSubmit={handleSubmit}
        labelPosition="top"
      >
        <FormRichTextArea
          field="content"
          label="评论内容"
          placeholder="请输入评论内容..."
          height={150}
          maxCount={2000}
          showWordCount={true}
          rules={[{ required: true, message: "请输入评论内容" }]}
        />

        <div style={{ marginTop: "12px", marginBottom: "16px" }}>
          <Form.Checkbox field="isInternal" label="内部评论">
            仅内部可见（客户无法查看此评论）
          </Form.Checkbox>
          <Text type="tertiary" size="small" style={{ display: "block", marginTop: "4px" }}>
            内部评论用于团队内部沟通，不会显示给客户
          </Text>
        </div>

        <div
          style={{
            display: "flex",
            justifyContent: "flex-end",
            gap: "8px",
            paddingTop: "12px",
            borderTop: "1px solid var(--semi-color-border)",
          }}
        >
          {onCancel && (
            <Button onClick={handleCancel} disabled={submitting} size="small">
              取消
            </Button>
          )}
          <Button theme="solid" type="primary" htmlType="submit" loading={submitting} size="small">
            发表评论
          </Button>
        </div>
      </Form>
    </div>
  );
}
