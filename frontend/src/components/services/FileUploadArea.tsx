import React, { useState, useCallback } from "react";
import { Upload, Button, Toast, Typography, List, Progress, Space } from "@douyinfe/semi-ui";
import { useAuthStore } from "@/stores/auth";
import { IconUpload, IconFile, IconDelete, IconDownload } from "@douyinfe/semi-icons";
import { upload } from "@/utils/request";

const { Text } = Typography;

interface FileUploadAreaProps {
  serviceId: string;
  onSuccess: () => void;
}

interface UploadFile {
  uid: string;
  name: string;
  status: "uploading" | "success" | "error";
  percent?: number;
  response?: any;
  error?: string;
  size: number;
}

export function FileUploadArea({ serviceId, onSuccess }: FileUploadAreaProps) {
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [uploading, setUploading] = useState(false);

  const beforeUpload = useCallback(({ file, fileList }: { file: any; fileList: any[] }) => {
    // 调试：打印完整的文件对象结构
    console.log("beforeUpload 参数:", { file, fileList });
    console.log("file 对象属性:", Object.keys(file));
    console.log("file.fileInstance:", file.fileInstance);

    // 获取实际的文件对象
    const fileInstance = file.fileInstance;
    if (!fileInstance) {
      Toast.error("无法获取文件信息");
      return {
        autoRemove: true,
        shouldUpload: false,
        status: "validateFail",
      };
    }

    console.log("fileInstance 属性:", Object.keys(fileInstance));
    console.log("文件大小检查:", {
      fileName: fileInstance.name,
      fileSize: fileInstance.size,
      fileType: fileInstance.type,
      fileSizeMB: (fileInstance.size / 1024 / 1024).toFixed(2),
    });

    // 检查文件大小
    const fileSizeMB = fileInstance.size / 1024 / 1024;
    const isValidSize = fileSizeMB <= 50; // 限制50MB
    if (!isValidSize) {
      Toast.error(`文件大小不能超过50MB，当前文件大小：${fileSizeMB.toFixed(2)}MB`);
      return {
        autoRemove: true,
        shouldUpload: false,
        status: "validateFail",
      };
    }

    // 检查文件类型
    const allowedTypes = [
      "image/jpeg",
      "image/png",
      "image/gif",
      "application/pdf",
      "application/msword",
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
      "application/vnd.ms-excel",
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      "text/plain",
      "application/zip",
      "application/x-rar-compressed",
    ];

    const isValidType = allowedTypes.includes(fileInstance.type);
    if (!isValidType) {
      Toast.error(
        `不支持的文件类型：${fileInstance.type || "未知"}，请上传图片、文档、表格或压缩包`
      );
      return {
        autoRemove: true,
        shouldUpload: false,
        status: "validateFail",
      };
    }

    // 验证通过，允许上传
    return {
      autoRemove: false,
      fileInstance,
      shouldUpload: true,
    };
  }, []);

  const customRequest = useCallback(
    async ({ file, onProgress, onSuccess: onUploadSuccess, onError }: any) => {
      setUploading(true);

      try {
        // 检查用户认证状态和权限
        const { user, isAuthenticated, hasPermission } = useAuthStore.getState();

        if (!isAuthenticated || !user) {
          throw {
            code: "NO_TOKEN",
            message: "请先登录后再上传文件",
            status: 401,
          };
        }

        if (!hasPermission("service:write")) {
          throw {
            code: "ACCESS_DENIED",
            message: "您没有权限上传附件",
            status: 403,
          };
        }

        // 调试：打印 customRequest 中的文件对象
        console.log("customRequest 文件对象:", file);
        console.log("customRequest 文件属性:", Object.keys(file));

        // 根据 Semi-UI 官方文档，使用 file.fileInstance 获取原始文件
        const actualFile = file.fileInstance || file;
        console.log("actualFile:", actualFile);

        if (!actualFile) {
          throw new Error("无法获取文件对象");
        }
        // 模拟上传进度
        const progressInterval = setInterval(() => {
          const progress = Math.min(Math.random() * 30 + 10, 90);
          onProgress({ percent: progress });
        }, 200);

        // 使用统一的上传函数
        const result = await upload("/api/v1/upload/service-attachment", actualFile, "file", {
          serviceId,
        });

        clearInterval(progressInterval);

        if (result.success) {
          onProgress({ percent: 100 });
          onUploadSuccess(result.data);
          Toast.success("文件上传成功");
          onSuccess(); // 刷新附件列表
        } else {
          throw new Error(result.message || "上传失败");
        }
      } catch (error: any) {
        console.error("文件上传失败:", error);

        // 根据错误类型提供不同的提示信息
        let errorMessage = "文件上传失败";

        if (error.code === "ACCESS_DENIED") {
          errorMessage = "权限不足，无法上传附件";
        } else if (error.code === "NO_TOKEN") {
          errorMessage = "登录状态已过期，请重新登录";
        } else if (error.status === 401) {
          errorMessage = "认证失败，请检查登录状态";
        } else if (error.status === 403) {
          errorMessage = "没有权限上传附件";
        } else if (error.message) {
          errorMessage = error.message;
        }

        onError(error);
        Toast.error(errorMessage);
      } finally {
        setUploading(false);
      }
    },
    [serviceId, onSuccess]
  );

  const handleChange = useCallback(({ fileList: newFileList }: any) => {
    setFileList(newFileList);
  }, []);

  const handleRemove = useCallback(
    (file: any) => {
      const newFileList = fileList.filter(item => item.uid !== file.uid);
      setFileList(newFileList);
    },
    [fileList]
  );

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return "0 B";
    const k = 1024;
    const sizes = ["B", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`;
  };

  return (
    <div
      style={{
        padding: "16px",
        background: "var(--semi-color-bg-2)",
        borderRadius: "6px",
        border: "1px solid var(--semi-color-border)",
        marginBottom: "16px",
      }}
    >
      <div style={{ marginBottom: "12px" }}>
        <Text strong>上传附件</Text>
        <Text type="tertiary" size="small" style={{ display: "block", marginTop: "4px" }}>
          支持图片、文档、表格、压缩包等格式，单个文件不超过50MB
        </Text>
      </div>

      <Upload
        action="/api/v1/upload/service-attachment"
        beforeUpload={beforeUpload}
        customRequest={customRequest}
        onChange={handleChange}
        onRemove={handleRemove}
        fileList={fileList as any}
        multiple
        directory={false}
        dragMainText="点击上传文件或拖拽文件到此区域"
        dragSubText="支持单个或批量上传"
        showUploadList={false}
        style={{ width: "100%" }}
      >
        <div
          style={{
            border: "2px dashed var(--semi-color-border)",
            borderRadius: "6px",
            padding: "20px",
            textAlign: "center",
            cursor: "pointer",
            transition: "border-color 0.3s",
          }}
        >
          <IconUpload size="large" style={{ color: "var(--semi-color-text-2)" }} />
          <div>
            <Text style={{ color: "var(--semi-color-text-1)", marginTop: "8px", display: "block" }}>
              点击上传文件或拖拽文件到此区域
            </Text>
            <Text type="tertiary" size="small">
              支持图片、文档、表格、压缩包等格式，单个文件不超过50MB
            </Text>
          </div>
        </div>
      </Upload>

      {fileList.length > 0 && (
        <div style={{ marginTop: "16px" }}>
          <Text strong size="small" style={{ marginBottom: "8px", display: "block" }}>
            上传队列
          </Text>
          <List
            size="small"
            dataSource={fileList}
            renderItem={(file: UploadFile) => (
              <List.Item
                main={
                  <div
                    style={{ display: "flex", alignItems: "center", gap: "12px", width: "100%" }}
                  >
                    <IconFile />
                    <div style={{ flex: 1, minWidth: 0 }}>
                      <Text strong style={{ fontSize: "12px" }}>
                        {file.name}
                      </Text>
                      {file.status === "uploading" && file.percent && (
                        <Progress
                          percent={file.percent}
                          size="small"
                          showInfo={false}
                          style={{ marginTop: "4px" }}
                        />
                      )}
                      {file.status === "error" && (
                        <Text
                          type="danger"
                          size="small"
                          style={{ display: "block", marginTop: "2px" }}
                        >
                          {file.error || "上传失败"}
                        </Text>
                      )}
                      {file.status === "success" && (
                        <Text
                          type="success"
                          size="small"
                          style={{ display: "block", marginTop: "2px" }}
                        >
                          上传成功
                        </Text>
                      )}
                    </div>
                    <Space>
                      {file.status === "success" && (
                        <Button
                          theme="borderless"
                          size="small"
                          icon={<IconDownload />}
                          onClick={() => {
                            if (file.response?.filePath) {
                              window.open(
                                `/api/v1/upload/attachments/${file.response.id}/download`,
                                "_blank"
                              );
                            }
                          }}
                        />
                      )}
                      <Button
                        theme="borderless"
                        size="small"
                        type="danger"
                        icon={<IconDelete />}
                        onClick={() => handleRemove(file)}
                      />
                    </Space>
                  </div>
                }
              />
            )}
            style={{
              background: "var(--semi-color-bg-0)",
              borderRadius: "4px",
              border: "1px solid var(--semi-color-border)",
            }}
          />
        </div>
      )}
    </div>
  );
}
