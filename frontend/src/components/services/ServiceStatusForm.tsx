import React, { useState, useRef, useCallback } from "react";
import { Form, Select, TextArea, Button, Toast, Typography, Space } from "@douyinfe/semi-ui";
import { put } from "@/utils/request";

const { Text } = Typography;
const { Option } = Select;

interface ServiceStatusFormProps {
  serviceId: string;
  currentStatus: string;
  onSuccess: () => void;
  onCancel?: () => void;
}

interface StatusFormData {
  status: "PENDING" | "IN_PROGRESS" | "WAITING_CUSTOMER" | "RESOLVED" | "CLOSED";
  statusNote?: string;
}

const statusOptions = [
  { label: "待处理", value: "PENDING" },
  { label: "处理中", value: "IN_PROGRESS" },
  { label: "等待客户", value: "WAITING_CUSTOMER" },
  { label: "已解决", value: "RESOLVED" },
  { label: "已关闭", value: "CLOSED" },
];

// 状态流转规则
const statusFlow: Record<string, string[]> = {
  PENDING: ["IN_PROGRESS", "CLOSED"],
  IN_PROGRESS: ["WAITING_CUSTOMER", "RESOLVED", "CLOSED"],
  WAITING_CUSTOMER: ["IN_PROGRESS", "RESOLVED", "CLOSED"],
  RESOLVED: ["CLOSED", "IN_PROGRESS"],
  CLOSED: ["IN_PROGRESS"], // 只有管理员可以重新打开已关闭的工单
};

export function ServiceStatusForm({
  serviceId,
  currentStatus,
  onSuccess,
  onCancel,
}: ServiceStatusFormProps) {
  const formRef = useRef<Form>(null);
  const [submitting, setSubmitting] = useState(false);

  // 获取可选择的状态
  const getAvailableStatuses = () => {
    const allowedStatuses = statusFlow[currentStatus] || [];
    return statusOptions.filter(
      option => allowedStatuses.includes(option.value) || option.value === currentStatus
    );
  };

  const handleSubmit = useCallback(
    async (values: StatusFormData) => {
      setSubmitting(true);

      console.log("更新工单状态开始:", {
        serviceId,
        values,
        currentUrl: window.location.href,
      });

      try {
        const response = await put(`/api/v1/services/${serviceId}`, {
          status: values.status,
          statusNote: values.statusNote,
          operationType: "STATUS_CHANGE",
          operationNote: values.statusNote,
        });

        console.log("状态更新响应:", response);

        if (response.success) {
          Toast.success("工单状态更新成功");
          onSuccess();
        } else {
          Toast.error(response.message || "状态更新失败");
        }
      } catch (error: any) {
        console.error("状态更新失败:", error);
        console.error("错误详情:", {
          status: error.status,
          message: error.message,
          stack: error.stack,
        });

        // 处理认证错误
        if (error.status === 401) {
          console.log("认证失败，将跳转到登录页");
          return;
        }

        Toast.error(error.message || "状态更新失败，请稍后重试");
      } finally {
        setSubmitting(false);
        console.log("状态更新结束");
      }
    },
    [serviceId, onSuccess]
  );

  const handleCancel = useCallback(() => {
    onCancel?.();
  }, [onCancel]);

  const availableStatuses = getAvailableStatuses();

  return (
    <div
      style={{
        padding: "16px",
        background: "var(--semi-color-bg-2)",
        borderRadius: "6px",
        border: "1px solid var(--semi-color-border)",
        marginBottom: "16px",
      }}
    >
      <Form
        ref={formRef}
        initValues={{ status: currentStatus }}
        onSubmit={handleSubmit}
        labelPosition="left"
        labelWidth={80}
        labelAlign="left"
      >
        <Form.Select
          field="status"
          label="新状态"
          placeholder="选择新的工单状态"
          rules={[{ required: true, message: "请选择工单状态" }]}
          style={{ width: "100%" }}
        >
          {availableStatuses.map(option => (
            <Option key={option.value} value={option.value}>
              {option.label}
            </Option>
          ))}
        </Form.Select>

        <Form.TextArea
          field="statusNote"
          label="状态说明"
          placeholder="请输入状态变更说明（可选）"
          rows={3}
          maxCount={500}
          showClear
        />

        <div
          style={{
            display: "flex",
            justifyContent: "flex-end",
            gap: "8px",
            marginTop: "16px",
            paddingTop: "16px",
            borderTop: "1px solid var(--semi-color-border)",
          }}
        >
          {onCancel && (
            <Button onClick={handleCancel} disabled={submitting} size="small">
              取消
            </Button>
          )}
          <Button theme="solid" type="primary" htmlType="submit" loading={submitting} size="small">
            更新状态
          </Button>
        </div>
      </Form>

      <div style={{ marginTop: "12px" }}>
        <Text type="tertiary" size="small">
          状态说明：状态变更将记录到工单历史中，并可能触发相关通知
        </Text>
      </div>
    </div>
  );
}
