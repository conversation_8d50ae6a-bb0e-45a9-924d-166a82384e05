import React, { useState, useEffect } from "react";
import {
  SideSheet,
  Typography,
  Card,
  Tag,
  Row,
  Col,
  Spin,
  Empty,
  Button,
  Descriptions,
  Table,
  Toast,
  Space,
  Timeline,
  Progress,
  Badge,
  Tabs,
  Collapsible,
} from "@douyinfe/semi-ui";
import {
  IconClose,
  IconTicketCodeStroked,
  IconUser,
  IconCode,
  IconClock,
  IconTickCircle,
  IconRefresh,
  IconComment,
  IconFile,
  IconCalendar,
  IconPlus,
  IconChevronDown,
  IconChevronUp,
  IconHistory,
  IconEyeOpened,
  IconDownload,
} from "@douyinfe/semi-icons";
import { get } from "@/utils/request";
import { serviceService } from "@/services/service";
import { SLAStatusCard } from "./SLAStatusCard";
import { WorkLogCreateForm } from "./WorkLogCreateForm";
import { CommentCreateForm } from "./CommentCreateForm";
import { FileUploadArea } from "./FileUploadArea";
import { ServiceStatusForm } from "./ServiceStatusForm";
import { ServiceTransferForm } from "./ServiceTransferForm";
import { ServiceOperationHistory } from "./ServiceOperationHistory";
import { AttachmentPreview, RichTextDisplay, SimpleRichTextDisplay } from "@/components/common";

const { Title, Text, Paragraph } = Typography;
const { TabPane } = Tabs;

interface ServiceDetailSideSheetProps {
  visible: boolean;
  serviceId: string | null;
  onClose: () => void;
  onDataChanged?: () => void; // 数据变化时的回调函数
}

interface Service {
  id: string;
  ticketNumber: string;
  title: string;
  description: string;
  category: string;
  priority: string;
  status: string;
  customerContact?: string;
  estimatedHours?: number;
  actualHours?: number;
  resolution?: string;
  customerFeedback?: string;
  satisfaction?: number;
  createdAt: string;
  updatedAt: string;
  startTime?: string;
  endTime?: string;
  firstResponseAt?: string;
  archive: {
    id: string;
    name: string;
    customer: {
      id: string;
      name: string;
      company?: string;
      level: string;
    };
  };
  assignedUser?: {
    id: string;
    username: string;
    fullName?: string;
  };
  createdByUser: {
    id: string;
    username: string;
    fullName?: string;
  };
  slaTemplate?: {
    id: string;
    name: string;
    responseTime: number;
    resolutionTime: number;
  };
  workLogs?: Array<{
    id: string;
    description: string;
    workHours: number;
    workDate: string;
    category: string;
    createdAt: string;
    user: {
      username: string;
      fullName?: string;
    };
  }>;
  comments?: Array<{
    id: string;
    content: string;
    isInternal: boolean;
    createdAt: string;
    author: {
      username: string;
      fullName?: string;
    };
  }>;
  attachments?: Array<{
    id: string;
    filename: string;
    originalName: string;
    fileSize: number;
    filePath: string;
    uploadedAt: string;
    uploader: {
      username: string;
      fullName?: string;
    };
  }>;
  operationHistory?: Array<{
    id: string;
    type: "STATUS_CHANGE" | "TRANSFER" | "CREATE" | "UPDATE";
    description: string;
    fromValue?: string;
    toValue?: string;
    note?: string;
    createdAt: string;
    user: {
      username: string;
      fullName?: string;
    };
  }>;
  _count?: {
    workLogs: number;
    attachments: number;
    comments: number;
  };
}

// 状态配置
const statusConfig = {
  PENDING: { text: "待处理", color: "orange", progress: 0 },
  IN_PROGRESS: { text: "处理中", color: "blue", progress: 40 },
  WAITING_CUSTOMER: { text: "等待客户", color: "yellow", progress: 70 },
  RESOLVED: { text: "已解决", color: "green", progress: 90 },
  CLOSED: { text: "已关闭", color: "grey", progress: 100 },
} as const;

const priorityConfig = {
  LOW: { text: "低", color: "grey" },
  MEDIUM: { text: "中", color: "blue" },
  HIGH: { text: "高", color: "orange" },
  URGENT: { text: "紧急", color: "red" },
} as const;

const categoryConfig = {
  MAINTENANCE: { text: "维护", color: "blue" },
  SUPPORT: { text: "支持", color: "green" },
  UPGRADE: { text: "升级", color: "purple" },
  BUGFIX: { text: "Bug修复", color: "red" },
  CONSULTING: { text: "咨询", color: "cyan" },
  MONITORING: { text: "监控", color: "orange" },
} as const;

const workCategoryConfig = {
  ANALYSIS: { text: "分析", color: "blue" },
  IMPLEMENTATION: { text: "实施", color: "green" },
  TESTING: { text: "测试", color: "purple" },
  DOCUMENTATION: { text: "文档", color: "cyan" },
  COMMUNICATION: { text: "沟通", color: "orange" },
  MAINTENANCE: { text: "维护", color: "grey" },
  SUPPORT: { text: "支持", color: "yellow" },
  OTHER: { text: "其他", color: "grey" },
} as const;

export function ServiceDetailSideSheet({
  visible,
  serviceId,
  onClose,
  onDataChanged,
}: ServiceDetailSideSheetProps) {
  const [loading, setLoading] = useState(false);
  const [service, setService] = useState<Service | null>(null);

  // 表单显示状态
  const [showWorkLogForm, setShowWorkLogForm] = useState(false);
  const [showCommentForm, setShowCommentForm] = useState(false);
  const [showFileUpload, setShowFileUpload] = useState(false);
  const [showStatusForm, setShowStatusForm] = useState(false);
  const [showTransferForm, setShowTransferForm] = useState(false);

  // 附件预览状态
  const [previewVisible, setPreviewVisible] = useState(false);
  const [previewAttachment, setPreviewAttachment] = useState<any>(null);

  // PDF导出状态
  const [exportingPDF, setExportingPDF] = useState(false);

  useEffect(() => {
    if (visible && serviceId) {
      loadServiceDetail();
    }
  }, [visible, serviceId]);

  const loadServiceDetail = async () => {
    if (!serviceId) return;

    setLoading(true);
    try {
      const response = await get(`/api/v1/services/${serviceId}`);
      if (response.success) {
        const serviceData = response.data;

        // 如果后端没有返回操作历史，创建一些基本的历史记录
        if (!serviceData.operationHistory) {
          serviceData.operationHistory = [
            {
              id: `create-${serviceData.id}`,
              type: "CREATE" as const,
              description: "创建工单",
              note: "工单已创建",
              createdAt: serviceData.createdAt,
              user: serviceData.createdByUser,
            },
          ];

          // 如果有分配用户且不是创建者，添加分配记录
          if (
            serviceData.assignedUser &&
            serviceData.assignedUser.id !== serviceData.createdByUser.id
          ) {
            serviceData.operationHistory.push({
              id: `assign-${serviceData.id}`,
              type: "TRANSFER" as const,
              description: "分配工单",
              toValue: serviceData.assignedUser.fullName || serviceData.assignedUser.username,
              note: "工单已分配",
              createdAt: serviceData.createdAt,
              user: serviceData.createdByUser,
            });
          }
        }

        setService(serviceData);
      } else {
        Toast.error(response.message || "获取工单详情失败");
      }
    } catch (error: any) {
      console.error("获取工单详情失败:", error);

      // 处理认证错误
      if (error.status === 401) {
        console.log("检测到认证错误，准备重定向到登录页面");

        // 检查是否有新token（后端自动刷新）
        if (error.code === "TOKEN_UPDATE_FAILED") {
          // token更新失败，需要重新登录
          Toast.error("登录已过期，请重新登录");
          const { useAuthStore } = await import("@/stores/auth");
          useAuthStore.getState().clearAuthAndRedirect();
          return;
        }

        // 其他401错误，显示错误信息
        Toast.error("访问被拒绝，请检查权限或重新登录");
        return;
      }

      // 处理403权限错误
      if (error.status === 403) {
        Toast.error("无权限访问此工单");
        return;
      }

      // 其他错误显示提示
      Toast.error(error.message || "获取工单详情失败");
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = () => {
    loadServiceDetail();
  };

  const handlePreviewAttachment = (attachment: any) => {
    setPreviewAttachment(attachment);
    setPreviewVisible(true);
  };

  const handleClosePreview = () => {
    setPreviewVisible(false);
    setPreviewAttachment(null);
  };

  // 导出PDF报告（异步：后台生成，完成后通过通知下载）
  const handleExportPDF = async () => {
    if (!service) return;

    // 检查token是否存在 - 使用与API请求相同的逻辑
    const getAuthToken = (): string | null => {
      try {
        // 优先从内存中的AuthStore获取最新token
        if (typeof window !== "undefined" && (window as any).__AUTH_STORE__) {
          const token = (window as any).__AUTH_STORE__.getState?.()?.token;
          if (token) return token;
        }

        // 从localStorage获取
        const authStorage = localStorage.getItem("auth-storage");
        if (authStorage) {
          const parsed = JSON.parse(authStorage);
          return parsed.state?.token || null;
        }
      } catch (error) {
        console.warn("Failed to get auth token:", error);
      }

      return null;
    };

    const token = getAuthToken();
    if (!token) {
      Toast.error("请先登录");
      return;
    }

    console.log("🔄 开始导出PDF:", {
      serviceId: service.id,
      ticketNumber: service.ticketNumber,
      hasToken: !!token,
      tokenPrefix: token ? `${token.substring(0, 20)}...` : "null",
    });

    setExportingPDF(true);
    try {
      const { jobId } = await serviceService.exportServiceReportPDFAsync(service.id);
      console.log("📤 已启动异步导出任务:", { jobId });
      Toast.info("已开始生成报告，完成后会通过通知提醒下载。");
    } catch (error) {
      console.error("导出PDF失败:", error);

      // 更详细的错误处理
      if (error instanceof Error) {
        if (error.message.includes("访问令牌无效") || error.message.includes("401")) {
          Toast.error("登录已过期，请重新登录");
          // 可以在这里触发重新登录逻辑
        } else if (error.message.includes("403")) {
          Toast.error("无权限导出此工单报告");
        } else if (error.message.includes("404")) {
          Toast.error("工单不存在");
        } else {
          Toast.error(error.message);
        }
      } else {
        Toast.error("导出PDF失败");
      }
    } finally {
      setExportingPDF(false);
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return "0 B";
    const k = 1024;
    const sizes = ["B", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`;
  };

  const getSatisfactionText = (score?: number) => {
    if (!score) return "-";
    if (score >= 5) return "非常满意";
    if (score >= 4) return "满意";
    if (score >= 3) return "一般";
    if (score >= 2) return "不满意";
    return "非常不满意";
  };

  const getSatisfactionColor = (score?: number) => {
    if (!score) return "grey";
    if (score >= 4) return "green";
    if (score >= 3) return "blue";
    if (score >= 2) return "orange";
    return "red";
  };

  // 工作日志表格列定义
  const workLogColumns = [
    {
      title: "工作日期",
      dataIndex: "workDate",
      key: "workDate",
      width: 100,
      render: (date: string) => (
        <Text size="small">{new Date(date).toLocaleDateString("zh-CN")}</Text>
      ),
    },
    {
      title: "工作内容",
      dataIndex: "description",
      key: "description",
      render: (text: string) => (
        <SimpleRichTextDisplay
          content={text}
          placeholder="暂无内容"
          maxLength={80}
          showMoreModal={true}
          modalTitle="工作内容详情"
        />
      ),
    },
    {
      title: "类别",
      dataIndex: "category",
      key: "category",
      width: 80,
      render: (category: keyof typeof workCategoryConfig) => (
        <Tag color={workCategoryConfig[category]?.color || "grey"} size="small">
          {workCategoryConfig[category]?.text || category}
        </Tag>
      ),
    },
    {
      title: "工时",
      dataIndex: "workHours",
      key: "workHours",
      width: 60,
      render: (hours: number) => <Text strong>{hours}h</Text>,
    },
    {
      title: "记录人",
      dataIndex: "user",
      key: "user",
      width: 80,
      render: (user: any) => (
        <Text size="small" type="tertiary">
          {user.fullName || user.username}
        </Text>
      ),
    },
  ];

  if (!visible) return null;

  return (
    <SideSheet
      title={
        <div
          style={{
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
            width: "100%",
          }}
        >
          <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
            <IconTicketCodeStroked size="large" />
            <span>服务工单详情</span>
          </div>
          <Space>
            <Button
              theme="borderless"
              icon={<IconDownload />}
              onClick={handleExportPDF}
              loading={exportingPDF}
              size="small"
              disabled={!service}
            >
              导出PDF
            </Button>
            <Button
              theme="borderless"
              icon={<IconRefresh />}
              onClick={handleRefresh}
              loading={loading}
              size="small"
            />
            <Button theme="borderless" icon={<IconClose />} onClick={onClose} size="small" />
          </Space>
        </div>
      }
      visible={visible}
      onCancel={onClose}
      width={900}
      placement="right"
      mask={true}
      maskClosable={true}
      closable={false}
      bodyStyle={{ padding: "24px" }}
    >
      {loading ? (
        <div
          style={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            height: "200px",
          }}
        >
          <Spin size="large" />
        </div>
      ) : service ? (
        <div style={{ display: "flex", flexDirection: "column", gap: "24px" }}>
          {/* 工单基本信息 */}
          <Card
            title={
              <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
                <IconTicketCodeStroked />
                <span>工单信息</span>
              </div>
            }
            style={{ background: "var(--semi-color-bg-1)" }}
          >
            <div style={{ marginBottom: "16px" }}>
              <div
                style={{ display: "flex", alignItems: "center", gap: "12px", marginBottom: "8px" }}
              >
                <Text strong style={{ fontFamily: "monospace", fontSize: "16px" }}>
                  {service.ticketNumber}
                </Text>
                <Tag
                  color={statusConfig[service.status as keyof typeof statusConfig].color}
                  size="large"
                >
                  {statusConfig[service.status as keyof typeof statusConfig].text}
                </Tag>
                <Tag color={priorityConfig[service.priority as keyof typeof priorityConfig].color}>
                  {priorityConfig[service.priority as keyof typeof priorityConfig].text}优先级
                </Tag>
                <Tag color={categoryConfig[service.category as keyof typeof categoryConfig].color}>
                  {categoryConfig[service.category as keyof typeof categoryConfig].text}
                </Tag>
              </div>
              <Title heading={4} style={{ margin: 0, color: "var(--semi-color-text-0)" }}>
                {service.title}
              </Title>
            </div>

            {/* 进度条 */}
            <div style={{ marginBottom: "16px" }}>
              <div
                style={{ display: "flex", alignItems: "center", gap: "8px", marginBottom: "8px" }}
              >
                <IconTickCircle size="small" />
                <Text type="secondary" size="small">
                  工单进度
                </Text>
              </div>
              <Progress
                percent={statusConfig[service.status as keyof typeof statusConfig].progress}
                stroke={statusConfig[service.status as keyof typeof statusConfig].color}
                showInfo={false}
                size="small"
              />
            </div>

            <Descriptions
              data={[
                {
                  key: "工单描述",
                  value: (
                    <SimpleRichTextDisplay
                      content={service.description}
                      placeholder="暂无内容"
                      maxLength={80}
                      showMoreModal={true}
                      modalTitle="工作内容详情"
                    />
                  ),
                },
                {
                  key: "客户联系人",
                  value: service.customerContact || "-",
                },
                {
                  key: "预估工时",
                  value: service.estimatedHours ? `${service.estimatedHours}小时` : "-",
                },
                {
                  key: "实际工时",
                  value: service.actualHours ? `${service.actualHours}小时` : "-",
                },
                {
                  key: "分配给",
                  value:
                    service.assignedUser?.fullName || service.assignedUser?.username || "未分配",
                },
                {
                  key: "创建人",
                  value: service.createdByUser.fullName || service.createdByUser.username,
                },
                {
                  key: "创建时间",
                  value: new Date(service.createdAt).toLocaleString("zh-CN"),
                },
                {
                  key: "更新时间",
                  value: new Date(service.updatedAt).toLocaleString("zh-CN"),
                },
              ]}
              row
              size="small"
            />

            {/* 工单操作按钮 */}
            <div
              style={{ marginTop: "16px", display: "flex", gap: "8px", justifyContent: "flex-end" }}
            >
              <Button
                theme="light"
                type="primary"
                size="small"
                onClick={() => setShowStatusForm(!showStatusForm)}
              >
                {showStatusForm ? "取消状态调整" : "调整状态"}
              </Button>
              <Button
                theme="light"
                type="secondary"
                size="small"
                onClick={() => setShowTransferForm(!showTransferForm)}
              >
                {showTransferForm ? "取消转交" : "转交工单"}
              </Button>
            </div>

            {/* 状态调整表单 */}
            {showStatusForm && (
              <div style={{ marginTop: "16px" }}>
                <ServiceStatusForm
                  serviceId={service.id}
                  currentStatus={service.status}
                  onSuccess={() => {
                    setShowStatusForm(false);
                    loadServiceDetail(); // 重新加载数据
                    onDataChanged?.(); // 通知父组件数据发生变化
                  }}
                  onCancel={() => setShowStatusForm(false)}
                />
              </div>
            )}

            {/* 工单转交表单 */}
            {showTransferForm && (
              <div style={{ marginTop: "16px" }}>
                <ServiceTransferForm
                  serviceId={service.id}
                  currentAssignee={service.assignedUser}
                  onSuccess={() => {
                    setShowTransferForm(false);
                    loadServiceDetail(); // 重新加载数据
                    onDataChanged?.(); // 通知父组件数据发生变化
                  }}
                  onCancel={() => setShowTransferForm(false)}
                />
              </div>
            )}
          </Card>

          {/* 项目和客户信息 */}
          <Card
            title={
              <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
                <IconCode />
                <span>项目信息</span>
              </div>
            }
            style={{ background: "var(--semi-color-bg-1)" }}
          >
            <Row gutter={[16, 16]}>
              <Col span={12}>
                <div>
                  <Text type="secondary" size="small">
                    项目名称
                  </Text>
                  <div style={{ color: "var(--semi-color-text-0)", fontWeight: 500 }}>
                    {service.archive.name}
                  </div>
                </div>
              </Col>
              <Col span={12}>
                <div>
                  <Text type="secondary" size="small">
                    客户名称
                  </Text>
                  <div style={{ color: "var(--semi-color-text-0)", fontWeight: 500 }}>
                    {service.archive.customer.name}
                  </div>
                </div>
              </Col>
            </Row>
          </Card>

          {/* SLA 状态监控 */}
          <SLAStatusCard
            slaTemplate={service.slaTemplate}
            createdAt={service.createdAt}
            startTime={service.startTime}
            endTime={service.endTime}
            firstResponseAt={service.firstResponseAt}
            status={service.status}
          />

          {/* 解决方案和客户反馈 */}
          {(service.resolution || service.customerFeedback || service.satisfaction) && (
            <Card
              title={
                <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
                  <IconTickCircle />
                  <span>解决方案与反馈</span>
                </div>
              }
              style={{ background: "var(--semi-color-bg-1)" }}
            >
              {service.resolution && (
                <div style={{ marginBottom: "16px" }}>
                  <Text type="secondary" size="small">
                    解决方案
                  </Text>
                  <div style={{ marginTop: "4px" }}>
                    <RichTextDisplay content={service.resolution} placeholder="暂无解决方案" />
                  </div>
                </div>
              )}

              {service.customerFeedback && (
                <div style={{ marginBottom: "16px" }}>
                  <Text type="secondary" size="small">
                    客户反馈
                  </Text>
                  <div style={{ marginTop: "4px" }}>
                    <RichTextDisplay
                      content={service.customerFeedback}
                      placeholder="暂无客户反馈"
                    />
                  </div>
                </div>
              )}

              {service.satisfaction && (
                <div>
                  <Text type="secondary" size="small">
                    满意度评分
                  </Text>
                  <div
                    style={{ display: "flex", alignItems: "center", gap: "8px", marginTop: "4px" }}
                  >
                    <Badge
                      count={`${service.satisfaction}/5`}
                      style={{
                        backgroundColor: `var(--semi-color-${getSatisfactionColor(service.satisfaction)})`,
                      }}
                    />
                    <Text style={{ color: "var(--semi-color-text-0)" }}>
                      {getSatisfactionText(service.satisfaction)}
                    </Text>
                  </div>
                </div>
              )}
            </Card>
          )}

          {/* 详细信息标签页 */}
          <Card style={{ background: "var(--semi-color-bg-1)" }}>
            <Tabs type="line" tabPosition="top">
              <TabPane
                tab={
                  <span>
                    <IconCalendar style={{ marginRight: "4px" }} />
                    工作日志 ({service.workLogs?.length || 0})
                  </span>
                }
                itemKey="workLogs"
              >
                {/* 添加工作日志按钮 */}
                <div style={{ marginBottom: "16px", display: "flex", justifyContent: "flex-end" }}>
                  <Button
                    theme="light"
                    type="primary"
                    size="small"
                    icon={<IconPlus />}
                    onClick={() => setShowWorkLogForm(!showWorkLogForm)}
                  >
                    {showWorkLogForm ? "取消" : "添加工作日志"}
                  </Button>
                </div>

                {/* 工作日志创建表单 */}
                {showWorkLogForm && (
                  <WorkLogCreateForm
                    serviceId={service.id}
                    onSuccess={() => {
                      setShowWorkLogForm(false);
                      loadServiceDetail(); // 重新加载数据
                    }}
                    onCancel={() => setShowWorkLogForm(false)}
                  />
                )}

                {service.workLogs && service.workLogs.length > 0 ? (
                  <div>
                    <Table
                      columns={workLogColumns}
                      dataSource={service.workLogs}
                      rowKey="id"
                      pagination={false}
                      size="small"
                      style={{ background: "var(--semi-color-bg-0)" }}
                    />
                    <div style={{ marginTop: "16px", textAlign: "right" }}>
                      <Text type="secondary" size="small">
                        总工时:{" "}
                        <Text strong>
                          {service.workLogs.reduce((sum, log) => sum + log.workHours, 0)}小时
                        </Text>
                      </Text>
                    </div>
                  </div>
                ) : (
                  <Empty
                    image={<IconCalendar size="extra-large" />}
                    title="暂无工作日志"
                    description="该工单暂未记录任何工作日志"
                  />
                )}
              </TabPane>

              <TabPane
                tab={
                  <span>
                    <IconComment style={{ marginRight: "4px" }} />
                    评论 ({service.comments?.length || 0})
                  </span>
                }
                itemKey="comments"
              >
                {/* 添加评论按钮 */}
                <div style={{ marginBottom: "16px", display: "flex", justifyContent: "flex-end" }}>
                  <Button
                    theme="light"
                    type="primary"
                    size="small"
                    icon={<IconPlus />}
                    onClick={() => setShowCommentForm(!showCommentForm)}
                  >
                    {showCommentForm ? "取消" : "添加评论"}
                  </Button>
                </div>

                {/* 评论创建表单 */}
                {showCommentForm && (
                  <CommentCreateForm
                    serviceId={service.id}
                    onSuccess={() => {
                      setShowCommentForm(false);
                      loadServiceDetail(); // 重新加载数据
                    }}
                    onCancel={() => setShowCommentForm(false)}
                  />
                )}

                {service.comments && service.comments.length > 0 ? (
                  <Timeline>
                    {service.comments.map(comment => (
                      <Timeline.Item
                        key={comment.id}
                        time={new Date(comment.createdAt).toLocaleString("zh-CN")}
                        type={comment.isInternal ? "warning" : "success"}
                      >
                        <div>
                          <div
                            style={{
                              display: "flex",
                              alignItems: "center",
                              gap: "8px",
                              marginBottom: "4px",
                            }}
                          >
                            <Text strong size="small">
                              {comment.author.fullName || comment.author.username}
                            </Text>
                            {comment.isInternal && (
                              <Tag color="orange" size="small">
                                内部
                              </Tag>
                            )}
                          </div>
                          <SimpleRichTextDisplay
                            content={comment.content}
                            placeholder="暂无内容"
                            maxLength={150}
                            showMoreModal={true}
                            modalTitle="评论详情"
                          />
                        </div>
                      </Timeline.Item>
                    ))}
                  </Timeline>
                ) : (
                  <Empty
                    image={<IconComment size="extra-large" />}
                    title="暂无评论"
                    description="该工单暂未有任何评论"
                  />
                )}
              </TabPane>

              <TabPane
                tab={
                  <span>
                    <IconFile style={{ marginRight: "4px" }} />
                    附件 ({service.attachments?.length || 0})
                  </span>
                }
                itemKey="attachments"
              >
                {/* 添加附件按钮 */}
                <div style={{ marginBottom: "16px", display: "flex", justifyContent: "flex-end" }}>
                  <Button
                    theme="light"
                    type="primary"
                    size="small"
                    icon={<IconPlus />}
                    onClick={() => setShowFileUpload(!showFileUpload)}
                  >
                    {showFileUpload ? "取消上传" : "上传附件"}
                  </Button>
                </div>

                {/* 文件上传区域 */}
                {showFileUpload && (
                  <FileUploadArea
                    serviceId={service.id}
                    onSuccess={() => {
                      setShowFileUpload(false);
                      loadServiceDetail(); // 重新加载数据
                    }}
                  />
                )}

                {service.attachments && service.attachments.length > 0 ? (
                  <div style={{ display: "flex", flexDirection: "column", gap: "12px" }}>
                    {service.attachments.map(attachment => (
                      <div
                        key={attachment.id}
                        style={{
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "space-between",
                          padding: "12px",
                          background: "var(--semi-color-bg-2)",
                          borderRadius: "6px",
                          border: "1px solid var(--semi-color-border)",
                        }}
                      >
                        <div style={{ display: "flex", alignItems: "center", gap: "12px" }}>
                          <IconFile />
                          <div>
                            <Text strong style={{ color: "var(--semi-color-text-0)" }}>
                              {attachment.originalName || attachment.filename}
                            </Text>
                            <div>
                              <Text type="tertiary" size="small">
                                {formatFileSize(attachment.fileSize)} •
                                {attachment.uploader.fullName || attachment.uploader.username} •
                                {new Date(attachment.uploadedAt).toLocaleDateString("zh-CN")}
                              </Text>
                            </div>
                          </div>
                        </div>
                        <Space>
                          <Button
                            theme="borderless"
                            size="small"
                            icon={<IconEyeOpened />}
                            onClick={() => handlePreviewAttachment(attachment)}
                          >
                            预览
                          </Button>
                          <Button
                            theme="borderless"
                            size="small"
                            onClick={() => {
                              window.open(
                                `/api/v1/upload/attachments/${attachment.id}/download`,
                                "_blank"
                              );
                            }}
                          >
                            下载
                          </Button>
                        </Space>
                      </div>
                    ))}
                  </div>
                ) : (
                  <Empty
                    image={<IconFile size="extra-large" />}
                    title="暂无附件"
                    description="该工单暂未上传任何附件"
                  />
                )}
              </TabPane>

              <TabPane
                tab={
                  <span>
                    <IconHistory style={{ marginRight: "4px" }} />
                    操作历史 ({service.operationHistory?.length || 0})
                  </span>
                }
                itemKey="history"
              >
                <ServiceOperationHistory history={service.operationHistory || []} />
              </TabPane>
            </Tabs>
          </Card>
        </div>
      ) : (
        <div style={{ textAlign: "center", padding: "48px" }}>
          <Empty title="工单不存在" description="无法找到指定的服务工单" />
        </div>
      )}

      {/* 附件预览组件 */}
      <AttachmentPreview
        visible={previewVisible}
        attachment={previewAttachment}
        onClose={handleClosePreview}
      />
    </SideSheet>
  );
}
