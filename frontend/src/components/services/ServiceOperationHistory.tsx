import React from "react";
import { Timeline, Typography, Tag, Empty, Card } from "@douyinfe/semi-ui";
import { IconHistory, IconRefresh, IconUser, IconEdit, IconPlus } from "@douyinfe/semi-icons";

const { Text } = Typography;

interface OperationHistoryItem {
  id: string;
  type: "STATUS_CHANGE" | "TRANSFER" | "CREATE" | "UPDATE";
  description: string;
  fromValue?: string;
  toValue?: string;
  note?: string;
  createdAt: string;
  user: {
    username: string;
    fullName?: string;
  };
}

interface ServiceOperationHistoryProps {
  history: OperationHistoryItem[];
}

const operationTypeConfig = {
  STATUS_CHANGE: {
    text: "状态变更",
    color: "blue",
    icon: <IconRefresh />,
  },
  TRANSFER: {
    text: "工单转交",
    color: "purple",
    icon: <IconUser />,
  },
  CREATE: {
    text: "创建工单",
    color: "green",
    icon: <IconPlus />,
  },
  UPDATE: {
    text: "更新信息",
    color: "orange",
    icon: <IconEdit />,
  },
};

const statusTranslation: Record<string, string> = {
  PENDING: "待处理",
  IN_PROGRESS: "处理中",
  WAITING_CUSTOMER: "等待客户",
  RESOLVED: "已解决",
  CLOSED: "已关闭",
};

export function ServiceOperationHistory({ history }: ServiceOperationHistoryProps) {
  if (!history || history.length === 0) {
    return (
      <Card
        title={
          <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
            <IconHistory />
            <span>操作历史</span>
          </div>
        }
        style={{ background: "var(--semi-color-bg-1)" }}
      >
        <Empty
          image={<IconHistory size="extra-large" />}
          title="暂无操作记录"
          description="该工单暂无操作历史记录"
        />
      </Card>
    );
  }

  const renderOperationDetail = (item: OperationHistoryItem) => {
    switch (item.type) {
      case "STATUS_CHANGE":
        return (
          <div>
            <div style={{ display: "flex", alignItems: "center", gap: "8px", marginBottom: "4px" }}>
              <Text strong>状态变更</Text>
              {item.fromValue && item.toValue && (
                <div style={{ display: "flex", alignItems: "center", gap: "4px" }}>
                  <Tag size="small" color="grey">
                    {statusTranslation[item.fromValue] || item.fromValue}
                  </Tag>
                  <Text type="tertiary" size="small">
                    →
                  </Text>
                  <Tag size="small" color="blue">
                    {statusTranslation[item.toValue] || item.toValue}
                  </Tag>
                </div>
              )}
            </div>
            {item.note && (
              <Text type="secondary" size="small" style={{ display: "block", marginTop: "4px" }}>
                说明：{item.note}
              </Text>
            )}
          </div>
        );

      case "TRANSFER":
        return (
          <div>
            <div style={{ marginBottom: "4px" }}>
              <Text strong>工单转交</Text>
              {item.toValue && (
                <Text style={{ marginLeft: "8px" }}>
                  转交给：<Text strong>{item.toValue}</Text>
                </Text>
              )}
            </div>
            {item.note && (
              <Text type="secondary" size="small" style={{ display: "block", marginTop: "4px" }}>
                转交说明：{item.note}
              </Text>
            )}
          </div>
        );

      case "CREATE":
        return (
          <div>
            <Text strong>创建工单</Text>
            {item.note && (
              <Text type="secondary" size="small" style={{ display: "block", marginTop: "4px" }}>
                {item.note}
              </Text>
            )}
          </div>
        );

      case "UPDATE":
        return (
          <div>
            <Text strong>更新信息</Text>
            <Text type="secondary" size="small" style={{ display: "block", marginTop: "4px" }}>
              {item.description}
            </Text>
            {item.note && (
              <Text type="secondary" size="small" style={{ display: "block", marginTop: "4px" }}>
                备注：{item.note}
              </Text>
            )}
          </div>
        );

      default:
        return (
          <div>
            <Text strong>{item.description}</Text>
            {item.note && (
              <Text type="secondary" size="small" style={{ display: "block", marginTop: "4px" }}>
                {item.note}
              </Text>
            )}
          </div>
        );
    }
  };

  // 按时间倒序排列
  const sortedHistory = [...history].sort(
    (a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
  );

  return (
    <Card
      title={
        <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
          <IconHistory />
          <span>操作历史 ({history.length})</span>
        </div>
      }
      style={{ background: "var(--semi-color-bg-1)" }}
    >
      <Timeline mode="left">
        {sortedHistory.map(item => {
          const config = operationTypeConfig[item.type];
          return (
            <Timeline.Item
              key={item.id}
              time={new Date(item.createdAt).toLocaleString("zh-CN")}
              type={
                config?.color === "blue"
                  ? "default"
                  : config?.color === "green"
                    ? "success"
                    : config?.color === "orange"
                      ? "warning"
                      : "default"
              }
              dot={config?.icon}
            >
              <div style={{ paddingLeft: "8px" }}>
                <div
                  style={{ display: "flex", alignItems: "center", gap: "8px", marginBottom: "8px" }}
                >
                  <Tag color={(config?.color as any) || "grey"} size="small">
                    {config?.text || item.type}
                  </Tag>
                  <Text type="tertiary" size="small">
                    {item.user.fullName || item.user.username}
                  </Text>
                </div>
                {renderOperationDetail(item)}
              </div>
            </Timeline.Item>
          );
        })}
      </Timeline>
    </Card>
  );
}
