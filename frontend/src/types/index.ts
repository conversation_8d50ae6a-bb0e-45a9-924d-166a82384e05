// API Types
export interface ApiResponse<T = any> {
  success: boolean;
  data: T;
  message: string;
  code?: number;
}

export interface PaginatedResponse<T> {
  customers?: T[];
  items?: T[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
  pagination?: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

// User Types
export interface User {
  id: string;
  username: string;
  email: string;
  fullName: string;
  role: Role;
  department?: string;
  phone?: string;
  isActive: boolean;
  lastLoginAt?: string;
  createdAt: string;
  updatedAt: string;
}

export interface Role {
  id: string;
  name: string;
  description: string;
  permissions: string[];
}

// Auth Types
export interface LoginCredentials {
  username: string;
  password: string;
  remember?: boolean;
}

export interface LoginResponse {
  token: string;
  user: User;
}

export interface LoginResult {
  success: boolean;
  message: string;
}

// Customer Types
export interface Customer {
  id: string;
  name: string;
  code: string;
  company?: string;
  industry?: string;
  type: "ENTERPRISE" | "INDIVIDUAL" | "GOVERNMENT" | "NONPROFIT";
  level: "BASIC" | "STANDARD" | "PREMIUM" | "ENTERPRISE";
  contactPerson?: string;
  contactPhone?: string;
  contactEmail?: string;
  address?: string;
  description?: string;
  isVip: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface CustomerCreateData {
  name: string;
  code: string;
  company?: string;
  industry?: string;
  type: "ENTERPRISE" | "INDIVIDUAL" | "GOVERNMENT" | "NONPROFIT";
  level?: "BASIC" | "STANDARD" | "PREMIUM" | "ENTERPRISE";
  contactPerson?: string;
  contactPhone?: string;
  contactEmail?: string;
  address?: string;
  description?: string;
  isVip?: boolean;
}

export interface CustomerUpdateData extends Partial<CustomerCreateData> {
  id: string;
}

export interface CustomerStats {
  totalCustomers: number;
  recentCustomers: number;
  levelDistribution: Array<{
    level: string;
    count: number;
  }>;
  topIndustries: Array<{
    industry: string;
    count: number;
  }>;
}

export interface CustomerListParams {
  page?: number;
  limit?: number;
  search?: string;
  type?: "ENTERPRISE" | "INDIVIDUAL" | "GOVERNMENT" | "NONPROFIT";
  level?: "BASIC" | "STANDARD" | "PREMIUM" | "ENTERPRISE";
  industry?: string;
  isVip?: boolean;
}

export interface CustomerContact {
  id: string;
  customerId: string;
  name: string;
  title?: string;
  phone: string;
  email: string;
  isPrimary: boolean;
  createdAt: string;
  updatedAt: string;
}

// Project Types
export interface Project {
  id: string;
  name: string;
  customerId: string;
  customer?: Customer;
  type: "web" | "mobile" | "desktop" | "api" | "other";
  status: "planning" | "active" | "completed" | "on_hold" | "cancelled";
  priority: "low" | "medium" | "high" | "urgent";
  budget?: number;
  startDate: string;
  endDate: string;
  progress: number;
  techStack: string;
  description?: string;
  requirements?: string;
  createdAt: string;
  updatedAt: string;
}

// Service Types
export interface Service {
  id: string;
  ticketNumber: string;
  title: string;
  description: string;
  category: "maintenance" | "support" | "upgrade" | "bugfix" | "consulting" | "monitoring";
  priority: "low" | "medium" | "high" | "urgent";
  status: "pending" | "in_progress" | "waiting_customer" | "resolved" | "closed";
  customerId: string;
  customer?: Customer;
  projectId?: string;
  project?: Project;
  assignedTo?: string;
  assignedUser?: User;
  contactId?: string;
  contact?: CustomerContact;
  estimatedHours?: number;
  actualHours?: number;
  dueDate?: string;
  resolvedAt?: string;
  closedAt?: string;
  createdAt: string;
  updatedAt: string;
}

// UI Types
export interface BreadcrumbItem {
  text: string;
  href?: string;
  icon?: string;
  disabled?: boolean;
}

export type Theme = "light" | "dark" | "auto";

export interface NotificationItem {
  id: string;
  type: "success" | "error" | "warning" | "info";
  title: string;
  message?: string;
  duration?: number;
  persistent?: boolean;
}

export interface ModalState {
  isOpen: boolean;
  title?: string;
  content?: string;
  confirmText?: string;
  cancelText?: string;
  onConfirm?: () => void;
  onCancel?: () => void;
}

export interface LoadingState {
  global: boolean;
  [key: string]: boolean;
}

// Permission Template Types
export type ID = string;
export type Timestamp = string;
export type JSONValue = any;

export interface PaginationQuery {
  page?: number;
  pageSize?: number;
}

export interface UserBasicInfo {
  id: ID;
  username: string;
  fullName?: string;
}

export interface PermissionDetail {
  key: string;
  name: string;
  description?: string;
  group?: string;
}

export interface PermissionGroup {
  key: string;
  name: string;
  label: string;
  description: string;
  permissionCount: number;
  permissions: PermissionDetail[];
}

export type PermissionTemplateCategory = "SYSTEM" | "BUSINESS" | "SERVICE" | "READONLY" | "CUSTOM";

export interface PermissionTemplate {
  id: ID;
  name: string;
  description?: string;
  permissions: string[];
  permissionCount: number;
  category: PermissionTemplateCategory;
  isDefault: boolean;
  isSystem: boolean;
  version: string;
  metadata?: Record<string, JSONValue>;
  usageCount: number;
  createdBy: UserBasicInfo;
  updatedBy?: UserBasicInfo;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export interface CreatePermissionTemplateRequest {
  name: string;
  description?: string;
  permissions: string[];
  category?: PermissionTemplateCategory;
  version?: string;
  metadata?: Record<string, JSONValue>;
}

export interface UpdatePermissionTemplateRequest {
  name?: string;
  description?: string;
  permissions?: string[];
  category?: PermissionTemplateCategory;
  version?: string;
  metadata?: Record<string, JSONValue>;
}

export interface PermissionTemplateQuery {
  page?: number;
  pageSize?: number;
  category?: PermissionTemplateCategory;
  search?: string;
  isDefault?: boolean;
  isSystem?: boolean;
  createdBy?: ID;
  sortBy?: "name" | "createdAt" | "updatedAt" | "usageCount";
  sortOrder?: "asc" | "desc";
}

// API Response Types for Permission Templates
export interface PermissionTemplateListResponse
  extends ApiResponse<{
    templates: PermissionTemplate[];
    pagination: {
      page: number;
      pageSize: number;
      total: number;
      totalPages: number;
    };
  }> {}

export interface PermissionTemplateStatsResponse
  extends ApiResponse<{
    overview: {
      total: number;
      default: number;
      system: number;
      custom: number;
      active: number;
    };
  }> {}

export interface PermissionGroupsResponse extends ApiResponse<PermissionGroup[]> {}

export interface AllPermissionsResponse
  extends ApiResponse<{
    total: number;
    permissions: PermissionGroup[];
    flatPermissions: PermissionDetail[];
  }> {}

// Export AI Types
export * from "./ai";
