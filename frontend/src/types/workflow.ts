/**
 * 工作流设计器相关类型定义
 * 定义工作流设计器所需的所有类型和接口
 */

// ========== 基础类型 ==========

/**
 * 工作流优先级
 */
export type WorkflowPriority = 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT' | 'CRITICAL';

/**
 * 工作流类别
 */
export type WorkflowCategory = 
  | 'SERVICE_AUTOMATION'      // 服务自动化
  | 'SLA_MONITORING'          // SLA监控
  | 'ALERT_PROCESSING'        // 告警处理
  | 'BACKUP_AUTOMATION'       // 备份自动化
  | 'MAINTENANCE'             // 维护任务
  | 'APPROVAL_PROCESS'        // 审批流程
  | 'NOTIFICATION'            // 通知流程
  | 'DATA_PROCESSING'         // 数据处理
  | 'INTEGRATION'             // 集成流程
  | 'CUSTOM';                 // 自定义

/**
 * 触发器类型
 */
export type TriggerType = 'MANUAL' | 'SCHEDULED' | 'EVENT' | 'WEBHOOK' | 'API' | 'CONDITION';

/**
 * 工作流步骤类型
 */
export type WorkflowStepType = 
  | 'ACTION'              // 动作步骤
  | 'CONDITION'           // 条件判断
  | 'APPROVAL'            // 人工审批
  | 'NOTIFICATION'        // 通知发送
  | 'DELAY'               // 延迟等待
  | 'PARALLEL'            // 并行执行
  | 'LOOP'                // 循环执行
  | 'SUBPROCESS'          // 子流程
  | 'SCRIPT'              // 脚本执行
  | 'HTTP_REQUEST'        // HTTP请求
  | 'DATABASE_OPERATION'  // 数据库操作
  | 'FILE_OPERATION';     // 文件操作

/**
 * 工作流执行状态
 */
export type WorkflowExecutionStatus = 'STARTING' | 'RUNNING' | 'PAUSED' | 'STOPPING' | 'STOPPED';

// ========== 工作流定义相关 ==========

/**
 * 触发器配置
 */
export interface TriggerConfig {
  type: TriggerType;
  config: {
    // 手动触发
    manual?: {
      requireApproval?: boolean;
      allowedRoles?: string[];
    };
    // 定时触发
    scheduled?: {
      cronExpression: string;
      timezone?: string;
      enabled: boolean;
    };
    // 事件触发
    event?: {
      eventType: string;
      filters?: Record<string, any>;
    };
    // Webhook触发
    webhook?: {
      endpoint: string;
      method: string;
      authentication?: {
        type: 'token' | 'signature' | 'basic';
        config: Record<string, any>;
      };
    };
    // 条件触发
    condition?: {
      conditions: Array<{
        field: string;
        operator: string;
        value: any;
        source: 'database' | 'api' | 'metric' | 'cache';
      }>;
      checkInterval: number;
    };
  };
}

/**
 * 重试配置
 */
export interface RetryConfig {
  maxAttempts: number;
  delay: number;
  backoffMultiplier?: number;
}

/**
 * 工作流步骤定义
 */
export interface WorkflowStep {
  index: number;
  name: string;
  type: WorkflowStepType;
  description?: string;
  config: Record<string, any>;
  condition?: {
    expression: string;
    variables: string[];
  };
  timeout?: number;
  retry?: RetryConfig;
  onSuccess?: {
    nextStep?: number;
    variables?: Record<string, any>;
  };
  onFailure?: {
    nextStep?: number;
    rollback?: boolean;
  };
}

/**
 * 工作流设置
 */
export interface WorkflowSettings {
  timeout?: number;
  maxRetries?: number;
  errorHandling: 'stop' | 'continue' | 'rollback';
  notifications?: string[];
  concurrency?: {
    enabled: boolean;
    maxParallel: number;
  };
  logging?: {
    level: 'DEBUG' | 'INFO' | 'WARN' | 'ERROR';
    retentionDays: number;
  };
}

/**
 * 工作流配置
 */
export interface WorkflowConfig {
  trigger: TriggerConfig;
  steps: WorkflowStep[];
  variables: Record<string, any>;
  settings: WorkflowSettings;
}

/**
 * 工作流定义
 */
export interface WorkflowDefinition {
  id: string;
  name: string;
  description?: string;
  category?: WorkflowCategory;
  priority?: WorkflowPriority;
  version: string;
  isActive: boolean;
  steps: WorkflowStep[];
  variables: Record<string, any>;
  settings: WorkflowSettings;
  tags?: string[];
  isTemplate?: boolean;
  createdBy?: string;
  createdAt: string;
  updatedAt: string;
}

// ========== 工作流执行相关 ==========

/**
 * 工作流执行状态
 */
export type WorkflowExecutionStatus = 'PENDING' | 'RUNNING' | 'COMPLETED' | 'FAILED' | 'CANCELLED' | 'TIMEOUT';

/**
 * 工作流执行步骤状态
 */
export type WorkflowExecutionStepStatus = 'PENDING' | 'RUNNING' | 'COMPLETED' | 'FAILED' | 'SKIPPED' | 'CANCELLED';

/**
 * 工作流执行步骤信息
 */
export interface WorkflowExecutionStep {
  id: string;
  stepId: string;
  status: WorkflowExecutionStepStatus;
  startTime: string;
  endTime?: string;
  duration?: number;
  input?: any;
  output?: any;
  error?: string;
}

/**
 * 工作流执行信息
 */
export interface WorkflowExecution {
  id: string;
  sessionId: string;
  workflowId: string;
  status: WorkflowExecutionStatus;
  startedAt: string;
  completedAt?: string;
  context: Record<string, any>;
  variables: Record<string, any>;
  steps: WorkflowExecutionStep[];
  error?: string;
}

// ========== 设计器UI相关 ==========

/**
 * 点坐标
 */
export interface Point {
  x: number;
  y: number;
}

/**
 * 画布节点位置
 */
export interface NodePosition {
  x: number;
  y: number;
}

/**
 * 节点尺寸
 */
export interface NodeSize {
  width: number;
  height: number;
}

/**
 * 画布连接线
 */
export interface Connection {
  id: string;
  sourceNodeId: string;
  targetNodeId: string;
  sourcePort: string;
  targetPort: string;
  label?: string;
  condition?: string;
}

/**
 * 设计器连接线
 */
export interface DesignerConnection {
  id: string;
  sourceNodeId: string;
  targetNodeId: string;
  label?: string;
  condition?: string;
  style?: {
    stroke?: string;
    strokeWidth?: number;
    strokeDasharray?: string;
  };
}

/**
 * 设计器节点
 */
export interface DesignerNode {
  id: string;
  type: WorkflowStepType;
  position: NodePosition;
  size: NodeSize;
  step: WorkflowStep;
  status?: 'pending' | 'running' | 'success' | 'error' | 'skipped';
  data: {
    step: WorkflowStep;
    isSelected: boolean;
    isHighlighted: boolean;
    validation: {
      isValid: boolean;
      errors: string[];
    };
  };
  ports: {
    input: Array<{
      id: string;
      label: string;
      type: 'default' | 'condition';
    }>;
    output: Array<{
      id: string;
      label: string;
      type: 'default' | 'condition' | 'success' | 'error';
    }>;
  };
}

/**
 * 拖拽信息
 */
export interface DragInfo {
  type: 'node' | 'selection';
  nodeId: string;
  startPosition: Point;
  originalPosition: Point;
  offset: Point;
}

/**
 * 画布选择
 */
export interface CanvasSelection {
  nodes: string[];
  connections: string[];
}

/**
 * 画布事件
 */
export interface CanvasEvent {
  type: 'node_moved' | 'node_selected' | 'node_deselected' | 'canvas_context_menu';
  nodeId?: string;
  position?: Point;
  data?: any;
}

/**
 * 设计器状态
 */
export interface DesignerState {
  nodes: DesignerNode[];
  connections: Connection[];
  selectedNodes: string[];
  copiedNodes: DesignerNode[];
  history: {
    past: Array<{
      nodes: DesignerNode[];
      connections: Connection[];
    }>;
    present: {
      nodes: DesignerNode[];
      connections: Connection[];
    };
    future: Array<{
      nodes: DesignerNode[];
      connections: Connection[];
    }>;
  };
  viewport: {
    x: number;
    y: number;
    zoom: number;
  };
  isReadonly: boolean;
}

/**
 * 设计器配置
 */
export interface DesignerConfig {
  canvas: {
    width: number;
    height: number;
    backgroundColor: string;
    gridSize: number;
    showGrid: boolean;
    snapToGrid: boolean;
  };
  nodes: {
    defaultWidth: number;
    defaultHeight: number;
    minWidth: number;
    minHeight: number;
    maxWidth: number;
    maxHeight: number;
  };
  connections: {
    strokeWidth: number;
    strokeColor: string;
    highlightColor: string;
    showArrows: boolean;
  };
  interaction: {
    enableDrag: boolean;
    enableResize: boolean;
    enableMultiSelect: boolean;
    enableZoom: boolean;
    enablePan: boolean;
  };
}

// ========== 步骤组件相关 ==========

/**
 * 步骤表单属性
 */
export interface StepFormProps {
  step: WorkflowStep;
  onChange: (step: WorkflowStep) => void;
  onValidation: (isValid: boolean, errors: string[]) => void;
  availableVariables: Array<{
    name: string;
    type: string;
    description: string;
  }>;
}

/**
 * 步骤配置表单字段
 */
export interface StepConfigField {
  name: string;
  label: string;
  type: 'text' | 'number' | 'boolean' | 'select' | 'multiselect' | 'json' | 'code';
  required: boolean;
  description?: string;
  defaultValue?: any;
  options?: Array<{
    label: string;
    value: any;
  }>;
  validation?: {
    min?: number;
    max?: number;
    pattern?: string;
    customValidator?: (value: any) => string | null;
  };
}

/**
 * 步骤模板
 */
export interface StepTemplate {
  type: WorkflowStepType;
  name: string;
  description: string;
  icon: string;
  category: string;
  configFields: StepConfigField[];
  defaultConfig: Record<string, any>;
  examples: Array<{
    name: string;
    description: string;
    config: Record<string, any>;
  }>;
}

// ========== API相关 ==========

/**
 * 工作流列表查询参数
 */
export interface WorkflowListParams {
  page?: number;
  limit?: number;
  category?: WorkflowCategory;
  isActive?: boolean;
  search?: string;
  tags?: string[];
  createdBy?: string;
}

/**
 * 工作流执行查询参数
 */
export interface ExecutionListParams {
  page?: number;
  limit?: number;
  workflowId?: string;
  status?: WorkflowExecutionStatus;
  triggerType?: string;
  startDate?: string;
  endDate?: string;
}

/**
 * 工作流执行请求
 */
export interface ExecuteWorkflowRequest {
  workflowId: string;
  context?: Record<string, any>;
  priority?: WorkflowPriority;
  tags?: string[];
  metadata?: Record<string, any>;
}

/**
 * 工作流执行控制请求
 */
export interface ExecutionControlRequest {
  sessionId: string;
  reason?: string;
}

// ========== 工具和辅助类型 ==========

/**
 * 验证结果
 */
export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

/**
 * 导入导出格式
 */
export interface WorkflowExportData {
  version: string;
  workflow: WorkflowDefinition;
  metadata: {
    exportTime: string;
    exportBy: string;
    source: string;
  };
}

/**
 * 设计器事件
 */
export type DesignerEvent = 
  | { type: 'NODE_SELECT'; nodeId: string; multiSelect?: boolean }
  | { type: 'NODE_DESELECT'; nodeId: string }
  | { type: 'NODE_MOVE'; nodeId: string; position: NodePosition }
  | { type: 'NODE_DELETE'; nodeId: string }
  | { type: 'NODE_COPY'; nodeId: string }
  | { type: 'NODE_PASTE'; position: NodePosition }
  | { type: 'CONNECTION_CREATE'; connection: Connection }
  | { type: 'CONNECTION_DELETE'; connectionId: string }
  | { type: 'CANVAS_CLICK'; position: NodePosition }
  | { type: 'VIEWPORT_CHANGE'; viewport: { x: number; y: number; zoom: number } }
  | { type: 'UNDO' }
  | { type: 'REDO' };

/**
 * 实时协作相关
 */
export interface CollaborationUser {
  id: string;
  name: string;
  avatar: string;
  cursor: NodePosition;
  selection: string[];
  isActive: boolean;
}

export interface CollaborationState {
  users: CollaborationUser[];
  lockState: Record<string, string>; // nodeId -> userId
  changes: Array<{
    userId: string;
    timestamp: string;
    event: DesignerEvent;
  }>;
}

// ========== 扩展功能 ==========

/**
 * 工作流模板市场
 */
export interface WorkflowTemplate {
  id: string;
  name: string;
  description: string;
  category: WorkflowCategory;
  tags: string[];
  author: {
    id: string;
    name: string;
    avatar?: string;
  };
  rating: number;
  downloads: number;
  preview: {
    thumbnail: string;
    screenshots: string[];
  };
  workflow: WorkflowDefinition;
  createdAt: string;
  updatedAt: string;
}

/**
 * 插件系统
 */
export interface WorkflowPlugin {
  id: string;
  name: string;
  version: string;
  description: string;
  author: string;
  stepTypes: Array<{
    type: string;
    template: StepTemplate;
  }>;
  hooks: {
    beforeExecute?: (context: any) => any;
    afterExecute?: (context: any, result: any) => any;
    onError?: (context: any, error: any) => any;
  };
  dependencies: string[];
  isEnabled: boolean;
}

export default {
  // 导出所有类型以便其他模块使用
};