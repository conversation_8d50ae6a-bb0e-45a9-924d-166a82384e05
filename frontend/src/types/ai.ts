/**
 * AI 功能相关类型定义
 */

// AI 配置模式
export type AIMode = "aggressive" | "user-triggered" | "disabled";

// 后端AI配置接口
export interface BackendAIConfiguration {
  id: string;
  provider: string;
  model: string;
  temperature: number;
  maxTokens: number;
  timeout: number;
  mode: string;
  enabledFields: {
    title: boolean;
    category: boolean;
    priority: boolean;
    slaTemplate: boolean;
  };
  autoFillThreshold: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

// AI 分析字段类型
export type AIAnalysisField = "title" | "category" | "priority" | "slaTemplate";

// AI 配置接口
export interface AIConfiguration {
  id?: string;
  userId?: string; // 用户特定配置，为空则为全局配置
  mode: AIMode;
  autoFillThreshold: number; // 自动填充的置信度阈值 (0-100)
  enabledFields: {
    title: boolean;
    category: boolean;
    priority: boolean;
    slaTemplate: boolean;
  };
  userPreferences: {
    showReasoningTooltips: boolean; // 显示推理说明提示
    enableSuggestionHistory: boolean; // 启用建议历史记录
    showConfidenceScores: boolean; // 显示置信度分数
  };
  createdAt?: string;
  updatedAt?: string;
}

// AI 分析请求接口
export interface AIAnalysisRequest {
  description: string;
  customerId?: string;
  customerLevel?: string;
  contextData?: {
    customerType?: string;
    industryType?: string;
    previousTickets?: Array<{
      category: string;
      priority: string;
      title: string;
    }>;
  };
}

// AI 建议结果
export interface AISuggestion {
  field: AIAnalysisField;
  suggested: string;
  confidence: number; // 0-100
  reasoning: string;
  alternatives?: Array<{
    value: string;
    confidence: number;
  }>;
}

// AI 分析响应接口
export interface AIAnalysisResponse {
  success: boolean;
  requestId: string;
  timestamp: string;
  suggestions: AISuggestion[];
  overallConfidence: number;
  processingTime: number; // 处理时间(毫秒)
  warning?: string;
  metadata?: {
    aiProvider?: string;
    model?: string;
    tags?: string[];
    estimatedHours?: number;
    urgencyIndicators?: string[];
  };
}

// AI 反馈接口
export interface AIFeedback {
  requestId: string;
  rating: number; // 1-5星评分
  helpful?: boolean; // 是否有帮助
  adopted?: Record<string, any>; // 采用了哪些建议
  comments?: string; // 用户评论
}

// 服务类别映射
export const SERVICE_CATEGORIES = {
  MAINTENANCE: "维护",
  SUPPORT: "支持",
  UPGRADE: "升级",
  BUGFIX: "Bug修复",
  CONSULTING: "咨询",
  MONITORING: "监控",
} as const;

// 优先级映射
export const PRIORITIES = {
  LOW: "低",
  MEDIUM: "中",
  HIGH: "高",
  URGENT: "紧急",
} as const;

// AI 分析关键词配置
export interface AIKeywordConfig {
  categories: Record<
    string,
    {
      keywords: string[];
      weight: number;
      context?: string[];
    }
  >;
  priorities: Record<
    string,
    {
      keywords: string[];
      weight: number;
      urgencyIndicators?: string[];
    }
  >;
}

// 默认 AI 配置
export const DEFAULT_AI_CONFIG: AIConfiguration = {
  mode: "user-triggered",
  autoFillThreshold: 80,
  enabledFields: {
    title: true,
    category: true,
    priority: true,
    slaTemplate: true,
  },
  userPreferences: {
    showReasoningTooltips: true,
    enableSuggestionHistory: false,
    showConfidenceScores: true,
  },
};

// AI 统计数据接口
export interface AIStatistics {
  totalAnalyses: number;
  adoptionRate: number; // 采用率 (0-100)
  averageConfidence: number;
  accuracyByField: Record<
    AIAnalysisField,
    {
      total: number;
      accurate: number;
      accuracy: number;
    }
  >;
  userSatisfaction: number; // 用户满意度 (1-5)
  performanceMetrics: {
    averageResponseTime: number; // 平均响应时间(毫秒)
    successRate: number; // 成功率 (0-100)
  };
}

// AI 模式切换事件
export interface AIModeChangeEvent {
  previousMode: AIMode;
  newMode: AIMode;
  userId?: string;
  timestamp: string;
  trigger: "user-action" | "system-config" | "auto-adapt";
}
