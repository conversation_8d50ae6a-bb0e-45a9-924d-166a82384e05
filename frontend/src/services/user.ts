import { get, post, put, del, patch } from "@/utils/request";
import type { User, Role } from "@/types";

// 重新导出User类型
export type { User } from "@/types";

export interface ProfileUpdateData {
  username?: string;
  email?: string;
  fullName?: string;
  department?: string;
  phone?: string;
}

export interface PasswordChangeData {
  currentPassword: string;
  newPassword: string;
}

export interface CreateUserData {
  username: string;
  email: string;
  password: string;
  fullName: string;
  roleId: string;
  department?: string;
  phone?: string;
  isActive?: boolean;
}

export interface UpdateUserData {
  username?: string;
  email?: string;
  fullName?: string;
  roleId?: string;
  department?: string;
  phone?: string;
  isActive?: boolean;
}

export interface UserListParams {
  page?: number;
  limit?: number;
  search?: string;
  roleId?: string;
  isActive?: boolean;
  department?: string;
}

export interface UserListResponse {
  success: boolean;
  data: {
    users: User[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
  message?: string;
}

export interface UserStatsResponse {
  success: boolean;
  data: {
    total: number;
    active: number;
    inactive: number;
    todayLogins: number;
    newThisWeek: number;
    totalUsers: number;
    activeUsers: number;
    inactiveUsers: number;
    usersByRole: Array<{
      roleName: string;
      count: number;
    }>;
    usersByDepartment: Array<{
      department: string;
      count: number;
    }>;
  };
}

class UserService {
  // 获取当前用户信息
  async getCurrentUser(): Promise<{ success: boolean; data: User; message?: string }> {
    return get("/api/v1/users/me");
  }

  // 更新当前用户信息
  async updateProfile(
    data: ProfileUpdateData
  ): Promise<{ success: boolean; data: User; message?: string }> {
    return put("/api/v1/users/me", data);
  }

  // 修改密码
  async changePassword(
    userId: string,
    data: PasswordChangeData
  ): Promise<{ success: boolean; message?: string }> {
    return post(`/api/v1/users/${userId}/change-password`, data);
  }

  // === 管理员功能 ===

  // 获取用户列表
  async getUsers(params: UserListParams = {}): Promise<UserListResponse> {
    return get("/api/v1/users", { params });
  }

  // 获取用户详情
  async getUser(id: string): Promise<{ success: boolean; data: User; message?: string }> {
    return get(`/api/v1/users/${id}`);
  }

  // 创建用户
  async createUser(
    data: CreateUserData
  ): Promise<{ success: boolean; data: User; message?: string }> {
    return post("/api/v1/users", data);
  }

  // 更新用户
  async updateUser(
    id: string,
    data: UpdateUserData
  ): Promise<{ success: boolean; data: User; message?: string }> {
    return put(`/api/v1/users/${id}`, data);
  }

  // 删除用户
  async deleteUser(id: string): Promise<{ success: boolean; message?: string }> {
    return del(`/api/v1/users/${id}`);
  }

  // 切换用户状态
  async toggleUserStatus(id: string): Promise<{ success: boolean; data: User; message?: string }> {
    return patch(`/api/v1/users/${id}/toggle-status`);
  }

  // 重置用户密码（管理员功能）
  async resetPassword(
    id: string,
    newPassword: string
  ): Promise<{ success: boolean; message?: string }> {
    return post(`/api/v1/users/${id}/reset-password`, { newPassword });
  }

  // 获取用户统计信息
  async getUserStats(): Promise<UserStatsResponse> {
    return get("/api/v1/users/stats");
  }

  // 获取部门列表
  async getDepartments(): Promise<{ success: boolean; data: string[]; message?: string }> {
    return get("/api/v1/users/departments");
  }

  // 批量导入用户
  async importUsers(users: CreateUserData[]): Promise<{
    success: boolean;
    data: {
      success: User[];
      failed: Array<{ user: CreateUserData; error: string }>;
    };
    message?: string;
  }> {
    return post("/api/v1/users/import", users);
  }

  // 批量更新用户
  async batchUpdateUsers(
    action: string,
    userIds: string[],
    roleId?: string
  ): Promise<{
    success: boolean;
    data: {
      updated: User[];
      failed: string[];
    };
    message?: string;
  }> {
    return post("/api/v1/users/batch-update", { action, userIds, roleId });
  }

  // 导出用户数据
  async exportUsers(options: any): Promise<Blob> {
    const token = (() => {
      try {
        if ((window as any).__AUTH_STORE__) {
          const t = (window as any).__AUTH_STORE__.getState?.()?.token;
          if (t) return t;
        }
        const authStorage = localStorage.getItem("auth-storage");
        if (authStorage) {
          const parsed = JSON.parse(authStorage);
          return parsed.state?.token || null;
        }
      } catch {}
      return null;
    })();

    const response = await fetch("/api/v1/users/export", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        ...(token ? { Authorization: `Bearer ${token}` } : {}),
      },
      body: JSON.stringify(options),
      credentials: "include",
    });
    if (!response.ok) {
      throw new Error("导出失败");
    }
    return response.blob();
  }
}

export const userService = new UserService();
