import { post, get } from "@/utils/request";
import type { LoginCredentials, LoginResponse, User, ApiResponse } from "@/types";

export const authService = {
  // 用户登录
  async login(credentials: LoginCredentials): Promise<ApiResponse<LoginResponse>> {
    return post<LoginResponse>("/api/v1/auth/login", credentials);
  },

  // 用户登出
  async logout(): Promise<ApiResponse<void>> {
    return post<void>("/api/v1/auth/logout");
  },

  // 获取当前用户信息
  async me(): Promise<ApiResponse<User>> {
    return get<User>("/api/v1/auth/me");
  },

  // 刷新令牌
  async refreshToken(): Promise<ApiResponse<{ token: string }>> {
    return post<{ token: string }>("/api/v1/auth/refresh");
  },

  // 修改密码
  async changePassword(data: {
    currentPassword: string;
    newPassword: string;
    confirmPassword: string;
  }): Promise<ApiResponse<void>> {
    return post<void>("/api/v1/auth/change-password", data);
  },

  // 更新用户档案
  async updateProfile(data: Partial<User>): Promise<ApiResponse<User>> {
    return post<User>("/api/v1/auth/profile", data);
  },

  // 忘记密码
  async forgotPassword(email: string): Promise<ApiResponse<void>> {
    return post<void>("/api/v1/auth/forgot-password", { email });
  },

  // 重置密码
  async resetPassword(data: {
    token: string;
    newPassword: string;
    confirmPassword: string;
  }): Promise<ApiResponse<void>> {
    return post<void>("/api/v1/auth/reset-password", data);
  },
};
