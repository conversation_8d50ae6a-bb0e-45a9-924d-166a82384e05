import { get, post, put } from "@/utils/request";
import type { ApiResponse } from "@/types";

export interface ActivitySummary {
  totalUsers: number;
  activeUsers: number;
  onlineUsers: number;
  todayLogins: number;
  avgSessionDuration: number;
  peakActivityTime: string;
  topActiveUsers: Array<{
    user: any;
    activityScore: number;
    loginCount: number;
    totalDuration: number;
  }>;
}

export interface OnlineUsersResponse {
  count: number;
  users: Array<{
    id: string;
    username: string;
    fullName: string;
    avatar?: string;
    department?: string;
    lastActivity: string;
    loginTime: string;
    ipAddress?: string;
    location?: any;
  }>;
}

export interface UserBehaviorAnalysis {
  userId: string;
  activityLevel: "HIGH" | "MEDIUM" | "LOW";
  preferredHours: number[];
  avgSessionDuration: number;
  frequentFeatures: string[];
  riskScore: number;
  patterns: any[];
  anomalies: any[];
}

export interface UserActivityStats {
  stats: Array<{
    id: string;
    userId: string;
    date: string;
    loginCount: number;
    sessionCount: number;
    totalDurationMinutes: number;
    operationCount: number;
    pageViews: number;
    activityScore: number;
    peakActivityHour?: number;
    featuresUsed?: any;
    lastActivity?: string;
    user: {
      id: string;
      username: string;
      fullName: string;
      email: string;
      department?: string;
      avatar?: string;
    };
  }>;
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export interface UserAnomaly {
  id: string;
  userId: string;
  anomalyType:
    | "UNUSUAL_TIME"
    | "EXCESSIVE_OPERATIONS"
    | "ABNORMAL_LOCATION"
    | "SUSPICIOUS_BEHAVIOR";
  description: string;
  severity: "LOW" | "MEDIUM" | "HIGH" | "CRITICAL";
  detectedValue: any;
  normalRange?: any;
  confidenceScore: number;
  isResolved: boolean;
  resolvedAt?: string;
  resolvedBy?: string;
  notes?: string;
  detectedAt: string;
  user: {
    id: string;
    username: string;
    fullName: string;
    department?: string;
  };
  resolvedByUser?: {
    id: string;
    username: string;
    fullName: string;
  };
}

export interface UserAnomaliesResponse {
  anomalies: UserAnomaly[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export interface HeatmapData {
  date: string;
  hour: number;
  value: number;
}

export interface UserRankingsResponse {
  type: string;
  period: string;
  rankings: Array<{
    rank: number;
    user: {
      id: string;
      username: string;
      fullName: string;
      avatar?: string;
      department?: string;
    };
    value: number;
    activityScore: number;
    loginCount: number;
    totalDurationMinutes: number;
    operationCount: number;
  }>;
}

export const userAnalyticsService = {
  /**
   * 获取用户活动概览
   */
  async getActivitySummary(
    startDate?: string,
    endDate?: string
  ): Promise<ApiResponse<ActivitySummary>> {
    const params = new URLSearchParams();
    if (startDate) params.append("startDate", startDate);
    if (endDate) params.append("endDate", endDate);

    const queryString = params.toString();
    const url = queryString
      ? `/api/v1/user-analytics/summary?${queryString}`
      : "/api/v1/user-analytics/summary";
    return get<ActivitySummary>(url);
  },

  /**
   * 获取在线用户列表
   */
  async getOnlineUsers(): Promise<ApiResponse<OnlineUsersResponse>> {
    return get<OnlineUsersResponse>("/api/v1/user-analytics/online");
  },

  /**
   * 获取用户行为分析
   */
  async getUserBehaviorAnalysis(
    userId: string,
    days: number = 30
  ): Promise<ApiResponse<UserBehaviorAnalysis>> {
    return get<UserBehaviorAnalysis>(`/api/v1/user-analytics/behavior/${userId}?days=${days}`);
  },

  /**
   * 获取用户活动热力图数据
   */
  async getActivityHeatmap(
    userId?: string,
    days: number = 30
  ): Promise<ApiResponse<HeatmapData[]>> {
    const params = new URLSearchParams();
    params.append("days", days.toString());
    if (userId) params.append("userId", userId);

    const queryString = params.toString();
    const url = `/api/v1/user-analytics/heatmap?${queryString}`;
    return get<HeatmapData[]>(url);
  },

  /**
   * 获取用户活动统计
   */
  async getUserActivityStats(
    page: number = 1,
    limit: number = 20,
    filters?: {
      userId?: string;
      startDate?: string;
      endDate?: string;
      sortBy?: string;
      sortOrder?: "asc" | "desc";
    }
  ): Promise<ApiResponse<UserActivityStats>> {
    const params = new URLSearchParams();
    params.append("page", page.toString());
    params.append("limit", limit.toString());

    if (filters) {
      if (filters.userId) params.append("userId", filters.userId);
      if (filters.startDate) params.append("startDate", filters.startDate);
      if (filters.endDate) params.append("endDate", filters.endDate);
      if (filters.sortBy) params.append("sortBy", filters.sortBy);
      if (filters.sortOrder) params.append("sortOrder", filters.sortOrder);
    }

    const queryString = params.toString();
    const url = `/api/v1/user-analytics/stats?${queryString}`;
    return get<UserActivityStats>(url);
  },

  /**
   * 获取用户行为模式
   */
  async getUserBehaviorPatterns(userId: string): Promise<ApiResponse<any[]>> {
    return get<any[]>(`/api/v1/user-analytics/patterns/${userId}`);
  },

  /**
   * 获取用户活动异常
   */
  async getUserAnomalies(
    page: number = 1,
    limit: number = 20,
    filters?: {
      userId?: string;
      severity?: string;
      anomalyType?: string;
      isResolved?: boolean;
    }
  ): Promise<ApiResponse<UserAnomaliesResponse>> {
    const params = new URLSearchParams();
    params.append("page", page.toString());
    params.append("limit", limit.toString());

    if (filters) {
      if (filters.userId) params.append("userId", filters.userId);
      if (filters.severity) params.append("severity", filters.severity);
      if (filters.anomalyType) params.append("anomalyType", filters.anomalyType);
      if (filters.isResolved !== undefined)
        params.append("isResolved", filters.isResolved.toString());
    }

    const queryString = params.toString();
    const url = `/api/v1/user-analytics/anomalies?${queryString}`;
    return get<UserAnomaliesResponse>(url);
  },

  /**
   * 标记异常为已解决
   */
  async resolveAnomaly(anomalyId: string, notes?: string): Promise<ApiResponse<UserAnomaly>> {
    return put<UserAnomaly>(`/api/v1/user-analytics/anomalies/${anomalyId}/resolve`, {
      notes,
    });
  },

  /**
   * 触发异常检测
   */
  async triggerAnomalyDetection(userId?: string): Promise<ApiResponse<void>> {
    return post<void>("/api/v1/user-analytics/detect-anomalies", {
      userId,
    });
  },

  /**
   * 更新用户行为模式
   */
  async updateBehaviorPatterns(userId?: string): Promise<ApiResponse<void>> {
    return post<void>("/api/v1/user-analytics/update-patterns", {
      userId,
    });
  },

  /**
   * 导出用户活动报告
   */
  async exportActivityReport(
    startDate: string,
    endDate: string,
    userIds?: string[],
    format: "json" | "csv" = "json"
  ): Promise<ApiResponse<any>> {
    const params = new URLSearchParams();
    params.append("startDate", startDate);
    params.append("endDate", endDate);
    params.append("format", format);

    if (userIds && userIds.length > 0) {
      params.append("userIds", userIds.join(","));
    }

    const queryString = params.toString();
    const url = `/api/v1/user-analytics/export?${queryString}`;
    return get<any>(url);
  },

  /**
   * 获取用户排行榜
   */
  async getUserRankings(
    type: "activity" | "login" | "duration" | "operation" = "activity",
    period: "day" | "week" | "month" = "week",
    limit: number = 20
  ): Promise<ApiResponse<UserRankingsResponse>> {
    const params = new URLSearchParams();
    params.append("type", type);
    params.append("period", period);
    params.append("limit", limit.toString());

    const queryString = params.toString();
    const url = `/api/v1/user-analytics/rankings?${queryString}`;
    return get<UserRankingsResponse>(url);
  },
};
