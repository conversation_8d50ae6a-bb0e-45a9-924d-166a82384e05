import { get } from "@/utils/request";
import type { ProjectArchive } from "shared/types";
import type { ApiResponse, PaginatedResponse } from "@/types";

export const archiveService = {
  // 获取项目档案列表
  async getArchives(params: {
    customerId?: string;
    page?: number;
    limit?: number;
  }): Promise<ApiResponse<PaginatedResponse<ProjectArchive>>> {
    return get<PaginatedResponse<ProjectArchive>>("/api/v1/archives", params);
  },
};
