/**
 * API 客户端
 *
 * 统一的API请求客户端，处理身份验证、错误处理等
 */

interface ApiClientOptions {
  responseType?: "json" | "blob" | "text";
}

function getAuthToken(): string | null {
  if (typeof window === "undefined") return null;
  try {
    if ((window as any).__AUTH_STORE__) {
      const token = (window as any).__AUTH_STORE__.getState?.()?.token;
      if (token) return token;
    }
    const authStorage = localStorage.getItem("auth-storage");
    if (authStorage) {
      const parsed = JSON.parse(authStorage);
      return parsed.state?.token || null;
    }
  } catch (e) {
    // noop
  }
  return null;
}

class ApiClient {
  // private baseURL = '/api/v1'
  // private async request<T = any>(
  //   method: string,
  //   url: string,
  //   data?: any,
  //   options: ApiClientOptions = {}
  // ): Promise<T> {
  //   const { responseType = 'json' } = options
  //   const isFormData = typeof FormData !== 'undefined' && data instanceof FormData
  //   const token = getAuthToken()
  //   const config: RequestInit = {
  //     method,
  //     headers: {
  //       ...(isFormData ? {} : { 'Content-Type': 'application/json' }),
  //       ...(token ? { 'Authorization': `Bearer ${token}` } : {}),
  //     },
  //     credentials: 'include', // 包含 cookie
  //   }
  //   if (data && method !== 'GET') {
  //     config.body = isFormData ? data : JSON.stringify(data)
  //   }
  //   try {
  //     const response = await fetch(`${this.baseURL}${url}`, config)
  //     if (!response.ok) {
  //       const errorData = await response.json().catch(() => ({}))
  //       throw new Error(errorData.message || `HTTP ${response.status}`)
  //     }
  //     if (responseType === 'blob') {
  //       return response.blob() as T
  //     } else if (responseType === 'text') {
  //       return response.text() as T
  //     } else {
  //       return response.json()
  //     }
  //   } catch (error) {
  //     console.error(`API请求失败 [${method} ${url}]:`, error)
  //     throw error
  //   }
  // }
  // get<T = any>(url: string, params?: any, options?: ApiClientOptions): Promise<T> {
  //   let finalUrl = url
  //   if (params) {
  //     const searchParams = new URLSearchParams()
  //     Object.entries(params).forEach(([key, value]) => {
  //       if (value !== undefined && value !== null) {
  //         searchParams.append(key, String(value))
  //       }
  //     })
  //     const queryString = searchParams.toString()
  //     if (queryString) {
  //       finalUrl += (url.includes('?') ? '&' : '?') + queryString
  //     }
  //   }
  //   return this.request('GET', finalUrl, undefined, options)
  // }
  // post<T = any>(url: string, data?: any, options?: ApiClientOptions): Promise<T> {
  //   return this.request('POST', url, data, options)
  // }
  // put<T = any>(url: string, data?: any, options?: ApiClientOptions): Promise<T> {
  //   return this.request('PUT', url, data, options)
  // }
  // delete<T = any>(url: string, options?: ApiClientOptions): Promise<T> {
  //   return this.request('DELETE', url, undefined, options)
  // }
  // patch<T = any>(url: string, data?: any, options?: ApiClientOptions): Promise<T> {
  //   return this.request('PATCH', url, data, options)
  // }
}

export const apiClient = new ApiClient();
export default apiClient;
