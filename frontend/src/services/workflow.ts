/**
 * 工作流API服务
 * 提供工作流相关的API调用功能
 */

import * as request from '../utils/request';
import {
  WorkflowDefinition,
  WorkflowListParams,
  ExecutionListParams,
  ExecuteWorkflowRequest,
  ExecutionControlRequest,
  WorkflowExecution,
  WorkflowTemplate
} from '../types/workflow';

// ========== 工作流定义API ==========

/**
 * 创建工作流
 */
export const createWorkflow = async (workflow: Omit<WorkflowDefinition, 'id' | 'createdAt' | 'updatedAt'>): Promise<WorkflowDefinition> => {
  const response = await request.post('/api/v1/workflows', workflow);
  return response.data;
};

/**
 * 获取工作流列表
 */
export const getWorkflows = async (params: WorkflowListParams = {}): Promise<{
  workflows: WorkflowDefinition[];
  pagination: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
}> => {
  const response = await request.get('/api/v1/workflows', params);
  return response.data;
};

/**
 * 根据ID获取工作流
 */
export const getWorkflowById = async (id: string): Promise<WorkflowDefinition> => {
  const response = await request.get(`/api/v1/workflows/${id}`);
  return response.data;
};

/**
 * 更新工作流
 */
export const updateWorkflow = async (id: string, workflow: Partial<WorkflowDefinition>): Promise<WorkflowDefinition> => {
  const response = await request.put(`/api/v1/workflows/${id}`, workflow);
  return response.data;
};

/**
 * 删除工作流
 */
export const deleteWorkflow = async (id: string): Promise<void> => {
  await request.del(`/api/v1/workflows/${id}`);
};

/**
 * 复制工作流
 */
export const cloneWorkflow = async (id: string, name: string): Promise<WorkflowDefinition> => {
  const response = await request.post(`/api/v1/workflows/${id}/clone`, { name });
  return response.data;
};

/**
 * 验证工作流配置
 */
export const validateWorkflowConfig = async (config: any): Promise<{
  valid: boolean;
  estimatedTime: number;
  errors?: string[];
}> => {
  const response = await request.post('/api/v1/workflows/validate', { config });
  return response.data;
};

// ========== 工作流执行API ==========

/**
 * 执行工作流
 */
export const executeWorkflow = async (requestData: ExecuteWorkflowRequest): Promise<{
  sessionId: string;
  workflowId: string;
  status: string;
  startTime: string;
  totalSteps: number;
  currentStep: number;
  message: string;
}> => {
  const response = await request.post('/api/v1/workflow/execute', requestData);
  return response.data;
};

/**
 * 获取执行状态
 */
export const getExecutionStatus = async (sessionId: string): Promise<{
  sessionId: string;
  executionId: string;
  workflowId: string;
  status: string;
  startTime: string;
  endTime?: string;
  currentStep: number;
  totalSteps: number;
  errors: any[];
  progress: number;
  duration: number;
}> => {
  const response = await request.get(`/api/v1/workflow/execution/${sessionId}`);
  return response.data;
};

/**
 * 获取执行列表
 */
export const getExecutions = async (params: ExecutionListParams = {}): Promise<{
  executions: WorkflowExecution[];
  pagination: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
}> => {
  const response = await request.get('/api/v1/workflow/executions', params);
  return response.data;
};

/**
 * 暂停执行
 */
export const pauseExecution = async (requestData: ExecutionControlRequest): Promise<void> => {
  await request.post('/api/v1/workflow/execution/pause', requestData);
};

/**
 * 恢复执行
 */
export const resumeExecution = async (requestData: ExecutionControlRequest): Promise<void> => {
  await request.post('/api/v1/workflow/execution/resume', requestData);
};

/**
 * 停止执行
 */
export const stopExecution = async (requestData: ExecutionControlRequest): Promise<void> => {
  await request.post('/api/v1/workflow/execution/stop', requestData);
};

/**
 * 获取执行跟踪信息
 */
export const getExecutionTracking = async (sessionId: string): Promise<{
  sessionId: string;
  executionId: string;
  workflowId: string;
  overallStatus: string;
  steps: Array<{
    stepIndex: number;
    status: string;
    startTime: string;
    endTime?: string;
    duration: number;
    retryCount: number;
    error?: any;
    logs: any[];
  }>;
  summary: {
    totalSteps: number;
    completedSteps: number;
    errorCount: number;
    startTime: string;
    endTime?: string;
  };
}> => {
  const response = await request.get(`/workflow/execution/${sessionId}/tracking`);
  return response.data;
};

// ========== 监控和统计API ==========

/**
 * 获取协调器状态
 */
export const getCoordinatorStatus = async (): Promise<{
  coordinator: {
    status: string;
    activeSessions: number;
    totalSessions: number;
    config: any;
  };
  statistics: {
    completedExecutions: number;
    failedExecutions: number;
    runningExecutions: number;
    averageExecutionTime: number;
    successRate: number;
  };
  timestamp: string;
}> => {
  const response = await request.get('/workflow/coordinator/status');
  return response.data;
};

/**
 * 获取执行指标
 */
export const getExecutionMetrics = async (params: {
  executorType?: string;
  timeWindow?: '1h' | '6h' | '24h' | '7d';
  includeDetails?: boolean;
} = {}): Promise<{
  metrics: any;
  healthScore: number;
  realtime: any;
  performanceTrend?: any;
  executorComparison?: any;
  timestamp: string;
}> => {
  const response = await request.get('/workflow/metrics', { params });
  return response.data;
};

/**
 * 获取执行日志
 */
export const getExecutionLogs = async (params: {
  sessionId?: string;
  workflowId?: string;
  level?: 'INFO' | 'WARN' | 'ERROR' | 'DEBUG';
  page?: number;
  limit?: number;
  startTime?: string;
  endTime?: string;
  search?: string;
} = {}): Promise<{
  logs: any[];
  pagination: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
    hasMore: boolean;
  };
  statistics: {
    totalLogs: number;
    levelDistribution: Record<string, number>;
    topErrors: any[];
  };
  aggregations?: {
    byLevel: Record<string, number>;
    bySource: Record<string, number>;
  };
}> => {
  const response = await request.get('/workflow/logs', { params });
  return response.data;
};

/**
 * 导出执行日志
 */
export const exportExecutionLogs = async (params: {
  sessionId?: string;
  workflowId?: string;
  level?: 'INFO' | 'WARN' | 'ERROR' | 'DEBUG';
  format?: 'json' | 'csv' | 'txt';
  includeMetadata?: boolean;
  startTime?: string;
  endTime?: string;
  search?: string;
}): Promise<Blob> => {
  const response = await request.get('/workflow/logs/export', {
    params,
    responseType: 'blob'
  });
  return response.data;
};

// ========== 工作流统计API ==========

/**
 * 获取工作流统计信息
 */
export const getWorkflowStats = async (): Promise<{
  totalWorkflows: number;
  activeWorkflows: number;
  totalExecutions: number;
  successfulExecutions: number;
  failedExecutions: number;
  averageExecutionTime: number;
  topCategories: Array<{
    category: string;
    count: number;
  }>;
  recentActivity: Array<{
    type: string;
    workflowName: string;
    timestamp: string;
    status: string;
  }>;
}> => {
  const response = await request.get('/workflows/stats');
  return response.data;
};

/**
 * 获取正在运行的执行
 */
export const getRunningExecutions = async (): Promise<{
  executions: Array<{
    sessionId: string;
    workflowId: string;
    workflowName: string;
    startTime: string;
    currentStep: number;
    totalSteps: number;
    progress: number;
  }>;
  count: number;
}> => {
  const response = await request.get('/workflows/running-executions');
  return response.data;
};

// ========== 模板相关API ==========

/**
 * 获取预设模板
 */
export const getPresetTemplates = async (): Promise<WorkflowTemplate[]> => {
  const response = await request.get('/workflows/preset-templates');
  return response.data;
};

/**
 * 创建模板
 */
export const createTemplate = async (template: Omit<WorkflowDefinition, 'id' | 'createdAt' | 'updatedAt'>): Promise<WorkflowDefinition> => {
  const response = await request.post('/workflows/templates', template);
  return response.data;
};

/**
 * 从模板创建工作流
 */
export const createFromTemplate = async (
  templateId: string,
  name: string,
  customizations: any = {}
): Promise<WorkflowDefinition> => {
  const response = await request.post(`/workflows/templates/${templateId}/create`, {
    name,
    customizations
  });
  return response.data;
};

// ========== 触发器相关API ==========

/**
 * 获取触发器统计信息
 */
export const getTriggerStats = async (): Promise<{
  totalTriggers: number;
  activeTriggers: number;
  triggerTypes: Record<string, number>;
  recentTriggers: Array<{
    type: string;
    workflowName: string;
    timestamp: string;
    status: string;
  }>;
}> => {
  const response = await request.get('/triggers/stats');
  return response.data;
};

/**
 * 手动触发事件
 */
export const triggerEvent = async (eventData: {
  eventType: string;
  data: any;
  source?: string;
}): Promise<void> => {
  await request.post('/triggers/event', eventData);
};

/**
 * 验证Cron表达式
 */
export const validateCronExpression = async (expression: string): Promise<{
  valid: boolean;
  description: string;
  nextRuns?: string[];
}> => {
  const response = await request.post('/triggers/validate-cron', { expression });
  return response.data;
};

// ========== 导出整合的服务对象 ==========
export const workflowService = {
  // 工作流定义
  createWorkflow,
  getWorkflows,
  getWorkflowById,
  updateWorkflow,
  deleteWorkflow,
  cloneWorkflow,
  validateWorkflowConfig,
  
  // 工作流执行
  executeWorkflow,
  getExecutionStatus,
  getExecutions,
  pauseExecution,
  resumeExecution,
  stopExecution,
  getExecutionTracking,
  
  // 监控和统计
  getCoordinatorStatus,
  getExecutionMetrics,
  getExecutionLogs,
  exportExecutionLogs,
  getWorkflowStats,
  getRunningExecutions,
  
  // 模板相关
  getPresetTemplates,
  createTemplate,
  createFromTemplate,
  
  // 触发器相关
  getTriggerStats,
  triggerEvent,
  validateCronExpression
};

export default workflowService;