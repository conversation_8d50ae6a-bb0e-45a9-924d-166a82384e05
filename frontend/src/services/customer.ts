import { get, post, put, del } from "@/utils/request";
import type {
  ApiResponse,
  PaginatedResponse,
  Customer,
  CustomerCreateData,
  CustomerUpdateData,
  CustomerStats,
  CustomerListParams,
} from "@/types";

// 客户相关 API 服务
export class CustomerService {
  private static baseUrl = "/api/v1/customers";

  /**
   * 获取客户列表
   */
  static async getCustomers(
    params?: CustomerListParams
  ): Promise<ApiResponse<PaginatedResponse<Customer>>> {
    // 转换参数名称以匹配后端API，过滤掉空值
    const apiParams: Record<string, any> = {};

    if (params?.page) apiParams.page = params.page;
    if (params?.limit) apiParams.limit = params.limit;
    if (params?.search) apiParams.search = params.search;
    if (params?.type) apiParams.type = params.type;
    if (params?.level) apiParams.level = params.level;
    if (params?.industry) apiParams.industry = params.industry;
    if (params?.isVip !== undefined) apiParams.isVip = params.isVip;

    return get(`${this.baseUrl}`, apiParams);
  }

  /**
   * 获取客户详情
   */
  static async getCustomer(id: string): Promise<ApiResponse<Customer>> {
    return get(`${this.baseUrl}/${id}`);
  }

  /**
   * 创建客户
   */
  static async createCustomer(data: CustomerCreateData): Promise<ApiResponse<Customer>> {
    return post(`${this.baseUrl}`, data);
  }

  /**
   * 更新客户
   */
  static async updateCustomer(
    id: string,
    data: Partial<CustomerUpdateData>
  ): Promise<ApiResponse<Customer>> {
    return put(`${this.baseUrl}/${id}`, data);
  }

  /**
   * 删除客户
   */
  static async deleteCustomer(id: string): Promise<ApiResponse<void>> {
    return del(`${this.baseUrl}/${id}`);
  }

  /**
   * 获取客户统计信息
   */
  static async getCustomerStats(): Promise<ApiResponse<CustomerStats>> {
    return get(`${this.baseUrl}/stats`);
  }

  /**
   * 获取客户的项目列表（使用项目档案API）
   */
  static async getCustomerProjects(
    customerId: string,
    params?: { page?: number; pageSize?: number }
  ): Promise<ApiResponse<PaginatedResponse<any>>> {
    return get(`/api/v1/archives/customer/${customerId}`, params);
  }

  /**
   * 获取客户的服务工单列表
   */
  static async getCustomerServices(
    customerId: string,
    params?: { page?: number; pageSize?: number }
  ): Promise<ApiResponse<PaginatedResponse<any>>> {
    const queryParams = { ...params, customerId };
    return get("/api/v1/services", queryParams);
  }

  /**
   * 获取客户的配置项列表
   */
  static async getCustomerConfigurations(
    customerId: string,
    params?: { page?: number; pageSize?: number }
  ): Promise<ApiResponse<PaginatedResponse<any>>> {
    const queryParams = { ...params, customerId };
    return get("/api/v1/configurations", queryParams);
  }

  /**
   * 获取客户详情页面的统计信息
   */
  static async getCustomerDetailStats(customerId: string): Promise<
    ApiResponse<{
      projects: number;
      services: number;
      pendingServices: number;
      configurations: number;
    }>
  > {
    return get(`${this.baseUrl}/${customerId}/stats`);
  }
}

// 兼容旧的导出方式
export const customerService = {
  getCustomers: CustomerService.getCustomers.bind(CustomerService),
  getCustomer: CustomerService.getCustomer.bind(CustomerService),
  createCustomer: CustomerService.createCustomer.bind(CustomerService),
  updateCustomer: CustomerService.updateCustomer.bind(CustomerService),
  deleteCustomer: CustomerService.deleteCustomer.bind(CustomerService),
  getCustomerStats: CustomerService.getCustomerStats.bind(CustomerService),
  getCustomerProjects: CustomerService.getCustomerProjects.bind(CustomerService),
  getCustomerServices: CustomerService.getCustomerServices.bind(CustomerService),
  getCustomerConfigurations: CustomerService.getCustomerConfigurations.bind(CustomerService),
  getCustomerDetailStats: CustomerService.getCustomerDetailStats.bind(CustomerService),
};

// 导出默认类
export default CustomerService;
