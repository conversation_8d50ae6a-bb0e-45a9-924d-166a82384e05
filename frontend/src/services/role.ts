import { get, post, put, del } from "@/utils/request";
import type { Role } from "@/types";

export interface CreateRoleData {
  name: string;
  description: string;
  permissions: string[];
}

export interface UpdateRoleData {
  name?: string;
  description?: string;
  permissions?: string[];
}

export interface RoleListParams {
  page?: number;
  limit?: number;
  search?: string;
}

export interface RoleListResponse {
  success: boolean;
  data: {
    roles: Role[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
  message?: string;
}

export interface PermissionGroup {
  name: string;
  label: string;
  permissions: Array<{
    name: string;
    label: string;
    description: string;
  }>;
}

export interface RoleTemplate {
  name: string;
  label: string;
  description: string;
  permissions: string[];
}

export interface RoleStatsResponse {
  success: boolean;
  data: {
    totalRoles: number;
    roleUsage: Array<{
      roleName: string;
      userCount: number;
    }>;
    permissionUsage: Array<{
      permission: string;
      roleCount: number;
    }>;
  };
}

class RoleService {
  // 获取角色列表
  async getRoles(params: RoleListParams = {}): Promise<RoleListResponse> {
    return get("/api/v1/roles", { params });
  }

  // 获取所有角色（用于下拉选择）
  async getAllRoles(): Promise<{ success: boolean; data: Role[]; message?: string }> {
    return get("/api/v1/roles/all");
  }

  // 获取角色详情
  async getRole(id: string): Promise<{ success: boolean; data: Role; message?: string }> {
    return get(`/api/v1/roles/${id}`);
  }

  // 创建角色
  async createRole(
    data: CreateRoleData
  ): Promise<{ success: boolean; data: Role; message?: string }> {
    return post("/api/v1/roles", data);
  }

  // 更新角色
  async updateRole(
    id: string,
    data: UpdateRoleData
  ): Promise<{ success: boolean; data: Role; message?: string }> {
    return put(`/api/v1/roles/${id}`, data);
  }

  // 删除角色
  async deleteRole(id: string): Promise<{ success: boolean; message?: string }> {
    return del(`/api/v1/roles/${id}`);
  }

  // 获取系统权限列表
  async getSystemPermissions(): Promise<{ success: boolean; data: string[]; message?: string }> {
    return get("/api/v1/roles/permissions");
  }

  // 获取权限分组
  async getPermissionGroups(): Promise<{
    success: boolean;
    data: PermissionGroup[];
    message?: string;
  }> {
    return get("/api/v1/roles/permissions/groups");
  }

  // 获取角色模板
  async getRoleTemplates(): Promise<{ success: boolean; data: RoleTemplate[]; message?: string }> {
    return get("/api/v1/roles/templates");
  }

  // 从模板创建角色
  async createRoleFromTemplate(
    templateName: string,
    customName?: string,
    customDescription?: string
  ): Promise<{ success: boolean; data: Role; message?: string }> {
    return post("/api/v1/roles/from-template", {
      templateName,
      customName,
      customDescription,
    });
  }

  // 复制角色
  async duplicateRole(
    id: string,
    newName: string,
    newDescription?: string
  ): Promise<{ success: boolean; data: Role; message?: string }> {
    return post(`/api/v1/roles/${id}/duplicate`, {
      newName,
      newDescription,
    });
  }

  // 获取角色统计信息
  async getRoleStats(): Promise<RoleStatsResponse> {
    return get("/api/v1/roles/stats");
  }
}

export const roleService = new RoleService();
