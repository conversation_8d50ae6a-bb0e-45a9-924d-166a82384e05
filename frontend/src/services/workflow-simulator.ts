/**
 * 工作流模拟执行引擎
 * 在设计器中模拟工作流执行，用于测试和调试
 */
import type { 
  WorkflowDefinition, 
  DesignerNode, 
  DesignerConnection, 
  WorkflowExecution, 
  WorkflowExecutionStep,
  WorkflowStepType
} from '../types/workflow';

export interface SimulationConfig {
  stepDelay?: number; // 步骤执行延迟（毫秒）
  failureRate?: number; // 失败率（0-1）
  enableRealRequests?: boolean; // 是否启用真实请求
}

export class WorkflowSimulator {
  private config: SimulationConfig;
  private executionCallbacks: Set<(execution: WorkflowExecution) => void> = new Set();

  constructor(config: SimulationConfig = {}) {
    this.config = {
      stepDelay: 1000,
      failureRate: 0.1,
      enableRealRequests: false,
      ...config
    };
  }

  /**
   * 添加执行状态监听器
   */
  onExecutionUpdate(callback: (execution: WorkflowExecution) => void) {
    this.executionCallbacks.add(callback);
    return () => this.executionCallbacks.delete(callback);
  }

  /**
   * 模拟执行工作流
   */
  async simulateExecution(
    workflow: WorkflowDefinition,
    nodes: DesignerNode[],
    connections: DesignerConnection[],
    context: Record<string, any> = {}
  ): Promise<WorkflowExecution> {
    const sessionId = this.generateSessionId();
    const startTime = new Date().toISOString();

    // 创建初始执行对象
    const execution: WorkflowExecution = {
      id: `sim_${sessionId}`,
      sessionId,
      workflowId: workflow.id,
      status: 'RUNNING',
      startedAt: startTime,
      context: { ...context },
      steps: [],
      variables: {}
    };

    this.notifyExecutionUpdate(execution);

    try {
      // 构建执行路径
      const executionPath = this.buildExecutionPath(nodes, connections);
      
      // 逐步执行
      for (const nodeId of executionPath) {
        const node = nodes.find(n => n.id === nodeId);
        if (!node) continue;

        const stepResult = await this.executeStep(node, execution);
        execution.steps.push(stepResult);
        
        // 如果步骤失败且没有错误处理，终止执行
        if (stepResult.status === 'FAILED' && !node.step.config.continueOnError) {
          execution.status = 'FAILED';
          execution.completedAt = new Date().toISOString();
          execution.error = stepResult.error;
          break;
        }

        // 更新变量
        if (stepResult.output) {
          execution.variables = {
            ...execution.variables,
            [`${node.step.name || node.id}_output`]: stepResult.output
          };
        }

        this.notifyExecutionUpdate(execution);
      }

      // 如果没有失败，标记为完成
      if (execution.status === 'RUNNING') {
        execution.status = 'COMPLETED';
        execution.completedAt = new Date().toISOString();
      }

    } catch (error) {
      execution.status = 'FAILED';
      execution.completedAt = new Date().toISOString();
      execution.error = error instanceof Error ? error.message : String(error);
    }

    this.notifyExecutionUpdate(execution);
    return execution;
  }

  /**
   * 构建执行路径
   */
  private buildExecutionPath(nodes: DesignerNode[], connections: DesignerConnection[]): string[] {
    const path: string[] = [];
    const visited = new Set<string>();
    
    // 找到起始节点（没有输入连接的节点）
    const startNodes = nodes.filter(node => 
      !connections.some(conn => conn.targetNodeId === node.id)
    );

    // 深度优先遍历
    const traverse = (nodeId: string) => {
      if (visited.has(nodeId)) return;
      
      visited.add(nodeId);
      path.push(nodeId);
      
      // 找到下一个节点
      const outgoingConnections = connections.filter(conn => conn.sourceNodeId === nodeId);
      outgoingConnections.forEach(conn => {
        traverse(conn.targetNodeId);
      });
    };

    // 从每个起始节点开始遍历
    startNodes.forEach(node => traverse(node.id));
    
    return path;
  }

  /**
   * 执行单个步骤
   */
  private async executeStep(node: DesignerNode, execution: WorkflowExecution): Promise<WorkflowExecutionStep> {
    const startTime = new Date().toISOString();
    const step: WorkflowExecutionStep = {
      id: this.generateStepId(),
      stepId: node.id,
      status: 'RUNNING',
      startTime,
      input: this.buildStepInput(node, execution)
    };

    // 模拟延迟
    if (this.config.stepDelay) {
      await this.delay(this.config.stepDelay);
    }

    try {
      // 根据步骤类型执行不同的模拟逻辑
      const result = await this.simulateStepExecution(node, step.input, execution);
      
      step.status = 'COMPLETED';
      step.endTime = new Date().toISOString();
      step.duration = new Date(step.endTime).getTime() - new Date(startTime).getTime();
      step.output = result;

    } catch (error) {
      step.status = 'FAILED';
      step.endTime = new Date().toISOString();
      step.duration = new Date(step.endTime).getTime() - new Date(startTime).getTime();
      step.error = error instanceof Error ? error.message : String(error);
    }

    return step;
  }

  /**
   * 模拟步骤执行
   */
  private async simulateStepExecution(
    node: DesignerNode, 
    input: any, 
    execution: WorkflowExecution
  ): Promise<any> {
    // 随机失败模拟
    if (Math.random() < (this.config.failureRate || 0)) {
      throw new Error(`模拟执行失败: ${node.step.name || node.step.type}`);
    }

    switch (node.step.type) {
      case 'HTTP_REQUEST':
        return this.simulateHttpRequest(node, input);
      
      case 'DATABASE_OPERATION':
        return this.simulateDatabaseOperation(node, input);
      
      case 'NOTIFICATION':
        return this.simulateNotification(node, input);
      
      case 'CONDITION':
        return this.simulateCondition(node, input, execution);
      
      case 'LOOP':
        return this.simulateLoop(node, input);
      
      case 'DELAY':
        return this.simulateDelay(node, input);
      
      case 'SCRIPT':
        return this.simulateScript(node, input);
      
      case 'APPROVAL':
        return this.simulateApproval(node, input);
      
      case 'FILE_OPERATION':
        return this.simulateFileOperation(node, input);
      
      default:
        return { message: `已执行 ${node.step.type} 步骤`, timestamp: new Date().toISOString() };
    }
  }

  /**
   * 模拟HTTP请求
   */
  private async simulateHttpRequest(node: DesignerNode, input: any): Promise<any> {
    const config = node.step.config;
    
    if (this.config.enableRealRequests && config.url) {
      try {
        // 这里可以实现真实的HTTP请求
        const response = await fetch(config.url, {
          method: config.method || 'GET',
          headers: config.headers || {},
          body: config.method !== 'GET' ? JSON.stringify(config.body) : undefined
        });
        
        return {
          status: response.status,
          statusText: response.statusText,
          data: await response.json()
        };
      } catch (error) {
        throw new Error(`HTTP请求失败: ${error}`);
      }
    }
    
    // 模拟响应
    return {
      status: 200,
      statusText: 'OK',
      data: {
        message: '模拟HTTP响应',
        method: config.method || 'GET',
        url: config.url || 'http://example.com',
        timestamp: new Date().toISOString(),
        simulatedData: {
          id: Math.random().toString(36).substr(2, 9),
          result: 'success'
        }
      }
    };
  }

  /**
   * 模拟数据库操作
   */
  private async simulateDatabaseOperation(node: DesignerNode, input: any): Promise<any> {
    const config = node.step.config;
    const query = config.query || 'SELECT * FROM users';
    
    // 根据SQL类型生成不同的模拟结果
    if (query.toUpperCase().includes('SELECT')) {
      return {
        data: [
          { id: 1, name: '张三', email: '<EMAIL>' },
          { id: 2, name: '李四', email: '<EMAIL>' }
        ],
        rowCount: 2,
        duration: Math.floor(Math.random() * 100) + 10
      };
    } else if (query.toUpperCase().includes('INSERT')) {
      return {
        insertId: Math.floor(Math.random() * 1000) + 1,
        rowCount: 1,
        duration: Math.floor(Math.random() * 50) + 5
      };
    } else if (query.toUpperCase().includes('UPDATE')) {
      return {
        rowCount: Math.floor(Math.random() * 5) + 1,
        duration: Math.floor(Math.random() * 75) + 8
      };
    } else if (query.toUpperCase().includes('DELETE')) {
      return {
        rowCount: Math.floor(Math.random() * 3) + 1,
        duration: Math.floor(Math.random() * 60) + 6
      };
    }
    
    return {
      result: 'success',
      duration: Math.floor(Math.random() * 100) + 10
    };
  }

  /**
   * 模拟通知发送
   */
  private async simulateNotification(node: DesignerNode, input: any): Promise<any> {
    const config = node.step.config;
    
    return {
      success: true,
      messageId: Math.random().toString(36).substr(2, 12),
      sentCount: Array.isArray(config.recipients) ? config.recipients.length : 1,
      failedCount: 0,
      notificationType: config.notificationType || 'email',
      timestamp: new Date().toISOString()
    };
  }

  /**
   * 模拟条件判断
   */
  private async simulateCondition(node: DesignerNode, input: any, execution: WorkflowExecution): Promise<any> {
    // 随机返回true或false
    const result = Math.random() > 0.5;
    
    return {
      condition: node.step.config.condition || 'true',
      result,
      message: result ? '条件满足，继续执行' : '条件不满足，跳过后续步骤'
    };
  }

  /**
   * 模拟循环执行
   */
  private async simulateLoop(node: DesignerNode, input: any): Promise<any> {
    const iterations = Math.floor(Math.random() * 5) + 1;
    
    return {
      iterations,
      results: Array.from({ length: iterations }, (_, i) => ({
        iteration: i + 1,
        result: `循环第 ${i + 1} 次执行结果`,
        timestamp: new Date().toISOString()
      }))
    };
  }

  /**
   * 模拟延时等待
   */
  private async simulateDelay(node: DesignerNode, input: any): Promise<any> {
    const delayMs = node.step.config.delay || 1000;
    await this.delay(Math.min(delayMs, 5000)); // 最大延迟5秒
    
    return {
      delay: delayMs,
      actualDelay: delayMs,
      message: `延时 ${delayMs}ms 执行完成`
    };
  }

  /**
   * 模拟脚本执行
   */
  private async simulateScript(node: DesignerNode, input: any): Promise<any> {
    const script = node.step.config.script || 'console.log("Hello World")';
    
    return {
      script,
      output: '脚本执行成功',
      logs: [
        'Script execution started',
        'Processing input data',
        'Script execution completed'
      ],
      exitCode: 0
    };
  }

  /**
   * 模拟审批流程
   */
  private async simulateApproval(node: DesignerNode, input: any): Promise<any> {
    // 模拟自动审批（实际应该是人工审批）
    const approved = Math.random() > 0.3; // 70%的通过率
    
    return {
      approved,
      approver: '系统模拟审批',
      approvalTime: new Date().toISOString(),
      comments: approved ? '审批通过' : '审批拒绝，需要重新提交'
    };
  }

  /**
   * 模拟文件操作
   */
  private async simulateFileOperation(node: DesignerNode, input: any): Promise<any> {
    const operation = node.step.config.operation || 'read';
    
    return {
      operation,
      fileName: node.step.config.fileName || 'example.txt',
      fileSize: Math.floor(Math.random() * 10000) + 1000,
      success: true,
      message: `文件${operation === 'read' ? '读取' : operation === 'write' ? '写入' : '操作'}成功`
    };
  }

  /**
   * 构建步骤输入数据
   */
  private buildStepInput(node: DesignerNode, execution: WorkflowExecution): any {
    const input: any = {
      stepName: node.step.name,
      stepType: node.step.type,
      config: node.step.config,
      context: execution.context,
      variables: execution.variables
    };

    // 添加变量替换逻辑
    const replaceVariables = (obj: any): any => {
      if (typeof obj === 'string') {
        return obj.replace(/\$\{([^}]+)\}/g, (match, varName) => {
          return execution.variables[varName] || execution.context[varName] || match;
        });
      } else if (Array.isArray(obj)) {
        return obj.map(replaceVariables);
      } else if (obj && typeof obj === 'object') {
        const result: any = {};
        for (const [key, value] of Object.entries(obj)) {
          result[key] = replaceVariables(value);
        }
        return result;
      }
      return obj;
    };

    return replaceVariables(input);
  }

  /**
   * 通知执行状态更新
   */
  private notifyExecutionUpdate(execution: WorkflowExecution) {
    this.executionCallbacks.forEach(callback => {
      try {
        callback(execution);
      } catch (error) {
        console.error('Error in execution callback:', error);
      }
    });
  }

  /**
   * 延迟函数
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 生成会话ID
   */
  private generateSessionId(): string {
    return `sim_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 生成步骤ID
   */
  private generateStepId(): string {
    return `step_${Date.now()}_${Math.random().toString(36).substr(2, 6)}`;
  }

  /**
   * 停止执行（在实际实现中可能需要更复杂的逻辑）
   */
  stopExecution(sessionId: string): void {
    console.log(`Stopping execution: ${sessionId}`);
    // 实际实现中，这里应该中断正在执行的步骤
  }
}

// 导出单例实例
export const workflowSimulator = new WorkflowSimulator();