/**
 * AI管理服务
 */

import { get, post, put, del } from "@/utils/request";

// AI配置接口类型
export interface AIConfiguration {
  id: string;
  provider: "OPENAI" | "ANTHROPIC" | "GEMINI";
  model: string;
  apiKey: string;
  maxTokens: number;
  temperature: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

// 创建AI配置请求
export interface CreateAIConfigurationRequest {
  provider: "OPENAI" | "ANTHROPIC" | "GEMINI";
  model: string;
  apiKey: string;
  maxTokens: number;
  temperature: number;
  isActive?: boolean;
}

// 更新AI配置请求
export interface UpdateAIConfigurationRequest {
  model?: string;
  apiKey?: string;
  maxTokens?: number;
  temperature?: number;
  isActive?: boolean;
}

// 提示词模板接口类型
export interface PromptTemplate {
  id: string;
  name: string;
  type: "TICKET_ANALYSIS" | "CATEGORY_SUGGESTION" | "PRIORITY_ASSESSMENT";
  provider: "OPENAI" | "ANTHROPIC" | "GEMINI";
  content: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

// 创建提示词模板请求
export interface CreatePromptTemplateRequest {
  name: string;
  type: "TICKET_ANALYSIS" | "CATEGORY_SUGGESTION" | "PRIORITY_ASSESSMENT";
  provider: "OPENAI" | "ANTHROPIC" | "GEMINI";
  content: string;
  isActive?: boolean;
}

// 更新提示词模板请求
export interface UpdatePromptTemplateRequest {
  name?: string;
  content?: string;
  isActive?: boolean;
}

// AI分析请求历史
export interface AIAnalysisRequest {
  id: string;
  provider: string;
  model: string;
  inputData: string;
  outputData: string;
  status: "PENDING" | "COMPLETED" | "FAILED";
  processingTime?: number;
  errorMessage?: string;
  createdAt: string;
  updatedAt: string;
}

// AI统计数据
export interface AIStatistics {
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  averageResponseTime: number;
  successRate: number;
  providerUsage: Record<
    string,
    {
      count: number;
      percentage: number;
    }
  >;
  templateUsage: Record<
    string,
    {
      count: number;
      percentage: number;
    }
  >;
  dailyStats: Array<{
    date: string;
    requests: number;
    successRate: number;
  }>;
}

// 导入统一的类型定义
import { PaginatedResponse } from '@/utils/data-helpers';
import type { BackendAIConfiguration } from '@/types/ai';

/**
 * AI配置管理API
 */

// 获取当前有效的AI配置
export function getCurrentAIConfiguration() {
  return get<{ success: boolean; data: BackendAIConfiguration }>("/api/v1/ai/configuration/current");
}

// 获取AI配置列表
export function getAIConfigurations(
  params: {
    page?: number;
    limit?: number;
    provider?: string;
    isActive?: boolean;
  } = {}
) {
  return get<{ success: boolean; data: PaginatedResponse<AIConfiguration> }>("/api/v1/ai/configurations", { params });
}

// 获取AI配置详情
export function getAIConfiguration(id: string) {
  return get<AIConfiguration>(`/api/v1/ai/config/${id}`);
}

// 创建AI配置
export function createAIConfiguration(data: CreateAIConfigurationRequest) {
  return post<AIConfiguration>("/api/v1/ai/config", data);
}

// 更新AI配置
export function updateAIConfiguration(id: string, data: UpdateAIConfigurationRequest) {
  return put<AIConfiguration>(`/api/v1/ai/config/${id}`, data);
}

// 删除AI配置
export function deleteAIConfiguration(id: string) {
  return del(`/api/v1/ai/config/${id}`);
}

/**
 * 提示词模板管理API
 */

// 获取提示词模板列表
export function getPromptTemplates(
  params: {
    page?: number;
    limit?: number;
    type?: string;
    provider?: string;
    isActive?: boolean;
  } = {}
) {
  return get<{ success: boolean; data: PaginatedResponse<PromptTemplate> }>("/api/v1/ai/prompt-templates", { params });
}

// 获取提示词模板详情
export function getPromptTemplate(id: string) {
  return get<PromptTemplate>(`/api/v1/ai/prompt-templates/${id}`);
}

// 创建提示词模板
export function createPromptTemplate(data: CreatePromptTemplateRequest) {
  return post<PromptTemplate>("/api/v1/ai/prompt-templates", data);
}

// 更新提示词模板
export function updatePromptTemplate(id: string, data: UpdatePromptTemplateRequest) {
  return put<PromptTemplate>(`/api/v1/ai/prompt-templates/${id}`, data);
}

// 删除提示词模板
export function deletePromptTemplate(id: string) {
  return del(`/api/v1/ai/prompt-templates/${id}`);
}

/**
 * AI分析历史API
 */

// 获取AI分析请求历史
export function getAnalysisHistory(
  params: {
    page?: number;
    limit?: number;
    provider?: string;
    status?: string;
    startDate?: string;
    endDate?: string;
  } = {}
) {
  return get<{ success: boolean; data: PaginatedResponse<AIAnalysisRequest> }>("/api/v1/ai/analysis-requests", { params });
}

// 获取AI分析请求详情
export function getAnalysisRequest(id: string) {
  return get<AIAnalysisRequest>(`/api/v1/ai/analysis-requests/${id}`);
}

/**
 * AI统计API
 */

// 获取AI使用统计
export function getAIStatistics(
  params: {
    startDate?: string;
    endDate?: string;
    provider?: string;
  } = {}
) {
  return get<{ success: boolean; data: AIStatistics }>("/api/v1/ai/statistics", { params });
}

// 获取AI健康检查
export function getAIHealthCheck() {
  return get<{
    status: "healthy" | "degraded" | "unhealthy";
    providers: Record<
      string,
      {
        status: "online" | "offline" | "error";
        responseTime?: number;
        lastCheck: string;
      }
    >;
    lastUpdated: string;
  }>("/api/v1/ai/health");
}

/**
 * AI测试API
 */

// 测试AI配置
export function testAIConfiguration(
  id: string,
  testData: {
    prompt: string;
    maxTokens?: number;
    temperature?: number;
  }
) {
  return post<{
    success: boolean;
    response?: string;
    responseTime: number;
    tokensUsed?: number;
    error?: string;
  }>(`/api/v1/ai/configurations/${id}/test`, testData);
}

// 测试提示词模板
export function testPromptTemplate(
  id: string,
  testData: {
    variables: Record<string, any>;
    maxTokens?: number;
    temperature?: number;
  }
) {
  return post<{
    success: boolean;
    response?: string;
    responseTime: number;
    tokensUsed?: number;
    error?: string;
  }>(`/api/v1/ai/prompt-templates/${id}/test`, testData);
}
