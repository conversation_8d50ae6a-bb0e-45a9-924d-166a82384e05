/**
 * 权限模板服务
 *
 * 提供权限模板相关的API调用
 */

import type { ApiResponse } from "@/types";
import { get, post, put, del } from "@/utils/request";

// ==================== 临时类型定义 ====================

type ID = string;

interface PermissionTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  permissions: string[];
  isSystem: boolean;
  isDefault: boolean;
  usageCount: number;
  version: string;
  createdAt: string;
  updatedAt: string;
}

interface PermissionTemplateDetail extends PermissionTemplate {
  appliedRoles: string[];
  usageHistory: any[];
}

interface PermissionTemplateQuery {
  page?: number;
  pageSize?: number;
  category?: string;
  search?: string;
  isDefault?: boolean;
  isSystem?: boolean;
  createdBy?: string;
  sortBy?: string;
  sortOrder?: string;
}

interface PermissionTemplateListResponse {
  success: boolean;
  data: {
    templates: PermissionTemplate[];
    total: number;
    page: number;
    pageSize: number;
    totalPages: number;
  };
}

interface PermissionTemplateDetailResponse {
  success: boolean;
  data: PermissionTemplateDetail;
}

interface PermissionTemplateStatsResponse {
  success: boolean;
  data: {
    totalTemplates: number;
    activeTemplates: number;
    systemTemplates: number;
    customTemplates: number;
  };
}

interface CreatePermissionTemplateRequest {
  name: string;
  description: string;
  category: string;
  permissions: string[];
  isDefault?: boolean;
}

interface CreatePermissionTemplateResponse {
  success: boolean;
  data: PermissionTemplate;
}

interface UpdatePermissionTemplateRequest {
  name?: string;
  description?: string;
  category?: string;
  permissions?: string[];
  isDefault?: boolean;
}

interface UpdatePermissionTemplateResponse {
  success: boolean;
  data: PermissionTemplate;
}

interface CopyPermissionTemplateRequest {
  name?: string;
  description?: string;
}

interface CopyTemplateResponse {
  success: boolean;
  data: PermissionTemplate;
}

interface ApplyTemplateToRolesRequest {
  roleIds: string[];
  mode: "replace" | "merge";
}

interface ApplyTemplateResponse {
  success: boolean;
  data: {
    appliedRoles: string[];
    skippedRoles: string[];
  };
}

interface BatchApplyTemplateRequest {
  templateId: string;
  roleIds: string[];
  mode: "replace" | "merge";
}

interface BatchApplyTemplateResponse {
  success: boolean;
  data: {
    appliedRoles: string[];
    skippedRoles: string[];
  };
}

interface ComparePermissionTemplatesRequest {
  templateIds: string[];
}

interface CompareTemplatesResponse {
  success: boolean;
  data: {
    differences: any[];
    common: string[];
  };
}

interface ExportPermissionTemplatesRequest {
  templateIds: string[];
  format: "json" | "csv";
}

interface ImportPermissionTemplatesRequest {
  templates: Partial<PermissionTemplate>[];
  mode: "replace" | "merge";
}

interface ImportTemplatesResponse {
  success: boolean;
  data: {
    imported: number;
    skipped: number;
    errors: string[];
  };
}

interface CreateDefaultTemplatesResponse {
  success: boolean;
  data: {
    created: PermissionTemplate[];
  };
}

interface AllPermissionsResponse {
  success: boolean;
  data: {
    permissions: PermissionDetail[];
    groups: PermissionGroup[];
  };
}

interface PermissionGroupsResponse {
  success: boolean;
  data: PermissionGroup[];
}

interface PermissionGroup {
  key: string;
  label: string;
  description: string;
  permissions: PermissionDetail[];
}

interface PermissionDetail {
  key: string;
  name: string;
  description: string;
  group: string;
}

// ==================== 权限模板基础服务 ====================

export class PermissionTemplateService {
  private readonly baseUrl = "/api/v1/permission-templates";

  /**
   * 获取权限模板列表
   */
  async getTemplates(
    query: PermissionTemplateQuery = {}
  ): Promise<ApiResponse<PermissionTemplateListResponse>> {
    const params = new URLSearchParams();

    if (query.page) params.append("page", query.page.toString());
    if (query.pageSize) params.append("pageSize", query.pageSize.toString());
    if (query.category) params.append("category", query.category);
    if (query.search) params.append("search", query.search);
    if (query.isDefault !== undefined) params.append("isDefault", query.isDefault.toString());
    if (query.isSystem !== undefined) params.append("isSystem", query.isSystem.toString());
    if (query.createdBy) params.append("createdBy", query.createdBy);
    if (query.sortBy) params.append("sortBy", query.sortBy);
    if (query.sortOrder) params.append("sortOrder", query.sortOrder);

    return get<PermissionTemplateListResponse>(`${this.baseUrl}?${params.toString()}`);
  }

  /**
   * 获取权限模板详情
   */
  async getTemplateById(id: ID): Promise<ApiResponse<PermissionTemplateDetailResponse>> {
    return get<PermissionTemplateDetailResponse>(`${this.baseUrl}/${id}`);
  }

  /**
   * 创建权限模板
   */
  async createTemplate(
    data: CreatePermissionTemplateRequest
  ): Promise<ApiResponse<CreatePermissionTemplateResponse>> {
    return post<CreatePermissionTemplateResponse>(this.baseUrl, data);
  }

  /**
   * 更新权限模板
   */
  async updateTemplate(
    id: ID,
    data: UpdatePermissionTemplateRequest
  ): Promise<ApiResponse<UpdatePermissionTemplateResponse>> {
    return put<UpdatePermissionTemplateResponse>(`${this.baseUrl}/${id}`, data);
  }

  /**
   * 删除权限模板
   */
  async deleteTemplate(id: ID): Promise<ApiResponse<void>> {
    return del<void>(`${this.baseUrl}/${id}`);
  }

  /**
   * 复制权限模板
   */
  async copyTemplate(
    id: ID,
    data: CopyPermissionTemplateRequest = {}
  ): Promise<ApiResponse<CopyTemplateResponse>> {
    return post<CopyTemplateResponse>(`${this.baseUrl}/${id}/copy`, data);
  }

  /**
   * 获取权限模板统计信息
   */
  async getTemplateStats(): Promise<ApiResponse<PermissionTemplateStatsResponse>> {
    return get<PermissionTemplateStatsResponse>(`${this.baseUrl}/stats`);
  }

  // ==================== 模板应用相关 ====================

  /**
   * 应用模板到角色
   */
  async applyTemplateToRoles(
    templateId: ID,
    data: ApplyTemplateToRolesRequest
  ): Promise<ApiResponse<ApplyTemplateResponse>> {
    return post<ApplyTemplateResponse>(`${this.baseUrl}/${templateId}/apply`, data);
  }

  /**
   * 批量应用模板
   */
  async batchApplyTemplate(
    data: BatchApplyTemplateRequest
  ): Promise<ApiResponse<BatchApplyTemplateResponse>> {
    return post<BatchApplyTemplateResponse>(`${this.baseUrl}/batch-apply`, data);
  }

  // ==================== 模板工具相关 ====================

  /**
   * 比较权限模板
   */
  async compareTemplates(
    data: ComparePermissionTemplatesRequest
  ): Promise<ApiResponse<CompareTemplatesResponse>> {
    return post<CompareTemplatesResponse>(`${this.baseUrl}/compare`, data);
  }

  /**
   * 导出权限模板
   */
  async exportTemplates(data: ExportPermissionTemplatesRequest): Promise<Blob> {
    const response = await post<Blob>(`${this.baseUrl}/export`, data);
    return response.data;
  }

  /**
   * 导入权限模板
   */
  async importTemplates(
    data: ImportPermissionTemplatesRequest
  ): Promise<ApiResponse<ImportTemplatesResponse>> {
    return post<ImportTemplatesResponse>(`${this.baseUrl}/import`, data);
  }

  /**
   * 创建默认权限模板
   */
  async createDefaultTemplates(): Promise<ApiResponse<CreateDefaultTemplatesResponse>> {
    return post<CreateDefaultTemplatesResponse>(`${this.baseUrl}/create-defaults`);
  }

  // ==================== 权限相关接口 ====================

  /**
   * 获取所有权限
   */
  async getAllPermissions(): Promise<ApiResponse<AllPermissionsResponse>> {
    return get<AllPermissionsResponse>("/api/v1/permissions");
  }

  /**
   * 获取权限分组
   */
  async getPermissionGroups(): Promise<ApiResponse<PermissionGroup[]>> {
    // 后端返回 { success, data: PermissionGroup[] }，这里直接声明为 PermissionGroup[]，便于前端直接使用数组
    return get<PermissionGroup[]>("/api/v1/permissions/groups");
  }

  // ==================== 工具方法 ====================

  /**
   * 验证模板数据
   */
  validateTemplateData(data: CreatePermissionTemplateRequest | UpdatePermissionTemplateRequest): {
    valid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];

    // 验证名称
    if ("name" in data && data.name) {
      if (data.name.length < 1) {
        errors.push("模板名称不能为空");
      } else if (data.name.length > 100) {
        errors.push("模板名称不能超过100个字符");
      }
    }

    // 验证权限列表
    if ("permissions" in data && data.permissions) {
      if (data.permissions.length === 0) {
        errors.push("权限列表不能为空");
      } else {
        // 验证权限格式
        const invalidPermissions = data.permissions.filter(
          (permission: string) => !/^[a-z_]+:[a-z_]+(?::[a-z_]+)*$/.test(permission)
        );
        if (invalidPermissions.length > 0) {
          errors.push(`权限格式无效: ${invalidPermissions.join(", ")}`);
        }
      }
    }

    // 验证分类
    if ("category" in data && data.category) {
      const validCategories = ["SYSTEM", "BUSINESS", "SERVICE", "READONLY", "CUSTOM"];
      if (!validCategories.includes(data.category)) {
        errors.push("无效的权限模板分类");
      }
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  }

  /**
   * 格式化模板显示信息
   */
  formatTemplateForDisplay(template: PermissionTemplate): {
    formattedPermissions: string;
    categoryLabel: string;
    statusText: string;
    usageText: string;
  } {
    // 格式化权限列表
    const formattedPermissions =
      template.permissions.length > 5
        ? `${template.permissions.slice(0, 5).join(", ")}... (+${template.permissions.length - 5})`
        : template.permissions.join(", ");

    // 分类标签
    const categoryLabels: Record<string, string> = {
      SYSTEM: "系统管理",
      BUSINESS: "业务管理",
      SERVICE: "服务运维",
      READONLY: "只读权限",
      CUSTOM: "自定义",
    };
    const categoryLabel = categoryLabels[template.category] || template.category;

    // 状态文本
    let statusText = "";
    if (template.isSystem) {
      statusText = "系统模板";
    } else if (template.isDefault) {
      statusText = "默认模板";
    } else {
      statusText = "自定义模板";
    }

    // 使用情况文本
    const usageText = template.usageCount > 0 ? `${template.usageCount} 个角色使用` : "暂未使用";

    return {
      formattedPermissions,
      categoryLabel,
      statusText,
      usageText,
    };
  }

  /**
   * 生成模板导出文件名
   */
  generateExportFilename(templateIds: ID[], format: "json" | "csv" = "json"): string {
    const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, "-");
    const count = templateIds.length;
    const suffix = count === 1 ? "template" : "templates";
    return `permission-${suffix}-${count}-${timestamp}.${format}`;
  }
}

// 导出服务实例
export const permissionTemplateService = new PermissionTemplateService();
export default permissionTemplateService;

// 导出类型供组件使用
export type {
  PermissionTemplate,
  PermissionTemplateDetail,
  PermissionTemplateQuery,
  CreatePermissionTemplateRequest,
  UpdatePermissionTemplateRequest,
  PermissionGroup,
  PermissionDetail,
};
