import { get, post, put, del } from "@/utils/request";

export interface EmailTemplate {
  id: string;
  name: string;
  type: string;
  category: "SYSTEM" | "BUSINESS" | "NOTIFICATION" | "SECURITY";
  subject: string;
  content: string;
  description?: string;
  variables?: string[];
  enabled: boolean;
  isSystem: boolean;
  version: string;
  createdBy: string;
  updatedBy?: string;
  createdAt: string;
  updatedAt: string;
  createdByUser?: {
    id: string;
    username: string;
    fullName?: string;
  };
  updatedByUser?: {
    id: string;
    username: string;
    fullName?: string;
  };
}

export interface EmailTemplateCreate {
  name: string;
  type: string;
  category?: "SYSTEM" | "BUSINESS" | "NOTIFICATION" | "SECURITY";
  subject: string;
  content: string;
  description?: string;
  variables?: string[];
  enabled?: boolean;
}

export interface EmailTemplateUpdate extends Partial<EmailTemplateCreate> {}

export interface EmailTemplatePreview {
  id: string;
  name: string;
  type: string;
  originalSubject: string;
  originalContent: string;
  renderedSubject: string;
  renderedContent: string;
  variables?: string[];
  providedVariables: Record<string, any>;
}

export interface EmailTemplateGrouped {
  [category: string]: EmailTemplate[];
}

export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  pagination?: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

export const emailTemplateService = {
  /**
   * 获取邮件模板列表
   */
  async getEmailTemplates(params?: {
    category?: string;
    type?: string;
    enabled?: boolean;
    page?: number;
    limit?: number;
  }): Promise<ApiResponse<EmailTemplate[]>> {
    const queryParams = new URLSearchParams();
    if (params?.category) queryParams.append("category", params.category);
    if (params?.type) queryParams.append("type", params.type);
    if (params?.enabled !== undefined) queryParams.append("enabled", params.enabled.toString());
    if (params?.page) queryParams.append("page", params.page.toString());
    if (params?.limit) queryParams.append("limit", params.limit.toString());

    const url = `/api/v1/email-templates${queryParams.toString() ? `?${queryParams.toString()}` : ""}`;
    return get<EmailTemplate[]>(url);
  },

  /**
   * 根据分类获取邮件模板（分组）
   */
  async getEmailTemplatesByCategory(): Promise<ApiResponse<EmailTemplateGrouped>> {
    return get<EmailTemplateGrouped>("/api/v1/email-templates/categories");
  },

  /**
   * 获取单个邮件模板
   */
  async getEmailTemplate(id: string): Promise<ApiResponse<EmailTemplate>> {
    return get<EmailTemplate>(`/api/v1/email-templates/${id}`);
  },

  /**
   * 创建邮件模板
   */
  async createEmailTemplate(data: EmailTemplateCreate): Promise<ApiResponse<EmailTemplate>> {
    return post<EmailTemplate>("/api/v1/email-templates", data);
  },

  /**
   * 更新邮件模板
   */
  async updateEmailTemplate(
    id: string,
    data: EmailTemplateUpdate
  ): Promise<ApiResponse<EmailTemplate>> {
    return put<EmailTemplate>(`/api/v1/email-templates/${id}`, data);
  },

  /**
   * 删除邮件模板
   */
  async deleteEmailTemplate(id: string): Promise<ApiResponse<void>> {
    return del<void>(`/api/v1/email-templates/${id}`);
  },

  /**
   * 复制邮件模板
   */
  async copyEmailTemplate(id: string, name?: string): Promise<ApiResponse<EmailTemplate>> {
    return post<EmailTemplate>(`/api/v1/email-templates/${id}/copy`, {
      name,
    });
  },

  /**
   * 预览邮件模板
   */
  async previewEmailTemplate(
    id: string,
    variables?: Record<string, any>
  ): Promise<ApiResponse<EmailTemplatePreview>> {
    return post<EmailTemplatePreview>(`/api/v1/email-templates/${id}/preview`, {
      variables,
    });
  },

  /**
   * 启用/禁用邮件模板
   */
  async toggleEmailTemplate(id: string, enabled?: boolean): Promise<ApiResponse<EmailTemplate>> {
    return put<EmailTemplate>(`/api/v1/email-templates/${id}/toggle`, {
      enabled,
    });
  },

  /**
   * 测试邮件模板发送
   */
  async testEmailTemplate(
    templateId: string,
    testEmail: string,
    variables?: Record<string, any>
  ): Promise<
    ApiResponse<{
      logId?: string;
      templateId: string;
      recipient: string;
      variables: Record<string, any>;
    }>
  > {
    return post<{
      logId?: string;
      templateId: string;
      recipient: string;
      variables: Record<string, any>;
    }>("/api/v1/system-config/test-template-email", {
      templateId,
      testEmail,
      variables,
    });
  },
};

export default emailTemplateService;
