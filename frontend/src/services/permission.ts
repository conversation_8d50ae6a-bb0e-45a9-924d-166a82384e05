/**
 * 权限管理服务
 *
 * 提供权限相关的API调用
 */

import type { ApiResponse } from "@/types";
import { get, post, put, del } from "@/utils/request";

// 权限定义接口
export interface Permission {
  resource: string;
  action: string;
  description?: string;
}

// 权限组接口
export interface PermissionGroup {
  group: string;
  label: string;
  description?: string;
  permissions: Permission[];
}

// 权限检查请求
export interface PermissionCheckRequest {
  userId: string;
  resource: string;
  action: string;
  context?: Record<string, any>;
}

// 权限检查响应
export interface PermissionCheckResponse {
  allowed: boolean;
  reason?: string;
  context?: Record<string, any>;
}

// 批量权限检查请求
export interface BatchPermissionCheckRequest {
  userId: string;
  checks: Array<{
    resource: string;
    action: string;
    context?: Record<string, any>;
  }>;
}

// 批量权限检查响应
export interface BatchPermissionCheckResponse {
  results: Record<string, PermissionCheckResponse>;
}

// 权限矩阵接口
export interface PermissionMatrix {
  resources: string[];
  actions: string[];
  matrix: Record<string, Record<string, boolean>>;
}

// 用户权限概览
export interface UserPermissionOverview {
  userId: string;
  userInfo: {
    id: string;
    username: string;
    fullName: string;
    email: string;
  };
  roles: Array<{
    id: string;
    name: string;
    permissions: string[];
  }>;
  directPermissions: string[];
  effectivePermissions: string[];
  permissionMatrix: PermissionMatrix;
  lastUpdated: string;
}

export const permissionService = {
  /**
   * 获取所有权限列表
   */
  async getAll(): Promise<ApiResponse<Permission[]>> {
    return get<Permission[]>("/api/v1/permissions");
  },

  /**
   * 按组获取权限
   */
  async getByGroups(): Promise<ApiResponse<PermissionGroup[]>> {
    return get<PermissionGroup[]>("/api/v1/permissions/groups");
  },

  /**
   * 获取权限资源列表
   */
  async getResources(): Promise<ApiResponse<string[]>> {
    return get<string[]>("/api/v1/permissions/resources");
  },

  /**
   * 获取权限操作列表
   */
  async getActions(resource?: string): Promise<ApiResponse<string[]>> {
    const params = resource ? { resource } : undefined;
    return get<string[]>("/api/v1/permissions/actions", params);
  },

  /**
   * 验证权限格式
   */
  async validateFormat(permission: string): Promise<
    ApiResponse<{
      valid: boolean;
      parsed?: Permission;
      errors?: string[];
    }>
  > {
    return post<{
      valid: boolean;
      parsed?: Permission;
      errors?: string[];
    }>("/api/v1/permissions/validate", { permission });
  },

  /**
   * 批量验证权限格式
   */
  async batchValidateFormat(permissions: string[]): Promise<
    ApiResponse<{
      valid: number;
      invalid: number;
      results: Array<{
        permission: string;
        valid: boolean;
        parsed?: Permission;
        errors?: string[];
      }>;
    }>
  > {
    return post<{
      valid: number;
      invalid: number;
      results: Array<{
        permission: string;
        valid: boolean;
        parsed?: Permission;
        errors?: string[];
      }>;
    }>("/api/v1/permissions/batch-validate", { permissions });
  },

  /**
   * 检查权限
   */
  async checkPermission(
    request: PermissionCheckRequest
  ): Promise<ApiResponse<PermissionCheckResponse>> {
    return post<PermissionCheckResponse>("/api/v1/permissions/check", request);
  },

  /**
   * 批量检查权限
   */
  async batchCheckPermissions(
    request: BatchPermissionCheckRequest
  ): Promise<ApiResponse<BatchPermissionCheckResponse>> {
    return post<BatchPermissionCheckResponse>("/api/v1/permissions/batch-check", request);
  },

  /**
   * 获取用户权限
   */
  async getUserPermissions(userId: string): Promise<ApiResponse<UserPermissionOverview>> {
    return get<UserPermissionOverview>(`/api/v1/permissions/user/${userId}`);
  },

  /**
   * 获取当前用户权限
   */
  async getCurrentUserPermissions(): Promise<ApiResponse<UserPermissionOverview>> {
    return get<UserPermissionOverview>("/api/v1/permissions/me");
  },

  /**
   * 获取权限矩阵
   */
  async getPermissionMatrix(
    target: "user" | "role",
    targetId: string
  ): Promise<ApiResponse<PermissionMatrix>> {
    return get<PermissionMatrix>(`/api/v1/permissions/matrix/${target}/${targetId}`);
  },

  /**
   * 获取角色权限
   */
  async getRolePermissions(roleId: string): Promise<
    ApiResponse<{
      roleId: string;
      roleName: string;
      permissions: string[];
      permissionDetails: Permission[];
    }>
  > {
    return get<{
      roleId: string;
      roleName: string;
      permissions: string[];
      permissionDetails: Permission[];
    }>(`/api/v1/permissions/role/${roleId}`);
  },

  /**
   * 更新角色权限
   */
  async updateRolePermissions(
    roleId: string,
    permissions: string[]
  ): Promise<
    ApiResponse<{
      roleId: string;
      permissions: string[];
      added: string[];
      removed: string[];
    }>
  > {
    return put<{
      roleId: string;
      permissions: string[];
      added: string[];
      removed: string[];
    }>(`/api/v1/permissions/role/${roleId}`, { permissions });
  },

  /**
   * 添加权限到角色
   */
  async addPermissionsToRole(
    roleId: string,
    permissions: string[]
  ): Promise<
    ApiResponse<{
      roleId: string;
      added: string[];
      current: string[];
    }>
  > {
    return post<{
      roleId: string;
      added: string[];
      current: string[];
    }>(`/api/v1/permissions/role/${roleId}/add`, { permissions });
  },

  /**
   * 从角色移除权限
   */
  async removePermissionsFromRole(
    roleId: string,
    permissions: string[]
  ): Promise<
    ApiResponse<{
      roleId: string;
      removed: string[];
      current: string[];
    }>
  > {
    return post<{
      roleId: string;
      removed: string[];
      current: string[];
    }>(`/api/v1/permissions/role/${roleId}/remove`, { permissions });
  },

  /**
   * 获取用户直接权限
   */
  async getUserDirectPermissions(userId: string): Promise<
    ApiResponse<{
      userId: string;
      permissions: string[];
      permissionDetails: Permission[];
    }>
  > {
    return get<{
      userId: string;
      permissions: string[];
      permissionDetails: Permission[];
    }>(`/api/v1/permissions/user/${userId}/direct`);
  },

  /**
   * 更新用户直接权限
   */
  async updateUserDirectPermissions(
    userId: string,
    permissions: string[]
  ): Promise<
    ApiResponse<{
      userId: string;
      permissions: string[];
      added: string[];
      removed: string[];
    }>
  > {
    return put<{
      userId: string;
      permissions: string[];
      added: string[];
      removed: string[];
    }>(`/api/v1/permissions/user/${userId}/direct`, { permissions });
  },

  /**
   * 添加直接权限到用户
   */
  async addDirectPermissionsToUser(
    userId: string,
    permissions: string[]
  ): Promise<
    ApiResponse<{
      userId: string;
      added: string[];
      current: string[];
    }>
  > {
    return post<{
      userId: string;
      added: string[];
      current: string[];
    }>(`/api/v1/permissions/user/${userId}/add`, { permissions });
  },

  /**
   * 从用户移除直接权限
   */
  async removeDirectPermissionsFromUser(
    userId: string,
    permissions: string[]
  ): Promise<
    ApiResponse<{
      userId: string;
      removed: string[];
      current: string[];
    }>
  > {
    return post<{
      userId: string;
      removed: string[];
      current: string[];
    }>(`/api/v1/permissions/user/${userId}/remove`, { permissions });
  },

  /**
   * 比较权限
   */
  async comparePermissions(
    source: { type: "user" | "role"; id: string },
    target: { type: "user" | "role"; id: string }
  ): Promise<
    ApiResponse<{
      sourcePermissions: string[];
      targetPermissions: string[];
      common: string[];
      sourceOnly: string[];
      targetOnly: string[];
      similarity: number;
    }>
  > {
    return post<{
      sourcePermissions: string[];
      targetPermissions: string[];
      common: string[];
      sourceOnly: string[];
      targetOnly: string[];
      similarity: number;
    }>("/api/v1/permissions/compare", { source, target });
  },

  /**
   * 获取权限统计
   */
  async getPermissionStats(): Promise<
    ApiResponse<{
      totalPermissions: number;
      totalResources: number;
      totalActions: number;
      permissionUsage: Array<{
        permission: string;
        usageCount: number;
        roles: string[];
        users: string[];
      }>;
      resourceUsage: Array<{
        resource: string;
        permissionCount: number;
        usageCount: number;
      }>;
      unusedPermissions: string[];
    }>
  > {
    return get<{
      totalPermissions: number;
      totalResources: number;
      totalActions: number;
      permissionUsage: Array<{
        permission: string;
        usageCount: number;
        roles: string[];
        users: string[];
      }>;
      resourceUsage: Array<{
        resource: string;
        permissionCount: number;
        usageCount: number;
      }>;
      unusedPermissions: string[];
    }>("/api/v1/permissions/stats");
  },

  /**
   * 导出权限
   */
  async exportPermissions(
    target?: "all" | "roles" | "users",
    format?: "json" | "csv" | "excel"
  ): Promise<
    ApiResponse<{
      downloadUrl: string;
      filename: string;
      size: number;
    }>
  > {
    const params: any = {};
    if (target) params.target = target;
    if (format) params.format = format;
    return post<{
      downloadUrl: string;
      filename: string;
      size: number;
    }>("/api/v1/permissions/export", params);
  },

  /**
   * 导入权限
   */
  async importPermissions(
    file: File,
    options: {
      overwrite?: boolean;
      validateOnly?: boolean;
      targetType?: "roles" | "users";
    } = {}
  ): Promise<
    ApiResponse<{
      imported: number;
      updated: number;
      skipped: number;
      errors: Array<{
        row: number;
        field: string;
        message: string;
      }>;
    }>
  > {
    const formData = new FormData();
    formData.append("file", file);
    if (options.overwrite !== undefined) formData.append("overwrite", options.overwrite.toString());
    if (options.validateOnly !== undefined)
      formData.append("validateOnly", options.validateOnly.toString());
    if (options.targetType) formData.append("targetType", options.targetType);

    return post<{
      imported: number;
      updated: number;
      skipped: number;
      errors: Array<{
        row: number;
        field: string;
        message: string;
      }>;
    }>("/api/v1/permissions/import", formData);
  },

  /**
   * 分析权限继承
   */
  async analyzePermissionInheritance(userId: string): Promise<
    ApiResponse<{
      user: {
        id: string;
        username: string;
        fullName: string;
      };
      inheritance: Array<{
        source: "direct" | "role";
        sourceId?: string;
        sourceName?: string;
        permissions: string[];
        priority: number;
      }>;
      conflicts: Array<{
        permission: string;
        sources: string[];
        resolution: "allow" | "deny";
      }>;
      effectivePermissions: string[];
    }>
  > {
    return get<{
      user: {
        id: string;
        username: string;
        fullName: string;
      };
      inheritance: Array<{
        source: "direct" | "role";
        sourceId?: string;
        sourceName?: string;
        permissions: string[];
        priority: number;
      }>;
      conflicts: Array<{
        permission: string;
        sources: string[];
        resolution: "allow" | "deny";
      }>;
      effectivePermissions: string[];
    }>(`/api/v1/permissions/inheritance/${userId}`);
  },

  /**
   * 分析权限依赖
   */
  async analyzePermissionDependencies(permission: string): Promise<
    ApiResponse<{
      permission: string;
      dependencies: string[];
      dependents: string[];
      related: string[];
      usageImpact: {
        affectedRoles: string[];
        affectedUsers: string[];
        totalUsage: number;
      };
    }>
  > {
    return post<{
      permission: string;
      dependencies: string[];
      dependents: string[];
      related: string[];
      usageImpact: {
        affectedRoles: string[];
        affectedUsers: string[];
        totalUsage: number;
      };
    }>("/api/v1/permissions/dependencies", { permission });
  },
};
