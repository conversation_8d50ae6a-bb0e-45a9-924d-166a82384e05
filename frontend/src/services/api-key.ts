/**
 * API Key管理服务
 */

import { get, post, put, del } from "@/utils/request";

// API Key接口类型
export interface ApiKey {
  id: string;
  name: string;
  systemId: string;
  description?: string;
  status: "ACTIVE" | "INACTIVE" | "REVOKED" | "EXPIRED";
  expiresAt?: string;
  lastUsedAt?: string;
  usageCount: number;
  createdAt: string;
  updatedAt: string;
  createdByUser: {
    username: string;
    fullName: string;
  };
}

// 创建API Key请求
export interface CreateApiKeyRequest {
  name: string;
  systemId: string;
  description?: string;
  expiresAt?: string;
}

// 创建API Key响应（包含密钥值）
export interface CreateApiKeyResponse extends Omit<ApiKey, "createdByUser"> {
  keyValue: string;
}

// 更新API Key请求
export interface UpdateApiKeyRequest {
  name?: string;
  description?: string;
  status?: "ACTIVE" | "INACTIVE" | "REVOKED";
  expiresAt?: string;
}

// API Key统计信息
export interface ApiKeyStats {
  totalUsage: number;
  totalServices: number;
  lastUsedAt?: string;
  recentDays: number;
  dailyUsage: Record<string, number>;
  recentUsageCount: number;
}

// 分页响应
export interface ApiKeyListResponse {
  apiKeys: ApiKey[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

/**
 * 获取API Key列表
 */
export function getApiKeys(
  params: {
    page?: number;
    limit?: number;
    status?: string;
    systemId?: string;
  } = {}
) {
  return get<ApiKeyListResponse>("/api/v1/api-keys", { params });
}

/**
 * 获取当前用户的 API Key 列表
 */
export function getMyApiKeys(
  params: {
    page?: number;
    limit?: number;
    status?: string;
    systemId?: string;
  } = {}
) {
  return get<ApiKeyListResponse>("/api/v1/me/api-keys", { params });
}

/**
 * 获取API Key详情
 */
export function getApiKey(id: string) {
  return get<ApiKey>(`/api/v1/api-keys/${id}`);
}

/**
 * 创建API Key
 */
export function createApiKey(data: CreateApiKeyRequest) {
  return post<CreateApiKeyResponse>("/api/v1/api-keys", data);
}

/**
 * 为当前用户创建 API Key
 */
export function createMyApiKey(data: CreateApiKeyRequest) {
  return post<CreateApiKeyResponse>("/api/v1/me/api-keys", data);
}

/**
 * 更新API Key
 */
export function updateApiKey(id: string, data: UpdateApiKeyRequest) {
  return put<ApiKey>(`/api/v1/api-keys/${id}`, data);
}

/**
 * 更新当前用户 API Key
 */
export function updateMyApiKey(id: string, data: UpdateApiKeyRequest) {
  return put<ApiKey>(`/api/v1/me/api-keys/${id}`, data);
}

/**
 * 删除/撤销API Key
 */
export function deleteApiKey(id: string) {
  return del(`/api/v1/api-keys/${id}`);
}

/**
 * 删除/撤销当前用户 API Key
 */
export function deleteMyApiKey(id: string) {
  return del(`/api/v1/me/api-keys/${id}`);
}

/**
 * 重新生成API Key
 */
export function regenerateApiKey(id: string) {
  return post<CreateApiKeyResponse>(`/api/v1/api-keys/${id}/regenerate`);
}

/**
 * 重新生成当前用户 API Key
 */
export function regenerateMyApiKey(id: string) {
  return post<CreateApiKeyResponse>(`/api/v1/me/api-keys/${id}/regenerate`);
}

/**
 * 获取API Key使用统计
 */
export function getApiKeyStats(id: string, days = 30) {
  return get<ApiKeyStats>(`/api/v1/api-keys/${id}/stats`, {
    params: { days },
  });
}

/**
 * 获取当前用户 API Key 使用统计
 */
export function getMyApiKeyStats(id: string, days = 30) {
  return get<ApiKeyStats>(`/api/v1/me/api-keys/${id}/stats`, {
    params: { days },
  });
}
