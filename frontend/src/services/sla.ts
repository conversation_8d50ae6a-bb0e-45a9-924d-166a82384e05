import { get, post, put, del } from "@/utils/request";
import type { ApiResponse, PaginatedResponse } from "@/types";

// SLA模板接口类型定义
export interface SlaTemplate {
  id: string;
  name: string;
  description?: string;
  responseTime: number; // 响应时间(分钟)
  resolutionTime: number; // 解决时间(小时)
  availability: number; // 可用性百分比
  createdAt: string;
  updatedAt: string;
}

export interface CreateSlaTemplateRequest {
  name: string;
  description?: string;
  responseTime: number;
  resolutionTime: number;
  availability?: number;
}

export interface UpdateSlaTemplateRequest {
  name?: string;
  description?: string;
  responseTime?: number;
  resolutionTime?: number;
  availability?: number;
}

export interface SlaSearchParams {
  search?: string;
  page?: number;
  limit?: number;
}

// SLA服务接口
export const slaService = {
  // 获取SLA模板列表
  async getSlaTemplates(
    params?: SlaSearchParams
  ): Promise<ApiResponse<PaginatedResponse<SlaTemplate>>> {
    return get<PaginatedResponse<SlaTemplate>>("/api/v1/sla/templates", params);
  },

  // 获取单个SLA模板详情
  async getSlaTemplate(id: string): Promise<ApiResponse<SlaTemplate>> {
    return get<SlaTemplate>(`/api/v1/sla/templates/${id}`);
  },

  // 创建SLA模板
  async createSlaTemplate(data: CreateSlaTemplateRequest): Promise<ApiResponse<SlaTemplate>> {
    return post<SlaTemplate>("/api/v1/sla/templates", data);
  },

  // 更新SLA模板
  async updateSlaTemplate(
    id: string,
    data: UpdateSlaTemplateRequest
  ): Promise<ApiResponse<SlaTemplate>> {
    return put<SlaTemplate>(`/api/v1/sla/templates/${id}`, data);
  },

  // 删除SLA模板
  async deleteSlaTemplate(id: string): Promise<ApiResponse<void>> {
    return del<void>(`/api/v1/sla/templates/${id}`);
  },

  // 根据条件推荐SLA模板
  async getRecommendedSlaTemplates(params: {
    customerLevel?: string;
    serviceCategory?: string;
    priority?: string;
  }): Promise<ApiResponse<SlaTemplate[]>> {
    return get<SlaTemplate[]>("/api/v1/sla/templates/recommend", params);
  },

  // 获取SLA模板使用统计
  async getSlaTemplateStats(id: string): Promise<
    ApiResponse<{
      totalServices: number;
      activeServices: number;
      averageResponseTime: number;
      averageResolutionTime: number;
      complianceRate: number;
    }>
  > {
    return get(`/api/v1/sla/templates/${id}/stats`);
  },

  // 获取单个服务的SLA状态
  async getServiceSlaStatus(serviceId: string): Promise<
    ApiResponse<{
      service: any;
      slaTemplate: SlaTemplate;
      status: string;
      responseTimeStatus: {
        elapsed: number;
        remaining: number;
        percentage: number;
        isBreached: boolean;
      };
      resolutionTimeStatus: {
        elapsed: number;
        remaining: number;
        percentage: number;
        isBreached: boolean;
      };
      riskLevel: "LOW" | "MEDIUM" | "HIGH" | "CRITICAL";
    }>
  > {
    return get(`/api/v1/sla/services/${serviceId}/status`);
  },

  // 获取活跃服务的SLA监控数据
  async getActiveSlaMonitoring(): Promise<
    ApiResponse<{
      totalServices: number;
      statusByRisk: Record<string, any[]>;
      statusByType: Record<string, any[]>;
      allStatuses: any[];
    }>
  > {
    return get("/api/v1/sla/monitoring/active");
  },

  // 获取SLA性能仪表板数据
  async getSlaPerformanceDashboard(): Promise<
    ApiResponse<{
      performance: {
        totalActiveServices: number;
        withinSla: number;
        approachingBreach: number;
        breached: number;
        averageResponseCompliance: number;
        averageResolutionCompliance: number;
        riskDistribution: Record<string, number>;
      };
      recentAlerts: Array<{
        id: string;
        subject: string;
        content: string;
        createdAt: string;
        status: string;
        recipient: string;
      }>;
      complianceTrend: Array<{
        date: string;
        compliance: number;
        totalServices: number;
        compliantServices: number;
      }>;
    }>
  > {
    return get("/api/v1/sla/dashboard");
  },

  // 手动触发SLA警报检查
  async triggerSlaAlertCheck(): Promise<ApiResponse<{ message: string }>> {
    return post("/api/v1/sla/alerts/trigger", {});
  },

  // 获取SLA违约风险预警列表
  async getSlaRiskAlerts(params?: {
    riskLevel?: "LOW" | "MEDIUM" | "HIGH" | "CRITICAL";
    limit?: number;
  }): Promise<
    ApiResponse<{
      total: number;
      items: any[];
      riskSummary: {
        critical: number;
        high: number;
        medium: number;
        low: number;
      };
    }>
  > {
    return get("/api/v1/sla/risks", params);
  },
};
