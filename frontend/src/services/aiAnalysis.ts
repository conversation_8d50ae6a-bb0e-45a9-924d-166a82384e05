/**
 * AI分析服务 - 调用后端AI服务
 */

import { post } from "@/utils/request";
import type { ApiResponse } from "@/types";
import type { AIAnalysisRequest, AIAnalysisResponse } from "@/types/ai";

class AIAnalysisService {
  /**
   * 智能分析工单内容 - 调用后端服务
   */
  async analyzeTicketContent(
    request: AIAnalysisRequest
  ): Promise<ApiResponse<AIAnalysisResponse | null>> {
    try {
      // 直接调用后端AI分析服务
      const response = await post("/api/v1/ai/analyze-content", {
        description: request.description,
        contextData: request.contextData,
      });
      console.log(response);
      // 检查响应格式并转换
      if (response.success && response.data) {
        // 后端返回的数据结构可能需要转换
        const analysisData = response.data;

        // 确保返回的数据符合前端期望的格式
        const formattedResponse: AIAnalysisResponse = {
          success: true,
          requestId: analysisData.requestId || `req_${Date.now()}`,
          timestamp: analysisData.timestamp || new Date().toISOString(),
          suggestions: analysisData.suggestions || [],
          overallConfidence: analysisData.overallConfidence || 0,
          processingTime: analysisData.processingTime || 0,
          warning: analysisData.warning,
          metadata: analysisData.metadata
        };

        return {
          success: true,
          data: formattedResponse,
          message: response.message || "AI分析完成"
        };
      }

      return response;
    } catch (error: any) {
      console.error("AI分析失败:", error);

      // 返回错误响应
      return {
        success: false,
        data: null,
        message: error.message || "AI分析服务暂时不可用",
      };
    }
  }

  /**
   * 提交AI反馈
   */
  async submitFeedback(feedbackData: {
    requestId: string;
    rating: number;
    helpful?: boolean;
    adopted?: Record<string, any>;
    comments?: string;
  }): Promise<ApiResponse<null>> {
    try {
      const response = await post("/api/v1/ai/feedback", feedbackData);
      return response;
    } catch (error: any) {
      console.error("提交AI反馈失败:", error);
      return {
        success: false,
        data: null,
        message: error.message || "提交反馈失败",
      };
    }
  }
}

// 导出单例
export const aiAnalysisService = new AIAnalysisService();
export default aiAnalysisService;
