/**
 * 系统配置服务
 *
 * 提供系统配置相关的API调用
 */

import type { ApiResponse } from "@/types";
import { get, post, put, del } from "@/utils/request";

// 系统配置项接口
export interface SystemConfig {
  key: string;
  value: any;
  type: "string" | "number" | "boolean" | "json" | "array";
  category: string;
  label: string;
  description?: string;
  defaultValue?: any;
  options?: any[];
  validation?: {
    required?: boolean;
    min?: number;
    max?: number;
    pattern?: string;
  };
  encrypted?: boolean;
  isSystem?: boolean;
  isPublic?: boolean;
  createdAt: string;
  updatedAt: string;
}

// 系统配置分组
export interface SystemConfigGroup {
  category: string;
  label: string;
  description?: string;
  configs: SystemConfig[];
}

// 系统配置更新请求
export interface SystemConfigUpdateRequest {
  key: string;
  value: any;
}

// 系统配置批量更新请求
export interface SystemConfigBatchUpdateRequest {
  configs: SystemConfigUpdateRequest[];
}

// 系统配置导出/导入
export interface SystemConfigExportData {
  configs: Pick<SystemConfig, "key" | "value" | "type" | "category">[];
  exportedAt: string;
  version: string;
}

export const systemConfigService = {
  /**
   * 获取所有系统配置
   */
  async getAll(): Promise<ApiResponse<SystemConfig[]>> {
    return get<SystemConfig[]>("/api/v1/system-config");
  },

  /**
   * 按分组获取系统配置
   */
  async getByGroups(): Promise<ApiResponse<SystemConfigGroup[]>> {
    return get<SystemConfigGroup[]>("/api/v1/system-config/groups");
  },

  /**
   * 获取单个配置项
   */
  async getByKey(key: string): Promise<ApiResponse<SystemConfig>> {
    return get<SystemConfig>(`/api/v1/system-config/${key}`);
  },

  /**
   * 获取特定分类的配置
   */
  async getByCategory(category: string): Promise<ApiResponse<SystemConfig[]>> {
    return get<SystemConfig[]>(`/api/v1/system-config/category/${category}`);
  },

  /**
   * 获取公开配置（不需要权限验证）
   */
  async getPublicConfigs(): Promise<ApiResponse<Record<string, any>>> {
    return get<Record<string, any>>("/api/v1/system-config/public");
  },

  /**
   * 更新配置项
   */
  async updateConfig(key: string, value: any): Promise<ApiResponse<SystemConfig>> {
    return put<SystemConfig>(`/api/v1/system-config/${key}`, { value });
  },

  /**
   * 批量更新配置
   */
  async batchUpdate(configs: SystemConfigUpdateRequest[]): Promise<ApiResponse<SystemConfig[]>> {
    return put<SystemConfig[]>("/api/v1/system-config/batch", { configs });
  },

  /**
   * 重置配置为默认值
   */
  async resetToDefault(key: string): Promise<ApiResponse<SystemConfig>> {
    return post<SystemConfig>(`/api/v1/system-config/${key}/reset`);
  },

  /**
   * 批量重置配置为默认值
   */
  async batchResetToDefault(keys: string[]): Promise<ApiResponse<SystemConfig[]>> {
    return post<SystemConfig[]>("/api/v1/system-config/batch-reset", { keys });
  },

  /**
   * 验证配置值
   */
  async validateConfig(
    key: string,
    value: any
  ): Promise<ApiResponse<{ valid: boolean; errors?: string[] }>> {
    return post<{ valid: boolean; errors?: string[] }>(`/api/v1/system-config/${key}/validate`, {
      value,
    });
  },

  /**
   * 导出配置
   */
  async exportConfigs(categories?: string[]): Promise<ApiResponse<SystemConfigExportData>> {
    const params = categories ? { categories } : undefined;
    return get<SystemConfigExportData>("/api/v1/system-config/export", params);
  },

  /**
   * 导入配置
   */
  async importConfigs(
    data: SystemConfigExportData,
    options: {
      overwrite?: boolean;
      validateOnly?: boolean;
    } = {}
  ): Promise<ApiResponse<{ imported: number; skipped: number; errors: string[] }>> {
    return post<{ imported: number; skipped: number; errors: string[] }>(
      "/api/v1/system-config/import",
      { data, options }
    );
  },

  /**
   * 获取配置历史
   */
  async getConfigHistory(
    key: string,
    page: number = 1,
    limit: number = 20
  ): Promise<
    ApiResponse<{
      history: Array<{
        id: string;
        key: string;
        oldValue: any;
        newValue: any;
        changedAt: string;
        changedBy:
          | string
          | {
              id?: string;
              username?: string;
              fullName?: string;
            };
        userInfo?: {
          id: string;
          username: string;
          fullName: string;
        };
      }>;
      pagination: {
        page: number;
        limit: number;
        total: number;
        totalPages: number;
      };
    }>
  > {
    return get<{
      history: Array<{
        id: string;
        key: string;
        oldValue: any;
        newValue: any;
        changedAt: string;
        changedBy:
          | string
          | {
              id?: string;
              username?: string;
              fullName?: string;
            };
        userInfo?: {
          id: string;
          username: string;
          fullName: string;
        };
      }>;
      pagination: {
        page: number;
        limit: number;
        total: number;
        totalPages: number;
      };
    }>(`/api/v1/system-config/${key}/history`, { page, limit });
  },

  /**
   * 获取系统信息
   */
  async getSystemInfo(): Promise<
    ApiResponse<{
      version: string;
      environment: string;
      uptime: number;
      buildTime: string;
      nodeVersion: string;
      platform: string;
      memory: {
        used: number;
        total: number;
        usage: number;
      };
    }>
  > {
    return get<{
      version: string;
      environment: string;
      uptime: number;
      buildTime: string;
      nodeVersion: string;
      platform: string;
      memory: {
        used: number;
        total: number;
        usage: number;
      };
    }>("/api/v1/system-config/system-info");
  },

  /**
   * 测试连接
   */
  async testConnection(
    type: "database" | "redis" | "smtp" | "sms",
    testData?: any
  ): Promise<
    ApiResponse<{
      success: boolean;
      message: string;
      details?: any;
    }>
  > {
    if (testData) {
      return post<{
        success: boolean;
        message: string;
        details?: any;
      }>("/api/v1/system-config/test-service", {
        serviceType: type,
        testData,
      });
    }
    return post<{
      success: boolean;
      message: string;
      details?: any;
    }>(`/api/v1/system-config/test/${type}`);
  },

  /**
   * 测试邮件配置
   */
  async testEmailConfig(testEmail: string): Promise<
    ApiResponse<{
      success: boolean;
      message: string;
      details?: any;
    }>
  > {
    return post<{
      success: boolean;
      message: string;
      details?: any;
    }>("/api/v1/system-config/test-email", {
      testEmail,
    });
  },

  /**
   * 获取邮件服务状态
   */
  async getEmailServiceStatus(): Promise<
    ApiResponse<{
      configured: boolean;
      connectionStatus: string;
      config?: any;
      stats?: any;
      statusText: string;
    }>
  > {
    return get<{
      configured: boolean;
      connectionStatus: string;
      config?: any;
      stats?: any;
      statusText: string;
    }>("/api/v1/system-config/email-status");
  },

  /**
   * 清除缓存
   */
  async clearCache(type?: "all" | "config" | "user" | "permission"): Promise<
    ApiResponse<{
      cleared: number;
      message: string;
    }>
  > {
    return post<{
      cleared: number;
      message: string;
    }>("/api/v1/system-config/clear-cache", { type });
  },

  /**
   * 获取健康状态
   */
  async getHealthStatus(): Promise<
    ApiResponse<{
      status: "healthy" | "warning" | "critical";
      services: Array<{
        name: string;
        status: "up" | "down" | "warning";
        responseTime?: number;
        message?: string;
      }>;
      checks: Array<{
        name: string;
        status: "pass" | "fail" | "warn";
        message?: string;
        details?: any;
      }>;
    }>
  > {
    return get<{
      status: "healthy" | "warning" | "critical";
      services: Array<{
        name: string;
        status: "up" | "down" | "warning";
        responseTime?: number;
        message?: string;
      }>;
      checks: Array<{
        name: string;
        status: "pass" | "fail" | "warn";
        message?: string;
        details?: any;
      }>;
    }>("/api/v1/system-config/health");
  },

  /**
   * 获取配置分类
   */
  async getCategories(): Promise<
    ApiResponse<
      Array<{
        category: string;
        label: string;
        description?: string;
        configCount: number;
      }>
    >
  > {
    return get<
      Array<{
        category: string;
        label: string;
        description?: string;
        configCount: number;
      }>
    >("/api/v1/system-config/categories");
  },
};
