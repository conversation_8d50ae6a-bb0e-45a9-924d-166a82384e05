import { get, post } from '@/utils/request'

// ========== 接口类型定义 ==========

export interface AnalyticsTimeRange {
  start: Date
  end: Date
}

export interface OperationEfficiencyMetrics {
  totalServices: number
  completedServices: number
  avgResponseTimeMinutes: number
  avgResolutionTimeHours: number
  slaComplianceRate: number
  engineerWorkload: Array<{
    userId: string
    userName: string
    assignedCount: number
    completedCount: number
    avgResolutionTime: number
    totalWorkHours: number
    efficiency: number
  }>
  categoryDistribution: Record<string, number>
  priorityDistribution: Record<string, number>
  statusDistribution: Record<string, number>
}

export interface CustomerSatisfactionMetrics {
  totalFeedbacks: number
  averageRating: number
  ratingDistribution: Record<number, number>
  customerSatisfactionTrend: Array<{
    date: string
    rating: number
    count: number
  }>
  topCustomers: Array<{
    customerId: string
    customerName: string
    totalServices: number
    avgRating: number
    lastServiceDate: Date
  }>
  satisfactionByCategory: Record<string, {
    avgRating: number
    count: number
  }>
  complaintsAnalysis: {
    totalComplaints: number
    resolvedComplaints: number
    avgResolutionTime: number
    commonIssues: Array<{
      issue: string
      count: number
      percentage: number
    }>
  }
}

export interface SLAComplianceReport {
  totalServices: number
  slaViolations: number
  complianceRate: number
  responseTimeCompliance: {
    total: number
    compliant: number
    rate: number
    avgResponseTime: number
  }
  resolutionTimeCompliance: {
    total: number
    compliant: number
    rate: number
    avgResolutionTime: number
  }
  violationsByPriority: Record<string, {
    total: number
    violations: number
    rate: number
  }>
  violationsByCategory: Record<string, {
    total: number
    violations: number
    rate: number
  }>
  monthlyTrend: Array<{
    month: string
    total: number
    violations: number
    rate: number
  }>
  costImpact: {
    totalViolationCost: number
    avgCostPerViolation: number
    potentialSavings: number
  }
}

export interface ServiceTrendAnalysis {
  timeRange: AnalyticsTimeRange
  totalServices: number
  serviceVolumeTrend: Array<{
    date: string
    count: number
    cumulative: number
  }>
  categoryTrends: Record<string, Array<{
    date: string
    count: number
  }>>
  resolutionTimeTrend: Array<{
    date: string
    avgHours: number
    minHours: number
    maxHours: number
  }>
  backlogAnalysis: {
    currentBacklog: number
    avgBacklogSize: number
    backlogTrend: Array<{
      date: string
      size: number
    }>
    oldestTicket: {
      id: string
      ticketNumber: string
      daysOld: number
    } | null
  }
  seasonalPatterns: {
    hourlyDistribution: Record<number, number>
    weeklyDistribution: Record<number, number>
    monthlyDistribution: Record<number, number>
  }
}

export interface DashboardData {
  timeRange: AnalyticsTimeRange
  operationEfficiency: OperationEfficiencyMetrics
  customerSatisfaction: CustomerSatisfactionMetrics
  slaCompliance: SLAComplianceReport
  serviceTrends: ServiceTrendAnalysis
  summary: {
    totalServices: number
    completionRate: string
    avgSatisfaction: string
    slaComplianceRate: string
    totalEngineers: number
  }
}

export interface QuickStats {
  totalServices: number
  completionRate: number
  avgResponseTime: number
  avgResolutionTime: number
  slaComplianceRate: number
  customerSatisfaction: number
  totalFeedbacks: number
  activeEngineers: number
  slaViolations: number
  timeRange: {
    start: string
    end: string
    days: number
  }
}

export interface CustomReportConfig {
  id?: string
  name: string
  description?: string
  timeRange: {
    type: 'custom' | 'last7days' | 'last30days' | 'lastQuarter' | 'lastYear'
    start?: Date
    end?: Date
  }
  metrics: Array<{
    type: 'service_count' | 'avg_response_time' | 'sla_compliance' | 'satisfaction' | 'workload'
    groupBy?: 'day' | 'week' | 'month' | 'quarter' | 'year' | 'category' | 'priority' | 'engineer'
    filters?: {
      categories?: string[]
      priorities?: string[]
      engineers?: string[]
      customers?: string[]
    }
  }>
  visualizations: Array<{
    type: 'line' | 'bar' | 'pie' | 'table' | 'number'
    title: string
    metricIndex: number
    config?: Record<string, any>
  }>
  exportFormats: ('pdf' | 'excel' | 'csv')[]
  schedule?: {
    enabled: boolean
    frequency: 'daily' | 'weekly' | 'monthly'
    recipients: string[]
  }
  createdBy: string
  createdAt?: Date
  lastGenerated?: Date
}

export interface ReportTemplate {
  id: string
  name: string
  description: string
  timeRange: CustomReportConfig['timeRange']
  metrics: CustomReportConfig['metrics']
  visualizations: CustomReportConfig['visualizations']
  exportFormats: CustomReportConfig['exportFormats']
}

// ========== 报表服务类 ==========

class ReportService {
  private readonly baseUrl = '/api/v1/reports'

  // ========== 数据分析接口 ==========

  /**
   * 获取运维效率分析数据
   */
  async getOperationEfficiency(params?: {
    start?: string
    end?: string
    days?: number
  }): Promise<OperationEfficiencyMetrics> {
    const response = await get(`${this.baseUrl}/operation-efficiency`, { params })
    return response.data
  }

  /**
   * 获取客户满意度分析数据
   */
  async getCustomerSatisfaction(params?: {
    start?: string
    end?: string
    days?: number
  }): Promise<CustomerSatisfactionMetrics> {
    const response = await get(`${this.baseUrl}/customer-satisfaction`, { params })
    return response.data
  }

  /**
   * 获取SLA合规性报告
   */
  async getSLACompliance(params?: {
    start?: string
    end?: string
    days?: number
  }): Promise<SLAComplianceReport> {
    const response = await get(`${this.baseUrl}/sla-compliance`, { params })
    return response.data
  }

  /**
   * 获取服务趋势分析数据
   */
  async getServiceTrends(params?: {
    start?: string
    end?: string
    days?: number
  }): Promise<ServiceTrendAnalysis> {
    const response = await get(`${this.baseUrl}/service-trends`, { params })
    return response.data
  }

  /**
   * 获取综合仪表板数据
   */
  async getDashboardData(params?: {
    start?: string
    end?: string
    days?: number
  }): Promise<DashboardData> {
    const response = await get(`${this.baseUrl}/dashboard`, { params })
    return response.data
  }

  /**
   * 获取快速统计数据
   */
  async getQuickStats(): Promise<QuickStats> {
    const response = await get(`${this.baseUrl}/quick-stats`)
    return response.data
  }

  // ========== 自定义报表接口 ==========

  /**
   * 生成自定义报表
   */
  async generateCustomReport(config: CustomReportConfig): Promise<{
    data: any
    metadata: {
      generatedAt: Date
      recordCount: number
      executionTime: number
    }
  }> {
    const response = await post(`${this.baseUrl}/custom`, config)
    return response.data
  }

  /**
   * 获取报表模板
   */
  async getReportTemplates(): Promise<{ templates: ReportTemplate[] }> {
    const response = await get(`${this.baseUrl}/templates`)
    return response.data
  }

  // ========== 导出功能接口 ==========

  /**
   * 导出Excel报表
   */
  async exportExcel(data: any, fileName: string): Promise<Blob> {
    const response = await fetch(`${this.baseUrl}/export/excel`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ data, fileName }),
      credentials: 'include'
    })

    if (!response.ok) {
      throw new Error('导出Excel失败')
    }

    return response.blob()
  }

  /**
   * 导出CSV数据
   */
  async exportCSV(data: any[], fileName: string): Promise<Blob> {
    const response = await fetch(`${this.baseUrl}/export/csv`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ data, fileName }),
      credentials: 'include'
    })

    if (!response.ok) {
      throw new Error('导出CSV失败')
    }

    return response.blob()
  }

  /**
   * 导出PDF报表
   */
  async exportPDF(serviceId: string, type: 'service' = 'service'): Promise<Blob> {
    const response = await fetch(`${this.baseUrl}/export/pdf`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ serviceId, type }),
      credentials: 'include'
    })

    if (!response.ok) {
      throw new Error('导出PDF失败')
    }

    return response.blob()
  }

  // ========== 工具方法 ==========

  /**
   * 触发文件下载
   */
  downloadFile(blob: Blob, fileName: string, type: 'excel' | 'csv' | 'pdf') {
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `${fileName}.${type === 'excel' ? 'xlsx' : type}`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)
  }

  /**
   * 格式化时间范围参数
   */
  formatTimeRangeParams(timeRange: {
    type: 'custom' | 'last7days' | 'last30days' | 'lastQuarter' | 'lastYear'
    start?: Date
    end?: Date
  }): { start?: string; end?: string; days?: number } {
    if (timeRange.type === 'custom' && timeRange.start && timeRange.end) {
      return {
        start: timeRange.start.toISOString(),
        end: timeRange.end.toISOString()
      }
    }

    const daysMap: Record<string, number> = {
      'last7days': 7,
      'last30days': 30,
      'lastQuarter': 90,
      'lastYear': 365
    }

    return {
      days: daysMap[timeRange.type] || 7
    }
  }
}

export const reportService = new ReportService()