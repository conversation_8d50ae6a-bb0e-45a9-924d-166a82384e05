/**
 * 系统监控服务
 *
 * 提供系统监控相关的API调用
 */

import type { ApiResponse } from "@/types";
import { get, post, put, del } from "@/utils/request";

// 系统监控数据接口
export interface SystemMetrics {
  timestamp: string;
  cpu: number;
  memory: number;
  disk: number;
  network: number;
}

export interface ServiceStatus {
  name: string;
  status: "healthy" | "warning" | "critical" | "down";
  uptime: string;
  responseTime: number;
  lastCheck: string;
  details?: string;
  version?: string;
  url?: string;
  connections?: {
    active: number;
    idle: number;
    total: number;
  };
  performance?: {
    queryCount?: number;
    slowQueries?: number;
    errorRate?: number;
  };
  stats?: {
    hitRate?: number;
    misses?: number;
    evictions?: number;
  };
}

export interface SystemAlert {
  id: string;
  title: string;
  message: string;
  severity: "LOW" | "MEDIUM" | "HIGH" | "CRITICAL";
  type: "SYSTEM" | "SERVICE" | "SECURITY" | "PERFORMANCE";
  source: string;
  status: "ACTIVE" | "RESOLVED" | "ACKNOWLEDGED";
  timestamp: string;
  resolvedAt?: string;
  resolvedBy?: string;
  metadata?: any;
}

export interface MonitorDashboardData {
  summary: {
    overall: "healthy" | "warning" | "critical";
    uptime: string;
    totalServices: number;
    healthyServices: number;
    criticalAlerts: number;
  };
  metrics: {
    current: SystemMetrics;
    history: SystemMetrics[];
  };
  services: ServiceStatus[];
  alerts: SystemAlert[];
  performance: {
    responseTime: {
      avg: number;
      p95: number;
      p99: number;
    };
    throughput: {
      current: number;
      peak: number;
    };
    errorRate: number;
  };
}

export interface SystemEvent {
  id: string;
  timestamp: string;
  level: "DEBUG" | "INFO" | "WARN" | "ERROR";
  service: string;
  message: string;
  metadata?: any;
  userId?: string;
  userInfo?: {
    id: string;
    username: string;
    fullName: string;
  };
}

export interface MonitorConfig {
  id: string;
  type: "THRESHOLD" | "HEARTBEAT" | "LOG_PATTERN";
  name: string;
  description?: string;
  enabled: boolean;
  config: {
    // 阈值监控
    metric?: string;
    threshold?: number;
    operator?: ">" | "<" | "==" | "!=";
    duration?: number;

    // 心跳监控
    interval?: number;
    timeout?: number;

    // 日志模式监控
    pattern?: string;
    logLevel?: string;

    // 通用配置
    alertSeverity?: "LOW" | "MEDIUM" | "HIGH" | "CRITICAL";
    notificationChannels?: string[];
  };
  createdAt: string;
  updatedAt: string;
}

export const monitorService = {
  /**
   * 获取监控仪表板数据
   */
  async getDashboard(detail?: 'lite' | 'full', sections?: string[]): Promise<ApiResponse<MonitorDashboardData>> {
    const params: any = {};
    if (detail) params.detail = detail;
    if (sections && sections.length > 0) params.sections = sections.join(',');
    return get<MonitorDashboardData>("/api/v1/monitor/dashboard", params);
  },

  /**
   * 获取实时系统指标
   */
  async getSystemMetrics(): Promise<ApiResponse<SystemMetrics>> {
    return get<SystemMetrics>("/api/v1/monitor/metrics/system");
  },

  /**
   * 获取数据库健康状态
   */
  async getDatabaseHealth(): Promise<ApiResponse<any>> {
    return get<any>("/api/v1/monitor/health/database");
  },

  /**
   * 获取Redis健康状态
   */
  async getRedisHealth(): Promise<ApiResponse<any>> {
    return get<any>("/api/v1/monitor/health/redis");
  },

  /**
   * 获取应用健康状态
   */
  async getApplicationHealth(): Promise<ApiResponse<any>> {
    return get<any>("/api/v1/monitor/health/application");
  },

  /**
   * 获取智能分析结果
   */
  async getIntelligentAnalysis(): Promise<ApiResponse<any>> {
    return get<any>("/api/v1/monitor/analysis/intelligent");
  },

  /**
   * 获取性能基准对比
   */
  async getPerformanceBenchmarks(): Promise<ApiResponse<any>> {
    return get<any>("/api/v1/monitor/analysis/benchmarks");
  },

  /**
   * 获取资源预警
   */
  async getResourceAlerts(): Promise<ApiResponse<any>> {
    return get<any>("/api/v1/monitor/analysis/alerts");
  },

  /**
   * 获取历史监控数据
   */
  async getHistoricalMetrics(metric: string, hours?: number, aggregation?: 'avg' | 'max' | 'min' | 'sum'): Promise<ApiResponse<any>> {
    const params: any = { metric };
    if (hours) params.hours = hours.toString();
    if (aggregation) params.aggregation = aggregation;
    return get<any>("/api/v1/monitor/metrics/historical", params);
  },

  /**
   * 获取服务可用性统计
   */
  async getAvailabilityStats(days?: number): Promise<ApiResponse<any>> {
    const params: any = {};
    if (days) params.days = days.toString();
    return get<any>("/api/v1/monitor/stats/availability", params);
  },

  /**
   * 获取系统事件日志
   */
  async getSystemEvents(page?: number, limit?: number, level?: string, type?: string): Promise<ApiResponse<any>> {
    const params: any = {};
    if (page) params.page = page.toString();
    if (limit) params.limit = limit.toString();
    if (level) params.level = level;
    if (type) params.type = type;
    return get<any>("/api/v1/monitor/events", params);
  },

  /**
   * 运行系统诊断
   */
  async runSystemDiagnostics(): Promise<ApiResponse<any>> {
    return post<any>("/api/v1/monitor/diagnostics");
  },

  /**
   * 生成监控报告
   */
  async generateReport(type?: string, format?: string): Promise<ApiResponse<any>> {
    const params: any = {};
    if (type) params.type = type;
    if (format) params.format = format;
    return get<any>("/api/v1/monitor/report", params);
  },

  /**
   * 更新监控阈值
   */
  async updateThreshold(data: { metric: string; warning: number; critical: number; duration: number }): Promise<ApiResponse<any>> {
    return put<any>("/api/v1/monitor/thresholds", data);
  },

  /**
   * 获取监控配置
   */
  async getMonitoringConfig(): Promise<ApiResponse<any>> {
    return get<any>("/api/v1/monitor/config");
  },

  /**
   * 更新监控配置
   */
  async updateMonitoringConfig(config: any): Promise<ApiResponse<any>> {
    return put<any>("/api/v1/monitor/config", config);
  },

  /**
   * 获取系统指标历史
   */
  async getMetricsHistory(
    startTime?: string,
    endTime?: string,
    interval?: "1m" | "5m" | "15m" | "1h" | "1d"
  ): Promise<
    ApiResponse<{
      metrics: SystemMetrics[];
      interval: string;
      startTime: string;
      endTime: string;
    }>
  > {
    const params: any = {};
    if (startTime) params.startTime = startTime;
    if (endTime) params.endTime = endTime;
    if (interval) params.interval = interval;

    return get<{
      metrics: SystemMetrics[];
      interval: string;
      startTime: string;
      endTime: string;
    }>("/api/v1/monitor/metrics/history", params);
  },

  /**
   * 获取当前系统指标
   */
  async getCurrentMetrics(): Promise<ApiResponse<SystemMetrics>> {
    return get<SystemMetrics>("/api/v1/monitor/metrics/current");
  },

  /**
   * 获取服务状态列表
   */
  async getServicesStatus(): Promise<ApiResponse<ServiceStatus[]>> {
    return get<ServiceStatus[]>("/api/v1/monitor/services");
  },

  /**
   * 获取特定服务状态
   */
  async getServiceStatus(serviceName: string): Promise<ApiResponse<ServiceStatus>> {
    return get<ServiceStatus>(`/api/v1/monitor/services/${serviceName}`);
  },

  /**
   * 健康检查特定服务
   */
  async checkServiceHealth(serviceName: string): Promise<ApiResponse<ServiceStatus>> {
    return post<ServiceStatus>(`/api/v1/monitor/services/${serviceName}/check`);
  },

  /**
   * 获取系统告警列表
   */
  async getAlerts(
    page: number = 1,
    limit: number = 20,
    filters?: {
      severity?: string;
      type?: string;
      status?: string;
      source?: string;
      startTime?: string;
      endTime?: string;
    }
  ): Promise<
    ApiResponse<{
      alerts: SystemAlert[];
      pagination: {
        page: number;
        limit: number;
        total: number;
        totalPages: number;
      };
    }>
  > {
    const params: any = { page, limit };
    if (filters) Object.assign(params, filters);

    return get<{
      alerts: SystemAlert[];
      pagination: {
        page: number;
        limit: number;
        total: number;
        totalPages: number;
      };
    }>("/api/v1/monitor/alerts", params);
  },

  /**
   * 确认告警
   */
  async acknowledgeAlert(alertId: string): Promise<ApiResponse<SystemAlert>> {
    return post<SystemAlert>(`/api/v1/monitor/alerts/${alertId}/acknowledge`);
  },

  /**
   * 解决告警
   */
  async resolveAlert(alertId: string, notes?: string): Promise<ApiResponse<SystemAlert>> {
    return post<SystemAlert>(`/api/v1/monitor/alerts/${alertId}/resolve`, { notes });
  },

  /**
   * 批量处理告警
   */
  async batchProcessAlerts(
    alertIds: string[],
    action: "acknowledge" | "resolve",
    notes?: string
  ): Promise<ApiResponse<{ processed: number; failed: string[] }>> {
    return post<{ processed: number; failed: string[] }>("/api/v1/monitor/alerts/batch", {
      alertIds,
      action,
      notes,
    });
  },

  /**
   * 获取系统事件日志
   */
  async getEvents(
    page: number = 1,
    limit: number = 20,
    filters?: {
      level?: string;
      service?: string;
      startTime?: string;
      endTime?: string;
      search?: string;
    }
  ): Promise<
    ApiResponse<{
      events: SystemEvent[];
      pagination: {
        page: number;
        limit: number;
        total: number;
        totalPages: number;
      };
    }>
  > {
    const params: any = { page, limit };
    if (filters) Object.assign(params, filters);

    return get<{
      events: SystemEvent[];
      pagination: {
        page: number;
        limit: number;
        total: number;
        totalPages: number;
      };
    }>("/api/v1/monitor/events", params);
  },

  /**
   * 获取性能统计
   */
  async getPerformanceStats(timeRange: "1h" | "6h" | "24h" | "7d" | "30d" = "24h"): Promise<
    ApiResponse<{
      responseTime: {
        avg: number;
        p50: number;
        p95: number;
        p99: number;
        history: Array<{ timestamp: string; value: number }>;
      };
      throughput: {
        current: number;
        peak: number;
        history: Array<{ timestamp: string; value: number }>;
      };
      errorRate: {
        current: number;
        history: Array<{ timestamp: string; value: number }>;
      };
      resources: {
        cpu: Array<{ timestamp: string; value: number }>;
        memory: Array<{ timestamp: string; value: number }>;
        disk: Array<{ timestamp: string; value: number }>;
      };
    }>
  > {
    return get<{
      responseTime: {
        avg: number;
        p50: number;
        p95: number;
        p99: number;
        history: Array<{ timestamp: string; value: number }>;
      };
      throughput: {
        current: number;
        peak: number;
        history: Array<{ timestamp: string; value: number }>;
      };
      errorRate: {
        current: number;
        history: Array<{ timestamp: string; value: number }>;
      };
      resources: {
        cpu: Array<{ timestamp: string; value: number }>;
        memory: Array<{ timestamp: string; value: number }>;
        disk: Array<{ timestamp: string; value: number }>;
      };
    }>("/api/v1/monitor/performance", { timeRange });
  },

  /**
   * 获取监控配置列表
   */
  async getConfigs(): Promise<ApiResponse<MonitorConfig[]>> {
    return get<MonitorConfig[]>("/api/v1/monitor/configs");
  },

  /**
   * 创建监控配置
   */
  async createConfig(
    config: Omit<MonitorConfig, "id" | "createdAt" | "updatedAt">
  ): Promise<ApiResponse<MonitorConfig>> {
    return post<MonitorConfig>("/api/v1/monitor/configs", config);
  },

  /**
   * 更新监控配置
   */
  async updateConfig(
    id: string,
    config: Partial<Omit<MonitorConfig, "id" | "createdAt" | "updatedAt">>
  ): Promise<ApiResponse<MonitorConfig>> {
    return put<MonitorConfig>(`/api/v1/monitor/configs/${id}`, config);
  },

  /**
   * 删除监控配置
   */
  async deleteConfig(id: string): Promise<ApiResponse<void>> {
    return del<void>(`/api/v1/monitor/configs/${id}`);
  },

  /**
   * 切换监控配置状态
   */
  async toggleConfig(id: string, enabled: boolean): Promise<ApiResponse<MonitorConfig>> {
    return post<MonitorConfig>(`/api/v1/monitor/configs/${id}/toggle`, { enabled });
  },

  /**
   * 测试监控配置
   */
  async testConfig(id: string): Promise<
    ApiResponse<{
      success: boolean;
      message: string;
      details?: any;
    }>
  > {
    return post<{
      success: boolean;
      message: string;
      details?: any;
    }>(`/api/v1/monitor/configs/${id}/test`);
  },

  /**
   * 导出监控报告
   */
  async exportReport(
    format: "pdf" | "excel" | "json",
    timeRange: "1h" | "6h" | "24h" | "7d" | "30d",
    sections?: Array<"summary" | "metrics" | "alerts" | "events" | "performance">
  ): Promise<
    ApiResponse<{
      downloadUrl: string;
      filename: string;
      size: number;
    }>
  > {
    return post<{
      downloadUrl: string;
      filename: string;
      size: number;
    }>("/api/v1/monitor/export/report", { format, timeRange, sections });
  },

  /**
   * 获取系统资源使用趋势
   */
  async getResourceTrends(timeRange: "1h" | "6h" | "24h" | "7d" | "30d" = "24h"): Promise<
    ApiResponse<{
      cpu: Array<{ timestamp: string; value: number }>;
      memory: Array<{ timestamp: string; value: number }>;
      disk: Array<{ timestamp: string; value: number }>;
      network: Array<{ timestamp: string; value: number }>;
      summary: {
        avgCpu: number;
        peakCpu: number;
        avgMemory: number;
        peakMemory: number;
        diskGrowth: number;
        networkTraffic: number;
      };
    }>
  > {
    return get<{
      cpu: Array<{ timestamp: string; value: number }>;
      memory: Array<{ timestamp: string; value: number }>;
      disk: Array<{ timestamp: string; value: number }>;
      network: Array<{ timestamp: string; value: number }>;
      summary: {
        avgCpu: number;
        peakCpu: number;
        avgMemory: number;
        peakMemory: number;
        diskGrowth: number;
        networkTraffic: number;
      };
    }>("/api/v1/monitor/resources/trends", { timeRange });
  },

  /**
   * 获取告警统计
   */
  async getAlertStats(timeRange: "1h" | "6h" | "24h" | "7d" | "30d" = "24h"): Promise<
    ApiResponse<{
      total: number;
      bySeverity: Record<string, number>;
      byType: Record<string, number>;
      byStatus: Record<string, number>;
      trends: Array<{ timestamp: string; count: number }>;
      topSources: Array<{ source: string; count: number }>;
    }>
  > {
    return get<{
      total: number;
      bySeverity: Record<string, number>;
      byType: Record<string, number>;
      byStatus: Record<string, number>;
      trends: Array<{ timestamp: string; count: number }>;
      topSources: Array<{ source: string; count: number }>;
    }>("/api/v1/monitor/alerts/stats", { timeRange });
  },
};
