import { del, get, post, put } from "@/utils/request";
import type {
  Service,
  CreateServiceRequest,
  UpdateServiceRequest,
  ServiceSearchParams,
} from "shared/types";
import type { ApiResponse, PaginatedResponse } from "@/types";

export const serviceService = {
  // 获取服务工单列表
  async getServices(
    params: ServiceSearchParams & { page?: number; limit?: number }
  ): Promise<ApiResponse<PaginatedResponse<Service>>> {
    return get<PaginatedResponse<Service>>("/api/v1/services", params);
  },

  // 获取单个服务工单详情
  async getService(id: string): Promise<ApiResponse<Service>> {
    return get<Service>(`/api/v1/services/${id}`);
  },

  // 创建服务工单
  async createService(data: CreateServiceRequest): Promise<ApiResponse<Service>> {
    return post<Service>("/api/v1/services", data);
  },

  // 更新服务工单
  async updateService(id: string, data: UpdateServiceRequest): Promise<ApiResponse<Service>> {
    return put<Service>(`/api/v1/services/${id}`, data);
  },

  // 删除服务工单
  async deleteService(id: string): Promise<ApiResponse<void>> {
    return del<void>(`/api/v1/services/${id}`);
  },

  // 获取服务工单统计信息
  async getServiceStats(): Promise<
    ApiResponse<{
      totalServices: number;
      recentServices: number;
      avgResolutionTime: number;
      statusDistribution: { status: string; count: number }[];
      categoryDistribution: { category: string; count: number }[];
      priorityDistribution: { priority: string; count: number }[];
    }>
  > {
    return get("/api/v1/services/stats");
  },

  // 导出工单PDF报告
  async exportServiceReportPDF(id: string): Promise<Blob> {
    // 获取token - 复用request工具的逻辑
    const getAuthToken = (): string | null => {
      try {
        // 优先从内存中的AuthStore获取最新token
        if (typeof window !== "undefined" && (window as any).__AUTH_STORE__) {
          const token = (window as any).__AUTH_STORE__.getState?.()?.token;
          if (token) return token;
        }

        // 从localStorage获取
        const authStorage = localStorage.getItem("auth-storage");
        if (authStorage) {
          const parsed = JSON.parse(authStorage);
          return parsed.state?.token || null;
        }
      } catch (error) {
        console.warn("Failed to get auth token:", error);
      }

      return null;
    };

    const token = getAuthToken();
    if (!token) {
      throw new Error("用户未登录");
    }

    // 构建请求头
    const headers: Record<string, string> = {
      Authorization: `Bearer ${token}`,
      Accept: "application/pdf",
    };

    const response = await fetch(`/api/v1/services/${id}/export/pdf`, {
      method: "GET",
      headers,
    });

    if (!response.ok) {
      let errorMessage = "导出PDF失败";
      try {
        const errorData = await response.json();
        errorMessage = errorData.message || errorMessage;
      } catch {
        // 如果无法解析JSON，使用状态码相关的错误信息
        if (response.status === 401) {
          errorMessage = "访问令牌无效，请重新登录";
        } else if (response.status === 403) {
          errorMessage = "无权限导出此工单报告";
        } else if (response.status === 404) {
          errorMessage = "工单不存在";
        } else if (response.status >= 500) {
          errorMessage = "服务器内部错误";
        }
      }
      throw new Error(errorMessage);
    }

    return response.blob();
  },

  // 异步导出工单PDF报告（后台生成，返回jobId）
  async exportServiceReportPDFAsync(id: string): Promise<{ jobId: string }> {
    const token = (() => {
      try {
        if (typeof window !== "undefined" && (window as any).__AUTH_STORE__) {
          const t = (window as any).__AUTH_STORE__.getState?.()?.token;
          if (t) return t;
        }
        const authStorage = localStorage.getItem("auth-storage");
        if (authStorage) {
          const parsed = JSON.parse(authStorage);
          return parsed.state?.token || null;
        }
      } catch {}
      return null;
    })();

    if (!token) throw new Error("用户未登录");

    const response = await fetch(`/api/v1/services/${id}/export/pdf/async`, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
    });

    const data = await response.json();
    if (!response.ok || !data?.success) {
      throw new Error(data?.message || "启动异步导出失败");
    }
    return data.data;
  },

  // 可选：查询任务状态（用于无WebSocket时轮询）
  async getExportJobStatus(jobId: string): Promise<any> {
    return get(`/api/v1/services/export/pdf/jobs/${jobId}`);
  },
};
