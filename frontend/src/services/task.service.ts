import { get, post } from "@/utils/request";

export interface TaskExecution {
  id: string;
  taskName: string;
  status: "RUNNING" | "SUCCESS" | "FAILED";
  startedAt: string;
  endedAt?: string;
  duration?: number;
  output?: string;
  error?: string;
  createdAt: string;
  updatedAt: string;
}

export interface Pagination {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
}

export interface PaginatedTasks {
  tasks: TaskExecution[];
  pagination: Pagination;
}

const BASE_URL = "/api/v1/tasks";

export const taskService = {
  /**
   * Get a paginated list of task executions.
   */
  getExecutions(page = 1, limit = 10, status?: string): Promise<{ data: PaginatedTasks }> {
    const params = new URLSearchParams({
      page: String(page),
      limit: String(limit),
    });
    if (status) {
      params.append("status", status);
    }
    return get(`${BASE_URL}?${params.toString()}`);
  },

  /**
   * Get details for a single task execution.
   */
  getExecutionDetails(id: string): Promise<{ data: TaskExecution }> {
    return get(`${BASE_URL}/${id}`);
  },

  /**
   * Retry a failed task execution.
   */
  retryExecution(id: string): Promise<{ message: string }> {
    return post(`${BASE_URL}/${id}/retry`);
  },
};
