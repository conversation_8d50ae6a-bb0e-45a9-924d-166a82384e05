import { get, post } from "@/utils/request";
// 临时类型定义
interface RealtimeStats {
  onlineUsers: number;
  activeConnections: number;
}

interface SystemMetricsMessage {
  cpu: number;
  memory: number;
  disk: number;
}

interface SystemAlertMessage {
  id: string;
  message: string;
  level: string;
}

interface NotificationMessage {
  id: string;
  title: string;
  content: string;
}

export const realtimeService = {
  /**
   * 获取实时推送统计信息
   */
  async getRealtimeStats(): Promise<RealtimeStats> {
    const response = await get<RealtimeStats>("/api/v1/realtime/stats");
    return response.data;
  },

  /**
   * 获取在线用户列表
   */
  async getOnlineUsers(): Promise<{ count: number; users: any[] }> {
    const response = await get<{ count: number; users: any[] }>("/api/v1/realtime/online-users");
    return response.data;
  },

  /**
   * 推送系统监控指标
   */
  async pushSystemMetrics(metrics: {
    cpu: number;
    memory: number;
    disk: number;
    network: number;
  }): Promise<{ sentCount: number }> {
    const response = await post<{ sentCount: number }>(
      "/api/v1/realtime/push/system-metrics",
      metrics
    );
    return response.data;
  },

  /**
   * 推送系统状态变更
   */
  async pushSystemStatus(status: {
    overall: "healthy" | "warning" | "critical";
    services: Array<{
      name: string;
      status: "healthy" | "warning" | "critical" | "down";
      responseTime: number;
      message?: string;
    }>;
  }): Promise<{ sentCount: number }> {
    const response = await post<{ sentCount: number }>(
      "/api/v1/realtime/push/system-status",
      status
    );
    return response.data;
  },

  /**
   * 推送系统告警
   */
  async pushSystemAlert(alert: {
    title: string;
    message: string;
    severity: "LOW" | "MEDIUM" | "HIGH" | "CRITICAL";
    component: string;
    status?: "PENDING" | "ACKNOWLEDGED" | "RESOLVED";
  }): Promise<{ sentCount: number; alertId: string }> {
    const response = await post<{ sentCount: number; alertId: string }>(
      "/api/v1/realtime/push/system-alert",
      alert
    );
    return response.data;
  },

  /**
   * 推送通知
   */
  async pushNotification(notification: {
    type: "info" | "warning" | "error" | "success";
    title: string;
    message: string;
    targetUserId?: string;
    actionUrl?: string;
    persistent?: boolean;
  }): Promise<{ sentCount: number; notificationId: string }> {
    const response = await post<{ sentCount: number; notificationId: string }>(
      "/api/v1/realtime/push/notification",
      notification
    );
    return response.data;
  },

  /**
   * 推送服务工单事件
   */
  async pushServiceEvent(data: {
    eventType:
      | "SERVICE_CREATED"
      | "SERVICE_UPDATED"
      | "SERVICE_STATUS_CHANGED"
      | "SERVICE_ASSIGNED";
    serviceData: {
      id: string;
      ticketNumber: string;
      title: string;
      status: string;
      priority: string;
      assignedTo?: string;
      customerName: string;
      updatedBy: string;
      changes?: Record<string, any>;
    };
  }): Promise<{ sentCount: number }> {
    const response = await post<{ sentCount: number }>("/api/v1/realtime/push/service-event", data);
    return response.data;
  },

  /**
   * 广播自定义消息
   */
  async broadcastMessage(message: {
    type: string;
    channel: string;
    priority?: "low" | "normal" | "high" | "critical";
    data: any;
    targetUserId?: string;
    strategy?: {
      channels?: string[];
      priority?: "low" | "normal" | "high" | "critical";
      throttle?: {
        enabled: boolean;
        interval: number;
        maxPerInterval: number;
      };
      filter?: {
        userRoles?: string[];
        permissions?: string[];
        conditions?: Record<string, any>;
      };
    };
  }): Promise<{ sentCount: number; messageId: string }> {
    const response = await post<{ sentCount: number; messageId: string }>(
      "/api/v1/realtime/broadcast",
      message
    );
    return response.data;
  },

  /**
   * 测试WebSocket连接
   */
  async testConnection(): Promise<{
    sentCount: number;
    connected: boolean;
    messageId: string;
  }> {
    const response = await post<{
      sentCount: number;
      connected: boolean;
      messageId: string;
    }>("/api/v1/realtime/test-connection");
    return response.data;
  },

  /**
   * 获取WebSocket连接详情
   */
  async getConnectionDetails(): Promise<{
    onlineUsers: any[];
    stats: RealtimeStats;
    summary: {
      totalConnections: number;
      activeConnections: number;
      idleConnections: number;
      messagesSent: number;
      messagesReceived: number;
      uptime: number;
    };
  }> {
    const response = await get<{
      onlineUsers: any[];
      stats: RealtimeStats;
      summary: {
        totalConnections: number;
        activeConnections: number;
        idleConnections: number;
        messagesSent: number;
        messagesReceived: number;
        uptime: number;
      };
    }>("/api/v1/realtime/connections");
    return response.data;
  },
};

export default realtimeService;
