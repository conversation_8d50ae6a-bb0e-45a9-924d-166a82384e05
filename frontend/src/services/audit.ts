import { get } from "@/utils/request";

export interface AuditLog {
  id: string;
  userId: string;
  user:
    | string
    | {
        id?: string;
        username?: string;
        fullName?: string;
      };
  resource: "AUTH" | "CUSTOMER" | "ARCHIVE" | "SERVICE" | "CONFIGURATION" | "SLA" | "USER" | "ROLE";
  action: "CREATE" | "UPDATE" | "DELETE" | "VIEW" | "LOGIN" | "LOGOUT" | "STATUS_CHANGE";
  resourceId?: string;
  details?: Record<string, any>;
  ipAddress?: string;
  timestamp: string;
}

export interface AuditLogListParams {
  page?: number;
  limit?: number;
  userId?: string;
  resource?: AuditLog["resource"];
  action?: AuditLog["action"];
  startDate?: string;
  endDate?: string;
}

export interface AuditLogListResponse {
  success: boolean;
  data: {
    logs: AuditLog[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
  message?: string;
}

export interface AuditStatsResponse {
  success: boolean;
  data: {
    totalOperations: number;
    resourceStats: Record<string, Record<string, number>>;
    actionBreakdown: Array<{
      resource: string;
      action: string;
      count: number;
    }>;
    activeUsers?: Array<{
      userId: string;
      username: string;
      fullName: string;
      operationCount: number;
    }>;
    timeRange?: {
      startDate?: string;
      endDate?: string;
    };
  };
}

class AuditService {
  // 获取操作日志列表
  async getAuditLogs(params: AuditLogListParams = {}): Promise<AuditLogListResponse> {
    return get("/api/v1/audit/logs", params);
  }

  // 获取操作统计
  async getAuditStats(
    params: {
      userId?: string;
      startDate?: string;
      endDate?: string;
    } = {}
  ): Promise<AuditStatsResponse> {
    return get("/api/v1/audit/stats", params);
  }

  // 获取当前用户的操作日志
  async getMyAuditLogs(
    params: {
      page?: number;
      limit?: number;
    } = {}
  ): Promise<AuditLogListResponse> {
    return get("/api/v1/audit/my-logs", params);
  }

  // 导出操作日志（管理员功能）
  async exportAuditLogs(params: AuditLogListParams = {}): Promise<Blob> {
    const searchParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) searchParams.append(key, String(value));
    });
    const queryString = searchParams.toString();
    const url = `/api/v1/audit/export${queryString ? `?${queryString}` : ""}`;

    const token = (() => {
      try {
        if ((window as any).__AUTH_STORE__) {
          const t = (window as any).__AUTH_STORE__.getState?.()?.token;
          if (t) return t;
        }
        const authStorage = localStorage.getItem("auth-storage");
        if (authStorage) {
          const parsed = JSON.parse(authStorage);
          return parsed.state?.token || null;
        }
      } catch {}
      return null;
    })();

    const response = await fetch(url, {
      method: "GET",
      headers: {
        ...(token ? { Authorization: `Bearer ${token}` } : {}),
      },
      credentials: "include",
    });

    if (!response.ok) {
      throw new Error("导出失败");
    }

    return response.blob();
  }

  // 格式化操作类型显示
  formatAction(action: AuditLog["action"]): string {
    const actionMap = {
      CREATE: "创建",
      UPDATE: "更新",
      DELETE: "删除",
      VIEW: "查看",
      LOGIN: "登录",
      LOGOUT: "登出",
      STATUS_CHANGE: "状态变更",
    };
    return actionMap[action] || action;
  }

  // 格式化资源类型显示
  formatResource(resource: AuditLog["resource"]): string {
    const resourceMap = {
      AUTH: "认证",
      CUSTOMER: "客户",
      ARCHIVE: "项目档案",
      SERVICE: "服务工单",
      CONFIGURATION: "配置",
      SLA: "SLA",
      USER: "用户",
      ROLE: "角色",
    };
    return resourceMap[resource] || resource;
  }
}

export const auditService = new AuditService();
