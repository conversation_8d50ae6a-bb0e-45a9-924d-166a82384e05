import { get } from "@/utils/request";
import type { ApiResponse } from "@/types";

export interface DashboardStats {
  // 基础统计
  customers: number;
  projects: number;
  pendingServices: number;
  configurations: number;

  // 服务工单详细统计
  totalServices: number;
  recentServices: number;
  avgResolutionTime: number;
  statusDistribution: { status: string; count: number }[];
  categoryDistribution: { category: string; count: number }[];
  priorityDistribution: { priority: string; count: number }[];

  // 系统监控数据
  systemHealth: "healthy" | "warning" | "critical";
  systemResources: {
    cpu: { usage: number; total: number };
    memory: { usage: number; total: number };
    disk: { usage: number; total: number };
  };

  // 数据库连接状态
  databaseStatus: {
    isConnected: boolean;
    responseTime?: number;
  };

  // Redis状态
  redisStatus: {
    isConnected: boolean;
    responseTime?: number;
  };
}

export interface TrendData {
  date: string;
  services: number;
  customers: number;
  projects: number;
}

class DashboardService {
  // 获取综合仪表板数据 - 并行请求优化
  async getDashboardStats(): Promise<ApiResponse<DashboardStats>> {
    try {
      // 并行请求所有需要的数据
      const [
        serviceStats,
        customerStats,
        projectStats,
        systemStatus,
        systemResources,
        dbStatus,
        redisStatus,
      ] = await Promise.all([
        this.getServiceStats(),
        this.getCustomerStats(),
        this.getProjectStats(),
        this.getSystemStatus(),
        this.getSystemResources(),
        this.getDatabaseStatus(),
        this.getRedisStatus(),
      ]);

      // 整合所有数据
      const dashboardData: DashboardStats = {
        // 基础统计
        customers: customerStats.data.total || 0,
        projects: projectStats.data.total || 0,
        pendingServices:
          serviceStats.data.statusDistribution?.find((s: any) => s.status === "PENDING")?.count ||
          0,
        configurations: 0, // 配置数据需要单独实现

        // 服务统计详情
        totalServices: serviceStats.data.totalServices || 0,
        recentServices: serviceStats.data.recentServices || 0,
        avgResolutionTime: serviceStats.data.avgResolutionTime || 0,
        statusDistribution: serviceStats.data.statusDistribution || [],
        categoryDistribution: serviceStats.data.categoryDistribution || [],
        priorityDistribution: serviceStats.data.priorityDistribution || [],

        // 系统状态
        systemHealth: this.determineSystemHealth(systemStatus.data),
        systemResources: {
          cpu: systemResources.data.cpu || { usage: 0, total: 100 },
          memory: systemResources.data.memory || { usage: 0, total: 100 },
          disk: systemResources.data.disk || { usage: 0, total: 100 },
        },

        // 数据库和Redis状态
        databaseStatus: {
          isConnected: dbStatus.data.isConnected || false,
          responseTime: dbStatus.data.responseTime,
        },
        redisStatus: {
          isConnected: redisStatus.data.isConnected || false,
          responseTime: redisStatus.data.responseTime,
        },
      };

      return {
        success: true,
        data: dashboardData,
        message: "Dashboard data loaded successfully",
      };
    } catch (error) {
      console.error("Dashboard stats error:", error);
      throw error;
    }
  }

  // 获取服务工单统计
  private async getServiceStats(): Promise<ApiResponse<any>> {
    return get("/api/v1/services/stats");
  }

  // 获取客户统计 (需要后端实现)
  private async getCustomerStats(): Promise<ApiResponse<{ total: number }>> {
    try {
      // 暂时通过获取客户列表来统计数量
      const response = await get("/api/v1/customers", { limit: 1 });
      return {
        success: true,
        data: { total: response.data?.pagination?.total || 0 },
        message: "Customer stats loaded",
      };
    } catch (error) {
      return { success: false, data: { total: 0 }, message: "Failed to load customer stats" };
    }
  }

  // 获取项目统计 (需要后端实现)
  private async getProjectStats(): Promise<ApiResponse<{ total: number }>> {
    try {
      // 暂时通过获取项目列表来统计数量
      const response = await get("/api/v1/archives", { limit: 1 });
      return {
        success: true,
        data: { total: response.data?.pagination?.total || 0 },
        message: "Project stats loaded",
      };
    } catch (error) {
      return { success: false, data: { total: 0 }, message: "Failed to load project stats" };
    }
  }

  // 获取系统状态
  private async getSystemStatus(): Promise<ApiResponse<any>> {
    try {
      return await get("/api/v1/system/status");
    } catch (error) {
      return {
        success: false,
        data: { status: "critical" },
        message: "Failed to get system status",
      };
    }
  }

  // 获取系统资源
  private async getSystemResources(): Promise<ApiResponse<any>> {
    try {
      return await get("/api/v1/system/resources");
    } catch (error) {
      return {
        success: false,
        data: {
          cpu: { usage: 0, total: 100 },
          memory: { usage: 0, total: 100 },
          disk: { usage: 0, total: 100 },
        },
        message: "Failed to get system resources",
      };
    }
  }

  // 获取数据库状态
  private async getDatabaseStatus(): Promise<ApiResponse<any>> {
    try {
      return await get("/api/v1/system/database");
    } catch (error) {
      return {
        success: false,
        data: { isConnected: false },
        message: "Failed to get database status",
      };
    }
  }

  // 获取Redis状态
  private async getRedisStatus(): Promise<ApiResponse<any>> {
    try {
      return await get("/api/v1/system/redis");
    } catch (error) {
      return {
        success: false,
        data: { isConnected: false },
        message: "Failed to get Redis status",
      };
    }
  }

  // 获取趋势数据 (7天内的数据变化)
  async getTrendData(days: number = 7): Promise<ApiResponse<TrendData[]>> {
    try {
      // 可以后端实现专门的趋势统计API
      // 暂时返回模拟数据结构
      const trendData: TrendData[] = [];
      const now = new Date();

      for (let i = days - 1; i >= 0; i--) {
        const date = new Date(now);
        date.setDate(date.getDate() - i);
        trendData.push({
          date: date.toISOString().split("T")[0],
          services: 0, // 实际应该从后端获取
          customers: 0,
          projects: 0,
        });
      }

      return {
        success: true,
        data: trendData,
        message: "Trend data loaded",
      };
    } catch (error) {
      console.error("Trend data error:", error);
      throw error;
    }
  }

  // 判断系统整体健康状态
  private determineSystemHealth(systemData: any): "healthy" | "warning" | "critical" {
    if (!systemData) return "critical";

    // 根据各项指标判断健康状态
    const cpu = systemData.cpu?.usage || 0;
    const memory = systemData.memory?.usage || 0;
    const disk = systemData.disk?.usage || 0;

    if (cpu > 90 || memory > 90 || disk > 90) {
      return "critical";
    }

    if (cpu > 70 || memory > 70 || disk > 70) {
      return "warning";
    }

    return "healthy";
  }

  // 获取快速统计数据 (仅基础指标，快速响应)
  async getQuickStats(): Promise<
    ApiResponse<{
      services: number;
      customers: number;
      projects: number;
      systemHealth: string;
    }>
  > {
    try {
      const [serviceStats, systemStatus] = await Promise.all([
        this.getServiceStats(),
        this.getSystemStatus(),
      ]);

      return {
        success: true,
        data: {
          services: serviceStats.data.totalServices || 0,
          customers: 0, // 可以从缓存获取
          projects: 0, // 可以从缓存获取
          systemHealth: this.determineSystemHealth(systemStatus.data),
        },
        message: "Quick stats loaded",
      };
    } catch (error) {
      console.error("Quick stats error:", error);
      throw error;
    }
  }
}

export const dashboardService = new DashboardService();
