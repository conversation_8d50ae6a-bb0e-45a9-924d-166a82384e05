/**
 * AI 分析服务模块 - 集成真正的AI分析能力
 */

import { get, post, put } from "@/utils/request";
import type { ApiResponse } from "@/types";
import type {
  AIConfiguration,
  AIAnalysisRequest,
  AIAnalysisResponse,
  AIFeedback,
  AIStatistics,
} from "@/types/ai";
import { DEFAULT_AI_CONFIG } from "@/types/ai";
import { aiAnalysisService } from "./aiAnalysis";

// AI分析结果缓存
const analysisCache = new Map<string, { result: AIAnalysisResponse; timestamp: number }>();
const CACHE_DURATION = 5 * 60 * 1000; // 5分钟缓存

class AIService {
  /**
   * 智能分析工单内容 - 使用真正的AI语义分析
   */
  async analyzeTicketContent(request: AIAnalysisRequest): Promise<ApiResponse<AIAnalysisResponse>> {
    const cacheKey = this.generateCacheKey(request);

    try {
      // 1. 检查缓存
      const cached = this.getCachedAnalysis(cacheKey);
      if (cached) {
        console.log("使用缓存的AI分析结果");
        return {
          success: true,
          data: cached,
          message: "AI分析完成(缓存)",
        };
      }

      // 2. 调用真正的AI分析服务
      const response = await aiAnalysisService.analyzeTicketContent(request);

      if (response.success && response.data) {
        // 缓存成功的分析结果
        this.setCachedAnalysis(cacheKey, response.data);
        return {
          success: true,
          data: response.data,
          message: response.message,
        };
      }

      // 3. AI分析失败，使用简单回退策略
      console.warn("AI智能分析失败，使用基础分析");
      return this.fallbackAnalysis(request);
    } catch (error: any) {
      console.error("AI分析服务异常:", error);

      // 异常情况下的回退分析
      return this.fallbackAnalysis(request);
    }
  }

  /**
   * 简单回退分析策略
   */
  private async fallbackAnalysis(
    request: AIAnalysisRequest
  ): Promise<ApiResponse<AIAnalysisResponse>> {
    const startTime = Date.now();
    const { description } = request;

    // 创建基础的默认建议
    const suggestions = [
      {
        field: "title" as const,
        suggested: description.length > 50 ? `${description.substring(0, 50)}...` : description,
        confidence: 40,
        reasoning: "基于描述内容的简单标题建议",
      },
      {
        field: "category" as const,
        suggested: "SUPPORT",
        confidence: 30,
        reasoning: "默认支持类别，建议手动选择具体类别",
      },
      {
        field: "priority" as const,
        suggested: "MEDIUM",
        confidence: 30,
        reasoning: "默认中等优先级，请根据实际情况调整",
      },
      {
        field: "slaTemplate" as const,
        suggested: "standard",
        confidence: 25,
        reasoning: "默认标准SLA模板",
      },
    ];

    const response: AIAnalysisResponse = {
      success: false, // 标记为失败，表示这是回退策略
      requestId: `fallback_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
      timestamp: new Date().toISOString(),
      suggestions,
      overallConfidence: 30,
      processingTime: Date.now() - startTime,
      warning: "AI智能分析服务暂时不可用，提供基础建议。建议手动填写或稍后重试智能分析。",
    };

    return {
      success: true,
      data: response,
      message: "AI分析服务不可用，已提供基础建议",
    };
  }

  /**
   * 生成缓存键
   */
  private generateCacheKey(request: AIAnalysisRequest): string {
    const content = request.description + JSON.stringify(request.contextData || {});
    return btoa(content)
      .replace(/[+=\/]/g, "")
      .substring(0, 20);
  }

  /**
   * 获取缓存的分析结果
   */
  private getCachedAnalysis(key: string): AIAnalysisResponse | null {
    const cached = analysisCache.get(key);
    if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
      return cached.result;
    }
    analysisCache.delete(key);
    return null;
  }

  /**
   * 设置缓存的分析结果
   */
  private setCachedAnalysis(key: string, result: AIAnalysisResponse): void {
    analysisCache.set(key, { result, timestamp: Date.now() });

    // 清理过期缓存，防止内存泄漏
    if (analysisCache.size > 50) {
      const now = Date.now();
      for (const [k, v] of analysisCache.entries()) {
        if (now - v.timestamp > CACHE_DURATION) {
          analysisCache.delete(k);
        }
      }
    }
  }

  /**
   * 清理所有缓存
   */
  clearAnalysisCache(): void {
    analysisCache.clear();
  }

  /**
   * 获取 AI 配置
   */
  async getAIConfiguration(userId?: string): Promise<ApiResponse<AIConfiguration>> {
    try {
      const url = userId ? `/api/v1/ai/config/${userId}` : "/api/v1/ai/config/global";
      return await get<AIConfiguration>(url);
    } catch (error) {
      // 返回默认配置
      return {
        success: true,
        data: DEFAULT_AI_CONFIG,
        message: "使用默认 AI 配置",
      };
    }
  }

  /**
   * 更新 AI 配置
   */
  async updateAIConfiguration(
    config: AIConfiguration,
    userId?: string
  ): Promise<ApiResponse<AIConfiguration>> {
    const url = userId ? `/api/v1/ai/config/${userId}` : "/api/v1/ai/config/global";
    return await put<AIConfiguration>(url, config);
  }

  /**
   * 提交 AI 反馈
   */
  async submitFeedback(feedback: AIFeedback): Promise<ApiResponse<void>> {
    return await post<void>("/api/v1/ai/feedback", feedback);
  }

  /**
   * 获取 AI 统计数据
   */
  async getAIStatistics(userId?: string): Promise<ApiResponse<AIStatistics>> {
    const url = userId ? `/api/v1/ai/statistics/${userId}` : "/api/v1/ai/statistics/global";
    return await get<AIStatistics>(url);
  }

  /**
   * 防抖函数工具
   */
  debounce<T extends (...args: any[]) => any>(func: T, delay: number): T {
    let timeoutId: NodeJS.Timeout;
    return ((...args: any[]) => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => func.apply(this, args), delay);
    }) as T;
  }
}

// 导出单例
export const aiService = new AIService();

// 默认导出
export default aiService;
