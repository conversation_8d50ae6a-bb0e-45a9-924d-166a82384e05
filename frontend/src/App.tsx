import React, { useEffect } from "react";
import { Router<PERSON>rovider, create<PERSON><PERSON>er<PERSON>outer } from "react-router-dom";
import { ErrorBoundary } from "react-error-boundary";
import { ConfigProvider } from "@douyinfe/semi-ui";
import { routeConfig } from "@/routes";
import { useUIStore } from "@/stores";
import { NotificationProvider } from "@/components/providers/NotificationProvider";
import { WebSocketProvider } from "@/contexts/WebSocketContext";
import "@douyinfe/semi-ui/dist/css/semi.css";

import { QueryClient, QueryClientProvider } from "@tanstack/react-query";

const queryClient = new QueryClient();

const router = createBrowserRouter(routeConfig);

function ErrorFallback({
  error,
  resetErrorBoundary,
}: {
  error: Error;
  resetErrorBoundary: () => void;
}) {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
      <div className="text-center">
        <h1 className="text-2xl font-bold text-red-600 mb-4">应用出现错误</h1>
        <p className="text-gray-600 dark:text-gray-400 mb-4">{error.message}</p>
        <button onClick={resetErrorBoundary} className="btn-primary">
          重新加载
        </button>
      </div>
    </div>
  );
}

export function App() {
  const { initTheme } = useUIStore();

  useEffect(() => {
    initTheme();
  }, [initTheme]);

  return (
    <ErrorBoundary FallbackComponent={ErrorFallback}>
      <QueryClientProvider client={queryClient}>
        <ConfigProvider>
          <WebSocketProvider>
            <NotificationProvider>
              <div className="App">
                <RouterProvider router={router} />
              </div>
            </NotificationProvider>
          </WebSocketProvider>
        </ConfigProvider>
      </QueryClientProvider>
    </ErrorBoundary>
  );
}
