import React, { useEffect, useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import {
  Button,
  Input,
  Select,
  Table,
  Pagination,
  Tag,
  Modal,
  Popconfirm,
  Space,
  Toast,
  Dropdown,
  Card,
  Row,
  Col,
  DatePicker,
  Checkbox,
  Progress,
  Avatar,
  Typography,
  Divider,
  Empty,
  Upload,
  Collapse,
  Badge,
  Spin,
  Tooltip,
  BackTop,
} from "@douyinfe/semi-ui";
import {
  IconPlus,
  IconSearch,
  IconRefresh,
  IconEdit,
  IconDelete,
  IconUser,
  IconLock,
  IconFilter,
  IconExport,
  IconImport,
  IconMore,
  IconCalendar,
  IconChevronDown,
  IconUserGroup,
  IconMail,
  IconPhone,
  IconShield,
  IconDownload,
  IconUpload,
  IconSetting,
  IconCopy,
  IconEyeOpened,
  IconEyeClosed,
  IconChevronUp,
  IconClose,
  IconTick,
  IconBell,
  IconActivity,
  IconFolder,
  IconUpload as IconTrendingUp,
} from "@douyinfe/semi-icons";
import { useUIStore } from "@/stores";
import { userService, type UserListParams, type User } from "@/services/user";
import { roleService } from "@/services/role";
import { UserForm } from "@/components/admin/UserForm";

const { Title, Text } = Typography;

export default function EnhancedUsersPage() {
  const { setBreadcrumbs } = useUIStore();
  const queryClient = useQueryClient();

  const [params, setParams] = useState<UserListParams>({
    page: 1,
    limit: 20,
  });
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingUser, setEditingUser] = useState<User | null>(null);
  const [isResetPasswordModalVisible, setIsResetPasswordModalVisible] = useState(false);
  const [resetPasswordUser, setResetPasswordUser] = useState<User | null>(null);
  const [newPassword, setNewPassword] = useState("");
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);
  const [isAdvancedFilterVisible, setIsAdvancedFilterVisible] = useState(false);
  const [isBatchModalVisible, setIsBatchModalVisible] = useState(false);
  const [isImportModalVisible, setIsImportModalVisible] = useState(false);
  const [isExportModalVisible, setIsExportModalVisible] = useState(false);
  const [batchAction, setBatchAction] = useState<"enable" | "disable" | "delete" | "assignRole">(
    "enable"
  );
  const [batchRoleId, setBatchRoleId] = useState<string>("");

  const [advancedFilters, setAdvancedFilters] = useState({
    createdDateRange: undefined as Date[] | undefined,
    lastLoginDateRange: undefined as Date[] | undefined,
    hasPhone: null as boolean | null,
    hasEmail: null as boolean | null,
  });

  const [exportOptions, setExportOptions] = useState({
    format: "excel",
    includeFields: [
      "username",
      "fullName",
      "email",
      "phone",
      "department",
      "role",
      "status",
      "createdAt",
    ],
    dateRange: undefined as Date[] | undefined,
  });

  useEffect(() => {
    setBreadcrumbs([
      { text: "首页", href: "/", icon: "🏠" },
      { text: "系统管理", icon: "⚙️" },
      { text: "用户管理", icon: "👤" },
    ]);
  }, [setBreadcrumbs]);

  // 获取用户列表
  const {
    data: usersData,
    isLoading,
    refetch,
  } = useQuery({
    queryKey: ["users", params, advancedFilters],
    queryFn: () => userService.getUsers({ ...params, ...advancedFilters }),
  });

  // 获取角色列表
  const { data: rolesData } = useQuery({
    queryKey: ["roles-all"],
    queryFn: () => roleService.getAllRoles(),
  });

  // 获取部门列表
  const { data: departmentsData } = useQuery({
    queryKey: ["departments"],
    queryFn: () => userService.getDepartments(),
  });

  // 获取用户统计
  const { data: userStats } = useQuery({
    queryKey: ["user-stats"],
    queryFn: () => userService.getUserStats(),
  });

  // 删除用户
  const deleteMutation = useMutation({
    mutationFn: (id: string) => userService.deleteUser(id),
    onSuccess: () => {
      Toast.success("用户删除成功");
      queryClient.invalidateQueries({ queryKey: ["users"] });
    },
    onError: (error: any) => {
      Toast.error(error.message || "删除失败");
    },
  });

  // 批量操作
  const batchMutation = useMutation({
    mutationFn: ({
      action,
      userIds,
      roleId,
    }: {
      action: string;
      userIds: string[];
      roleId?: string;
    }) => userService.batchUpdateUsers(action, userIds, roleId),
    onSuccess: (_, variables) => {
      const actionMap = {
        enable: "启用",
        disable: "禁用",
        delete: "删除",
        assignRole: "分配角色",
      };
      Toast.success(`批量${actionMap[variables.action as keyof typeof actionMap]}成功`);
      setSelectedRowKeys([]);
      setIsBatchModalVisible(false);
      queryClient.invalidateQueries({ queryKey: ["users"] });
    },
    onError: (error: any) => {
      Toast.error(error.message || "批量操作失败");
    },
  });

  // 导出用户数据
  const exportMutation = useMutation({
    mutationFn: (options: any) => userService.exportUsers(options),
    onSuccess: (data: any) => {
      // 创建下载链接
      const blob = new Blob([data], {
        type:
          exportOptions.format === "excel"
            ? "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
            : "text/csv",
      });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `用户数据.${exportOptions.format === "excel" ? "xlsx" : "csv"}`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      window.URL.revokeObjectURL(url);

      Toast.success("导出成功");
      setIsExportModalVisible(false);
    },
    onError: (error: any) => {
      Toast.error(error.message || "导出失败");
    },
  });

  // 导入用户数据
  const importMutation = useMutation({
    mutationFn: (file: File) => {
      // 这里应该解析文件并转换为CreateUserData[]，暂时返回空数组
      return userService.importUsers([]);
    },
    onSuccess: (data: any) => {
      Toast.success(`成功导入 ${data.successCount} 个用户，跳过 ${data.skipCount} 个用户`);
      setIsImportModalVisible(false);
      queryClient.invalidateQueries({ queryKey: ["users"] });
    },
    onError: (error: any) => {
      Toast.error(error.message || "导入失败");
    },
  });

  // 切换用户状态
  const toggleStatusMutation = useMutation({
    mutationFn: (id: string) => userService.toggleUserStatus(id),
    onSuccess: () => {
      Toast.success("用户状态更新成功");
      queryClient.invalidateQueries({ queryKey: ["users"] });
    },
    onError: (error: any) => {
      Toast.error(error.message || "状态更新失败");
    },
  });

  // 重置密码
  const resetPasswordMutation = useMutation({
    mutationFn: ({ id, password }: { id: string; password: string }) =>
      userService.resetPassword(id, password),
    onSuccess: () => {
      Toast.success("密码重置成功");
      setIsResetPasswordModalVisible(false);
      setResetPasswordUser(null);
      setNewPassword("");
    },
    onError: (error: any) => {
      Toast.error(error.message || "密码重置失败");
    },
  });

  const handleSearch = (value: string) => {
    setParams({ ...params, search: value, page: 1 });
  };

  const handleFilterChange = (key: string, value: any) => {
    setParams({ ...params, [key]: value, page: 1 });
  };

  const handleAdvancedFilterChange = (key: string, value: any) => {
    setAdvancedFilters({ ...advancedFilters, [key]: value });
  };

  const handlePageChange = (page: number, pageSize?: number) => {
    setParams({ ...params, page, limit: pageSize || params.limit });
  };

  const handleCreate = () => {
    setEditingUser(null);
    setIsModalVisible(true);
  };

  const handleEdit = (user: User) => {
    setEditingUser(user);
    setIsModalVisible(true);
  };

  const handleDelete = (user: User) => {
    deleteMutation.mutate(user.id);
  };

  const handleToggleStatus = (user: User) => {
    toggleStatusMutation.mutate(user.id);
  };

  const handleResetPassword = (user: User) => {
    setResetPasswordUser(user);
    setNewPassword("");
    setIsResetPasswordModalVisible(true);
  };

  const handleResetPasswordConfirm = () => {
    if (!resetPasswordUser || !newPassword) {
      Toast.error("请输入新密码");
      return;
    }
    if (newPassword.length < 6) {
      Toast.error("密码长度不能少于6位");
      return;
    }
    resetPasswordMutation.mutate({
      id: resetPasswordUser.id,
      password: newPassword,
    });
  };

  const handleBatchAction = () => {
    if (selectedRowKeys.length === 0) {
      Toast.error("请选择要操作的用户");
      return;
    }

    if (batchAction === "assignRole" && !batchRoleId) {
      Toast.error("请选择要分配的角色");
      return;
    }

    batchMutation.mutate({
      action: batchAction,
      userIds: selectedRowKeys,
      roleId: batchRoleId || undefined,
    });
  };

  const handleExport = () => {
    exportMutation.mutate(exportOptions);
  };

  const handleImport = (fileList: any[]) => {
    if (fileList.length > 0) {
      importMutation.mutate(fileList[0].fileInstance);
    }
  };

  const clearAdvancedFilters = () => {
    setAdvancedFilters({
      createdDateRange: undefined,
      lastLoginDateRange: undefined,
      hasPhone: null,
      hasEmail: null,
    });
  };

  const rowSelection: any = {
    selectedRowKeys,
    onChange: (keys: (string | number)[], _rows: User[]) => {
      setSelectedRowKeys(keys.map(k => String(k)));
    },
    onSelectAll: (selected: boolean, selectedRows: User[], changedRows: User[]) => {
      if (selected) {
        setSelectedRowKeys([...selectedRowKeys, ...changedRows.map(row => row.id)]);
      } else {
        const changedIds = new Set(changedRows.map(row => row.id));
        setSelectedRowKeys(selectedRowKeys.filter(id => !changedIds.has(id)));
      }
    },
  };

  const batchDropdownItems = [
    {
      node: "item" as const,
      name: "批量启用",
      onClick: () => {
        setBatchAction("enable");
        setIsBatchModalVisible(true);
      },
    },
    {
      node: "item" as const,
      name: "批量禁用",
      onClick: () => {
        setBatchAction("disable");
        setIsBatchModalVisible(true);
      },
    },
    {
      node: "item" as const,
      name: "批量分配角色",
      onClick: () => {
        setBatchAction("assignRole");
        setIsBatchModalVisible(true);
      },
    },
    {
      node: "item" as const,
      name: "批量删除",
      type: "danger" as const,
      onClick: () => {
        setBatchAction("delete");
        setIsBatchModalVisible(true);
      },
    },
  ];

  const moreActionsItems = [
    {
      node: "item" as const,
      name: "导出用户",
      icon: <IconExport />,
      onClick: () => setIsExportModalVisible(true),
    },
    {
      node: "item" as const,
      name: "导入用户",
      icon: <IconImport />,
      onClick: () => setIsImportModalVisible(true),
    },
    {
      node: "item" as const,
      name: "下载模板",
      icon: <IconDownload />,
      onClick: () => {
        // 下载导入模板
        const link = document.createElement("a");
        link.href = "/api/users/template";
        link.download = "用户导入模板.xlsx";
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      },
    },
  ];

  const columns = [
    {
      title: "用户信息",
      dataIndex: "username",
      key: "userInfo",
      width: 200,
      render: (text: string, record: User) => (
        <div className="flex items-center space-x-3">
          <Avatar
            size="small"
            style={{
              backgroundColor: record.isActive
                ? "var(--semi-color-primary)"
                : "var(--semi-color-tertiary)",
            }}
          >
            {record.fullName?.[0] || record.username?.[0]}
          </Avatar>
          <div className="flex-1 min-w-0">
            <div className="flex items-center space-x-2">
              <Text strong className="truncate">
                {record.fullName}
              </Text>
              {!record.isActive && (
                <Badge count="禁" style={{ backgroundColor: "var(--semi-color-danger)" }} />
              )}
            </div>
            <Text type="secondary" size="small" className="truncate">
              @{record.username}
            </Text>
          </div>
        </div>
      ),
    },
    {
      title: "联系方式",
      key: "contact",
      width: 220,
      render: (_: any, record: User) => (
        <div className="space-y-1">
          <div className="flex items-center space-x-2">
            <IconMail size="small" />
            <Text size="small" className="truncate">
              {record.email || "-"}
            </Text>
          </div>
          <div className="flex items-center space-x-2">
            <IconPhone size="small" />
            <Text size="small">{record.phone || "-"}</Text>
          </div>
        </div>
      ),
    },
    {
      title: "角色权限",
      key: "roleInfo",
      width: 160,
      render: (_: any, record: User) => (
        <div className="space-y-1">
          <Tag color="blue" size="small">
            {record.role?.name || "-"}
          </Tag>
          <div className="flex items-center space-x-1">
            <IconShield size="small" />
            <Text type="secondary" size="small">
              {record.role?.permissions?.length || 0} 项权限
            </Text>
          </div>
        </div>
      ),
    },
    {
      title: "部门",
      dataIndex: "department",
      key: "department",
      width: 120,
      render: (text: string) => (
        <div className="flex items-center space-x-2">
          <IconFolder size="small" />
          <Text size="small">{text || "-"}</Text>
        </div>
      ),
    },
    {
      title: "状态",
      dataIndex: "isActive",
      key: "status",
      width: 100,
      render: (isActive: boolean, record: User) => (
        <div className="space-y-1">
          <Tag color={isActive ? "green" : "red"}>{isActive ? "正常" : "禁用"}</Tag>
          {record.lastLoginAt && (
            <div className="flex items-center space-x-1">
              <IconActivity size="small" />
              <Text type="secondary" size="small">
                {new Date(record.lastLoginAt).toLocaleDateString()}
              </Text>
            </div>
          )}
        </div>
      ),
    },
    {
      title: "创建时间",
      dataIndex: "createdAt",
      key: "createdAt",
      width: 120,
      render: (text: string) => <Text size="small">{new Date(text).toLocaleDateString()}</Text>,
    },
    {
      title: "操作",
      key: "actions",
      width: 120,
      fixed: "right",
      render: (_: any, record: User) => (
        <Dropdown
          trigger="click"
          menu={[
            {
              node: "item" as const,
              name: "编辑",
              icon: <IconEdit />,
              onClick: () => handleEdit(record),
            },
            {
              node: "item" as const,
              name: "重置密码",
              icon: <IconLock />,
              onClick: () => handleResetPassword(record),
            },
            {
              node: "item" as const,
              name: record.isActive ? "禁用" : "启用",
              icon: record.isActive ? <IconEyeClosed /> : <IconEyeOpened />,
              onClick: () => handleToggleStatus(record),
            },
            {
              node: "divider",
            },
            {
              node: "item" as const,
              name: "删除",
              type: "danger",
              icon: <IconDelete />,
              onClick: () => {
                Modal.confirm({
                  title: "确认删除",
                  content: `确定要删除用户 "${record.fullName}" 吗？此操作不可撤销。`,
                  onOk: () => handleDelete(record),
                });
              },
            },
          ]}
        >
          <Button theme="borderless" type="tertiary" icon={<IconMore />} size="small" />
        </Dropdown>
      ),
    },
  ];

  return (
    <div className="space-y-6">
      <div className="ops-page-header">
        <div className="flex justify-between items-start flex-wrap gap-4">
          <div>
            <h1 className="ops-page-title">用户管理</h1>
            <p className="ops-page-subtitle">统一管理系统用户、角色权限和账户安全配置</p>
          </div>
          <div className="ops-page-actions">
            <Button type="primary" icon={<IconPlus />} onClick={handleCreate} size="large">
              新建用户
            </Button>
            <Dropdown trigger="click" menu={moreActionsItems}>
              <Button icon={<IconMore />} size="large">
                更多操作
                <IconChevronDown />
              </Button>
            </Dropdown>
          </div>
        </div>
      </div>

      {/* 统计卡片 */}
      {userStats && (
        <Row gutter={[24, 24]}>
          <Col span={24} md={6}>
            <div className="ops-stats-card ops-gradient-card">
              <div className="ops-stats-card-title">总用户数</div>
              <div className="ops-stats-card-value">{userStats?.data?.total}</div>
              <div className="ops-stats-card-trend">
                <IconTrendingUp size="small" />
                <span>系统总用户</span>
              </div>
              <div className="ops-stats-card-icon">
                <IconUser size="large" />
              </div>
            </div>
          </Col>
          <Col span={24} md={6}>
            <div className="ops-stats-card ops-gradient-card success">
              <div className="ops-stats-card-title">活跃用户</div>
              <div className="ops-stats-card-value">{userStats?.data?.active}</div>
              <div className="ops-stats-card-trend">
                <IconActivity size="small" />
                <span>活跃状态</span>
              </div>
              <div className="ops-stats-card-icon">
                <IconActivity size="large" />
              </div>
            </div>
          </Col>
          <Col span={24} md={6}>
            <div className="ops-stats-card ops-gradient-card info">
              <div className="ops-stats-card-title">今日登录</div>
              <div className="ops-stats-card-value">{userStats?.data?.todayLogins}</div>
              <div className="ops-stats-card-trend">
                <IconBell size="small" />
                <span>登录次数</span>
              </div>
              <div className="ops-stats-card-icon">
                <IconBell size="large" />
              </div>
            </div>
          </Col>
          <Col span={24} md={6}>
            <div className="ops-stats-card ops-gradient-card warning">
              <div className="ops-stats-card-title">新增用户</div>
              <div className="ops-stats-card-value">{userStats?.data?.newThisWeek}</div>
              <div className="ops-stats-card-trend">
                <IconUserGroup size="small" />
                <span>本周</span>
              </div>
              <div className="ops-stats-card-icon">
                <IconUserGroup size="large" />
              </div>
            </div>
          </Col>
        </Row>
      )}

      <div className="ops-table-container">
        {/* 搜索和基础筛选 */}
        <div className="ops-search-toolbar">
          <div className="ops-search-row">
            <Input
              prefix={<IconSearch />}
              placeholder="搜索用户名、姓名、邮箱或手机号"
              style={{ width: 320 }}
              onEnterPress={() => handleSearch(params.search || "")}
              showClear
              size="large"
            />
            <Select
              placeholder="角色筛选"
              style={{ width: 160 }}
              showClear
              onChange={value => handleFilterChange("roleId", value)}
              size="large"
            >
              {rolesData?.data?.map(role => (
                <Select.Option key={role.id} value={role.id}>
                  {role.name}
                </Select.Option>
              ))}
            </Select>
            <Select
              placeholder="部门筛选"
              style={{ width: 160 }}
              showClear
              onChange={value => handleFilterChange("department", value)}
              size="large"
            >
              {departmentsData?.data?.map(dept => (
                <Select.Option key={dept} value={dept}>
                  {dept}
                </Select.Option>
              ))}
            </Select>
            <Select
              placeholder="状态筛选"
              style={{ width: 130 }}
              showClear
              onChange={value => handleFilterChange("isActive", value)}
              size="large"
            >
              <Select.Option value="true">正常</Select.Option>
              <Select.Option value="false">禁用</Select.Option>
            </Select>
            <Button
              icon={<IconFilter />}
              type={isAdvancedFilterVisible ? "primary" : "tertiary"}
              onClick={() => setIsAdvancedFilterVisible(!isAdvancedFilterVisible)}
              size="large"
            >
              高级筛选
            </Button>
            <Button icon={<IconRefresh />} onClick={() => refetch()} size="large">
              刷新
            </Button>
          </div>
        </div>

        {/* 高级筛选 */}
        <Collapse
          activeKey={isAdvancedFilterVisible ? ["1"] : []}
          onChange={() => setIsAdvancedFilterVisible(!isAdvancedFilterVisible)}
        >
          <Collapse.Panel header="高级筛选条件" itemKey="1">
            <Row gutter={[16, 16]}>
              <Col span={24} md={6}>
                <div>
                  <Text strong>创建时间范围</Text>
                  <DatePicker
                    type="dateRange"
                    style={{ width: "100%", marginTop: 8 }}
                    value={advancedFilters.createdDateRange}
                    onChange={value => handleAdvancedFilterChange("createdDateRange", value)}
                  />
                </div>
              </Col>
              <Col span={24} md={6}>
                <div>
                  <Text strong>最后登录时间</Text>
                  <DatePicker
                    type="dateRange"
                    style={{ width: "100%", marginTop: 8 }}
                    value={advancedFilters.lastLoginDateRange}
                    onChange={value => handleAdvancedFilterChange("lastLoginDateRange", value)}
                  />
                </div>
              </Col>
              <Col span={24} md={6}>
                <div>
                  <Text strong>联系方式</Text>
                  <div className="space-y-2 mt-2">
                    <Checkbox
                      checked={advancedFilters.hasEmail === true}
                      indeterminate={advancedFilters.hasEmail === null}
                      onChange={checked =>
                        handleAdvancedFilterChange("hasEmail", checked ? true : null)
                      }
                    >
                      有邮箱
                    </Checkbox>
                    <Checkbox
                      checked={advancedFilters.hasPhone === true}
                      indeterminate={advancedFilters.hasPhone === null}
                      onChange={checked =>
                        handleAdvancedFilterChange("hasPhone", checked ? true : null)
                      }
                    >
                      有手机号
                    </Checkbox>
                  </div>
                </div>
              </Col>
              <Col span={24} md={6}>
                <div>
                  <Text strong>操作</Text>
                  <div className="mt-2">
                    <Button onClick={clearAdvancedFilters}>清空条件</Button>
                  </div>
                </div>
              </Col>
            </Row>
          </Collapse.Panel>
        </Collapse>

        {/* 批量操作栏 */}
        {selectedRowKeys.length > 0 && (
          <div className="ops-batch-actions">
            <div className="ops-batch-info">
              <IconUser />
              <Text strong>已选择 {selectedRowKeys.length} 个用户</Text>
              <Button size="small" theme="borderless" onClick={() => setSelectedRowKeys([])}>
                清空选择
              </Button>
            </div>
            <div className="ops-batch-controls">
              <Dropdown trigger="click" menu={batchDropdownItems}>
                <Button type="primary">
                  批量操作
                  <IconChevronDown />
                </Button>
              </Dropdown>
            </div>
          </div>
        )}

        {/* 用户表格 */}
        <div className="bg-white">
          <Table
            columns={columns as any}
            dataSource={usersData?.data?.users || []}
            rowKey="id"
            loading={isLoading}
            pagination={false}
            scroll={{ x: 1400 }}
            rowSelection={rowSelection}
            empty={
              <div className="flex flex-col items-center py-16">
                <div className="w-20 h-20 rounded-full bg-gray-100 flex items-center justify-center mb-6">
                  <IconUser style={{ fontSize: 32 }} className="text-gray-400" />
                </div>
                <Title heading={5} className="mb-2 text-gray-600">
                  暂无用户数据
                </Title>
                <Text type="secondary" className="mb-6">
                  您可以创建新用户或调整筛选条件
                </Text>
                <Button type="primary" icon={<IconPlus />} onClick={handleCreate}>
                  创建第一个用户
                </Button>
              </div>
            }
          />
        </div>

        {/* 分页 */}
        {usersData?.data && (
          <div className="ops-card-footer">
            <div className="flex justify-between items-center">
              <div className="flex items-center space-x-4">
                <Text type="secondary" className="text-base">
                  共 <Text strong>{usersData.data.total}</Text> 条记录，第{" "}
                  <Text strong>{usersData.data.page}</Text> 页，共{" "}
                  <Text strong>{Math.ceil(usersData.data.total / usersData.data.limit)}</Text> 页
                </Text>
              </div>
              <Pagination
                currentPage={usersData.data.page}
                total={usersData.data.total}
                pageSize={usersData.data.limit}
                showSizeChanger
                showQuickJumper
                onPageChange={handlePageChange}
                pageSizeOpts={[10, 20, 50, 100]}
                size="default"
              />
            </div>
          </div>
        )}
      </div>

      {/* 用户表单弹窗 */}
      <Modal
        title={
          <div className="flex items-center space-x-2">
            <IconUser />
            <span>{editingUser ? "编辑用户" : "新建用户"}</span>
          </div>
        }
        visible={isModalVisible}
        onCancel={() => setIsModalVisible(false)}
        footer={null}
        width={700}
        style={{ maxHeight: "90vh" }}
      >
        <UserForm
          user={
            editingUser
              ? {
                  id: editingUser.id,
                  username: editingUser.username,
                  email: editingUser.email,
                  fullName: editingUser.fullName,
                  roleId: editingUser.role?.id || "",
                  department: editingUser.department,
                  phone: editingUser.phone,
                  isActive: editingUser.isActive,
                }
              : null
          }
          onSuccess={() => {
            setIsModalVisible(false);
            queryClient.invalidateQueries({ queryKey: ["users"] });
          }}
          onCancel={() => setIsModalVisible(false)}
        />
      </Modal>

      {/* 重置密码弹窗 */}
      <Modal
        title="重置密码"
        visible={isResetPasswordModalVisible}
        onOk={handleResetPasswordConfirm}
        onCancel={() => {
          setIsResetPasswordModalVisible(false);
          setResetPasswordUser(null);
          setNewPassword("");
        }}
        confirmLoading={resetPasswordMutation.isPending}
      >
        <div className="space-y-4">
          <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded">
            <Avatar size="small">
              {resetPasswordUser?.fullName?.[0] || resetPasswordUser?.username?.[0]}
            </Avatar>
            <div>
              <Text strong>{resetPasswordUser?.fullName}</Text>
              <div>
                <Text type="secondary" size="small">
                  @{resetPasswordUser?.username}
                </Text>
              </div>
            </div>
          </div>
          <div>
            <Text strong>新密码</Text>
            <Input
              mode="password"
              value={newPassword}
              onChange={value => setNewPassword(value)}
              placeholder="请输入新密码（至少6位）"
              style={{ marginTop: 8 }}
            />
          </div>
        </div>
      </Modal>

      {/* 批量操作确认弹窗 */}
      <Modal
        title="批量操作确认"
        visible={isBatchModalVisible}
        onOk={handleBatchAction}
        onCancel={() => {
          setIsBatchModalVisible(false);
          setBatchRoleId("");
        }}
        confirmLoading={batchMutation.isPending}
      >
        <div className="space-y-4">
          <div>
            <Text>
              确定要对选中的 <Text strong>{selectedRowKeys.length}</Text> 个用户执行
              <Text strong type="warning">
                {batchAction === "enable" && "启用"}
                {batchAction === "disable" && "禁用"}
                {batchAction === "delete" && "删除"}
                {batchAction === "assignRole" && "分配角色"}
              </Text>
              操作吗？
            </Text>
          </div>
          {batchAction === "assignRole" && (
            <div>
              <Text strong>选择角色</Text>
              <Select
                placeholder="请选择要分配的角色"
                style={{ width: "100%", marginTop: 8 }}
                value={batchRoleId}
                onChange={value => setBatchRoleId(String(value || ""))}
              >
                {rolesData?.data?.map(role => (
                  <Select.Option key={role.id} value={role.id}>
                    {role.name}
                  </Select.Option>
                ))}
              </Select>
            </div>
          )}
          {batchAction === "delete" && (
            <div className="p-3 bg-red-50 border border-red-200 rounded">
              <Text type="danger">
                <IconShield /> 警告：删除操作不可撤销，请谨慎操作！
              </Text>
            </div>
          )}
        </div>
      </Modal>

      {/* 导出弹窗 */}
      <Modal
        title="导出用户数据"
        visible={isExportModalVisible}
        onOk={handleExport}
        onCancel={() => setIsExportModalVisible(false)}
        confirmLoading={exportMutation.isPending}
        width={600}
      >
        <div className="space-y-4">
          <div>
            <Text strong>导出格式</Text>
            <Select
              value={exportOptions.format}
              onChange={value => setExportOptions({ ...exportOptions, format: String(value) })}
              style={{ width: "100%", marginTop: 8 }}
            >
              <Select.Option value="excel">Excel (.xlsx)</Select.Option>
              <Select.Option value="csv">CSV (.csv)</Select.Option>
            </Select>
          </div>

          <div>
            <Text strong>导出字段</Text>
            <div className="grid grid-cols-2 gap-2 mt-2">
              {[
                { key: "username", label: "用户名" },
                { key: "fullName", label: "姓名" },
                { key: "email", label: "邮箱" },
                { key: "phone", label: "手机号" },
                { key: "department", label: "部门" },
                { key: "role", label: "角色" },
                { key: "status", label: "状态" },
                { key: "createdAt", label: "创建时间" },
              ].map(field => (
                <Checkbox
                  key={field.key}
                  checked={exportOptions.includeFields.includes(field.key)}
                  onChange={checked => {
                    if (checked) {
                      setExportOptions({
                        ...exportOptions,
                        includeFields: [...exportOptions.includeFields, field.key],
                      });
                    } else {
                      setExportOptions({
                        ...exportOptions,
                        includeFields: exportOptions.includeFields.filter(f => f !== field.key),
                      });
                    }
                  }}
                >
                  {field.label}
                </Checkbox>
              ))}
            </div>
          </div>

          <div>
            <Text strong>时间范围</Text>
            <DatePicker
              type="dateRange"
              style={{ width: "100%", marginTop: 8 }}
              value={exportOptions.dateRange}
              onChange={value =>
                setExportOptions({ ...exportOptions, dateRange: value as Date[] | undefined })
              }
              placeholder={["开始时间", "结束时间"]}
            />
          </div>
        </div>
      </Modal>

      {/* 导入弹窗 */}
      <Modal
        title="导入用户数据"
        visible={isImportModalVisible}
        onCancel={() => setIsImportModalVisible(false)}
        footer={null}
        width={600}
      >
        <div className="space-y-4">
          <div className="p-4 bg-blue-50 border border-blue-200 rounded">
            <div className="flex items-start space-x-2">
              <IconBell className="text-blue-500 mt-1" />
              <div>
                <Text strong>导入说明：</Text>
                <ul className="list-disc list-inside mt-2 space-y-1 text-sm">
                  <li>支持 Excel (.xlsx) 和 CSV (.csv) 格式</li>
                  <li>请先下载导入模板，按照格式填写数据</li>
                  <li>用户名和邮箱必须唯一，重复的将被跳过</li>
                  <li>默认密码为：123456（首次登录需修改）</li>
                </ul>
              </div>
            </div>
          </div>

          <div>
            <div className="flex items-center justify-between mb-2">
              <Text strong>选择文件</Text>
              <Button
                size="small"
                icon={<IconDownload />}
                onClick={() => {
                  const link = document.createElement("a");
                  link.href = "/api/users/template";
                  link.download = "用户导入模板.xlsx";
                  document.body.appendChild(link);
                  link.click();
                  document.body.removeChild(link);
                }}
              >
                下载模板
              </Button>
            </div>
            <Upload
              action=""
              accept=".xlsx,.csv"
              limit={1}
              onChange={({ fileList }) => handleImport(fileList)}
              showUploadList
            >
              <Button icon={<IconUpload />} theme="light" block>
                点击上传或拖拽文件到此处
              </Button>
            </Upload>
          </div>
        </div>
      </Modal>

      <BackTop />
    </div>
  );
}
