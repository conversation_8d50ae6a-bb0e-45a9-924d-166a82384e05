import React, { useState, useMemo } from 'react'
import { 
  Card, 
  Typography, 
  DatePicker, 
  Button, 
  Select, 
  Space, 
  Spin, 
  Empty,
  Row,
  Col,
  Tabs,
  TabPane,
  Badge,
  Toast,
  Skeleton
} from '@douyinfe/semi-ui'
import { 
  IconCalendar, 
  IconDownload, 
  IconRefresh, 
  IconActivity,
  IconStar,
  IconShield
} from '@douyinfe/semi-icons'
import { 
  useDashboardData, 
  useOperationEfficiency, 
  useCustomerSatisfaction, 
  useSLACompliance, 
  useServiceTrends, 
  useReportExport,
  useReportCache 
} from '@/hooks/useReportData'
import { EfficiencyChart } from '@/components/reports/EfficiencyChart'
import { SatisfactionChart } from '@/components/reports/SatisfactionChart'
import { SLAComplianceChart } from '@/components/reports/SLAComplianceChart'
import { TrendAnalysisChart } from '@/components/reports/TrendAnalysisChart'
import { EngineerWorkloadTable } from '@/components/reports/EngineerWorkloadTable'

const { Title, Text } = Typography

export const ReportsPage: React.FC = () => {
  // ========== 状态管理 ==========
  const [timeRange, setTimeRange] = useState<{
    type: 'custom' | 'last7days' | 'last30days' | 'lastQuarter' | 'lastYear'
    start?: Date
    end?: Date
  }>({
    type: 'last30days',
    start: undefined,
    end: undefined
  })
  const [activeTab, setActiveTab] = useState('efficiency')

  // ========== 数据查询 ==========
  const timeParams = useMemo(() => {
    if (timeRange.type === 'custom' && timeRange.start && timeRange.end) {
      return {
        start: timeRange.start.toISOString(),
        end: timeRange.end.toISOString()
      }
    }
    
    const daysMap: Record<string, number> = {
      'last7days': 7,
      'last30days': 30,
      'lastQuarter': 90,
      'lastYear': 365
    }
    
    return timeRange.type === 'custom' ? {} : { days: daysMap[timeRange.type] || 30 }
  }, [timeRange])

  const dashboardQuery = useDashboardData(timeParams)
  
  // 按需加载数据
  const efficiencyQuery = useOperationEfficiency({
    ...timeParams,
    enabled: activeTab === 'efficiency'
  })
  const satisfactionQuery = useCustomerSatisfaction({
    ...timeParams,
    enabled: activeTab === 'satisfaction'
  })
  const slaQuery = useSLACompliance({
    ...timeParams,
    enabled: activeTab === 'sla'
  })
  const trendsQuery = useServiceTrends({
    ...timeParams,
    enabled: activeTab === 'trends'
  })

  // ========== Hook使用 ==========
  const { downloadExcel, downloadCSV, downloadPDF } = useReportExport()
  const { invalidateAllReports } = useReportCache()

  // ========== 事件处理 ==========
  const handleRefresh = () => {
    invalidateAllReports()
    Toast.success('数据刷新中...')
  }

  const handleExportExcel = async () => {
    try {
      if (dashboardQuery.data) {
        await downloadExcel(dashboardQuery.data, `报表数据_${new Date().toISOString().split('T')[0]}`)
        Toast.success('Excel导出成功')
      }
    } catch (error) {
      Toast.error('Excel导出失败')
    }
  }

  const handleTimeRangeChange = (type: string | number | any[] | Record<string, any> | undefined) => {
    const typeStr = String(type)
    if (typeStr === 'custom') {
      setTimeRange({ type: 'custom', start: undefined, end: undefined })
    } else {
      setTimeRange({ type: typeStr as 'last7days' | 'last30days' | 'lastQuarter' | 'lastYear', start: undefined, end: undefined })
    }
  }

  const handleCustomTimeChange = (dates: string | string[] | Date | Date[] | undefined) => {
    if (Array.isArray(dates) && dates.length === 2 && dates[0] instanceof Date && dates[1] instanceof Date) {
      setTimeRange({
        type: 'custom',
        start: dates[0],
        end: dates[1]
      })
    }
  }

  // ========== 渲染方法 ==========

  const renderOverviewStats = () => {
    const { data: dashboard, isLoading } = dashboardQuery
    
    if (isLoading) {
      return <Spin size="large" />
    }

    if (!dashboard) {
      return <Empty description="暂无数据" />
    }

    return (
      <Row gutter={16}>
        <Col span={6}>
          <Card className="text-center">
            <div className="mb-2">
              <IconActivity style={{ fontSize: '24px', color: '#1890ff' }} />
            </div>
            <div className="text-2xl font-bold text-blue-600 mb-1">
              {dashboard.summary.totalServices}
            </div>
            <div className="text-gray-600 text-sm">总服务工单</div>
          </Card>
        </Col>
        <Col span={6}>
          <Card className="text-center">
            <div className="mb-2">
              <IconActivity style={{ fontSize: '24px', color: '#52c41a' }} />
            </div>
            <div className="text-2xl font-bold text-green-600 mb-1">
              {dashboard.summary.completionRate}%
            </div>
            <div className="text-gray-600 text-sm">完成率</div>
          </Card>
        </Col>
        <Col span={6}>
          <Card className="text-center">
            <div className="mb-2">
              <IconStar style={{ fontSize: '24px', color: '#faad14' }} />
            </div>
            <div className="text-2xl font-bold text-yellow-600 mb-1">
              {dashboard.summary.avgSatisfaction}/5.0
            </div>
            <div className="text-gray-600 text-sm">客户满意度</div>
          </Card>
        </Col>
        <Col span={6}>
          <Card className="text-center">
            <div className="mb-2">
              <IconShield style={{ fontSize: '24px', color: dashboard.summary.slaComplianceRate >= '95' ? '#52c41a' : '#ff4d4f' }} />
            </div>
            <div className="text-2xl font-bold mb-1" style={{ color: dashboard.summary.slaComplianceRate >= '95' ? '#52c41a' : '#ff4d4f' }}>
              {dashboard.summary.slaComplianceRate}%
            </div>
            <div className="text-gray-600 text-sm">SLA合规率</div>
          </Card>
        </Col>
      </Row>
    )
  }

  const renderTimeRangeSelector = () => (
    <Space>
      <Select
        value={timeRange.type}
        onChange={handleTimeRangeChange}
        style={{ width: 120 }}
      >
        <Select.Option value="last7days">最近7天</Select.Option>
        <Select.Option value="last30days">最近30天</Select.Option>
        <Select.Option value="lastQuarter">最近3个月</Select.Option>
        <Select.Option value="lastYear">最近1年</Select.Option>
        <Select.Option value="custom">自定义</Select.Option>
      </Select>
      
      {timeRange.type === 'custom' && (
        <DatePicker
          type="dateTimeRange"
          placeholder={['开始时间', '结束时间']}
          onChange={handleCustomTimeChange}
          style={{ width: 300 }}
        />
      )}
      
      <Button 
        icon={<IconRefresh />} 
        onClick={handleRefresh}
        theme="borderless"
      >
        刷新
      </Button>
      
      <Button 
        icon={<IconDownload />} 
        onClick={handleExportExcel}
        type="primary"
        theme="solid"
      >
        导出Excel
      </Button>
    </Space>
  )

  // ========== Skeleton 组件 ==========
  const renderEfficiencySkeleton = () => (
    <div className="space-y-6">
      {/* 关键指标卡片 Skeleton */}
      <Row gutter={16}>
        {[1, 2, 3, 4].map(i => (
          <Col span={6} key={i}>
            <Card className="text-center">
              <Skeleton.Avatar size="large" style={{ margin: '0 auto 16px' }} />
              <Skeleton.Title style={{ margin: '0 auto' }} />
              <Skeleton.Paragraph rows={1} style={{ margin: '8px auto 0' }} />
            </Card>
          </Col>
        ))}
      </Row>

      {/* 分布图表 Skeleton */}
      <Row gutter={16}>
        {[1, 2, 3].map(i => (
          <Col span={8} key={i}>
            <Card>
              <Skeleton.Title style={{ marginBottom: 16 }} />
              <Skeleton.Image style={{ width: '100%', height: '300px' }} />
            </Card>
          </Col>
        ))}
      </Row>

      {/* 工程师工作负载 Skeleton */}
      <Card title="工程师工作负载概览">
        <Row gutter={16}>
          {[1, 2, 3, 4, 5, 6].map(i => (
            <Col span={8} key={i}>
              <Card>
                <Skeleton.Title />
                <Skeleton.Paragraph rows={3} />
                <Skeleton.Button />
              </Card>
            </Col>
          ))}
        </Row>
      </Card>
    </div>
  )

  const renderSatisfactionSkeleton = () => (
    <div className="space-y-6">
      {/* 满意度概览 Skeleton */}
      <Row gutter={16}>
        <Col span={8}>
          <Card className="text-center">
            <Skeleton.Avatar size="large" style={{ margin: '0 auto 16px' }} />
            <Skeleton.Title style={{ margin: '0 auto' }} />
            <Skeleton.Paragraph rows={1} style={{ margin: '8px auto 0' }} />
          </Card>
        </Col>
        <Col span={8}>
          <Card className="text-center">
            <Skeleton.Title style={{ margin: '0 auto' }} />
            <Skeleton.Paragraph rows={1} style={{ margin: '8px auto 0' }} />
            <div className="mt-3">
              <Row gutter={4}>
                {[1, 2, 3, 4, 5].map(i => (
                  <Col span={4.8} key={i}>
                    <Skeleton.Paragraph rows={1} />
                  </Col>
                ))}
              </Row>
            </div>
          </Card>
        </Col>
        <Col span={8}>
          <Card className="text-center">
            <Skeleton.Title style={{ margin: '0 auto' }} />
            <Skeleton.Paragraph rows={1} style={{ margin: '8px auto 0' }} />
            <Skeleton.Button style={{ marginTop: 8 }} />
          </Card>
        </Col>
      </Row>

      {/* 图表分析 Skeleton */}
      <Row gutter={16}>
        <Col span={12}>
          <Card>
            <Skeleton.Title style={{ marginBottom: 16 }} />
            <Skeleton.Image style={{ width: '100%', height: '400px' }} />
          </Card>
        </Col>
        <Col span={12}>
          <Card>
            <Skeleton.Title style={{ marginBottom: 16 }} />
            <Skeleton.Image style={{ width: '100%', height: '400px' }} />
          </Card>
        </Col>
      </Row>
    </div>
  )

  const renderSLASkeleton = () => (
    <div className="space-y-6">
      {/* SLA 概览 Skeleton */}
      <Row gutter={16}>
        {[1, 2, 3, 4].map(i => (
          <Col span={6} key={i}>
            <Card className="text-center">
              <Skeleton.Avatar size="large" style={{ margin: '0 auto 16px' }} />
              <Skeleton.Title style={{ margin: '0 auto' }} />
              <Skeleton.Paragraph rows={1} style={{ margin: '8px auto 0' }} />
            </Card>
          </Col>
        ))}
      </Row>

      {/* 图表分析 Skeleton */}
      <Row gutter={16}>
        <Col span={12}>
          <Card>
            <Skeleton.Title style={{ marginBottom: 16 }} />
            <Skeleton.Image style={{ width: '100%', height: '400px' }} />
          </Card>
        </Col>
        <Col span={12}>
          <Card>
            <Skeleton.Title style={{ marginBottom: 16 }} />
            <Skeleton.Image style={{ width: '100%', height: '400px' }} />
          </Card>
        </Col>
      </Row>
    </div>
  )

  const renderTrendsSkeleton = () => (
    <div className="space-y-6">
      {/* 趋势图表 Skeleton */}
      <Card>
        <Skeleton.Title style={{ marginBottom: 16 }} />
        <Skeleton.Image style={{ width: '100%', height: '400px' }} />
      </Card>

      <Card>
        <Skeleton.Title style={{ marginBottom: 16 }} />
        <Skeleton.Image style={{ width: '100%', height: '400px' }} />
      </Card>

      {/* 类别趋势和积压分析 Skeleton */}
      <Row gutter={16}>
        <Col span={16}>
          <Card>
            <Skeleton.Title style={{ marginBottom: 16 }} />
            <Skeleton.Image style={{ width: '100%', height: '350px' }} />
          </Card>
        </Col>
        <Col span={8}>
          <Card title="积压工单分析">
            <div className="space-y-4">
              <div className="text-center p-4 bg-gray-50 rounded">
                <Skeleton.Title style={{ margin: '0 auto' }} />
                <Skeleton.Paragraph rows={1} style={{ margin: '8px auto 0' }} />
              </div>
              <Skeleton.Paragraph rows={2} />
              <Skeleton.Button />
            </div>
          </Card>
        </Col>
      </Row>
    </div>
  )

  return (
    <div className="p-6">
      {/* 页面标题和操作栏 */}
      <div className="flex justify-between items-center mb-6">
        <div>
          <Title heading={3} style={{ margin: 0 }}>
            📊 报告和数据分析
          </Title>
          <Text type="secondary" className="mt-1">
            运维效率分析、客户满意度统计、SLA合规性报告
          </Text>
        </div>
        
        {renderTimeRangeSelector()}
      </div>

      {/* 概览统计 */}
      <div className="mb-6">
        {renderOverviewStats()}
      </div>

      <div className="mb-6 bg-white p-4 rounded-lg">
        {/* 详细分析标签页 */}
        <Tabs 
          activeKey={activeTab} 
          onChange={(key) => {
            setActiveTab(key)
            // Tab 切换后延迟重新初始化图表
            setTimeout(() => {
              window.dispatchEvent(new Event('resize'))
            }, 300)
          }}
          size="large"
          type="card"
        >
          <TabPane 
            tab={
              <span>
                <IconActivity />
                运维效率分析
              </span>
            } 
            itemKey="efficiency"
          >
            <div className="space-y-6">
              <EfficiencyChart 
                data={efficiencyQuery.data} 
                loading={efficiencyQuery.isLoading}
                error={efficiencyQuery.error}
              />
              <EngineerWorkloadTable 
                data={efficiencyQuery.data?.engineerWorkload || []}
                loading={efficiencyQuery.isLoading}
              />
            </div>
          </TabPane>

          <TabPane 
            tab={
              <span>
                <IconStar />
                客户满意度
              </span>
            } 
            itemKey="satisfaction"
          >
            <SatisfactionChart 
              data={satisfactionQuery.data}
              loading={satisfactionQuery.isLoading}
              error={satisfactionQuery.error}
            />
          </TabPane>

          <TabPane 
            tab={
              <span>
                <IconShield />
                SLA合规性
                {slaQuery.data && slaQuery.data.slaViolations > 0 && (
                  <Badge 
                    count={slaQuery.data.slaViolations} 
                    type="danger" 
                    style={{ marginLeft: 8 }}
                  />
                )}
              </span>
            } 
            itemKey="sla"
          >
            <SLAComplianceChart 
              data={slaQuery.data}
              loading={slaQuery.isLoading}
              error={slaQuery.error}
            />
          </TabPane>

          <TabPane 
            tab={
              <span>
                <IconActivity />
                趋势分析
              </span>
            } 
            itemKey="trends"
          >
            <TrendAnalysisChart 
              data={trendsQuery.data}
              loading={trendsQuery.isLoading}
              error={trendsQuery.error}
            />
          </TabPane>
        </Tabs>
      </div>
    </div>
  )
}

export default ReportsPage