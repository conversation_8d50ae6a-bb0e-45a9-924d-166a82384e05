import React, { useState, useRef } from "react";
import {
  Card,
  Tabs,
  TabPane,
  Button,
  Table,
  Modal,
  Form,
  Input,
  Select,
  Switch,
  Space,
  Typography,
  Tag,
  Divider,
  Toast,
  Spin,
  Empty,
  Descriptions,
  Progress,
  Tooltip,
} from "@douyinfe/semi-ui";
import {
  IconPlus,
  IconEdit,
  IconDelete,
  IconRefresh,
  IconSetting,
  IconEyeOpened,
  IconCode,
  IconActivity,
  IconPieChartStroked,
} from "@douyinfe/semi-icons";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import {
  getAIConfigurations,
  createAIConfiguration,
  updateAIConfiguration,
  deleteAIConfiguration,
  getPromptTemplates,
  createPromptTemplate,
  updatePromptTemplate,
  deletePromptTemplate,
  getAnalysisHistory,
  getAIStatistics,
} from "@/services/aiManagement";
import { isArray } from "@visactor/vchart/esm/util";

const { Title, Text, Paragraph } = Typography;

// 类型定义
interface AIConfiguration {
  id: string;
  provider: "OPENAI" | "ANTHROPIC" | "GEMINI";
  model: string;
  maxTokens: number;
  temperature: number;
  isActive: boolean;
  createdAt?: string;
  updatedAt?: string;
}

interface PromptTemplate {
  id: string;
  name: string;
  type: "TICKET_ANALYSIS" | "CATEGORY_SUGGESTION" | "PRIORITY_ASSESSMENT";
  provider: "OPENAI" | "ANTHROPIC" | "GEMINI";
  content: string;
  isActive: boolean;
  createdAt?: string;
  updatedAt?: string;
}

interface AIAnalysisRequest {
  id: string;
  provider: string;
  model: string;
  inputData: string;
  outputData: string | null;
  status: "PENDING" | "COMPLETED" | "FAILED";
  processingTime?: number;
  errorMessage?: string;
  createdAt: string;
  updatedAt: string;
}

interface AIStatistics {
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  averageResponseTime: number;
  successRate: number;
  providerUsage: Record<string, { count: number; percentage: number }>;
  templateUsage: Record<string, { count: number; percentage: number }>;
  dailyStats: Array<{
    date: string;
    requests: number;
    successRate: number;
  }>;
}

// AI配置管理组件
const AIConfigurationTab = ({ isActive }: { isActive: boolean }) => {
  const configFormRef = useRef<any>();
  const [showConfigModal, setShowConfigModal] = useState(false);
  const [editingConfig, setEditingConfig] = useState<AIConfiguration | null>(null);
  const queryClient = useQueryClient();

  const { data: configurations, isLoading, refetch } = useQuery({
    queryKey: ["ai-configurations"],
    queryFn: () => getAIConfigurations(),
    enabled: isActive, // 只有当标签页激活时才加载数据
  });

  const createMutation = useMutation({
    mutationFn: createAIConfiguration,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["ai-configurations"] });
      Toast.success("AI配置已创建");
      setShowConfigModal(false);
    },
    onError: (error: any) => {
      Toast.error(error.message || "创建失败");
    },
  });

  const updateMutation = useMutation({
    mutationFn: ({ id, data }: { id: string; data: any }) => updateAIConfiguration(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["ai-configurations"] });
      Toast.success("AI配置已更新");
      setShowConfigModal(false);
    },
    onError: (error: any) => {
      Toast.error(error.message || "更新失败");
    },
  });

  const deleteMutation = useMutation({
    mutationFn: deleteAIConfiguration,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["ai-configurations"] });
      Toast.success("AI配置已删除");
    },
    onError: (error: any) => {
      Toast.error(error.message || "删除失败");
    },
  });

  const configColumns = [
    {
      title: "AI提供商",
      dataIndex: "provider",
      render: (provider: string) => {
        const providerMap: Record<string, { color: string; text: string }> = {
          OPENAI: { color: "green", text: "OpenAI" },
          ANTHROPIC: { color: "blue", text: "Anthropic" },
          GEMINI: { color: "orange", text: "Google Gemini" },
        };
        const config = providerMap[provider] || { color: "grey", text: provider };
        return <Tag color={config.color as any}>{config.text}</Tag>;
      },
    },
    {
      title: "模型",
      dataIndex: "model",
      render: (model: string) => <Text code>{model}</Text>,
    },
    {
      title: "状态",
      dataIndex: "isActive",
      render: (isActive: boolean) => (
        <Tag color={isActive ? "green" : "grey"}>{isActive ? "启用" : "禁用"}</Tag>
      ),
    },
    {
      title: "最大Token",
      dataIndex: "maxTokens",
      render: (tokens: number) => <Text type="secondary">{tokens?.toLocaleString()}</Text>,
    },
    {
      title: "温度",
      dataIndex: "temperature",
      render: (temp: number) => <Text type="secondary">{temp}</Text>,
    },
    {
      title: "操作",
      render: (_: any, record: AIConfiguration) => (
        <Space>
          <Button
            theme="borderless"
            type="primary"
            icon={<IconEdit />}
            onClick={() => handleEditConfig(record)}
          >
            编辑
          </Button>
          <Button
            theme="borderless"
            type="danger"
            icon={<IconDelete />}
            onClick={() => handleDeleteConfig(record.id)}
          >
            删除
          </Button>
        </Space>
      ),
    },
  ];

  const handleEditConfig = (config: AIConfiguration) => {
    setEditingConfig(config);
    configFormRef.current?.setFieldsValue(config);
    setShowConfigModal(true);
  };

  const handleDeleteConfig = (id: string) => {
    Modal.confirm({
      title: "确认删除",
      content: "确定要删除这个AI配置吗？",
      onOk: () => {
        deleteMutation.mutate(id);
      },
    });
  };

  const handleRefresh = () => {
    refetch();
  };

  if (!isActive) {
    return null; // 不激活时不渲染内容
  }

  return (
    <Spin spinning={isLoading}>
      <div>
        <div style={{ marginBottom: 16, display: "flex", justifyContent: "space-between", alignItems: "center" }}>
          <Title heading={4}>AI配置管理</Title>
          <Space>
            <Button icon={<IconRefresh />} onClick={handleRefresh} loading={isLoading}>
              刷新
            </Button>
            <Button
              theme="solid"
              type="primary"
              icon={<IconPlus />}
              onClick={() => {
                setEditingConfig(null);
                configFormRef.current?.reset();
                setShowConfigModal(true);
              }}
            >
              添加配置
            </Button>
          </Space>
        </div>

        <Card>
          <Table
            columns={configColumns}
            dataSource={Array.isArray(configurations?.data?.data) ? configurations.data.data : []}
            loading={isLoading}
            pagination={{
              pageSize: 10,
              total: (configurations?.data as any)?.pagination?.total || 0,
            }}
            empty={<Empty description="暂无AI配置" />}
          />
        </Card>

        {/* 配置编辑模态框 */}
        <Modal
          title={editingConfig ? "编辑AI配置" : "添加AI配置"}
          visible={showConfigModal}
          onCancel={() => setShowConfigModal(false)}
          onOk={() => configFormRef.current?.submit()}
          width={600}
          confirmLoading={createMutation.isPending || updateMutation.isPending}
        >
          <Form<AIConfiguration>
            getFormApi={(formApi) => (configFormRef.current = formApi)}
            labelPosition="left"
            labelWidth={120}
            onSubmit={(values) => {
              if (editingConfig) {
                updateMutation.mutate({ id: editingConfig.id, data: values });
              } else {
                createMutation.mutate(values as any);
              }
            }}
          >
            <Form.Select
              field="provider"
              label="AI提供商"
              placeholder="选择AI提供商"
              rules={[{ required: true, message: "请选择AI提供商" }]}
            >
              <Form.Select.Option value="OPENAI">OpenAI</Form.Select.Option>
              <Form.Select.Option value="ANTHROPIC">Anthropic</Form.Select.Option>
              <Form.Select.Option value="GEMINI">Google Gemini</Form.Select.Option>
            </Form.Select>

            <Form.Input
              field="model"
              label="模型名称"
              placeholder="如: gpt-4, claude-3-sonnet"
              rules={[{ required: true, message: "请输入模型名称" }]}
            />

            <Form.Input
              field="apiKey"
              label="API密钥"
              type="password"
              placeholder="输入API密钥"
              rules={[{ required: true, message: "请输入API密钥" }]}
            />

            <Form.InputNumber
              field="maxTokens"
              label="最大Token数"
              placeholder="如: 4000"
              min={100}
              max={100000}
              rules={[{ required: true, message: "请输入最大Token数" }]}
            />

            <Form.InputNumber
              field="temperature"
              label="温度"
              placeholder="0.0 - 2.0"
              min={0}
              max={2}
              step={0.1}
              rules={[{ required: true, message: "请输入温度值" }]}
            />

            <Form.Switch field="isActive" label="启用配置" />
          </Form>
        </Modal>
      </div>
    </Spin>
  );
};

// 提示词模板管理组件
const PromptTemplateTab = ({ isActive }: { isActive: boolean }) => {
  const templateFormRef = useRef<any>();
  const [showTemplateModal, setShowTemplateModal] = useState(false);
  const [editingTemplate, setEditingTemplate] = useState<PromptTemplate | null>(null);
  const queryClient = useQueryClient();

  const { data: templates, isLoading, refetch } = useQuery({
    queryKey: ["prompt-templates"],
    queryFn: () => getPromptTemplates(),
    enabled: isActive, // 只有当标签页激活时才加载数据
  });

  const createMutation = useMutation({
    mutationFn: createPromptTemplate,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["prompt-templates"] });
      Toast.success("提示词模板已创建");
      setShowTemplateModal(false);
    },
    onError: (error: any) => {
      Toast.error(error.message || "创建失败");
    },
  });

  const updateMutation = useMutation({
    mutationFn: ({ id, data }: { id: string; data: any }) => updatePromptTemplate(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["prompt-templates"] });
      Toast.success("提示词模板已更新");
      setShowTemplateModal(false);
    },
    onError: (error: any) => {
      Toast.error(error.message || "更新失败");
    },
  });

  const deleteMutation = useMutation({
    mutationFn: deletePromptTemplate,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["prompt-templates"] });
      Toast.success("提示词模板已删除");
    },
    onError: (error: any) => {
      Toast.error(error.message || "删除失败");
    },
  });

  const templateColumns = [
    {
      title: "模板名称",
      dataIndex: "name",
      render: (name: string) => <Text strong>{name}</Text>,
    },
    {
      title: "类型",
      dataIndex: "type",
      render: (type: string) => {
        const typeMap: Record<string, { color: string; text: string }> = {
          TICKET_ANALYSIS: { color: "blue", text: "工单分析" },
          CATEGORY_SUGGESTION: { color: "green", text: "分类建议" },
          PRIORITY_ASSESSMENT: { color: "orange", text: "优先级评估" },
        };
        const config = typeMap[type] || { color: "grey", text: type };
        return <Tag color={config.color as any}>{config.text}</Tag>;
      },
    },
    {
      title: "AI提供商",
      dataIndex: "provider",
      render: (provider: string) => <Text code>{provider}</Text>,
    },
    {
      title: "状态",
      dataIndex: "isActive",
      render: (isActive: boolean) => (
        <Tag color={isActive ? "green" : "grey"}>{isActive ? "启用" : "禁用"}</Tag>
      ),
    },
    {
      title: "更新时间",
      dataIndex: "updatedAt",
      render: (date: string) => new Date(date).toLocaleString(),
    },
    {
      title: "操作",
      render: (_: any, record: PromptTemplate) => (
        <Space>
          <Button
            theme="borderless"
            type="tertiary"
            icon={<IconEyeOpened />}
            onClick={() => handleViewTemplate(record)}
          >
            查看
          </Button>
          <Button
            theme="borderless"
            type="primary"
            icon={<IconEdit />}
            onClick={() => handleEditTemplate(record)}
          >
            编辑
          </Button>
          <Button
            theme="borderless"
            type="danger"
            icon={<IconDelete />}
            onClick={() => handleDeleteTemplate(record.id)}
          >
            删除
          </Button>
        </Space>
      ),
    },
  ];

  const handleViewTemplate = (template: PromptTemplate) => {
    Modal.info({
      title: `提示词模板: ${template.name}`,
      content: (
        <div>
          <Descriptions
            data={[
              { key: "类型", value: template.type },
              { key: "AI提供商", value: template.provider },
              { key: "状态", value: template.isActive ? "启用" : "禁用" },
            ]}
          />
          <Divider />
          <Text strong>模板内容:</Text>
          <div style={{ marginTop: 8, padding: 12, backgroundColor: "#f8f9fa", borderRadius: 4 }}>
            <Text code style={{ whiteSpace: "pre-wrap" }}>
              {template.content}
            </Text>
          </div>
        </div>
      ),
      width: 800,
    });
  };

  const handleEditTemplate = (template: PromptTemplate) => {
    setEditingTemplate(template);
    templateFormRef.current?.setFieldsValue(template);
    setShowTemplateModal(true);
  };

  const handleDeleteTemplate = (id: string) => {
    Modal.confirm({
      title: "确认删除",
      content: "确定要删除这个提示词模板吗？",
      onOk: () => {
        deleteMutation.mutate(id);
      },
    });
  };

  if (!isActive) {
    return null;
  }

  return (
    <Spin spinning={isLoading}>
      <div>
        <div style={{ marginBottom: 16, display: "flex", justifyContent: "space-between", alignItems: "center" }}>
          <Title heading={4}>提示词模板管理</Title>
          <Space>
            <Button icon={<IconRefresh />} onClick={() => refetch()} loading={isLoading}>
              刷新
            </Button>
            <Button
              theme="solid"
              type="primary"
              icon={<IconPlus />}
              onClick={() => {
                setEditingTemplate(null);
                templateFormRef.current?.reset();
                setShowTemplateModal(true);
              }}
            >
              添加模板
            </Button>
          </Space>
        </div>

        <Card>
          <Table
            columns={templateColumns}
            dataSource={Array.isArray(templates?.data?.data) ? templates.data.data : []}
            loading={isLoading}
            pagination={{
              pageSize: 10,
              total: (templates?.data as any)?.pagination?.total || 0,
            }}
            empty={<Empty description="暂无提示词模板" />}
          />
        </Card>

        {/* 模板编辑模态框 */}
        <Modal
          title={editingTemplate ? "编辑提示词模板" : "添加提示词模板"}
          visible={showTemplateModal}
          onCancel={() => setShowTemplateModal(false)}
          onOk={() => templateFormRef.current?.submit()}
          width={800}
          confirmLoading={createMutation.isPending || updateMutation.isPending}
        >
          <Form<PromptTemplate>
            getFormApi={(formApi) => (templateFormRef.current = formApi)}
            labelPosition="top"
            onSubmit={(values) => {
              if (editingTemplate) {
                updateMutation.mutate({ id: editingTemplate.id, data: values });
              } else {
                createMutation.mutate(values);
              }
            }}
          >
            <Form.Input
              field="name"
              label="模板名称"
              placeholder="输入模板名称"
              rules={[{ required: true, message: "请输入模板名称" }]}
            />

            <Form.Select
              field="type"
              label="模板类型"
              placeholder="选择模板类型"
              rules={[{ required: true, message: "请选择模板类型" }]}
            >
              <Form.Select.Option value="TICKET_ANALYSIS">工单分析</Form.Select.Option>
              <Form.Select.Option value="CATEGORY_SUGGESTION">分类建议</Form.Select.Option>
              <Form.Select.Option value="PRIORITY_ASSESSMENT">优先级评估</Form.Select.Option>
            </Form.Select>

            <Form.Select
              field="provider"
              label="AI提供商"
              placeholder="选择AI提供商"
              rules={[{ required: true, message: "请选择AI提供商" }]}
            >
              <Form.Select.Option value="OPENAI">OpenAI</Form.Select.Option>
              <Form.Select.Option value="ANTHROPIC">Anthropic</Form.Select.Option>
              <Form.Select.Option value="GEMINI">Google Gemini</Form.Select.Option>
            </Form.Select>

            <Form.TextArea
              field="content"
              label="模板内容"
              placeholder="输入提示词模板内容，可使用变量如 {{description}}, {{context}} 等"
              rows={8}
              rules={[{ required: true, message: "请输入模板内容" }]}
            />

            <Form.Switch field="isActive" label="启用模板" />
          </Form>
        </Modal>
      </div>
    </Spin>
  );
};

// 分析历史组件
const AnalysisHistoryTab = ({ isActive }: { isActive: boolean }) => {
  const { data: analysisHistory, isLoading, refetch } = useQuery({
    queryKey: ["analysis-history"],
    queryFn: () => getAnalysisHistory(),
    enabled: isActive, // 只有当标签页激活时才加载数据
  });

  const historyColumns = [
    {
      title: "请求ID",
      dataIndex: "id",
      render: (id: string) => <Text code>{id.slice(0, 8)}...</Text>,
    },
    {
      title: "工单描述",
      dataIndex: "inputData",
      render: (inputData: string) => {
        try {
          const description = JSON.parse(inputData)?.description || "";
          return (
            <Tooltip content={description}>
              <Text ellipsis style={{ maxWidth: 200 }}>
                {description}
              </Text>
            </Tooltip>
          );
        } catch {
          return <Text type="secondary">-</Text>;
        }
      },
    },
    {
      title: "AI提供商",
      dataIndex: "provider",
      render: (provider: string) => <Tag color="blue">{provider}</Tag>,
    },
    {
      title: "状态",
      dataIndex: "status",
      render: (status: string) => {
        const statusMap: Record<string, { color: string; text: string }> = {
          PENDING: { color: "light-blue", text: "处理中" },
          COMPLETED: { color: "grey", text: "已完成" },
          FAILED: { color: "red", text: "失败" },
        };
        const config = statusMap[status] || { color: "grey", text: status };
        return <Tag color={config.color as any}>{config.text}</Tag>;
      },
    },
    {
      title: "处理时间",
      dataIndex: "processingTime",
      render: (time: number) => (time ? `${time}ms` : "-"),
    },
    {
      title: "创建时间",
      dataIndex: "createdAt",
      render: (date: string) => new Date(date).toLocaleString(),
    },
    {
      title: "操作",
      render: (_: any, record: AIAnalysisRequest) => (
        <Space>
          <Button
            theme="borderless"
            type="primary"
            icon={<IconEyeOpened />}
            onClick={() => handleViewAnalysis(record)}
          >
            查看详情
          </Button>
        </Space>
      ),
    },
  ];

  const handleViewAnalysis = (record: AIAnalysisRequest) => {
    let inputData = {};
    let outputData = {};

    try {
      inputData = JSON.parse(record.inputData || "{}");
    } catch {
      inputData = { error: "无法解析输入数据" };
    }

    try {
      outputData = record.outputData ? JSON.parse(record.outputData) : { message: "无输出数据" };
    } catch {
      outputData = { error: "无法解析输出数据" };
    }

    Modal.info({
      title: `分析详情 - ${record.id}`,
      content: (
        <div>
          <Descriptions
            data={[
              { key: "请求ID", value: record.id },
              { key: "AI提供商", value: record.provider },
              { key: "模型", value: record.model },
              { key: "状态", value: record.status },
              { key: "处理时间", value: record.processingTime ? `${record.processingTime}ms` : "-" },
              { key: "创建时间", value: new Date(record.createdAt).toLocaleString() },
            ]}
          />
          <Divider />
          <div style={{ marginBottom: 16 }}>
            <Text strong>输入数据:</Text>
            <div style={{ marginTop: 8, padding: 12, backgroundColor: "#f8f9fa", borderRadius: 4 }}>
              <Text code style={{ whiteSpace: "pre-wrap" }}>
                {JSON.stringify(inputData, null, 2)}
              </Text>
            </div>
          </div>
          <div>
            <Text strong>输出结果:</Text>
            <div style={{ marginTop: 8, padding: 12, backgroundColor: "#f8f9fa", borderRadius: 4 }}>
              <Text code style={{ whiteSpace: "pre-wrap" }}>
                {JSON.stringify(outputData, null, 2)}
              </Text>
            </div>
          </div>
          {record.errorMessage && (
            <div style={{ marginTop: 16 }}>
              <Text strong style={{ color: "#f5222d" }}>错误信息:</Text>
              <div style={{ marginTop: 8, padding: 12, backgroundColor: "#fff2f0", borderRadius: 4, border: "1px solid #ffccc7" }}>
                <Text style={{ color: "#f5222d" }}>{record.errorMessage}</Text>
              </div>
            </div>
          )}
        </div>
      ),
      width: 800,
    });
  };

  if (!isActive) {
    return null;
  }

  return (
    <Spin spinning={isLoading}>
      <div>
        <div style={{ marginBottom: 16, display: "flex", justifyContent: "space-between", alignItems: "center" }}>
          <Title heading={4}>AI分析历史</Title>
          <Space>
            <Button icon={<IconRefresh />} onClick={() => refetch()} loading={isLoading}>
              刷新
            </Button>
          </Space>
        </div>

        <Card>
          <Table
            columns={historyColumns}
            dataSource={Array.isArray(analysisHistory?.data?.data) ? analysisHistory.data.data : []}
            loading={isLoading}
            pagination={{
              pageSize: 10,
              total: (analysisHistory?.data as any)?.pagination?.total || 0,
            }}
            empty={<Empty description="暂无分析历史" />}
          />
        </Card>
      </div>
    </Spin>
  );
};

// 统计报告组件
const StatisticsTab = ({ isActive }: { isActive: boolean }) => {
  const { data: stats, isLoading, refetch } = useQuery({
    queryKey: ["ai-statistics"],
    queryFn: () => getAIStatistics(),
    enabled: isActive, // 只有当标签页激活时才加载数据
  });

  const statsData = stats?.data || {
    totalRequests: 0,
    successfulRequests: 0,
    failedRequests: 0,
    averageResponseTime: 0,
    successRate: 0,
    providerUsage: {},
    templateUsage: {},
    dailyStats: [],
  };

  if (!isActive) {
    return null;
  }

  return (
    <Spin spinning={isLoading}>
      <div>
        <div style={{ marginBottom: 16, display: "flex", justifyContent: "space-between", alignItems: "center" }}>
          <Title heading={4}>AI使用统计</Title>
          <Space>
            <Button icon={<IconRefresh />} onClick={() => refetch()} loading={isLoading}>
              刷新
            </Button>
          </Space>
        </div>

        <div style={{ display: "grid", gridTemplateColumns: "repeat(auto-fit, minmax(300px, 1fr))", gap: 16, marginBottom: 24 }}>
          <Card title="总体统计" headerStyle={{ borderBottom: "1px solid #e8e8e8" }}>
            <Descriptions
              data={[
                { key: "总请求数", value: (statsData as any)?.totalRequests?.toLocaleString() || "0" },
                { key: "成功请求", value: (statsData as any)?.successfulRequests?.toLocaleString() || "0" },
                { key: "失败请求", value: (statsData as any)?.failedRequests?.toLocaleString() || "0" },
                {
                  key: "平均响应时间",
                  value: (statsData as any)?.averageResponseTime ? `${(statsData as any).averageResponseTime}ms` : "-",
                },
              ]}
            />
          </Card>

          <Card title="AI提供商使用情况" headerStyle={{ borderBottom: "1px solid #e8e8e8" }}>
            <div style={{ display: "flex", flexDirection: "column", gap: 12 }}>
              {Object.entries((statsData as any).providerUsage || {}).map(([provider, usage]: [string, any]) => (
                <div key={provider}>
                  <div style={{ display: "flex", justifyContent: "space-between", marginBottom: 4 }}>
                    <Text>{provider}</Text>
                    <Text type="secondary">{usage?.count || 0} 次</Text>
                  </div>
                  <Progress percent={usage?.percentage || 0} showInfo={false} size="small" />
                </div>
              ))}
            </div>
          </Card>

          <Card title="模板使用统计" headerStyle={{ borderBottom: "1px solid #e8e8e8" }}>
            <div style={{ display: "flex", flexDirection: "column", gap: 12 }}>
              {Object.entries((statsData as any).templateUsage || {}).map(([template, usage]: [string, any]) => (
                <div key={template}>
                  <div style={{ display: "flex", justifyContent: "space-between", marginBottom: 4 }}>
                    <Text ellipsis style={{ maxWidth: 150 }}>
                      {template}
                    </Text>
                    <Text type="secondary">{usage?.count || 0} 次</Text>
                  </div>
                  <Progress percent={usage?.percentage || 0} showInfo={false} size="small" />
                </div>
              ))}
            </div>
          </Card>

          <Card title="成功率统计" headerStyle={{ borderBottom: "1px solid #e8e8e8" }}>
            <div style={{ textAlign: "center" }}>
              <Progress
                type="circle"
                percent={(statsData as any).successRate || 0}
                format={(percent) => `${percent}%`}
                width={120}
              />
              <div style={{ marginTop: 12 }}>
                <Text type="secondary">整体成功率</Text>
              </div>
            </div>
          </Card>
        </div>

        <Card title="最近7天趋势" headerStyle={{ borderBottom: "1px solid #e8e8e8" }}>
          {(statsData as any).dailyStats && (statsData as any).dailyStats.length > 0 ? (
            <div style={{ display: "flex", flexDirection: "column", gap: 12 }}>
              {(statsData as any).dailyStats.map((stat: any, index: number) => (
                <div key={index} style={{ display: "flex", alignItems: "center", gap: 16 }}>
                  <Text style={{ minWidth: 100 }}>{stat.date}</Text>
                  <div style={{ flex: 1, display: "flex", alignItems: "center", gap: 8 }}>
                    <Text type="secondary" style={{ minWidth: 80 }}>
                      {stat.requests} 次请求
                    </Text>
                    <Progress
                      percent={stat.successRate}
                      showInfo={false}
                      size="small"
                      style={{ flex: 1 }}
                    />
                    <Text type="secondary" style={{ minWidth: 60 }}>
                      {stat.successRate}%
                    </Text>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div style={{ height: 200, display: "flex", alignItems: "center", justifyContent: "center" }}>
              <Empty description="暂无趋势数据" />
            </div>
          )}
        </Card>
      </div>
    </Spin>
  );
};

export default function AIManagementPage() {
  const [activeTab, setActiveTab] = useState("config");

  const handleTabChange = (tabKey: string) => {
    setActiveTab(tabKey);
  };

  return (
    <div style={{ padding: 24 }}>
      <div style={{ marginBottom: 24 }}>
        <Title heading={2}>AI集成管理</Title>
        <Paragraph type="secondary">
          管理AI配置、提示词模板、分析历史和系统统计
        </Paragraph>
      </div>

      <Tabs type="line" activeKey={activeTab} onChange={handleTabChange}>
        <TabPane
          tab={
            <span>
              <IconSetting style={{ marginRight: 4 }} />
              AI配置
            </span>
          }
          itemKey="config"
        >
          <AIConfigurationTab isActive={activeTab === "config"} />
        </TabPane>

        <TabPane
          tab={
            <span>
              <IconCode style={{ marginRight: 4 }} />
              提示词模板
            </span>
          }
          itemKey="templates"
        >
          <PromptTemplateTab isActive={activeTab === "templates"} />
        </TabPane>

        <TabPane
          tab={
            <span>
              <IconActivity style={{ marginRight: 4 }} />
              分析历史
            </span>
          }
          itemKey="history"
        >
          <AnalysisHistoryTab isActive={activeTab === "history"} />
        </TabPane>

        <TabPane
          tab={
            <span>
              <IconPieChartStroked style={{ marginRight: 4 }} />
              统计报告
            </span>
          }
          itemKey="stats"
        >
          <StatisticsTab isActive={activeTab === "stats"} />
        </TabPane>
      </Tabs>
    </div>
  );
}
