/**
 * API Key管理页面
 */

import React, { useState, useEffect } from "react";
import {
  Card,
  Button,
  Table,
  Tag,
  Space,
  Popconfirm,
  Toast,
  Input,
  Select,
  Typography,
  Dropdown,
  Modal,
} from "@douyinfe/semi-ui";
import {
  IconPlus,
  IconSearch,
  IconMore,
  IconEdit,
  IconDelete,
  IconRefresh,
  IconKey,
  IconCopy,
  IconBarChartHStroked,
} from "@douyinfe/semi-icons";
import dayjs from "dayjs";
import relativeTime from "dayjs/plugin/relativeTime";

import ApiKeyForm from "@/components/admin/ApiKeyForm";
import {
  getApiKeys,
  deleteApiKey,
  regenerateApiKey,
  getApiKeyStats,
  type ApiKey,
  type ApiKeyStats,
} from "@/services/api-key";

dayjs.extend(relativeTime);

const { Title } = Typography;

interface ApiKeyStatsModalProps {
  visible: boolean;
  apiKey: ApiKey | null;
  onClose: () => void;
}

const ApiKeyStatsModal: React.FC<ApiKeyStatsModalProps> = ({ visible, apiKey, onClose }) => {
  const [stats, setStats] = useState<ApiKeyStats | null>(null);
  const [loading, setLoading] = useState(false);

  const fetchStats = async () => {
    if (!apiKey) return;

    setLoading(true);
    try {
      const response = await getApiKeyStats(apiKey.id);
      if (response.success && response.data) {
        setStats(response.data);
      }
    } catch (error) {
      console.error("获取统计数据失败:", error);
      Toast.error("获取统计数据失败");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (visible && apiKey) {
      fetchStats();
    }
  }, [visible, apiKey]);

  return (
    <Modal
      title={`API Key 使用统计 - ${apiKey?.name}`}
      visible={visible}
      onCancel={onClose}
      footer={<Button onClick={onClose}>关闭</Button>}
      width={800}
    >
      <div className="space-y-4">
        {loading ? (
          <div className="text-center py-8">加载中...</div>
        ) : stats ? (
          <>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <Card className="text-center">
                <div className="text-2xl font-bold text-blue-600">{stats.totalUsage}</div>
                <div className="text-sm text-gray-500">总调用次数</div>
              </Card>
              <Card className="text-center">
                <div className="text-2xl font-bold text-green-600">{stats.totalServices}</div>
                <div className="text-sm text-gray-500">创建工单数</div>
              </Card>
              <Card className="text-center">
                <div className="text-2xl font-bold text-orange-600">{stats.recentUsageCount}</div>
                <div className="text-sm text-gray-500">近期使用次数</div>
              </Card>
              <Card className="text-center">
                <div className="text-2xl font-bold text-purple-600">
                  {stats.lastUsedAt ? dayjs(stats.lastUsedAt).fromNow() : "从未使用"}
                </div>
                <div className="text-sm text-gray-500">最后使用时间</div>
              </Card>
            </div>

            <Card title="每日使用量">
              {Object.keys(stats.dailyUsage).length > 0 ? (
                <div className="space-y-2">
                  {Object.entries(stats.dailyUsage)
                    .sort(([a], [b]) => a.localeCompare(b))
                    .map(([date, count]) => (
                      <div key={date} className="flex justify-between items-center">
                        <span className="text-sm text-gray-600">{date}</span>
                        <span className="text-sm font-medium">{count} 次</span>
                      </div>
                    ))}
                </div>
              ) : (
                <div className="text-center text-gray-500 py-4">
                  最近 {stats.recentDays} 天内无使用记录
                </div>
              )}
            </Card>
          </>
        ) : (
          <div className="text-center py-8 text-gray-500">暂无统计数据</div>
        )}
      </div>
    </Modal>
  );
};

const ApiKeyPage: React.FC = () => {
  const [apiKeys, setApiKeys] = useState<ApiKey[]>([]);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0,
  });

  // 搜索和过滤状态
  const [searchText, setSearchText] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("");

  // 弹窗状态
  const [formVisible, setFormVisible] = useState(false);
  const [statsVisible, setStatsVisible] = useState(false);
  const [editingApiKey, setEditingApiKey] = useState<ApiKey | null>(null);
  const [statsApiKey, setStatsApiKey] = useState<ApiKey | null>(null);

  // 状态标签配置
  const statusConfig = {
    ACTIVE: { color: "green", text: "活跃" },
    INACTIVE: { color: "orange", text: "禁用" },
    REVOKED: { color: "red", text: "撤销" },
    EXPIRED: { color: "grey", text: "过期" },
  } as const;

  // 获取API Key列表
  const fetchApiKeys = async () => {
    setLoading(true);
    try {
      const params = {
        page: pagination.current,
        limit: pagination.pageSize,
        ...(statusFilter && { status: statusFilter }),
        ...(searchText && { systemId: searchText }),
      };

      const response = await getApiKeys(params);
      if (response.success && response.data) {
        setApiKeys(response.data.apiKeys);
        setPagination(prev => ({
          ...prev,
          total: response.data.pagination.total,
        }));
      }
    } catch (error) {
      console.error("获取API Key列表失败:", error);
      Toast.error("获取API Key列表失败");
    } finally {
      setLoading(false);
    }
  };

  // 删除API Key
  const handleDelete = async (apiKey: ApiKey) => {
    try {
      await deleteApiKey(apiKey.id);
      Toast.success("API Key已撤销");
      fetchApiKeys();
    } catch (error: any) {
      console.error("撤销API Key失败:", error);
      Toast.error(error.message || "撤销API Key失败");
    }
  };

  // 重新生成API Key
  const handleRegenerate = async (apiKey: ApiKey) => {
    try {
      const response = (await regenerateApiKey(apiKey.id)) as any;
      if (response.success && response.data) {
        // 显示新的API Key
        Modal.info({
          title: "🔄 API Key重新生成成功",
          width: 650,
          content: (
            <div className="space-y-4">
              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <div className="text-sm text-green-800">
                  <strong>✅ 重新生成成功！</strong> 旧的API Key已失效，请使用新的密钥。
                </div>
              </div>

              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <div className="text-sm text-yellow-800">
                  <strong>⚠️ 安全提示：</strong>请立即复制并安全保存新的API
                  Key，出于安全考虑，它只会显示这一次。
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-3">
                  🔑 新的API Key
                </label>
                <div className="flex items-center space-x-2">
                  <Input
                    value={response.data.keyValue}
                    readOnly
                    className="flex-1 font-mono text-sm bg-gray-50"
                    style={{ fontSize: "13px", letterSpacing: "0.5px" }}
                    addonAfter={
                      <Button
                        theme="borderless"
                        icon={<IconCopy />}
                        onClick={() => {
                          navigator.clipboard
                            .writeText(response.data.keyValue)
                            .then(() => {
                              Toast.success("API Key已复制到剪贴板");
                            })
                            .catch(() => {
                              Toast.error("复制失败，请手动复制");
                            });
                        }}
                        title="复制API Key"
                      />
                    }
                  />
                </div>
                <div className="text-xs text-gray-500 mt-2">
                  格式：ak_[时间戳][随机字符][校验码] - 系统自动生成，确保唯一性和安全性
                </div>
              </div>

              <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                <div className="text-sm text-red-800">
                  <strong>🚨 重要：</strong>旧的API
                  Key已立即失效，使用旧密钥的所有外部调用将被拒绝。请及时更新您的外部系统配置。
                </div>
              </div>
            </div>
          ),
        });

        Toast.success("API Key重新生成成功");
        fetchApiKeys();
      }
    } catch (error: any) {
      console.error("重新生成API Key失败:", error);
      Toast.error(error.message || "重新生成API Key失败");
    }
  };

  // 表格列定义
  const columns = [
    {
      title: "API Key名称",
      dataIndex: "name",
      key: "name",
      render: (text: string, record: ApiKey) => (
        <div>
          <div className="font-medium">{text}</div>
          <div className="text-xs text-gray-500">系统ID: {record.systemId}</div>
        </div>
      ),
    },
    {
      title: "描述",
      dataIndex: "description",
      key: "description",
      ellipsis: true,
      render: (text: string) => text || "-",
    },
    {
      title: "状态",
      dataIndex: "status",
      key: "status",
      render: (status: keyof typeof statusConfig) => {
        const config = statusConfig[status];
        return <Tag color={config.color}>{config.text}</Tag>;
      },
    },
    {
      title: "使用统计",
      key: "usage",
      render: (_: any, record: ApiKey) => (
        <div className="text-sm">
          <div>调用次数: {record.usageCount}</div>
          <div className="text-gray-500">
            最后使用: {record.lastUsedAt ? dayjs(record.lastUsedAt).fromNow() : "从未使用"}
          </div>
        </div>
      ),
    },
    {
      title: "创建信息",
      key: "created",
      render: (_: any, record: ApiKey) => (
        <div className="text-sm">
          <div>创建者: {record.createdByUser?.fullName || record.createdByUser?.username}</div>
          <div className="text-gray-500">{dayjs(record.createdAt).format("YYYY-MM-DD HH:mm")}</div>
        </div>
      ),
    },
    {
      title: "过期时间",
      dataIndex: "expiresAt",
      key: "expiresAt",
      render: (expiresAt: string) => {
        if (!expiresAt) return "永不过期";

        const isExpired = dayjs(expiresAt).isBefore(dayjs());
        return (
          <div className={`text-sm ${isExpired ? "text-red-500" : ""}`}>
            {dayjs(expiresAt).format("YYYY-MM-DD HH:mm")}
            {isExpired && <div className="text-xs">已过期</div>}
          </div>
        );
      },
    },
    {
      title: "操作",
      key: "actions",
      fixed: "right" as const,
      width: 120,
      render: (_: any, record: ApiKey) => (
        <Dropdown
          trigger="click"
          position="bottomRight"
          render={
            <Dropdown.Menu>
              <Dropdown.Item
                icon={<IconEdit />}
                onClick={() => {
                  setEditingApiKey(record);
                  setFormVisible(true);
                }}
              >
                编辑
              </Dropdown.Item>

              <Dropdown.Item
                icon={<IconBarChartHStroked />}
                onClick={() => {
                  setStatsApiKey(record);
                  setStatsVisible(true);
                }}
              >
                使用统计
              </Dropdown.Item>

              <Dropdown.Item
                icon={<IconRefresh />}
                onClick={() => handleRegenerate(record)}
                disabled={record.status !== "ACTIVE"}
              >
                重新生成
              </Dropdown.Item>

              <Dropdown.Divider />

              <Dropdown.Item
                icon={<IconDelete />}
                type="danger"
                onClick={() => {
                  Modal.confirm({
                    title: "确认撤销API Key？",
                    content: `撤销后，API Key "${record.name}" 将无法继续使用，该操作不可逆。`,
                    onOk: () => handleDelete(record),
                  });
                }}
                disabled={record.status === "REVOKED"}
              >
                撤销
              </Dropdown.Item>
            </Dropdown.Menu>
          }
        >
          <Button theme="borderless" icon={<IconMore />} />
        </Dropdown>
      ),
    },
  ];

  // 初始化加载
  useEffect(() => {
    fetchApiKeys();
  }, [pagination.current, pagination.pageSize]);

  return (
    <div className="p-6 space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <Title heading={3} className="mb-2">
            API Key 管理
          </Title>
          <p className="text-gray-600">管理外部系统调用的API密钥，确保系统安全访问</p>
        </div>

        <Button
          theme="solid"
          type="primary"
          icon={<IconPlus />}
          onClick={() => {
            setEditingApiKey(null);
            setFormVisible(true);
          }}
        >
          创建API Key
        </Button>
      </div>

      {/* 搜索和过滤 */}
      <Card>
        <div className="flex items-center space-x-4">
          <Input
            placeholder="搜索系统ID..."
            value={searchText}
            onChange={setSearchText}
            prefix={<IconSearch />}
            showClear
            style={{ width: 240 }}
          />

          <Select
            placeholder="筛选状态"
            value={statusFilter}
            onChange={value => setStatusFilter(value as string)}
            style={{ width: 120 }}
          >
            <Select.Option value="ACTIVE">活跃</Select.Option>
            <Select.Option value="INACTIVE">禁用</Select.Option>
            <Select.Option value="REVOKED">撤销</Select.Option>
            <Select.Option value="EXPIRED">过期</Select.Option>
          </Select>

          <Button icon={<IconSearch />} onClick={fetchApiKeys}>
            搜索
          </Button>
        </div>
      </Card>

      {/* API Key表格 */}
      <Card>
        <Table
          columns={columns}
          dataSource={apiKeys}
          rowKey="id"
          loading={loading}
          pagination={{
            currentPage: pagination.current,
            pageSize: pagination.pageSize,
            total: pagination.total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: true,
            onPageChange: (page: number) => {
              setPagination(prev => ({
                ...prev,
                current: page,
              }));
            },
            onPageSizeChange: (pageSize: number) => {
              setPagination(prev => ({
                ...prev,
                current: 1,
                pageSize,
              }));
            },
          }}
        />
      </Card>

      {/* API Key表单弹窗 */}
      <ApiKeyForm
        visible={formVisible}
        apiKey={editingApiKey}
        onSuccess={() => {
          setFormVisible(false);
          setEditingApiKey(null);
          fetchApiKeys();
        }}
        onCancel={() => {
          setFormVisible(false);
          setEditingApiKey(null);
        }}
      />

      {/* 使用统计弹窗 */}
      <ApiKeyStatsModal
        visible={statsVisible}
        apiKey={statsApiKey}
        onClose={() => {
          setStatsVisible(false);
          setStatsApiKey(null);
        }}
      />
    </div>
  );
};

export default ApiKeyPage;
