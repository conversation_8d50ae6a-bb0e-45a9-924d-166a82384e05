import React, { useEffect, useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useGlobalWebSocket } from "@/contexts/WebSocketContext";
import { ConnectionStatus } from "@/composables/useWebSocket";
import { monitorService } from "@/services/monitor";
import {
  Card,
  Row,
  Col,
  Progress,
  Typography,
  Table,
  Tag,
  Button,
  Modal,
  List,
  Spin,
  Empty,
  Space,
  Tooltip,
  Switch,
  Select,
  Skeleton,
  Tabs,
  TabPane,
  Badge,
  Divider,
  Form,
  InputNumber,
  Input,
  Toast,
  Popover,
  Timeline,
  Descriptions,
} from "@douyinfe/semi-ui";
import {
  IconActivity,
  IconServer,
  IconCloud,
  IconBell,
  IconRefresh,
  IconSetting,
  IconEyeOpened,
  IconAlertTriangle,
  IconTickCircle,
  IconClock,
  IconBarChartHStroked as IconChart,
  IconWifi,
  IconArchive,
  IconDesktop,
  IconDownload,
  IconPlay,
  IconArrowUp,
  IconShield,
  IconBulb,
  IconCalendar,
  IconFile,
} from "@douyinfe/semi-icons";
import { useUIStore } from "@/stores";
import { DiskUsageDonut } from "@/components/dashboard/DiskUsageDonut";
import { IntelligentAnalysisCard, IntelligentRecommendationsCard } from "@/components/monitor/IntelligentAnalysisCard";
import ResourceAlertsCard from "@/components/monitor/ResourceAlertsCard";
import {
  IllustrationConstruction,
  IllustrationConstructionDark,
} from "@douyinfe/semi-illustrations";

const { Title, Text } = Typography;

interface ThresholdFormData {
  metric: string;
  warning: number;
  critical: number;
  duration: number;
}

const EnhancedMonitorSkeleton = () => (
  <div className="space-y-6">
    <div className="ops-page-header">
      <div className="flex justify-between items-start flex-wrap gap-4">
        <div>
          <Skeleton.Title style={{ width: 200, marginBottom: 16 }} />
          <Skeleton.Paragraph rows={1} style={{ width: 400 }} />
        </div>
        <div className="flex items-center space-x-3">
          <Skeleton.Button style={{ width: 100 }} />
          <Skeleton.Button style={{ width: 100 }} />
          <Skeleton.Button style={{ width: 100 }} />
        </div>
      </div>
    </div>

    <Row gutter={[24, 24]}>
      {[1, 2, 3, 4].map(i => (
        <Col span={24} lg={6} key={i}>
          <div className="ops-card">
            <div className="ops-card-body">
              <Skeleton.Avatar size="large" />
              <Skeleton.Title style={{ width: 120, marginTop: 16 }} />
              <Skeleton.Paragraph rows={2} />
            </div>
          </div>
        </Col>
      ))}
    </Row>

    <Row gutter={[24, 24]}>
      <Col span={24} lg={12}>
        <div className="ops-card">
          <div className="ops-card-header">
            <Skeleton.Title style={{ width: 120 }} />
          </div>
          <div className="ops-card-body">
            <Skeleton.Paragraph rows={6} />
          </div>
        </div>
      </Col>
      <Col span={24} lg={12}>
        <div className="ops-card">
          <div className="ops-card-header">
            <Skeleton.Title style={{ width: 120 }} />
          </div>
          <div className="ops-card-body">
            <Skeleton.Paragraph rows={6} />
          </div>
        </div>
      </Col>
    </Row>
  </div>
);

export default function EnhancedMonitorPage() {
  const { setBreadcrumbs } = useUIStore();
  const queryClient = useQueryClient();
  const [refreshInterval, setRefreshInterval] = useState(30);
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [selectedLogLevel, setSelectedLogLevel] = useState<string>("");
  const [isThresholdModalVisible, setIsThresholdModalVisible] = useState(false);
  const [isDiagnosticsModalVisible, setIsDiagnosticsModalVisible] = useState(false);
  const [isConfigModalVisible, setIsConfigModalVisible] = useState(false);
  const [thresholdFormData, setThresholdFormData] = useState<ThresholdFormData>({
    metric: '',
    warning: 0,
    critical: 0,
    duration: 0
  });

  // 使用全局WebSocket连接
  const webSocket = useGlobalWebSocket();
  const wsState = webSocket.state;

  useEffect(() => {
    setBreadcrumbs([
      { text: "首页", href: "/", icon: "🏠" },
      { text: "系统管理", icon: "⚙️" },
      { text: "增强监控", icon: "📊" },
    ]);
  }, [setBreadcrumbs]);

  // 获取监控仪表板数据
  const {
    data: dashboardData,
    isLoading: isDashboardLoading,
    refetch: refetchDashboard,
  } = useQuery({
    queryKey: ["enhanced-monitor-dashboard"],
    queryFn: async () => {
      const response = await monitorService.getDashboard('full');
      return response.data;
    },
    refetchInterval:
      autoRefresh && wsState.status !== ConnectionStatus.CONNECTED ? refreshInterval * 1000 : false,
    retry: 1,
    retryDelay: 1000,
  });

  // 获取智能分析
  const { data: intelligentAnalysis, isLoading: isAnalysisLoading, error: analysisError } = useQuery({
    queryKey: ["intelligent-analysis"],
    queryFn: async () => {
      const response = await monitorService.getIntelligentAnalysis();
      return response.data;
    },
    refetchInterval:
      autoRefresh && wsState.status !== ConnectionStatus.CONNECTED ? refreshInterval * 1000 : false,
    retry: 2,
    retryDelay: 1000,
  });

  // 获取性能基准
  const { data: performanceBenchmarks, isLoading: isBenchmarksLoading } = useQuery({
    queryKey: ["performance-benchmarks"],
    queryFn: async () => {
      const response = await monitorService.getPerformanceBenchmarks();
      return response.data;
    },
    refetchInterval:
      autoRefresh && wsState.status !== ConnectionStatus.CONNECTED ? refreshInterval * 1000 : false,
  });

  // 获取资源预警
  const { data: resourceAlerts, isLoading: isAlertsLoading } = useQuery({
    queryKey: ["resource-alerts"],
    queryFn: async () => {
      const response = await monitorService.getResourceAlerts();
      return response.data;
    },
    refetchInterval:
      autoRefresh && wsState.status !== ConnectionStatus.CONNECTED ? refreshInterval * 1000 : false,
  });

  // 获取可用性统计
  const { data: availabilityStats, isLoading: isAvailabilityLoading } = useQuery({
    queryKey: ["availability-stats"],
    queryFn: async () => {
      const response = await monitorService.getAvailabilityStats(7);
      return response.data;
    },
    refetchInterval:
      autoRefresh && wsState.status !== ConnectionStatus.CONNECTED ? refreshInterval * 1000 : false,
  });

  // 获取系统事件
  const { data: systemEvents, isLoading: isEventsLoading } = useQuery({
    queryKey: ["system-events", selectedLogLevel],
    queryFn: async () => {
      const response = await monitorService.getSystemEvents(1, 20, selectedLogLevel);
      return response.data;
    },
    refetchInterval:
      autoRefresh && wsState.status !== ConnectionStatus.CONNECTED ? refreshInterval * 1000 : false,
  });

  // 获取监控配置
  const { data: monitorConfig, isLoading: isConfigLoading } = useQuery({
    queryKey: ["monitor-config"],
    queryFn: async () => {
      const response = await monitorService.getMonitoringConfig();
      return response.data;
    },
  });

  // 运行系统诊断
  const diagnosticsMutation = useMutation({
    mutationFn: () => monitorService.runSystemDiagnostics(),
    onSuccess: () => {
      Toast.success("系统诊断完成");
      queryClient.invalidateQueries({ queryKey: ["enhanced-monitor-dashboard"] });
    },
    onError: () => {
      Toast.error("系统诊断失败");
    },
  });

  // 更新阈值
  const updateThresholdMutation = useMutation({
    mutationFn: (data: ThresholdFormData) => monitorService.updateThreshold(data),
    onSuccess: () => {
      Toast.success("阈值更新成功");
      setIsThresholdModalVisible(false);
      setThresholdFormData({ metric: '', warning: 0, critical: 0, duration: 0 });
      queryClient.invalidateQueries({ queryKey: ["enhanced-monitor-dashboard"] });
    },
    onError: () => {
      Toast.error("阈值更新失败");
    },
  });

  // 生成报告
  const generateReportMutation = useMutation({
    mutationFn: (params: { type: string; format: string }) => 
      monitorService.generateReport(params.type, params.format),
    onSuccess: (data) => {
      Toast.success("报告生成成功");
      // 这里可以处理下载逻辑
      console.log("Report data:", data);
    },
    onError: () => {
      Toast.error("报告生成失败");
    },
  });

  const handleRefreshAll = () => {
    refetchDashboard();
    queryClient.invalidateQueries({ queryKey: ["intelligent-analysis"] });
    queryClient.invalidateQueries({ queryKey: ["performance-benchmarks"] });
    queryClient.invalidateQueries({ queryKey: ["resource-alerts"] });
    queryClient.invalidateQueries({ queryKey: ["availability-stats"] });
    queryClient.invalidateQueries({ queryKey: ["system-events"] });
  };

  const handleRunDiagnostics = () => {
    setIsDiagnosticsModalVisible(true);
    diagnosticsMutation.mutate();
  };

  const handleUpdateThreshold = (values: ThresholdFormData) => {
    updateThresholdMutation.mutate(values);
  };

  const handleGenerateReport = (type: string, format: string) => {
    generateReportMutation.mutate({ type, format });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "healthy":
        return "green";
      case "warning":
        return "orange";
      case "critical":
      case "down":
        return "red";
      default:
        return "grey";
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case "healthy":
        return "正常";
      case "warning":
        return "警告";
      case "critical":
        return "严重";
      case "down":
        return "离线";
      default:
        return "未知";
    }
  };

  if (isDashboardLoading && !dashboardData) {
    return <EnhancedMonitorSkeleton />;
  }

  return (
    <div className="space-y-6">
      <div className="ops-page-header">
        <div className="flex justify-between items-start flex-wrap gap-4">
          <div>
            <h1 className="ops-page-title">增强监控管理</h1>
            <p className="ops-page-subtitle">
              全面的系统监控、智能分析、性能基准和预警管理
            </p>
          </div>
          <div className="flex items-center space-x-3">
            {/* WebSocket连接状态 */}
            <div
              className={`ops-realtime-status ${
                wsState.status === ConnectionStatus.CONNECTED
                  ? "connected"
                  : wsState.status === ConnectionStatus.CONNECTING
                    ? "connecting"
                    : "disconnected"
              }`}
            >
              <div className="ops-realtime-dot"></div>
              <span>
                {wsState.status === ConnectionStatus.CONNECTED
                  ? "实时连接"
                  : wsState.status === ConnectionStatus.CONNECTING
                    ? "连接中..."
                    : "连接断开"}
              </span>
            </div>
            <div className="flex items-center space-x-2">
              <Text>自动刷新</Text>
              <Switch checked={autoRefresh} onChange={setAutoRefresh} />
            </div>
            <Select
              value={refreshInterval}
              onChange={value => setRefreshInterval(value as number)}
              style={{ width: 100 }}
            >
              <Select.Option value={10}>10秒</Select.Option>
              <Select.Option value={30}>30秒</Select.Option>
              <Select.Option value={60}>1分钟</Select.Option>
              <Select.Option value={300}>5分钟</Select.Option>
            </Select>
            <Button icon={<IconRefresh />} onClick={handleRefreshAll}>
              刷新全部
            </Button>
            <Button 
              icon={<IconPlay />} 
              onClick={handleRunDiagnostics}
              loading={diagnosticsMutation.isPending}
            >
              系统诊断
            </Button>
            <Button 
              icon={<IconSetting />} 
              onClick={() => setIsThresholdModalVisible(true)}
            >
              阈值设置
            </Button>
          </div>
        </div>
      </div>

      {/* 系统整体状态 */}
      <Card>
        <div className="flex justify-between items-center">
          <div className="flex items-center space-x-4">
            <Badge
              dot
              type={dashboardData?.summary?.overall === "healthy" ? "success" :
                    dashboardData?.summary?.overall === "warning" ? "warning" : "danger"}
            >
              <IconServer size="large" />
            </Badge>
            <Title heading={5} className="m-0">
              系统整体状态:{" "}
              <Text
                className={`font-semibold ${
                  dashboardData?.summary?.overall === "healthy"
                    ? "text-green-600"
                    : dashboardData?.summary?.overall === "warning"
                      ? "text-yellow-600"
                      : "text-red-600"
                }`}
              >
                {getStatusText(dashboardData?.summary?.overall || "healthy")}
              </Text>
            </Title>
          </div>
          <div className="flex items-center space-x-4">
            <Text type="secondary">
              运行时间: {dashboardData?.summary?.uptime || "N/A"}
            </Text>
            <Text type="secondary">
              服务数: {dashboardData?.summary?.totalServices || 0} /
              健康: {dashboardData?.summary?.healthyServices || 0}
            </Text>
            <Text type="secondary">
              严重告警: {dashboardData?.summary?.criticalAlerts || 0}
            </Text>
            <Text type="secondary">最后更新: {new Date().toLocaleString()}</Text>
          </div>
        </div>
      </Card>

      {/* 快速概览卡片 */}
      <Row gutter={[24, 24]}>
        <Col span={24} lg={6}>
          <Card className="ops-metric-card">
            <div className="flex items-center justify-between">
              <div>
                <Text type="secondary">智能分析</Text>
                <Title heading={4} className="m-0 mt-2">
                  {intelligentAnalysis?.score || 0}
                </Title>
                <Text size="small" type="secondary">
                  系统健康评分
                </Text>
              </div>
              <div className="ops-metric-icon">
                <IconArrowUp size="extra-large" style={{ color: "var(--semi-color-primary)" }} />
              </div>
            </div>
            {intelligentAnalysis?.trend && (
              <div className="mt-3">
                <Text size="small" type={intelligentAnalysis.trend === 'up' ? 'success' : 'danger'}>
                  {intelligentAnalysis.trend === 'up' ? '↗ 趋势良好' : '↘ 需要关注'}
                </Text>
              </div>
            )}
          </Card>
        </Col>

        <Col span={24} lg={6}>
          <Card className="ops-metric-card">
            <div className="flex items-center justify-between">
              <div>
                <Text type="secondary">性能基准</Text>
                <Title heading={4} className="m-0 mt-2">
                  {performanceBenchmarks?.overall || "N/A"}
                </Title>
                <Text size="small" type="secondary">
                  相对基准性能
                </Text>
              </div>
              <div className="ops-metric-icon">
                <IconBulb size="extra-large" style={{ color: "var(--semi-color-success)" }} />
              </div>
            </div>
            {performanceBenchmarks?.comparison && (
              <div className="mt-3">
                <Text size="small" type="secondary">
                  对比上周: {performanceBenchmarks.comparison}
                </Text>
              </div>
            )}
          </Card>
        </Col>

        <Col span={24} lg={6}>
          <Card className="ops-metric-card">
            <div className="flex items-center justify-between">
              <div>
                <Text type="secondary">资源预警</Text>
                <Title heading={4} className="m-0 mt-2">
                  {resourceAlerts?.active || 0}
                </Title>
                <Text size="small" type="secondary">
                  活跃预警数量
                </Text>
              </div>
              <div className="ops-metric-icon">
                <IconShield size="extra-large" style={{ color: "var(--semi-color-warning)" }} />
              </div>
            </div>
            {resourceAlerts?.severity && (
              <div className="mt-3">
                <Text size="small" type="warning">
                  高危: {resourceAlerts.severity.high || 0}
                </Text>
              </div>
            )}
          </Card>
        </Col>

        <Col span={24} lg={6}>
          <Card className="ops-metric-card">
            <div className="flex items-center justify-between">
              <div>
                <Text type="secondary">可用性</Text>
                <Title heading={4} className="m-0 mt-2">
                  {availabilityStats?.overall || "99.9%"}
                </Title>
                <Text size="small" type="secondary">
                  7天平均可用性
                </Text>
              </div>
              <div className="ops-metric-icon">
                <IconTickCircle size="extra-large" style={{ color: "var(--semi-color-success)" }} />
              </div>
            </div>
            {availabilityStats?.sla && (
              <div className="mt-3">
                <Text size="small" type="success">
                  SLA达标: {availabilityStats.sla}
                </Text>
              </div>
            )}
          </Card>
        </Col>
      </Row>

      {/* 主要内容区域 */}
      <Tabs type="card" defaultActiveKey="overview">
        <TabPane tab="系统概览" itemKey="overview">
          <Row gutter={[24, 24]}>
            {/* 系统组件状态 */}
            <Col span={24} lg={8}>
              <Card title="组件状态" style={{ minHeight: 400 }} loading={isDashboardLoading}>
                <Table
                  dataSource={dashboardData?.services?.map((service: any, index: number) => ({
                    key: index,
                    name: service.name,
                    status: service.status,
                    responseTime: service.responseTime || 0,
                    uptime: service.uptime || "N/A",
                  })) || []}
                  pagination={false}
                  columns={[
                    {
                      title: "组件",
                      dataIndex: "name",
                    },
                    {
                      title: "状态",
                      dataIndex: "status",
                      render: (status: string) => (
                        <Tag color={getStatusColor(status)}>{getStatusText(status)}</Tag>
                      ),
                    },
                    {
                      title: "响应时间",
                      dataIndex: "responseTime",
                      render: (time: number) => `${time}ms`,
                    },
                    {
                      title: "运行时间",
                      dataIndex: "uptime",
                    },
                  ]}
                />
              </Card>
            </Col>

            {/* 资源使用率 */}
            <Col span={24} lg={8}>
              <Card title="资源使用率" style={{ minHeight: 400 }}>
                <div className="space-y-4">
                  <div className="flex items-center">
                    <Text className="w-20">CPU</Text>
                    <div className="flex-1">
                      <Progress
                        percent={dashboardData?.metrics?.current?.cpu || 0}
                        stroke={(dashboardData?.metrics?.current?.cpu || 0) > 80 ? "red" :
                               (dashboardData?.metrics?.current?.cpu || 0) > 60 ? "orange" : "green"}
                        showInfo
                        format={p => `${p}%`}
                      />
                    </div>
                  </div>
                  <div className="flex items-center">
                    <Text className="w-20">内存</Text>
                    <div className="flex-1">
                      <Progress
                        percent={dashboardData?.metrics?.current?.memory || 0}
                        stroke={(dashboardData?.metrics?.current?.memory || 0) > 80 ? "red" :
                               (dashboardData?.metrics?.current?.memory || 0) > 60 ? "orange" : "green"}
                        showInfo
                        format={p => `${p}%`}
                      />
                    </div>
                  </div>
                  <div className="flex items-center">
                    <Text className="w-20">磁盘</Text>
                    <div className="flex-1 flex items-center">
                      <DiskUsageDonut percent={dashboardData?.metrics?.current?.disk || 0} />
                    </div>
                  </div>
                  <div className="flex items-center">
                    <Text className="w-20">网络</Text>
                    <div className="flex-1">
                      <Progress
                        percent={dashboardData?.metrics?.current?.network || 0}
                        stroke={(dashboardData?.metrics?.current?.network || 0) > 80 ? "red" :
                               (dashboardData?.metrics?.current?.network || 0) > 60 ? "orange" : "green"}
                        showInfo
                        format={p => `${p}%`}
                      />
                    </div>
                  </div>
                </div>
              </Card>
            </Col>

            {/* 性能指标 */}
            <Col span={24} lg={8}>
              <Card title="性能指标" style={{ minHeight: 400 }}>
                <List
                  dataSource={[
                    {
                      label: "平均响应时间",
                      value: `${dashboardData?.performance?.responseTime?.avg || 0}ms`,
                      details: `P95: ${dashboardData?.performance?.responseTime?.p95 || 0}ms`,
                    },
                    {
                      label: "当前吞吐量",
                      value: `${dashboardData?.performance?.throughput?.current || 0}/s`,
                      details: `峰值: ${dashboardData?.performance?.throughput?.peak || 0}/s`,
                    },
                    {
                      label: "错误率",
                      value: `${dashboardData?.performance?.errorRate || 0}%`,
                      details: "过去1小时",
                    },
                    {
                      label: "数据库连接",
                      value: dashboardData?.services?.find((s: any) =>
                        s.name.includes("MySQL") || s.name.includes("Database")
                      )?.connections?.active || 0,
                      details: `总计: ${dashboardData?.services?.find((s: any) =>
                        s.name.includes("MySQL") || s.name.includes("Database")
                      )?.connections?.total || 0}`,
                    },
                    {
                      label: "缓存命中率",
                      value: `${dashboardData?.services?.find((s: any) =>
                        s.name.includes("Redis") || s.name.includes("Cache")
                      )?.stats?.hitRate || 0}%`,
                      details: `未命中: ${dashboardData?.services?.find((s: any) =>
                        s.name.includes("Redis") || s.name.includes("Cache")
                      )?.stats?.misses || 0}`,
                    },
                  ]}
                  renderItem={item => (
                    <List.Item>
                      <div className="flex justify-between w-full">
                        <Text>{item.label}</Text>
                        <div>
                          <Text strong>{item.value}</Text>
                          <Text type="secondary" className="ml-2">
                            {item.details}
                          </Text>
                        </div>
                      </div>
                    </List.Item>
                  )}
                />
              </Card>
            </Col>
          </Row>
        </TabPane>

        <TabPane tab="智能分析" itemKey="analysis">
          <Row gutter={[24, 24]}>
            <Col span={24} lg={12}>
              <IntelligentAnalysisCard
                data={intelligentAnalysis}
                isLoading={isAnalysisLoading}
                error={analysisError}
              />
            </Col>

            <Col span={24} lg={12}>
              <IntelligentRecommendationsCard
                data={intelligentAnalysis}
                isLoading={isAnalysisLoading}
                error={analysisError}
              />
            </Col>
          </Row>
        </TabPane>

        <TabPane tab="资源预警" itemKey="alerts">
          <Row gutter={[24, 24]}>
            <Col span={24}>
              <ResourceAlertsCard
                data={resourceAlerts}
                isLoading={isAlertsLoading}
                error={null}
                onConfigureThresholds={() => setIsThresholdModalVisible(true)}
              />
            </Col>
          </Row>
        </TabPane>

        <TabPane tab="可用性统计" itemKey="availability">
          <Spin spinning={isAvailabilityLoading}>
            <Row gutter={[24, 24]}>
              <Col span={24} lg={12}>
                <Card title="服务可用性" style={{ minHeight: 300 }}>
                  {availabilityStats?.services ? (
                    <List
                      dataSource={availabilityStats.services}
                      renderItem={(service: any) => (
                        <List.Item>
                          <div className="flex justify-between w-full items-center">
                            <div>
                              <Text strong>{service.name}</Text>
                              <div>
                                <Text type="secondary" size="small">
                                  {service.description}
                                </Text>
                              </div>
                            </div>
                            <div className="text-right">
                              <Text strong className={
                                service.availability >= 99.9 ? "text-green-600" :
                                service.availability >= 99.0 ? "text-yellow-600" : "text-red-600"
                              }>
                                {service.availability}%
                              </Text>
                              <div>
                                <Text type="secondary" size="small">
                                  停机: {service.downtime}
                                </Text>
                              </div>
                            </div>
                          </div>
                        </List.Item>
                      )}
                    />
                  ) : (
                    <Empty
                      image={<IllustrationConstruction style={{ width: 150, height: 150 }} />}
                      title="暂无可用性数据"
                      description="正在收集服务可用性数据"
                    />
                  )}
                </Card>
              </Col>

              <Col span={24} lg={12}>
                <Card title="SLA 达标情况" style={{ minHeight: 300 }}>
                  {availabilityStats?.sla ? (
                    <div className="space-y-4">
                      <div className="text-center">
                        <Title heading={3} className="m-0">
                          {availabilityStats.sla.overall}%
                        </Title>
                        <Text type="secondary">整体SLA达标率</Text>
                      </div>
                      <Divider />
                      <div className="space-y-3">
                        {availabilityStats.sla.breakdown?.map((item: any, index: number) => (
                          <div key={index} className="flex justify-between items-center">
                            <Text>{item.period}</Text>
                            <div className="flex items-center space-x-2">
                              <Progress
                                percent={item.percentage}
                                showInfo={false}
                                size="small"
                                style={{ width: 100 }}
                                stroke={item.percentage >= 99.9 ? "green" :
                                       item.percentage >= 99.0 ? "orange" : "red"}
                              />
                              <Text strong>{item.percentage}%</Text>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  ) : (
                    <Empty
                      image={<IllustrationConstruction style={{ width: 150, height: 150 }} />}
                      title="暂无SLA数据"
                      description="正在收集SLA统计数据"
                    />
                  )}
                </Card>
              </Col>
            </Row>
          </Spin>
        </TabPane>

        <TabPane tab="系统事件" itemKey="events">
          <Spin spinning={isEventsLoading}>
            <Card
              title="系统事件日志"
              headerExtraContent={
                <Space>
                  <Select
                    placeholder="日志级别"
                    value={selectedLogLevel}
                    onChange={value => setSelectedLogLevel(value as string)}
                    style={{ width: 120 }}
                    size="small"
                  >
                    <Select.Option value="">全部</Select.Option>
                    <Select.Option value="ERROR">ERROR</Select.Option>
                    <Select.Option value="WARN">WARN</Select.Option>
                    <Select.Option value="INFO">INFO</Select.Option>
                    <Select.Option value="DEBUG">DEBUG</Select.Option>
                  </Select>
                </Space>
              }
            >
              <div className="space-y-3 max-h-96 overflow-y-auto">
                {systemEvents?.events?.map((event: any) => (
                  <div
                    key={event.id}
                    className="p-4 border rounded-lg hover:shadow-sm transition-shadow bg-white"
                  >
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center space-x-3">
                        <Tag color={
                          event.level === 'ERROR' ? 'red' :
                          event.level === 'WARN' ? 'orange' :
                          event.level === 'INFO' ? 'blue' : 'grey'
                        } size="small">
                          {event.level}
                        </Tag>
                        <Text strong size="small">
                          {event.service || event.type || "SYSTEM"}
                        </Text>
                      </div>
                      <Text size="small" type="secondary">
                        {new Date(event.timestamp).toLocaleString()}
                      </Text>
                    </div>
                    <Text size="small" className="text-gray-700 leading-relaxed">
                      {event.message}
                    </Text>
                    {event.metadata && (
                      <div className="mt-2">
                        <Text size="small" type="tertiary">
                          {JSON.stringify(event.metadata, null, 2)}
                        </Text>
                      </div>
                    )}
                  </div>
                ))}
              </div>
              {(!systemEvents?.events || systemEvents.events.length === 0) && (
                <Empty
                  image={<IllustrationConstruction style={{ width: 150, height: 150 }} />}
                  title="暂无系统事件"
                  description="当前暂无系统事件日志"
                />
              )}
            </Card>
          </Spin>
        </TabPane>

        <TabPane tab="报告管理" itemKey="reports">
          <Card
            title="监控报告"
            headerExtraContent={
              <Space>
                <Button
                  type="primary"
                  icon={<IconDownload />}
                  onClick={() => handleGenerateReport('daily', 'json')}
                  loading={generateReportMutation.isPending}
                >
                  生成日报
                </Button>
                <Button
                  icon={<IconDownload />}
                  onClick={() => handleGenerateReport('weekly', 'json')}
                  loading={generateReportMutation.isPending}
                >
                  生成周报
                </Button>
                <Button
                  icon={<IconDownload />}
                  onClick={() => handleGenerateReport('monthly', 'json')}
                  loading={generateReportMutation.isPending}
                >
                  生成月报
                </Button>
              </Space>
            }
          >
            <Row gutter={[24, 24]}>
              <Col span={24} lg={8}>
                <Card title="报告类型">
                  <List
                    dataSource={[
                      { name: "日报", description: "每日系统运行状况", icon: <IconCalendar /> },
                      { name: "周报", description: "每周性能趋势分析", icon: <IconChart /> },
                      { name: "月报", description: "每月综合运营报告", icon: <IconFile /> },
                    ]}
                    renderItem={item => (
                      <List.Item>
                        <div className="flex items-center space-x-3">
                          {item.icon}
                          <div>
                            <Text strong>{item.name}</Text>
                            <div>
                              <Text type="secondary" size="small">
                                {item.description}
                              </Text>
                            </div>
                          </div>
                        </div>
                      </List.Item>
                    )}
                  />
                </Card>
              </Col>

              <Col span={24} lg={8}>
                <Card title="导出格式">
                  <List
                    dataSource={[
                      { name: "JSON", description: "结构化数据格式", available: true },
                      { name: "PDF", description: "便于打印和分享", available: false },
                      { name: "Excel", description: "便于数据分析", available: false },
                    ]}
                    renderItem={item => (
                      <List.Item>
                        <div className="flex items-center justify-between w-full">
                          <div>
                            <Text strong>{item.name}</Text>
                            <div>
                              <Text type="secondary" size="small">
                                {item.description}
                              </Text>
                            </div>
                          </div>
                          <Tag color={item.available ? "green" : "grey"}>
                            {item.available ? "可用" : "开发中"}
                          </Tag>
                        </div>
                      </List.Item>
                    )}
                  />
                </Card>
              </Col>

              <Col span={24} lg={8}>
                <Card title="最近报告">
                  <Empty
                    image={<IllustrationConstruction style={{ width: 100, height: 100 }} />}
                    title="暂无历史报告"
                    description="生成报告后将在此显示"
                  />
                </Card>
              </Col>
            </Row>
          </Card>
        </TabPane>
      </Tabs>

      {/* 阈值设置模态框 */}
      <Modal
        title="监控阈值设置"
        visible={isThresholdModalVisible}
        onCancel={() => {
          setIsThresholdModalVisible(false);
          setThresholdFormData({ metric: '', warning: 0, critical: 0, duration: 0 });
        }}
        footer={null}
        width={600}
      >
        <Form
          onSubmit={handleUpdateThreshold}
          labelPosition="left"
          labelWidth={100}
        >
          <Form.Select
            field="metric"
            label="监控指标"
            placeholder="选择监控指标"
            rules={[{ required: true, message: "请选择监控指标" }]}
          >
            <Form.Select.Option value="cpu">CPU使用率</Form.Select.Option>
            <Form.Select.Option value="memory">内存使用率</Form.Select.Option>
            <Form.Select.Option value="disk">磁盘使用率</Form.Select.Option>
            <Form.Select.Option value="network">网络使用率</Form.Select.Option>
          </Form.Select>

          <Form.InputNumber
            field="warning"
            label="警告阈值"
            placeholder="输入警告阈值"
            suffix="%"
            min={0}
            max={100}
            rules={[{ required: true, message: "请输入警告阈值" }]}
          />

          <Form.InputNumber
            field="critical"
            label="严重阈值"
            placeholder="输入严重阈值"
            suffix="%"
            min={0}
            max={100}
            rules={[{ required: true, message: "请输入严重阈值" }]}
          />

          <Form.InputNumber
            field="duration"
            label="持续时间"
            placeholder="输入持续时间"
            suffix="秒"
            min={1}
            max={3600}
            rules={[{ required: true, message: "请输入持续时间" }]}
          />

          <div className="flex justify-end space-x-3 mt-6">
            <Button onClick={() => setIsThresholdModalVisible(false)}>
              取消
            </Button>
            <Button
              type="primary"
              htmlType="submit"
              loading={updateThresholdMutation.isPending}
            >
              保存设置
            </Button>
          </div>
        </Form>
      </Modal>

      {/* 系统诊断模态框 */}
      <Modal
        title="系统诊断"
        visible={isDiagnosticsModalVisible}
        onCancel={() => setIsDiagnosticsModalVisible(false)}
        footer={
          <Button onClick={() => setIsDiagnosticsModalVisible(false)}>
            关闭
          </Button>
        }
        width={800}
      >
        <Spin spinning={diagnosticsMutation.isPending}>
          {diagnosticsMutation.data?.data ? (
            <div className="space-y-4">
              <div className="bg-gray-50 p-4 rounded">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Text strong>诊断时间：</Text>
                    <Text>{new Date().toLocaleString()}</Text>
                  </div>
                  <div>
                    <Text strong>总体状态：</Text>
                    <Tag color={diagnosticsMutation.data.data.overall === "healthy" ? "green" : "red"}>
                      {diagnosticsMutation.data.data.overall === "healthy" ? "正常" : "异常"}
                    </Tag>
                  </div>
                </div>
              </div>

              <div>
                <Text strong>诊断结果：</Text>
                <Timeline className="mt-3">
                  {diagnosticsMutation.data.data.tests?.map((test: any, index: number) => (
                    <Timeline.Item
                      key={index}
                      dot={test.passed ?
                        <IconTickCircle style={{ color: "var(--semi-color-success)" }} /> :
                        <IconAlertTriangle style={{ color: "var(--semi-color-danger)" }} />
                      }
                    >
                      <div>
                        <Text strong>{test.name}</Text>
                        <div className="mt-1">
                          <Text type="secondary">{test.description}</Text>
                        </div>
                        {test.details && (
                          <div className="mt-2">
                            <Text size="small" type="tertiary">
                              {test.details}
                            </Text>
                          </div>
                        )}
                      </div>
                    </Timeline.Item>
                  ))}
                </Timeline>
              </div>

              {diagnosticsMutation.data.data.recommendations && (
                <div>
                  <Text strong>优化建议：</Text>
                  <List
                    className="mt-3"
                    dataSource={diagnosticsMutation.data.data.recommendations}
                    renderItem={(item: any) => (
                      <List.Item>
                        <div className="w-full">
                          <div className="flex items-center space-x-2 mb-1">
                            <Tag color={item.priority === 'high' ? 'red' :
                                       item.priority === 'medium' ? 'orange' : 'blue'}>
                              {item.priority === 'high' ? '高优先级' :
                               item.priority === 'medium' ? '中优先级' : '低优先级'}
                            </Tag>
                          </div>
                          <Text strong>{item.title}</Text>
                          <div className="mt-1">
                            <Text type="secondary">{item.description}</Text>
                          </div>
                        </div>
                      </List.Item>
                    )}
                  />
                </div>
              )}
            </div>
          ) : (
            <div className="text-center py-8">
              <Text type="secondary">正在运行系统诊断...</Text>
            </div>
          )}
        </Spin>
      </Modal>

      {/* 监控配置模态框 */}
      <Modal
        title="监控配置"
        visible={isConfigModalVisible}
        onCancel={() => setIsConfigModalVisible(false)}
        footer={null}
        width={800}
      >
        <Spin spinning={isConfigLoading}>
          {monitorConfig ? (
            <div className="space-y-4">
              <Descriptions
                data={[
                  { key: "刷新间隔", value: `${monitorConfig.refreshInterval || 30}秒` },
                  { key: "数据保留", value: `${monitorConfig.dataRetention || 7}天` },
                  { key: "告警通知", value: monitorConfig.alertNotification ? "启用" : "禁用" },
                  { key: "自动清理", value: monitorConfig.autoCleanup ? "启用" : "禁用" },
                ]}
              />
              <Divider />
              <div>
                <Text strong>监控项目：</Text>
                <div className="mt-3 space-y-2">
                  {monitorConfig.monitors?.map((monitor: any, index: number) => (
                    <div key={index} className="flex items-center justify-between p-3 border rounded">
                      <div>
                        <Text strong>{monitor.name}</Text>
                        <div>
                          <Text type="secondary" size="small">
                            {monitor.description}
                          </Text>
                        </div>
                      </div>
                      <Switch checked={monitor.enabled} disabled />
                    </div>
                  ))}
                </div>
              </div>
            </div>
          ) : (
            <Empty
              image={<IllustrationConstruction style={{ width: 150, height: 150 }} />}
              title="暂无配置数据"
              description="正在加载监控配置"
            />
          )}
        </Spin>
      </Modal>
    </div>
  );
}
