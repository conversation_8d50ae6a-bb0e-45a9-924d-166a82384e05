import React, { useEffect, useState } from "react";
import { useQuery } from "@tanstack/react-query";
import {
  Button,
  Input,
  Select,
  Table,
  Pagination,
  Tag,
  DatePicker,
  Space,
  Card,
  Toast,
  Modal,
  Typography,
} from "@douyinfe/semi-ui";
import {
  IconSearch,
  IconRefresh,
  IconDownload,
  IconEyeOpened,
  IconUser,
  IconClock,
} from "@douyinfe/semi-icons";
import dayjs from "dayjs";
import { useUIStore } from "@/stores";
import { auditService, type AuditLogListParams, type AuditLog } from "@/services/audit";
import { userService } from "@/services/user";

const { Text } = Typography;

export default function AuditLogsPage() {
  const { setBreadcrumbs } = useUIStore();

  const [params, setParams] = useState<AuditLogListParams>({
    page: 1,
    limit: 50,
  });
  const [selectedLog, setSelectedLog] = useState<AuditLog | null>(null);
  const [isDetailModalVisible, setIsDetailModalVisible] = useState(false);

  useEffect(() => {
    setBreadcrumbs([
      { text: "首页", href: "/", icon: "🏠" },
      { text: "系统管理", icon: "⚙️" },
      { text: "审计日志", icon: "📝" },
    ]);
  }, [setBreadcrumbs]);

  // 获取审计日志列表
  const {
    data: logsData,
    isLoading,
    refetch,
  } = useQuery({
    queryKey: ["audit-logs", params],
    queryFn: () => auditService.getAuditLogs(params),
  });

  // 获取审计统计
  const { data: statsData } = useQuery({
    queryKey: ["audit-stats", params.startDate, params.endDate],
    queryFn: () =>
      auditService.getAuditStats({
        startDate: params.startDate,
        endDate: params.endDate,
      }),
  });

  // 获取用户列表（用于筛选）
  const { data: usersData } = useQuery({
    queryKey: ["users-simple"],
    queryFn: () => userService.getUsers({ limit: 1000 }),
  });

  const handleSearch = (value: string) => {
    // 这里可以根据搜索内容智能匹配用户名或资源ID
    setParams({ ...params, page: 1 });
  };

  const handleFilterChange = (key: string, value: any) => {
    setParams({ ...params, [key]: value, page: 1 });
  };

  const handleDateRangeChange = (dateStr: string | string[] | Date | Date[] | undefined) => {
    if (Array.isArray(dateStr) && dateStr.length === 2) {
      setParams({
        ...params,
        startDate: typeof dateStr[0] === "string" ? dateStr[0] : undefined,
        endDate: typeof dateStr[1] === "string" ? dateStr[1] : undefined,
        page: 1,
      });
    } else {
      setParams({
        ...params,
        startDate: undefined,
        endDate: undefined,
        page: 1,
      });
    }
  };

  const handlePageChange = (page: number, pageSize?: number) => {
    setParams({ ...params, page, limit: pageSize || params.limit });
  };

  const handleViewDetails = (log: AuditLog) => {
    setSelectedLog(log);
    setIsDetailModalVisible(true);
  };

  const handleExport = async () => {
    try {
      const blob = await auditService.exportAuditLogs(params);
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.download = `audit_logs_${dayjs().format("YYYY-MM-DD_HH-mm-ss")}.csv`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      Toast.success("审计日志导出成功");
    } catch (error: any) {
      Toast.error(error.message || "导出失败");
    }
  };

  // 统一格式化用户显示
  const getUserDisplay = (user: AuditLog["user"]): { primary: string; secondary?: string } => {
    if (typeof user === "string") {
      return { primary: user };
    }
    if (user && typeof user === "object") {
      return {
        primary: user.username || user.fullName || user.id || "-",
        secondary: user.fullName,
      };
    }
    return { primary: "-" };
  };

  const getResourceColor = (
    resource: string
  ):
    | "amber"
    | "blue"
    | "cyan"
    | "green"
    | "grey"
    | "indigo"
    | "light-blue"
    | "light-green"
    | "lime"
    | "orange"
    | "pink"
    | "purple"
    | "red"
    | "teal"
    | "violet"
    | "yellow"
    | undefined => {
    const colors: Record<
      string,
      | "amber"
      | "blue"
      | "cyan"
      | "green"
      | "grey"
      | "indigo"
      | "light-blue"
      | "light-green"
      | "lime"
      | "orange"
      | "pink"
      | "purple"
      | "red"
      | "teal"
      | "violet"
      | "yellow"
    > = {
      AUTH: "blue",
      USER: "green",
      ROLE: "purple",
      CUSTOMER: "orange",
      ARCHIVE: "cyan",
      SERVICE: "red",
      CONFIGURATION: "pink",
      SLA: "amber",
    };
    return colors[resource];
  };

  const getActionColor = (
    action: string
  ):
    | "amber"
    | "blue"
    | "cyan"
    | "green"
    | "grey"
    | "indigo"
    | "light-blue"
    | "light-green"
    | "lime"
    | "orange"
    | "pink"
    | "purple"
    | "red"
    | "teal"
    | "violet"
    | "yellow"
    | undefined => {
    const colors: Record<
      string,
      | "amber"
      | "blue"
      | "cyan"
      | "green"
      | "grey"
      | "indigo"
      | "light-blue"
      | "light-green"
      | "lime"
      | "orange"
      | "pink"
      | "purple"
      | "red"
      | "teal"
      | "violet"
      | "yellow"
    > = {
      CREATE: "green",
      UPDATE: "blue",
      DELETE: "red",
      VIEW: "grey",
      LOGIN: "cyan",
      LOGOUT: "orange",
      STATUS_CHANGE: "purple",
    };
    return colors[action];
  };

  const columns = [
    {
      title: "时间",
      dataIndex: "timestamp",
      key: "timestamp",
      width: 160,
      render: (text: string) => (
        <div>
          <div>{dayjs(text).format("MM-DD HH:mm:ss")}</div>
          <div className="text-xs text-gray-500">{dayjs(text).format("YYYY")}</div>
        </div>
      ),
      sorter: (a?: AuditLog, b?: AuditLog) => {
        if (!a || !b) return 0;
        return new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime();
      },
    },
    {
      title: "用户",
      dataIndex: "user",
      key: "user",
      width: 120,
      render: (_: any, record: AuditLog) => {
        const { primary, secondary } = getUserDisplay(record.user);
        return (
          <div className="flex items-center space-x-2">
            <IconUser />
            <div>
              <div className="font-medium">{primary}</div>
              {secondary && (
                <Text size="small" type="quaternary">
                  {secondary}
                </Text>
              )}
            </div>
          </div>
        );
      },
    },
    {
      title: "操作",
      dataIndex: "action",
      key: "action",
      width: 100,
      render: (action: string) => (
        <Tag color={getActionColor(action)}>{auditService.formatAction(action as any)}</Tag>
      ),
    },
    {
      title: "资源",
      dataIndex: "resource",
      key: "resource",
      width: 100,
      render: (resource: string) => (
        <Tag color={getResourceColor(resource)}>{auditService.formatResource(resource as any)}</Tag>
      ),
    },
    {
      title: "资源ID",
      dataIndex: "resourceId",
      key: "resourceId",
      width: 120,
      render: (resourceId: string) => (
        <span className="font-mono text-sm">{resourceId || "-"}</span>
      ),
    },
    {
      title: "IP地址",
      dataIndex: "ipAddress",
      key: "ipAddress",
      width: 120,
      render: (ip: string) => <span className="font-mono text-sm">{ip || "-"}</span>,
    },
    {
      title: "详情",
      key: "actions",
      width: 80,
      render: (_: any, record: AuditLog) => (
        <Button
          theme="borderless"
          type="primary"
          size="small"
          icon={<IconEyeOpened />}
          onClick={() => handleViewDetails(record)}
        >
          查看
        </Button>
      ),
    },
  ];

  return (
    <div className="space-y-6">
      <div className="page-header">
        <h1 className="page-title">审计日志</h1>
        <p className="page-subtitle">查看系统操作记录和审计信息</p>
      </div>

      {/* 统计卡片 */}
      {statsData?.data && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card title="总操作数" style={{ textAlign: "center" }} bodyStyle={{ padding: "20px" }}>
            <div className="flex items-center justify-center space-x-2">
              <IconClock style={{ color: "#1890ff" }} />
              <div className="text-2xl font-bold text-blue-600">
                {statsData.data.totalOperations}
              </div>
            </div>
          </Card>
          <Card title="活跃用户" style={{ textAlign: "center" }} bodyStyle={{ padding: "20px" }}>
            <div className="flex items-center justify-center space-x-2">
              <IconUser style={{ color: "#52c41a" }} />
              <div className="text-2xl font-bold text-green-600">
                {statsData.data.activeUsers?.length || 0}
                <span className="text-sm text-gray-500 ml-1">人</span>
              </div>
            </div>
          </Card>
          <Card
            title="最多操作类型"
            style={{ textAlign: "center" }}
            bodyStyle={{ padding: "20px" }}
          >
            <div className="text-2xl font-bold text-purple-600">
              {
                Object.keys(statsData.data.resourceStats || {}).reduce(
                  (max, resource) => {
                    const total = Object.values(
                      statsData.data.resourceStats[resource] || {}
                    ).reduce((sum: number, count: any) => sum + count, 0);
                    return total > (max.count || 0)
                      ? { resource: auditService.formatResource(resource as any), count: total }
                      : max;
                  },
                  { resource: "-", count: 0 }
                ).resource
              }
            </div>
          </Card>
          <Card title="时间范围" style={{ textAlign: "center" }} bodyStyle={{ padding: "20px" }}>
            <div className="flex items-center justify-center space-x-2">
              <IconClock style={{ color: "#722ed1" }} />
              <div className="text-lg font-bold text-purple-600">
                {params.startDate && params.endDate
                  ? `${dayjs(params.startDate).format("MM-DD")} 至 ${dayjs(params.endDate).format("MM-DD")}`
                  : "全部"}
              </div>
            </div>
          </Card>
        </div>
      )}

      <div className="card">
        {/* 搜索和筛选 */}
        <div className="flex flex-wrap gap-4 mb-6">
          <Input
            placeholder="搜索操作内容..."
            style={{ width: 250 }}
            suffix={<IconSearch onClick={() => handleSearch("")} style={{ cursor: "pointer" }} />}
            onEnterPress={e => handleSearch(e.currentTarget.value)}
            showClear
          />
          <Select
            placeholder="用户筛选"
            style={{ width: 150 }}
            showClear
            filter
            onChange={value => handleFilterChange("userId", value)}
          >
            {usersData?.data?.users?.map(user => (
              <Select.Option key={user.id} value={user.id}>
                {user.fullName} ({user.username})
              </Select.Option>
            ))}
          </Select>
          <Select
            placeholder="资源类型"
            style={{ width: 120 }}
            showClear
            onChange={value => handleFilterChange("resource", value)}
          >
            <Select.Option value="AUTH">认证</Select.Option>
            <Select.Option value="USER">用户</Select.Option>
            <Select.Option value="ROLE">角色</Select.Option>
            <Select.Option value="CUSTOMER">客户</Select.Option>
            <Select.Option value="ARCHIVE">项目档案</Select.Option>
            <Select.Option value="SERVICE">服务工单</Select.Option>
            <Select.Option value="CONFIGURATION">配置</Select.Option>
            <Select.Option value="SLA">SLA</Select.Option>
          </Select>
          <Select
            placeholder="操作类型"
            style={{ width: 120 }}
            showClear
            onChange={value => handleFilterChange("action", value)}
          >
            <Select.Option value="CREATE">创建</Select.Option>
            <Select.Option value="UPDATE">更新</Select.Option>
            <Select.Option value="DELETE">删除</Select.Option>
            <Select.Option value="VIEW">查看</Select.Option>
            <Select.Option value="LOGIN">登录</Select.Option>
            <Select.Option value="LOGOUT">登出</Select.Option>
            <Select.Option value="STATUS_CHANGE">状态变更</Select.Option>
          </Select>
          <DatePicker
            type="dateRange"
            style={{ width: 240 }}
            onChange={handleDateRangeChange}
            placeholder={["开始日期", "结束日期"]}
          />
          <Space>
            <Button icon={<IconRefresh />} onClick={() => refetch()}>
              刷新
            </Button>
            <Button icon={<IconDownload />} onClick={handleExport}>
              导出
            </Button>
          </Space>
        </div>

        {/* 日志表格 */}
        <Table
          columns={columns}
          dataSource={logsData?.data?.logs || []}
          rowKey="id"
          loading={isLoading}
          pagination={false}
          size="small"
          style={{
            width: "100%",
          }}
        />

        {/* 分页 */}
        {logsData?.data && (
          <div className="flex justify-end mt-4">
            <Pagination
              currentPage={logsData.data.page}
              total={logsData.data.total}
              pageSize={logsData.data.limit}
              showSizeChanger
              showQuickJumper
              showTotal
              onPageChange={handlePageChange}
            />
          </div>
        )}
      </div>

      {/* 详情弹窗 */}
      <Modal
        title="操作详情"
        visible={isDetailModalVisible}
        onCancel={() => setIsDetailModalVisible(false)}
        footer={null}
        width={600}
      >
        {selectedLog && (
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <span className="text-gray-500">操作时间:</span>
                <Text>{dayjs(selectedLog.timestamp).format("YYYY-MM-DD HH:mm:ss")}</Text>
              </div>
              <div>
                <span className="text-gray-500">操作用户:</span>
                <Text>
                  {(() => {
                    const { primary, secondary } = getUserDisplay(selectedLog.user);
                    return secondary ? `${secondary} (${primary})` : primary;
                  })()}
                </Text>
              </div>
              <div>
                <span className="text-gray-500">操作类型:</span>
                <div>
                  <Tag color={getActionColor(selectedLog.action)}>
                    {auditService.formatAction(selectedLog.action)}
                  </Tag>
                </div>
              </div>
              <div>
                <span className="text-gray-500">资源类型:</span>
                <div>
                  <Tag color={getResourceColor(selectedLog.resource)}>
                    {auditService.formatResource(selectedLog.resource)}
                  </Tag>
                </div>
              </div>
              <div>
                <span className="text-gray-500">资源ID:</span>
                <Text code>{selectedLog.resourceId || "-"}</Text>
              </div>
              <div>
                <span className="text-gray-500">IP地址:</span>
                <Text code>{selectedLog.ipAddress || "-"}</Text>
              </div>
            </div>

            {selectedLog.details && (
              <div>
                <span className="text-gray-500">操作详情:</span>
                <pre className="mt-2 p-3 bg-gray-50 rounded text-sm overflow-auto">
                  {JSON.stringify(selectedLog.details, null, 2)}
                </pre>
              </div>
            )}
          </div>
        )}
      </Modal>
    </div>
  );
}
