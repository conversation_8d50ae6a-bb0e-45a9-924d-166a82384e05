import React, { useEffect, useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import {
  Row,
  Col,
  Card,
  Button,
  Typography,
  Avatar,
  Progress,
  List,
  Tag,
  Spin,
  Empty,
  Space,
  Select,
  DatePicker,
  Table,
  Badge,
  Tooltip,
} from "@douyinfe/semi-ui";
import {
  IconUser,
  IconActivity,
  IconUpload as IconTrendingUp,
  IconAlertTriangle,
  IconClock,
  IconRefresh,
  IconSetting,
} from "@douyinfe/semi-icons";
import { useUIStore } from "@/stores";
import { userAnalyticsService } from "@/services/user-analytics";
import { realtimeService } from "@/services/realtime";
const { Text } = Typography;
const { Option } = Select;
// DatePicker 组件直接支持 type="dateRange"

export default function UserAnalyticsPage() {
  const { setBreadcrumbs } = useUIStore();
  const queryClient = useQueryClient();
  // const [selectedUserId, setSelectedUserId] = useState<string>("");
  const [dateRange, setDateRange] = useState<[Date, Date]>([
    new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
    new Date(),
  ]);
  const [anomalyFilter, setAnomalyFilter] = useState({
    severity: "",
    type: "",
    isResolved: undefined as boolean | undefined,
  });

  useEffect(() => {
    setBreadcrumbs([
      { text: "首页", href: "/", icon: "🏠" },
      { text: "系统管理", icon: "⚙️" },
      { text: "用户活动分析", icon: "📊" },
    ]);
  }, [setBreadcrumbs]);

  // 获取活动概览数据
  const {
    data: activitySummary,
    isLoading: summaryLoading,
    refetch: refetchSummary,
  } = useQuery({
    queryKey: ["activity-summary", dateRange],
    queryFn: () =>
      userAnalyticsService
        .getActivitySummary(
          dateRange[0].toISOString().split("T")[0],
          dateRange[1].toISOString().split("T")[0]
        )
        .then(res => res.data),
    refetchInterval: 30000,
  });

  // 获取在线用户 - 使用WebSocket方式
  const { data: onlineUsers, isLoading: onlineLoading } = useQuery({
    queryKey: ["online-users"],
    queryFn: () => realtimeService.getOnlineUsers(),
    refetchInterval: 15000,
  });

  // 获取用户排行榜
  const { data: rankings, isLoading: rankingsLoading } = useQuery({
    queryKey: ["user-rankings"],
    queryFn: () =>
      userAnalyticsService.getUserRankings("activity", "week", 10).then(res => res.data),
  });

  // 获取异常记录
  const { data: anomalies, isLoading: anomaliesLoading } = useQuery({
    queryKey: ["user-anomalies", anomalyFilter],
    queryFn: () =>
      userAnalyticsService.getUserAnomalies(1, 20, anomalyFilter).then(res => res.data),
  });

  // 获取热力图数据 (暂时注释)
  // const { data: heatmapData, isLoading: heatmapLoading } = useQuery({
  //   queryKey: ["activity-heatmap", selectedUserId],
  //   queryFn: () =>
  //     userAnalyticsService.getActivityHeatmap(selectedUserId || undefined).then(res => res.data),
  // });

  // 触发异常检测
  const detectAnomaliesMutation = useMutation({
    mutationFn: (userId?: string) => userAnalyticsService.triggerAnomalyDetection(userId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["user-anomalies"] });
    },
  });

  // 解决异常
  const resolveAnomalyMutation = useMutation({
    mutationFn: ({ anomalyId, notes }: { anomalyId: string; notes?: string }) =>
      userAnalyticsService.resolveAnomaly(anomalyId, notes),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["user-anomalies"] });
    },
  });

  // 异常类型映射
  const getAnomalyTypeLabel = (type: string) => {
    const typeMap = {
      UNUSUAL_TIME: "异常时间",
      EXCESSIVE_OPERATIONS: "过度操作",
      ABNORMAL_LOCATION: "异常位置",
      SUSPICIOUS_BEHAVIOR: "可疑行为",
    };
    return typeMap[type as keyof typeof typeMap] || type;
  };

  // 严重程度颜色
  const getSeverityColor = (
    severity: string
  ):
    | "amber"
    | "blue"
    | "cyan"
    | "green"
    | "grey"
    | "indigo"
    | "light-blue"
    | "light-green"
    | "lime"
    | "orange"
    | "pink"
    | "purple"
    | "red"
    | "teal"
    | "violet"
    | "yellow" => {
    const colorMap: Record<
      string,
      | "amber"
      | "blue"
      | "cyan"
      | "green"
      | "grey"
      | "indigo"
      | "light-blue"
      | "light-green"
      | "lime"
      | "orange"
      | "pink"
      | "purple"
      | "red"
      | "teal"
      | "violet"
      | "yellow"
    > = {
      LOW: "blue",
      MEDIUM: "orange",
      HIGH: "red",
      CRITICAL: "purple",
    };
    return colorMap[severity] || "grey";
  };

  // 异常表格列定义
  const anomalyColumns = [
    {
      title: "用户",
      dataIndex: "user",
      render: (user: any) => (
        <div className="flex items-center">
          <Avatar size="small" style={{ marginRight: 8 }}>
            {user.fullName?.[0] || user.username?.[0]}
          </Avatar>
          <div>
            <Text strong>{user.fullName || user.username}</Text>
            <Text type="secondary" size="small" style={{ display: "block" }}>
              @{user.username}
            </Text>
          </div>
        </div>
      ),
    },
    {
      title: "异常类型",
      dataIndex: "anomalyType",
      render: (type: string) => <Tag color="cyan">{getAnomalyTypeLabel(type)}</Tag>,
    },
    {
      title: "严重程度",
      dataIndex: "severity",
      render: (severity: string) => <Tag color={getSeverityColor(severity)}>{severity}</Tag>,
    },
    {
      title: "描述",
      dataIndex: "description",
      width: 200,
      render: (text: string) => (
        <Tooltip content={text}>
          <Text ellipsis={{ showTooltip: true, pos: "middle" }}>{text}</Text>
        </Tooltip>
      ),
    },
    {
      title: "置信度",
      dataIndex: "confidenceScore",
      render: (score: number) => (
        <Progress percent={Math.round(score * 100)} size="small" showInfo />
      ),
    },
    {
      title: "检测时间",
      dataIndex: "detectedAt",
      render: (date: string) => new Date(date).toLocaleString(),
    },
    {
      title: "状态",
      dataIndex: "isResolved",
      render: (isResolved: boolean, record: any) => (
        <Space>
          {isResolved ? (
            <Badge count="已解决" style={{ backgroundColor: "var(--semi-color-success)" }} />
          ) : (
            <Badge count="待处理" style={{ backgroundColor: "var(--semi-color-warning)" }} />
          )}
          {!isResolved && (
            <Button
              size="small"
              onClick={() => resolveAnomalyMutation.mutate({ anomalyId: record.id })}
              loading={resolveAnomalyMutation.isPending}
            >
              标记解决
            </Button>
          )}
        </Space>
      ),
    },
  ];

  if (summaryLoading || onlineLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Spin size="large" />
        <Text style={{ marginLeft: 16 }}>正在加载用户活动数据...</Text>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="page-header">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="page-title">用户活动分析</h1>
            <p className="page-subtitle">智能分析用户行为模式，识别异常活动和潜在风险</p>
          </div>
          <Space>
            <DatePicker
              type="dateRange"
              value={dateRange}
              onChange={(dates: any) => setDateRange(dates as [Date, Date])}
              style={{ width: 240 }}
            />
            <Button
              icon={<IconRefresh />}
              onClick={() => refetchSummary()}
              loading={summaryLoading}
            >
              刷新数据
            </Button>
            <Button
              icon={<IconSetting />}
              onClick={() => detectAnomaliesMutation.mutate(undefined)}
              loading={detectAnomaliesMutation.isPending}
            >
              触发异常检测
            </Button>
          </Space>
        </div>
      </div>

      {/* 概览统计卡片 */}
      <Row gutter={[16, 16]}>
        <Col span={24} sm={12} lg={6}>
          <Card
            className="stat-card"
            bodyStyle={{ padding: "20px" }}
            style={{
              background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
              color: "white",
            }}
          >
            <div className="flex items-center justify-between">
              <div>
                <Text style={{ color: "rgba(255,255,255,0.8)", fontSize: 14 }}>总用户数</Text>
                <div className="text-2xl font-bold mt-1">{activitySummary?.totalUsers || 0}</div>
                <div className="flex items-center mt-2">
                  <IconTrendingUp size="small" />
                  <Text style={{ color: "rgba(255,255,255,0.9)", fontSize: 12, marginLeft: 4 }}>
                    活跃 {activitySummary?.activeUsers || 0}
                  </Text>
                </div>
              </div>
              <Avatar size="large" style={{ backgroundColor: "rgba(255,255,255,0.2)" }}>
                <IconUser />
              </Avatar>
            </div>
          </Card>
        </Col>

        <Col span={24} sm={12} lg={6}>
          <Card
            className="stat-card"
            bodyStyle={{ padding: "20px" }}
            style={{
              background: "linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)",
              color: "white",
            }}
          >
            <div className="flex items-center justify-between">
              <div>
                <Text style={{ color: "rgba(255,255,255,0.8)", fontSize: 14 }}>在线用户</Text>
                <div className="text-2xl font-bold mt-1">{onlineUsers?.count || 0}</div>
                <div className="flex items-center mt-2">
                  <IconActivity size="small" />
                  <Text style={{ color: "rgba(255,255,255,0.9)", fontSize: 12, marginLeft: 4 }}>
                    平均时长 {Math.round(activitySummary?.avgSessionDuration || 0)}分钟
                  </Text>
                </div>
              </div>
              <Avatar size="large" style={{ backgroundColor: "rgba(255,255,255,0.2)" }}>
                <IconActivity />
              </Avatar>
            </div>
          </Card>
        </Col>

        <Col span={24} sm={12} lg={6}>
          <Card
            className="stat-card"
            bodyStyle={{ padding: "20px" }}
            style={{
              background: "linear-gradient(135deg, #fa709a 0%, #fee140 100%)",
              color: "white",
            }}
          >
            <div className="flex items-center justify-between">
              <div>
                <Text style={{ color: "rgba(255,255,255,0.8)", fontSize: 14 }}>今日登录</Text>
                <div className="text-2xl font-bold mt-1">{activitySummary?.todayLogins || 0}</div>
                <div className="flex items-center mt-2">
                  <IconClock size="small" />
                  <Text style={{ color: "rgba(255,255,255,0.9)", fontSize: 12, marginLeft: 4 }}>
                    高峰 {activitySummary?.peakActivityTime || "未知"}
                  </Text>
                </div>
              </div>
              <Avatar size="large" style={{ backgroundColor: "rgba(255,255,255,0.2)" }}>
                <IconClock />
              </Avatar>
            </div>
          </Card>
        </Col>

        <Col span={24} sm={12} lg={6}>
          <Card
            className="stat-card"
            bodyStyle={{ padding: "20px" }}
            style={{
              background: "linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)",
              color: "#333",
            }}
          >
            <div className="flex items-center justify-between">
              <div>
                <Text style={{ color: "rgba(51,51,51,0.8)", fontSize: 14 }}>异常警告</Text>
                <div className="text-2xl font-bold mt-1">
                  {anomalies?.anomalies?.filter(a => !a.isResolved).length || 0}
                </div>
                <div className="flex items-center mt-2">
                  <IconAlertTriangle size="small" />
                  <Text style={{ color: "rgba(51,51,51,0.9)", fontSize: 12, marginLeft: 4 }}>
                    待处理
                  </Text>
                </div>
              </div>
              <Avatar size="large" style={{ backgroundColor: "rgba(51,51,51,0.1)" }}>
                <IconAlertTriangle />
              </Avatar>
            </div>
          </Card>
        </Col>
      </Row>

      {/* 内容区域 */}
      <Row gutter={[16, 16]}>
        {/* 在线用户列表 */}
        <Col span={24} lg={8}>
          <Card
            title="在线用户"
            headerExtraContent={<Tag color="green">{onlineUsers?.count || 0} 在线</Tag>}
          >
            <List
              dataSource={onlineUsers?.users?.slice(0, 10) || []}
              loading={onlineLoading}
              renderItem={(user: any) => (
                <List.Item>
                  <div className="flex items-center w-full">
                    <Avatar size="small" style={{ backgroundColor: "var(--semi-color-primary)" }}>
                      {user.fullName?.[0] || user.username?.[0]}
                    </Avatar>
                    <div className="ml-3 flex-1">
                      <div className="flex justify-between items-center">
                        <div>
                          <Text strong>{user.fullName || user.username}</Text>
                          {user.department && (
                            <Tag size="small" style={{ marginLeft: 4 }}>
                              {user.department}
                            </Tag>
                          )}
                        </div>
                        <div className="flex items-center">
                          <div className="w-2 h-2 rounded-full bg-green-500 mr-2"></div>
                          <Text type="secondary" size="small">
                            {user.lastActivity
                              ? new Date(user.lastActivity).toLocaleTimeString()
                              : "刚刚"}
                          </Text>
                        </div>
                      </div>
                      <div className="flex justify-between items-center mt-1">
                        <Text type="secondary" size="small">
                          @{user.username}
                        </Text>
                        <Button
                          size="small"
                          theme="borderless"
                          // icon={<IconEye />}
                          // onClick={() => setSelectedUserId(user.id)}
                        >
                          分析
                        </Button>
                      </div>
                    </div>
                  </div>
                </List.Item>
              )}
              emptyContent={<Empty description="暂无在线用户" />}
            />
          </Card>
        </Col>

        {/* 活跃用户排行 */}
        <Col span={24} lg={8}>
          <Card title="活跃用户排行" loading={rankingsLoading}>
            <List
              dataSource={rankings?.rankings || []}
              renderItem={(item: any) => (
                <List.Item>
                  <div className="flex items-center w-full">
                    <div className="flex items-center min-w-[40px]">
                      <Text
                        strong
                        style={{
                          fontSize: 18,
                          color:
                            item.rank <= 3
                              ? "var(--semi-color-primary)"
                              : "var(--semi-color-text-2)",
                        }}
                      >
                        #{item.rank}
                      </Text>
                    </div>
                    <Avatar size="small" style={{ margin: "0 8px" }}>
                      {item.user.fullName?.[0] || item.user.username?.[0]}
                    </Avatar>
                    <div className="flex-1">
                      <div className="flex justify-between items-center">
                        <Text strong>{item.user.fullName || item.user.username}</Text>
                        <Text type="secondary" size="small">
                          评分: {Math.round(item.activityScore)}
                        </Text>
                      </div>
                      <div className="flex justify-between items-center mt-1">
                        <Text type="secondary" size="small">
                          登录: {item.loginCount}次
                        </Text>
                        <Text type="secondary" size="small">
                          时长: {Math.round(item.totalDurationMinutes / 60)}h
                        </Text>
                      </div>
                    </div>
                  </div>
                </List.Item>
              )}
              emptyContent={<Empty description="暂无排行数据" />}
            />
          </Card>
        </Col>

        {/* 最新异常 */}
        <Col span={24} lg={8}>
          <Card
            title="最新异常"
            headerExtraContent={
              <Button
                size="small"
                onClick={() => {
                  /* TODO: 打开异常详情 */
                }}
              >
                查看全部
              </Button>
            }
          >
            <List
              dataSource={anomalies?.anomalies?.slice(0, 5) || []}
              loading={anomaliesLoading}
              renderItem={(anomaly: any) => (
                <List.Item>
                  <div className="flex items-start w-full">
                    <div className="mr-3 mt-1">
                      <IconAlertTriangle
                        style={{
                          color:
                            anomaly.severity === "HIGH" || anomaly.severity === "CRITICAL"
                              ? "var(--semi-color-danger)"
                              : "var(--semi-color-warning)",
                        }}
                      />
                    </div>
                    <div className="flex-1">
                      <div className="flex justify-between items-start">
                        <Text strong>{anomaly.user.fullName || anomaly.user.username}</Text>
                        <Tag color={getSeverityColor(anomaly.severity)} size="small">
                          {anomaly.severity}
                        </Tag>
                      </div>
                      <Text type="secondary" size="small" ellipsis={{ showTooltip: true }}>
                        {anomaly.description}
                      </Text>
                      <div className="flex justify-between items-center mt-1">
                        <Text type="secondary" size="small">
                          {new Date(anomaly.detectedAt).toLocaleString()}
                        </Text>
                        {!anomaly.isResolved && (
                          <Button
                            size="small"
                            theme="borderless"
                            onClick={() => resolveAnomalyMutation.mutate({ anomalyId: anomaly.id })}
                          >
                            解决
                          </Button>
                        )}
                      </div>
                    </div>
                  </div>
                </List.Item>
              )}
              emptyContent={<Empty description="暂无异常记录" />}
            />
          </Card>
        </Col>
      </Row>

      {/* 异常记录表格 */}
      <Card
        title="异常记录管理"
        headerExtraContent={
          <Space>
            <Select
              placeholder="筛选严重程度"
              value={anomalyFilter.severity}
              onChange={value => setAnomalyFilter(prev => ({ ...prev, severity: value as string }))}
              style={{ width: 120 }}
              showClear
            >
              <Option value="LOW">低</Option>
              <Option value="MEDIUM">中</Option>
              <Option value="HIGH">高</Option>
              <Option value="CRITICAL">严重</Option>
            </Select>
            <Select
              placeholder="筛选异常类型"
              value={anomalyFilter.type}
              onChange={value => setAnomalyFilter(prev => ({ ...prev, type: value as string }))}
              style={{ width: 140 }}
              showClear
            >
              <Option value="UNUSUAL_TIME">异常时间</Option>
              <Option value="EXCESSIVE_OPERATIONS">过度操作</Option>
              <Option value="ABNORMAL_LOCATION">异常位置</Option>
              <Option value="SUSPICIOUS_BEHAVIOR">可疑行为</Option>
            </Select>
            <Select
              placeholder="处理状态"
              value={
                anomalyFilter.isResolved === true
                  ? "true"
                  : anomalyFilter.isResolved === false
                    ? "false"
                    : undefined
              }
              onChange={value =>
                setAnomalyFilter(prev => ({
                  ...prev,
                  isResolved: value === "true" ? true : value === "false" ? false : undefined,
                }))
              }
              style={{ width: 120 }}
              showClear
            >
              <Option value="false">未解决</Option>
              <Option value="true">已解决</Option>
            </Select>
          </Space>
        }
      >
        <Table
          dataSource={anomalies?.anomalies || []}
          columns={anomalyColumns}
          loading={anomaliesLoading}
          pagination={{
            currentPage: 1,
            pageSize: 20,
            total: anomalies?.pagination?.total || 0,
            showQuickJumper: true,
            showSizeChanger: true,
          }}
          rowKey="id"
        />
      </Card>
    </div>
  );
}
