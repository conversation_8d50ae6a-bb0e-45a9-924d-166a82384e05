import React, { useEffect, useState } from "react";
import { useQuery } from "@tanstack/react-query";
import {
  Button,
  Input,
  Select,
  Table,
  Tag,
  Modal,
  Popconfirm,
  Space,
  Toast,
  Card,
  Row,
  Col,
  Avatar,
  Typography,
  Divider,
  Empty,
  Badge,
  Spin,
  Tooltip,
  Tree,
  Form,
} from "@douyinfe/semi-ui";
import {
  IconPlus,
  IconSearch,
  IconRefresh,
  IconEdit,
  IconDelete,
  IconShield,
  IconKey,
  IconCopy,
  IconEyeOpened,
  IconSetting,
  IconUser,
  IconUserGroup,
  IconActivity,
  IconFolder,
  IconLock,
} from "@douyinfe/semi-icons";
import { useUIStore } from "@/stores";
import {
  permissionTemplateService,
  type PermissionTemplate,
  type PermissionGroup,
  type PermissionDetail,
} from "@/services/permission-template";

const { Text } = Typography;

// Types are now imported from the service

const permissionCategories = [
  { key: "user", label: "用户管理", icon: <IconUser />, color: "blue" },
  { key: "customer", label: "客户管理", icon: <IconUserGroup />, color: "green" },
  { key: "project", label: "项目管理", icon: <IconFolder />, color: "orange" },
  { key: "service", label: "服务管理", icon: <IconSetting />, color: "purple" },
  { key: "system", label: "系统管理", icon: <IconShield />, color: "red" },
  { key: "audit", label: "审计日志", icon: <IconActivity />, color: "teal" },
];

export default function PermissionTemplatesPage() {
  const { setBreadcrumbs } = useUIStore();

  const [_searchKeyword, _setSearchKeyword] = useState("");
  const [selectedCategory, setSelectedCategory] = useState<string>("");
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [isPermissionModalVisible, setIsPermissionModalVisible] = useState(false);
  const [editingTemplate, setEditingTemplate] = useState<PermissionTemplate | null>(null);
  const [selectedPermissions, setSelectedPermissions] = useState<string[]>([]);
  const [formApi, setFormApi] = useState<any>(null);
  const form = formApi;

  useEffect(() => {
    setBreadcrumbs([
      { text: "首页", href: "/", icon: "🏠" },
      { text: "系统管理", icon: "⚙️" },
      { text: "权限模板", icon: "🛡️" },
    ]);
  }, [setBreadcrumbs]);

  // 获取权限模板列表
  const {
    data: templatesData,
    isLoading: templatesLoading,
    refetch: refetchTemplates,
  } = useQuery({
    queryKey: [
      "permission-templates",
      {
        page: 1,
        pageSize: 20,
        category: selectedCategory || undefined,
        search: _searchKeyword || undefined,
      },
    ],
    queryFn: async () => {
      try {
        const result = await permissionTemplateService.getTemplates({
          page: 1,
          pageSize: 20,
          category: (selectedCategory as any) || undefined,
          search: _searchKeyword || undefined,
        });
        console.log("权限模板数据:", result);
        return result;
      } catch (error) {
        console.error("获取权限模板失败:", error);
        return {
          success: false,
          data: { templates: [], pagination: { page: 1, pageSize: 20, total: 0, totalPages: 0 } },
        };
      }
    },
  });

  // 获取所有权限
  const { data: permissionsData, isLoading: permissionsLoading } = useQuery({
    queryKey: ["permission-groups"],
    queryFn: async () => {
      try {
        const result = await permissionTemplateService.getPermissionGroups();
        console.log("权限数据:", result);
        return result;
      } catch (error) {
        console.error("获取权限失败:", error);
        return { success: false, data: [] } as any;
      }
    },
  });

  // 获取模板使用统计
  const { data: templateStats } = useQuery({
    queryKey: ["permission-template-stats"],
    queryFn: async () => {
      try {
        const result = await permissionTemplateService.getTemplateStats();
        if (result && result.success && result.data) {
          const { totalTemplates, activeTemplates, systemTemplates, customTemplates } =
            result.data as any;
          return {
            total: totalTemplates || 0,
            default: systemTemplates || 0,
            custom: customTemplates || 0,
            active: activeTemplates || 0,
          };
        }
        return { total: 0, default: 0, custom: 0, active: 0 };
      } catch (error) {
        console.error("获取模板统计失败:", error);
        return { total: 0, default: 0, custom: 0, active: 0 };
      }
    },
  });

  // 创建/更新模板
  const saveTemplateMutation = {
    mutate: async (data: any) => {
      try {
        if (editingTemplate) {
          await permissionTemplateService.updateTemplate(editingTemplate.id, data);
          Toast.success("模板更新成功");
        } else {
          await permissionTemplateService.createTemplate(data);
          Toast.success("模板创建成功");
        }

        setIsModalVisible(false);
        setEditingTemplate(null);
        form?.reset();
        setSelectedPermissions([]);
        refetchTemplates();
      } catch (error: any) {
        console.error("保存模板失败:", error);
        Toast.error(error.message || "保存模板失败");
      }
    },
    isPending: false,
  };

  // 删除模板
  const deleteTemplateMutation = {
    mutate: async (templateId: string) => {
      try {
        await permissionTemplateService.deleteTemplate(templateId);
        Toast.success("模板删除成功");
        refetchTemplates();
      } catch (error: any) {
        console.error("删除模板失败:", error);
        Toast.error(error.message || "删除模板失败");
      }
    },
  };

  // 复制模板
  const copyTemplateMutation = {
    mutate: async (templateId: string) => {
      try {
        await permissionTemplateService.copyTemplate(templateId);
        Toast.success("模板复制成功");
        refetchTemplates();
      } catch (error: any) {
        console.error("复制模板失败:", error);
        Toast.error(error.message || "复制模板失败");
      }
    },
  };

  const handleSearch = (value: string) => {
    _setSearchKeyword(value);
  };

  const handleCreate = () => {
    setEditingTemplate(null);
    setSelectedPermissions([]);
    form.reset();
    setIsModalVisible(true);
  };

  const handleEdit = (template: PermissionTemplate) => {
    setEditingTemplate(template);
    setSelectedPermissions(template.permissions);
    form.setValues({
      name: template.name,
      description: template.description,
      category: template.category,
    });
    setIsModalVisible(true);
  };

  const handleDelete = (template: PermissionTemplate) => {
    if (template.isDefault || template.isSystem) {
      Toast.error("默认模板和系统模板不能删除");
      return;
    }
    deleteTemplateMutation.mutate(template.id);
  };

  const handleCopy = (template: PermissionTemplate) => {
    copyTemplateMutation.mutate(template.id);
  };

  const handleSave = () => {
    form
      ?.validate()
      .then((values: any) => {
        if (selectedPermissions.length === 0) {
          Toast.error("请至少选择一个权限");
          return;
        }

        const templateData = {
          ...values,
          permissions: selectedPermissions,
        };

        saveTemplateMutation.mutate(templateData);
      })
      .catch(() => {
        Toast.error("请检查表单内容");
      });
  };

  const handlePermissionChange = (selectedKeys: any) => {
    setSelectedPermissions(Array.isArray(selectedKeys) ? selectedKeys : [selectedKeys || ""]);
  };

  const getPermissionTree = () => {
    if (!permissionsData?.success || !permissionsData?.data || !Array.isArray(permissionsData.data))
      return [];

    return permissionsData.data.map((group: PermissionGroup) => ({
      label: group.label,
      value: group.key,
      key: group.key,
      children: group.permissions.map((permission: PermissionDetail) => ({
        label: permission.name,
        value: permission.key,
        key: permission.key,
        description: permission.description,
      })),
    }));
  };

  const renderPermissionDescription = (permissions: string[]) => {
    if (!permissionsData?.success || !permissionsData?.data || !Array.isArray(permissionsData.data))
      return "";

    const grouped: { [key: string]: { category: string; color: string; permissions: string[] } } =
      {};

    permissionsData.data.forEach((group: PermissionGroup) => {
      const category = permissionCategories.find(cat => cat.key === group.key);
      const groupPermissions = group.permissions
        .filter((permission: PermissionDetail) => permissions.includes(permission.key))
        .map((permission: PermissionDetail) => permission.name);

      if (groupPermissions.length > 0) {
        grouped[group.key] = {
          category: category?.label || group.label,
          color: category?.color || "gray",
          permissions: groupPermissions,
        };
      }
    });

    return Object.entries(grouped).map(([groupName, data]) => (
      <div key={groupName} className="mb-2">
        <Text strong style={{ color: `var(--semi-color-${data.color})` }}>
          {data.category}：
        </Text>
        <Text size="small">{data.permissions.join(", ")}</Text>
      </div>
    ));
  };

  const columns = [
    {
      title: "模板信息",
      key: "templateInfo",
      width: 250,
      render: (_: any, record: PermissionTemplate) => (
        <div className="flex items-start space-x-3">
          <Avatar
            size="small"
            style={{
              backgroundColor: record.isDefault
                ? "var(--semi-color-warning)"
                : "var(--semi-color-primary)",
            }}
          >
            <IconShield />
          </Avatar>
          <div className="flex-1 min-w-0">
            <div className="flex items-center space-x-2">
              <Text strong className="truncate">
                {record.name}
              </Text>
              {record.isDefault && (
                <Badge count="默认" style={{ backgroundColor: "var(--semi-color-warning)" }} />
              )}
            </div>
            <Text type="secondary" size="small" className="truncate">
              {record.description}
            </Text>
            <div className="flex items-center space-x-2 mt-1">
              <Tag
                size="small"
                color={
                  (permissionCategories.find(cat => cat.key === record.category)?.color as any) ||
                  "gray"
                }
              >
                {permissionCategories.find(cat => cat.key === record.category)?.label ||
                  record.category}
              </Tag>
            </div>
          </div>
        </div>
      ),
    },
    {
      title: "权限详情",
      key: "permissions",
      width: 300,
      render: (_: any, record: PermissionTemplate) => (
        <div className="space-y-1">
          <div className="flex items-center space-x-2">
            <IconKey size="small" />
            <Text strong>{record.permissions.length} 项权限</Text>
          </div>
          <div className="max-h-20 overflow-y-auto">
            {renderPermissionDescription(record.permissions)}
          </div>
        </div>
      ),
    },
    {
      title: "使用情况",
      key: "usage",
      width: 120,
      render: (_: any, record: PermissionTemplate) => (
        <div className="space-y-1">
          <div className="flex items-center space-x-2">
            <IconUserGroup size="small" />
            <Text strong>{record.usageCount}</Text>
          </div>
          <Text type="secondary" size="small">
            个角色使用
          </Text>
        </div>
      ),
    },
    {
      title: "更新时间",
      dataIndex: "updatedAt",
      key: "updatedAt",
      width: 140,
      render: (text: string) => (
        <div className="space-y-1">
          <Text size="small">{new Date(text).toLocaleDateString()}</Text>
          <Text type="secondary" size="small">
            {new Date(text).toLocaleTimeString()}
          </Text>
        </div>
      ),
    },
    {
      title: "操作",
      key: "actions",
      width: 140,
      render: (_: any, record: PermissionTemplate) => (
        <Space>
          <Tooltip content="查看权限详情">
            <Button
              theme="borderless"
              type="tertiary"
              icon={<IconEyeOpened />}
              size="small"
              onClick={() => {
                setEditingTemplate(record);
                setSelectedPermissions(record.permissions);
                setIsPermissionModalVisible(true);
              }}
            />
          </Tooltip>
          <Tooltip content="复制模板">
            <Button
              theme="borderless"
              type="secondary"
              icon={<IconCopy />}
              size="small"
              onClick={() => handleCopy(record)}
            />
          </Tooltip>
          <Tooltip content="编辑模板">
            <Button
              theme="borderless"
              type="primary"
              icon={<IconEdit />}
              size="small"
              onClick={() => handleEdit(record)}
            />
          </Tooltip>
          {!record.isDefault && (
            <Popconfirm
              title="确定删除此模板吗？"
              content="此操作不可撤销，使用该模板的角色不受影响"
              onConfirm={() => handleDelete(record)}
              okText="确定"
              cancelText="取消"
            >
              <Tooltip content="删除模板">
                <Button theme="borderless" type="danger" icon={<IconDelete />} size="small" />
              </Tooltip>
            </Popconfirm>
          )}
        </Space>
      ),
    },
  ];

  return (
    <div className="space-y-6">
      <div className="page-header">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="page-title">权限模板管理</h1>
            <p className="page-subtitle">统一管理角色权限模板，简化权限配置流程</p>
          </div>
          <Button type="primary" icon={<IconPlus />} onClick={handleCreate}>
            创建模板
          </Button>
        </div>
      </div>

      {/* 统计卡片 */}
      {templateStats && (
        <Row gutter={[16, 16]}>
          <Col span={24} md={6}>
            <Card
              bodyStyle={{ padding: "20px" }}
              style={{
                background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
                color: "white",
              }}
            >
              <div className="flex items-center justify-between">
                <div>
                  <Text style={{ color: "rgba(255,255,255,0.8)" }}>模板总数</Text>
                  <div className="text-2xl font-bold mt-1">{templateStats?.total || 0}</div>
                </div>
                <Avatar size="large" style={{ backgroundColor: "rgba(255,255,255,0.2)" }}>
                  <IconShield />
                </Avatar>
              </div>
            </Card>
          </Col>
          <Col span={24} md={6}>
            <Card
              bodyStyle={{ padding: "20px" }}
              style={{
                background: "linear-gradient(135deg, #f093fb 0%, #f5576c 100%)",
                color: "white",
              }}
            >
              <div className="flex items-center justify-between">
                <div>
                  <Text style={{ color: "rgba(255,255,255,0.8)" }}>默认模板</Text>
                  <div className="text-2xl font-bold mt-1">{templateStats?.default || 0}</div>
                </div>
                <Avatar size="large" style={{ backgroundColor: "rgba(255,255,255,0.2)" }}>
                  <IconLock />
                </Avatar>
              </div>
            </Card>
          </Col>
          <Col span={24} md={6}>
            <Card
              bodyStyle={{ padding: "20px" }}
              style={{
                background: "linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)",
                color: "white",
              }}
            >
              <div className="flex items-center justify-between">
                <div>
                  <Text style={{ color: "rgba(255,255,255,0.8)" }}>使用中</Text>
                  <div className="text-2xl font-bold mt-1">{templateStats?.active || 0}</div>
                </div>
                <Avatar size="large" style={{ backgroundColor: "rgba(255,255,255,0.2)" }}>
                  <IconActivity />
                </Avatar>
              </div>
            </Card>
          </Col>
          <Col span={24} md={6}>
            <Card
              bodyStyle={{ padding: "20px" }}
              style={{
                background: "linear-gradient(135deg, #fa709a 0%, #fee140 100%)",
                color: "white",
              }}
            >
              <div className="flex items-center justify-between">
                <div>
                  <Text style={{ color: "rgba(255,255,255,0.8)" }}>权限总数</Text>
                  <div className="text-2xl font-bold mt-1">
                    {permissionsData?.success && Array.isArray(permissionsData.data)
                      ? permissionsData.data.length
                      : 0}
                  </div>
                </div>
                <Avatar size="large" style={{ backgroundColor: "rgba(255,255,255,0.2)" }}>
                  <IconKey />
                </Avatar>
              </div>
            </Card>
          </Col>
        </Row>
      )}

      <Card>
        {/* 搜索和筛选 */}
        <div className="flex flex-wrap gap-4 mb-4">
          <Input
            prefix={<IconSearch />}
            placeholder="搜索模板名称或描述"
            style={{ width: 280 }}
            onEnterPress={(e: React.KeyboardEvent<HTMLInputElement>) => {
              const target = e.target as HTMLInputElement;
              handleSearch(target.value);
            }}
            showClear
          />
          <Select
            placeholder="权限类别"
            style={{ width: 150 }}
            showClear
            value={selectedCategory}
            onChange={(value: string | number | any[] | Record<string, any> | undefined) =>
              setSelectedCategory(String(value || ""))
            }
          >
            {permissionCategories.map(category => (
              <Select.Option key={category.key} value={category.key}>
                <div className="flex items-center space-x-2">
                  {category.icon}
                  <span>{category.label}</span>
                </div>
              </Select.Option>
            ))}
          </Select>
          <Button icon={<IconRefresh />} onClick={() => refetchTemplates()}>
            刷新
          </Button>
        </div>

        {/* 权限模板表格 */}
        <Table
          columns={columns as any}
          dataSource={templatesData?.success ? (templatesData.data as any).templates : []}
          rowKey="id"
          loading={templatesLoading}
          pagination={{
            currentPage:
              (templatesData?.data as any)?.page ||
              (templatesData?.data as any)?.pagination?.page ||
              1,
            pageSize:
              (templatesData?.data as any)?.pageSize ||
              (templatesData?.data as any)?.pagination?.pageSize ||
              20,
            total:
              (templatesData?.data as any)?.total ||
              (templatesData?.data as any)?.pagination?.total ||
              0,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: true,
          }}
          scroll={{ x: 1200 }}
          empty={
            <Empty
              image={<IconShield style={{ fontSize: 48 }} />}
              title="暂无权限模板"
              description="您可以创建新的权限模板来简化角色权限配置"
            />
          }
        />
      </Card>

      {/* 创建/编辑模板弹窗 */}
      <Modal
        title={
          <div className="flex items-center space-x-2">
            <IconShield />
            <span>{editingTemplate ? "编辑权限模板" : "创建权限模板"}</span>
          </div>
        }
        visible={isModalVisible}
        onCancel={() => {
          setIsModalVisible(false);
          setEditingTemplate(null);
          form?.reset();
          setSelectedPermissions([]);
        }}
        onOk={handleSave}
        confirmLoading={saveTemplateMutation.isPending}
        width={800}
        style={{ maxHeight: "90vh" }}
      >
        <div className="space-y-6">
          {/* 避免复杂泛型导致的“类型实例化过深”，弱化表单泛型推断 */}
          <Form<any> getFormApi={setFormApi} layout="vertical">
            <Row gutter={16}>
              <Col span={12}>
                <Form.Input
                  field="name"
                  label="模板名称"
                  rules={[{ required: true, message: "请输入模板名称" }]}
                  placeholder="请输入模板名称"
                />
              </Col>
              <Col span={12}>
                <Form.Select
                  field="category"
                  label="权限类别"
                  rules={[{ required: true, message: "请选择权限类别" }]}
                  placeholder="请选择权限类别"
                >
                  {permissionCategories.map(category => (
                    <Form.Select.Option key={category.key} value={category.key}>
                      <div className="flex items-center space-x-2">
                        {category.icon}
                        <span>{category.label}</span>
                      </div>
                    </Form.Select.Option>
                  ))}
                </Form.Select>
              </Col>
            </Row>

            <Form.TextArea
              field="description"
              label="模板描述"
              placeholder="请输入模板描述"
              rows={3}
              maxCount={200}
              showClear
            />
          </Form>

          <div>
            <div className="flex items-center justify-between mb-3">
              <Text strong>权限配置</Text>
              <Text type="secondary">已选择 {selectedPermissions.length} 项权限</Text>
            </div>

            <div className="border rounded p-4 max-h-60 overflow-y-auto">
              {permissionsLoading ? (
                <div className="flex justify-center py-4">
                  <Spin />
                </div>
              ) : (
                <Tree
                  treeData={getPermissionTree()}
                  multiple
                  value={selectedPermissions}
                  onChange={handlePermissionChange}
                />
              )}
            </div>
          </div>
        </div>
      </Modal>

      {/* 权限详情查看弹窗 */}
      <Modal
        title={
          <div className="flex items-center space-x-2">
            <IconEyeOpened />
            <span>权限详情 - {editingTemplate?.name}</span>
          </div>
        }
        visible={isPermissionModalVisible}
        onCancel={() => {
          setIsPermissionModalVisible(false);
          setEditingTemplate(null);
          setSelectedPermissions([]);
        }}
        footer={<Button onClick={() => setIsPermissionModalVisible(false)}>关闭</Button>}
        width={700}
      >
        <div className="space-y-4">
          {editingTemplate && (
            <div className="bg-gray-50 p-4 rounded">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Text strong>模板名称：</Text>
                  <Text>{editingTemplate.name}</Text>
                </div>
                <div>
                  <Text strong>权限类别：</Text>
                  <Tag
                    color={
                      (permissionCategories.find(cat => cat.key === editingTemplate.category)
                        ?.color as any) || "gray"
                    }
                  >
                    {permissionCategories.find(cat => cat.key === editingTemplate.category)
                      ?.label || editingTemplate.category}
                  </Tag>
                </div>
                <div className="col-span-2">
                  <Text strong>模板描述：</Text>
                  <Text>{editingTemplate.description || "暂无描述"}</Text>
                </div>
                <div>
                  <Text strong>使用情况：</Text>
                  <Text>{editingTemplate.usageCount} 个角色使用</Text>
                </div>
                <div>
                  <Text strong>创建时间：</Text>
                  <Text>{new Date(editingTemplate.createdAt).toLocaleString()}</Text>
                </div>
              </div>
            </div>
          )}

          <Divider>权限详情 ({selectedPermissions.length} 项)</Divider>

          <div className="space-y-3">{renderPermissionDescription(selectedPermissions)}</div>

          {permissionsData?.success && permissionsData?.data && (
            <div className="border-t pt-4">
              <Text strong>详细权限列表：</Text>
              <div className="grid grid-cols-1 gap-2 mt-2 max-h-40 overflow-y-auto">
                {selectedPermissions.map(permissionKey => {
                  if (!permissionsData?.data || !Array.isArray(permissionsData.data)) return null;

                  for (const group of permissionsData.data) {
                    const permission = group.permissions.find(
                      (p: PermissionDetail) => p.key === permissionKey
                    );
                    if (permission) {
                      return (
                        <div
                          key={permissionKey}
                          className="flex items-center justify-between p-2 bg-gray-50 rounded"
                        >
                          <div>
                            <Text strong size="small">
                              {permission.name}
                            </Text>
                            <div>
                              <Text type="secondary" size="small">
                                {permission.description}
                              </Text>
                            </div>
                          </div>
                          <Tag size="small">{group.label}</Tag>
                        </div>
                      );
                    }
                  }
                  return null;
                })}
              </div>
            </div>
          )}
        </div>
      </Modal>
    </div>
  );
}
