import React, { useEffect, useMemo, useState } from "react";
import { useNavigate } from "react-router-dom";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import {
  Row,
  Col,
  Card,
  Button,
  Typography,
  Spin,
  Empty,
  Space,
  Tag,
  Toast,
  Modal,
  List,
  Divider,
} from "@douyinfe/semi-ui";
import {
  IconUser,
  IconKey,
  IconArticle,
  IconShield,
  IconSetting,
  IconActivity,
  IconBell,
  IconAlertTriangle,
  IconTickCircle,
  IconClock,
  IconRefresh,
  IconServer,
  IconSend,
  IconWifi,
  IconUserGroup,
  IconHelpCircle,
  IconPlayCircle,
  IconFile,
  IconMore,
} from "@douyinfe/semi-icons";
import { useUIStore } from "@/stores";
import { auditService } from "@/services/audit";
import { slaService } from "@/services/sla";
import { monitorService } from "@/services/monitor";
import { getA<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/services/api-key";
import { userService } from "@/services/user";

const { Title, Text } = Typography;

// --- Reusable Components ---

interface KpiCardProps {
  title: string;
  value: string | number;
  trend?: React.ReactNode;
  icon: React.ReactNode;
  onClick?: () => void;
  loading?: boolean;
  color?: "blue" | "green" | "red" | "orange" | "purple" | "grey";
}

const KpiCard: React.FC<KpiCardProps> = ({
  title,
  value,
  trend,
  icon,
  onClick,
  loading,
  color = "blue",
}) => {
  const cardStyle = `ops-stats-card ops-gradient-card ${color} h-full flex flex-col`;
  return (
    <div
      className={cardStyle}
      onClick={onClick}
      style={{ cursor: onClick ? "pointer" : "default" }}
    >
      {loading ? (
        <div className="flex items-center justify-center h-full">
          <Spin />
        </div>
      ) : (
        <>
          <div className="ops-stats-card-title">{title}</div>
          <div className="ops-stats-card-value">{value}</div>
          {trend && <div className="ops-stats-card-trend">{trend}</div>}
          <div className="ops-stats-card-icon">{icon}</div>
        </>
      )}
    </div>
  );
};

// --- Main Dashboard Page ---

export default function AdminDashboardPage() {
  const { setBreadcrumbs } = useUIStore();
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const [lastRefreshed, setLastRefreshed] = useState(new Date());

  useEffect(() => {
    setBreadcrumbs([
      { text: "首页", href: "/", icon: "🏠" },
      { text: "系统管理", icon: "⚙️" },
      { text: "管理概览", icon: "📊" },
    ]);
  }, [setBreadcrumbs]);

  const handleRefresh = () => {
    Toast.info("正在刷新页面数据...");
    queryClient.invalidateQueries();
    setLastRefreshed(new Date());
  };

  // --- Data Fetching ---

  const { data: slaDashboard, isLoading: slaLoading } = useQuery({
    queryKey: ["sla-dashboard"],
    queryFn: () => slaService.getSlaPerformanceDashboard().then(res => res.data),
  });

  const { data: slaRisks, isLoading: slaRisksLoading } = useQuery({
    queryKey: ["sla-risks"],
    queryFn: () => slaService.getSlaRiskAlerts({ limit: 5 }).then(res => res.data),
  });

  const { data: recentAuditLogs, isLoading: auditLoading } = useQuery({
    queryKey: ["recent-critical-audit-logs"],
    queryFn: () =>
      auditService
        .getAuditLogs({
          page: 1,
          limit: 10
        })
        .then(res => res.data?.logs || []),
  });

  const { data: apiKeyList, isLoading: apiKeyLoading } = useQuery({
    queryKey: ["api-keys-all"],
    queryFn: () => getApiKeys({ page: 1, limit: 1000 }).then(res => res.data?.apiKeys || []),
  });

  const { data: highPrivilegeUsers, isLoading: usersLoading } = useQuery({
    queryKey: ["users-high-privilege"],
    // In a real scenario, this should be a dedicated API endpoint
    queryFn: async () => {
      const res = await userService.getUsers({ page: 1, limit: 1000 });
      const admins = res.data.users.filter(u => u.role?.name === "Super Admin");
      return {
        count: admins.length,
        change: 0, // Mocked data
      };
    },
  });

  // Mock data for metrics not yet available from backend
  const apiErrorRate = { value: "0.12%", trend: "stable" };
  const failedTasks = { count: 2, lastFailed: "邮件发送任务" };

  // --- Memoized Computations for KPIs ---

  const kpiData = useMemo(() => {
    const slaCompliance = slaDashboard?.performance.totalActiveServices
      ? (slaDashboard.performance.withinSla / slaDashboard.performance.totalActiveServices) * 100
      : 100;

    const atRiskOrBreached =
      (slaDashboard?.performance.approachingBreach || 0) +
      (slaDashboard?.performance.breached || 0);

    const expiringApiKeys =
      apiKeyList?.filter((key: any) => {
        if (!key.expiresAt) return false;
        const diff = new Date(key.expiresAt).getTime() - new Date().getTime();
        return diff > 0 && diff < 7 * 24 * 60 * 60 * 1000;
      }).length || 0;

    return {
      slaCompliance: {
        value: `${slaCompliance.toFixed(1)}%`,
        trend: `近7天`,
      },
      atRiskOrBreached: {
        value: atRiskOrBreached,
        trend: `${slaDashboard?.performance.breached || 0} 已违约`,
      },
      expiringApiKeys: {
        value: expiringApiKeys,
        trend: `≤7天内到期`,
      },
      highPrivilegeUsers: {
        value: highPrivilegeUsers?.count ?? "-",
        trend: `近7天 ${(highPrivilegeUsers?.change ?? 0) >= 0 ? "+" : ""}${highPrivilegeUsers?.change ?? 0}`,
      },
    };
  }, [slaDashboard, apiKeyList, highPrivilegeUsers]);

  const isLoading = slaLoading || slaRisksLoading || auditLoading || apiKeyLoading || usersLoading;

  // --- Render Functions ---

  const renderKpiCards = () => (
    <Row gutter={[16, 16]}>
      <Col span={8}>
        <KpiCard
          title="SLA 合规率"
          value={kpiData.slaCompliance.value}
          trend={kpiData.slaCompliance.trend}
          icon={<IconTickCircle />}
          color="green"
          onClick={() => navigate("/services?filter=sla_at_risk")}
          loading={slaLoading}
        />
      </Col>
      <Col span={8}>
        <KpiCard
          title="逼近/已违约工单"
          value={kpiData.atRiskOrBreached.value}
          trend={kpiData.atRiskOrBreached.trend}
          icon={<IconAlertTriangle />}
          color="red"
          onClick={() => navigate("/admin/sla-monitoring")}
          loading={slaLoading}
        />
      </Col>
      <Col span={8}>
        <KpiCard
          title="API 错误率 (1h)"
          value={apiErrorRate.value}
          trend={<Text type="secondary">模拟数据</Text>}
          icon={<IconWifi />}
          color="orange"
          onClick={() => navigate("/admin/system-monitoring")}
          loading={false}
        />
      </Col>
      <Col span={8}>
        <KpiCard
          title="队列/任务失败 (24h)"
          value={failedTasks.count}
          trend={<Text type="secondary">模拟数据</Text>}
          icon={<IconSend />}
          color="orange"
          onClick={() => navigate("/admin/tasks")}
          loading={false}
        />
      </Col>
      <Col span={8}>
        <KpiCard
          title="高权限用户数"
          value={kpiData.highPrivilegeUsers.value}
          trend={kpiData.highPrivilegeUsers.trend}
          icon={<IconUserGroup />}
          color="purple"
          onClick={() => navigate("/admin/users?role=Super+Admin")}
          loading={usersLoading}
        />
      </Col>
      <Col span={8}>
        <KpiCard
          title="即将过期的 API Key"
          value={kpiData.expiringApiKeys.value}
          trend={kpiData.expiringApiKeys.trend}
          icon={<IconKey />}
          color="red"
          onClick={() => navigate("/admin/api-keys?sort=expiresAt")}
          loading={apiKeyLoading}
        />
      </Col>
    </Row>
  );

  const renderTodoList = () => (
    <Card title="待处理事项" style={{ height: "100%" }}>
      {isLoading ? (
        <Spin />
      ) : (
        <List
          dataSource={[
            { title: "待指派工单", count: 0, link: "/services?status=OPEN" },
            {
              title: "即将违约的SLA工单",
              count: slaRisks?.total || 0,
              link: "/admin/sla-monitoring",
            },
            {
              title: "即将过期的API Key",
              count: kpiData.expiringApiKeys.value,
              link: "/admin/api-keys?sort=expiresAt",
            },
            {
              title: "最近失败的任务",
              count: failedTasks.count,
              link: "/admin/tasks",
              action: () => {
                Toast.info("重试功能待实现");
              },
            },
          ]}
          renderItem={item => (
            <List.Item
              main={
                <div>
                  <Text>{item.title}</Text>
                  {item.count > 0 && (
                    <Tag color="red" style={{ marginLeft: 8 }}>
                      {item.count}
                    </Tag>
                  )}
                </div>
              }
              extra={
                <Space>
                  {item.action && (
                    <Button size="small" type="secondary" onClick={item.action}>
                      重试
                    </Button>
                  )}
                  <Button size="small" onClick={() => navigate(item.link)}>
                    查看/处理
                  </Button>
                </Space>
              }
            />
          )}
          split={false}
          size="small"
        />
      )}
    </Card>
  );

  const renderRecentChanges = () => (
    <Card
      title="最近重要变更"
      style={{ height: "100%" }}
      headerExtraContent={
        <Button size="small" theme="borderless" onClick={() => navigate("/admin/audit-logs")}>
          查看全部
        </Button>
      }
    >
      {auditLoading ? (
        <Spin />
      ) : (
        <List
          dataSource={recentAuditLogs}
          renderItem={(log: any) => (
            <List.Item
              main={
                <div>
                  <Text strong>{log.action}</Text>
                  <Text type="secondary" size="small" style={{ marginLeft: 8 }}>
                    ({log.resource})
                  </Text>
                </div>
              }
              extra={<Text type="tertiary">{new Date(log.createdAt).toLocaleString()}</Text>}
            />
          )}
          split={false}
          size="small"
        />
      )}
    </Card>
  );

  const renderQuickActions = () => {
    const actions = [
      {
        title: "新建用户",
        icon: <IconUser />,
        action: () => {
          navigate("/admin/users/new");
        },
      },
      {
        title: "生成/轮换 API Key",
        icon: <IconKey />,
        action: () => {
          navigate("/admin/api-keys");
        },
      },
      {
        title: "导出审计日志",
        icon: <IconFile />,
        action: () => {
          Toast.info("导出最近7天日志...");
        },
      },
      {
        title: "清理缓存/重载配置",
        icon: <IconRefresh />,
        action: () => {
          Modal.confirm({
            title: "确认操作",
            content: "确定要清理缓存并重新加载配置吗？",
            onOk: () => {
              Toast.success("操作已执行");
            },
          });
        },
      },
    ];
    return (
      <Card title="快捷操作">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {actions.map((action, index) => (
            <Button
              key={index}
              icon={action.icon}
              theme="light"
              type="secondary"
              size="large"
              onClick={action.action}
              style={{
                height: 80,
                display: "flex",
                flexDirection: "column",
                alignItems: "center",
                justifyContent: "center",
                gap: 8,
              }}
            >
              <div className="font-medium">{action.title}</div>
            </Button>
          ))}
        </div>
      </Card>
    );
  };

  return (
    <div className="space-y-6">
      {/* 1. Top Status Bar */}
      <Card bodyStyle={{ padding: "12px 24px" }}>
        <Row align="middle" justify="space-between">
          <Col>
            <Space>
              <Tag color="green" size="large">
                <IconWifi /> 系统状态正常
              </Tag>
              <Divider />
              <Text type="secondary">
                <IconClock style={{ marginRight: 4 }} />
                数据更新于: {lastRefreshed.toLocaleTimeString()}
              </Text>
            </Space>
          </Col>
          <Col>
            <Space>
              <Text strong>
                未处理任务:{" "}
                {(slaRisks?.total || 0) + kpiData.expiringApiKeys.value + failedTasks.count}
              </Text>
              <Button icon={<IconRefresh />} onClick={handleRefresh}>
                刷新
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 2. Metrics Area */}
      <div className="ops-card">
        <div className="ops-card-header">
          <Title heading={5} className="m-0">
            关键指标
          </Title>
        </div>
        <div className="ops-card-body">{renderKpiCards()}</div>
      </div>

      {/* 3. List Area */}
      <Row gutter={16}>
        <Col span={12}>{renderTodoList()}</Col>
        <Col span={12}>{renderRecentChanges()}</Col>
      </Row>

      {/* 4. Quick Actions */}
      {renderQuickActions()}
    </div>
  );
}
