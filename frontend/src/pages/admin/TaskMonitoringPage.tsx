import React, { useState, useEffect } from "react";
import {
  Table,
  Tag,
  Button,
  Space,
  Modal,
  Spin,
  Typography,
  Toast,
  Card,
  Select,
  Empty,
} from "@douyinfe/semi-ui";
import { IconRefresh, IconEyeOpened, IconPlayCircle } from "@douyinfe/semi-icons";
import {
  useQuery,
  useQueryClient,
  useMutation,
  UseQueryOptions,
  UseMutationOptions,
} from "@tanstack/react-query";
import { useUIStore } from "@/stores";
import { taskService, TaskExecution, PaginatedTasks } from "@/services/task.service";

const { Title, Paragraph, Text } = Typography;

export default function TaskMonitoringPage() {
  const { setBreadcrumbs } = useUIStore();
  const queryClient = useQueryClient();

  const [detailVisible, setDetailVisible] = useState(false);
  const [selectedTask, setSelectedTask] = useState<TaskExecution | null>(null);
  const [pagination, setPagination] = useState({ currentPage: 1, pageSize: 10 });
  const [statusFilter, setStatusFilter] = useState("");

  useEffect(() => {
    setBreadcrumbs([{ text: "首页", href: "/" }, { text: "系统管理" }, { text: "任务监控" }]);
  }, [setBreadcrumbs]);

  const queryKey = ["task-executions", pagination.currentPage, pagination.pageSize, statusFilter];

  const { data, isLoading, isError } = useQuery<{ data: PaginatedTasks }, Error>({
    queryKey,
    queryFn: () =>
      taskService.getExecutions(pagination.currentPage, pagination.pageSize, statusFilter),
    staleTime: 1000 * 60 * 5, // 5分钟缓存
  });

  const detailQuery = useQuery<{ data: TaskExecution }, Error>({
    queryKey: ["task-detail", selectedTask?.id],
    queryFn: () => taskService.getExecutionDetails(selectedTask!.id),
    enabled: !!selectedTask?.id && detailVisible,
  });

  const retryMutation = useMutation<{ message: string }, Error, string>({
    mutationFn: (id: string) => taskService.retryExecution(id),
    onSuccess: res => {
      Toast.success(res.message);
      queryClient.invalidateQueries({ queryKey });
    },
    onError: error => {
      Toast.error(error.message || "重试失败");
    },
  });

  const handleTableChange = (pagination: any) => {
    setPagination({ currentPage: pagination.current, pageSize: pagination.pageSize });
  };

  const handleShowDetails = (record: TaskExecution) => {
    setSelectedTask(record);
    setDetailVisible(true);
  };

  const columns = [
    { title: "任务名称", dataIndex: "taskName", key: "taskName", width: 200 },
    {
      title: "状态",
      dataIndex: "status",
      key: "status",
      width: 120,
      render: (status: string) => {
        const color = status === "SUCCESS" ? "green" : status === "FAILED" ? "red" : "blue";
        return <Tag color={color}>{status}</Tag>;
      },
    },
    {
      title: "开始时间",
      dataIndex: "startedAt",
      key: "startedAt",
      render: (ts: string) => new Date(ts).toLocaleString(),
    },
    { title: "持续时间 (ms)", dataIndex: "duration", key: "duration", width: 150 },
    {
      title: "操作",
      key: "action",
      width: 200,
      render: (_: any, record: TaskExecution) => (
        <Space>
          <Button icon={<IconEyeOpened />} size="small" onClick={() => handleShowDetails(record)}>
            详情
          </Button>
          {record.status === "FAILED" && (
            <Button
              icon={<IconPlayCircle />}
              type="danger"
              size="small"
              onClick={() => retryMutation.mutate(record.id)}
              loading={retryMutation.isPending && retryMutation.variables === record.id}
            >
              重试
            </Button>
          )}
        </Space>
      ),
    },
  ];

  const taskDetail = detailQuery.data?.data;

  return (
    <div className="space-y-4">
      <div className="ops-page-header">
        <Title heading={3}>任务监控</Title>
        <Paragraph>查看系统定时任务的执行状态，并对失败的任务进行管理。</Paragraph>
      </div>

      <Card>
        <div className="flex justify-between mb-4">
          <Select
            placeholder="按状态筛选"
            style={{ width: 150 }}
            onChange={(value: string | number | any[] | Record<string, any> | undefined) => {
              setStatusFilter(value as string);
              setPagination({ ...pagination, currentPage: 1 });
            }}
            onClear={() => setStatusFilter("")}
          >
            <Select.Option value="SUCCESS">成功</Select.Option>
            <Select.Option value="FAILED">失败</Select.Option>
            <Select.Option value="RUNNING">运行中</Select.Option>
          </Select>
          <Button
            icon={<IconRefresh />}
            onClick={() => queryClient.invalidateQueries({ queryKey })}
            loading={isLoading}
          >
            刷新
          </Button>
        </div>
        <Table
          columns={columns}
          dataSource={data?.data?.tasks || []}
          pagination={{
            currentPage: data?.data?.pagination?.page || 1,
            pageSize: data?.data?.pagination?.limit || 10,
            total: data?.data?.pagination?.total || 0,
          }}
          loading={isLoading}
          onChange={handleTableChange}
        />
      </Card>

      <Modal
        title="任务执行详情"
        visible={detailVisible}
        onCancel={() => setDetailVisible(false)}
        footer={<Button onClick={() => setDetailVisible(false)}>关闭</Button>}
        width={800}
      >
        {detailQuery.isLoading ? (
          <Spin />
        ) : taskDetail ? (
          <div className="space-y-4">
            <div>
              <Text strong>任务名:</Text> {taskDetail.taskName}
            </div>
            <div>
              <Text strong>状态:</Text>{" "}
              <Tag color={taskDetail.status === "SUCCESS" ? "green" : "red"}>
                {taskDetail.status}
              </Tag>
            </div>
            <div>
              <Text strong>开始时间:</Text> {new Date(taskDetail.startedAt).toLocaleString()}
            </div>
            <div>
              <Text strong>结束时间:</Text>{" "}
              {taskDetail.endedAt ? new Date(taskDetail.endedAt).toLocaleString() : "-"}
            </div>
            <div>
              <Text strong>持续时间:</Text> {taskDetail.duration}ms
            </div>
            <div>
              <Text strong>输出:</Text>
              <pre className="p-2 bg-gray-100 rounded text-xs mt-1 max-h-40 overflow-y-auto">
                {taskDetail.output || "无输出"}
              </pre>
            </div>
            <div>
              <Text strong>错误:</Text>
              <pre className="p-2 bg-red-50 text-red-700 rounded text-xs mt-1 max-h-60 overflow-y-auto">
                {taskDetail.error || "无错误"}
              </pre>
            </div>
          </div>
        ) : (
          <Empty description="无法加载任务详情" />
        )}
      </Modal>
    </div>
  );
}
