import React from 'react';
import {
  Card,
  Tabs,
  TabPane,
  Typography,
  Empty,
  Space,
  Button
} from '@douyinfe/semi-ui';
import {
  IconSetting,
  IconCode,
  IconActivity,
  IconPieChartStroked
} from '@douyinfe/semi-icons';

const { Title, Paragraph } = Typography;

// AI配置管理组件
const AIConfigurationTab = () => {
  return (
    <div>
      <div style={{ marginBottom: 16 }}>
        <Title heading={4}>AI配置管理</Title>
      </div>
      <Card>
        <Empty 
          description="AI配置管理功能开发中..." 
          image={<IconSetting size="large" />}
        />
      </Card>
    </div>
  );
};

// 提示词模板管理组件
const PromptTemplateTab = () => {
  return (
    <div>
      <div style={{ marginBottom: 16 }}>
        <Title heading={4}>提示词模板管理</Title>
      </div>
      <Card>
        <Empty 
          description="提示词模板管理功能开发中..." 
          image={<IconCode size="large" />}
        />
      </Card>
    </div>
  );
};

// 分析历史组件
const AnalysisHistoryTab = () => {
  return (
    <div>
      <div style={{ marginBottom: 16 }}>
        <Title heading={4}>AI分析历史</Title>
      </div>
      <Card>
        <Empty 
          description="AI分析历史功能开发中..." 
          image={<IconActivity size="large" />}
        />
      </Card>
    </div>
  );
};

// 统计报告组件
const StatisticsTab = () => {
  return (
    <div>
      <div style={{ marginBottom: 16 }}>
        <Title heading={4}>AI使用统计</Title>
      </div>
      <Card>
        <Empty 
          description="AI统计报告功能开发中..." 
          image={<IconPieChartStroked size="large" />}
        />
      </Card>
    </div>
  );
};

export default function AIManagementPage() {
  return (
    <div style={{ padding: 24 }}>
      <div style={{ marginBottom: 24 }}>
        <Title heading={2}>AI集成管理</Title>
        <Paragraph type="secondary">
          管理AI配置、提示词模板、分析历史和系统统计
        </Paragraph>
      </div>

      <Card>
        <div style={{ textAlign: 'center', padding: 40 }}>
          <IconSetting size="extra-large" style={{ color: '#1890ff', marginBottom: 16 }} />
          <Title heading={3}>AI集成管理系统</Title>
          <Paragraph type="secondary" style={{ marginBottom: 24 }}>
            AI功能已成功集成到工单创建流程中，管理界面正在开发中
          </Paragraph>
          
          <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: 16, marginTop: 32 }}>
            <Card 
              title="AI配置管理" 
              headerStyle={{ textAlign: 'center' }}
              bodyStyle={{ textAlign: 'center', padding: 20 }}
            >
              <IconSetting size="large" style={{ color: '#52c41a', marginBottom: 8 }} />
              <Paragraph type="secondary">
                管理AI提供商配置、API密钥和模型参数
              </Paragraph>
            </Card>
            
            <Card 
              title="提示词模板" 
              headerStyle={{ textAlign: 'center' }}
              bodyStyle={{ textAlign: 'center', padding: 20 }}
            >
              <IconCode size="large" style={{ color: '#1890ff', marginBottom: 8 }} />
              <Paragraph type="secondary">
                创建和管理AI分析的提示词模板
              </Paragraph>
            </Card>
            
            <Card 
              title="分析历史" 
              headerStyle={{ textAlign: 'center' }}
              bodyStyle={{ textAlign: 'center', padding: 20 }}
            >
              <IconActivity size="large" style={{ color: '#fa8c16', marginBottom: 8 }} />
              <Paragraph type="secondary">
                查看AI分析请求的历史记录和结果
              </Paragraph>
            </Card>
            
            <Card 
              title="统计报告" 
              headerStyle={{ textAlign: 'center' }}
              bodyStyle={{ textAlign: 'center', padding: 20 }}
            >
              <IconPieChartStroked size="large" style={{ color: '#722ed1', marginBottom: 8 }} />
              <Paragraph type="secondary">
                AI使用情况统计和性能分析报告
              </Paragraph>
            </Card>
          </div>

          <div style={{ marginTop: 32, padding: 20, backgroundColor: '#f6ffed', borderRadius: 8, border: '1px solid #b7eb8f' }}>
            <Title heading={4} style={{ color: '#52c41a', marginBottom: 8 }}>
              ✅ AI功能已集成
            </Title>
            <Paragraph type="secondary">
              AI智能分析功能已成功集成到工单创建页面中，可以自动分析工单内容并提供分类、优先级等建议。
              管理界面将在后续版本中完善。
            </Paragraph>
          </div>
        </div>
      </Card>

      {/* 隐藏的标签页，为将来扩展预留 */}
      <div style={{ display: 'none' }}>
        <Tabs type="line" defaultActiveKey="config">
          <TabPane tab={
            <span>
              <IconSetting style={{ marginRight: 4 }} />
              AI配置
            </span>
          } itemKey="config">
            <AIConfigurationTab />
          </TabPane>

          <TabPane tab={
            <span>
              <IconCode style={{ marginRight: 4 }} />
              提示词模板
            </span>
          } itemKey="templates">
            <PromptTemplateTab />
          </TabPane>

          <TabPane tab={
            <span>
              <IconActivity style={{ marginRight: 4 }} />
              分析历史
            </span>
          } itemKey="history">
            <AnalysisHistoryTab />
          </TabPane>

          <TabPane tab={
            <span>
              <IconPieChartStroked style={{ marginRight: 4 }} />
              统计报告
            </span>
          } itemKey="stats">
            <StatisticsTab />
          </TabPane>
        </Tabs>
      </div>
    </div>
  );
}
