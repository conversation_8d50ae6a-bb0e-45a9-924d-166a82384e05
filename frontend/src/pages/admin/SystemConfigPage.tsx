import React, { useEffect, useState, useMemo } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import {
  Button,
  Input,
  Select,
  Table,
  Pagination,
  Tag,
  Modal,
  Space,
  Toast,
  Dropdown,
  Card,
  Row,
  Col,
  Tabs,
  Badge,
  Typography,
  Empty,
  Spin,
  Tooltip,
  BackTop,
  Form,
  TextArea,
  InputNumber,
  Switch,
  Collapse,
  Progress,
  Timeline,
  Descriptions,
  Popover,
  SideSheet as Drawer,
} from "@douyinfe/semi-ui";
import {
  IconPlus,
  IconSearch,
  IconRefresh,
  IconEdit,
  IconDelete,
  IconMore,
  IconSetting,
  IconExport,
  IconImport,
  IconDownload,
  IconUpload,
  IconCopy,
  IconEyeOpened,
  IconEyeClosed,
  IconFilter,
  IconHistory,
  IconShield,
  IconTickCircle as IconCheckCircle,
  IconAlertTriangle,
  IconClock,
  IconUser,
  IconFolder,
  IconCode,
  IconMail,
  IconCloud,
  IconCloud as IconDatabase,
  IconBell,
  IconChevronDown,
  IconChevronRight,
  IconClose,
  IconTick,
  IconRefresh2,
  IconUndo as IconRestore,
} from "@douyinfe/semi-icons";
import { useUIStore, useAuthStore } from "@/stores";
import {
  systemConfigService,
  type SystemConfig,
  type SystemConfigGroup,
} from "@/services/system-config";
import { emailTemplateService, type EmailTemplate } from "@/services/email-template";
// Import shared types
type SystemConfigCategory =
  | "GENERAL"
  | "SECURITY"
  | "EMAIL"
  | "SMS"
  | "NOTIFICATION"
  | "STORAGE"
  | "BACKUP"
  | "SYSTEM"
  | "INTEGRATION"
  | "CUSTOM";
type SystemConfigDataType =
  | "STRING"
  | "NUMBER"
  | "BOOLEAN"
  | "JSON"
  | "EMAIL"
  | "URL"
  | "PASSWORD"
  | "TEXTAREA"
  | "SELECT"
  | "MULTI_SELECT";

const { Title, Text } = Typography;
const { TabPane } = Tabs;

// 配置分类图标映射
const categoryIcons = {
  GENERAL: <IconSetting />,
  SECURITY: <IconShield />,
  EMAIL: <IconMail />,
  SMS: <IconBell />,
  NOTIFICATION: <IconBell />,
  STORAGE: <IconCloud />,
  BACKUP: <IconDatabase />,
  SYSTEM: <IconCode />,
  INTEGRATION: <IconFolder />,
  CUSTOM: <IconUser />,
} as const;

// 配置分类标签映射
const categoryLabels = {
  GENERAL: "基本配置",
  SECURITY: "安全设置",
  EMAIL: "邮件配置",
  SMS: "短信配置",
  NOTIFICATION: "通知配置",
  STORAGE: "存储配置",
  BACKUP: "备份配置",
  SYSTEM: "系统配置",
  INTEGRATION: "集成配置",
  CUSTOM: "自定义配置",
} as const;

// 数据类型标签映射
const dataTypeLabels = {
  string: "字符串",
  number: "数字",
  boolean: "布尔值",
  json: "JSON",
  email: "邮箱",
  url: "链接",
  password: "密码",
  textarea: "文本区域",
  select: "选择",
  multi_select: "多选",
} as const;

interface ConfigEditForm {
  key: string;
  value: any;
  description?: string;
  changeReason?: string;
}

interface ConfigTestResult {
  success: boolean;
  message: string;
  details?: any;
}

export default function SystemConfigPage() {
  const { setBreadcrumbs } = useUIStore();
  const { hasPermission } = useAuthStore();
  const queryClient = useQueryClient();

  // 权限检查
  const canView = hasPermission("system:read");
  const canEdit = hasPermission("system:write");
  const canViewSensitive = hasPermission("system:security");
  const canExport = hasPermission("system:export");

  // 状态管理
  const [activeTab, setActiveTab] = useState<string>("all");
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedConfig, setSelectedConfig] = useState<SystemConfig | null>(null);
  const [isEditModalVisible, setIsEditModalVisible] = useState(false);
  const [isHistoryDrawerVisible, setIsHistoryDrawerVisible] = useState(false);
  const [isTestModalVisible, setIsTestModalVisible] = useState(false);
  const [isBatchModalVisible, setIsBatchModalVisible] = useState(false);
  const [isImportModalVisible, setIsImportModalVisible] = useState(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);
  const [batchAction, setBatchAction] = useState<"export" | "reset" | "enable" | "disable">(
    "export"
  );
  const [showSensitiveValues, setShowSensitiveValues] = useState<Record<string, boolean>>({});
  const [editForm, setEditForm] = useState<ConfigEditForm>({
    key: "",
    value: "",
    description: "",
    changeReason: "",
  });

  // 配置测试相关状态
  const [testEmailAddress, setTestEmailAddress] = useState("");
  const [testPhoneNumber, setTestPhoneNumber] = useState("");
  const [backupTestType, setBackupTestType] = useState<string>("");
  const [emailTestResult, setEmailTestResult] = useState<ConfigTestResult | null>(null);
  const [smsTestResult, setSmsTestResult] = useState<ConfigTestResult | null>(null);
  const [backupTestResult, setBackupTestResult] = useState<ConfigTestResult | null>(null);

  // 邮件模板相关状态
  const [activeTemplateTab, setActiveTemplateTab] = useState("system");
  const [isTemplateModalVisible, setIsTemplateModalVisible] = useState(false);
  const [isTemplatePreviewVisible, setIsTemplatePreviewVisible] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<EmailTemplate | null>(null);
  const [templateForm, setTemplateForm] = useState<Partial<EmailTemplate>>({});

  useEffect(() => {
    setBreadcrumbs([
      { text: "首页", href: "/", icon: "🏠" },
      { text: "系统管理", icon: "⚙️" },
      { text: "系统配置", icon: "🔧" },
    ]);
  }, [setBreadcrumbs]);

  // 获取配置分组数据
  const {
    data: configGroupsData,
    isLoading: isLoadingGroups,
    refetch: refetchGroups,
  } = useQuery({
    queryKey: ["system-config-groups"],
    queryFn: () => systemConfigService.getByGroups(),
    enabled: canView,
  });

  // 获取配置分类统计
  const { data: categoriesData } = useQuery({
    queryKey: ["system-config-categories"],
    queryFn: () => systemConfigService.getCategories(),
    enabled: canView,
  });

  // 获取邮件模板数据
  const {
    data: emailTemplatesData,
    isLoading: isLoadingTemplates,
    refetch: refetchTemplates,
  } = useQuery({
    queryKey: ["email-templates-categories"],
    queryFn: () => emailTemplateService.getEmailTemplatesByCategory(),
    enabled: canView,
  });

  // 处理后端返回的数据结构 (SYSTEM/BUSINESS) 转换为前端需要的格式 (system/business)
  const emailTemplates = emailTemplatesData?.data || {};

  // 确保 emailTemplates 的属性都是数组，并处理大小写转换
  const safeEmailTemplates = {
    system: Array.isArray(emailTemplates.SYSTEM) ? emailTemplates.SYSTEM : [],
    business: Array.isArray(emailTemplates.BUSINESS) ? emailTemplates.BUSINESS : [],
  };

  // 获取配置历史记录
  const { data: configHistoryData, isLoading: isLoadingHistory } = useQuery({
    queryKey: ["config-history", selectedConfig?.key],
    queryFn: () => systemConfigService.getConfigHistory(selectedConfig!.key, 1, 10),
    enabled: !!selectedConfig && isHistoryDrawerVisible,
  });

  // 更新配置
  const updateConfigMutation = useMutation({
    mutationFn: ({ key, value }: { key: string; value: any }) =>
      systemConfigService.updateConfig(key, value),
    onSuccess: () => {
      Toast.success("配置更新成功");
      setIsEditModalVisible(false);
      queryClient.invalidateQueries({ queryKey: ["system-config-groups"] });
    },
    onError: (error: any) => {
      Toast.error(error.message || "配置更新失败");
    },
  });

  // 重置配置
  const resetConfigMutation = useMutation({
    mutationFn: (key: string) => systemConfigService.resetToDefault(key),
    onSuccess: () => {
      Toast.success("配置重置成功");
      queryClient.invalidateQueries({ queryKey: ["system-config-groups"] });
    },
    onError: (error: any) => {
      Toast.error(error.message || "配置重置失败");
    },
  });

  // 批量重置配置
  const batchResetMutation = useMutation({
    mutationFn: (keys: string[]) => systemConfigService.batchResetToDefault(keys),
    onSuccess: () => {
      Toast.success("批量重置成功");
      setSelectedRowKeys([]);
      setIsBatchModalVisible(false);
      queryClient.invalidateQueries({ queryKey: ["system-config-groups"] });
    },
    onError: (error: any) => {
      Toast.error(error.message || "批量重置失败");
    },
  });

  // 导出配置
  const exportConfigMutation = useMutation({
    mutationFn: (categories?: string[]) => systemConfigService.exportConfigs(categories),
    onSuccess: data => {
      // 创建下载链接
      const blob = new Blob([JSON.stringify(data.data, null, 2)], {
        type: "application/json",
      });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `system-config-${new Date().toISOString().split("T")[0]}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      window.URL.revokeObjectURL(url);
      Toast.success("配置导出成功");
    },
    onError: (error: any) => {
      Toast.error(error.message || "配置导出失败");
    },
  });

  // 清理缓存
  const clearCacheMutation = useMutation({
    mutationFn: (type?: "all" | "config" | "user" | "permission") =>
      systemConfigService.clearCache(type),
    onSuccess: data => {
      Toast.success(data.data.message);
    },
    onError: (error: any) => {
      Toast.error(error.message || "缓存清理失败");
    },
  });

  // 测试邮件发送
  const testEmailMutation = useMutation({
    mutationFn: (email: string) => systemConfigService.testEmailConfig(email),
    onSuccess: data => {
      setEmailTestResult({
        success: data.data.success,
        message: data.data.message,
        details: data.data.details,
      });
    },
    onError: (error: any) => {
      setEmailTestResult({
        success: false,
        message: error.response?.data?.message || error.message || "邮件测试失败",
      });
    },
  });

  // 测试短信发送
  const testSmsMutation = useMutation({
    mutationFn: (phone: string) => systemConfigService.testConnection("sms", { testPhone: phone }),
    onSuccess: data => {
      setSmsTestResult({
        success: data.data.success,
        message: data.data.message,
        details: data.data.details,
      });
    },
    onError: (error: any) => {
      setSmsTestResult({
        success: false,
        message: error.response?.data?.message || error.message || "短信测试失败",
      });
    },
  });

  // 测试备份功能
  const testBackupMutation = useMutation({
    mutationFn: (type: string) =>
      systemConfigService.testConnection("database", { backupType: type }),
    onSuccess: data => {
      setBackupTestResult({
        success: data.data.success,
        message: data.data.message,
        details: data.data.details,
      });
    },
    onError: (error: any) => {
      setBackupTestResult({
        success: false,
        message: error.response?.data?.message || error.message || "备份测试失败",
      });
    },
  });

  // 处理过滤后的配置数据
  const filteredConfigGroups = useMemo(() => {
    if (!configGroupsData?.data || !Array.isArray(configGroupsData.data)) return [];

    return configGroupsData.data
      .map(group => ({
        ...group,
        configs: Array.isArray(group.configs)
          ? group.configs.filter(config => {
              // 搜索过滤
              if (searchQuery) {
                const query = searchQuery.toLowerCase();
                const matchesSearch =
                  config.key.toLowerCase().includes(query) ||
                  config.label.toLowerCase().includes(query) ||
                  (config.description && config.description.toLowerCase().includes(query));
                if (!matchesSearch) return false;
              }

              // 分类过滤
              if (activeTab !== "all" && config.category !== activeTab) {
                return false;
              }

              // 敏感配置权限检查
              if (config.encrypted && !canViewSensitive) {
                return false;
              }

              return true;
            })
          : [],
      }))
      .filter(group => Array.isArray(group.configs) && group.configs.length > 0);
  }, [configGroupsData?.data, searchQuery, activeTab, canViewSensitive]);

  // 处理编辑配置
  const handleEditConfig = (config: SystemConfig) => {
    setSelectedConfig(config);
    setEditForm({
      key: config.key,
      value: config.value,
      description: config.description,
      changeReason: "",
    });
    setIsEditModalVisible(true);
  };

  // 处理重置配置
  const handleResetConfig = (config: SystemConfig) => {
    Modal.confirm({
      title: "确认重置配置",
      content: `确定要将配置项 "${config.label}" 重置为默认值吗？`,
      onOk: () => resetConfigMutation.mutate(config.key),
    });
  };

  // 处理查看历史记录
  const handleViewHistory = (config: SystemConfig) => {
    setSelectedConfig(config);
    setIsHistoryDrawerVisible(true);
  };

  // 处理切换敏感值显示
  const handleToggleSensitiveValue = (configKey: string) => {
    setShowSensitiveValues(prev => ({
      ...prev,
      [configKey]: !prev[configKey],
    }));
  };

  // 处理批量操作
  const handleBatchAction = () => {
    if (selectedRowKeys.length === 0) {
      Toast.error("请选择要操作的配置项");
      return;
    }

    switch (batchAction) {
      case "export":
        // 获取选中配置的分类
        const selectedConfigs = filteredConfigGroups
          .flatMap(group => group.configs)
          .filter(config => selectedRowKeys.includes(config.key));
        const categories = Array.from(new Set(selectedConfigs.map(c => c.category)));
        exportConfigMutation.mutate(categories);
        break;
      case "reset":
        Modal.confirm({
          title: "确认批量重置",
          content: `确定要重置选中的 ${selectedRowKeys.length} 个配置项吗？`,
          onOk: () => batchResetMutation.mutate(selectedRowKeys),
        });
        break;
    }
  };

  // 处理表单提交
  const handleFormSubmit = () => {
    if (!selectedConfig) return;

    updateConfigMutation.mutate({
      key: selectedConfig.key,
      value: editForm.value,
    });
  };

  // 处理配置测试
  const handleTestEmail = () => {
    if (!testEmailAddress) {
      Toast.error("请输入测试邮箱地址");
      return;
    }
    setEmailTestResult(null);
    testEmailMutation.mutate(testEmailAddress);
  };

  const handleTestSms = () => {
    if (!testPhoneNumber) {
      Toast.error("请输入测试手机号");
      return;
    }
    setSmsTestResult(null);
    testSmsMutation.mutate(testPhoneNumber);
  };

  const handleTestBackup = () => {
    if (!backupTestType) {
      Toast.error("请选择备份类型");
      return;
    }
    setBackupTestResult(null);
    testBackupMutation.mutate(backupTestType);
  };

  // 处理邮件模板操作
  const handlePreviewTemplate = (template: EmailTemplate) => {
    setSelectedTemplate(template);
    setIsTemplatePreviewVisible(true);
  };

  const handleEditTemplate = (template: EmailTemplate) => {
    setSelectedTemplate(template);
    setTemplateForm(template);
    setIsTemplateModalVisible(true);
  };

  const handleTestTemplate = async (template: EmailTemplate) => {
    if (!testEmailAddress) {
      Toast.error("请先在邮件测试面板中输入测试邮箱地址");
      return;
    }

    try {
      Toast.info(`正在向 ${testEmailAddress} 发送测试邮件模板: ${template.name}`);

      // 使用模板变量的示例值
      const sampleVariables: Record<string, any> = {};
      if (template.variables) {
        template.variables.forEach(variable => {
          switch (variable) {
            case "alertLevel":
              sampleVariables[variable] = "高";
              break;
            case "alertTitle":
              sampleVariables[variable] = "系统CPU使用率过高";
              break;
            case "alertMessage":
              sampleVariables[variable] = "CPU使用率达到85%，请及时处理";
              break;
            case "serviceNumber":
              sampleVariables[variable] = "SRV-2024-001";
              break;
            case "serviceTitle":
              sampleVariables[variable] = "服务器性能优化";
              break;
            case "priority":
              sampleVariables[variable] = "高";
              break;
            case "customerName":
              sampleVariables[variable] = "测试客户";
              break;
            case "timestamp":
              sampleVariables[variable] = new Date().toLocaleString();
              break;
            default:
              sampleVariables[variable] = `示例${variable}`;
              break;
          }
        });
      }

      const result = await emailTemplateService.testEmailTemplate(
        template.id,
        testEmailAddress,
        sampleVariables
      );

      if (result.success) {
        Toast.success("模板邮件发送成功！请检查邮箱");
      } else {
        Toast.error(`模板邮件发送失败: ${result.message}`);
      }
    } catch (error: any) {
      Toast.error(`模板邮件发送失败: ${error.response?.data?.message || error.message}`);
    }
  };

  // 渲染配置值
  const renderConfigValue = (config: SystemConfig) => {
    if (config.encrypted && !showSensitiveValues[config.key]) {
      return (
        <div className="flex items-center gap-2">
          <Text type="secondary">••••••••</Text>
          <Button
            theme="borderless"
            size="small"
            icon={<IconEyeOpened />}
            onClick={() => handleToggleSensitiveValue(config.key)}
          />
        </div>
      );
    }

    let displayValue = config.value;
    if (typeof displayValue === "object") {
      displayValue = JSON.stringify(displayValue, null, 2);
    }

    return (
      <div className="flex items-center gap-2">
        <div className="flex-1 min-w-0">
          {config.type === "boolean" ? (
            <Tag color={config.value ? "green" : "red"}>{config.value ? "启用" : "禁用"}</Tag>
          ) : config.encrypted ? (
            <code className="text-xs bg-gray-100 px-2 py-1 rounded font-mono">
              {typeof displayValue === "string" && displayValue.length > 50
                ? `${displayValue.substring(0, 50)}...`
                : displayValue}
            </code>
          ) : (
            <Text className="truncate">
              {typeof displayValue === "string" && displayValue.length > 100
                ? `${displayValue.substring(0, 100)}...`
                : String(displayValue)}
            </Text>
          )}
        </div>
        {config.encrypted && showSensitiveValues[config.key] && (
          <Button
            theme="borderless"
            size="small"
            icon={<IconEyeClosed />}
            onClick={() => handleToggleSensitiveValue(config.key)}
          />
        )}
        <Tooltip content="复制值">
          <Button
            theme="borderless"
            size="small"
            icon={<IconCopy />}
            onClick={() => {
              navigator.clipboard.writeText(String(displayValue));
              Toast.success("已复制到剪贴板");
            }}
          />
        </Tooltip>
      </div>
    );
  };

  // 渲染配置编辑表单
  const renderConfigEditForm = () => {
    if (!selectedConfig) return null;

    switch (selectedConfig.type) {
      case "boolean":
        return (
          <Switch
            checked={editForm.value}
            onChange={checked => setEditForm(prev => ({ ...prev, value: checked }))}
            disabled={!canEdit}
          />
        );
      case "number":
        return (
          <InputNumber
            value={editForm.value}
            onChange={value => setEditForm(prev => ({ ...prev, value }))}
            style={{ width: "100%" }}
            disabled={!canEdit}
          />
        );
      case "json":
        return (
          <TextArea
            value={
              typeof editForm.value === "object"
                ? JSON.stringify(editForm.value, null, 2)
                : editForm.value
            }
            onChange={value => {
              try {
                const parsed = JSON.parse(value);
                setEditForm(prev => ({ ...prev, value: parsed }));
              } catch {
                setEditForm(prev => ({ ...prev, value }));
              }
            }}
            rows={6}
            style={{ fontFamily: "monospace" }}
            disabled={!canEdit}
          />
        );
      default:
        // For multi-line text or special cases
        if (
          selectedConfig.key.includes("DESCRIPTION") ||
          selectedConfig.key.includes("MESSAGE") ||
          selectedConfig.key.includes("TEMPLATE")
        ) {
          return (
            <TextArea
              value={editForm.value}
              onChange={value => setEditForm(prev => ({ ...prev, value }))}
              rows={4}
              disabled={!canEdit}
            />
          );
        }
        return (
          <Input
            value={editForm.value}
            onChange={value => setEditForm(prev => ({ ...prev, value }))}
            type={selectedConfig.encrypted ? "password" : "text"}
            disabled={!canEdit}
          />
        );
    }
  };

  const columns = [
    {
      title: "配置项",
      dataIndex: "key",
      key: "config",
      width: 200,
      render: (_: any, record: SystemConfig) => (
        <div className="space-y-1">
          <div className="flex items-center gap-2">
            <Text strong className="text-base">
              {record.label}
            </Text>
            {record.isSystem && (
              <Badge count="系统" style={{ backgroundColor: "var(--semi-color-info)" }} />
            )}
            {record.encrypted && (
              <Badge count="加密" style={{ backgroundColor: "var(--semi-color-warning)" }} />
            )}
          </div>
          <Text type="secondary" size="small" className="font-mono">
            {record.key}
          </Text>
          {record.description && (
            <Text type="tertiary" size="small" className="line-clamp-2">
              {record.description}
            </Text>
          )}
        </div>
      ),
    },
    {
      title: "类型",
      dataIndex: "type",
      key: "type",
      width: 100,
      render: (type: keyof typeof dataTypeLabels) => (
        <Tag size="small">{dataTypeLabels[type] || type}</Tag>
      ),
    },
    {
      title: "当前值",
      dataIndex: "value",
      key: "value",
      width: 300,
      render: (_: any, record: SystemConfig) => renderConfigValue(record),
    },
    {
      title: "更新时间",
      dataIndex: "updatedAt",
      key: "updatedAt",
      width: 120,
      render: (text: string, record: SystemConfig) => (
        <div className="space-y-1">
          <Text size="small">{new Date(text).toLocaleDateString()}</Text>
          <Text type="secondary" size="small">
            最后更新
          </Text>
        </div>
      ),
    },
    {
      title: "操作",
      key: "actions",
      width: 120,
      fixed: "right",
      render: (_: any, record: SystemConfig) => (
        <Dropdown
          trigger="click"
          clickToHide
          menu={[
            {
              node: "item" as const,
              name: "编辑",
              icon: <IconEdit />,
              onClick: () => handleEditConfig(record),
              disabled: !canEdit,
            },
            {
              node: "item" as const,
              name: "历史记录",
              icon: <IconHistory />,
              onClick: () => handleViewHistory(record),
            },
            {
              node: "item" as const,
              name: "重置为默认值",
              icon: <IconRestore />,
              onClick: () => handleResetConfig(record),
              disabled: !canEdit,
            },
            {
              node: "divider",
            },
            {
              node: "item" as const,
              name: "复制配置键",
              icon: <IconCopy />,
              onClick: () => {
                navigator.clipboard.writeText(record.key);
                Toast.success("已复制配置键");
              },
            },
          ]}
        >
          <Button theme="borderless" type="tertiary" icon={<IconMore />} size="small" />
        </Dropdown>
      ),
    },
  ];

  const formatChangedBy = (
    changedBy: unknown,
    userInfo?: { id?: string; username?: string; fullName?: string }
  ): string => {
    if (userInfo?.fullName) return userInfo.fullName;
    if (typeof changedBy === "object" && changedBy !== null) {
      const obj = changedBy as { id?: string; username?: string; fullName?: string };
      return obj.fullName || obj.username || obj.id || "-";
    }
    return changedBy !== undefined && changedBy !== null ? String(changedBy) : "-";
  };

  if (!canView) {
    return (
      <div className="flex flex-col items-center justify-center h-64">
        <div className="w-20 h-20 rounded-full bg-gray-100 flex items-center justify-center mb-6">
          <IconShield style={{ fontSize: 32 }} className="text-gray-400" />
        </div>
        <Title heading={5} className="mb-2 text-gray-600">
          权限不足
        </Title>
        <Text type="secondary">您没有权限查看系统配置</Text>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* 页面头部 */}
      <div className="ops-page-header">
        <div className="flex justify-between items-start flex-wrap gap-4">
          <div>
            <h1 className="ops-page-title">系统配置管理</h1>
            <p className="ops-page-subtitle">统一管理系统全局配置参数，支持分类管理和权限控制</p>
          </div>
          <div className="ops-page-actions">
            <Button
              icon={<IconRefresh2 />}
              onClick={() => clearCacheMutation.mutate("config")}
              loading={clearCacheMutation.isPending}
            >
              清理缓存
            </Button>
            {canExport && (
              <Dropdown
                trigger="click"
                menu={[
                  {
                    node: "item" as const,
                    name: "导出全部配置",
                    icon: <IconExport />,
                    onClick: () => exportConfigMutation.mutate([]),
                  },
                  {
                    node: "item" as const,
                    name: "导入配置",
                    icon: <IconImport />,
                    onClick: () => setIsImportModalVisible(true),
                  },
                ]}
              >
                <Button icon={<IconMore />} size="large">
                  更多操作
                  <IconChevronDown />
                </Button>
              </Dropdown>
            )}
            <Button icon={<IconRefresh />} onClick={() => refetchGroups()} size="large">
              刷新数据
            </Button>
          </div>
        </div>
      </div>

      {/* 配置测试功能面板 */}
      <Row gutter={[24, 24]}>
        <Col span={24} lg={8}>
          <div className="ops-card">
            <div className="ops-card-header">
              <Title heading={5} className="m-0 flex items-center gap-2">
                <IconMail />
                邮件配置测试
              </Title>
            </div>
            <div className="ops-card-body">
              <div className="space-y-4">
                <div>
                  <Text type="secondary" size="small">
                    测试当前邮件配置是否正常工作
                  </Text>
                </div>
                <Input
                  placeholder="输入测试邮箱地址"
                  value={testEmailAddress}
                  onChange={setTestEmailAddress}
                  prefix={<IconMail />}
                />
                <Button
                  block
                  type="primary"
                  icon={<IconMail />}
                  onClick={() => handleTestEmail()}
                  loading={testEmailMutation.isPending}
                  disabled={!testEmailAddress || !canEdit}
                >
                  发送测试邮件
                </Button>
                {emailTestResult && (
                  <div
                    className={`p-3 rounded-lg ${emailTestResult.success ? "bg-green-50 border border-green-200" : "bg-red-50 border border-red-200"}`}
                  >
                    <Text type={emailTestResult.success ? "success" : "danger"} size="small">
                      {emailTestResult.message}
                    </Text>
                  </div>
                )}
              </div>
            </div>
          </div>
        </Col>

        <Col span={24} lg={8}>
          <div className="ops-card">
            <div className="ops-card-header">
              <Title heading={5} className="m-0 flex items-center gap-2">
                <IconBell />
                短信配置测试
              </Title>
            </div>
            <div className="ops-card-body">
              <div className="space-y-4">
                <div>
                  <Text type="secondary" size="small">
                    测试当前短信配置是否正常工作
                  </Text>
                </div>
                <Input
                  placeholder="输入测试手机号"
                  value={testPhoneNumber}
                  onChange={setTestPhoneNumber}
                  prefix={<IconBell />}
                />
                <Button
                  block
                  type="primary"
                  icon={<IconBell />}
                  onClick={() => handleTestSms()}
                  loading={testSmsMutation.isPending}
                  disabled={!testPhoneNumber || !canEdit}
                >
                  发送测试短信
                </Button>
                {smsTestResult && (
                  <div
                    className={`p-3 rounded-lg ${smsTestResult.success ? "bg-green-50 border border-green-200" : "bg-red-50 border border-red-200"}`}
                  >
                    <Text type={smsTestResult.success ? "success" : "danger"} size="small">
                      {smsTestResult.message}
                    </Text>
                  </div>
                )}
              </div>
            </div>
          </div>
        </Col>

        <Col span={24} lg={8}>
          <div className="ops-card">
            <div className="ops-card-header">
              <Title heading={5} className="m-0 flex items-center gap-2">
                <IconDatabase />
                备份配置测试
              </Title>
            </div>
            <div className="ops-card-body">
              <div className="space-y-4">
                <div>
                  <Text type="secondary" size="small">
                    测试备份存储配置和执行备份任务
                  </Text>
                </div>
                <Select
                  placeholder="选择备份类型"
                  value={backupTestType}
                  onChange={value => setBackupTestType(value as string)}
                  style={{ width: "100%" }}
                >
                  <Select.Option value="database">数据库备份</Select.Option>
                  <Select.Option value="files">文件备份</Select.Option>
                  <Select.Option value="config">配置备份</Select.Option>
                </Select>
                <Button
                  block
                  type="primary"
                  icon={<IconDatabase />}
                  onClick={() => handleTestBackup()}
                  loading={testBackupMutation.isPending}
                  disabled={!backupTestType || !canEdit}
                >
                  执行测试备份
                </Button>
                {backupTestResult && (
                  <div
                    className={`p-3 rounded-lg ${backupTestResult.success ? "bg-green-50 border border-green-200" : "bg-red-50 border border-red-200"}`}
                  >
                    <Text type={backupTestResult.success ? "success" : "danger"} size="small">
                      {backupTestResult.message}
                    </Text>
                    {backupTestResult.details && (
                      <div className="mt-2">
                        <Text type="secondary" size="small">
                          详情: {backupTestResult.details}
                        </Text>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
          </div>
        </Col>
      </Row>

      {/* 邮件通知模板配置 */}
      <Row gutter={[16, 16]}>
        <Col span={24}>
          <div className="ops-card">
            <div className="ops-card-header">
              <div className="flex items-center justify-between">
                <Title heading={5} className="m-0 flex items-center gap-2">
                  <IconMail />
                  邮件通知模板配置
                </Title>
                <Button
                  icon={<IconPlus />}
                  onClick={() => setIsTemplateModalVisible(true)}
                  disabled={!canEdit}
                >
                  新建模板
                </Button>
              </div>
            </div>
            <div className="ops-card-body">
              <Tabs activeKey={activeTemplateTab} onChange={setActiveTemplateTab}>
                <TabPane tab="系统通知模板" itemKey="system">
                  <div className="space-y-4">
                    {safeEmailTemplates.system.map(template => (
                      <div
                        key={template.id}
                        className="p-4 border rounded-lg hover:shadow-sm transition-shadow"
                      >
                        <div className="flex items-center justify-between mb-3">
                          <div className="flex items-center gap-3">
                            <Tag color={template.enabled ? "green" : "red"}>
                              {template.enabled ? "启用" : "禁用"}
                            </Tag>
                            <Text strong>{template.name}</Text>
                            <Text type="secondary" size="small">
                              {template.type}
                            </Text>
                          </div>
                          <div className="flex items-center gap-2">
                            <Button
                              size="small"
                              icon={<IconEyeOpened />}
                              onClick={() => handlePreviewTemplate(template)}
                            >
                              预览
                            </Button>
                            <Button
                              size="small"
                              icon={<IconEdit />}
                              onClick={() => handleEditTemplate(template)}
                              disabled={!canEdit}
                            >
                              编辑
                            </Button>
                            <Button
                              size="small"
                              icon={<IconMail />}
                              onClick={() => handleTestTemplate(template)}
                              disabled={!canEdit}
                            >
                              测试
                            </Button>
                          </div>
                        </div>
                        <Text type="secondary" size="small" className="block mb-2">
                          {template.description}
                        </Text>
                        <div className="text-xs bg-gray-50 p-2 rounded font-mono">
                          主题: {template.subject}
                        </div>
                      </div>
                    ))}
                  </div>
                </TabPane>

                <TabPane tab="业务通知模板" itemKey="business">
                  <div className="space-y-4">
                    {safeEmailTemplates.business.map(template => (
                      <div
                        key={template.id}
                        className="p-4 border rounded-lg hover:shadow-sm transition-shadow"
                      >
                        <div className="flex items-center justify-between mb-3">
                          <div className="flex items-center gap-3">
                            <Tag color={template.enabled ? "green" : "red"}>
                              {template.enabled ? "启用" : "禁用"}
                            </Tag>
                            <Text strong>{template.name}</Text>
                            <Text type="secondary" size="small">
                              {template.type}
                            </Text>
                          </div>
                          <div className="flex items-center gap-2">
                            <Button
                              size="small"
                              icon={<IconEyeOpened />}
                              onClick={() => handlePreviewTemplate(template)}
                            >
                              预览
                            </Button>
                            <Button
                              size="small"
                              icon={<IconEdit />}
                              onClick={() => handleEditTemplate(template)}
                              disabled={!canEdit}
                            >
                              编辑
                            </Button>
                            <Button
                              size="small"
                              icon={<IconMail />}
                              onClick={() => handleTestTemplate(template)}
                              disabled={!canEdit}
                            >
                              测试
                            </Button>
                          </div>
                        </div>
                        <Text type="secondary" size="small" className="block mb-2">
                          {template.description}
                        </Text>
                        <div className="text-xs bg-gray-50 p-2 rounded font-mono">
                          主题: {template.subject}
                        </div>
                      </div>
                    ))}
                  </div>
                </TabPane>
              </Tabs>
            </div>
          </div>
        </Col>
      </Row>

      {/* 配置管理区域 */}
      <div className="ops-table-container p-3">
        {/* 搜索和筛选区域 */}
        <div className="ops-search-toolbar">
          <div className="ops-search-row">
            <Input
              prefix={<IconSearch />}
              placeholder="搜索配置项名称、键名或描述"
              value={searchQuery}
              onChange={setSearchQuery}
              style={{ width: 320 }}
              showClear
              size="large"
            />
          </div>
        </div>

        {/* 分类标签页 */}
        <div className="bg-white border-b border-gray-200">
          <Tabs
            activeKey={activeTab}
            onChange={setActiveTab}
            type="line"
            size="large"
            tabBarExtraContent={
              <div className="flex items-center gap-2">
                <Badge
                  count={filteredConfigGroups.reduce((sum, group) => sum + group.configs.length, 0)}
                />
                <Text type="secondary" size="small">
                  个配置项
                </Text>
              </div>
            }
          >
            <TabPane
              tab={
                <div className="flex items-center gap-2">
                  <IconSetting />
                  <span>全部配置</span>
                </div>
              }
              itemKey="all"
            />
            {Object.entries(categoryLabels).map(([key, label]) => {
              const count =
                configGroupsData?.data?.find(group => group.category === key)?.configs.length || 0;
              return (
                <TabPane
                  key={key}
                  tab={
                    <div className="flex items-center gap-2">
                      {categoryIcons[key as keyof typeof categoryIcons]}
                      <span>{label}</span>
                      <Badge
                        count={count}
                        style={{ backgroundColor: "var(--semi-color-tertiary)" }}
                      />
                    </div>
                  }
                  itemKey={key}
                />
              );
            })}
          </Tabs>
        </div>

        {/* 批量操作栏 */}
        {selectedRowKeys.length > 0 && (
          <div className="ops-batch-actions">
            <div className="ops-batch-info">
              <IconSetting />
              <Text strong>已选择 {selectedRowKeys.length} 个配置项</Text>
              <Button size="small" theme="borderless" onClick={() => setSelectedRowKeys([])}>
                清空选择
              </Button>
            </div>
            <div className="ops-batch-controls">
              <Select
                value={batchAction}
                onChange={value => setBatchAction(value as any)}
                style={{ width: 120 }}
              >
                <Select.Option value="export">导出配置</Select.Option>
                {canEdit && <Select.Option value="reset">重置默认值</Select.Option>}
              </Select>
              <Button type="primary" onClick={handleBatchAction}>
                执行操作
              </Button>
            </div>
          </div>
        )}

        {/* 配置表格 */}
        <div className="bg-white">
          {isLoadingGroups ? (
            <div className="flex justify-center items-center h-64">
              <Spin size="large" />
              <Text style={{ marginLeft: 16 }}>正在加载配置数据...</Text>
            </div>
          ) : filteredConfigGroups.length === 0 ? (
            <div className="flex flex-col items-center py-16">
              <div className="w-20 h-20 rounded-full bg-gray-100 flex items-center justify-center mb-6">
                <IconSetting style={{ fontSize: 32 }} className="text-gray-400" />
              </div>
              <Title heading={5} className="mb-2 text-gray-600">
                暂无配置数据
              </Title>
              <Text type="secondary" className="mb-6">
                没有找到匹配的配置项
              </Text>
            </div>
          ) : (
            <div className="space-y-0">
              {filteredConfigGroups.map(group => (
                <Collapse key={group.category} defaultActiveKey={[group.category]}>
                  <Collapse.Panel
                    header={
                      <div className="flex items-center justify-between w-full pr-4">
                        <div className="flex items-center gap-3">
                          {categoryIcons[group.category as keyof typeof categoryIcons]}
                          <Text strong className="text-lg">
                            {group.label}
                          </Text>
                          <Badge
                            count={group.configs.length}
                            style={{ backgroundColor: "var(--semi-color-tertiary)" }}
                          />
                        </div>
                        <Text type="secondary" size="small">
                          {group.description}
                        </Text>
                      </div>
                    }
                    itemKey={group.category}
                  >
                    <Table
                      columns={columns as any}
                      dataSource={group.configs}
                      rowKey="key"
                      pagination={false}
                      scroll={{ x: 1000 }}
                      rowSelection={{
                        selectedRowKeys,
                        onChange: keys => {
                          if (keys) {
                            setSelectedRowKeys(keys.map(k => String(k)));
                          }
                        },
                      }}
                      size="small"
                    />
                  </Collapse.Panel>
                </Collapse>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* 配置编辑弹窗 */}
      <Modal
        title={
          <div className="flex items-center gap-2">
            <IconEdit />
            <span>编辑配置项</span>
          </div>
        }
        visible={isEditModalVisible}
        onOk={handleFormSubmit}
        onCancel={() => setIsEditModalVisible(false)}
        width={600}
        confirmLoading={updateConfigMutation.isPending}
      >
        {selectedConfig && (
          <div className="space-y-6">
            <div className="p-4 bg-gray-50 rounded-lg">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Text strong>配置键名</Text>
                  <div className="font-mono text-sm mt-1">{selectedConfig.key}</div>
                </div>
                <div>
                  <Text strong>数据类型</Text>
                  <div className="mt-1">
                    <Tag size="small">
                      {dataTypeLabels[selectedConfig.type as keyof typeof dataTypeLabels]}
                    </Tag>
                  </div>
                </div>
              </div>
              {selectedConfig.description && (
                <div className="mt-4">
                  <Text strong>说明</Text>
                  <div className="text-sm mt-1">{selectedConfig.description}</div>
                </div>
              )}
            </div>

            <Form.Label text="配置值" required />
            {renderConfigEditForm()}

            <Form.Label text="变更原因">
              <TextArea
                value={editForm.changeReason}
                onChange={value => setEditForm(prev => ({ ...prev, changeReason: value }))}
                placeholder="请简要说明此次配置变更的原因（可选）"
                rows={2}
              />
            </Form.Label>

            {selectedConfig.defaultValue !== undefined && (
              <div className="p-3 bg-blue-50 border border-blue-200 rounded">
                <Text strong size="small">
                  默认值：
                </Text>
                <code className="ml-2 text-xs bg-white px-2 py-1 rounded">
                  {typeof selectedConfig.defaultValue === "object"
                    ? JSON.stringify(selectedConfig.defaultValue)
                    : String(selectedConfig.defaultValue)}
                </code>
              </div>
            )}
          </div>
        )}
      </Modal>

      {/* 配置历史记录抽屉 */}
      <Drawer
        title={
          <div className="flex items-center gap-2">
            <IconHistory />
            <span>配置历史记录</span>
          </div>
        }
        visible={isHistoryDrawerVisible}
        onCancel={() => setIsHistoryDrawerVisible(false)}
        width={600}
      >
        {selectedConfig && (
          <div className="space-y-4">
            <div className="p-4 bg-gray-50 rounded-lg">
              <Title heading={6}>{selectedConfig.label}</Title>
              <Text type="secondary" className="font-mono text-sm">
                {selectedConfig.key}
              </Text>
            </div>

            {isLoadingHistory ? (
              <div className="flex justify-center py-8">
                <Spin />
              </div>
            ) : configHistoryData?.data?.history?.length ? (
              <Timeline className="ops-timeline">
                {configHistoryData.data.history.map((record, index) => (
                  <Timeline.Item
                    key={record.id}
                    dot={
                      <div className="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center">
                        <IconEdit size="small" className="text-blue-600" />
                      </div>
                    }
                    time={
                      <span className="text-xs text-gray-500">
                        {new Date(record.changedAt).toLocaleString()}
                      </span>
                    }
                  >
                    <div className="bg-white p-4 rounded-lg border border-gray-100">
                      <div className="flex items-center justify-between mb-3">
                        <Text strong>配置变更</Text>
                        <div className="flex items-center gap-2">
                          <IconUser size="small" />
                          <Text type="secondary" size="small">
                            {formatChangedBy(record.changedBy as unknown, record.userInfo)}
                          </Text>
                        </div>
                      </div>

                      <div className="space-y-2">
                        {record.oldValue !== undefined && (
                          <div>
                            <Text type="secondary" size="small">
                              原值：
                            </Text>
                            <code className="block text-xs bg-red-50 p-2 rounded mt-1 font-mono">
                              {typeof record.oldValue === "object"
                                ? JSON.stringify(record.oldValue, null, 2)
                                : String(record.oldValue)}
                            </code>
                          </div>
                        )}
                        <div>
                          <Text type="secondary" size="small">
                            新值：
                          </Text>
                          <code className="block text-xs bg-green-50 p-2 rounded mt-1 font-mono">
                            {typeof record.newValue === "object"
                              ? JSON.stringify(record.newValue, null, 2)
                              : String(record.newValue)}
                          </code>
                        </div>
                      </div>
                    </div>
                  </Timeline.Item>
                ))}
              </Timeline>
            ) : (
              <Empty description="暂无历史记录" />
            )}
          </div>
        )}
      </Drawer>

      {/* 批量操作确认弹窗 */}
      <Modal
        title="批量操作确认"
        visible={isBatchModalVisible}
        onOk={handleBatchAction}
        onCancel={() => setIsBatchModalVisible(false)}
        confirmLoading={batchResetMutation.isPending}
      >
        <div className="space-y-4">
          <Text>
            确定要对选中的 <Text strong>{selectedRowKeys.length}</Text> 个配置项执行
            <Text strong type="warning">
              {batchAction === "export" && "导出配置"}
              {batchAction === "reset" && "重置为默认值"}
            </Text>
            操作吗？
          </Text>
          {batchAction === "reset" && (
            <div className="p-3 bg-red-50 border border-red-200 rounded">
              <Text type="danger">
                <IconShield /> 警告：重置操作将恢复配置项的默认值，可能影响系统运行！
              </Text>
            </div>
          )}
        </div>
      </Modal>

      {/* 邮件模板编辑弹窗 */}
      <Modal
        title={
          <div className="flex items-center gap-2">
            <IconMail />
            <span>{selectedTemplate ? "编辑邮件模板" : "新建邮件模板"}</span>
          </div>
        }
        visible={isTemplateModalVisible}
        onOk={() => {
          Toast.success("模板保存成功");
          setIsTemplateModalVisible(false);
          setTemplateForm({});
        }}
        onCancel={() => {
          setIsTemplateModalVisible(false);
          setTemplateForm({});
        }}
        width={800}
        confirmLoading={false}
      >
        <div className="space-y-4">
          <Form.Label text="模板名称" required>
            <Input
              value={templateForm.name}
              onChange={name => setTemplateForm(prev => ({ ...prev, name }))}
              placeholder="请输入模板名称"
            />
          </Form.Label>

          <Form.Label text="模板类型" required>
            <Select
              value={templateForm.type}
              onChange={type => setTemplateForm(prev => ({ ...prev, type: type as string }))}
              placeholder="选择模板类型"
              style={{ width: "100%" }}
            >
              <Select.Option value="alert">系统告警</Select.Option>
              <Select.Option value="maintenance">系统维护</Select.Option>
              <Select.Option value="service">服务通知</Select.Option>
              <Select.Option value="custom">自定义</Select.Option>
            </Select>
          </Form.Label>

          <Form.Label text="邮件主题" required>
            <Input
              value={templateForm.subject}
              onChange={subject => setTemplateForm(prev => ({ ...prev, subject }))}
              placeholder="请输入邮件主题（支持变量如 {{title}}）"
            />
          </Form.Label>

          <Form.Label text="模板描述">
            <TextArea
              value={templateForm.description}
              onChange={description => setTemplateForm(prev => ({ ...prev, description }))}
              placeholder="请输入模板描述"
              rows={2}
            />
          </Form.Label>

          <Form.Label text="邮件内容" required>
            <TextArea
              value={templateForm.content}
              onChange={content => setTemplateForm(prev => ({ ...prev, content }))}
              placeholder="请输入邮件内容（支持变量如 {{userName}}、{{timestamp}} 等）"
              rows={8}
              style={{ fontFamily: "monospace" }}
            />
          </Form.Label>

          <Form.Label text="启用状态">
            <Switch
              checked={templateForm.enabled}
              onChange={enabled => setTemplateForm(prev => ({ ...prev, enabled }))}
            />
            <Text type="secondary" size="small" style={{ marginLeft: 8 }}>
              启用后该模板可用于系统自动发送邮件
            </Text>
          </Form.Label>

          <div className="p-3 bg-blue-50 border border-blue-200 rounded">
            <Text strong size="small">
              可用变量：
            </Text>
            <div className="mt-2 text-xs text-gray-600">
              <code>{"{{userName}}"}</code> - 用户名，
              <code>{"{{timestamp}}"}</code> - 时间戳，
              <code>{"{{title}}"}</code> - 标题，
              <code>{"{{content}}"}</code> - 内容，
              <code>{"{{url}}"}</code> - 链接地址
            </div>
          </div>
        </div>
      </Modal>

      {/* 邮件模板预览弹窗 */}
      <Modal
        title={
          <div className="flex items-center gap-2">
            <IconEyeOpened />
            <span>模板预览</span>
          </div>
        }
        visible={isTemplatePreviewVisible}
        onCancel={() => setIsTemplatePreviewVisible(false)}
        footer={
          <div className="flex justify-between">
            <Button
              icon={<IconMail />}
              onClick={() => {
                if (selectedTemplate) {
                  handleTestTemplate(selectedTemplate);
                }
              }}
              disabled={!testEmailAddress}
            >
              发送测试邮件
            </Button>
            <Button onClick={() => setIsTemplatePreviewVisible(false)}>关闭</Button>
          </div>
        }
        width={600}
      >
        {selectedTemplate && (
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4 p-3 bg-gray-50 rounded">
              <div>
                <Text strong>模板名称：</Text>
                <div>{selectedTemplate.name}</div>
              </div>
              <div>
                <Text strong>模板类型：</Text>
                <div>{selectedTemplate.type}</div>
              </div>
              <div>
                <Text strong>状态：</Text>
                <Tag color={selectedTemplate.enabled ? "green" : "red"}>
                  {selectedTemplate.enabled ? "启用" : "禁用"}
                </Tag>
              </div>
              <div>
                <Text strong>变量数量：</Text>
                <div>{selectedTemplate.variables?.length || 0} 个</div>
              </div>
            </div>

            <div>
              <Text strong>邮件主题：</Text>
              <div className="mt-1 p-2 bg-gray-50 rounded font-mono text-sm">
                {selectedTemplate.subject}
              </div>
            </div>

            <div>
              <Text strong>邮件内容：</Text>
              <div className="mt-1 p-3 bg-gray-50 rounded font-mono text-sm whitespace-pre-wrap">
                {selectedTemplate.content}
              </div>
            </div>

            {selectedTemplate.variables &&
              Array.isArray(selectedTemplate.variables) &&
              selectedTemplate.variables.length > 0 && (
                <div>
                  <Text strong>可用变量：</Text>
                  <div className="mt-1 flex flex-wrap gap-2">
                    {selectedTemplate.variables.map(variable => (
                      <Tag key={variable} size="small">
                        {`{{${variable}}}`}
                      </Tag>
                    ))}
                  </div>
                </div>
              )}

            <div>
              <Text strong>描述：</Text>
              <div className="mt-1 text-sm text-gray-600">{selectedTemplate.description}</div>
            </div>
          </div>
        )}
      </Modal>

      <BackTop />
    </div>
  );
}
