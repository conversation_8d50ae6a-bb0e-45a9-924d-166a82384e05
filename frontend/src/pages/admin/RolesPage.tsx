import React, { useEffect, useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import {
  Button,
  Input,
  Table,
  Pagination,
  Tag,
  Popconfirm,
  Toast,
  Space,
  Modal,
  Dropdown,
  Card,
  Descriptions,
  Typography,
} from "@douyinfe/semi-ui";
import {
  IconPlus,
  IconRefresh,
  IconEdit,
  IconDelete,
  IconKey,
  IconCopy,
  IconChevronDown,
} from "@douyinfe/semi-icons";
import { useUIStore } from "@/stores";
import { roleService, type RoleListParams } from "@/services/role";
import type { Role } from "@/types";
import { RoleForm } from "@/components/admin/RoleForm";
import { PermissionMatrix } from "@/components/admin/PermissionMatrix";

export default function RolesPage() {
  const { setBreadcrumbs } = useUIStore();
  const queryClient = useQueryClient();

  const [params, setParams] = useState<RoleListParams>({
    page: 1,
    limit: 20,
  });
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingRole, setEditingRole] = useState<Role | null>(null);
  const [isPermissionModalVisible, setIsPermissionModalVisible] = useState(false);
  const [viewingRole, setViewingRole] = useState<Role | null>(null);

  useEffect(() => {
    setBreadcrumbs([
      { text: "首页", href: "/", icon: "🏠" },
      { text: "系统管理", icon: "⚙️" },
      { text: "角色管理", icon: "🔑" },
    ]);
  }, [setBreadcrumbs]);

  // 获取角色列表
  const {
    data: rolesData,
    isLoading,
    refetch,
  } = useQuery({
    queryKey: ["roles", params],
    queryFn: () => roleService.getRoles(params),
  });

  // 获取角色统计
  const { data: statsData } = useQuery({
    queryKey: ["role-stats"],
    queryFn: () => roleService.getRoleStats(),
  });

  // 获取角色模板
  const { data: templatesData } = useQuery({
    queryKey: ["role-templates"],
    queryFn: () => roleService.getRoleTemplates(),
  });

  // 删除角色
  const deleteMutation = useMutation({
    mutationFn: (id: string) => roleService.deleteRole(id),
    onSuccess: () => {
      Toast.success("角色删除成功");
      queryClient.invalidateQueries({ queryKey: ["roles"] });
      queryClient.invalidateQueries({ queryKey: ["role-stats"] });
    },
    onError: (error: any) => {
      Toast.error(error.message || "删除失败");
    },
  });

  // 复制角色
  const duplicateMutation = useMutation({
    mutationFn: ({ id, newName }: { id: string; newName: string }) =>
      roleService.duplicateRole(id, newName, `${editingRole?.description}（副本）`),
    onSuccess: () => {
      Toast.success("角色复制成功");
      queryClient.invalidateQueries({ queryKey: ["roles"] });
    },
    onError: (error: any) => {
      Toast.error(error.message || "复制失败");
    },
  });

  // 从模板创建角色
  const createFromTemplateMutation = useMutation({
    mutationFn: ({ templateName }: { templateName: string }) =>
      roleService.createRoleFromTemplate(templateName),
    onSuccess: () => {
      Toast.success("角色创建成功");
      queryClient.invalidateQueries({ queryKey: ["roles"] });
    },
    onError: (error: any) => {
      Toast.error(error.message || "创建失败");
    },
  });

  const handleSearch = (value: string) => {
    setParams({ ...params, search: value, page: 1 });
  };

  const handlePageChange = (page: number, pageSize?: number) => {
    setParams({ ...params, page, limit: pageSize || params.limit });
  };

  const handleCreate = () => {
    setEditingRole(null);
    setIsModalVisible(true);
  };

  const handleEdit = (role: Role) => {
    setEditingRole(role);
    setIsModalVisible(true);
  };

  const handleDelete = (role: Role) => {
    deleteMutation.mutate(role.id);
  };

  const handleViewPermissions = (role: Role) => {
    setViewingRole(role);
    setIsPermissionModalVisible(true);
  };

  const handleDuplicate = (role: Role) => {
    const newName = `${role.name}_copy_${Date.now()}`;
    setEditingRole(role);
    duplicateMutation.mutate({ id: role.id, newName });
  };

  const handleCreateFromTemplate = (templateName: string) => {
    createFromTemplateMutation.mutate({ templateName });
  };

  const getCreateMenuItems = () => {
    const items = [
      {
        key: "custom",
        label: "自定义角色",
        icon: <IconPlus />,
        onClick: handleCreate,
      },
    ];

    if (templatesData?.data) {
      items.push({
        key: "divider",
        type: "divider",
      } as any);

      templatesData.data.forEach(template => {
        items.push({
          key: template.name,
          label: `从模板创建: ${template.label}`,
          icon: <IconCopy />,
          onClick: () => handleCreateFromTemplate(template.name),
        });
      });
    }

    return items;
  };

  const columns = [
    {
      title: "角色名称",
      dataIndex: "name",
      key: "name",
      render: (text: string, record: Role) => (
        <Space>
          <IconKey />
          <Typography.Text className="font-medium">{text}</Typography.Text>
        </Space>
      ),
    },
    {
      title: "描述",
      dataIndex: "description",
      key: "description",
      ellipsis: true,
    },
    {
      title: "权限数量",
      dataIndex: "permissions",
      key: "permissions",
      render: (permissions: string[]) => <Tag color="blue">{permissions.length} 个权限</Tag>,
    },
    {
      title: "用户数量",
      dataIndex: "userCount",
      key: "userCount",
      render: (count: number) => <Tag color="green">{count || 0} 个用户</Tag>,
    },
    {
      title: "创建时间",
      dataIndex: "createdAt",
      key: "createdAt",
      render: (text: string) => new Date(text).toLocaleString(),
    },
    {
      title: "操作",
      key: "actions",
      render: (_: any, record: Role) => (
        <Space>
          <Button
            theme="borderless"
            type="primary"
            size="small"
            icon={<IconKey />}
            onClick={() => handleViewPermissions(record)}
          >
            权限详情
          </Button>
          <Button
            theme="borderless"
            type="secondary"
            size="small"
            icon={<IconEdit />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Button
            theme="borderless"
            type="tertiary"
            size="small"
            icon={<IconCopy />}
            onClick={() => handleDuplicate(record)}
          >
            复制
          </Button>
          <Popconfirm
            title="确定删除此角色吗？"
            content="删除后该角色下的用户将失去相应权限"
            onConfirm={() => handleDelete(record)}
          >
            <Button theme="borderless" type="danger" size="small" icon={<IconDelete />}>
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div className="space-y-6">
      <div className="page-header">
        <h1 className="page-title">角色管理</h1>
        <p className="page-subtitle">管理系统角色和权限配置</p>
      </div>

      {/* 统计卡片 */}
      {statsData?.data && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card title="总角色数" style={{ textAlign: "center" }} bodyStyle={{ padding: "20px" }}>
            <div className="text-2xl font-bold text-blue-600">{statsData.data.totalRoles}</div>
          </Card>
          <Card
            title="最多用户的角色"
            style={{ textAlign: "center" }}
            bodyStyle={{ padding: "20px" }}
          >
            <div className="text-2xl font-bold text-green-600">
              {statsData.data.roleUsage?.[0]?.userCount || 0}
              <span className="text-sm text-gray-500 ml-1">个用户</span>
            </div>
          </Card>
          <Card title="平均权限数" style={{ textAlign: "center" }} bodyStyle={{ padding: "20px" }}>
            <div className="text-2xl font-bold text-purple-600">
              {Math.round(
                (statsData.data.permissionUsage?.length || 0) / statsData.data.totalRoles
              ) || 0}
              <span className="text-sm text-gray-500 ml-1">个权限</span>
            </div>
          </Card>
        </div>
      )}

      <div className="card">
        {/* 搜索和操作 */}
        <div className="flex flex-wrap gap-4 mb-6">
          <Input
            prefix={<IconKey />}
            placeholder="搜索角色名称或描述"
            style={{ width: 300 }}
            onEnterPress={e => handleSearch(e.currentTarget.value)}
            showClear
          />
          <Button icon={<IconRefresh />} onClick={() => refetch()}>
            刷新
          </Button>
          <Dropdown
            render={
              <Dropdown.Menu>
                <Dropdown.Item onClick={handleCreate}>
                  <IconPlus /> 自定义角色
                </Dropdown.Item>
                <Dropdown.Divider />
                {templatesData?.data?.map(template => (
                  <Dropdown.Item
                    key={template.name}
                    onClick={() => handleCreateFromTemplate(template.name)}
                  >
                    从模板创建: {template.label}
                  </Dropdown.Item>
                )) || []}
              </Dropdown.Menu>
            }
          >
            <Button type="primary">
              新建角色 <IconChevronDown />
            </Button>
          </Dropdown>
        </div>

        {/* 角色表格 */}
        <Table
          columns={columns}
          dataSource={rolesData?.data?.roles || []}
          rowKey="id"
          loading={isLoading}
          pagination={false}
          scroll={{ x: 1000 }}
        />

        {/* 分页 */}
        {rolesData?.data && (
          <div className="flex justify-end mt-4">
            <Pagination
              currentPage={rolesData.data.page}
              total={rolesData.data.total}
              pageSize={rolesData.data.limit}
              showSizeChanger
              showQuickJumper
              showTotal
              onPageChange={handlePageChange}
            />
          </div>
        )}
      </div>

      {/* 角色表单弹窗 */}
      <Modal
        title={editingRole ? "编辑角色" : "新建角色"}
        visible={isModalVisible}
        onCancel={() => setIsModalVisible(false)}
        footer={null}
        width={800}
      >
        <RoleForm
          role={editingRole}
          onSuccess={() => {
            setIsModalVisible(false);
            queryClient.invalidateQueries({ queryKey: ["roles"] });
            queryClient.invalidateQueries({ queryKey: ["role-stats"] });
          }}
          onCancel={() => setIsModalVisible(false)}
        />
      </Modal>

      {/* 权限详情弹窗 */}
      <Modal
        title={`角色权限详情 - ${viewingRole?.name}`}
        visible={isPermissionModalVisible}
        onCancel={() => setIsPermissionModalVisible(false)}
        footer={null}
        width={900}
      >
        {viewingRole && <PermissionMatrix role={viewingRole} readonly />}
      </Modal>
    </div>
  );
}
