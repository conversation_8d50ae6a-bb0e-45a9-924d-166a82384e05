import React, { useEffect, useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import {
  Button,
  Input,
  Select,
  Table,
  Pagination,
  Tag,
  Modal,
  Popconfirm,
  Space,
  Toast,
} from "@douyinfe/semi-ui";
import {
  IconPlus,
  IconSearch,
  IconRefresh,
  IconEdit,
  IconDelete,
  IconUser,
  IconLock,
} from "@douyinfe/semi-icons";
import { useUIStore } from "@/stores";
import { userService, type UserListParams } from "@/services/user";
import type { User } from "@/types";
import { roleService } from "@/services/role";
import { UserForm } from "@/components/admin/UserForm";

// Semi-UI Select 组件不需要单独导入 Option

export default function UsersPage() {
  const setBreadcrumbs = useUIStore(state => state.setBreadcrumbs);
  const queryClient = useQueryClient();

  const [params, setParams] = useState<UserListParams>({
    page: 1,
    limit: 20,
  });
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingUser, setEditingUser] = useState<User | null>(null);
  const [isResetPasswordModalVisible, setIsResetPasswordModalVisible] = useState(false);
  const [resetPasswordUser, setResetPasswordUser] = useState<User | null>(null);
  const [newPassword, setNewPassword] = useState("");

  useEffect(() => {
    setBreadcrumbs([
      { text: "首页", href: "/", icon: "🏠" },
      { text: "系统管理", icon: "⚙️" },
      { text: "用户管理", icon: "👤" },
    ]);
  }, [setBreadcrumbs]);

  // 获取用户列表
  const {
    data: usersData,
    isLoading,
    refetch,
  } = useQuery({
    queryKey: ["users", params],
    queryFn: () => userService.getUsers(params),
  });

  // 获取角色列表（用于筛选）
  const { data: rolesData } = useQuery({
    queryKey: ["roles-all"],
    queryFn: () => roleService.getAllRoles(),
  });

  // 获取部门列表（用于筛选）
  const { data: departmentsData } = useQuery({
    queryKey: ["departments"],
    queryFn: () => userService.getDepartments(),
  });

  // 删除用户
  const deleteMutation = useMutation({
    mutationFn: (id: string) => userService.deleteUser(id),
    onSuccess: () => {
      Toast.success("用户删除成功");
      queryClient.invalidateQueries({ queryKey: ["users"] });
    },
    onError: (error: any) => {
      Toast.error(error.message || "删除失败");
    },
  });

  // 切换用户状态
  const toggleStatusMutation = useMutation({
    mutationFn: (id: string) => userService.toggleUserStatus(id),
    onSuccess: () => {
      Toast.success("用户状态更新成功");
      queryClient.invalidateQueries({ queryKey: ["users"] });
    },
    onError: (error: any) => {
      Toast.error(error.message || "状态更新失败");
    },
  });

  // 重置密码
  const resetPasswordMutation = useMutation({
    mutationFn: ({ id, password }: { id: string; password: string }) =>
      userService.resetPassword(id, password),
    onSuccess: () => {
      Toast.success("密码重置成功");
      setIsResetPasswordModalVisible(false);
      setResetPasswordUser(null);
      setNewPassword("");
    },
    onError: (error: any) => {
      Toast.error(error.message || "密码重置失败");
    },
  });

  const handleSearch = (value: string) => {
    setParams({ ...params, search: value, page: 1 });
  };

  const handleFilterChange = (key: string, value: any) => {
    setParams({ ...params, [key]: value, page: 1 });
  };

  const handlePageChange = (page: number, pageSize?: number) => {
    setParams({ ...params, page, limit: pageSize || params.limit });
  };

  const handleCreate = () => {
    setEditingUser(null);
    setIsModalVisible(true);
  };

  const handleEdit = (user: User) => {
    setEditingUser(user);
    setIsModalVisible(true);
  };

  const handleDelete = (user: User) => {
    deleteMutation.mutate(user.id);
  };

  const handleToggleStatus = (user: User) => {
    toggleStatusMutation.mutate(user.id);
  };

  const handleResetPassword = (user: User) => {
    setResetPasswordUser(user);
    setNewPassword("");
    setIsResetPasswordModalVisible(true);
  };

  const handleResetPasswordConfirm = () => {
    if (!resetPasswordUser || !newPassword) {
      Toast.error("请输入新密码");
      return;
    }
    if (newPassword.length < 6) {
      Toast.error("密码长度不能少于6位");
      return;
    }
    resetPasswordMutation.mutate({
      id: resetPasswordUser.id,
      password: newPassword,
    });
  };

  const columns = [
    {
      title: "用户名",
      dataIndex: "username",
      key: "username",
      render: (text: string, record: User) => (
        <Space>
          <IconUser />
          <span>{text}</span>
          {!record.isActive && <Tag color="red">已禁用</Tag>}
        </Space>
      ),
    },
    {
      title: "姓名",
      dataIndex: "fullName",
      key: "fullName",
    },
    {
      title: "邮箱",
      dataIndex: "email",
      key: "email",
    },
    {
      title: "角色",
      dataIndex: "role",
      key: "role",
      render: (role: any) => <Tag color="blue">{role?.name || "-"}</Tag>,
    },
    {
      title: "部门",
      dataIndex: "department",
      key: "department",
      render: (text: string) => text || "-",
    },
    {
      title: "手机号",
      dataIndex: "phone",
      key: "phone",
      render: (text: string) => text || "-",
    },
    {
      title: "状态",
      dataIndex: "isActive",
      key: "isActive",
      render: (isActive: boolean) => (
        <Tag color={isActive ? "green" : "red"}>{isActive ? "正常" : "禁用"}</Tag>
      ),
    },
    {
      title: "创建时间",
      dataIndex: "createdAt",
      key: "createdAt",
      render: (text: string) => new Date(text).toLocaleString(),
    },
    {
      title: "操作",
      key: "actions",
      render: (_: any, record: User) => (
        <Space>
          <Button
            theme="borderless"
            type="primary"
            size="small"
            icon={<IconEdit />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Button
            theme="borderless"
            type="secondary"
            size="small"
            icon={<IconLock />}
            onClick={() => handleResetPassword(record)}
          >
            重置密码
          </Button>
          <Button
            theme="borderless"
            type="warning"
            size="small"
            onClick={() => handleToggleStatus(record)}
          >
            {record.isActive ? "禁用" : "启用"}
          </Button>
          <Popconfirm
            title="确定删除此用户吗？"
            onConfirm={() => handleDelete(record)}
            okText="确定"
            cancelText="取消"
          >
            <Button theme="borderless" type="danger" size="small" icon={<IconDelete />}>
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div className="space-y-6">
      <div className="page-header">
        <h1 className="page-title">用户管理</h1>
        <p className="page-subtitle">管理系统用户和权限</p>
      </div>

      <div className="card">
        {/* 搜索和筛选 */}
        <div className="flex flex-wrap gap-4 mb-6">
          <Input
            prefix={<IconSearch />}
            placeholder="搜索用户名、姓名或邮箱"
            style={{ width: 300 }}
            onEnterPress={e => handleSearch(e.currentTarget.value)}
            showClear
          />
          <Select
            placeholder="角色筛选"
            style={{ width: 150 }}
            showClear
            onChange={value => handleFilterChange("roleId", value)}
          >
            {rolesData?.data?.map(role => (
              <Select.Option key={role.id} value={role.id}>
                {role.name}
              </Select.Option>
            ))}
          </Select>
          <Select
            placeholder="部门筛选"
            style={{ width: 150 }}
            showClear
            onChange={value => handleFilterChange("department", value)}
          >
            {departmentsData?.data?.map(dept => (
              <Select.Option key={dept} value={dept}>
                {dept}
              </Select.Option>
            ))}
          </Select>
          <Select
            placeholder="状态筛选"
            style={{ width: 120 }}
            showClear
            onChange={value => handleFilterChange("isActive", value)}
          >
            <Select.Option value="true">正常</Select.Option>
            <Select.Option value="false">禁用</Select.Option>
          </Select>
          <Button icon={<IconRefresh />} onClick={() => refetch()}>
            刷新
          </Button>
          <Button type="primary" icon={<IconPlus />} onClick={handleCreate}>
            新建用户
          </Button>
        </div>

        {/* 用户表格 */}
        <Table
          columns={columns}
          dataSource={usersData?.data?.users || []}
          rowKey="id"
          loading={isLoading}
          pagination={false}
          scroll={{ x: 1200 }}
        />

        {/* 分页 */}
        {usersData?.data && (
          <div className="flex justify-end mt-4">
            <Pagination
              currentPage={usersData.data.page}
              total={usersData.data.total}
              pageSize={usersData.data.limit}
              showSizeChanger
              showQuickJumper
              showTotal
              onPageChange={handlePageChange}
            />
          </div>
        )}
      </div>

      {/* 用户表单弹窗 */}
      <Modal
        title={editingUser ? "编辑用户" : "新建用户"}
        visible={isModalVisible}
        onCancel={() => setIsModalVisible(false)}
        footer={null}
        width={600}
      >
        <UserForm
          user={editingUser as any}
          onSuccess={() => {
            setIsModalVisible(false);
            queryClient.invalidateQueries({ queryKey: ["users"] });
          }}
          onCancel={() => setIsModalVisible(false)}
        />
      </Modal>

      {/* 重置密码弹窗 */}
      <Modal
        title="重置密码"
        visible={isResetPasswordModalVisible}
        onOk={handleResetPasswordConfirm}
        onCancel={() => {
          setIsResetPasswordModalVisible(false);
          setResetPasswordUser(null);
          setNewPassword("");
        }}
        confirmLoading={resetPasswordMutation.isPending}
      >
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              用户: {resetPasswordUser?.fullName} ({resetPasswordUser?.username})
            </label>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">新密码</label>
            <Input
              mode="password"
              value={newPassword}
              onChange={value => setNewPassword(value)}
              placeholder="请输入新密码（至少6位）"
            />
          </div>
        </div>
      </Modal>
    </div>
  );
}
