import React, { useState, useEffect, useRef } from "react";
import {
  Card,
  Tabs,
  TabPane,
  Button,
  Table,
  Modal,
  Form,
  Input,
  Select,
  Switch,
  Space,
  Typography,
  Tag,
  Divider,
  Toast,
  Spin,
  Empty,
  Descriptions,
  Progress,
  Tooltip,
} from "@douyinfe/semi-ui";
import {
  IconPlus,
  IconEdit,
  IconDelete,
  IconRefresh,
  IconSetting,
  IconEyeOpened,
  IconCode,
  IconActivity,
  IconPieChartStroked,
  IconKey,
} from "@douyinfe/semi-icons";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { safeNumber } from "@/utils/data-helpers";
import {
  getAIConfigurations,
  createAIConfiguration,
  updateAIConfiguration,
  deleteAIConfiguration,
  getPromptTemplates,
  createPromptTemplate,
  updatePromptTemplate,
  deletePromptTemplate,
  getAnalysisHistory,
  getAIStatistics,
  testAIConfiguration,
  testPromptTemplate,
} from "@/services/aiManagement";

const { Title, Text, Paragraph } = Typography;

// 类型安全的辅助函数
const getSafeData = <T,>(response: any): T[] => {
  return Array.isArray(response?.data?.data) ? response.data.data : [];
};

const getSafeTotal = (response: any): number => {
  return safeNumber(response?.data?.pagination?.total, 0);
};

const getSafeStats = (response: any): AIStatistics => {
  if (response?.data && typeof response.data === 'object' && 'totalRequests' in response.data) {
    return response.data as AIStatistics;
  }
  return {
    totalRequests: 0,
    successfulRequests: 0,
    failedRequests: 0,
    averageResponseTime: 0,
    successRate: 0,
    providerUsage: {},
    templateUsage: {}
  };
};

// 类型定义
interface AIConfiguration {
  id: string;
  provider: "OPENAI" | "ANTHROPIC" | "GEMINI";
  model: string;
  apiKey: string;
  maxTokens: number;
  temperature: number;
  isActive: boolean;
  createdAt?: string;
  updatedAt?: string;
}

interface PromptTemplate {
  id: string;
  name: string;
  type: "TICKET_ANALYSIS" | "CATEGORY_SUGGESTION" | "PRIORITY_ASSESSMENT";
  provider: string;
  content: string;
  isActive: boolean;
  createdAt?: string;
  updatedAt?: string;
}

interface AnalysisRecord {
  id: string;
  inputData: string;
  outputData: string;
  provider: string;
  model: string;
  status: "PENDING" | "COMPLETED" | "FAILED";
  processingTime?: number;
  createdAt: string;
}

interface AIStatistics {
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  averageResponseTime?: number;
  successRate: number;
  providerUsage: Record<string, { count: number; percentage: number }>;
  templateUsage: Record<string, { count: number; percentage: number }>;
}

// AI配置管理组件
const AIConfigurationTab = () => {
  const configFormRef = useRef<any>();
  const [showConfigModal, setShowConfigModal] = useState(false);
  const [editingConfig, setEditingConfig] = useState<AIConfiguration | null>(null);
  const queryClient = useQueryClient();

  const { data: configurations, isLoading } = useQuery({
    queryKey: ["ai-configurations"],
    queryFn: () => getAIConfigurations(),
  });

  const createMutation = useMutation({
    mutationFn: createAIConfiguration,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["ai-configurations"] });
      Toast.success("AI配置已创建");
      setShowConfigModal(false);
    },
    onError: (error: any) => {
      Toast.error(error.message || "创建失败");
    },
  });

  const updateMutation = useMutation({
    mutationFn: ({ id, data }: { id: string; data: any }) => updateAIConfiguration(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["ai-configurations"] });
      Toast.success("AI配置已更新");
      setShowConfigModal(false);
    },
    onError: (error: any) => {
      Toast.error(error.message || "更新失败");
    },
  });

  const deleteMutation = useMutation({
    mutationFn: deleteAIConfiguration,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["ai-configurations"] });
      Toast.success("AI配置已删除");
    },
    onError: (error: any) => {
      Toast.error(error.message || "删除失败");
    },
  });

  const configColumns = [
    {
      title: "AI提供商",
      dataIndex: "provider",
      render: (provider: AIConfiguration["provider"]) => {
        const providerMap: Record<
          AIConfiguration["provider"],
          { color: "green" | "blue" | "orange" | "grey"; text: string }
        > = {
          OPENAI: { color: "green", text: "OpenAI" },
          ANTHROPIC: { color: "blue", text: "Anthropic" },
          GEMINI: { color: "orange", text: "Google Gemini" },
        };
        const config = providerMap[provider] || { color: "grey", text: provider };
        return <Tag color={config.color}>{config.text}</Tag>;
      },
    },
    {
      title: "模型",
      dataIndex: "model",
      render: (model: string) => <Text code>{model}</Text>,
    },
    {
      title: "状态",
      dataIndex: "isActive",
      render: (isActive: boolean) => (
        <Tag color={isActive ? "green" : "grey"}>{isActive ? "启用" : "禁用"}</Tag>
      ),
    },
    {
      title: "最大Token",
      dataIndex: "maxTokens",
      render: (tokens: number) => <Text type="secondary">{tokens?.toLocaleString()}</Text>,
    },
    {
      title: "温度",
      dataIndex: "temperature",
      render: (temp: number) => <Text type="secondary">{temp}</Text>,
    },
    {
      title: "操作",
      render: (_: any, record: AIConfiguration) => (
        <Space>
          <Button
            theme="borderless"
            type="primary"
            icon={<IconEdit />}
            onClick={() => handleEditConfig(record)}
          >
            编辑
          </Button>
          <Button
            theme="borderless"
            type="danger"
            icon={<IconDelete />}
            onClick={() => handleDeleteConfig(record.id)}
          >
            删除
          </Button>
        </Space>
      ),
    },
  ];

  const handleEditConfig = (config: AIConfiguration) => {
    setEditingConfig(config);
    configFormRef.current?.setValues(config);
    setShowConfigModal(true);
  };

  const handleDeleteConfig = (id: string) => {
    Modal.confirm({
      title: "确认删除",
      content: "确定要删除这个AI配置吗？",
      onOk: () => {
        deleteMutation.mutate(id);
      },
    });
  };

  const handleConfigSubmit = (values: any) => {
    if (editingConfig) {
      updateMutation.mutate({ id: editingConfig.id, data: values });
    } else {
      createMutation.mutate(values);
    }
  };

  return (
    <div>
      <div
        style={{
          marginBottom: 16,
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
        }}
      >
        <Title heading={4}>AI配置管理</Title>
        <Space>
          <Button
            icon={<IconRefresh />}
            onClick={() => {
              /* 刷新逻辑 */
            }}
          >
            刷新
          </Button>
          <Button
            theme="solid"
            type="primary"
            icon={<IconPlus />}
            onClick={() => {
              setEditingConfig(null);
              configFormRef.current?.reset();
              setShowConfigModal(true);
            }}
          >
            添加配置
          </Button>
        </Space>
      </div>

      <Card>
        <Table
          columns={configColumns}
          dataSource={getSafeData<AIConfiguration>(configurations)}
          loading={isLoading}
          pagination={{
            pageSize: 10,
            total: getSafeTotal(configurations),
          }}
          empty={<Empty description="暂无AI配置" />}
        />
      </Card>

      {/* 配置编辑模态框 */}
      <Modal
        title={editingConfig ? "编辑AI配置" : "添加AI配置"}
        visible={showConfigModal}
        onCancel={() => setShowConfigModal(false)}
        onOk={() => configFormRef.current?.submitForm()}
        width={600}
      >
        <Form<AIConfiguration>
          ref={configFormRef}
          labelPosition="left"
          labelWidth={120}
          onSubmit={handleConfigSubmit}
        >
          <Form.Select
            field="provider"
            label="AI提供商"
            placeholder="选择AI提供商"
            rules={[{ required: true, message: "请选择AI提供商" }]}
          >
            <Form.Select.Option value="OPENAI">OpenAI</Form.Select.Option>
            <Form.Select.Option value="ANTHROPIC">Anthropic</Form.Select.Option>
            <Form.Select.Option value="GEMINI">Google Gemini</Form.Select.Option>
          </Form.Select>

          <Form.Input
            field="model"
            label="模型名称"
            placeholder="如: gpt-4, claude-3-sonnet"
            rules={[{ required: true, message: "请输入模型名称" }]}
          />

          <Form.Input
            field="apiKey"
            label="API密钥"
            type="password"
            placeholder="输入API密钥"
            rules={[{ required: true, message: "请输入API密钥" }]}
          />

          <Form.InputNumber
            field="maxTokens"
            label="最大Token数"
            placeholder="如: 4000"
            min={100}
            max={100000}
            rules={[{ required: true, message: "请输入最大Token数" }]}
          />

          <Form.InputNumber
            field="temperature"
            label="温度"
            placeholder="0.0 - 2.0"
            min={0}
            max={2}
            step={0.1}
            rules={[{ required: true, message: "请输入温度值" }]}
          />

          <Form.Switch field="isActive" label="启用配置" />
        </Form>
      </Modal>
    </div>
  );
};

// 提示词模板管理组件
const PromptTemplateTab = () => {
  const templateFormRef = useRef<any>();
  const [showTemplateModal, setShowTemplateModal] = useState(false);
  const [editingTemplate, setEditingTemplate] = useState<PromptTemplate | null>(null);

  // 调试打印 获取提示词模板 templates
  const { data: templates, isLoading, error } = useQuery({
    queryKey: ["prompt-templates"],
    queryFn: async () => {
      console.log("🔍 开始获取提示词模板...");
      try {
        const result = await getPromptTemplates();
        return result;
      } catch (error) {
        console.error("❌ 获取提示词模板失败:", error);
        throw error;
      }
    },
  });

  // 添加 useEffect 来监听数据变化
  useEffect(() => {
    console.log("📝 提示词模板数据更新:", {
      isLoading,
      hasData: !!templates,
      dataStructure: templates,
      templatesArray: templates?.data?.data || [],
      error
    });
  }, [templates, isLoading, error]);

  const templateColumns = [
    {
      title: "模板名称",
      dataIndex: "name",
      render: (name: string) => <Text strong>{name}</Text>,
    },
    {
      title: "类型",
      dataIndex: "category", // 修改：从 "type" 改为 "category"
      render: (category: string) => {
        const categoryMap: Record<string, { color: "blue" | "green" | "orange" | "grey"; text: string }> = {
          ticket_analysis: { color: "blue", text: "工单分析" },
          category_suggestion: { color: "green", text: "分类建议" },
          priority_assessment: { color: "orange", text: "优先级评估" },
        };
        const config = categoryMap[category] || { color: "grey", text: category };
        return <Tag color={config.color}>{config.text}</Tag>;
      },
    },
    {
      title: "AI提供商",
      dataIndex: "provider",
      render: (provider: string) => <Text code>{provider || "通用"}</Text>, // 修改：处理null值
    },
    {
      title: "状态",
      dataIndex: "isActive",
      render: (isActive: boolean) => (
        <Tag color={isActive ? "green" : "grey"}>{isActive ? "启用" : "禁用"}</Tag>
      ),
    },
    {
      title: "更新时间",
      dataIndex: "updatedAt",
      render: (date: string) => new Date(date).toLocaleString(),
    },
    {
      title: "操作",
      render: (_: any, record: any) => ( // 修改：使用any类型以匹配API数据结构
        <Space>
          <Button
            theme="borderless"
            type="tertiary"
            icon={<IconEyeOpened />}
            onClick={() => handleViewTemplate(record)}
          >
            查看
          </Button>
          <Button
            theme="borderless"
            type="primary"
            icon={<IconEdit />}
            onClick={() => handleEditTemplate(record)}
          >
            编辑
          </Button>
          <Button
            theme="borderless"
            type="danger"
            icon={<IconDelete />}
            onClick={() => handleDeleteTemplate(record.id)}
          >
            删除
          </Button>
        </Space>
      ),
    },
  ];

  const handleViewTemplate = (template: any) => { // 修改：使用any类型以匹配API数据结构
    Modal.info({
      title: `提示词模板: ${template.name}`,
      content: (
        <div>
          <Descriptions
            data={[
              { key: "类型", value: template.category },
              { key: "AI提供商", value: template.provider || "通用" },
              { key: "状态", value: template.isActive ? "启用" : "禁用" },
            ]}
          />
          <Divider />
          <Text strong>模板内容:</Text>
          <div style={{ marginTop: 8, padding: 12, backgroundColor: "#f8f9fa", borderRadius: 4 }}>
            <Text code style={{ whiteSpace: "pre-wrap" }}>
              {template.template} {/* 修改：从 template.content 改为 template.template */}
            </Text>
          </div>
        </div>
      ),
      width: 800,
    });
  };

  const handleEditTemplate = (template: any) => { // 修改：使用any类型以匹配API数据结构
    setEditingTemplate(template);
    // 修改：将API字段映射到表单字段
    const formData = {
      ...template,
      type: template.category, // 将category映射为type
      content: template.template, // 将template映射为content
    };
    templateFormRef.current?.setValues(formData);
    setShowTemplateModal(true);
  };

  const handleDeleteTemplate = (id: string) => {
    Modal.confirm({
      title: "确认删除",
      content: "确定要删除这个提示词模板吗？",
      onOk: () => {
        Toast.success("模板已删除");
      },
    });
  };

  const handleTemplateSubmit = (values: any) => {
    console.log("提交模板:", values);
    setShowTemplateModal(false);
    Toast.success(editingTemplate ? "模板已更新" : "模板已添加");
  };

  return (
    <div>
      <div
        style={{
          marginBottom: 16,
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
        }}
      >
        <Title heading={4}>提示词模板管理</Title>
        <Space>
          <Button
            type="primary"
            icon={<IconPlus />}
            onClick={() => {
              setEditingTemplate(null);
              templateFormRef.current?.reset();
              setShowTemplateModal(true);
            }}
          >
            添加模板
          </Button>
        </Space>
      </div>

      <Card>
                  <Table
            columns={templateColumns}
            dataSource={getSafeData<PromptTemplate>(templates)}
            loading={isLoading}
            pagination={{ pageSize: 10 }}
            empty={<Empty description="暂无提示词模板" />}
          />
      </Card>

      {/* 模板编辑模态框 */}
      <Modal
        title={editingTemplate ? "编辑提示词模板" : "添加提示词模板"}
        visible={showTemplateModal}
        onCancel={() => setShowTemplateModal(false)}
        onOk={() => templateFormRef.current?.submitForm()}
        width={800}
      >
        <Form<PromptTemplate>
          ref={templateFormRef}
          labelPosition="top"
          onSubmit={handleTemplateSubmit}
        >
          <Form.Input
            field="name"
            label="模板名称"
            placeholder="输入模板名称"
            rules={[{ required: true, message: "请输入模板名称" }]}
          />

          <Form.Select
            field="type"
            label="模板类型"
            placeholder="选择模板类型"
            rules={[{ required: true, message: "请选择模板类型" }]}
          >
            <Form.Select.Option value="TICKET_ANALYSIS">工单分析</Form.Select.Option>
            <Form.Select.Option value="CATEGORY_SUGGESTION">分类建议</Form.Select.Option>
            <Form.Select.Option value="PRIORITY_ASSESSMENT">优先级评估</Form.Select.Option>
          </Form.Select>

          <Form.Select
            field="provider"
            label="AI提供商"
            placeholder="选择AI提供商"
            rules={[{ required: true, message: "请选择AI提供商" }]}
          >
            <Form.Select.Option value="OPENAI">OpenAI</Form.Select.Option>
            <Form.Select.Option value="ANTHROPIC">Anthropic</Form.Select.Option>
            <Form.Select.Option value="GEMINI">Google Gemini</Form.Select.Option>
          </Form.Select>

          <Form.TextArea
            field="content"
            label="模板内容"
            placeholder="输入提示词模板内容，可使用变量如 {{description}}, {{context}} 等"
            rows={8}
            rules={[{ required: true, message: "请输入模板内容" }]}
          />

          <Form.Switch field="isActive" label="启用模板" />
        </Form>
      </Modal>
    </div>
  );
};

// 分析历史组件
const AnalysisHistoryTab = () => {
  const { data: analysisHistory, isLoading } = useQuery({
    queryKey: ["analysis-history"],
    queryFn: () => getAnalysisHistory(),
  });

  const historyColumns = [
    {
      title: "请求ID",
      dataIndex: "id",
      render: (id: string) => <Text code>{id.slice(0, 8)}...</Text>,
    },
    {
      title: "工单描述",
      dataIndex: "inputData",
      render: (inputData: string) => {
        const description = JSON.parse(inputData)?.description || "";
        return (
          <Tooltip content={description}>
            <Text ellipsis style={{ maxWidth: 200 }}>
              {description}
            </Text>
          </Tooltip>
        );
      },
    },
    {
      title: "AI提供商",
      dataIndex: "provider",
      render: (provider: string) => <Tag color="blue">{provider}</Tag>,
    },
    {
      title: "状态",
      dataIndex: "status",
      render: (status: AnalysisRecord["status"]) => {
        const statusMap: Record<
          AnalysisRecord["status"],
          { color: "orange" | "green" | "red" | "grey"; text: string }
        > = {
          PENDING: { color: "orange", text: "处理中" },
          COMPLETED: { color: "green", text: "已完成" },
          FAILED: { color: "red", text: "失败" },
        };
        const config = statusMap[status] || { color: "grey" as const, text: status };
        return <Tag color={config.color}>{config.text}</Tag>;
      },
    },
    {
      title: "处理时间",
      dataIndex: "processingTime",
      render: (time: number) => (time ? `${time}ms` : "-"),
    },
    {
      title: "创建时间",
      dataIndex: "createdAt",
      render: (date: string) => new Date(date).toLocaleString(),
    },
    {
      title: "操作",
      render: (_: any, record: AnalysisRecord) => (
        <Space>
          <Button
            theme="borderless"
            type="primary"
            icon={<IconEyeOpened />}
            onClick={() => handleViewAnalysis(record)}
          >
            查看详情
          </Button>
        </Space>
      ),
    },
  ];

  const handleViewAnalysis = (record: AnalysisRecord) => {
    const inputData = JSON.parse(record.inputData || "{}");
    const outputData = JSON.parse(record.outputData || "{}");

    Modal.info({
      title: `分析详情 - ${record.id}`,
      content: (
        <div>
          <Descriptions
            data={[
              { key: "请求ID", value: record.id },
              { key: "AI提供商", value: record.provider },
              { key: "模型", value: record.model },
              { key: "状态", value: record.status },
              {
                key: "处理时间",
                value: record.processingTime ? `${record.processingTime}ms` : "-",
              },
              { key: "创建时间", value: new Date(record.createdAt).toLocaleString() },
            ]}
          />
          <Divider />
          <div style={{ marginBottom: 16 }}>
            <Text strong>输入数据:</Text>
            <div style={{ marginTop: 8, padding: 12, backgroundColor: "#f8f9fa", borderRadius: 4 }}>
              <Text code style={{ whiteSpace: "pre-wrap" }}>
                {JSON.stringify(inputData, null, 2)}
              </Text>
            </div>
          </div>
          <div>
            <Text strong>输出结果:</Text>
            <div style={{ marginTop: 8, padding: 12, backgroundColor: "#f8f9fa", borderRadius: 4 }}>
              <Text code style={{ whiteSpace: "pre-wrap" }}>
                {JSON.stringify(outputData, null, 2)}
              </Text>
            </div>
          </div>
        </div>
      ),
      width: 800,
    });
  };

  return (
    <div>
      <div
        style={{
          marginBottom: 16,
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
        }}
      >
        <Title heading={4}>AI分析历史</Title>
        <Space>
          <Button
            icon={<IconRefresh />}
            onClick={() => {
              /* 刷新逻辑 */
            }}
          >
            刷新
          </Button>
        </Space>
      </div>

      <Card>
        <Table
          columns={historyColumns}
          dataSource={getSafeData<AnalysisRecord>(analysisHistory)}
          loading={isLoading}
          pagination={{ pageSize: 10 }}
          empty={<Empty description="暂无分析历史" />}
        />
      </Card>
    </div>
  );
};

// 统计报告组件
const StatisticsTab = () => {
  const { data: stats, isLoading } = useQuery({
    queryKey: ["ai-statistics"],
    queryFn: () => getAIStatistics(),
  });

  const statsData = getSafeStats(stats);

  return (
    <div>
      <div style={{ marginBottom: 16 }}>
        <Title heading={4}>AI使用统计</Title>
      </div>

      <div
        style={{
          display: "grid",
          gridTemplateColumns: "repeat(auto-fit, minmax(300px, 1fr))",
          gap: 16,
          marginBottom: 24,
        }}
      >
        <Card title="总体统计" headerStyle={{ borderBottom: "1px solid #e8e8e8" }}>
          <Descriptions
            data={[
              { key: "总请求数", value: statsData.totalRequests?.toLocaleString() || "0" },
              { key: "成功请求", value: statsData.successfulRequests?.toLocaleString() || "0" },
              { key: "失败请求", value: statsData.failedRequests?.toLocaleString() || "0" },
              {
                key: "平均响应时间",
                value: statsData.averageResponseTime ? `${statsData.averageResponseTime}ms` : "-",
              },
            ]}
          />
        </Card>

        <Card title="AI提供商使用情况" headerStyle={{ borderBottom: "1px solid #e8e8e8" }}>
          <div style={{ display: "flex", flexDirection: "column", gap: 12 }}>
            {Object.entries(statsData.providerUsage || {}).map(([provider, usage]) => (
              <div key={provider}>
                <div style={{ display: "flex", justifyContent: "space-between", marginBottom: 4 }}>
                  <Text>{provider}</Text>
                  <Text type="secondary">{usage.count || 0} 次</Text>
                </div>
                <Progress percent={usage.percentage || 0} showInfo={false} size="small" />
              </div>
            ))}
          </div>
        </Card>

        <Card title="模板使用统计" headerStyle={{ borderBottom: "1px solid #e8e8e8" }}>
          <div style={{ display: "flex", flexDirection: "column", gap: 12 }}>
            {Object.entries(statsData.templateUsage || {}).map(([template, usage]) => (
              <div key={template}>
                <div style={{ display: "flex", justifyContent: "space-between", marginBottom: 4 }}>
                  <Text ellipsis style={{ maxWidth: 150 }}>
                    {template}
                  </Text>
                  <Text type="secondary">{usage.count || 0} 次</Text>
                </div>
                <Progress percent={usage.percentage || 0} showInfo={false} size="small" />
              </div>
            ))}
          </div>
        </Card>

        <Card title="成功率统计" headerStyle={{ borderBottom: "1px solid #e8e8e8" }}>
          <div style={{ textAlign: "center" }}>
            <Progress
              type="circle"
              percent={statsData.successRate || 0}
              format={percent => `${percent}%`}
              width={120}
            />
            <div style={{ marginTop: 12 }}>
              <Text type="secondary">整体成功率</Text>
            </div>
          </div>
        </Card>
      </div>

      <Card title="最近7天趋势" headerStyle={{ borderBottom: "1px solid #e8e8e8" }}>
        <div
          style={{ height: 300, display: "flex", alignItems: "center", justifyContent: "center" }}
        >
          <Text type="secondary">图表功能开发中...</Text>
        </div>
      </Card>
    </div>
  );
};

export default function AIManagementPage() {
  return (
    <div style={{ padding: 24 }}>
      <div style={{ marginBottom: 24 }}>
        <Title heading={2}>AI集成管理</Title>
        <Paragraph type="secondary">管理AI配置、提示词模板、分析历史和系统统计</Paragraph>
      </div>

      <Tabs type="line" defaultActiveKey="config">
        <TabPane
          tab={
            <span>
              <IconSetting style={{ marginRight: 4 }} />
              AI配置
            </span>
          }
          itemKey="config"
        >
          <AIConfigurationTab />
        </TabPane>

        <TabPane
          tab={
            <span>
              <IconCode style={{ marginRight: 4 }} />
              提示词模板
            </span>
          }
          itemKey="templates"
        >
          <PromptTemplateTab />
        </TabPane>

        <TabPane
          tab={
            <span>
              <IconActivity style={{ marginRight: 4 }} />
              分析历史
            </span>
          }
          itemKey="history"
        >
          <AnalysisHistoryTab />
        </TabPane>

        <TabPane
          tab={
            <span>
              <IconPieChartStroked style={{ marginRight: 4 }} />
              统计报告
            </span>
          }
          itemKey="stats"
        >
          <StatisticsTab />
        </TabPane>
      </Tabs>
    </div>
  );
}
