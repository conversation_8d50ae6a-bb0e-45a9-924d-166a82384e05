import React, { useEffect, useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { useGlobalWebSocket } from "@/contexts/WebSocketContext";
import { ConnectionStatus, RealtimeChannelType } from "@/composables/useWebSocket";
import { get } from "@/utils/request";
import { debugDataMapping, validateDataMapping } from "@/utils/data-mapping-debug";
import { calculateAndValidateMetrics } from "@/utils/metrics-calculator";
import {
  IllustrationConstruction,
  IllustrationConstructionDark,
} from "@douyinfe/semi-illustrations";
import {
  Card,
  Row,
  Col,
  Progress,
  Avatar,
  Typography,
  Table,
  Tag,
  Badge,
  Button,
  Modal,
  List,
  Timeline,
  Spin,
  Empty,
  Divider,
  Space,
  Tooltip,
  Switch,
  Select,
  Skeleton,
  Tabs,
  TabPane,
  Typography as SemiTypography,
  Button as SemiButton,
  Popover,
} from "@douyinfe/semi-ui";
import {
  IconActivity,
  IconServer,
  IconCloud,
  IconBell,
  IconRefresh,
  IconSetting,
  IconEyeOpened,
  IconAlertTriangle,
  IconTickCircle,
  IconClock,
  IconBarChartHStroked as IconChart,
  IconWifi,
  IconArchive,
  IconDesktop,
} from "@douyinfe/semi-icons";
import { useUIStore } from "@/stores";
import { DiskUsageDonut } from "@/components/dashboard/DiskUsageDonut";

const { Title, Text } = Typography;

interface SystemStatus {
  overall: "healthy" | "warning" | "critical";
  components: {
    database: ComponentStatus;
    redis: ComponentStatus;
    api: ComponentStatus;
    storage: ComponentStatus;
  };
  metrics: {
    cpu: number;
    memory: number;
    disk: number;
    network: number;
  };
  alerts: Alert[];
  logs: SystemLog[];
  performance: PerformanceMetrics;
}

interface ComponentStatus {
  status: "healthy" | "warning" | "critical" | "down";
  uptime: string;
  responseTime: number;
  lastCheck: string;
  details: string;
}

interface Alert {
  id: string;
  type: "error" | "warning" | "info";
  title: string;
  message: string;
  timestamp: string;
  component: string;
  resolved: boolean;
}

interface SystemLog {
  id: string;
  timestamp: string;
  level: "error" | "warn" | "info" | "debug";
  service: string;
  message: string;
  metadata?: any;
}

interface PerformanceMetrics {
  requests: {
    total: number;
    success: number;
    errors: number;
    avgResponseTime: number;
  };
  database: {
    connections: number;
    queries: number;
    slowQueries: number;
  };
  cache: {
    hitRate: number;
    misses: number;
    evictions: number;
  };
}

const SystemMonitorSkeleton = () => (
  <div className="space-y-6">
    <div className="ops-page-header">
      <div className="flex justify-between items-start flex-wrap gap-4">
        <div>
          <Skeleton.Title style={{ width: 200, marginBottom: 16 }} />
          <Skeleton.Paragraph rows={1} style={{ width: 400 }} />
        </div>
        <div className="flex items-center space-x-3">
          <Skeleton.Button style={{ width: 100 }} />
          <Skeleton.Button style={{ width: 100 }} />
          <Skeleton.Button style={{ width: 100 }} />
        </div>
      </div>
    </div>

    <div className="ops-card">
      <div className="ops-card-body">
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center space-x-6">
            <Skeleton.Avatar size="large" />
            <div>
              <Skeleton.Title style={{ width: 150, marginBottom: 8 }} />
              <Skeleton.Paragraph rows={1} style={{ width: 250 }} />
            </div>
          </div>
          <Skeleton.Button style={{ width: 120, height: 48 }} />
        </div>

        <Row gutter={[24, 24]}>
          <Col span={24} lg={12}>
            <Skeleton.Title style={{ width: 100, marginBottom: 24 }} />
            <div className="space-y-4">
              <Skeleton.Paragraph rows={4} />
            </div>
          </Col>
          <Col span={24} lg={12}>
            <Skeleton.Title style={{ width: 100, marginBottom: 24 }} />
            <div className="space-y-6">
              <Skeleton.Paragraph rows={1} />
              <Skeleton.Paragraph rows={1} />
              <Skeleton.Paragraph rows={1} />
              <Skeleton.Paragraph rows={1} />
            </div>
          </Col>
        </Row>
      </div>
    </div>

    <Row gutter={[24, 24]}>
      <Col span={24} lg={8}>
        <div className="ops-card">
          <div className="ops-card-header">
            <Skeleton.Title style={{ width: 120 }} />
          </div>
          <div className="ops-card-body">
            <Skeleton.Paragraph rows={6} />
          </div>
        </div>
      </Col>
      <Col span={24} lg={8}>
        <div className="ops-card">
          <div className="ops-card-header">
            <Skeleton.Title style={{ width: 120 }} />
          </div>
          <div className="ops-card-body">
            <Skeleton.Paragraph rows={6} />
          </div>
        </div>
      </Col>
      <Col span={24} lg={8}>
        <div className="ops-card">
          <div className="ops-card-header">
            <Skeleton.Title style={{ width: 120 }} />
          </div>
          <div className="ops-card-body">
            <Skeleton.Paragraph rows={6} />
          </div>
        </div>
      </Col>
    </Row>
  </div>
);

export default function SystemMonitorPage() {
  const { setBreadcrumbs } = useUIStore();
  const [refreshInterval, setRefreshInterval] = useState(30); // 秒
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [selectedLogLevel, setSelectedLogLevel] = useState<string>("");
  const [isAlertModalVisible, setIsAlertModalVisible] = useState(false);
  const [selectedAlert, setSelectedAlert] = useState<Alert | null>(null);
  // 使用 Popover 展示“更多”信息，不再使用抽屉

  // 使用全局WebSocket连接
  const webSocket = useGlobalWebSocket();
  const wsState = webSocket.state;

  useEffect(() => {
    setBreadcrumbs([
      { text: "首页", href: "/", icon: "🏠" },
      { text: "系统管理", icon: "⚙️" },
      { text: "系统监控", icon: "📊" },
    ]);
  }, [setBreadcrumbs]);

  // 获取监控仪表板数据
  const {
    data: dashboardData,
    isLoading,
    refetch,
  } = useQuery({
    queryKey: ["monitor-dashboard"],
    queryFn: async () => {
      const response = await get("/api/v1/monitor/dashboard");
      return response.data;
    },
    // 只有在WebSocket断开时才启用HTTP轮询
    refetchInterval:
      autoRefresh && wsState.status !== ConnectionStatus.CONNECTED ? refreshInterval * 1000 : false,
    retry: 1,
    retryDelay: 1000,
  });

  // 获取系统事件日志
  const { data: systemEvents, isLoading: isLogsLoading } = useQuery({
    queryKey: ["system-events", selectedLogLevel],
    queryFn: async () => {
      const params = {
        page: "1",
        limit: "20",
        ...(selectedLogLevel && { level: selectedLogLevel }),
      };

      const response = await get("/api/v1/monitor/events", params);
      return response.data;
    },
    // 只有在WebSocket断开时才启用HTTP轮询
    refetchInterval:
      autoRefresh && wsState.status !== ConnectionStatus.CONNECTED ? refreshInterval * 1000 : false,
  });

  // 转换数据格式以兼容现有UI
  const systemStatus = dashboardData
    ? {
        overall: dashboardData.summary.overall,
        components: {
          database: dashboardData.services.find(
            (s: any) => s.name.includes("MySQL") || s.name.includes("Database")
          ) || {
            status: "down",
            uptime: "0%",
            responseTime: 0,
            lastCheck: new Date().toISOString(),
            details: "数据库服务未找到",
          },
          redis: dashboardData.services.find(
            (s: any) => s.name.includes("Redis") || s.name.includes("Cache")
          ) || {
            status: "down",
            uptime: "0%",
            responseTime: 0,
            lastCheck: new Date().toISOString(),
            details: "Redis服务未找到",
          },
          api: dashboardData.services.find(
            (s: any) =>
              s.name.includes("Node.js") || s.name.includes("Application") || s.name.includes("API")
          ) || {
            status: "healthy",
            uptime: "99.9%",
            responseTime: 1,
            lastCheck: new Date().toISOString(),
            details: "API服务运行正常",
          },
          storage: {
            status: "healthy",
            uptime: "99.99%",
            responseTime: 8,
            lastCheck: new Date().toISOString(),
            details: "文件存储 - 运行正常",
          },
        },
        metrics: (() => {
          const calculatedMetrics = calculateAndValidateMetrics(dashboardData.metrics);
          return {
            cpu: calculatedMetrics.cpu,
            memory: calculatedMetrics.memory,
            disk: calculatedMetrics.disk,
            network: calculatedMetrics.network,
          };
        })(),
        alerts: dashboardData.alerts.map((alert: any) => ({
          id: alert.id,
          type: alert.severity.toLowerCase(),
          title: alert.title,
          message: alert.message,
          timestamp: alert.timestamp,
          component: alert.type.toLowerCase(),
          resolved: alert.status === "resolved",
        })),
        logs: systemEvents?.events || [],
        performance: {
          requests: {
            total: 15420,
            success: 14950,
            errors: 470,
            avgResponseTime: 65,
          },
          database: {
            connections:
              dashboardData.services.find(
                (s: any) => s.name.includes("MySQL") || s.name.includes("Database")
              )?.connections?.active || 0,
            queries:
              dashboardData.services.find(
                (s: any) => s.name.includes("MySQL") || s.name.includes("Database")
              )?.performance?.queryCount || 0,
            slowQueries:
              dashboardData.services.find(
                (s: any) => s.name.includes("MySQL") || s.name.includes("Database")
              )?.performance?.slowQueries || 0,
          },
          cache: {
            hitRate:
              dashboardData.services.find(
                (s: any) => s.name.includes("Redis") || s.name.includes("Cache")
              )?.stats?.hitRate || 0,
            misses:
              dashboardData.services.find(
                (s: any) => s.name.includes("Redis") || s.name.includes("Cache")
              )?.stats?.misses || 0,
            evictions: 5,
          },
        },
      }
    : null;

  // 自动刷新控制 - 只在WebSocket断开时启用
  useEffect(() => {
    if (autoRefresh && wsState.status !== ConnectionStatus.CONNECTED) {
      const interval = setInterval(() => {
        refetch();
      }, refreshInterval * 1000);

      return () => clearInterval(interval);
    }
  }, [autoRefresh, refreshInterval, refetch, wsState.status]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case "healthy":
        return "green";
      case "warning":
        return "orange";
      case "critical":
      case "down":
        return "red";
      default:
        return "grey";
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case "healthy":
        return "正常";
      case "warning":
        return "警告";
      case "critical":
        return "严重";
      case "down":
        return "离线";
      default:
        return "未知";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "healthy":
        return <IconTickCircle style={{ color: "var(--semi-color-success)" }} />;
      case "warning":
        return <IconAlertTriangle style={{ color: "var(--semi-color-warning)" }} />;
      case "critical":
      case "down":
        return <IconAlertTriangle style={{ color: "var(--semi-color-danger)" }} />;
      default:
        return <IconActivity />;
    }
  };

  const getMetricColor = (value: number) => {
    if (value <= 50) return "green";
    if (value <= 80) return "orange";
    return "red";
  };

  const getLogLevelColor = (level: string) => {
    switch (level) {
      case "error":
        return "red";
      case "warn":
        return "orange";
      case "info":
        return "blue";
      case "debug":
        return "grey";
      default:
        return "grey";
    }
  };

  const handleAlertClick = (alert: Alert) => {
    setSelectedAlert(alert);
    setIsAlertModalVisible(true);
  };

  if (isLoading && !systemStatus) {
    return <SystemMonitorSkeleton />;
  }

  return (
    <div className="space-y-6">
      <div className="ops-page-header">
        <div className="flex justify-between items-start flex-wrap gap-4">
          <div>
            <h1 className="ops-page-title">系统监控</h1>
            <p className="ops-page-subtitle">实时监控系统运行状态和性能指标，确保服务稳定运行</p>
          </div>
          <div className="flex items-center space-x-3">
            {/* WebSocket连接状态 */}
            <div
              className={`ops-realtime-status ${
                wsState.status === ConnectionStatus.CONNECTED
                  ? "connected"
                  : wsState.status === ConnectionStatus.CONNECTING
                    ? "connecting"
                    : "disconnected"
              }`}
            >
              <div className="ops-realtime-dot"></div>
              <span>
                {wsState.status === ConnectionStatus.CONNECTED
                  ? "实时连接"
                  : wsState.status === ConnectionStatus.CONNECTING
                    ? "连接中..."
                    : "连接断开"}
              </span>
            </div>
            <div className="flex items-center space-x-2">
              <Text>自动刷新</Text>
              <Switch checked={autoRefresh} onChange={setAutoRefresh} />
            </div>
            <Select
              value={refreshInterval}
              onChange={value => setRefreshInterval(value as number)}
              style={{ width: 100 }}
            >
              <Select.Option value={10}>10秒</Select.Option>
              <Select.Option value={30}>30秒</Select.Option>
              <Select.Option value={60}>1分钟</Select.Option>
              <Select.Option value={300}>5分钟</Select.Option>
            </Select>
            <Button icon={<IconRefresh />} onClick={() => refetch()}>
              手动刷新
            </Button>
          </div>
        </div>
      </div>

      {/* 系统整体状态 */}
      <Card>
        <div className="flex justify-between items-center">
          <div className="flex items-center space-x-4">
            {getStatusIcon(systemStatus?.overall || "healthy")}
            <Title heading={5} className="m-0">
              系统整体状态:{" "}
              <Text
                className={`font-semibold ${
                  systemStatus?.overall === "healthy"
                    ? "text-green-600"
                    : systemStatus?.overall === "warning"
                      ? "text-yellow-600"
                      : "text-red-600"
                }`}
              >
                {getStatusText(systemStatus?.overall || "healthy")}
              </Text>
            </Title>
          </div>
          <Text type="secondary">最后更新: {new Date().toLocaleString()}</Text>
        </div>
      </Card>

      <Row gutter={[24, 24]}>
        {/* 系统组件状态 */}
        <Col span={24} lg={8}>
          <Card title="组件状态" style={{ minHeight: 360 }} loading={isLoading}>
            <Table
              dataSource={Object.entries(systemStatus?.components || {}).map(
                ([key, component]) => ({
                  key,
                  name:
                    key === "database"
                      ? "数据库"
                      : key === "redis"
                        ? "Redis缓存"
                        : key === "api"
                          ? "API服务"
                          : "存储服务",
                  ...component,
                })
              )}
              pagination={false}
              columns={[
                {
                  title: "组件",
                  dataIndex: "name",
                },
                {
                  title: "状态",
                  dataIndex: "status",
                  render: (status: string) => (
                    <Tag color={getStatusColor(status)}>{getStatusText(status)}</Tag>
                  ),
                },
                {
                  title: "响应时间",
                  dataIndex: "responseTime",
                  render: (time: number) => `${time}ms`,
                },
                {
                  title: "更多",
                  dataIndex: "action",
                  render: (_: any, record: any) => {
                    const svc = (dashboardData?.services || []).find((s: any) => {
                      if (record.key === "database") return /mysql|database/i.test(s.name);
                      if (record.key === "redis") return /redis|cache/i.test(s.name);
                      if (record.key === "api") return /node|application|api/i.test(s.name);
                      return false;
                    });
                    const content = (
                      <div className="text-xs text-gray-700 space-y-2" style={{ maxWidth: 320 }}>
                        <div>
                          <Text type="secondary">状态</Text>
                          <div>
                            <Tag color={getStatusColor(record.status)}>
                              {getStatusText(record.status)}
                            </Tag>
                          </div>
                        </div>
                        {record.key === "database" && (
                          <>
                            <div>
                              连接：{svc?.connections?.active ?? 0} / {svc?.connections?.max ?? 0}
                              （active/max）
                            </div>
                            <div>慢查询：{svc?.performance?.slowQueries ?? 0}</div>
                            <div>平均RT：{svc?.performance?.avgResponseTime ?? 0} ms</div>
                            <div>
                              表/记录：{svc?.statistics?.totalTables ?? 0} /{" "}
                              {svc?.statistics?.totalRecords ?? 0}
                            </div>
                            <div>库大小：{svc?.statistics?.dbSize ?? 0} MB</div>
                          </>
                        )}
                        {record.key === "redis" && (
                          <>
                            <div>版本：{svc?.info?.version ?? "-"}</div>
                            <div>命中率：{svc?.stats?.hitRate ?? 0}%</div>
                            <div>OPS：{svc?.stats?.operationsPerSec ?? 0}</div>
                            <div>Key 数：{svc?.stats?.keyCount ?? 0}</div>
                            <div>内存：{svc?.memory?.used ?? 0}</div>
                          </>
                        )}
                        {record.key === "api" && (
                          <>
                            <div>版本：{svc?.version ?? "-"}</div>
                            <div>RSS：{svc?.memoryUsage?.rss ?? 0} MB</div>
                            <div>Heap Used：{svc?.memoryUsage?.heapUsed ?? 0} MB</div>
                            <div>EL 延迟：{svc?.eventLoop?.delay ?? 0} ms</div>
                          </>
                        )}
                        <div>
                          <Text type="secondary">最近检查</Text>
                          <div>{svc?.lastCheck ?? "-"}</div>
                        </div>
                        <div>
                          <Text type="secondary">详情</Text>
                          <div>{svc?.details ?? "-"}</div>
                        </div>
                      </div>
                    );
                    return (
                      <Popover content={content} showArrow position="right">
                        <SemiButton size="small">查看</SemiButton>
                      </Popover>
                    );
                  },
                },
              ]}
              // Popover 展示更多信息，移除表格扩展行
            />
          </Card>
        </Col>

        {/* 右侧：资源使用率 + 性能指标（同一行显示，大屏各占一半） */}
        <Col span={24} lg={16}>
          <Row gutter={[16, 16]}>
            <Col span={24} lg={12}>
              <Card title="资源使用率" style={{ minHeight: 370 }}>
                <div className="space-y-4">
                  <div className="flex items-center">
                    <Text className="w-20">CPU</Text>
                    <div className="flex-1">
                      <Progress
                        percent={systemStatus?.metrics.cpu}
                        stroke={getMetricColor(systemStatus?.metrics.cpu || 0)}
                        showInfo
                        format={p => `${p}%`}
                      />
                    </div>
                  </div>
                  <div className="flex items-center">
                    <Text className="w-20">内存</Text>
                    <div className="flex-1">
                      <Progress
                        percent={systemStatus?.metrics.memory}
                        stroke={getMetricColor(systemStatus?.metrics.memory || 0)}
                        showInfo
                        format={p => `${p}%`}
                      />
                    </div>
                  </div>
                  <div className="flex items-center">
                    <Text className="w-20">磁盘</Text>
                    <div className="flex-1 flex items-center">
                      <DiskUsageDonut percent={systemStatus?.metrics.disk || 0} />
                    </div>
                  </div>
                  <div className="flex items-center">
                    <Text className="w-20">网络</Text>
                    <div className="flex-1">
                      <Progress
                        percent={systemStatus?.metrics.network}
                        stroke={getMetricColor(systemStatus?.metrics.network || 0)}
                        showInfo
                        format={p => `${p}%`}
                      />
                    </div>
                  </div>
                </div>
              </Card>
            </Col>
            <Col span={24} lg={12}>
              <Card title="性能指标" style={{ minHeight: 360 }}>
                <List
                  dataSource={[
                    {
                      label: "请求统计",
                      value: `${systemStatus?.performance.requests.success ?? 0} / ${
                        systemStatus?.performance.requests.total ?? 0
                      }`,
                      details: `错误率: ${(
                        ((systemStatus?.performance.requests.errors ?? 0) /
                          (systemStatus?.performance.requests.total ?? 1)) *
                        100
                      ).toFixed(2)}%`,
                    },
                    {
                      label: "数据库连接",
                      value: systemStatus?.performance.database.connections,
                      details: `慢查询: ${systemStatus?.performance.database.slowQueries}`,
                    },
                    {
                      label: "缓存命中率",
                      value: `${systemStatus?.performance.cache.hitRate}%`,
                      details: `未命中: ${systemStatus?.performance.cache.misses}`,
                    },
                    // 追加更丰富的 DB/Redis/Node 指标
                    {
                      label: "DB 平均RT",
                      value: `${(dashboardData?.services || []).find((s: any) => /mysql|database/i.test(s.name))?.performance?.avgResponseTime ?? 0} ms`,
                      details: `表: ${(dashboardData?.services || []).find((s: any) => /mysql|database/i.test(s.name))?.statistics?.totalTables ?? 0}`,
                    },
                    {
                      label: "Redis OPS",
                      value:
                        (dashboardData?.services || []).find((s: any) =>
                          /redis|cache/i.test(s.name)
                        )?.stats?.operationsPerSec ?? 0,
                      details: `Key: ${(dashboardData?.services || []).find((s: any) => /redis|cache/i.test(s.name))?.stats?.keyCount ?? 0}`,
                    },
                    {
                      label: "Node RSS",
                      value: `${(dashboardData?.services || []).find((s: any) => /node|application|api/i.test(s.name))?.memoryUsage?.rss ?? 0} MB`,
                      details: `EL 延迟: ${(dashboardData?.services || []).find((s: any) => /node|application|api/i.test(s.name))?.eventLoop?.delay ?? 0} ms`,
                    },
                  ]}
                  renderItem={item => (
                    <List.Item>
                      <div className="flex justify-between w-full">
                        <Text>{item.label}</Text>
                        <div>
                          <Text strong>{item.value}</Text>
                          <Text type="secondary" className="ml-2">
                            {item.details}
                          </Text>
                        </div>
                      </div>
                    </List.Item>
                  )}
                />
              </Card>
            </Col>
          </Row>
        </Col>
      </Row>

      {/* 系统事件（单独一行，铺满宽度） */}
      <Row gutter={[24, 24]}>
        <Col span={24}>
          <Card title="事件">
            <Tabs type="card">
              <TabPane tab="告警" itemKey="1">
                <div className="space-y-4 max-h-80 overflow-y-auto">
                  {systemStatus?.alerts?.map((alert: Alert) => (
                    <div
                      key={alert.id}
                      className={`ops-alert-card ${alert.type}`}
                      onClick={() => handleAlertClick(alert)}
                    >
                      <div className="ops-alert-header">
                        <div>
                          <div className="flex items-center space-x-2 mb-2">
                            <Tag
                              color={
                                alert.type === "error"
                                  ? "red"
                                  : alert.type === "warning"
                                    ? "orange"
                                    : "blue"
                              }
                              size="small"
                            >
                              {alert.component}
                            </Tag>
                            {alert.resolved && (
                              <div
                                className="ops-status-indicator success"
                                style={{ padding: "2px 8px", fontSize: "10px" }}
                              >
                                已解决
                              </div>
                            )}
                          </div>
                          <h4 className="ops-alert-title">{alert.title}</h4>
                          <p className="ops-alert-message">{alert.message}</p>
                        </div>
                        <span className="ops-alert-time">
                          {new Date(alert.timestamp).toLocaleTimeString()}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
                {(!systemStatus?.alerts || systemStatus.alerts.length === 0) && (
                  <Empty
                    image={<IllustrationConstruction style={{ width: 150, height: 150 }} />}
                    darkModeImage={
                      <IllustrationConstructionDark style={{ width: 150, height: 150 }} />
                    }
                    title={"暂无系统告警"}
                    description="当前暂无系统告警"
                  />
                )}
              </TabPane>
              <TabPane tab="日志" itemKey="2">
                <Spin spinning={isLogsLoading} style={{ width: "100%" }}>
                  <div className="flex items-center justify-between mb-4">
                    <Title heading={6} className="m-0">
                      系统日志
                    </Title>
                    <Select
                      placeholder="日志级别"
                      value={selectedLogLevel}
                      onChange={value => setSelectedLogLevel(value as string)}
                      style={{ width: 100 }}
                      size="small"
                    >
                      <Select.Option value="">全部</Select.Option>
                      <Select.Option value="error">ERROR</Select.Option>
                      <Select.Option value="warn">WARN</Select.Option>
                      <Select.Option value="info">INFO</Select.Option>
                      <Select.Option value="debug">DEBUG</Select.Option>
                    </Select>
                  </div>
                  <div className="space-y-3 max-h-80 overflow-y-auto">
                    {systemEvents?.events?.map((log: any) => (
                      <div
                        key={log.id}
                        className="p-4 border rounded-lg hover:shadow-sm transition-shadow bg-white"
                      >
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center space-x-3">
                            <Tag color={getLogLevelColor(log.level)} size="small">
                              {String(log.level || "").toUpperCase()}
                            </Tag>
                            <Text strong size="small">
                              {log.service || log.type || "SYSTEM"}
                            </Text>
                          </div>
                          <Text size="small" type="secondary">
                            {new Date(log.timestamp).toLocaleTimeString()}
                          </Text>
                        </div>
                        <Text size="small" className="text-gray-700 leading-relaxed">
                          {log.message}
                        </Text>
                      </div>
                    ))}
                  </div>
                  {(!systemEvents?.events || systemEvents.events.length === 0) && (
                    <Empty
                      image={<IllustrationConstruction style={{ width: 150, height: 150 }} />}
                      darkModeImage={
                        <IllustrationConstructionDark style={{ width: 150, height: 150 }} />
                      }
                      title={"暂无系统日志"}
                      description="当前暂无系统日志"
                    />
                  )}
                </Spin>
              </TabPane>
            </Tabs>
          </Card>
        </Col>
      </Row>

      <Modal
        title={
          <div className="flex items-center space-x-2">
            <IconBell />
            <span>告警详情</span>
          </div>
        }
        visible={isAlertModalVisible}
        onCancel={() => {
          setIsAlertModalVisible(false);
          setSelectedAlert(null);
        }}
        footer={<Button onClick={() => setIsAlertModalVisible(false)}>关闭</Button>}
        width={600}
      >
        {selectedAlert && (
          <div className="space-y-4">
            <div className="bg-gray-50 p-4 rounded">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Text strong>告警类型：</Text>
                  <Tag
                    color={
                      selectedAlert.type === "error"
                        ? "red"
                        : selectedAlert.type === "warning"
                          ? "orange"
                          : "blue"
                    }
                  >
                    {selectedAlert.type.toUpperCase()}
                  </Tag>
                </div>
                <div>
                  <Text strong>组件：</Text>
                  <Text>{selectedAlert.component}</Text>
                </div>
                <div>
                  <Text strong>状态：</Text>
                  {selectedAlert.resolved ? (
                    <Tag color="green">已解决</Tag>
                  ) : (
                    <Tag color="red">未解决</Tag>
                  )}
                </div>
                <div>
                  <Text strong>触发时间：</Text>
                  <Text>{new Date(selectedAlert.timestamp).toLocaleString()}</Text>
                </div>
              </div>
            </div>

            <div>
              <Text strong>告警标题：</Text>
              <div className="mt-1">
                <Text>{selectedAlert.title}</Text>
              </div>
            </div>

            <div>
              <Text strong>详细描述：</Text>
              <div className="mt-1">
                <Text>{selectedAlert.message}</Text>
              </div>
            </div>

            {!selectedAlert.resolved && (
              <div className="p-3 bg-yellow-50 border border-yellow-200 rounded">
                <Text type="warning">
                  <IconAlertTriangle /> 此告警尚未解决，建议及时处理
                </Text>
              </div>
            )}
          </div>
        )}
      </Modal>
    </div>
  );
}
