import React, { useEffect, useState, useMemo, useCallback } from "react";
import { useUIStore } from "@/stores";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { serviceService } from "@/services/service";
import { useRealTimeServices } from "@/hooks/useRealTimeServices";
import {
  Table,
  Button,
  Toast,
  Modal,
  Spin,
  Tag,
  Empty,
  Typography,
  Space,
  Input,
  Select,
  Card,
  Row,
  Col,
  Avatar,
  Tooltip,
  Dropdown,
} from "@douyinfe/semi-ui";
import {
  IconPlus,
  IconSearch,
  IconExport,
  IconMore,
  IconEdit,
  IconDelete,
  IconUser,
  IconClock,
  IconAlertTriangle,
  IconActivity,
  IconTickCircle,
  IconUserAdd,
  IconCalendar,
} from "@douyinfe/semi-icons";
import { Service, ServiceStatus, Priority, ServiceCategory, ServiceSource } from "shared/types";
import { isEmpty } from "lodash-es";
import ServiceFormModal from "@/components/services/ServiceFormModal";
import { ServiceDetailSideSheet } from "@/components/services/ServiceDetailSideSheet";
import { RealTimeIndicator } from "@/components/common/RealTimeIndicator";
import { NewTicketsBanner } from "@/components/common/NewTicketsBanner";
import { SLAStatusIndicator } from "@/components/common/SLAStatusIndicator";

const { Title, Text } = Typography;

export default function ServicesPage() {
  const { setBreadcrumbs } = useUIStore();
  const queryClient = useQueryClient();
  const [searchParams, setSearchParams] = useState({
    page: 1,
    limit: 20,
    search: "",
    status: undefined as ServiceStatus | undefined,
    priority: undefined as Priority | undefined,
    category: undefined as ServiceCategory | undefined,
    source: undefined as ServiceSource | undefined,
    assignedTo: "",
  });
  const [modalVisible, setModalVisible] = useState(false);
  const [currentService, setCurrentService] = useState<Service | null>(null);
  const [detailVisible, setDetailVisible] = useState(false);
  const [selectedServiceId, setSelectedServiceId] = useState<string | null>(null);
  const [quickStatsExpanded, setQuickStatsExpanded] = useState(true);

  useEffect(() => {
    setBreadcrumbs([
      { text: "首页", href: "/", icon: "🏠" },
      { text: "服务工单", icon: "🎫" },
    ]);
  }, [setBreadcrumbs]);

  // 使用实时数据Hook
  const {
    data,
    isLoading,
    isError,
    error,
    refetch,
    newTicketsCount,
    hasDataChanged,
    clearNewTickets,
    pauseRealTime,
    resumeRealTime,
    isRealTimePaused,
  } = useRealTimeServices({
    searchParams,
    enabled: true,
    refreshInterval: 15000, // 15秒检测新工单
    backgroundRefreshInterval: 60000, // 后台60秒刷新
  });

  // 服务工单统计数据查询
  const { data: apiStatsData } = useQuery({
    queryKey: ["services-stats"],
    queryFn: () => serviceService.getServiceStats(),
    select: response => response.data,
    refetchInterval: 60000, // 每分钟刷新统计数据
  });

  // 处理统计数据
  const statsData = useMemo(() => {
    if (!apiStatsData) return null;

    const statusMap = apiStatsData.statusDistribution.reduce(
      (acc, item) => {
        acc[item.status] = item.count;
        return acc;
      },
      {} as Record<string, number>
    );

    return {
      total: apiStatsData.totalServices,
      pending: statusMap["PENDING"] || 0,
      inProgress: statusMap["IN_PROGRESS"] || 0,
      resolved: statusMap["RESOLVED"] || 0,
      closed: statusMap["CLOSED"] || 0,
      recent: apiStatsData.recentServices,
      avgResolutionTime: apiStatsData.avgResolutionTime,
    };
  }, [apiStatsData]);

  const deleteMutation = useMutation({
    mutationFn: (id: string) => serviceService.deleteService(id),
    onSuccess: () => {
      Toast.success("工单删除成功");
      queryClient.invalidateQueries({ queryKey: ["services"] });
    },
    onError: (err: Error) => {
      Toast.error(`删除失败: ${err.message}`);
    },
  });

  const handleDelete = useCallback(
    (id: string) => {
      Modal.confirm({
        title: "确认删除",
        content: "确定要删除此服务工单吗？此操作不可撤销。",
        onOk: () => deleteMutation.mutate(id),
      });
    },
    [deleteMutation]
  );

  const openModal = useCallback((service: Service | null = null) => {
    setCurrentService(service);
    setModalVisible(true);
  }, []);

  const closeModal = useCallback(() => {
    setCurrentService(null);
    setModalVisible(false);
  }, []);

  const openDetail = useCallback((serviceId: string) => {
    setSelectedServiceId(serviceId);
    setDetailVisible(true);
  }, []);

  const closeDetail = useCallback(() => {
    setSelectedServiceId(null);
    setDetailVisible(false);
  }, []);

  const handleRefresh = useCallback(() => {
    refetch();
    clearNewTickets();
  }, [refetch, clearNewTickets]);

  const handleToggleRealTime = useCallback(() => {
    if (isRealTimePaused) {
      resumeRealTime();
    } else {
      pauseRealTime();
    }
  }, [isRealTimePaused, resumeRealTime, pauseRealTime]);

  const handleViewNewTickets = useCallback(() => {
    // 滚动到顶部并刷新数据
    window.scrollTo({ top: 0, behavior: "smooth" });
    refetch();
    clearNewTickets();
  }, [refetch, clearNewTickets]);

  // 状态配置
  const statusConfig = {
    PENDING: { text: "待处理", color: "orange", icon: IconClock },
    IN_PROGRESS: { text: "处理中", color: "blue", icon: IconActivity },
    WAITING_CUSTOMER: { text: "等待客户", color: "yellow", icon: IconUser },
    RESOLVED: { text: "已解决", color: "green", icon: IconTickCircle },
    CLOSED: { text: "已关闭", color: "grey", icon: IconTickCircle },
  } as const;

  const priorityConfig = {
    LOW: { text: "低", color: "grey" },
    MEDIUM: { text: "中", color: "blue" },
    HIGH: { text: "高", color: "orange" },
    URGENT: { text: "紧急", color: "red" },
  } as const;

  const categoryConfig = {
    MAINTENANCE: { text: "维护", color: "blue" },
    SUPPORT: { text: "支持", color: "green" },
    UPGRADE: { text: "升级", color: "purple" },
    BUGFIX: { text: "Bug修复", color: "red" },
    CONSULTING: { text: "咨询", color: "cyan" },
    MONITORING: { text: "监控", color: "orange" },
  } as const;

  const sourceConfig = {
    INTERNAL: { text: "内部", color: "blue", icon: "🏢" },
    EXTERNAL: { text: "外部", color: "orange", icon: "🌐" },
  } as const;

  const columns = useMemo(
    () => [
      {
        title: "工单号",
        dataIndex: "ticketNumber",
        width: 260,
        render: (ticketNumber: string, record: Service) => (
          <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
            <IconCalendar size="small" style={{ color: "var(--semi-color-primary)" }} />
            <Text
              strong
              style={{ fontFamily: "monospace", cursor: "pointer" }}
              onClick={() => openDetail(record.id)}
            >
              {ticketNumber}
            </Text>
          </div>
        ),
      },
      {
        title: "标题",
        dataIndex: "title",
        render: (text: string, record: Service) => (
          <div>
            <Text
              ellipsis={{ showTooltip: true }}
              style={{ cursor: "pointer", color: "var(--semi-color-text-0)" }}
              onClick={() => openDetail(record.id)}
            >
              {text}
            </Text>
            <div style={{ marginTop: "4px", display: "flex", gap: "4px", flexWrap: "wrap" }}>
              <Tag
                color={
                  categoryConfig[record.category as keyof typeof categoryConfig]?.color || "grey"
                }
                size="small"
              >
                {categoryConfig[record.category as keyof typeof categoryConfig]?.text ||
                  record.category}
              </Tag>
              <Tag color={sourceConfig[record.source]?.color || "grey"} size="small">
                <span style={{ marginRight: "2px" }}>{sourceConfig[record.source]?.icon}</span>
                {sourceConfig[record.source]?.text || record.source}
              </Tag>
            </div>
          </div>
        ),
      },
      {
        title: "状态",
        dataIndex: "status",
        width: 110,
        render: (status: ServiceStatus) => {
          const config = statusConfig[status];
          const Icon = config?.icon || IconClock;
          return (
            <Tag color={config?.color || "grey"} size="large">
              <Icon size="small" style={{ marginRight: "4px" }} />
              {config?.text || status}
            </Tag>
          );
        },
      },
      {
        title: "优先级",
        dataIndex: "priority",
        width: 90,
        render: (priority: Priority) => {
          const config = priorityConfig[priority];
          return (
            <Tag color={config?.color || "grey"}>
              {priority === "URGENT" && (
                <IconAlertTriangle size="small" style={{ marginRight: "4px" }} />
              )}
              {config?.text || priority}
            </Tag>
          );
        },
      },

      {
        title: "SLA状态",
        dataIndex: "slaStatus",
        width: 110,
        render: (_: unknown, record: Service) => (
          <SLAStatusIndicator
            slaTemplate={record?.slaTemplate}
            createdAt={record.createdAt}
            startTime={record.startTime}
            endTime={record.endTime}
            firstResponseAt={record?.firstResponseAt}
            status={record.status}
            size="small"
            showProgress={false}
          />
        ),
      },
      {
        title: "分配给",
        dataIndex: "assignedUser",
        width: 120,
        render: (assignedUser: any) =>
          assignedUser ? (
            <div style={{ display: "flex", alignItems: "center", gap: "6px" }}>
              <Avatar size="small" color="blue">
                {(assignedUser.fullName || assignedUser.username).charAt(0).toUpperCase()}
              </Avatar>
              <Text size="small">{assignedUser.fullName || assignedUser.username}</Text>
            </div>
          ) : (
            <Text type="tertiary" size="small">
              未分配
            </Text>
          ),
      },
      {
        title: "客户信息",
        dataIndex: "archive",
        width: 180,
        render: (archive: any) => (
          <div>
            <Text size="small" strong>
              {archive?.customer?.name || "-"}
            </Text>
            <div>
              <Text type="tertiary" size="small">
                {archive?.name || "-"}
              </Text>
            </div>
          </div>
        ),
      },
      {
        title: "创建时间",
        dataIndex: "createdAt",
        width: 140,
        render: (date: string) => (
          <div>
            <Text size="small">{new Date(date).toLocaleDateString("zh-CN")}</Text>
            <div>
              <Text type="tertiary" size="small">
                {new Date(date).toLocaleTimeString("zh-CN", { hour: "2-digit", minute: "2-digit" })}
              </Text>
            </div>
          </div>
        ),
      },
      {
        title: "操作",
        dataIndex: "operate",
        width: 140,
        fixed: "right" as const,
        render: (_: unknown, record: Service) => (
          <Space>
            <Tooltip content="查看详情">
              <Button
                type="primary"
                theme="borderless"
                size="small"
                icon={<IconUserAdd />}
                onClick={() => openDetail(record.id)}
              />
            </Tooltip>
            <Tooltip content="编辑工单">
              <Button
                type="secondary"
                theme="borderless"
                size="small"
                icon={<IconEdit />}
                onClick={() => openModal(record)}
              />
            </Tooltip>
            <Dropdown
              trigger="click"
              position="bottomRight"
              render={
                <Dropdown.Menu>
                  <Dropdown.Item onClick={() => handleDelete(record.id)}>
                    <IconDelete style={{ marginRight: "8px" }} />
                    删除工单
                  </Dropdown.Item>
                </Dropdown.Menu>
              }
            >
              <Button theme="borderless" size="small" icon={<IconMore />} />
            </Dropdown>
          </Space>
        ),
      },
    ],
    [
      deleteMutation.isPending,
      handleDelete,
      openModal,
      openDetail,
      statusConfig,
      priorityConfig,
      categoryConfig,
      sourceConfig,
    ]
  );

  const handlePageChange = (page: number, limit: number) => {
    setSearchParams(prev => ({ ...prev, page, limit }));
  };

  const renderTable = () => {
    if (isLoading) {
      return (
        <div
          style={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            height: "200px",
          }}
        >
          <Spin size="large" tip="正在加载工单数据..." />
        </div>
      );
    }

    if (isError) {
      return <Empty title="加载失败" description={error?.message} />;
    }

    if (!data || isEmpty(data.items)) {
      return <Empty title="暂无工单" description="没有找到符合条件的工单记录。" />;
    }

    return (
      <Table
        columns={columns}
        dataSource={data.items}
        pagination={{
          currentPage: data?.pagination?.page || 1,
          pageSize: data?.pagination?.limit || 10,
          total: data?.pagination?.total || 0,
          onPageChange: page => handlePageChange(page, searchParams.limit),
          onPageSizeChange: limit => handlePageChange(1, limit),
        }}
      />
    );
  };

  // 渲染统计卡片
  const renderStatsCards = () => {
    if (!statsData) return null;

    return (
      <Card style={{ marginBottom: "24px" }}>
        <div
          style={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            marginBottom: "16px",
          }}
        >
          <Title heading={4} style={{ margin: 0 }}>
            工单统计概览
          </Title>
          <Button
            theme="borderless"
            size="small"
            icon={<IconActivity />}
            onClick={() => setQuickStatsExpanded(!quickStatsExpanded)}
          >
            {quickStatsExpanded ? "收起" : "展开"}
          </Button>
        </div>

        {quickStatsExpanded && (
          <Row gutter={[16, 16]}>
            <Col span={6}>
              <Card
                style={{
                  textAlign: "center",
                  background: "var(--semi-color-primary-light-default)",
                }}
              >
                <div style={{ padding: "16px" }}>
                  <div
                    style={{
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                      marginBottom: "8px",
                    }}
                  >
                    <IconCalendar
                      style={{ color: "var(--semi-color-primary)", marginRight: "8px" }}
                    />
                    <Text strong style={{ fontSize: "20px" }}>
                      {statsData?.total || 0}
                    </Text>
                  </div>
                  <Text type="secondary" size="small">
                    总工单数
                  </Text>
                </div>
              </Card>
            </Col>
            <Col span={6}>
              <Card
                style={{
                  textAlign: "center",
                  background: "var(--semi-color-warning-light-default)",
                }}
              >
                <div style={{ padding: "16px" }}>
                  <div
                    style={{
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                      marginBottom: "8px",
                    }}
                  >
                    <IconClock style={{ color: "var(--semi-color-warning)", marginRight: "8px" }} />
                    <Text strong style={{ fontSize: "20px" }}>
                      {statsData?.pending || 0}
                    </Text>
                  </div>
                  <Text type="secondary" size="small">
                    待处理
                  </Text>
                </div>
              </Card>
            </Col>
            <Col span={6}>
              <Card
                style={{ textAlign: "center", background: "var(--semi-color-info-light-default)" }}
              >
                <div style={{ padding: "16px" }}>
                  <div
                    style={{
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                      marginBottom: "8px",
                    }}
                  >
                    <IconActivity style={{ color: "var(--semi-color-info)", marginRight: "8px" }} />
                    <Text strong style={{ fontSize: "20px" }}>
                      {statsData?.inProgress || 0}
                    </Text>
                  </div>
                  <Text type="secondary" size="small">
                    处理中
                  </Text>
                </div>
              </Card>
            </Col>
            <Col span={6}>
              <Card
                style={{
                  textAlign: "center",
                  background: "var(--semi-color-success-light-default)",
                }}
              >
                <div style={{ padding: "16px" }}>
                  <div
                    style={{
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                      marginBottom: "8px",
                    }}
                  >
                    <IconTickCircle
                      style={{ color: "var(--semi-color-success)", marginRight: "8px" }}
                    />
                    <Text strong style={{ fontSize: "20px" }}>
                      {(statsData?.resolved || 0) + (statsData?.closed || 0)}
                    </Text>
                  </div>
                  <Text type="secondary" size="small">
                    已完成
                  </Text>
                </div>
              </Card>
            </Col>
          </Row>
        )}
      </Card>
    );
  };

  return (
    <div className="space-y-6">
      <div className="page-header">
        <div style={{ display: "flex", justifyContent: "space-between", alignItems: "flex-start" }}>
          <div>
            <Title heading={2}>服务工单管理</Title>
            <Text type="tertiary">专业的IT运维服务工单跟踪和管理系统</Text>
          </div>
          <Space>
            <RealTimeIndicator
              isActive={!isRealTimePaused}
              isPaused={isRealTimePaused}
              hasUpdates={hasDataChanged}
              onTogglePause={handleToggleRealTime}
              onRefresh={handleRefresh}
              lastUpdateTime={new Date()}
            />
            <Button theme="light" icon={<IconExport />}>
              导出
            </Button>
            <Button type="primary" icon={<IconPlus />} onClick={() => openModal()}>
              创建工单
            </Button>
          </Space>
        </div>
      </div>

      {/* 新工单提醒横幅 */}
      <NewTicketsBanner
        newTicketsCount={newTicketsCount}
        onViewNew={handleViewNewTickets}
        onDismiss={clearNewTickets}
        autoHide={true}
        autoHideDelay={10000}
      />

      {/* 统计概览卡片 */}
      {renderStatsCards()}

      {/* 筛选和搜索 */}
      <Card>
        <div style={{ marginBottom: "16px" }}>
          <Title heading={5} style={{ margin: 0, marginBottom: "12px" }}>
            筛选条件
          </Title>
          <Row gutter={[12, 12]}>
            <Col span={6}>
              <Input
                prefix={<IconSearch />}
                placeholder="搜索工单号、标题或客户"
                value={searchParams.search}
                onChange={value => setSearchParams(prev => ({ ...prev, search: value, page: 1 }))}
                showClear
              />
            </Col>
            <Col span={4}>
              <Select
                placeholder="状态筛选"
                value={searchParams.status}
                onChange={value =>
                  setSearchParams(prev => ({
                    ...prev,
                    status: value as ServiceStatus | undefined,
                    page: 1,
                  }))
                }
                style={{ width: "100%" }}
                showClear
              >
                <Select.Option value="">全部状态</Select.Option>
                <Select.Option value="PENDING">待处理</Select.Option>
                <Select.Option value="IN_PROGRESS">处理中</Select.Option>
                <Select.Option value="WAITING_CUSTOMER">等待客户</Select.Option>
                <Select.Option value="RESOLVED">已解决</Select.Option>
                <Select.Option value="CLOSED">已关闭</Select.Option>
              </Select>
            </Col>
            <Col span={4}>
              <Select
                placeholder="优先级筛选"
                value={searchParams.priority}
                onChange={value =>
                  setSearchParams(prev => ({
                    ...prev,
                    priority: value as Priority | undefined,
                    page: 1,
                  }))
                }
                style={{ width: "100%" }}
                showClear
              >
                <Select.Option value="">全部优先级</Select.Option>
                <Select.Option value="LOW">低</Select.Option>
                <Select.Option value="MEDIUM">中</Select.Option>
                <Select.Option value="HIGH">高</Select.Option>
                <Select.Option value="URGENT">紧急</Select.Option>
              </Select>
            </Col>
            <Col span={4}>
              <Select
                placeholder="服务类别"
                value={searchParams.category}
                onChange={value =>
                  setSearchParams(prev => ({
                    ...prev,
                    category: value as ServiceCategory | undefined,
                    page: 1,
                  }))
                }
                style={{ width: "100%" }}
                showClear
              >
                <Select.Option value="">全部类别</Select.Option>
                <Select.Option value="MAINTENANCE">维护</Select.Option>
                <Select.Option value="SUPPORT">支持</Select.Option>
                <Select.Option value="UPGRADE">升级</Select.Option>
                <Select.Option value="BUGFIX">Bug修复</Select.Option>
                <Select.Option value="CONSULTING">咨询</Select.Option>
                <Select.Option value="MONITORING">监控</Select.Option>
              </Select>
            </Col>
            <Col span={4}>
              <Select
                placeholder="来源筛选"
                value={searchParams.source}
                onChange={value =>
                  setSearchParams(prev => ({
                    ...prev,
                    source: value as ServiceSource | undefined,
                    page: 1,
                  }))
                }
                style={{ width: "100%" }}
                showClear
              >
                <Select.Option value="">全部来源</Select.Option>
                <Select.Option value="INTERNAL">内部创建</Select.Option>
                <Select.Option value="EXTERNAL">外部API</Select.Option>
              </Select>
            </Col>
          </Row>
        </div>

        {/* 工单列表 */}
        {renderTable()}
      </Card>

      {/* 创建工单弹窗 */}
      {modalVisible && (
        <ServiceFormModal visible={modalVisible} onClose={closeModal} service={currentService} />
      )}

      {/* 工单详情侧边栏 */}
      {detailVisible && selectedServiceId && (
        <ServiceDetailSideSheet
          visible={detailVisible}
          serviceId={selectedServiceId}
          onClose={closeDetail}
          onDataChanged={() => {
            // 刷新服务列表数据
            queryClient.invalidateQueries({ queryKey: ["services"] });
          }}
        />
      )}
    </div>
  );
}
