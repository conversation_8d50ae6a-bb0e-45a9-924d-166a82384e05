import React, { useEffect, useMemo } from "react";
import { Typography, Row, Col, Button, Spin, Toast } from "@douyinfe/semi-ui";
import {
  IconUserGroup,
  IconFolder,
  IconTicketCodeStroked,
  IconSetting,
  IconRefresh,
} from "@douyinfe/semi-icons";
import { useAuthStore, useUIStore } from "@/stores";
import { StatCard } from "@/components/dashboard/StatCard";
import { ServiceStatusChart } from "@/components/dashboard/ServiceStatusChart";
import { MonthlyTrendChart } from "@/components/dashboard/MonthlyTrendChart";
import { RecentServices } from "@/components/dashboard/RecentServices";
import { SystemNotifications } from "@/components/dashboard/SystemNotifications";
import { SystemHealthCard } from "@/components/dashboard/SystemHealthCard";
import { TrendChart } from "@/components/dashboard/TrendChart";
import { useDashboardData, useInvalidateDashboard } from "@/hooks/useDashboard";

const { Title, Text } = Typography;

export default function DashboardPage() {
  const { user } = useAuthStore();
  const { setBreadcrumbs } = useUIStore();

  // 使用优化的Dashboard数据Hook
  const { stats, trends, isLoading, isError, errors, statsLoading, refetchAll } =
    useDashboardData();

  // 数据刷新工具
  const { forceRefresh } = useInvalidateDashboard();

  useEffect(() => {
    setBreadcrumbs([{ text: "仪表盘", icon: "🏠" }]);
  }, [setBreadcrumbs]);

  // 手动刷新数据
  const handleRefresh = async () => {
    try {
      await refetchAll();
      Toast.success("数据刷新成功");
    } catch (error) {
      console.error("数据刷新失败:", error);
      Toast.error("数据刷新失败，请稍后重试");
    }
  };

  // 处理错误状态
  const handleRetry = () => {
    forceRefresh();
  };

  // 工单状态映射函数 - 必须在useMemo之前声明
  const getStatusName = (status: string) => {
    const statusMap: Record<string, string> = {
      PENDING: "待处理",
      IN_PROGRESS: "处理中",
      WAITING_CUSTOMER: "等待客户",
      RESOLVED: "已解决",
      CLOSED: "已关闭",
    };
    return statusMap[status] || status;
  };

  const getStatusColor = (status: string) => {
    const colorMap: Record<string, string> = {
      PENDING: "#f59e0b",
      IN_PROGRESS: "#3b82f6",
      WAITING_CUSTOMER: "#8b5cf6",
      RESOLVED: "#10b981",
      CLOSED: "#6b7280",
    };
    return colorMap[status] || "#6b7280";
  };

  // 图表数据处理 - 基于真实API数据
  const statusChartData = useMemo(() => {
    if (!stats?.statusDistribution) return [];

    return stats.statusDistribution.map(item => ({
      name: getStatusName(item.status),
      value: item.count,
      color: getStatusColor(item.status),
    }));
  }, [stats]);

  const trendChartData = useMemo(() => {
    if (!trends || trends.length === 0) return null;

    return {
      months: trends.map(t => new Date(t.date).toLocaleDateString("zh-CN", { month: "short" })),
      newServices: trends.map(t => t.services),
      completedServices: trends.map(t => Math.floor(t.services * 0.8)), // 模拟已完成数据
    };
  }, [trends]);

  // 趋势计算
  const trendCalculations = useMemo(() => {
    if (!stats) return { customers: 0, projects: 0, services: 0, configurations: 0 };

    // 基于历史数据计算趋势（这里使用模拟计算，实际应该从API获取）
    return {
      customers: Math.floor(Math.random() * 20) - 10, // -10 到 +10
      projects: Math.floor(Math.random() * 15) - 5, // -5 到 +10
      services: Math.floor(Math.random() * 25) - 12, // -12 到 +13
      configurations: Math.floor(Math.random() * 30) - 15, // -15 到 +15
    };
  }, [stats]);

  // 错误处理界面
  if (isError && !isLoading) {
    return (
      <div className="space-y-6">
        <div className="text-center py-12">
          <Text type="danger">加载仪表盘数据失败</Text>
          <div className="mt-4">
            <Button type="primary" onClick={handleRetry}>
              重试
            </Button>
          </div>
          {errors && errors.length > 0 && (
            <div className="mt-4 text-left bg-red-50 p-4 rounded">
              <Text type="danger" size="small">
                错误详情: {errors[0]?.message || "未知错误"}
              </Text>
            </div>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* 页面标题和刷新按钮 */}
      <div className="flex justify-between items-start">
        <div>
          <Title heading={2} className="!mb-2" style={{ color: "var(--semi-color-text-0)" }}>
            仪表盘
          </Title>
          <Text type="secondary">
            欢迎回来，{user?.fullName || user?.username || "用户"}！这里是系统实时概览。
          </Text>
        </div>
        <Button
          icon={<IconRefresh />}
          onClick={handleRefresh}
          loading={isLoading}
          theme="borderless"
        >
          刷新数据
        </Button>
      </div>

      {/* 统计卡片 - 使用真实数据 */}
      <Row gutter={[16, 16]}>
        <Col span={24} sm={12} lg={6}>
          <StatCard
            title="客户总数"
            value={stats?.customers || 0}
            icon={<IconUserGroup />}
            color="blue"
            trend={{
              value: Math.abs(trendCalculations.customers),
              isPositive: trendCalculations.customers >= 0,
            }}
          />
        </Col>
        <Col span={24} sm={12} lg={6}>
          <StatCard
            title="项目总数"
            value={stats?.projects || 0}
            icon={<IconFolder />}
            color="green"
            trend={{
              value: Math.abs(trendCalculations.projects),
              isPositive: trendCalculations.projects >= 0,
            }}
          />
        </Col>
        <Col span={24} sm={12} lg={6}>
          <StatCard
            title="待处理工单"
            value={stats?.pendingServices || 0}
            icon={<IconTicketCodeStroked />}
            color="yellow"
            trend={{
              value: Math.abs(trendCalculations.services),
              isPositive: trendCalculations.services <= 0, // 待处理减少是好事
            }}
          />
        </Col>
        <Col span={24} sm={12} lg={6}>
          <StatCard
            title="总工单数量"
            value={stats?.totalServices || 0}
            icon={<IconSetting />}
            color="purple"
            trend={{
              value: Math.abs(trendCalculations.configurations),
              isPositive: trendCalculations.configurations >= 0,
            }}
          />
        </Col>
      </Row>

      {/* 新增系统监控区域 */}
      <Row gutter={[16, 16]}>
        <Col span={24} lg={8}>
          <SystemHealthCard />
        </Col>
        <Col span={24} lg={16}>
          <TrendChart days={7} />
        </Col>
      </Row>

      {/* 图表区域 - 基于真实数据 */}
      <Row gutter={[16, 16]}>
        <Col span={24} lg={12}>
          <ServiceStatusChart data={statusChartData} loading={statsLoading} />
        </Col>
        <Col span={24} lg={12}>
          {trendChartData ? (
            <MonthlyTrendChart data={trendChartData} loading={statsLoading} />
          ) : (
            <div className="h-64 flex items-center justify-center bg-gray-50 rounded">
              <Spin size="large" />
            </div>
          )}
        </Col>
      </Row>

      {/* 最近活动 - 保持现有组件但添加骨架屏 */}
      <Row gutter={[16, 16]}>
        <Col span={24} lg={12}>
          <RecentServices
            services={[]} // 需要实现最近服务API
            loading={statsLoading}
          />
        </Col>
        <Col span={24} lg={12}>
          <SystemNotifications
            notifications={[]} // 需要实现通知API
            loading={statsLoading}
          />
        </Col>
      </Row>
    </div>
  );
}
