import React, { useState, useEffect, useRef } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { useAuthStore } from "@/stores";
import { Input, Button, Checkbox, Typography, Card, Spin } from "@douyinfe/semi-ui";
import { showError, showSuccess } from "@/utils/notification";
import {
  IconUser,
  IconLock,
  IconServer,
  IconKey,
  IconShield,
  IconChevronRight,
} from "@douyinfe/semi-icons";

const { Title, Text } = Typography;

interface FormData {
  username: string;
  password: string;
  remember: boolean;
}

interface ValidationErrors {
  username?: string;
  password?: string;
}

export default function LoginPage() {
  const [formData, setFormData] = useState<FormData>({
    username: "",
    password: "",
    remember: false,
  });
  const [errors, setErrors] = useState<ValidationErrors>({});
  const [loginAttempts, setLoginAttempts] = useState(0);

  const usernameInputRef = useRef<HTMLInputElement>(null);
  const passwordInputRef = useRef<HTMLInputElement>(null);

  const navigate = useNavigate();
  const location = useLocation();
  const { login, loading, isAuthenticated } = useAuthStore();

  const from = (location.state as { from?: { pathname: string } })?.from?.pathname || "/";

  // 表单验证
  const validateForm = (): boolean => {
    const newErrors: ValidationErrors = {};

    if (!formData.username.trim()) {
      newErrors.username = "请输入用户名";
    } else if (formData.username.length < 3) {
      newErrors.username = "用户名至少需要3个字符";
    }

    if (!formData.password.trim()) {
      newErrors.password = "请输入密码";
    } else if (formData.password.length < 6) {
      newErrors.password = "密码至少需要6个字符";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // 提交处理
  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }

    const result = await login({
      username: formData.username,
      password: formData.password,
      remember: formData.remember,
    });

    if (result.success) {
      showSuccess("登录成功，正在跳转...");
      navigate(from, { replace: true });
    } else {
      setLoginAttempts(prev => prev + 1);
      showError("登录失败", result.message || "用户名或密码错误");

      // 登录失败后聚焦到密码输入框
      setTimeout(() => {
        passwordInputRef.current?.focus();
        passwordInputRef.current?.select();
      }, 100);
    }
  };

  // 自动聚焦到用户名输入框
  useEffect(() => {
    const timer = setTimeout(() => {
      usernameInputRef.current?.focus();
    }, 100);
    return () => clearTimeout(timer);
  }, []);

  useEffect(() => {
    if (isAuthenticated) {
      navigate(from, { replace: true });
    }
  }, [isAuthenticated, navigate, from]);

  // 键盘事件处理
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === "Enter" && !loading) {
        handleSubmit();
      }
    };

    document.addEventListener("keydown", handleKeyDown);
    return () => document.removeEventListener("keydown", handleKeyDown);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [loading]);

  // 输入处理
  const handleInputChange = (field: keyof FormData, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }));

    // 清除相关错误
    if (errors[field as keyof ValidationErrors]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const handleFormSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    handleSubmit();
  };

  // 演示账号快速填充
  const fillDemoAccount = () => {
    setFormData({
      username: "admin",
      password: "admin123",
      remember: false,
    });
  };

  const isFormValid = formData.username && formData.password;
  const shouldShowAttemptsBanner = loginAttempts >= 3;

  return (
    <div className="min-h-screen relative overflow-hidden">
      {/* 动态背景 */}
      <div className="absolute inset-0 bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 dark:from-gray-900 dark:via-slate-800 dark:to-blue-900">
        <div className='absolute inset-0 bg-[url("data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%239C92AC" fill-opacity="0.05"%3E%3Ccircle cx="30" cy="30" r="1.5"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")] opacity-40'></div>
      </div>

      {/* 装饰性元素 */}
      <div className="absolute top-0 left-0 w-full h-full overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -left-40 w-80 h-80 bg-blue-500/5 rounded-full blur-3xl animate-pulse"></div>
        <div
          className="absolute -bottom-40 -right-40 w-96 h-96 bg-indigo-500/5 rounded-full blur-3xl animate-pulse"
          style={{ animationDelay: "2s" }}
        ></div>
        <div
          className="absolute top-1/4 right-1/3 w-72 h-72 bg-purple-500/3 rounded-full blur-2xl animate-pulse"
          style={{ animationDelay: "4s" }}
        ></div>
      </div>

      {/* 主容器 */}
      <div className="relative min-h-screen flex items-center justify-center p-4">
        <div className="w-full max-w-md">
          {/* 顶部品牌区域 */}
          <div className="text-center mb-8">
            {/* Logo */}
            <div className="inline-flex items-center justify-center w-20 h-20 mb-6 rounded-2xl bg-gradient-to-br from-blue-600 via-blue-500 to-indigo-600 shadow-xl transform hover:scale-105 transition-all duration-300 hover:shadow-2xl">
              <IconServer size="extra-large" className="text-white" />
            </div>

            {/* 标题 */}
            <div className="space-y-2">
              <Title
                heading={1}
                className="text-3xl font-bold bg-gradient-to-r from-gray-900 via-blue-800 to-indigo-800 dark:from-white dark:via-blue-200 dark:to-indigo-200 bg-clip-text text-transparent"
              >
                运维服务管理系统
              </Title>
              <Text className="text-gray-600 dark:text-gray-400 text-base">
                专业、安全、高效的运维管理平台
              </Text>
            </div>
          </div>

          {/* 登录失败次数提示 */}
          {shouldShowAttemptsBanner && (
            <div className="mb-6 p-4 bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-lg">
              <Text className="text-amber-800 dark:text-amber-200 text-sm">
                登录失败 {loginAttempts} 次，请确认账号密码正确
              </Text>
            </div>
          )}

          {/* 登录卡片 */}
          <Card
            className="backdrop-blur-xl bg-white/90 dark:bg-gray-800/90 border border-white/20 dark:border-gray-700/30 shadow-2xl hover:shadow-3xl transition-all duration-300"
            bodyStyle={{ padding: "2rem" }}
            style={{
              borderRadius: "1.5rem",
              backdropFilter: "blur(20px)",
            }}
          >
            {/* 登录表单标题 */}
            <div className="text-center mb-8">
              <div className="inline-flex items-center justify-center w-12 h-12 mb-4 rounded-xl bg-blue-100 dark:bg-blue-900/30">
                <IconShield className="text-blue-600 dark:text-blue-400" size="large" />
              </div>
              <Text strong className="text-lg text-gray-800 dark:text-gray-200 block">
                安全登录
              </Text>
              <Text type="tertiary" className="text-sm">
                请使用您的账号登录系统
              </Text>
            </div>

            <form onSubmit={handleFormSubmit} className="space-y-6">
              {/* 用户名输入 */}
              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  用户名
                </label>
                <Input
                  ref={usernameInputRef}
                  size="large"
                  prefix={<IconUser className="text-gray-400" />}
                  placeholder="请输入用户名"
                  value={formData.username}
                  onChange={value => handleInputChange("username", value)}
                  validateStatus={errors.username ? "error" : undefined}
                  className="transition-all duration-200"
                  style={{
                    borderRadius: "0.75rem",
                  }}
                />
                {errors.username && (
                  <Text type="danger" className="text-sm mt-1">
                    {errors.username}
                  </Text>
                )}
              </div>

              {/* 密码输入 */}
              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  密码
                </label>
                <Input
                  ref={passwordInputRef}
                  size="large"
                  mode="password"
                  prefix={<IconLock className="text-gray-400" />}
                  placeholder="请输入密码"
                  value={formData.password}
                  onChange={value => handleInputChange("password", value)}
                  validateStatus={errors.password ? "error" : undefined}
                  className="transition-all duration-200"
                  style={{
                    borderRadius: "0.75rem",
                  }}
                />
                {errors.password && (
                  <Text type="danger" className="text-sm mt-1">
                    {errors.password}
                  </Text>
                )}
              </div>

              {/* 选项行 */}
              <div className="flex items-center justify-between">
                <Checkbox
                  checked={formData.remember}
                  onChange={e => handleInputChange("remember", e.target.checked || false)}
                  className="text-gray-600 dark:text-gray-400 hover:text-blue-600 transition-colors"
                >
                  记住登录状态
                </Checkbox>
                <Text
                  link
                  className="text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 font-medium transition-colors cursor-pointer"
                >
                  忘记密码？
                </Text>
              </div>

              {/* 登录按钮 */}
              <Button
                htmlType="submit"
                type="primary"
                size="large"
                disabled={!isFormValid}
                theme="solid"
                className="w-full semi-button-size-large"
                style={{
                  borderRadius: "0.75rem",
                  height: "48px",
                  border: "none",
                }}
              >
                {loading ? (
                  <span className="flex items-center justify-center gap-2">
                    <Spin size="small" />
                    正在登录...
                  </span>
                ) : (
                  <span className="flex items-center justify-center gap-2">
                    <IconKey size="small" />
                    立即登录
                    <IconChevronRight size="small" />
                  </span>
                )}
              </Button>

              {/* 演示账号 */}
              <div className="text-center">
                <Text type="tertiary" className="text-xs mb-2 block">
                  演示账号
                </Text>
                <Button
                  type="tertiary"
                  size="small"
                  onClick={fillDemoAccount}
                  className="text-xs px-4 py-1 rounded-full hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-colors"
                >
                  使用演示账号 (admin / admin123)
                </Button>
              </div>
            </form>
          </Card>

          {/* 底部信息 */}
          <div className="text-center mt-8 space-y-2">
            <Text type="tertiary" className="text-xs">
              © 2024 运维服务管理系统 · 版权所有
            </Text>
            <Text type="tertiary" className="text-xs flex items-center justify-center gap-4">
              <span className="hover:text-blue-600 cursor-pointer transition-colors">服务条款</span>
              <span>·</span>
              <span className="hover:text-blue-600 cursor-pointer transition-colors">隐私政策</span>
              <span>·</span>
              <span className="hover:text-blue-600 cursor-pointer transition-colors">技术支持</span>
            </Text>
          </div>
        </div>
      </div>
    </div>
  );
}
