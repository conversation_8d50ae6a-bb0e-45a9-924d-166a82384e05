import React, { useEffect } from "react";
import { useUIStore } from "@/stores";

export default function ProjectsPage() {
  const { setBreadcrumbs } = useUIStore();

  useEffect(() => {
    setBreadcrumbs([
      { text: "首页", href: "/", icon: "🏠" },
      { text: "项目档案", icon: "📁" },
    ]);
  }, [setBreadcrumbs]);

  return (
    <div className="space-y-6">
      <div className="page-header">
        <h1 className="page-title">项目档案</h1>
        <p className="page-subtitle">管理所有项目信息和文档</p>
      </div>

      <div className="card">
        <p className="text-gray-600 dark:text-gray-400">项目档案页面正在开发中...</p>
      </div>
    </div>
  );
}
