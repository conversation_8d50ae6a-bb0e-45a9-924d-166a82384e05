import React, { useEffect } from "react";
import { useParams } from "react-router-dom";
import { useUIStore } from "@/stores";

export default function ProjectDetailPage() {
  const { id } = useParams<{ id: string }>();
  const { setBreadcrumbs } = useUIStore();

  useEffect(() => {
    setBreadcrumbs([
      { text: "首页", href: "/", icon: "🏠" },
      { text: "项目档案", href: "/projects", icon: "📁" },
      { text: "项目详情", icon: "📄" },
    ]);
  }, [setBreadcrumbs]);

  return (
    <div className="space-y-6">
      <div className="page-header">
        <h1 className="page-title">项目详情</h1>
        <p className="page-subtitle">项目ID: {id}</p>
      </div>

      <div className="card">
        <p className="text-gray-600 dark:text-gray-400">项目详情页面正在开发中...</p>
      </div>
    </div>
  );
}
