import React from "react";
import { useLocation, useNavigate } from "react-router-dom";
import {
  Typo<PERSON>,
  Button,
  Card,
  Space,
  Empty,
  Tag,
  Descriptions,
  Row,
  Col,
} from "@douyinfe/semi-ui";
import { IconLock, IconArrowLeft, IconHome, IconMail } from "@douyinfe/semi-icons";
import { useAuthStore } from "@/stores";

const { Title, Text, Paragraph } = Typography;

export default function ForbiddenPage() {
  const location = useLocation();
  const navigate = useNavigate();
  const { user } = useAuthStore();

  // 从路由状态中获取权限信息
  const locationState = location.state as {
    from?: Location;
    requiredPermissions?: string[];
    requiredRoles?: string[];
    currentUser?: {
      role: string;
      permissions: string[];
    };
  } | null;

  const requiredPermissions = locationState?.requiredPermissions || [];
  const requiredRoles = locationState?.requiredRoles || [];
  const fromPath = locationState?.from?.pathname || "";
  const currentUserRole = user?.role.name || "";
  const currentUserPermissions = user?.role.permissions || [];

  const handleGoBack = () => {
    if (window.history.length > 1) {
      navigate(-1);
    } else {
      navigate("/");
    }
  };

  const handleGoHome = () => {
    navigate("/");
  };

  const handleContactAdmin = () => {
    // 这里可以集成邮件系统或工单系统
    window.location.href = `mailto:<EMAIL>?subject=权限申请&body=用户：${user?.fullName || user?.username}%0A当前角色：${currentUserRole}%0A申请访问：${fromPath}%0A所需权限：${requiredPermissions.join(", ")}%0A所需角色：${requiredRoles.join(", ")}`;
  };

  return (
    <div
      className="min-h-screen flex items-center justify-center bg-gray-50"
      style={{ background: "var(--semi-color-bg-0)" }}
    >
      <div className="max-w-2xl w-full mx-4">
        <Card
          style={{
            background: "var(--semi-color-bg-2)",
            borderColor: "var(--semi-color-border)",
          }}
          bodyStyle={{ padding: "40px" }}
        >
          {/* 图标和标题 */}
          <div className="text-center mb-8">
            <Empty
              image={<IconLock size="extra-large" style={{ color: "var(--semi-color-danger)" }} />}
              title={
                <Title heading={2} style={{ color: "var(--semi-color-text-0)", marginTop: "16px" }}>
                  访问被拒绝
                </Title>
              }
              description={
                <Paragraph
                  style={{ color: "var(--semi-color-text-1)", fontSize: "16px", marginTop: "8px" }}
                >
                  抱歉，您没有足够的权限访问此页面
                </Paragraph>
              }
            />
          </div>

          {/* 详细信息 */}
          <div className="space-y-6">
            {/* 当前用户信息 */}
            <Card
              title="当前用户信息"
              style={{
                background: "var(--semi-color-bg-1)",
                borderColor: "var(--semi-color-border)",
              }}
              bodyStyle={{ padding: "16px" }}
            >
              <Descriptions
                data={[
                  { key: "用户名", value: user?.username || "-" },
                  { key: "姓名", value: user?.fullName || "-" },
                  { key: "当前角色", value: currentUserRole || "-" },
                  { key: "部门", value: user?.department || "-" },
                ]}
                row
                size="small"
              />
            </Card>

            {/* 权限要求信息 */}
            {(requiredPermissions.length > 0 || requiredRoles.length > 0) && (
              <Card
                title="访问要求"
                style={{
                  background: "var(--semi-color-bg-1)",
                  borderColor: "var(--semi-color-border)",
                }}
                bodyStyle={{ padding: "16px" }}
              >
                <Row gutter={[24, 16]}>
                  {requiredRoles.length > 0 && (
                    <Col span={24}>
                      <div className="space-y-2">
                        <Text strong style={{ color: "var(--semi-color-text-0)" }}>
                          所需角色：
                        </Text>
                        <div className="flex flex-wrap gap-2">
                          {requiredRoles.map(role => (
                            <Tag
                              key={role}
                              color={currentUserRole === role ? "green" : "orange"}
                              size="large"
                            >
                              {role}
                              {currentUserRole === role && " ✓"}
                            </Tag>
                          ))}
                        </div>
                      </div>
                    </Col>
                  )}

                  {requiredPermissions.length > 0 && (
                    <Col span={24}>
                      <div className="space-y-2">
                        <Text strong style={{ color: "var(--semi-color-text-0)" }}>
                          所需权限：
                        </Text>
                        <div className="flex flex-wrap gap-2">
                          {requiredPermissions.map(permission => (
                            <Tag
                              key={permission}
                              color={currentUserPermissions.includes(permission) ? "green" : "red"}
                              size="large"
                            >
                              {permission}
                              {currentUserPermissions.includes(permission) && " ✓"}
                            </Tag>
                          ))}
                        </div>
                      </div>
                    </Col>
                  )}
                </Row>
              </Card>
            )}

            {/* 当前权限信息 */}
            {currentUserPermissions.length > 0 && (
              <Card
                title="您的当前权限"
                style={{
                  background: "var(--semi-color-bg-1)",
                  borderColor: "var(--semi-color-border)",
                }}
                bodyStyle={{ padding: "16px" }}
              >
                <div className="flex flex-wrap gap-2">
                  {currentUserPermissions.slice(0, 10).map(permission => (
                    <Tag key={permission} color="blue" size="small">
                      {permission}
                    </Tag>
                  ))}
                  {currentUserPermissions.length > 10 && (
                    <Tag color="grey" size="small">
                      +{currentUserPermissions.length - 10} 更多...
                    </Tag>
                  )}
                </div>
              </Card>
            )}

            {/* 解决方案建议 */}
            <Card
              title="解决方案"
              style={{
                background: "var(--semi-color-bg-1)",
                borderColor: "var(--semi-color-border)",
              }}
              bodyStyle={{ padding: "16px" }}
            >
              <div className="space-y-3">
                <div className="flex items-start space-x-3">
                  <div className="w-6 h-6 rounded-full bg-blue-100 flex items-center justify-center mt-0.5">
                    <span className="text-blue-600 text-sm font-semibold">1</span>
                  </div>
                  <div>
                    <Text strong style={{ color: "var(--semi-color-text-0)" }}>
                      联系系统管理员
                    </Text>
                    <Paragraph type="secondary" style={{ margin: "4px 0 0 0" }}>
                      请联系您的系统管理员申请相应的权限或角色
                    </Paragraph>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <div className="w-6 h-6 rounded-full bg-blue-100 flex items-center justify-center mt-0.5">
                    <span className="text-blue-600 text-sm font-semibold">2</span>
                  </div>
                  <div>
                    <Text strong style={{ color: "var(--semi-color-text-0)" }}>
                      确认访问目标
                    </Text>
                    <Paragraph type="secondary" style={{ margin: "4px 0 0 0" }}>
                      确认您确实需要访问此功能，避免误操作
                    </Paragraph>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <div className="w-6 h-6 rounded-full bg-blue-100 flex items-center justify-center mt-0.5">
                    <span className="text-blue-600 text-sm font-semibold">3</span>
                  </div>
                  <div>
                    <Text strong style={{ color: "var(--semi-color-text-0)" }}>
                      返回安全区域
                    </Text>
                    <Paragraph type="secondary" style={{ margin: "4px 0 0 0" }}>
                      返回首页或上一页面，继续使用您有权访问的功能
                    </Paragraph>
                  </div>
                </div>
              </div>
            </Card>
          </div>

          {/* 操作按钮 */}
          <div className="flex justify-center space-x-4 mt-8">
            <Button theme="borderless" size="large" icon={<IconArrowLeft />} onClick={handleGoBack}>
              返回上页
            </Button>

            <Button
              theme="light"
              type="primary"
              size="large"
              icon={<IconHome />}
              onClick={handleGoHome}
            >
              返回首页
            </Button>

            <Button
              theme="solid"
              type="secondary"
              size="large"
              icon={<IconMail />}
              onClick={handleContactAdmin}
            >
              申请权限
            </Button>
          </div>

          {/* 提示信息 */}
          {fromPath && (
            <div
              className="mt-6 p-4 rounded-lg"
              style={{ background: "var(--semi-color-info-light-default)" }}
            >
              <Text type="secondary" size="small">
                您尝试访问的页面：<Text code>{fromPath}</Text>
              </Text>
            </div>
          )}
        </Card>
      </div>
    </div>
  );
}
