import React, { useEffect, useState } from "react";
import { use<PERSON><PERSON><PERSON>, <PERSON> } from "react-router-dom";
import {
  Typo<PERSON>,
  Card,
  Button,
  Tag,
  Tabs,
  Table,
  Empty,
  Modal,
  Toast,
  Space,
  Row,
  Col,
  Spin,
} from "@douyinfe/semi-ui";
import {
  IconArrowLeft,
  IconEdit,
  IconDelete,
  IconFolderOpen,
  IconTicketCodeStroked,
  IconSetting,
  IconFolder,
  IconEyeOpened,
} from "@douyinfe/semi-icons";
import { useAuthStore, useUIStore } from "@/stores";
import { CustomerEditModal } from "@/components/customers/CustomerEditModal";
import { ArchiveDetailSideSheet, ArchiveCreateModal } from "@/components/archives";
import { ServiceDetailSideSheet, ServiceCreateModal } from "@/components/services";
import { ConfigurationCreateModal } from "@/components/admin/ConfigurationCreateModal";
import CustomerService from "@/services/customer";
import type { Customer as CustomerType } from "@/types";

const { Title, Text } = Typography;
const { TabPane } = Tabs;

// 数据类型定义
type Customer = CustomerType;

interface CustomerStats {
  projects: number;
  services: number;
  pendingServices: number;
  configurations: number;
}

interface Project {
  id: string;
  name: string;
  description: string;
  status: "ACTIVE" | "MAINTENANCE" | "DEPRECATED" | "ARCHIVED";
  createdAt: string;
}

// 项目档案数据结构
interface Archive {
  id: string;
  name: string;
  description: string;
  status: "ACTIVE" | "MAINTENANCE" | "DEPRECATED" | "ARCHIVED";
  createdAt: string;
}

interface Service {
  id: string;
  ticketNumber?: string;
  title: string;
  description: string;
  status: "PENDING" | "IN_PROGRESS" | "WAITING_CUSTOMER" | "RESOLVED" | "CLOSED";
  priority: "LOW" | "MEDIUM" | "HIGH" | "URGENT";
  category?: string;
  createdAt: string;
}

interface Configuration {
  id: string;
  title: string;
  description: string;
  configType: "SERVER" | "DATABASE" | "VPN" | "ACCOUNT" | "ENVIRONMENT" | "OTHER";
  isActive: boolean;
  configData: Record<string, any>;
  createdAt: string;
  lastUpdated?: string;
}

// 配置对象
const customerTypeConfig = {
  ENTERPRISE: { text: "企业客户", color: "blue" },
  INDIVIDUAL: { text: "个人客户", color: "green" },
  GOVERNMENT: { text: "政府机构", color: "purple" },
  NONPROFIT: { text: "非营利组织", color: "orange" },
} as const;

const serviceStatusConfig = {
  PENDING: { text: "待处理", color: "orange" },
  IN_PROGRESS: { text: "处理中", color: "blue" },
  WAITING_CUSTOMER: { text: "等待客户", color: "yellow" },
  RESOLVED: { text: "已解决", color: "green" },
  CLOSED: { text: "已关闭", color: "grey" },
} as const;

const projectStatusConfig = {
  ACTIVE: { text: "进行中", color: "green" },
  MAINTENANCE: { text: "维护中", color: "blue" },
  DEPRECATED: { text: "已废弃", color: "red" },
  ARCHIVED: { text: "已归档", color: "grey" },
} as const;

const configTypeConfig = {
  SERVER: { text: "服务器", color: "blue" },
  DATABASE: { text: "数据库", color: "green" },
  VPN: { text: "VPN", color: "purple" },
  ACCOUNT: { text: "账户", color: "orange" },
  ENVIRONMENT: { text: "环境", color: "cyan" },
  OTHER: { text: "其他", color: "grey" },
} as const;

export default function CustomerDetailPage() {
  const { id } = useParams<{ id: string }>();
  const { hasPermission } = useAuthStore();
  const { setBreadcrumbs } = useUIStore();

  // 状态管理
  const [loading, setLoading] = useState(false);
  const [customer, setCustomer] = useState<Customer | null>(null);
  const [stats, setStats] = useState<CustomerStats>({
    projects: 0,
    services: 0,
    pendingServices: 0,
    configurations: 0,
  });
  const [projects, setProjects] = useState<Project[]>([]);
  const [services, setServices] = useState<Service[]>([]);
  const [configurations, setConfigurations] = useState<Configuration[]>([]);

  // 模态框状态
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [deleting, setDeleting] = useState(false);

  // SideSheet状态
  const [showArchiveSideSheet, setShowArchiveSideSheet] = useState(false);
  const [selectedArchiveId, setSelectedArchiveId] = useState<string | null>(null);
  const [showServiceSideSheet, setShowServiceSideSheet] = useState(false);
  const [selectedServiceId, setSelectedServiceId] = useState<string | null>(null);

  // 服务工单创建模态框状态
  const [showServiceCreateModal, setShowServiceCreateModal] = useState(false);

  // 项目档案创建模态框状态
  const [showArchiveCreateModal, setShowArchiveCreateModal] = useState(false);

  // 配置创建模态框状态
  const [showConfigCreateModal, setShowConfigCreateModal] = useState(false);

  useEffect(() => {
    if (customer) {
      setBreadcrumbs([
        { text: "首页", href: "/", icon: "🏠" },
        { text: "客户管理", href: "/customers", icon: "👥" },
        { text: customer.name, icon: "👤" },
      ]);
    }
  }, [setBreadcrumbs, customer]);

  useEffect(() => {
    if (id) {
      loadCustomerDetail();
    }
  }, [id]);

  // 加载客户详情
  const loadCustomerDetail = async () => {
    setLoading(true);
    try {
      if (!id) {
        Toast.error("客户ID不存在");
        return;
      }

      // 首先加载客户基本信息
      const customerResponse = await CustomerService.getCustomer(id);
      if (!customerResponse.success) {
        Toast.error(customerResponse.message || "获取客户信息失败");
        return;
      }
      setCustomer(customerResponse.data);

      // 并行加载其他数据，但允许部分失败
      const [statsResponse, projectsResponse, servicesResponse, configurationsResponse] =
        await Promise.allSettled([
          CustomerService.getCustomerDetailStats(id),
          CustomerService.getCustomerProjects(id, { page: 1, pageSize: 10 }),
          CustomerService.getCustomerServices(id, { page: 1, pageSize: 10 }),
          CustomerService.getCustomerConfigurations(id, { page: 1, pageSize: 10 }),
        ]);

      // 处理统计信息
      if (statsResponse.status === "fulfilled" && statsResponse.value.success) {
        setStats(statsResponse.value.data);
      } else {
        console.warn("获取客户统计信息失败:", statsResponse);
      }

      // 处理项目档案数据（转换为项目格式）
      if (projectsResponse.status === "fulfilled" && projectsResponse.value.success) {
        // 注意：getCustomerArchives 直接返回数组，不是分页结构
        const archives = Array.isArray(projectsResponse.value.data)
          ? projectsResponse.value.data
          : projectsResponse.value.data.items || [];
        const mappedProjects: Project[] = archives.map((archive: Archive) => ({
          id: archive.id,
          name: archive.name,
          description: archive.description,
          status: archive.status,
          createdAt: archive.createdAt,
        }));
        setProjects(mappedProjects);
        console.log("成功加载项目档案:", mappedProjects);
      } else {
        console.warn("获取客户项目列表失败:", projectsResponse);
        setProjects([]);
      }

      // 处理服务工单数据
      if (servicesResponse.status === "fulfilled" && servicesResponse.value.success) {
        // 服务API返回 { data: { services, pagination } }
        const serviceData = servicesResponse.value.data as any;
        const services = serviceData.services || serviceData.items || [];
        setServices(services);
        console.log("成功加载服务工单:", services);
      } else {
        console.warn("获取客户服务工单失败:", servicesResponse);
        setServices([]);
      }

      // 处理配置项数据
      if (configurationsResponse.status === "fulfilled" && configurationsResponse.value.success) {
        // 配置API返回 { data: { configurations, pagination } }
        const configData = configurationsResponse.value.data as any;
        const configurations = configData.configurations || configData.items || [];
        setConfigurations(configurations);
        console.log("成功加载配置项:", configurations);
      } else {
        console.warn("获取客户配置项失败:", configurationsResponse);
        setConfigurations([]);
      }
    } catch (error: any) {
      console.error("加载客户详情失败:", error);
      if (error.status === 404) {
        Toast.error("客户不存在");
      } else {
        Toast.error(error.message || "加载客户详情失败");
      }
    } finally {
      setLoading(false);
    }
  };

  // 删除客户
  const handleDelete = async () => {
    if (!customer) return;

    setDeleting(true);
    try {
      const response = await CustomerService.deleteCustomer(customer.id);

      if (response.success) {
        Toast.success(response.message || "客户删除成功");
        // 返回客户列表
        window.location.href = "/customers";
      } else {
        Toast.error(response.message || "删除失败");
      }
    } catch (error: any) {
      console.error("删除客户失败:", error);
      Toast.error(error.message || "删除失败，请稍后重试");
    } finally {
      setDeleting(false);
    }
  };

  // 处理查看项目档案
  const handleViewArchive = (archiveId: string) => {
    setSelectedArchiveId(archiveId);
    setShowArchiveSideSheet(true);
  };

  // 处理查看服务工单
  const handleViewService = (serviceId: string) => {
    setSelectedServiceId(serviceId);
    setShowServiceSideSheet(true);
  };

  // 项目表格列定义
  const projectColumns = [
    {
      title: "项目名称",
      dataIndex: "name",
      key: "name",
      render: (_text: string, record: Project) => (
        <div>
          <div className="font-medium" style={{ color: "var(--semi-color-text-0)" }}>
            {record.name}
          </div>
          <div className="text-sm" style={{ color: "var(--semi-color-text-2)" }}>
            {record.description}
          </div>
        </div>
      ),
    },
    {
      title: "状态",
      dataIndex: "status",
      key: "status",
      render: (status: Project["status"]) => (
        <Tag color={projectStatusConfig[status].color} size="small">
          {projectStatusConfig[status].text}
        </Tag>
      ),
    },
    {
      title: "创建时间",
      dataIndex: "createdAt",
      key: "createdAt",
      render: (date: string) => new Date(date).toLocaleDateString("zh-CN"),
    },
    {
      title: "操作",
      key: "actions",
      render: (_text: any, record: Project) => (
        <Button
          theme="borderless"
          size="small"
          icon={<IconEyeOpened />}
          onClick={() => handleViewArchive(record.id)}
        >
          查看
        </Button>
      ),
    },
  ];

  // 服务表格列定义
  const serviceColumns = [
    {
      title: "工单号",
      dataIndex: "ticketNumber",
      key: "ticketNumber",
      width: 120,
      render: (ticketNumber: string) => (
        <Text strong style={{ fontFamily: "monospace", fontSize: "12px" }}>
          {ticketNumber || "-"}
        </Text>
      ),
    },
    {
      title: "服务标题",
      dataIndex: "title",
      key: "title",
      render: (_text: string, record: Service) => (
        <div>
          <div className="font-medium" style={{ color: "var(--semi-color-text-0)" }}>
            {record.title}
          </div>
          <div className="text-sm" style={{ color: "var(--semi-color-text-2)" }}>
            {(() => {
              if (!record.description) return "";
              // 移除HTML标签，只显示纯文本
              let textContent = record.description.replace(/<[^>]*>/g, "");
              // 解码HTML实体
              textContent = textContent
                .replace(/&lt;/g, "<")
                .replace(/&gt;/g, ">")
                .replace(/&amp;/g, "&")
                .replace(/&quot;/g, '"')
                .replace(/&#39;/g, "'")
                .replace(/&nbsp;/g, " ")
                .replace(/\s+/g, " ")
                .trim();
              return textContent.length > 50 ? `${textContent.substring(0, 50)}...` : textContent;
            })()}
          </div>
        </div>
      ),
    },
    {
      title: "状态",
      dataIndex: "status",
      key: "status",
      width: 80,
      render: (status: Service["status"]) => (
        <Tag color={serviceStatusConfig[status].color} size="small">
          {serviceStatusConfig[status].text}
        </Tag>
      ),
    },
    {
      title: "优先级",
      dataIndex: "priority",
      key: "priority",
      width: 70,
      render: (priority: Service["priority"]) => {
        const priorityConfig = {
          LOW: { text: "低", color: "grey" },
          MEDIUM: { text: "中", color: "blue" },
          HIGH: { text: "高", color: "orange" },
          URGENT: { text: "紧急", color: "red" },
        } as const;
        return (
          <Tag color={priorityConfig[priority]?.color || "grey"} size="small">
            {priorityConfig[priority]?.text || priority}
          </Tag>
        );
      },
    },
    {
      title: "创建时间",
      dataIndex: "createdAt",
      key: "createdAt",
      width: 100,
      render: (date: string) => (
        <Text type="tertiary" size="small">
          {new Date(date).toLocaleDateString("zh-CN")}
        </Text>
      ),
    },
    {
      title: "操作",
      key: "actions",
      width: 80,
      render: (_text: any, record: Service) => (
        <Button
          theme="borderless"
          size="small"
          icon={<IconEyeOpened />}
          onClick={() => handleViewService(record.id)}
        >
          查看
        </Button>
      ),
    },
  ];

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Spin size="large" />
      </div>
    );
  }

  if (!customer) {
    return (
      <div className="text-center py-12">
        <Text type="tertiary">客户不存在</Text>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* 页面头部 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <Link to="/customers">
            <Button theme="borderless" icon={<IconArrowLeft />} size="large" />
          </Link>
          <div>
            <Title heading={2} className="!mb-1" style={{ color: "var(--semi-color-text-0)" }}>
              {customer.name}
            </Title>
            <Text type="secondary">客户编码：{customer.code}</Text>
          </div>
          <Tag color={customer.isVip ? "amber" : "grey"}>
            {customer.isVip ? "VIP客户" : "普通客户"}
          </Tag>
        </div>

        <Space>
          {hasPermission("customer:update") && (
            <Button
              theme="light"
              type="primary"
              icon={<IconEdit />}
              onClick={() => setShowEditModal(true)}
            >
              编辑
            </Button>
          )}
          {hasPermission("customer:delete") && (
            <Button
              theme="light"
              type="danger"
              icon={<IconDelete />}
              onClick={() => setShowDeleteModal(true)}
            >
              删除
            </Button>
          )}
        </Space>
      </div>

      {/* 主要内容区域 */}
      <Row gutter={[24, 24]}>
        {/* 左侧基本信息 */}
        <Col span={16}>
          <Card
            title="基本信息"
            style={{
              background: "var(--semi-color-bg-2)",
              borderColor: "var(--semi-color-border)",
            }}
          >
            <Row gutter={[24, 16]}>
              <Col span={12}>
                <div className="space-y-1">
                  <Text type="secondary" size="small">
                    客户名称
                  </Text>
                  <div style={{ color: "var(--semi-color-text-0)" }}>{customer.name}</div>
                </div>
              </Col>
              <Col span={12}>
                <div className="space-y-1">
                  <Text type="secondary" size="small">
                    客户编码
                  </Text>
                  <div style={{ color: "var(--semi-color-text-0)" }}>{customer.code}</div>
                </div>
              </Col>
              <Col span={12}>
                <div className="space-y-1">
                  <Text type="secondary" size="small">
                    客户类型
                  </Text>
                  <div>
                    <Tag color={customerTypeConfig[customer.type].color} size="small">
                      {customerTypeConfig[customer.type].text}
                    </Tag>
                  </div>
                </div>
              </Col>
              <Col span={12}>
                <div className="space-y-1">
                  <Text type="secondary" size="small">
                    所属行业
                  </Text>
                  <div style={{ color: "var(--semi-color-text-0)" }}>
                    {customer.industry || "-"}
                  </div>
                </div>
              </Col>
              <Col span={12}>
                <div className="space-y-1">
                  <Text type="secondary" size="small">
                    联系人
                  </Text>
                  <div style={{ color: "var(--semi-color-text-0)" }}>{customer.contactPerson}</div>
                </div>
              </Col>
              <Col span={12}>
                <div className="space-y-1">
                  <Text type="secondary" size="small">
                    联系电话
                  </Text>
                  <div style={{ color: "var(--semi-color-text-0)" }}>{customer.contactPhone}</div>
                </div>
              </Col>
              <Col span={12}>
                <div className="space-y-1">
                  <Text type="secondary" size="small">
                    联系邮箱
                  </Text>
                  <div style={{ color: "var(--semi-color-text-0)" }}>{customer.contactEmail}</div>
                </div>
              </Col>
              <Col span={12}>
                <div className="space-y-1">
                  <Text type="secondary" size="small">
                    VIP状态
                  </Text>
                  <div>
                    <Tag color={customer.isVip ? "amber" : "grey"} size="small">
                      {customer.isVip ? "VIP" : "普通"}
                    </Tag>
                  </div>
                </div>
              </Col>
              <Col span={24}>
                <div className="space-y-1">
                  <Text type="secondary" size="small">
                    客户地址
                  </Text>
                  <div style={{ color: "var(--semi-color-text-0)" }}>{customer.address || "-"}</div>
                </div>
              </Col>
              <Col span={24}>
                <div className="space-y-1">
                  <Text type="secondary" size="small">
                    客户描述
                  </Text>
                  <div style={{ color: "var(--semi-color-text-0)" }}>
                    {customer.description || "-"}
                  </div>
                </div>
              </Col>
              <Col span={12}>
                <div className="space-y-1">
                  <Text type="secondary" size="small">
                    创建时间
                  </Text>
                  <div style={{ color: "var(--semi-color-text-0)" }}>
                    {new Date(customer.createdAt).toLocaleString("zh-CN")}
                  </div>
                </div>
              </Col>
              <Col span={12}>
                <div className="space-y-1">
                  <Text type="secondary" size="small">
                    更新时间
                  </Text>
                  <div style={{ color: "var(--semi-color-text-0)" }}>
                    {new Date(customer.updatedAt).toLocaleString("zh-CN")}
                  </div>
                </div>
              </Col>
            </Row>
          </Card>
        </Col>

        {/* 右侧统计信息和快速操作 */}
        <Col span={8}>
          <div className="space-y-6">
            {/* 统计信息 */}
            <Card
              title="统计信息"
              style={{
                background: "var(--semi-color-bg-2)",
                borderColor: "var(--semi-color-border)",
              }}
            >
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <Text type="secondary">项目档案</Text>
                  <Text strong style={{ color: "var(--semi-color-text-0)", fontSize: "18px" }}>
                    {stats.projects}
                  </Text>
                </div>
                <div className="flex justify-between items-center">
                  <Text type="secondary">服务工单</Text>
                  <Text strong style={{ color: "var(--semi-color-text-0)", fontSize: "18px" }}>
                    {stats.services}
                  </Text>
                </div>
                <div className="flex justify-between items-center">
                  <Text type="secondary">待处理工单</Text>
                  <Text strong style={{ color: "var(--semi-color-warning)", fontSize: "18px" }}>
                    {stats.pendingServices}
                  </Text>
                </div>
                <div className="flex justify-between items-center">
                  <Text type="secondary">配置项数量</Text>
                  <Text strong style={{ color: "var(--semi-color-text-0)", fontSize: "18px" }}>
                    {stats.configurations}
                  </Text>
                </div>
              </div>
            </Card>

            {/* 快速操作 */}
            <Card
              title="快速操作"
              style={{
                background: "var(--semi-color-bg-2)",
                borderColor: "var(--semi-color-border)",
              }}
            >
              <div className="space-y-3">
                {hasPermission("archive:create") && (
                  <Button
                    block
                    theme="light"
                    type="primary"
                    icon={<IconFolderOpen />}
                    onClick={() => setShowArchiveCreateModal(true)}
                  >
                    创建项目档案
                  </Button>
                )}
                {hasPermission("service:create") && (
                  <Button
                    block
                    theme="light"
                    type="primary"
                    icon={<IconTicketCodeStroked />}
                    onClick={() => setShowServiceCreateModal(true)}
                  >
                    创建工单
                  </Button>
                )}
                {hasPermission("configuration:create") && (
                  <Button
                    block
                    theme="light"
                    type="primary"
                    icon={<IconSetting />}
                    onClick={() => setShowConfigCreateModal(true)}
                  >
                    添加配置
                  </Button>
                )}
              </div>
            </Card>
          </div>
        </Col>
      </Row>

      {/* 相关数据标签页 */}
      <Card
        style={{
          background: "var(--semi-color-bg-2)",
          borderColor: "var(--semi-color-border)",
        }}
        bodyStyle={{ padding: 0 }}
      >
        <Tabs type="line" tabPosition="top">
          <TabPane tab="项目档案" itemKey="projects">
            <div className="p-6">
              <div className="flex justify-between items-center mb-4">
                <Title heading={4} className="!mb-0">
                  项目档案
                </Title>
                {hasPermission("archive:create") && (
                  <Button
                    size="small"
                    icon={<IconFolderOpen />}
                    onClick={() => setShowArchiveCreateModal(true)}
                  >
                    新增项目档案
                  </Button>
                )}
              </div>
              <Table
                columns={projectColumns}
                dataSource={projects}
                rowKey="id"
                pagination={false}
                empty={
                  <Empty
                    image={<IconFolder size="extra-large" />}
                    title="暂无项目档案"
                    description="还没有为该客户创建任何项目档案"
                  />
                }
              />
            </div>
          </TabPane>

          <TabPane tab="服务工单" itemKey="services">
            <div className="p-6">
              <div className="flex justify-between items-center mb-4">
                <Title heading={4} className="!mb-0">
                  服务工单
                </Title>
                {hasPermission("service:create") && (
                  <Button
                    size="small"
                    icon={<IconTicketCodeStroked />}
                    onClick={() => setShowServiceCreateModal(true)}
                  >
                    新增工单
                  </Button>
                )}
              </div>
              <Table
                columns={serviceColumns}
                dataSource={services}
                rowKey="id"
                pagination={false}
                empty={
                  <Empty
                    image={<IconTicketCodeStroked size="extra-large" />}
                    title="暂无工单数据"
                    description="还没有为该客户创建任何服务工单"
                  />
                }
              />
            </div>
          </TabPane>

          <TabPane tab="配置项" itemKey="configurations">
            <div className="p-6">
              <div className="flex justify-between items-center mb-4">
                <Title heading={4} className="!mb-0">
                  配置项
                </Title>
                {hasPermission("configuration:create") && (
                  <Button
                    size="small"
                    icon={<IconSetting />}
                    onClick={() => setShowConfigCreateModal(true)}
                  >
                    新增配置
                  </Button>
                )}
              </div>

              {configurations.length > 0 ? (
                <div className="space-y-4">
                  {configurations.map(config => (
                    <Card
                      key={config.id}
                      style={{
                        background: "var(--semi-color-bg-1)",
                        borderColor: "var(--semi-color-border)",
                      }}
                      bodyStyle={{ padding: "16px" }}
                    >
                      <div className="flex justify-between items-start mb-3">
                        <div className="flex-1">
                          <Title heading={5} className="!mb-1">
                            {config.title}
                          </Title>
                          <Text type="secondary" size="small">
                            {config.description}
                          </Text>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Tag color={configTypeConfig[config.configType].color} size="small">
                            {configTypeConfig[config.configType].text}
                          </Tag>
                          <Tag color={config.isActive ? "green" : "grey"} size="small">
                            {config.isActive ? "启用" : "禁用"}
                          </Tag>
                        </div>
                      </div>

                      <div
                        className="bg-white p-3 rounded border"
                        style={{ borderColor: "var(--semi-color-border)" }}
                      >
                        <Row gutter={[16, 8]}>
                          {config.configData.host && (
                            <Col span={12}>
                              <div className="flex justify-between">
                                <Text type="secondary" size="small">
                                  主机:
                                </Text>
                                <Text size="small" className="font-mono">
                                  {config.configData.host}
                                </Text>
                              </div>
                            </Col>
                          )}
                          {config.configData.port && (
                            <Col span={12}>
                              <div className="flex justify-between">
                                <Text type="secondary" size="small">
                                  端口:
                                </Text>
                                <Text size="small" className="font-mono">
                                  {config.configData.port}
                                </Text>
                              </div>
                            </Col>
                          )}
                          {config.configData.username && (
                            <Col span={12}>
                              <div className="flex justify-between">
                                <Text type="secondary" size="small">
                                  用户名:
                                </Text>
                                <Text size="small" className="font-mono">
                                  {config.configData.username}
                                </Text>
                              </div>
                            </Col>
                          )}
                          {config.configData.database && (
                            <Col span={12}>
                              <div className="flex justify-between">
                                <Text type="secondary" size="small">
                                  数据库:
                                </Text>
                                <Text size="small" className="font-mono">
                                  {config.configData.database}
                                </Text>
                              </div>
                            </Col>
                          )}
                          {config.configData.password && (
                            <Col span={12}>
                              <div className="flex justify-between">
                                <Text type="secondary" size="small">
                                  密码:
                                </Text>
                                <Text size="small" className="font-mono">
                                  ••••••••
                                </Text>
                              </div>
                            </Col>
                          )}
                        </Row>

                        <div
                          className="flex justify-between items-center mt-3 pt-2 border-t"
                          style={{ borderColor: "var(--semi-color-border)" }}
                        >
                          <div className="flex items-center space-x-4">
                            <Text type="tertiary" size="small">
                              创建: {new Date(config.createdAt).toLocaleDateString("zh-CN")}
                            </Text>
                            {config.lastUpdated && (
                              <Text type="tertiary" size="small">
                                更新: {new Date(config.lastUpdated).toLocaleDateString("zh-CN")}
                              </Text>
                            )}
                          </div>
                          <Button
                            size="small"
                            theme="borderless"
                            icon={<IconEyeOpened />}
                            onClick={() =>
                              (window.location.href = `/admin/configurations/${config.id}`)
                            }
                          >
                            查看详情
                          </Button>
                        </div>
                      </div>
                    </Card>
                  ))}
                </div>
              ) : (
                <Empty
                  image={<IconSetting size="extra-large" />}
                  title="暂无配置数据"
                  description="还没有为该客户创建任何配置项"
                />
              )}
            </div>
          </TabPane>
        </Tabs>
      </Card>

      {/* 编辑模态框 */}
      {showEditModal && customer && (
        <CustomerEditModal
          visible={showEditModal}
          customer={customer}
          onCancel={() => setShowEditModal(false)}
          onSuccess={() => {
            setShowEditModal(false);
            loadCustomerDetail(); // 重新加载数据
          }}
        />
      )}

      {/* 删除确认模态框 */}
      <Modal
        title="确认删除"
        visible={showDeleteModal}
        onCancel={() => setShowDeleteModal(false)}
        onOk={handleDelete}
        okText="确认删除"
        cancelText="取消"
        okType="danger"
        confirmLoading={deleting}
      >
        <div className="space-y-3">
          <Text>
            确定要删除客户 <Text strong>{customer.name}</Text> 吗？
          </Text>
          <Text type="warning" className="block">
            此操作不可撤销，相关的项目和工单也将受到影响。
          </Text>
        </div>
      </Modal>

      {/* 项目档案详情SideSheet */}
      <ArchiveDetailSideSheet
        visible={showArchiveSideSheet}
        archiveId={selectedArchiveId}
        onClose={() => {
          setShowArchiveSideSheet(false);
          setSelectedArchiveId(null);
        }}
      />

      {/* 服务工单详情SideSheet */}
      <ServiceDetailSideSheet
        visible={showServiceSideSheet}
        serviceId={selectedServiceId}
        onClose={() => {
          setShowServiceSideSheet(false);
          setSelectedServiceId(null);
        }}
        onDataChanged={() => {
          // 刷新相关数据
          loadCustomerDetail(); // 刷新客户数据和关联的服务工单
        }}
      />

      {/* 创建服务工单模态框 */}
      {customer && (
        <ServiceCreateModal
          visible={showServiceCreateModal}
          customerId={customer.id}
          onCancel={() => setShowServiceCreateModal(false)}
          onSuccess={() => {
            setShowServiceCreateModal(false);
            loadCustomerDetail(); // 重新加载数据
          }}
        />
      )}

      {/* 创建项目档案模态框 */}
      {customer && (
        <ArchiveCreateModal
          visible={showArchiveCreateModal}
          customerId={customer.id}
          onCancel={() => setShowArchiveCreateModal(false)}
          onSuccess={() => {
            setShowArchiveCreateModal(false);
            loadCustomerDetail(); // 重新加载数据
          }}
        />
      )}

      {/* 创建配置模态框 */}
      {customer && (
        <ConfigurationCreateModal
          visible={showConfigCreateModal}
          customerId={customer.id}
          onCancel={() => setShowConfigCreateModal(false)}
          onSuccess={() => {
            setShowConfigCreateModal(false);
            loadCustomerDetail(); // 重新加载数据
          }}
        />
      )}
    </div>
  );
}
