import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import {
  Card,
  Table,
  Button,
  Input,
  Select,
  Space,
  Tag,
  Typography,
  Row,
  Col,
  Pagination,
} from "@douyinfe/semi-ui";
import {
  IconSearch,
  IconPlus,
  IconDownload,
  IconEdit,
  IconDelete,
  IconEyeOpened,
} from "@douyinfe/semi-icons";
import { useAuthStore, useUIStore } from "@/stores";
import { showSuccess, showError, showInfo } from "@/utils/notification";
import { CustomerCreateModal } from "@/components/customers/CustomerCreateModal";
import { CustomerEditModal } from "@/components/customers/CustomerEditModal";
import CustomerService from "@/services/customer";
import type { Customer as CustomerType, CustomerStats, CustomerListParams } from "@/types";

const { Title, Text } = Typography;

type Customer = CustomerType;

interface PaginationData {
  current: number;
  pageSize: number;
  total: number;
  totalPages: number;
}

export default function CustomersPage() {
  const navigate = useNavigate();
  const { hasPermission } = useAuthStore();
  const { setBreadcrumbs } = useUIStore();

  // 状态管理
  const [loading, setLoading] = useState(false);
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [stats, setStats] = useState<CustomerStats>({
    totalCustomers: 0,
    recentCustomers: 0,
    levelDistribution: [],
    topIndustries: [],
  });
  const [pagination, setPagination] = useState<PaginationData>({
    current: 1,
    pageSize: 15,
    total: 0,
    totalPages: 0,
  });

  // 筛选和搜索状态
  const [searchQuery, setSearchQuery] = useState("");
  const [typeFilter, setTypeFilter] = useState("");
  const [levelFilter, setLevelFilter] = useState("");
  const [industryFilter, setIndustryFilter] = useState("");
  const [vipFilter, setVipFilter] = useState<boolean | undefined>(undefined);

  // 模态框状态
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [editingCustomer, setEditingCustomer] = useState<Customer | null>(null);
  const [deletingCustomer, setDeletingCustomer] = useState<Customer | null>(null);

  // 设置面包屑
  useEffect(() => {
    setBreadcrumbs([
      { text: "首页", href: "/", icon: "🏠" },
      { text: "客户管理", icon: "👥" },
    ]);
  }, [setBreadcrumbs]);

  // 加载客户数据
  const loadCustomers = async () => {
    setLoading(true);
    try {
      const params: CustomerListParams = {
        page: pagination.current,
        limit: pagination.pageSize,
        search: searchQuery || undefined,
        type: (typeFilter as any) || undefined,
        level: (levelFilter as any) || undefined,
        industry: industryFilter || undefined,
        isVip: vipFilter,
      };

      const response = await CustomerService.getCustomers(params);

      if (response.success) {
        // 处理不同的响应格式
        const customerData = response.data.customers || response.data.items || [];
        const paginationData = response.data.pagination || response.data;

        setCustomers(customerData);
        setPagination(prev => ({
          ...prev,
          total: paginationData.total || 0,
          totalPages: (paginationData as any).totalPages || (paginationData as any).pages || 0,
        }));
      } else {
        showError(response.message || "加载客户数据失败");
      }
    } catch (error: any) {
      showError(error.message || "加载客户数据失败");
    } finally {
      setLoading(false);
    }
  };

  // 加载统计数据
  const loadStats = async () => {
    try {
      const response = await CustomerService.getCustomerStats();
      if (response.success) {
        setStats(response.data);
      }
    } catch (error) {
      console.error("加载统计数据失败:", error);
    }
  };

  // 初始加载
  useEffect(() => {
    loadCustomers();
    loadStats();
  }, []);

  // 搜索和筛选变化时重新加载
  useEffect(() => {
    const timer = setTimeout(() => {
      setPagination(prev => ({ ...prev, current: 1 }));
      loadCustomers();
    }, 300);

    return () => clearTimeout(timer);
  }, [searchQuery, typeFilter, levelFilter, industryFilter, vipFilter]);

  // 分页变化时重新加载
  useEffect(() => {
    loadCustomers();
  }, [pagination.current, pagination.pageSize]);

  // 处理分页变化
  const handlePageChange = (page: number, pageSize: number) => {
    setPagination(prev => ({
      ...prev,
      current: page,
      pageSize,
    }));
  };

  // 处理搜索
  const handleSearch = (value: string) => {
    setSearchQuery(value);
  };

  // 处理筛选
  const handleFilterChange = (type: string, value: any) => {
    switch (type) {
      case "type":
        setTypeFilter(value);
        break;
      case "level":
        setLevelFilter(value);
        break;
      case "industry":
        setIndustryFilter(value);
        break;
      case "vip":
        setVipFilter(value);
        break;
    }
  };

  // 处理导出
  const handleExport = () => {
    showInfo("导出功能开发中...");
  };

  // 处理查看客户详情
  const handleView = (customer: Customer) => {
    navigate(`/customers/${customer.id}`);
  };

  // 处理编辑客户
  const handleEdit = (customer: Customer) => {
    setEditingCustomer(customer);
    setShowEditModal(true);
  };

  // 处理删除客户
  const handleDeleteClick = (customer: Customer) => {
    setDeletingCustomer(customer);
  };

  // 确认删除客户
  const handleDelete = async () => {
    if (!deletingCustomer) return;

    try {
      const response = await CustomerService.deleteCustomer(deletingCustomer.id);

      if (response.success) {
        showSuccess(response.message || "客户删除成功");
        setDeletingCustomer(null);
        loadCustomers(); // 重新加载数据
        loadStats(); // 重新加载统计
      } else {
        showError(response.message || "删除失败");
      }
    } catch (error: any) {
      showError(error.message || "删除失败，请稍后重试");
    }
  };

  // 处理创建成功
  const handleCreateSuccess = () => {
    setShowCreateModal(false);
    loadCustomers();
    loadStats();
  };

  // 处理编辑成功
  const handleEditSuccess = () => {
    setShowEditModal(false);
    setEditingCustomer(null);
    loadCustomers();
    loadStats();
  };

  // 表格列定义
  const columns = [
    {
      title: "客户名称",
      dataIndex: "name",
      key: "name",
      render: (text: string, record: Customer) => (
        <div>
          <div style={{ fontWeight: 500, color: "var(--semi-color-text-0)" }}>{text}</div>
          <div style={{ fontSize: "12px", color: "var(--semi-color-text-2)" }}>{record.code}</div>
        </div>
      ),
    },
    {
      title: "类型",
      dataIndex: "type",
      key: "type",
      render: (type: string) => {
        const typeConfig: Record<string, { label: string; color: any }> = {
          ENTERPRISE: { label: "企业客户", color: "blue" },
          INDIVIDUAL: { label: "个人客户", color: "green" },
          GOVERNMENT: { label: "政府机构", color: "orange" },
          NONPROFIT: { label: "非营利组织", color: "purple" },
        };
        const config = typeConfig[type];
        return <Tag color={config?.color}>{config?.label}</Tag>;
      },
    },
    {
      title: "等级",
      dataIndex: "level",
      key: "level",
      render: (level: string) => {
        const levelConfig: Record<string, { label: string; color: any }> = {
          BASIC: { label: "基础", color: "grey" },
          STANDARD: { label: "标准", color: "blue" },
          PREMIUM: { label: "高级", color: "orange" },
          ENTERPRISE: { label: "企业", color: "red" },
        };
        const config = levelConfig[level];
        return <Tag color={config?.color}>{config?.label}</Tag>;
      },
    },
    {
      title: "联系人",
      dataIndex: "contactPerson",
      key: "contactPerson",
      render: (contactPerson: string, record: Customer) => (
        <div>
          <div>{contactPerson || "-"}</div>
          <div style={{ fontSize: "12px", color: "var(--semi-color-text-2)" }}>
            {record.contactPhone || "-"}
          </div>
        </div>
      ),
    },
    {
      title: "行业",
      dataIndex: "industry",
      key: "industry",
      render: (industry: string) => industry || "-",
    },
    {
      title: "状态",
      key: "status",
      render: (record: Customer) => (
        <Tag color={record.isVip ? "red" : "green"}>{record.isVip ? "VIP客户" : "普通客户"}</Tag>
      ),
    },
    {
      title: "操作",
      key: "actions",
      render: (record: Customer) => (
        <Space>
          <Button
            icon={<IconEyeOpened />}
            size="small"
            theme="borderless"
            onClick={() => handleView(record)}
          >
            查看
          </Button>
          {hasPermission("customer:write") && (
            <Button
              icon={<IconEdit />}
              size="small"
              theme="borderless"
              onClick={() => handleEdit(record)}
            >
              编辑
            </Button>
          )}
          {hasPermission("customer:delete") && (
            <Button
              icon={<IconDelete />}
              size="small"
              theme="borderless"
              type="danger"
              onClick={() => handleDeleteClick(record)}
            >
              删除
            </Button>
          )}
        </Space>
      ),
    },
  ];

  // 筛选选项
  const typeFilterOptions = [
    { label: "全部类型", value: "" },
    { label: "企业客户", value: "ENTERPRISE" },
    { label: "个人客户", value: "INDIVIDUAL" },
    { label: "政府机构", value: "GOVERNMENT" },
    { label: "非营利组织", value: "NONPROFIT" },
  ];

  const levelFilterOptions = [
    { label: "全部等级", value: "" },
    { label: "基础", value: "BASIC" },
    { label: "标准", value: "STANDARD" },
    { label: "高级", value: "PREMIUM" },
    { label: "企业", value: "ENTERPRISE" },
  ];

  const vipFilterOptions = [
    { label: "全部客户", value: "" },
    { label: "VIP客户", value: "true" },
    { label: "普通客户", value: "false" },
  ];

  return (
    <div style={{ padding: "24px" }}>
      {/* 统计卡片 */}
      <div style={{ marginBottom: "24px" }}>
        <Row gutter={[16, 16]}>
          <Col span={6}>
            <Card
              style={{
                background: "var(--semi-color-bg-2)",
                borderColor: "var(--semi-color-border)",
              }}
              bodyStyle={{ padding: "16px" }}
            >
              <div className="text-center">
                <div
                  className="text-2xl font-bold mb-1"
                  style={{ color: "var(--semi-color-primary)" }}
                >
                  {stats.totalCustomers || 0}
                </div>
                <div className="text-sm" style={{ color: "var(--semi-color-text-2)" }}>
                  总客户数
                </div>
              </div>
            </Card>
          </Col>
          <Col span={6}>
            <Card
              style={{
                background: "var(--semi-color-bg-2)",
                borderColor: "var(--semi-color-border)",
              }}
              bodyStyle={{ padding: "16px" }}
            >
              <div className="text-center">
                <div
                  className="text-2xl font-bold mb-1"
                  style={{ color: "var(--semi-color-success)" }}
                >
                  {stats.recentCustomers || 0}
                </div>
                <div className="text-sm" style={{ color: "var(--semi-color-text-2)" }}>
                  最近客户
                </div>
              </div>
            </Card>
          </Col>
          <Col span={6}>
            <Card
              style={{
                background: "var(--semi-color-bg-2)",
                borderColor: "var(--semi-color-border)",
              }}
              bodyStyle={{ padding: "16px" }}
            >
              <div className="text-center">
                <div
                  className="text-2xl font-bold mb-1"
                  style={{ color: "var(--semi-color-warning)" }}
                >
                  {stats.levelDistribution?.find(item => item.level === "PREMIUM")?.count || 0}
                </div>
                <div className="text-sm" style={{ color: "var(--semi-color-text-2)" }}>
                  高级客户
                </div>
              </div>
            </Card>
          </Col>
          <Col span={6}>
            <Card
              style={{
                background: "var(--semi-color-bg-2)",
                borderColor: "var(--semi-color-border)",
              }}
              bodyStyle={{ padding: "16px" }}
            >
              <div className="text-center">
                <div
                  className="text-2xl font-bold mb-1"
                  style={{ color: "var(--semi-color-primary)" }}
                >
                  {stats.topIndustries?.length || 0}
                </div>
                <div className="text-sm" style={{ color: "var(--semi-color-text-2)" }}>
                  行业分布
                </div>
              </div>
            </Card>
          </Col>
        </Row>
      </div>

      {/* 操作栏 */}
      <Card
        style={{
          background: "var(--semi-color-bg-2)",
          borderColor: "var(--semi-color-border)",
        }}
        bodyStyle={{ padding: "16px" }}
      >
        <Row justify="space-between" align="middle">
          <Col>
            <Space>
              {hasPermission("customer:write") && (
                <Button
                  theme="solid"
                  type="primary"
                  icon={<IconPlus />}
                  onClick={() => {
                    console.log("点击新增客户按钮");
                    setShowCreateModal(true);
                    console.log("showCreateModal设置为true");
                  }}
                >
                  新增客户
                </Button>
              )}
              {hasPermission("customer:export") && (
                <Button
                  theme="light"
                  type="secondary"
                  icon={<IconDownload />}
                  onClick={handleExport}
                >
                  导出数据
                </Button>
              )}
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 搜索和筛选 */}
      <Card
        style={{
          background: "var(--semi-color-bg-2)",
          borderColor: "var(--semi-color-border)",
        }}
        bodyStyle={{ padding: "16px" }}
      >
        <Row gutter={[16, 16]} align="middle">
          <Col span={6}>
            <Input
              placeholder="搜索客户名称、联系人或邮箱..."
              prefix={<IconSearch />}
              value={searchQuery}
              onChange={handleSearch}
            />
          </Col>
          <Col span={6}>
            <Select
              placeholder="类型筛选"
              optionList={typeFilterOptions}
              value={typeFilter}
              onChange={value => handleFilterChange("type", value)}
              style={{ width: "100%" }}
            />
          </Col>
          <Col span={6}>
            <Select
              placeholder="等级筛选"
              optionList={levelFilterOptions}
              value={levelFilter}
              onChange={value => handleFilterChange("level", value)}
              style={{ width: "100%" }}
            />
          </Col>
          <Col span={6}>
            <Select
              placeholder="VIP筛选"
              optionList={vipFilterOptions}
              value={vipFilter?.toString() || ""}
              onChange={value =>
                handleFilterChange(
                  "vip",
                  value === "true" ? true : value === "false" ? false : undefined
                )
              }
              style={{ width: "100%" }}
            />
          </Col>
        </Row>
      </Card>

      {/* 客户列表 */}
      <Card
        style={{
          background: "var(--semi-color-bg-2)",
          borderColor: "var(--semi-color-border)",
        }}
        bodyStyle={{ padding: "16px" }}
      >
        <Table
          columns={columns}
          dataSource={customers}
          loading={loading}
          pagination={false}
          rowKey="id"
        />

        {/* 分页 */}
        <div style={{ marginTop: "16px", textAlign: "right" }}>
          <Pagination
            currentPage={pagination.current}
            pageSize={pagination.pageSize}
            total={pagination.total}
            onPageChange={page => handlePageChange(page, pagination.pageSize)}
            onPageSizeChange={pageSize => handlePageChange(1, pageSize)}
            showSizeChanger
            showQuickJumper
            showTotal
          />
        </div>
      </Card>

      {/* 模态框 */}
      <CustomerCreateModal
        visible={showCreateModal}
        onCancel={() => setShowCreateModal(false)}
        onSuccess={handleCreateSuccess}
      />

      {editingCustomer && (
        <CustomerEditModal
          visible={showEditModal}
          customer={editingCustomer}
          onCancel={() => {
            setShowEditModal(false);
            setEditingCustomer(null);
          }}
          onSuccess={handleEditSuccess}
        />
      )}

      {/* 删除确认对话框 */}
      {deletingCustomer && (
        <div
          style={{
            position: "fixed",
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: "rgba(0, 0, 0, 0.5)",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            zIndex: 1000,
          }}
        >
          <Card style={{ width: "400px" }}>
            <Title heading={4}>确认删除</Title>
            <Text>确定要删除客户 "{deletingCustomer.name}" 吗？此操作不可撤销。</Text>
            <div style={{ marginTop: "24px", textAlign: "right" }}>
              <Space>
                <Button onClick={() => setDeletingCustomer(null)}>取消</Button>
                <Button type="danger" onClick={handleDelete}>
                  确认删除
                </Button>
              </Space>
            </div>
          </Card>
        </div>
      )}
    </div>
  );
}
