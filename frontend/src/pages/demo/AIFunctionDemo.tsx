import React, { useState, useRef } from "react";
import {
  Card,
  Form,
  Input,
  TextArea,
  Button,
  Space,
  Typography,
  Row,
  Col,
  Select,
  Toast,
  Divider,
  Tag,
  Progress,
} from "@douyinfe/semi-ui";
import {
  IconBolt as IconRobot,
  IconStarStroked as IconMagicWand,
  IconRefresh,
  IconTick as IconCheck,
} from "@douyinfe/semi-icons";
import { useAI } from "@/hooks/useAI";
import { AISuggestionCard } from "@/components/ai";

const { Title, Text, Paragraph } = Typography;
const { Option } = Select;

// 演示数据
const demoScenarios = [
  {
    title: "紧急故障场景",
    description: "系统出现严重故障，数据库连接异常，用户无法登录，需要紧急处理",
    contextData: {
      customerId: "vip-customer-001",
      customerType: "VIP",
      industry: "finance"
    }
  },
  {
    title: "咨询服务场景",
    description: "希望咨询一下系统升级的最佳实践和建议方案",
    contextData: {
      customerId: "standard-customer-001", 
      customerType: "Standard",
      industry: "retail"
    }
  },
  {
    title: "维护服务场景",
    description: "定期系统维护，需要更新服务器补丁和优化数据库性能",
    contextData: {
      customerId: "enterprise-customer-001",
      customerType: "Enterprise",
      industry: "technology"
    }
  },
  {
    title: "Bug修复场景",
    description: "发现系统bug，用户报告页面显示错误，需要修复代码缺陷",
    contextData: {
      customerId: "standard-customer-002",
      customerType: "Standard", 
      industry: "education"
    }
  }
];

const categoryOptions = [
  { label: "维护", value: "MAINTENANCE" },
  { label: "支持", value: "SUPPORT" },
  { label: "升级", value: "UPGRADE" },
  { label: "Bug修复", value: "BUGFIX" },
  { label: "咨询", value: "CONSULTING" },
  { label: "监控", value: "MONITORING" },
];

const priorityOptions = [
  { label: "低", value: "LOW" },
  { label: "中", value: "MEDIUM" },
  { label: "高", value: "HIGH" },
  { label: "紧急", value: "URGENT" },
];

export default function AIFunctionDemo() {
  const formRef = useRef<any>();
  const [currentDescription, setCurrentDescription] = useState("");
  const [showAISuggestions, setShowAISuggestions] = useState(false);
  const [selectedScenario, setSelectedScenario] = useState<number | null>(null);

  // 初始化AI功能
  const ai = useAI({
    userId: "demo-user",
    autoLoadConfig: true,
    contextData: {
      customerId: "demo-customer",
      customerType: "Standard",
    },
  });

  // 加载演示场景
  const handleLoadScenario = (index: number) => {
    const scenario = demoScenarios[index];
    setSelectedScenario(index);
    setCurrentDescription(scenario.description);
    
    // 更新表单
    if (formRef.current) {
      formRef.current.setFieldsValue({
        title: "",
        description: scenario.description,
        category: "",
        priority: "",
      });
    }

    // 清除之前的建议
    ai.clearSuggestions();
    setShowAISuggestions(false);
  };

  // 手动触发AI分析
  const handleAnalyzeWithAI = async () => {
    if (!currentDescription.trim()) {
      Toast.warning("请先输入工单描述内容");
      return;
    }

    setShowAISuggestions(true);
    
    // 使用选中场景的上下文数据
    const contextData = selectedScenario !== null 
      ? demoScenarios[selectedScenario].contextData 
      : { customerType: "Standard" };
      
    await ai.analyzeContent(currentDescription);
  };

  // 采用单个AI建议
  const handleAdoptSuggestion = (field: string, value: string) => {
    const formApi = formRef.current;
    if (!formApi) return;

    switch (field) {
      case "title":
        formApi.setValue("title", value);
        break;
      case "category":
        formApi.setValue("category", value);
        break;
      case "priority":
        formApi.setValue("priority", value);
        break;
    }

    ai.adoptSuggestion(field, value);
    Toast.success(`已采用${field === "title" ? "标题" : field === "category" ? "类别" : "优先级"}建议`);
  };

  // 采用所有AI建议
  const handleAdoptAllSuggestions = () => {
    const adoptedValues = ai.adoptAllSuggestions();
    const formApi = formRef.current;

    if (!formApi || Object.keys(adoptedValues).length === 0) {
      Toast.warning("暂无可采用的建议");
      return;
    }

    let adoptedCount = 0;
    Object.entries(adoptedValues).forEach(([field, value]) => {
      switch (field) {
        case "title":
          formApi.setValue("title", value);
          adoptedCount++;
          break;
        case "category":
          formApi.setValue("category", value);
          adoptedCount++;
          break;
        case "priority":
          formApi.setValue("priority", value);
          adoptedCount++;
          break;
      }
    });

    Toast.success(`已采用 ${adoptedCount} 个AI建议`);
  };

  return (
    <div style={{ padding: 24, maxWidth: 1200, margin: "0 auto" }}>
      <div style={{ marginBottom: 24 }}>
        <Title heading={2}>AI功能演示</Title>
        <Paragraph type="secondary">
          演示运维服务管理系统的AI智能分析功能，包括工单分类、优先级评估和标题生成。
        </Paragraph>
      </div>

      <Row gutter={24}>
        {/* 演示场景选择 */}
        <Col span={8}>
          <Card title="演示场景" style={{ marginBottom: 16 }}>
            <div style={{ display: "flex", flexDirection: "column", gap: "8px" }}>
              {demoScenarios.map((scenario, index) => (
                <Button
                  key={index}
                  block
                  type={selectedScenario === index ? "primary" : "tertiary"}
                  onClick={() => handleLoadScenario(index)}
                >
                  {scenario.title}
                </Button>
              ))}
            </div>
          </Card>

          {/* AI状态信息 */}
          <Card title="AI状态">
            <div style={{ display: "flex", flexDirection: "column", gap: "12px" }}>
              <div>
                <Text strong>AI模式: </Text>
                <Tag color={ai.isAIDisabled ? "red" : "green"}>
                  {ai.isAIDisabled ? "已禁用" : ai.isAggressiveMode ? "智能模式" : "手动触发"}
                </Tag>
              </div>
              <div>
                <Text strong>分析状态: </Text>
                <Tag color={ai.analyzing ? "blue" : "grey"}>
                  {ai.analyzing ? "分析中..." : "就绪"}
                </Tag>
              </div>
              <div>
                <Text strong>建议数量: </Text>
                <Text>{ai.suggestions.length}</Text>
              </div>
              {ai.lastAnalysis && (
                <div>
                  <Text strong>整体置信度: </Text>
                  <Progress
                    percent={ai.lastAnalysis.overallConfidence}
                    size="small"
                    showInfo={false}
                    style={{ width: 100, display: "inline-block", marginLeft: 8 }}
                  />
                  <Text style={{ marginLeft: 8 }}>{ai.lastAnalysis.overallConfidence}%</Text>
                </div>
              )}
            </div>
          </Card>
        </Col>

        {/* 工单表单 */}
        <Col span={16}>
          <Card title="工单创建表单">
            <Form
              getFormApi={(formApi) => (formRef.current = formApi)}
              labelPosition="left"
              labelWidth={100}
            >
              <Form.Input
                field="title"
                label={
                  <Space>
                    <span>工单标题</span>
                    {ai.suggestions.find(s => s.field === "title") && (
                      <span style={{ fontSize: "12px", color: "var(--semi-color-success)" }}>
                        🤖 AI建议
                      </span>
                    )}
                  </Space>
                }
                placeholder="AI将根据描述生成标题建议"
              />

              <div>
                <div style={{ display: "flex", justifyContent: "space-between", marginBottom: 8 }}>
                  <span className="semi-form-field-label">工单描述</span>
                  <Button
                    theme="light"
                    type="primary"
                    size="small"
                    icon={<IconMagicWand />}
                    onClick={handleAnalyzeWithAI}
                    loading={ai.analyzing}
                    disabled={!currentDescription.trim()}
                  >
                    AI智能分析
                  </Button>
                </div>
                <TextArea
                  placeholder="请输入工单描述内容，或选择上方的演示场景"
                  rows={4}
                  value={currentDescription}
                  onChange={(value) => setCurrentDescription(value)}
                />
              </div>

              <Form.Select
                field="category"
                label={
                  <Space>
                    <span>服务类别</span>
                    {ai.suggestions.find(s => s.field === "category") && (
                      <span style={{ fontSize: "12px", color: "var(--semi-color-success)" }}>
                        🤖 AI建议
                      </span>
                    )}
                  </Space>
                }
                placeholder="请选择服务类别"
              >
                {categoryOptions.map(option => (
                  <Option key={option.value} value={option.value}>
                    {option.label}
                  </Option>
                ))}
              </Form.Select>

              <Form.Select
                field="priority"
                label={
                  <Space>
                    <span>优先级</span>
                    {ai.suggestions.find(s => s.field === "priority") && (
                      <span style={{ fontSize: "12px", color: "var(--semi-color-success)" }}>
                        🤖 AI建议
                      </span>
                    )}
                  </Space>
                }
                placeholder="请选择优先级"
              >
                {priorityOptions.map(option => (
                  <Option key={option.value} value={option.value}>
                    {option.label}
                  </Option>
                ))}
              </Form.Select>
            </Form>

            {/* AI建议区域 */}
            {showAISuggestions && (
              <div style={{ marginTop: 16 }}>
                <Divider>AI分析结果</Divider>
                <AISuggestionCard
                  analysis={ai.lastAnalysis}
                  loading={ai.analyzing}
                  error={ai.error}
                  onAdoptAll={handleAdoptAllSuggestions}
                  onAdoptSuggestion={handleAdoptSuggestion}
                  onRetryAnalysis={() => ai.analyzeContent(currentDescription)}
                  onClose={() => {
                    setShowAISuggestions(false);
                    ai.clearSuggestions();
                  }}
                />
              </div>
            )}
          </Card>
        </Col>
      </Row>
    </div>
  );
}
