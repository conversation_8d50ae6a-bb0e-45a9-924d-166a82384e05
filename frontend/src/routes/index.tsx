import { lazy, Suspense } from "react";
import { RouteObject } from "react-router-dom";
import { PageLoading } from "@/components/common/PageLoading";
import { BasicLayout } from "@/components/layout/BasicLayout";
import { AuthGuard } from "@/components/guards/AuthGuard";
import { PermissionGuard } from "@/components/guards/PermissionGuard";

// Lazy load pages
const LoginPage = lazy(() => import("@/pages/auth/LoginPage"));
const DashboardPage = lazy(() => import("@/pages/DashboardPage"));
const CustomersPage = lazy(() => import("@/pages/customers/CustomersPage"));
const CustomerDetailPage = lazy(() => import("@/pages/customers/CustomerDetailPage"));
const ProjectsPage = lazy(() => import("@/pages/projects/ProjectsPage"));
const ProjectDetailPage = lazy(() => import("@/pages/projects/ProjectDetailPage"));
const ServicesPage = lazy(() => import("@/pages/services/ServicesPage"));
const ServiceDetailPage = lazy(() => import("@/pages/services/ServiceDetailPage"));
const AdminDashboardPage = lazy(() => import("@/pages/admin/AdminDashboardPage"));
const EnhancedUsersPage = lazy(() => import("@/pages/admin/EnhancedUsersPage"));
const UsersPage = lazy(() => import("@/pages/admin/UsersPage"));
const RolesPage = lazy(() => import("@/pages/admin/RolesPage"));
const PermissionTemplatesPage = lazy(() => import("@/pages/admin/PermissionTemplatesPage"));
const SystemMonitorPage = lazy(() => import("@/pages/admin/SystemMonitorPage"));
const EnhancedMonitorPage = lazy(() => import("@/pages/admin/EnhancedMonitorPage"));
const SystemConfigPage = lazy(() => import("@/pages/admin/SystemConfigPage"));
const AuditLogsPage = lazy(() => import("@/pages/admin/AuditLogsPage"));
const ApiKeyPage = lazy(() => import("@/pages/admin/ApiKeyPage"));
const MyApiKeysPage = lazy(() => import("@/pages/profile/MyApiKeysPage"));
const TaskMonitoringPage = lazy(() => import("@/pages/admin/TaskMonitoringPage"));
const AIManagementPage = lazy(() => import("@/pages/admin/AIManagementPageOptimized"));
const ReportsPage = lazy(() => import("@/pages/admin/ReportsPage"));
const AIFunctionDemo = lazy(() => import("@/pages/demo/AIFunctionDemo"));
const NotFoundPage = lazy(() => import("@/pages/NotFoundPage"));
const ForbiddenPage = lazy(() => import("@/pages/ForbiddenPage"));
const WorkflowDesignerPage = lazy(() => import("@/pages/admin/WorkflowDesignerPage"));

const withSuspense = (Component: React.ComponentType) => (
  <Suspense fallback={<PageLoading />}>
    <Component />
  </Suspense>
);

const withAuth = (Component: React.ComponentType) => (
  <AuthGuard>{withSuspense(Component)}</AuthGuard>
);

const withPermission = (
  Component: React.ComponentType,
  permissions?: string | string[],
  roles?: string | string[],
  requireAll = false
) => (
  <AuthGuard>
    <PermissionGuard permissions={permissions} roles={roles} requireAll={requireAll}>
      {withSuspense(Component)}
    </PermissionGuard>
  </AuthGuard>
);

export const routeConfig: RouteObject[] = [
  {
    path: "/login",
    element: withSuspense(LoginPage),
  },
  {
    path: "/403",
    element: withSuspense(ForbiddenPage),
  },
  {
    path: "/",
    element: <BasicLayout />,
    children: [
      {
        index: true,
        element: withAuth(DashboardPage),
      },
      {
        path: "customers",
        children: [
          {
            index: true,
            element: withPermission(CustomersPage, "customer:read"),
          },
          {
            path: ":id",
            element: withPermission(CustomerDetailPage, "customer:read"),
          },
        ],
      },
      {
        path: "projects",
        children: [
          {
            index: true,
            element: withPermission(ProjectsPage, "project:read"),
          },
          {
            path: ":id",
            element: withPermission(ProjectDetailPage, "project:read"),
          },
        ],
      },
      {
        path: "services",
        children: [
          {
            index: true,
            element: withPermission(ServicesPage, "service:read"),
          },
          {
            path: ":id",
            element: withPermission(ServiceDetailPage, "service:read"),
          },
        ],
      },
      {
        path: "admin",
        children: [
          {
            path: "dashboard",
            element: withPermission(AdminDashboardPage, ["admin:all"]),
          },
          {
            path: "users",
            element: withPermission(EnhancedUsersPage, ["user:read", "admin:all"]),
          },
          {
            path: "roles",
            element: withPermission(RolesPage, ["role:read", "admin:all"], [], false),
          },
          {
            path: "permission-templates",
            element: withPermission(PermissionTemplatesPage, ["role:read", "admin:all"]),
          },
          {
            path: "monitor",
            element: withPermission(SystemMonitorPage, ["admin:all"]),
          },
          {
            path: "enhanced-monitor",
            element: withPermission(EnhancedMonitorPage, ["admin:all"]),
          },
          {
            path: "system-config",
            element: withPermission(SystemConfigPage, ["system:read", "admin:all"], [], false),
          },
          {
            path: "audit-logs",
            element: withPermission(AuditLogsPage, ["audit:read", "admin:all"]),
          },
          // 移除 admin 下 API Key 页面入口
          {
            path: "tasks",
            element: withPermission(TaskMonitoringPage, ["admin:all"]),
          },
          {
            path: "ai-management",
            element: withPermission(AIManagementPage, ["admin:all"]),
          },
          {
            path: "reports",
            element: withPermission(ReportsPage, ["report:read", "admin:all"], [], false),
          },
          {
            path: "workflow-designer",
            element: withPermission(WorkflowDesignerPage, ["admin:all"]),
          },
        ],
      },
      {
        path: "demo",
        children: [
          {
            path: "ai-function",
            element: withAuth(AIFunctionDemo),
          },
        ],
      },
      {
        path: "me",
        children: [
          {
            path: "api-keys",
            element: withAuth(MyApiKeysPage),
          },
        ],
      },
    ],
  },
  {
    path: "*",
    element: withSuspense(NotFoundPage),
  },
];
