import React, { createContext, useContext, useRef, useEffect, useMemo } from "react";
import { useWebSocket, RealtimeChannelType } from "@/composables/useWebSocket";

// WebSocket Context类型定义
interface WebSocketContextType {
  webSocket: ReturnType<typeof useWebSocket>;
}

// 创建Context
const WebSocketContext = createContext<WebSocketContextType | null>(null);

// WebSocket Provider组件
interface WebSocketProviderProps {
  children: React.ReactNode;
}

export const WebSocketProvider: React.FC<WebSocketProviderProps> = ({ children }) => {
  const initRef = useRef(false);
  const mountedRef = useRef(true);

  // 使用useMemo确保配置对象稳定
  const webSocketConfig = useMemo(
    () => ({
      subscriptions: [
        RealtimeChannelType.NOTIFICATION,
        RealtimeChannelType.USER,
        RealtimeChannelType.SYSTEM_MONITOR,
        RealtimeChannelType.SYSTEM_ALERT,
        RealtimeChannelType.GLOBAL,
      ],
      autoConnect: true,
      autoReconnect: true,
      maxReconnectAttempts: 3,
      reconnectDelay: 5000,
    }),
    []
  );

  // 正确使用Hook
  const webSocket = useWebSocket(webSocketConfig);

  // 初始化日志
  useEffect(() => {
    if (!initRef.current && mountedRef.current) {
      console.log("🔗 全局WebSocket Provider已初始化");
      initRef.current = true;
    }

    return () => {
      mountedRef.current = false;
    };
  }, []);

  // 监听连接状态变化 - 限制日志频率
  useEffect(() => {
    if (mountedRef.current) {
      console.log(`📡 WebSocket状态变更: ${webSocket.state.status}`);
    }
  }, [webSocket.state.status]);

  return <WebSocketContext.Provider value={{ webSocket }}>{children}</WebSocketContext.Provider>;
};

// Hook来获取WebSocket实例
export const useGlobalWebSocket = () => {
  const context = useContext(WebSocketContext);

  if (!context) {
    throw new Error("useGlobalWebSocket must be used within a WebSocketProvider");
  }

  return context.webSocket;
};

// 导出Context用于特殊场景
export { WebSocketContext };
