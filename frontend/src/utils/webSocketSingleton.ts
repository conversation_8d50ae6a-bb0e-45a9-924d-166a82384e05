// WebSocket单例管理器
let globalWebSocketInstance: WebSocket | null = null;
let connectionPromise: Promise<WebSocket> | null = null;

export const getGlobalWebSocket = async (url: string): Promise<WebSocket> => {
  // 如果已存在有效连接，直接返回
  if (globalWebSocketInstance?.readyState === WebSocket.OPEN) {
    return globalWebSocketInstance;
  }

  // 如果正在连接中，等待连接完成
  if (connectionPromise) {
    return connectionPromise;
  }

  // 创建新连接
  connectionPromise = new Promise((resolve, reject) => {
    try {
      const ws = new WebSocket(url);

      ws.onopen = () => {
        console.log("🔗 全局WebSocket连接已建立");
        globalWebSocketInstance = ws;
        connectionPromise = null;
        resolve(ws);
      };

      ws.onerror = error => {
        console.error("❌ WebSocket连接失败:", error);
        connectionPromise = null;
        reject(error);
      };

      ws.onclose = () => {
        console.log("🔌 WebSocket连接已关闭");
        globalWebSocketInstance = null;
        connectionPromise = null;
      };
    } catch (error) {
      connectionPromise = null;
      reject(error);
    }
  });

  return connectionPromise;
};

export const closeGlobalWebSocket = () => {
  if (globalWebSocketInstance) {
    globalWebSocketInstance.close();
    globalWebSocketInstance = null;
  }
  connectionPromise = null;
};

export const getWebSocketStatus = () => {
  return globalWebSocketInstance?.readyState || WebSocket.CLOSED;
};
