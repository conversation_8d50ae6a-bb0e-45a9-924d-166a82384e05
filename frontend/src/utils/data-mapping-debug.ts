/**
 * 数据映射调试工具
 * 用于验证后端返回的数据是否正确映射到前端UI
 */

interface BackendService {
  name: string;
  status: string;
  responseTime: number;
  lastCheck: string;
  uptime: string;
  details: string;
  connections?: {
    active: number;
    idle: number;
    max: number;
  };
  performance?: {
    queryCount: number;
    slowQueries: number;
    avgResponseTime: number;
  };
  stats?: {
    hitRate: number;
    misses: number;
    operationsPerSec: number;
  };
}

interface BackendDashboardData {
  summary: {
    overall: string;
    score: number;
    uptime: string;
    issues: number;
    criticalIssues: number;
  };
  services: BackendService[];
  metrics: {
    cpu: {
      usage: number;
      cores: number;
      loadAverage: number[];
    };
    memory: {
      total: number;
      used: number;
      free: number;
      available: number;
      usage: number;
    };
    disk: {
      total: number;
      used: number;
      free: number;
      usage: number;
    };
    network: {
      totalRxBytes: number;
      totalTxBytes: number;
      totalRxSpeed: number;
      totalTxSpeed: number;
    };
  };
  alerts: any[];
  lastUpdated: string;
}

/**
 * 调试数据映射
 */
export function debugDataMapping(dashboardData: BackendDashboardData) {
  console.group("🔍 数据映射调试");

  // 1. 检查整体状态
  console.log("📊 整体状态:", {
    overall: dashboardData.summary.overall,
    score: dashboardData.summary.score,
    uptime: dashboardData.summary.uptime,
    issues: dashboardData.summary.issues,
    criticalIssues: dashboardData.summary.criticalIssues,
  });

  // 2. 检查服务列表
  console.log("🔧 服务列表:");
  dashboardData.services.forEach((service, index) => {
    console.log(`  ${index + 1}. ${service.name} - ${service.status}`);
  });

  // 3. 测试服务查找逻辑
  console.log("🔍 服务查找测试:");

  // 数据库服务查找
  const databaseService = dashboardData.services.find(
    s => s.name.includes("MySQL") || s.name.includes("Database")
  );
  console.log(
    "  数据库服务:",
    databaseService
      ? {
          name: databaseService.name,
          status: databaseService.status,
          connections: databaseService.connections?.active || 0,
          queries: databaseService.performance?.queryCount || 0,
        }
      : "未找到"
  );

  // Redis服务查找
  const redisService = dashboardData.services.find(
    s => s.name.includes("Redis") || s.name.includes("Cache")
  );
  console.log(
    "  Redis服务:",
    redisService
      ? {
          name: redisService.name,
          status: redisService.status,
          hitRate: redisService.stats?.hitRate || 0,
          misses: redisService.stats?.misses || 0,
        }
      : "未找到"
  );

  // API服务查找
  const apiService = dashboardData.services.find(
    s => s.name.includes("Node.js") || s.name.includes("Application") || s.name.includes("API")
  );
  console.log(
    "  API服务:",
    apiService
      ? {
          name: apiService.name,
          status: apiService.status,
          responseTime: apiService.responseTime,
        }
      : "未找到"
  );

  // 4. 检查指标数据
  console.log("📈 系统指标:", {
    cpu: `${dashboardData.metrics.cpu.usage}%`,
    memory: `${dashboardData.metrics.memory.usage}%`,
    disk: `${dashboardData.metrics.disk.usage}%`,
    network: `${Math.round(((dashboardData.metrics.network.totalRxSpeed + dashboardData.metrics.network.totalTxSpeed) / 1024 / 1024) * 100)}%`,
  });

  // 5. 检查告警数据
  console.log("🚨 告警数量:", dashboardData.alerts.length);

  console.groupEnd();
}

/**
 * 验证数据映射结果
 */
export function validateDataMapping(dashboardData: BackendDashboardData, mappedData: any) {
  console.group("✅ 数据映射验证");

  const issues: string[] = [];

  // 检查整体状态映射
  if (mappedData.overall !== dashboardData.summary.overall) {
    issues.push(
      `整体状态不匹配: 后端=${dashboardData.summary.overall}, 前端=${mappedData.overall}`
    );
  }

  // 检查服务状态映射
  const databaseService = dashboardData.services.find(
    s => s.name.includes("MySQL") || s.name.includes("Database")
  );
  if (databaseService && mappedData.components.database.status !== databaseService.status) {
    issues.push(
      `数据库状态不匹配: 后端=${databaseService.status}, 前端=${mappedData.components.database.status}`
    );
  }

  const redisService = dashboardData.services.find(
    s => s.name.includes("Redis") || s.name.includes("Cache")
  );
  if (redisService && mappedData.components.redis.status !== redisService.status) {
    issues.push(
      `Redis状态不匹配: 后端=${redisService.status}, 前端=${mappedData.components.redis.status}`
    );
  }

  const apiService = dashboardData.services.find(
    s => s.name.includes("Node.js") || s.name.includes("Application") || s.name.includes("API")
  );
  if (apiService && mappedData.components.api.status !== apiService.status) {
    issues.push(
      `API状态不匹配: 后端=${apiService.status}, 前端=${mappedData.components.api.status}`
    );
  }

  // 检查指标映射
  if (mappedData.metrics.cpu !== dashboardData.metrics.cpu.usage) {
    issues.push(
      `CPU指标不匹配: 后端=${dashboardData.metrics.cpu.usage}%, 前端=${mappedData.metrics.cpu}%`
    );
  }

  if (mappedData.metrics.memory !== dashboardData.metrics.memory.usage) {
    issues.push(
      `内存指标不匹配: 后端=${dashboardData.metrics.memory.usage}%, 前端=${mappedData.metrics.memory}%`
    );
  }

  if (mappedData.metrics.disk !== dashboardData.metrics.disk.usage) {
    issues.push(
      `磁盘指标不匹配: 后端=${dashboardData.metrics.disk.usage}%, 前端=${mappedData.metrics.disk}%`
    );
  }

  // 输出验证结果
  if (issues.length === 0) {
    console.log("✅ 数据映射验证通过");
  } else {
    console.warn("⚠️ 发现数据映射问题:");
    issues.forEach(issue => console.warn(`  - ${issue}`));
  }

  console.groupEnd();
  return issues.length === 0;
}

/**
 * 生成数据映射报告
 */
export function generateMappingReport(dashboardData: BackendDashboardData) {
  return {
    summary: {
      totalServices: dashboardData.services.length,
      healthyServices: dashboardData.services.filter(s => s.status === "healthy").length,
      criticalServices: dashboardData.services.filter(s => s.status === "critical").length,
      downServices: dashboardData.services.filter(s => s.status === "down").length,
    },
    services: dashboardData.services.map(s => ({
      name: s.name,
      status: s.status,
      responseTime: s.responseTime,
      uptime: s.uptime,
    })),
    metrics: {
      cpu: dashboardData.metrics.cpu.usage,
      memory: dashboardData.metrics.memory.usage,
      disk: dashboardData.metrics.disk.usage,
      network: Math.round(
        ((dashboardData.metrics.network.totalRxSpeed + dashboardData.metrics.network.totalTxSpeed) /
          1024 /
          1024) *
          100
      ),
    },
    alerts: dashboardData.alerts.length,
    lastUpdated: dashboardData.lastUpdated,
  };
}
