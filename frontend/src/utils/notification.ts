import { Toast, Modal } from "@douyinfe/semi-ui";

/**
 * 显示成功通知
 */
export const showSuccess = (message: string, title?: string) => {
  Toast.success({
    content: title ? `${title}: ${message}` : message,
    duration: 3,
  });
};

/**
 * 显示错误通知
 */
export const showError = (message: string, title?: string) => {
  Toast.error({
    content: title ? `${title}: ${message}` : message,
    duration: 5,
  });
};

/**
 * 显示警告通知
 */
export const showWarning = (message: string, title?: string) => {
  Toast.warning({
    content: title ? `${title}: ${message}` : message,
    duration: 4,
  });
};

/**
 * 显示信息通知
 */
export const showInfo = (message: string, title?: string) => {
  Toast.info({
    content: title ? `${title}: ${message}` : message,
    duration: 3,
  });
};

/**
 * 显示确认对话框
 */
export const showConfirm = (
  title: string,
  content: string,
  onConfirm: () => void,
  onCancel?: () => void
) => {
  Modal.confirm({
    title,
    content,
    onOk: () => {
      onConfirm();
    },
    onCancel: () => {
      onCancel?.();
    },
    okText: "确认",
    cancelText: "取消",
  });
};
