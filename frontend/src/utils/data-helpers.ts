/**
 * 数据辅助工具函数
 * 提供类型安全的数据提取和转换功能
 */

// 统一的API响应类型
export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
}

// 统一的分页数据结构
export interface PaginationData<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// 统一的分页响应类型
export interface PaginatedResponse<T> extends ApiResponse<PaginationData<T>> {}

// 提取分页数据的类型安全函数
export function extractPaginatedData<T>(response: PaginatedResponse<T> | null | undefined) {
  if (!response?.success || !response?.data) {
    return {
      data: [] as T[],
      pagination: {
        page: 1,
        limit: 10,
        total: 0,
        totalPages: 0
      }
    };
  }

  return {
    data: response.data.data || [],
    pagination: response.data.pagination || {
      page: 1,
      limit: 10,
      total: 0,
      totalPages: 0
    }
  };
}

// 处理嵌套API响应的分页数据提取函数
export function extractNestedPaginatedData<T>(response: { success: boolean; data: PaginatedResponse<T> } | null | undefined) {
  if (!response?.success || !response?.data) {
    return {
      data: [] as T[],
      pagination: {
        page: 1,
        limit: 10,
        total: 0,
        totalPages: 0
      }
    };
  }

  return extractPaginatedData(response.data);
}

// 提取简单数据的类型安全函数
export function extractData<T>(response: ApiResponse<T> | null | undefined): T | null {
  if (!response?.success || !response?.data) {
    return null;
  }
  return response.data;
}

// 检查响应是否成功的类型安全函数
export function isResponseSuccess(response: any): response is { success: true; data: any } {
  return response && typeof response === 'object' && response.success === true;
}

// 类型安全的数组检查
export function isArray<T>(value: unknown): value is T[] {
  return Array.isArray(value);
}

// 类型安全的数字检查
export function isNumber(value: unknown): value is number {
  return typeof value === 'number' && !isNaN(value);
}

// 安全的数字转换
export function safeNumber(value: unknown, defaultValue: number = 0): number {
  if (isNumber(value)) {
    return value;
  }
  if (typeof value === 'string') {
    const parsed = parseFloat(value);
    return isNaN(parsed) ? defaultValue : parsed;
  }
  return defaultValue;
}

// 安全的字符串转换
export function safeString(value: unknown, defaultValue: string = ''): string {
  if (typeof value === 'string') {
    return value;
  }
  if (value != null) {
    return String(value);
  }
  return defaultValue;
}
