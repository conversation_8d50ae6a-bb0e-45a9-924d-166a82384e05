import { get } from "./request";

// API 健康检查
export async function checkApiHealth(): Promise<{
  isHealthy: boolean;
  message: string;
  details?: any;
}> {
  try {
    console.log("🔍 Checking API health...");

    // 尝试访问健康检查端点
    const response = await get("/api/v1/health", undefined, {
      timeout: 5000,
    });

    console.log("✅ API health check response:", response);

    return {
      isHealthy: true,
      message: "API is healthy",
      details: response.data,
    };
  } catch (error: any) {
    console.error("❌ API health check failed:", error);

    return {
      isHealthy: false,
      message: `API health check failed: ${error.message}`,
      details: {
        status: error.status,
        code: error.code,
        stack: error.stack,
      },
    };
  }
}

// 代理测试
export async function testProxy(): Promise<{
  isWorking: boolean;
  message: string;
  details?: any;
}> {
  try {
    console.log("🔄 Testing proxy configuration...");

    // 测试基础 API 调用
    const startTime = Date.now();
    const response = await fetch("/api/v1/health", {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    });

    const responseTime = Date.now() - startTime;
    const data = await response.text();

    console.log(`📊 Proxy test result:`, {
      status: response.status,
      statusText: response.statusText,
      responseTime: `${responseTime}ms`,
      headers: Object.fromEntries(response.headers.entries()),
      data,
    });

    if (response.ok) {
      return {
        isWorking: true,
        message: `Proxy is working correctly (${responseTime}ms)`,
        details: {
          status: response.status,
          responseTime,
          data,
        },
      };
    } else {
      return {
        isWorking: false,
        message: `Proxy returned error: ${response.status} ${response.statusText}`,
        details: {
          status: response.status,
          statusText: response.statusText,
          data,
        },
      };
    }
  } catch (error: any) {
    console.error("🚫 Proxy test failed:", error);

    return {
      isWorking: false,
      message: `Proxy test failed: ${error.message}`,
      details: {
        error: error.message,
        stack: error.stack,
      },
    };
  }
}

// 全面的连接性测试
export async function runConnectivityTest(): Promise<{
  proxy: Awaited<ReturnType<typeof testProxy>>;
  api: Awaited<ReturnType<typeof checkApiHealth>>;
  summary: {
    allWorking: boolean;
    message: string;
  };
}> {
  console.log("🚀 Running connectivity test...");

  const proxy = await testProxy();
  const api = await checkApiHealth();

  const allWorking = proxy.isWorking && api.isHealthy;
  const summary = {
    allWorking,
    message: allWorking
      ? "✅ All systems working correctly"
      : "⚠️ Some issues detected - check proxy and backend server",
  };

  console.log("📋 Connectivity test summary:", summary);

  return {
    proxy,
    api,
    summary,
  };
}
