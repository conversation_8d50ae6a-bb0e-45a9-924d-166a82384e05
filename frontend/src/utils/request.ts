import type { ApiResponse } from "@/types";

// 扩展 RequestInit 以支持 timeout
interface ExtendedRequestInit extends RequestInit {
  timeout?: number;
  isFormData?: boolean;
}

// 获取环境变量
const getApiBaseUrl = (): string => {
  // 开发环境使用代理，baseURL为空（因为service中已经包含/api/v1）
  if (process.env.NODE_ENV === "development") {
    return "";
  }

  // 生产环境使用环境变量或默认值
  if (typeof window !== "undefined") {
    // 客户端环境
    return import.meta.env.VITE_API_BASE_URL || "";
  }

  // 服务端环境 (SSR)
  return process.env.VITE_API_BASE_URL || "";
};

// API 基础配置
const API_CONFIG = {
  baseURL: getApiBaseUrl(),
  timeout: 10000,
};

// 请求错误类
export class ApiError extends Error {
  public status: number;
  public code?: string;
  public data?: any;

  constructor(message: string, status: number, code?: string, data?: any) {
    super(message);
    this.name = "ApiError";
    this.status = status;
    this.code = code;
    this.data = data;
  }
}

// 获取认证 token - 支持实时获取最新token
function getAuthToken(): string | null {
  if (typeof window === "undefined") return null;

  try {
    // 优先从内存中的AuthStore获取最新token
    if (typeof window !== "undefined" && (window as any).__AUTH_STORE__) {
      const token = (window as any).__AUTH_STORE__.getState?.()?.token;
      if (token) return token;
    }

    // 从localStorage获取
    const authStorage = localStorage.getItem("auth-storage");
    if (authStorage) {
      const parsed = JSON.parse(authStorage);
      return parsed.state?.token || null;
    }
  } catch (error) {
    console.warn("Failed to get auth token:", error);
  }

  return null;
}

// 请求拦截器 - 添加认证头
function addAuthHeaders(headers: Record<string, string> = {}): Record<string, string> {
  const token = getAuthToken();

  const defaultHeaders: Record<string, string> = {
    "Content-Type": "application/json",
    ...headers,
  };
  if (token) {
    defaultHeaders["Authorization"] = `Bearer ${token}`;
  }

  return defaultHeaders;
}

// 响应处理器
async function handleResponse<T>(response: Response): Promise<ApiResponse<T>> {
  const contentType = response.headers.get("content-type");

  // 检查是否有新的访问令牌（后端自动刷新）
  const newAccessToken = response.headers.get("X-New-Access-Token");
  const tokenRefreshed = response.headers.get("X-Token-Refreshed");

  // 如果后端返回了新token，自动更新本地token
  if (newAccessToken && tokenRefreshed === "true" && typeof window !== "undefined") {
    try {
      const { useAuthStore } = await import("@/stores/auth");
      await useAuthStore.getState().updateToken(newAccessToken);
    } catch (error) {
      console.error("自动更新token失败:", error);
    }
  }

  let data: any;

  try {
    if (contentType?.includes("application/json")) {
      data = await response.json();
    } else {
      data = await response.text();
    }
  } catch (error) {
    throw new ApiError("Invalid response format", response.status, "INVALID_RESPONSE");
  }

  // 如果响应不成功
  if (!response.ok) {
    const message = data?.message || `HTTP ${response.status}: ${response.statusText}`;
    const apiError = new ApiError(message, response.status, data?.code, data);

    // 将响应对象附加到错误中，用于错误处理
    (apiError as any).response = response;

    throw apiError;
  }

  // 返回标准化的 API 响应格式
  if (typeof data === "object" && data !== null && "success" in data) {
    return data;
  }

  // 如果后端没有返回标准格式，包装一下
  return {
    success: true,
    data,
    message: "Request successful",
  };
}

async function handleAuthError(
  originalError: ApiError,
  response: Response,
  retryRequest: () => Promise<any>
): Promise<any> {
  if (typeof window === "undefined") {
    throw originalError;
  }

  // 检查响应头中是否包含新的访问令牌（后端自动刷新）
  const newAccessToken = response.headers.get("X-New-Access-Token");
  const tokenRefreshed = response.headers.get("X-Token-Refreshed");

  if (originalError.status === 401) {
    if (newAccessToken && tokenRefreshed === "true") {
      try {
        const { useAuthStore } = await import("@/stores/auth");
        await useAuthStore.getState().updateToken(newAccessToken);

        // 不重试请求，直接抛出原始错误，让调用方处理
        throw originalError;
      } catch (error) {
        throw new ApiError(
          "Failed to update token after auto refresh.",
          401,
          "TOKEN_UPDATE_FAILED"
        );
      }
    } else {
      // 没有新token，检查当前token是否存在
      const currentToken = getAuthToken();

      if (!currentToken) {
        // 没有token，需要重新登录

        const { useAuthStore } = await import("@/stores/auth");
        useAuthStore.getState().clearAuthAndRedirect();
        // 不抛出错误，因为已经重定向了
        return Promise.reject(
          new ApiError("No access token found. Please log in.", 401, "NO_TOKEN")
        );
      } else {
        // 有token但被拒绝，可能是权限问题或token格式错误

        // 不自动清除认证状态，让调用方决定如何处理
        throw new ApiError(
          originalError.message || "访问被拒绝，请检查权限或重新登录",
          401,
          "ACCESS_DENIED"
        );
      }
    }
  } else if (originalError.status === 403) {
    if (window.location.pathname !== "/403") {
      window.location.href = "/403";
    }
    throw originalError;
  }

  throw originalError;
}

// 基础请求函数
async function request<T = any>(
  url: string,
  options: ExtendedRequestInit = {}
): Promise<ApiResponse<T>> {
  const { headers = {}, timeout = API_CONFIG.timeout, ...restOptions } = options;

  // 构建完整 URL
  const fullUrl = url.startsWith("http")
    ? url
    : API_CONFIG.baseURL
      ? `${API_CONFIG.baseURL}${url.startsWith("/") ? url : `/${url}`}`
      : url.startsWith("/")
        ? url
        : `/${url}`;

  // 设置请求头
  const requestHeaders = addAuthHeaders(headers as Record<string, string>);
  if (options.isFormData) {
    delete requestHeaders["Content-Type"];
  }

  // 记录请求开始
  const currentToken = getAuthToken();

  // 创建 AbortController 用于超时控制
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), timeout);

  try {
    const response = await fetch(fullUrl, {
      ...restOptions,
      headers: requestHeaders,
      signal: controller.signal,
    });

    clearTimeout(timeoutId);

    // 记录响应状态

    return await handleResponse<T>(response);
  } catch (error: any) {
    clearTimeout(timeoutId);

    if (error.name === "AbortError") {
      throw new ApiError("Request timeout", 408, "TIMEOUT");
    }

    if (error instanceof ApiError) {
      // 处理401和403错误，但不重试
      if (error.status === 401 || error.status === 403) {
        const response = (error as any).response;
        return await handleAuthError(error, response, () => Promise.reject(error));
      }
      // 其他ApiError直接抛出
      throw error;
    }

    // 网络错误或其他错误
    throw new ApiError(error.message || "Network error", 0, "NETWORK_ERROR");
  }
}

// GET 请求
export async function get<T = any>(
  url: string,
  params?: Record<string, any>,
  options?: Omit<ExtendedRequestInit, "method" | "body">
): Promise<ApiResponse<T>> {
  let finalUrl = url;

  if (params) {
    const searchParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        searchParams.append(key, String(value));
      }
    });

    const queryString = searchParams.toString();
    if (queryString) {
      finalUrl += (url.includes("?") ? "&" : "?") + queryString;
    }
  }

  return request<T>(finalUrl, {
    ...options,
    method: "GET",
  });
}

// POST 请求
export async function post<T = any>(
  url: string,
  data?: any,
  options?: Omit<ExtendedRequestInit, "method" | "body">
): Promise<ApiResponse<T>> {
  return request<T>(url, {
    ...options,
    method: "POST",
    body: data ? JSON.stringify(data) : undefined,
  });
}

// PUT 请求
export async function put<T = any>(
  url: string,
  data?: any,
  options?: Omit<ExtendedRequestInit, "method" | "body">
): Promise<ApiResponse<T>> {
  return request<T>(url, {
    ...options,
    method: "PUT",
    body: data ? JSON.stringify(data) : undefined,
  });
}

// PATCH 请求
export async function patch<T = any>(
  url: string,
  data?: any,
  options?: Omit<ExtendedRequestInit, "method" | "body">
): Promise<ApiResponse<T>> {
  return request<T>(url, {
    ...options,
    method: "PATCH",
    body: data ? JSON.stringify(data) : undefined,
  });
}

// DELETE 请求
export async function del<T = any>(
  url: string,
  options?: Omit<ExtendedRequestInit, "method" | "body">
): Promise<ApiResponse<T>> {
  return request<T>(url, {
    ...options,
    method: "DELETE",
  });
}

// 文件上传
export async function upload<T = any>(
  url: string,
  file: File,
  fieldName = "file",
  additionalData?: Record<string, string>,
  options?: Omit<ExtendedRequestInit, "method" | "body" | "headers">
): Promise<ApiResponse<T>> {
  const formData = new FormData();
  formData.append(fieldName, file);

  if (additionalData) {
    Object.entries(additionalData).forEach(([key, value]) => {
      formData.append(key, value);
    });
  }

  // 注意：上传文件时不设置 Content-Type，让浏览器自动设置
  const headers: Record<string, string> = {};

  return request<T>(url, {
    ...options,
    method: "POST",
    body: formData,
    isFormData: true,
  });
}

// 导出默认对象
export default {
  get,
  post,
  put,
  patch,
  delete: del,
  upload,
  request,
};
