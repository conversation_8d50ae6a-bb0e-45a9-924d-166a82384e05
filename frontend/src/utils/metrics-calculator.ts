/**
 * 系统指标计算工具
 * 用于准确计算CPU、内存、磁盘、网络等系统指标
 */

interface SystemMetrics {
  cpu: {
    usage: number;
    cores: number;
    loadAverage: number[];
  };
  memory: {
    total: number;
    used: number;
    free: number;
    available: number;
    usage: number;
  };
  disk: {
    total: number;
    used: number;
    free: number;
    usage: number;
  };
  network: {
    totalRxBytes: number;
    totalTxBytes: number;
    totalRxSpeed: number;
    totalTxSpeed: number;
  };
}

/**
 * 计算网络使用率
 */
export function calculateNetworkUsage(network: SystemMetrics["network"]): number {
  const totalSpeed = network.totalRxSpeed + network.totalTxSpeed;

  // 如果有实时速度数据，直接计算
  if (totalSpeed > 0) {
    // 假设1Gbps带宽，计算使用率
    const bandwidthBytesPerSecond = (1024 * 1024 * 1024) / 8; // 1Gbps in bytes/s
    const usagePercentage = (totalSpeed / bandwidthBytesPerSecond) * 100;
    return Math.min(Math.round(usagePercentage), 100);
  }

  // 如果没有实时速度，基于总流量计算
  const totalBytes = network.totalRxBytes + network.totalTxBytes;

  // 假设系统运行时间，计算平均使用率
  // 这里使用一个合理的默认值，实际应该从系统启动时间计算
  const estimatedRuntimeSeconds = 3600; // 假设运行1小时
  const averageSpeed = totalBytes / estimatedRuntimeSeconds;
  const bandwidthBytesPerSecond = (1024 * 1024 * 1024) / 8; // 1Gbps
  const usagePercentage = (averageSpeed / bandwidthBytesPerSecond) * 100;

  return Math.min(Math.round(usagePercentage), 100);
}

/**
 * 验证内存指标
 */
export function validateMemoryMetrics(memory: SystemMetrics["memory"]): {
  isValid: boolean;
  issues: string[];
  correctedUsage?: number;
} {
  const issues: string[] = [];

  // 检查基本逻辑
  if (memory.used + memory.free !== memory.total) {
    issues.push("内存使用量 + 空闲量 ≠ 总内存量");
  }

  if (memory.available > memory.total) {
    issues.push("可用内存大于总内存");
  }

  if (memory.usage < 0 || memory.usage > 100) {
    issues.push("内存使用率超出合理范围(0-100%)");
  }

  // 计算正确的使用率
  let correctedUsage = memory.usage;
  if (memory.total > 0) {
    const calculatedUsage = (memory.used / memory.total) * 100;
    if (Math.abs(calculatedUsage - memory.usage) > 5) {
      issues.push("内存使用率计算不准确");
      correctedUsage = Math.round(calculatedUsage);
    }
  }

  return {
    isValid: issues.length === 0,
    issues,
    correctedUsage: issues.length > 0 ? correctedUsage : undefined,
  };
}

/**
 * 验证磁盘指标
 */
export function validateDiskMetrics(disk: SystemMetrics["disk"]): {
  isValid: boolean;
  issues: string[];
  correctedUsage?: number;
} {
  const issues: string[] = [];

  // 检查基本逻辑
  if (disk.used + disk.free !== disk.total) {
    issues.push("磁盘使用量 + 空闲量 ≠ 总磁盘量");
  }

  if (disk.usage < 0 || disk.usage > 100) {
    issues.push("磁盘使用率超出合理范围(0-100%)");
  }

  // 计算正确的使用率
  let correctedUsage = disk.usage;
  if (disk.total > 0) {
    const calculatedUsage = (disk.used / disk.total) * 100;
    if (Math.abs(calculatedUsage - disk.usage) > 5) {
      issues.push("磁盘使用率计算不准确");
      correctedUsage = Math.round(calculatedUsage);
    }
  }

  return {
    isValid: issues.length === 0,
    issues,
    correctedUsage: issues.length > 0 ? correctedUsage : undefined,
  };
}

/**
 * 验证CPU指标
 */
export function validateCpuMetrics(cpu: SystemMetrics["cpu"]): {
  isValid: boolean;
  issues: string[];
  correctedUsage?: number;
} {
  const issues: string[] = [];

  if (cpu.usage < 0 || cpu.usage > 100) {
    issues.push("CPU使用率超出合理范围(0-100%)");
  }

  if (cpu.cores <= 0) {
    issues.push("CPU核心数无效");
  }

  // 检查负载平均值
  if (cpu.loadAverage.some(load => load < 0)) {
    issues.push("CPU负载平均值包含负数");
  }

  return {
    isValid: issues.length === 0,
    issues,
    correctedUsage: issues.length > 0 ? Math.max(0, Math.min(100, cpu.usage)) : undefined,
  };
}

/**
 * 计算并验证所有系统指标
 */
export function calculateAndValidateMetrics(metrics: SystemMetrics): {
  cpu: number;
  memory: number;
  disk: number;
  network: number;
  validation: {
    cpu: { isValid: boolean; issues: string[] };
    memory: { isValid: boolean; issues: string[] };
    disk: { isValid: boolean; issues: string[] };
    network: { isValid: boolean; issues: string[] };
  };
} {
  // 验证各项指标
  const cpuValidation = validateCpuMetrics(metrics.cpu);
  const memoryValidation = validateMemoryMetrics(metrics.memory);
  const diskValidation = validateDiskMetrics(metrics.disk);

  // 计算网络使用率
  const networkUsage = calculateNetworkUsage(metrics.network);
  const networkValidation = {
    isValid: networkUsage >= 0 && networkUsage <= 100,
    issues: networkUsage < 0 || networkUsage > 100 ? ["网络使用率超出合理范围"] : [],
  };

  // 输出验证结果（开发环境）
  if (process.env.NODE_ENV === "development") {
    console.group("🔍 指标验证结果");

    if (!cpuValidation.isValid) {
      console.warn("CPU指标问题:", cpuValidation.issues);
    }
    if (!memoryValidation.isValid) {
      console.warn("内存指标问题:", memoryValidation.issues);
    }
    if (!diskValidation.isValid) {
      console.warn("磁盘指标问题:", diskValidation.issues);
    }
    if (!networkValidation.isValid) {
      console.warn("网络指标问题:", networkValidation.issues);
    }

    console.groupEnd();
  }

  return {
    cpu: cpuValidation.correctedUsage ?? metrics.cpu.usage,
    memory: memoryValidation.correctedUsage ?? metrics.memory.usage,
    disk: diskValidation.correctedUsage ?? metrics.disk.usage,
    network: networkUsage,
    validation: {
      cpu: { isValid: cpuValidation.isValid, issues: cpuValidation.issues },
      memory: { isValid: memoryValidation.isValid, issues: memoryValidation.issues },
      disk: { isValid: diskValidation.isValid, issues: diskValidation.issues },
      network: networkValidation,
    },
  };
}

/**
 * 格式化指标显示
 */
export function formatMetricValue(value: number, unit: string = "%"): string {
  if (unit === "%") {
    return `${Math.round(value)}%`;
  }

  if (unit === "GB") {
    return `${(value / 1024 / 1024 / 1024).toFixed(2)}GB`;
  }

  if (unit === "MB") {
    return `${(value / 1024 / 1024).toFixed(2)}MB`;
  }

  return `${value}${unit}`;
}

/**
 * 获取指标颜色
 */
export function getMetricColor(
  value: number,
  thresholds: { warning: number; critical: number } = { warning: 70, critical: 90 }
): string {
  if (value >= thresholds.critical) {
    return "red";
  }
  if (value >= thresholds.warning) {
    return "orange";
  }
  return "green";
}
