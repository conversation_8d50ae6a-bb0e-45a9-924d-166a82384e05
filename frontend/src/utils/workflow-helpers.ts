/**
 * 工作流辅助函数
 * 提供工作流相关的工具方法
 */

import {
  WorkflowDefinition,
  WorkflowConfig,
  WorkflowStep,
  DesignerNode,
  ValidationResult,
  WorkflowStepType,
  TriggerType,
  WorkflowCategory,
  NodePosition
} from '../types/workflow';

// ========== ID生成器 ==========

/**
 * 生成唯一的节点ID
 */
export const generateNodeId = (): string => {
  return `node_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};

/**
 * 生成唯一的连接ID
 */
export const generateConnectionId = (): string => {
  return `conn_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};

/**
 * 生成唯一的工作流ID
 */
export const generateWorkflowId = (): string => {
  return `workflow_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};

// ========== 工作流创建 ==========

/**
 * 创建空的工作流定义
 */
export const createEmptyWorkflow = (): WorkflowDefinition => {
  return {
    id: '',
    name: '新建工作流',
    description: '',
    category: 'CUSTOM',
    priority: 'MEDIUM',
    version: '1.0.0',
    isActive: false,
    steps: [],
    variables: {},
    settings: {
      timeout: 300000, // 5分钟
      maxRetries: 3,
      errorHandling: 'stop',
      notifications: [],
      concurrency: {
        enabled: false,
        maxParallel: 1
      },
      logging: {
        level: 'INFO',
        retentionDays: 30
      }
    },
    tags: [],
    isTemplate: false,
    createdBy: '',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };
};

/**
 * 创建默认的工作流步骤
 */
export const createDefaultStep = (type: WorkflowStepType, index: number): WorkflowStep => {
  const baseStep: WorkflowStep = {
    index,
    name: getStepTypeName(type),
    type,
    config: {},
    timeout: 30000, // 30秒默认超时
    retry: {
      maxAttempts: 3,
      delay: 1000
    }
  };

  // 根据类型设置默认配置
  switch (type) {
    case 'ACTION':
      baseStep.config = {
        actionType: 'custom',
        parameters: {}
      };
      break;
      
    case 'CONDITION':
      baseStep.config = {
        expression: 'true',
        trueStep: index + 1,
        falseStep: index + 2
      };
      break;
      
    case 'NOTIFICATION':
      baseStep.config = {
        type: 'email',
        recipients: [],
        subject: '工作流通知',
        content: '工作流执行通知'
      };
      break;
      
    case 'HTTP_REQUEST':
      baseStep.config = {
        method: 'GET',
        url: '',
        headers: {},
        body: {},
        timeout: 10000
      };
      break;
      
    case 'DATABASE_OPERATION':
      baseStep.config = {
        operation: 'SELECT',
        connection: '',
        query: '',
        parameters: {}
      };
      break;
      
    case 'SCRIPT':
      baseStep.config = {
        language: 'javascript',
        code: '// 在此编写脚本代码\nreturn { success: true };',
        timeout: 30000
      };
      break;
      
    case 'DELAY':
      baseStep.config = {
        duration: 1000, // 1秒
        unit: 'milliseconds'
      };
      break;
      
    case 'APPROVAL':
      baseStep.config = {
        approvers: [],
        timeout: 86400000, // 24小时
        requireAllApproval: false,
        autoReject: false
      };
      break;
      
    case 'PARALLEL':
      baseStep.config = {
        branches: [],
        waitForAll: true,
        timeout: 300000
      };
      break;
      
    case 'LOOP':
      baseStep.config = {
        condition: 'true',
        maxIterations: 10,
        steps: []
      };
      break;
      
    case 'SUBPROCESS':
      baseStep.config = {
        workflowId: '',
        context: {},
        waitForCompletion: true
      };
      break;
      
    case 'FILE_OPERATION':
      baseStep.config = {
        operation: 'read',
        path: '',
        encoding: 'utf8'
      };
      break;
      
    default:
      baseStep.config = {};
  }

  return baseStep;
};

/**
 * 创建默认的设计器节点
 */
export const createDefaultNode = (type: WorkflowStepType, position: NodePosition): DesignerNode => {
  const step = createDefaultStep(type, 0);
  
  return {
    id: generateNodeId(),
    type,
    position,
    data: {
      step,
      isSelected: false,
      isHighlighted: false,
      validation: { isValid: true, errors: [] }
    },
    ports: getNodePorts(type)
  };
};

// ========== 验证功能 ==========

/**
 * 验证工作流定义
 */
export const validateWorkflow = (workflow: WorkflowDefinition): ValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];
  
  // 基础验证
  if (!workflow.name.trim()) {
    errors.push('工作流名称不能为空');
  }
  
  if (workflow.name.length > 100) {
    errors.push('工作流名称不能超过100个字符');
  }
  
  if (!workflow.category) {
    errors.push('必须选择工作流类别');
  }
  
  // 步骤验证
  if (workflow.steps.length === 0) {
    warnings.push('工作流没有任何步骤');
  } else {
    workflow.steps.forEach((step, index) => {
      const stepValidation = validateStep(step, index);
      errors.push(...stepValidation.errors);
      warnings.push(...stepValidation.warnings);
    });
  }
  
  // 连接关系验证
  const connectivityValidation = validateConnectivity(workflow.steps);
  errors.push(...connectivityValidation.errors);
  warnings.push(...connectivityValidation.warnings);
  
  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
};

/**
 * 验证触发器配置
 */
export const validateTrigger = (trigger: any): ValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];
  
  if (!trigger.type) {
    errors.push('必须设置触发器类型');
    return { isValid: false, errors, warnings };
  }
  
  switch (trigger.type) {
    case 'SCHEDULED':
      if (!trigger.config.scheduled?.cronExpression) {
        errors.push('定时触发器必须设置Cron表达式');
      } else if (!isValidCronExpression(trigger.config.scheduled.cronExpression)) {
        errors.push('Cron表达式格式无效');
      }
      break;
      
    case 'EVENT':
      if (!trigger.config.event?.eventType) {
        errors.push('事件触发器必须设置事件类型');
      }
      break;
      
    case 'WEBHOOK':
      if (!trigger.config.webhook?.endpoint) {
        errors.push('Webhook触发器必须设置端点路径');
      }
      if (!trigger.config.webhook?.method) {
        errors.push('Webhook触发器必须设置HTTP方法');
      }
      break;
      
    case 'CONDITION':
      if (!trigger.config.condition?.conditions?.length) {
        errors.push('条件触发器必须设置至少一个条件');
      }
      break;
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
};

/**
 * 验证工作流步骤
 */
export const validateStep = (step: WorkflowStep, index: number): ValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];
  const prefix = `步骤 ${index + 1} (${step.name})`;
  
  // 基础验证
  if (!step.name.trim()) {
    errors.push(`${prefix}: 步骤名称不能为空`);
  }
  
  if (!step.type) {
    errors.push(`${prefix}: 必须设置步骤类型`);
  }
  
  if (step.timeout && step.timeout <= 0) {
    errors.push(`${prefix}: 超时时间必须大于0`);
  }
  
  // 类型特定验证
  switch (step.type) {
    case 'HTTP_REQUEST':
      if (!step.config.url) {
        errors.push(`${prefix}: HTTP请求步骤必须设置URL`);
      } else if (!isValidUrl(step.config.url)) {
        errors.push(`${prefix}: URL格式无效`);
      }
      break;
      
    case 'DATABASE_OPERATION':
      if (!step.config.query) {
        errors.push(`${prefix}: 数据库操作步骤必须设置查询语句`);
      }
      if (!step.config.connection) {
        errors.push(`${prefix}: 数据库操作步骤必须设置连接配置`);
      }
      break;
      
    case 'NOTIFICATION':
      if (!step.config.recipients?.length) {
        warnings.push(`${prefix}: 通知步骤没有设置接收者`);
      }
      break;
      
    case 'CONDITION':
      if (!step.config.expression) {
        errors.push(`${prefix}: 条件步骤必须设置判断表达式`);
      }
      break;
      
    case 'SCRIPT':
      if (!step.config.code) {
        errors.push(`${prefix}: 脚本步骤必须包含代码`);
      }
      break;
      
    case 'APPROVAL':
      if (!step.config.approvers?.length) {
        errors.push(`${prefix}: 审批步骤必须设置审批人`);
      }
      break;
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
};

/**
 * 验证步骤连接关系
 */
export const validateConnectivity = (steps: WorkflowStep[]): ValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];
  
  if (steps.length === 0) {
    return { isValid: true, errors, warnings };
  }
  
  // 检查是否有孤立的步骤
  const connectedSteps = new Set<number>();
  connectedSteps.add(0); // 第一个步骤总是连接的
  
  steps.forEach((step, index) => {
    if (step.onSuccess?.nextStep !== undefined) {
      connectedSteps.add(step.onSuccess.nextStep);
    }
    if (step.onFailure?.nextStep !== undefined) {
      connectedSteps.add(step.onFailure.nextStep);
    }
  });
  
  steps.forEach((step, index) => {
    if (index > 0 && !connectedSteps.has(index)) {
      warnings.push(`步骤 ${index + 1} (${step.name}) 没有被其他步骤引用，可能不会被执行`);
    }
  });
  
  // 检查循环引用
  const visited = new Set<number>();
  const visiting = new Set<number>();
  
  const hasCycle = (stepIndex: number): boolean => {
    if (visiting.has(stepIndex)) return true;
    if (visited.has(stepIndex)) return false;
    
    visiting.add(stepIndex);
    
    const step = steps[stepIndex];
    if (step.onSuccess?.nextStep !== undefined) {
      if (hasCycle(step.onSuccess.nextStep)) return true;
    }
    if (step.onFailure?.nextStep !== undefined) {
      if (hasCycle(step.onFailure.nextStep)) return true;
    }
    
    visiting.delete(stepIndex);
    visited.add(stepIndex);
    return false;
  };
  
  if (hasCycle(0)) {
    errors.push('检测到循环引用，可能导致无限循环');
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
};

// ========== 工具函数 ==========

/**
 * 获取步骤类型的中文名称
 */
export const getStepTypeName = (type: WorkflowStepType): string => {
  const typeNames: Record<WorkflowStepType, string> = {
    ACTION: '执行动作',
    CONDITION: '条件判断',
    APPROVAL: '人工审批',
    NOTIFICATION: '发送通知',
    DELAY: '延迟等待',
    PARALLEL: '并行执行',
    LOOP: '循环执行',
    SUBPROCESS: '子流程',
    SCRIPT: '脚本执行',
    HTTP_REQUEST: 'HTTP请求',
    DATABASE_OPERATION: '数据库操作',
    FILE_OPERATION: '文件操作'
  };
  
  return typeNames[type] || type;
};

/**
 * 获取触发器类型的中文名称
 */
export const getTriggerTypeName = (type: TriggerType): string => {
  const typeNames: Record<TriggerType, string> = {
    MANUAL: '手动触发',
    SCHEDULED: '定时触发',
    EVENT: '事件触发',
    WEBHOOK: 'Webhook触发',
    API: 'API触发',
    CONDITION: '条件触发'
  };
  
  return typeNames[type] || type;
};

/**
 * 获取工作流类别的中文名称
 */
export const getCategoryName = (category: WorkflowCategory): string => {
  const categoryNames: Record<WorkflowCategory, string> = {
    SERVICE_AUTOMATION: '服务自动化',
    SLA_MONITORING: 'SLA监控',
    ALERT_PROCESSING: '告警处理',
    BACKUP_AUTOMATION: '备份自动化',
    MAINTENANCE: '维护任务',
    APPROVAL_PROCESS: '审批流程',
    NOTIFICATION: '通知流程',
    DATA_PROCESSING: '数据处理',
    INTEGRATION: '集成流程',
    CUSTOM: '自定义'
  };
  
  return categoryNames[category] || category;
};

/**
 * 获取节点端口配置
 */
export const getNodePorts = (type: WorkflowStepType) => {
  const basePorts = {
    input: [{ id: 'input', label: '输入', type: 'default' as const }],
    output: [{ id: 'output', label: '输出', type: 'default' as const }]
  };
  
  switch (type) {
    case 'CONDITION':
      return {
        input: basePorts.input,
        output: [
          { id: 'true', label: '是', type: 'condition' as const },
          { id: 'false', label: '否', type: 'condition' as const }
        ]
      };
      
    case 'PARALLEL':
      return {
        input: basePorts.input,
        output: [
          { id: 'branch1', label: '分支1', type: 'default' as const },
          { id: 'branch2', label: '分支2', type: 'default' as const },
          { id: 'merge', label: '合并', type: 'default' as const }
        ]
      };
      
    default:
      return {
        ...basePorts,
        output: [
          ...basePorts.output,
          { id: 'error', label: '错误', type: 'error' as const }
        ]
      };
  }
};

/**
 * 计算节点的自动布局位置
 */
export const calculateAutoLayout = (nodes: DesignerNode[]): DesignerNode[] => {
  // 简单的网格布局算法
  const gridSize = 200;
  const cols = Math.ceil(Math.sqrt(nodes.length));
  
  return nodes.map((node, index) => ({
    ...node,
    position: {
      x: (index % cols) * gridSize + 100,
      y: Math.floor(index / cols) * gridSize + 100
    }
  }));
};

// ========== 验证辅助函数 ==========

/**
 * 验证Cron表达式
 */
const isValidCronExpression = (expr: string): boolean => {
  // 简单的Cron表达式验证
  const parts = expr.trim().split(/\s+/);
  return parts.length === 5 || parts.length === 6;
};

/**
 * 验证URL格式
 */
const isValidUrl = (url: string): boolean => {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
};

/**
 * 深度克隆对象
 */
export const deepClone = <T>(obj: T): T => {
  return JSON.parse(JSON.stringify(obj));
};

/**
 * 生成步骤执行顺序
 */
export const generateExecutionOrder = (steps: WorkflowStep[]): number[] => {
  const order: number[] = [];
  const visited = new Set<number>();
  
  const visit = (index: number) => {
    if (visited.has(index) || index >= steps.length) return;
    
    visited.add(index);
    order.push(index);
    
    const step = steps[index];
    if (step.onSuccess?.nextStep !== undefined) {
      visit(step.onSuccess.nextStep);
    }
  };
  
  if (steps.length > 0) {
    visit(0);
  }
  
  return order;
};