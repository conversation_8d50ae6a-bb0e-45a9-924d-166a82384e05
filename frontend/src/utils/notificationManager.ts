import React from "react";
import { Notification } from "@douyinfe/semi-ui";

// 获取认证令牌
const getAuthToken = (): string | null => {
  if (typeof window === "undefined") return null;

  try {
    // 优先从内存中的AuthStore获取最新token
    if (typeof window !== "undefined" && (window as any).__AUTH_STORE__) {
      const token = (window as any).__AUTH_STORE__.getState?.()?.token;
      if (token) return token;
    }

    // 从localStorage获取
    const authStorage = localStorage.getItem("auth-storage");
    if (authStorage) {
      const parsed = JSON.parse(authStorage);
      return parsed.state?.token || null;
    }
  } catch (error) {
    console.warn("Failed to get auth token:", error);
  }

  return null;
};

export interface NotificationData {
  id: string;
  type: "info" | "warning" | "error" | "success";
  title: string;
  message: string;
  actionUrl?: string;
  persistent?: boolean;
}

class NotificationManager {
  private static instance: NotificationManager;
  private processedNotifications = new Set<string>();
  private isInitialized = false;

  public static getInstance(): NotificationManager {
    if (!NotificationManager.instance) {
      NotificationManager.instance = new NotificationManager();
    }
    return NotificationManager.instance;
  }

  public initializeWebSocketListeners(webSocket: any): void {
    if (this.isInitialized) {
      return; // 防止重复初始化
    }

    console.log("🎯 初始化全局通知监听器");
    this.isInitialized = true;

    const handleNotificationMessage = (message: any) => {
      // 支持多种通知消息类型
      if (message.type === "notification_new" && message.data) {
        const notificationId = message.data.id || message.id || Date.now().toString();

        // 防止重复处理同一通知
        if (this.processedNotifications.has(notificationId)) {
          return;
        }

        this.processedNotifications.add(notificationId);

        const notification: NotificationData = {
          id: notificationId,
          type: message.data.type || "info",
          title: message.data.title || "通知",
          message: message.data.message || "",
          actionUrl: message.data.actionUrl,
          persistent: message.data.persistent || false,
        };

        // 使用通知管理器显示通知
        this.showNotification(notification);
      } else if (message.channel === "notification" || message.type === "notification") {
        // 兼容旧的通知格式
        const notificationId = message.id || Date.now().toString();

        if (this.processedNotifications.has(notificationId)) {
          return;
        }

        this.processedNotifications.add(notificationId);

        const notification: NotificationData = {
          id: notificationId,
          type: message.data?.type || "info",
          title: message.data?.title || message.title || "通知",
          message: message.data?.message || message.message || "",
          actionUrl: message.data?.actionUrl || message.actionUrl,
          persistent: message.data?.persistent || message.persistent || false,
        };

        this.showNotification(notification);
      }
    };

    // 监听通知频道的消息
    webSocket.onNotification(handleNotificationMessage);

    // 也监听user频道，因为通知消息实际发在user频道
    webSocket.onOnlineUsers((message: any) => {
      if (message.type === "notification_new") {
        handleNotificationMessage(message);
      }
    });
  }

  public showNotification(notification: NotificationData): void {
    const notificationConfig: any = {
      title: notification.title,
      content: notification.message,
      duration: notification.persistent ? 0 : 6000,
      showClose: true,
      position: "bottomRight",
    };

    if (notification.actionUrl) {
      const handleClick = async () => {
        if (notification.actionUrl!.includes("/download")) {
          // 使用带认证的请求下载文件
          try {
            const response = await fetch(notification.actionUrl!, {
              method: "GET",
              credentials: "include", // 包含 Cookie
              headers: {
                Authorization: `Bearer ${getAuthToken()}`,
                "Content-Type": "application/json",
              },
            });

            if (response.ok) {
              const blob = await response.blob();
              const url = window.URL.createObjectURL(blob);
              const a = document.createElement("a");
              a.href = url;
              a.download = `report-${Date.now()}.pdf`;
              document.body.appendChild(a);
              a.click();
              window.URL.revokeObjectURL(url);
              document.body.removeChild(a);
            } else {
              console.error("下载失败:", response.status, response.statusText);
              // 如果认证失败，尝试直接打开链接
              window.open(notification.actionUrl!, "_blank");
            }
          } catch (error) {
            console.error("下载错误:", error);
            // 如果出错，尝试直接打开链接
            window.open(notification.actionUrl!, "_blank");
          }
        } else {
          window.location.href = notification.actionUrl!;
        }
      };

      notificationConfig.content = React.createElement(
        "div",
        null,
        React.createElement("div", { style: { marginBottom: "8px" } }, notification.message),
        React.createElement(
          "button",
          {
            style: {
              color: "var(--semi-color-primary)",
              background: "none",
              border: "1px solid var(--semi-color-primary)",
              borderRadius: "4px",
              padding: "4px 12px",
              cursor: "pointer",
              fontSize: "12px",
            },
            onClick: handleClick,
          },
          "点击下载"
        )
      );
    }

    switch (notification.type) {
      case "success":
        Notification.success(notificationConfig);
        break;
      case "error":
        Notification.error(notificationConfig);
        break;
      case "warning":
        Notification.warning(notificationConfig);
        break;
      case "info":
      default:
        Notification.info(notificationConfig);
        break;
    }
  }
}

export const notificationManager = NotificationManager.getInstance();
