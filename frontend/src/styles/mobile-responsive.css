/* 移动端响应式优化样式 - 现代化增强版 */

/* 新增现代化响应式样式 */
@media (max-width: 768px) {
  /* 新设计系统组件适配 */
  .ops-page-header {
    @apply px-4 py-6;
  }

  .ops-page-title {
    @apply text-2xl;
  }

  .ops-page-subtitle {
    @apply text-sm leading-relaxed;
  }

  .ops-page-actions {
    @apply flex-col w-full;
  }

  .ops-stats-card {
    @apply p-4;
  }

  .ops-stats-card-value {
    @apply text-xl;
  }

  .ops-stats-card-icon {
    @apply w-12 h-12 top-4 right-4;
  }

  .ops-card {
    @apply mx-2 shadow-sm;
  }

  .ops-card-header,
  .ops-card-body,
  .ops-card-footer {
    @apply px-4;
  }

  .ops-search-toolbar {
    @apply p-4;
  }

  .ops-search-row {
    @apply flex-col space-y-3;
  }

  .ops-search-row > * {
    @apply w-full;
  }

  .ops-batch-actions {
    @apply flex-col space-y-3 p-4;
  }

  .ops-batch-controls {
    @apply w-full;
  }

  .ops-batch-controls .semi-button {
    @apply w-full;
  }

  .ops-component-status {
    @apply flex-col items-start p-4;
  }

  .ops-component-info {
    @apply w-full mb-3;
  }

  .ops-component-metrics {
    @apply w-full text-left;
  }

  .ops-performance-grid {
    @apply grid-cols-1 gap-3;
  }

  .ops-alert-header {
    @apply flex-col space-y-2 items-start;
  }

  .ops-realtime-status {
    @apply text-sm px-3 py-2;
  }

  /* 表格移动端优化 */
  .ops-table-container .semi-table {
    @apply text-sm;
  }

  .ops-table-container .semi-table-thead {
    @apply hidden;
  }

  .ops-table-container .semi-table-tbody-tr {
    @apply block border-0 border-b pb-4 mb-4;
  }

  .ops-table-container .semi-table-tbody-td {
    @apply block py-1 border-0;
  }

  .ops-table-container .semi-table-tbody-td:before {
    content: attr(data-label) ": ";
    @apply font-medium text-gray-600 mr-2 inline-block w-20 text-sm;
  }
}

/* 全局移动端优化 */
@media (max-width: 768px) {
  /* 页面头部适配 */
  .page-header {
    @apply px-4 py-3;
  }

  .page-title {
    @apply text-xl;
  }

  .page-subtitle {
    @apply text-sm;
  }

  /* 卡片容器适配 */
  .card {
    @apply mx-2 rounded-lg;
  }

  /* 统计卡片栅格适配 */
  .stat-card {
    @apply p-3;
  }

  .stat-card .text-2xl {
    @apply text-xl;
  }

  /* 表格适配 */
  .semi-table-wrapper {
    @apply overflow-x-auto;
  }

  .semi-table {
    min-width: 800px;
  }

  /* 搜索栏适配 */
  .search-toolbar {
    @apply flex-col space-y-3;
  }

  .search-toolbar > .semi-input-wrapper {
    @apply w-full;
  }

  .search-toolbar > .semi-select {
    @apply w-full;
  }

  /* 操作按钮组适配 */
  .action-buttons {
    @apply flex-col space-y-2 w-full;
  }

  .action-buttons > .semi-button {
    @apply w-full justify-center;
  }

  /* 批量操作栏适配 */
  .batch-actions {
    @apply flex-col space-y-3 p-3;
  }

  .batch-actions .semi-space {
    @apply w-full;
  }

  .batch-actions .semi-button {
    @apply flex-1;
  }

  /* 弹窗适配 */
  .semi-modal {
    @apply m-4 w-auto max-w-none;
  }

  .semi-modal-content {
    @apply max-h-screen overflow-y-auto;
  }

  /* 表单适配 */
  .form-grid {
    @apply grid-cols-1;
  }

  /* 详情页适配 */
  .detail-content {
    @apply px-4;
  }

  .detail-sections {
    @apply space-y-4;
  }

  /* 标签页适配 */
  .semi-tabs-bar {
    @apply overflow-x-auto;
  }

  .semi-tabs-tab {
    @apply whitespace-nowrap;
  }

  /* 下拉菜单适配 */
  .semi-dropdown-menu {
    @apply max-w-xs;
  }

  /* 时间线适配 */
  .semi-timeline-item-content {
    @apply text-sm;
  }

  /* 进度条适配 */
  .progress-wrapper {
    @apply w-full;
  }

  /* 图表容器适配 */
  .chart-container {
    @apply h-64 w-full;
  }
}

/* 超小屏幕设备优化 */
@media (max-width: 480px) {
  /* 新设计系统超小屏适配 */
  .ops-page-header {
    @apply px-2 py-4;
  }

  .ops-page-title {
    @apply text-xl;
  }

  .ops-stats-card {
    @apply p-3;
  }

  .ops-stats-card-value {
    @apply text-lg;
  }

  .ops-stats-card-icon {
    @apply w-10 h-10;
  }

  .ops-card-header,
  .ops-card-body,
  .ops-card-footer {
    @apply px-3;
  }

  .ops-search-toolbar {
    @apply p-3;
  }

  .ops-batch-actions {
    @apply p-3;
  }

  .ops-component-status {
    @apply p-3;
  }

  .ops-performance-card {
    @apply p-3;
  }

  .ops-alert-card {
    @apply p-3;
  }

  .ops-metric-progress {
    @apply mb-4;
  }
  /* 导航栏适配 */
  .semi-nav {
    @apply w-full;
  }

  /* 页面布局适配 */
  .main-layout {
    @apply flex-col;
  }

  .content-area {
    @apply p-2;
  }

  /* 卡片标题适配 */
  .semi-card-header-wrapper {
    @apply pb-2;
  }

  .semi-card-header-wrapper .semi-card-header-title {
    @apply text-base;
  }

  /* 按钮适配 */
  .semi-button-group .semi-button {
    @apply px-2 text-sm;
  }

  /* 分页适配 */
  .semi-page {
    @apply justify-center;
  }

  .semi-page-item {
    @apply min-w-8 h-8;
  }

  /* 描述列表适配 */
  .semi-descriptions {
    @apply text-sm;
  }

  .semi-descriptions-item-key {
    @apply w-20;
  }

  /* 头像适配 */
  .semi-avatar-large {
    @apply w-10 h-10 text-sm;
  }

  /* 标签适配 */
  .semi-tag {
    @apply text-xs px-1;
  }

  /* 空状态适配 */
  .semi-empty {
    @apply py-8;
  }

  .semi-empty-image {
    @apply w-16 h-16;
  }

  .semi-empty-description {
    @apply text-sm;
  }
}

/* 触摸设备优化 */
@media (pointer: coarse) {
  /* 增加点击区域 */
  .semi-button {
    @apply min-h-11;
  }

  .semi-input {
    @apply min-h-11;
  }

  .semi-select-selection {
    @apply min-h-11;
  }

  .semi-table-tbody-tr {
    @apply min-h-12;
  }

  /* 增加间距 */
  .clickable-item {
    @apply py-3;
  }

  /* 优化滚动体验 */
  .scrollable-content {
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
  }
}

/* 系统管理页面专用移动端优化 */
@media (max-width: 768px) {
  /* 系统管理概览页 */
  .admin-dashboard {
    @apply space-y-4;
  }

  .admin-stats-grid {
    @apply grid-cols-2 gap-3;
  }

  .admin-quick-actions {
    @apply grid-cols-2 gap-3;
  }

  /* 增强版用户管理页 */
  .users-page .search-toolbar {
    @apply space-y-3;
  }

  .users-page .filter-collapse {
    @apply p-3;
  }

  .users-page .advanced-filters {
    @apply grid-cols-1 gap-3;
  }

  .users-table .semi-table-thead {
    @apply hidden;
  }

  .users-table .semi-table-tbody-tr {
    @apply block border-b pb-4 mb-4;
  }

  .users-table .semi-table-tbody-td {
    @apply block py-1 border-none;
  }

  .users-table .semi-table-tbody-td:first-child {
    @apply font-medium text-base mb-2;
  }

  .users-table .semi-table-tbody-td:before {
    content: attr(data-label) ": ";
    @apply font-medium text-gray-600 mr-2;
  }

  /* 用户统计卡片 */
  .user-stats-cards {
    @apply grid-cols-2 gap-2;
  }

  .user-stats-card {
    @apply p-3;
  }

  .user-stats-card .text-2xl {
    @apply text-lg;
  }

  /* 批量操作区域 */
  .batch-operations {
    @apply flex-col space-y-3 p-3;
  }

  .batch-dropdown {
    @apply w-full;
  }

  /* 高级筛选 */
  .advanced-filter-collapse {
    @apply p-3;
  }

  .filter-row {
    @apply grid-cols-1 gap-3;
  }

  /* 导入导出弹窗 */
  .import-export-modal .semi-modal {
    @apply m-2;
  }

  .export-fields-grid {
    @apply grid-cols-2 gap-2;
  }

  /* 角色管理页 */
  .roles-page .role-stats {
    @apply grid-cols-2 gap-2;
  }

  .roles-page .permission-matrix {
    @apply text-sm;
  }

  /* 权限模板管理页 */
  .permission-templates .template-stats {
    @apply grid-cols-2 gap-3;
  }

  .permission-templates .template-card {
    @apply p-3;
  }

  .permission-templates .template-card .text-2xl {
    @apply text-lg;
  }

  .permission-templates .template-grid {
    @apply grid-cols-1;
  }

  .permission-templates .search-filters {
    @apply flex-col space-y-3;
  }

  /* 权限树 */
  .permission-tree {
    @apply text-sm;
  }

  .permission-tree-node {
    @apply py-1;
  }

  .permission-category {
    @apply grid-cols-1 gap-2;
  }

  .permission-details {
    @apply space-y-2;
  }

  /* 系统监控页 */
  .system-monitor .monitor-stats {
    @apply grid-cols-2 gap-3;
  }

  .system-monitor .metrics-grid {
    @apply grid-cols-1 gap-3;
  }

  .system-monitor .metric-card {
    @apply p-3;
  }

  .system-monitor .metric-card .text-2xl {
    @apply text-lg;
  }

  .system-monitor .service-table {
    @apply text-xs;
  }

  .system-monitor .chart-container {
    @apply h-48;
  }

  .system-monitor .timeline-item {
    @apply text-sm;
  }

  /* 系统状态卡片 */
  .system-status-cards {
    @apply grid-cols-1 gap-3;
  }

  .system-component-card {
    @apply p-3;
  }

  .resource-metrics {
    @apply space-y-3;
  }

  .metric-progress {
    @apply space-y-2;
  }

  /* 告警区域 */
  .alerts-section {
    @apply space-y-3 max-h-60 overflow-y-auto;
  }

  .alert-card {
    @apply p-3 text-sm;
  }

  .alert-header {
    @apply flex-col space-y-2 items-start;
  }

  /* 系统日志 */
  .system-logs {
    @apply space-y-2 max-h-52 overflow-y-auto;
  }

  .log-item {
    @apply p-2 text-xs;
  }

  .log-header {
    @apply flex-col space-y-1 items-start;
  }

  /* 性能指标区域 */
  .performance-metrics {
    @apply space-y-3;
  }

  .performance-card {
    @apply p-3;
  }

  .performance-grid {
    @apply grid-cols-2 gap-2;
  }

  /* 控制面板 */
  .monitor-controls {
    @apply flex-col space-y-3;
  }

  .refresh-controls {
    @apply flex-col space-y-2;
  }

  .auto-refresh-toggle {
    @apply justify-between w-full;
  }
}

/* 横屏模式优化 */
@media (max-width: 768px) and (orientation: landscape) {
  .page-header {
    @apply py-2;
  }

  .stat-card {
    @apply py-2;
  }

  .modal-content {
    @apply max-h-screen overflow-y-auto;
  }
}

/* 暗色模式适配 - 现代化增强 */
@media (prefers-color-scheme: dark) {
  .ops-card {
    @apply bg-gray-800 border-gray-700;
  }

  .ops-stats-card {
    @apply bg-gray-800 border-gray-700;
  }

  .ops-search-toolbar {
    @apply bg-gray-800 border-gray-700;
  }

  .ops-batch-actions {
    @apply bg-gray-800 border-gray-700;
  }

  .ops-component-status {
    @apply bg-gray-800 border-gray-700;
  }

  .ops-performance-card {
    @apply bg-gray-800;
  }

  .ops-alert-card {
    @apply bg-gray-800;
  }

  /* 兼容旧样式 */
  .stat-card {
    @apply bg-gray-800 border-gray-700;
  }

  .search-toolbar {
    @apply bg-gray-800;
  }

  .batch-actions {
    @apply bg-gray-800;
  }
}

/* 打印样式 */
@media print {
  .page-header,
  .action-buttons,
  .batch-actions,
  .semi-button {
    @apply hidden;
  }

  .card {
    @apply border border-gray-300 mb-4;
  }

  .semi-table {
    @apply text-sm;
  }
}

/* 无障碍优化 - 现代化增强 */
@media (prefers-reduced-motion: reduce) {
  .ops-card,
  .ops-stats-card,
  .ops-component-status,
  .ops-performance-card,
  .ops-alert-card,
  .ops-realtime-status {
    transition: none;
  }

  .ops-realtime-dot {
    animation: none !important;
  }

  /* 兼容旧样式 */
  .semi-modal,
  .semi-dropdown,
  .semi-tooltip {
    transition: none;
  }

  .progress-animation {
    animation: none;
  }
}

/* 高对比度模式 - 现代化增强 */
@media (prefers-contrast: high) {
  .ops-card,
  .ops-stats-card,
  .ops-component-status,
  .ops-performance-card,
  .ops-alert-card {
    @apply border-2 border-black;
  }

  .ops-search-toolbar,
  .ops-batch-actions {
    @apply border-2 border-black;
  }

  /* 兼容旧样式 */
  .stat-card {
    @apply border-2 border-black;
  }

  .semi-button {
    @apply border-2 border-black;
  }

  .semi-input {
    @apply border-2 border-black;
  }
}

/* 现代化工具类 */
.ops-mobile-hidden {
  @apply hidden;
}

.ops-mobile-visible {
  @apply hidden;
}

.ops-desktop-hidden {
  @apply hidden;
}

.ops-desktop-visible {
  @apply block;
}

/* 兼容旧工具类 */
.mobile-hidden {
  @apply hidden;
}

@media (max-width: 768px) {
  .mobile-hidden {
    @apply hidden;
  }

  .mobile-visible {
    @apply block;
  }

  .mobile-full-width {
    @apply w-full;
  }

  .mobile-text-center {
    @apply text-center;
  }

  .mobile-space-y-2 > * + * {
    @apply mt-2;
  }

  .mobile-space-y-3 > * + * {
    @apply mt-3;
  }

  .mobile-grid-cols-1 {
    @apply grid-cols-1;
  }

  .mobile-grid-cols-2 {
    @apply grid-cols-2;
  }

  .mobile-flex-col {
    @apply flex-col;
  }

  .mobile-p-2 {
    @apply p-2;
  }

  .mobile-p-3 {
    @apply p-3;
  }

  .mobile-p-4 {
    @apply p-4;
  }

  .mobile-rounded {
    @apply rounded;
  }

  .mobile-rounded-lg {
    @apply rounded-lg;
  }
}
