/**
 * AI 功能自定义 Hook
 */

import { useEffect, useCallback, useRef } from "react";
import { useAIStore, aiStoreSelectors } from "@/stores/aiStore";
import { aiAnalysisService } from "@/services/aiAnalysis";
import type { AIMode, AISuggestion, AIFeedback } from "@/types/ai";

interface UseAIOptions {
  /** 用户ID，用于个人配置 */
  userId?: string;
  /** 是否自动加载配置 */
  autoLoadConfig?: boolean;
  /** 激进模式的防抖延迟时间(ms) */
  debounceDelay?: number;
  /** 上下文数据 */
  contextData?: any;
}

interface UseAIReturn {
  // AI配置相关
  mode: AIMode;
  config: any;
  isAggressiveMode: boolean;
  isUserTriggeredMode: boolean;
  isAIDisabled: boolean;

  // AI分析相关
  suggestions: AISuggestion[];
  analyzing: boolean;
  lastAnalysis: any;

  // 状态相关
  loading: boolean;
  error: string | null;
  hasError: boolean;

  // 方法
  analyzeContent: (description: string) => Promise<void>;
  adoptSuggestion: (field: string, value: string) => void;
  adoptAllSuggestions: () => Record<string, string>;
  switchMode: (mode: AIMode) => Promise<void>;
  updateConfig: (config: any) => Promise<void>;
  submitFeedback: (feedback: Omit<AIFeedback, "requestId">) => Promise<void>;
  clearSuggestions: () => void;
  clearError: () => void;

  // 激进模式专用
  debouncedAnalyze: (description: string) => void;
}

export const useAI = (options: UseAIOptions = {}): UseAIReturn => {
  const { userId, autoLoadConfig = true, debounceDelay = 800, contextData } = options;

  // Store状态
  const {
    config,
    loading,
    error,
    analyzing,
    lastAnalysis,
    suggestions,
    analysisError,
    loadConfiguration,
    updateConfiguration,
    switchMode: storeSwitchMode,
    analyzeContent: storeAnalyzeContent,
    adoptSuggestion,
    adoptAllSuggestions,
    clearSuggestions,
    setError,
    clearError,
  } = useAIStore();

  // 计算状态
  const mode = aiStoreSelectors.getCurrentMode(useAIStore.getState());
  const isAggressiveMode = aiStoreSelectors.isAggressiveMode(useAIStore.getState());
  const isUserTriggeredMode = aiStoreSelectors.isUserTriggeredMode(useAIStore.getState());
  const isAIDisabled = aiStoreSelectors.isAIDisabled(useAIStore.getState());
  const hasError = aiStoreSelectors.hasError(useAIStore.getState());
  const currentError = aiStoreSelectors.getError(useAIStore.getState());

  // 防抖引用
  const debounceTimeoutRef = useRef<NodeJS.Timeout>();
  const lastAnalysisRequestRef = useRef<string>("");

  // 初始化加载配置
  useEffect(() => {
    if (autoLoadConfig) {
      loadConfiguration(userId);
    }
  }, [userId, autoLoadConfig, loadConfiguration]);

  // AI内容分析
  const analyzeContent = useCallback(
    async (description: string) => {
      if (!description.trim()) {
        setError("描述内容不能为空");
        return;
      }

      if (isAIDisabled) {
        setError("AI 功能已禁用");
        return;
      }

      // 避免重复分析相同内容
      if (lastAnalysisRequestRef.current === description.trim()) {
        return;
      }

      lastAnalysisRequestRef.current = description.trim();

      await storeAnalyzeContent(description, {
        ...contextData,
        userId,
      });
    },
    [isAIDisabled, storeAnalyzeContent, contextData, userId, setError]
  );

  // 激进模式专用的防抖分析
  const debouncedAnalyze = useCallback(
    (description: string) => {
      if (!isAggressiveMode) {
        return;
      }

      // 清除之前的定时器
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }

      // 设置新的防抖定时器
      debounceTimeoutRef.current = setTimeout(() => {
        analyzeContent(description);
      }, debounceDelay);
    },
    [isAggressiveMode, debounceDelay, analyzeContent]
  );

  // 切换AI模式
  const switchMode = useCallback(
    async (newMode: AIMode) => {
      await storeSwitchMode(newMode, userId);
    },
    [storeSwitchMode, userId]
  );

  // 更新配置
  const updateConfig = useCallback(
    async (configUpdate: any) => {
      await updateConfiguration(configUpdate, userId);
    },
    [updateConfiguration, userId]
  );

  // 提交反馈
  const submitFeedback = useCallback(
    async (feedbackData: Omit<AIFeedback, "requestId">) => {
      if (!lastAnalysis?.requestId) {
        console.warn("No request ID available for feedback");
        return;
      }

      const feedback = {
        ...feedbackData,
        requestId: lastAnalysis.requestId,
      };

      try {
        await aiAnalysisService.submitFeedback(feedback);
      } catch (error) {
        console.error("Failed to submit AI feedback:", error);
      }
    },
    [lastAnalysis?.requestId]
  );

  // 清理副作用
  useEffect(() => {
    return () => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
    };
  }, []);

  // 激进模式自动采用高置信度建议
  useEffect(() => {
    if (isAggressiveMode && suggestions.length > 0 && !analyzing) {
      const highConfidenceSuggestions = aiStoreSelectors.getHighConfidenceSuggestions(
        useAIStore.getState()
      );

      // 自动采用高置信度建议
      if (highConfidenceSuggestions.length > 0) {
        highConfidenceSuggestions.forEach(suggestion => {
          adoptSuggestion(suggestion.field, suggestion.suggested);
        });
      }
    }
  }, [isAggressiveMode, suggestions, analyzing, adoptSuggestion]);

  return {
    // AI配置相关
    mode,
    config,
    isAggressiveMode,
    isUserTriggeredMode,
    isAIDisabled,

    // AI分析相关
    suggestions: aiStoreSelectors.getAvailableSuggestions(useAIStore.getState()),
    analyzing,
    lastAnalysis,

    // 状态相关
    loading,
    error: currentError,
    hasError,

    // 方法
    analyzeContent,
    adoptSuggestion,
    adoptAllSuggestions,
    switchMode,
    updateConfig,
    submitFeedback,
    clearSuggestions,
    clearError,

    // 激进模式专用
    debouncedAnalyze,
  };
};

// 专门用于表单字段的AI Hook
interface UseAIFormFieldOptions extends UseAIOptions {
  /** 字段名 */
  fieldName: string;
  /** 表单API引用 */
  formApi?: any;
  /** 字段值变化回调 */
  onFieldChange?: (field: string, value: string, confidence: number) => void;
}

export const useAIFormField = (options: UseAIFormFieldOptions) => {
  const { fieldName, formApi, onFieldChange, ...aiOptions } = options;

  const ai = useAI(aiOptions);

  // 获取当前字段的建议
  const fieldSuggestion = ai.suggestions.find(s => s.field === fieldName);

  // 采用字段建议
  const adoptFieldSuggestion = useCallback(() => {
    if (fieldSuggestion && formApi) {
      formApi.setValue(fieldName, fieldSuggestion.suggested);
      ai.adoptSuggestion(fieldName, fieldSuggestion.suggested);

      if (onFieldChange) {
        onFieldChange(fieldName, fieldSuggestion.suggested, fieldSuggestion.confidence);
      }
    }
  }, [fieldSuggestion, formApi, fieldName, onFieldChange, ai]);

  // 检查是否应该自动填充
  const shouldAutoFill = useCallback(() => {
    if (!ai.isAggressiveMode || !fieldSuggestion) {
      return false;
    }

    return (
      fieldSuggestion.confidence >= ai.config.autoFillThreshold &&
      ai.config.enabledFields[fieldName as keyof typeof ai.config.enabledFields]
    );
  }, [ai.isAggressiveMode, fieldSuggestion, ai.config, fieldName]);

  // 激进模式自动填充
  useEffect(() => {
    if (shouldAutoFill() && formApi) {
      const currentValue = formApi.getValue(fieldName);
      // 只有当前字段为空时才自动填充
      if (!currentValue && fieldSuggestion) {
        adoptFieldSuggestion();
      }
    }
  }, [shouldAutoFill, formApi, fieldName, fieldSuggestion, adoptFieldSuggestion]);

  return {
    ...ai,
    fieldSuggestion,
    adoptFieldSuggestion,
    shouldAutoFill: shouldAutoFill(),
    hasFieldSuggestion: !!fieldSuggestion,
    fieldConfidence: fieldSuggestion?.confidence || 0,
  };
};

export default useAI;
