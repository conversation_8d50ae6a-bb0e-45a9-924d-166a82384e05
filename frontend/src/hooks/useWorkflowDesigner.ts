/**
 * 工作流设计器Hook
 * 管理工作流设计器的状态和操作
 */

import { useState, useCallback, useRef, useEffect } from 'react';
import {
  WorkflowDefinition,
  DesignerState,
  DesignerNode,
  DesignerEvent,
  Connection,
  ExecuteWorkflowRequest,
  WorkflowExportData,
  ValidationResult
} from '../types/workflow';
import { workflowService } from '../services/workflow';
import { generateNodeId, validateWorkflow, createEmptyWorkflow } from '../utils/workflow-helpers';

// 历史记录管理
interface HistoryState {
  past: Array<{ nodes: DesignerNode[]; connections: Connection[] }>;
  present: { nodes: DesignerNode[]; connections: Connection[] };
  future: Array<{ nodes: DesignerNode[]; connections: Connection[] }>;
}

export const useWorkflowDesigner = () => {
  // 基础状态
  const [workflow, setWorkflow] = useState<WorkflowDefinition | null>(null);
  const [designerState, setDesignerState] = useState<DesignerState>({
    nodes: [],
    connections: [],
    selectedNodes: [],
    copiedNodes: [],
    history: {
      past: [],
      present: { nodes: [], connections: [] },
      future: []
    },
    viewport: { x: 0, y: 0, zoom: 1 },
    isReadonly: false
  });
  const [isModified, setIsModified] = useState(false);
  const [validationResult, setValidationResult] = useState<ValidationResult>({
    isValid: true,
    errors: [],
    warnings: []
  });

  // 内部状态
  const initialStateRef = useRef<string>('');
  const autoSaveTimeoutRef = useRef<NodeJS.Timeout>();

  // 计算属性
  const selectedNodes = designerState.selectedNodes;
  const isValid = validationResult.isValid;
  const validationErrors = validationResult.errors;
  const canUndo = designerState.history.past.length > 0;
  const canRedo = designerState.history.future.length > 0;

  // 添加到历史记录
  const addToHistory = useCallback((nodes: DesignerNode[], connections: Connection[]) => {
    setDesignerState(prev => {
      const newHistory: HistoryState = {
        past: [...prev.history.past, prev.history.present],
        present: { nodes: [...nodes], connections: [...connections] },
        future: [] // 清空重做栈
      };
      
      // 限制历史记录数量
      if (newHistory.past.length > 50) {
        newHistory.past.shift();
      }

      return {
        ...prev,
        nodes,
        connections,
        history: newHistory
      };
    });
    setIsModified(true);
  }, []);

  // 验证工作流
  const validateCurrentWorkflow = useCallback(() => {
    if (!workflow) return;
    
    const result = validateWorkflow({
      ...workflow,
      steps: designerState.nodes.map(node => node.step)
    });
    
    setValidationResult(result);
  }, [workflow, designerState.nodes]);

  // 监听状态变化进行验证
  useEffect(() => {
    validateCurrentWorkflow();
  }, [validateCurrentWorkflow]);

  // 自动保存
  useEffect(() => {
    if (!isModified || !workflow) return;

    if (autoSaveTimeoutRef.current) {
      clearTimeout(autoSaveTimeoutRef.current);
    }

    autoSaveTimeoutRef.current = setTimeout(() => {
      // 这里可以实现自动保存到本地存储
      const state = {
        workflow,
        designerState,
        timestamp: Date.now()
      };
      localStorage.setItem(`workflow_autosave_${workflow.id}`, JSON.stringify(state));
    }, 5000); // 5秒后自动保存

    return () => {
      if (autoSaveTimeoutRef.current) {
        clearTimeout(autoSaveTimeoutRef.current);
      }
    };
  }, [isModified, workflow, designerState]);

  // 加载工作流
  const loadWorkflow = useCallback(async (workflowId: string, asTemplate = false) => {
    try {
      const loadedWorkflow = await workflowService.getWorkflowById(workflowId);
      
      if (asTemplate) {
        // 作为模板创建新工作流
        const newWorkflow = {
          ...loadedWorkflow,
          id: '', // 清空ID表示新建
          name: `${loadedWorkflow.name} (副本)`,
          isTemplate: false,
          isActive: false,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };
        setWorkflow(newWorkflow);
      } else {
        setWorkflow(loadedWorkflow);
      }

      // 转换为设计器节点
      const nodes = convertStepsToNodes(loadedWorkflow.steps);
      const connections = extractConnectionsFromSteps(loadedWorkflow.steps);
      
      setDesignerState(prev => ({
        ...prev,
        nodes,
        connections,
        selectedNodes: [],
        history: {
          past: [],
          present: { nodes, connections },
          future: []
        }
      }));
      
      // 记录初始状态
      initialStateRef.current = JSON.stringify({ nodes, connections });
      setIsModified(false);
      
    } catch (error) {
      console.error('加载工作流失败:', error);
      throw error;
    }
  }, []);

  // 创建新工作流
  const createNewWorkflow = useCallback(() => {
    const newWorkflow = createEmptyWorkflow();
    setWorkflow(newWorkflow);
    
    const emptyState = {
      nodes: [],
      connections: []
    };
    
    setDesignerState(prev => ({
      ...prev,
      nodes: [],
      connections: [],
      selectedNodes: [],
      history: {
        past: [],
        present: emptyState,
        future: []
      }
    }));
    
    initialStateRef.current = JSON.stringify(emptyState);
    setIsModified(false);
  }, []);

  // 保存工作流
  const saveWorkflow = useCallback(async () => {
    if (!workflow || !isValid) {
      throw new Error('工作流无效，无法保存');
    }

    const workflowToSave: WorkflowDefinition = {
      ...workflow,
      steps: designerState.nodes.map(node => node.step),
      updatedAt: new Date().toISOString()
    };

    try {
      let savedWorkflow;
      if (workflow.id) {
        // 更新现有工作流
        savedWorkflow = await workflowService.updateWorkflow(workflow.id, workflowToSave);
      } else {
        // 创建新工作流
        savedWorkflow = await workflowService.createWorkflow(workflowToSave);
      }
      
      setWorkflow(savedWorkflow);
      setIsModified(false);
      
      // 更新初始状态
      initialStateRef.current = JSON.stringify({
        nodes: designerState.nodes,
        connections: designerState.connections
      });
      
      return savedWorkflow;
    } catch (error) {
      console.error('保存工作流失败:', error);
      throw error;
    }
  }, [workflow, designerState.nodes, designerState.connections, isValid]);

  // 执行工作流
  const executeWorkflow = useCallback(async (request: ExecuteWorkflowRequest) => {
    try {
      const result = await workflowService.executeWorkflow(request);
      return result;
    } catch (error) {
      console.error('执行工作流失败:', error);
      throw error;
    }
  }, []);

  // 导出工作流
  const exportWorkflow = useCallback(async (): Promise<WorkflowExportData> => {
    if (!workflow) {
      throw new Error('没有可导出的工作流');
    }

    return {
      version: '1.0',
      workflow: {
        ...workflow,
        steps: designerState.nodes.map(node => node.step)
      },
      metadata: {
        exportTime: new Date().toISOString(),
        exportBy: 'current-user', // 应该从认证状态获取
        source: 'workflow-designer'
      }
    };
  }, [workflow, designerState.nodes]);

  // 导入工作流
  const importWorkflow = useCallback(async (importData: WorkflowExportData) => {
    try {
      const { workflow: importedWorkflow } = importData;
      
      // 清空ID作为新工作流
      const newWorkflow = {
        ...importedWorkflow,
        id: '',
        name: `${importedWorkflow.name} (导入)`,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      
      setWorkflow(newWorkflow);
      
      // 转换为设计器节点
      const nodes = convertStepsToNodes(importedWorkflow.steps);
      const connections = extractConnectionsFromSteps(importedWorkflow.steps);
      
      addToHistory(nodes, connections);
      
    } catch (error) {
      console.error('导入工作流失败:', error);
      throw error;
    }
  }, [addToHistory]);

  // 处理设计器事件
  const handleDesignerEvent = useCallback((event: DesignerEvent) => {
    setDesignerState(prev => {
      let newNodes = [...prev.nodes];
      let newConnections = [...prev.connections];
      let newSelectedNodes = [...prev.selectedNodes];
      
      switch (event.type) {
        case 'NODE_SELECT':
          if (event.multiSelect) {
            if (newSelectedNodes.includes(event.nodeId)) {
              newSelectedNodes = newSelectedNodes.filter(id => id !== event.nodeId);
            } else {
              newSelectedNodes.push(event.nodeId);
            }
          } else {
            newSelectedNodes = [event.nodeId];
          }
          return { ...prev, selectedNodes: newSelectedNodes };
          
        case 'NODE_DESELECT':
          newSelectedNodes = newSelectedNodes.filter(id => id !== event.nodeId);
          return { ...prev, selectedNodes: newSelectedNodes };
          
        case 'NODE_MOVE':
          newNodes = newNodes.map(node => 
            node.id === event.nodeId 
              ? { ...node, position: event.position }
              : node
          );
          break;
          
        case 'NODE_DELETE':
          newNodes = newNodes.filter(node => node.id !== event.nodeId);
          newConnections = newConnections.filter(conn => 
            conn.sourceNodeId !== event.nodeId && conn.targetNodeId !== event.nodeId
          );
          newSelectedNodes = newSelectedNodes.filter(id => id !== event.nodeId);
          break;
          
        case 'NODE_COPY':
          const nodeToCopy = newNodes.find(n => n.id === event.nodeId);
          if (nodeToCopy) {
            return {
              ...prev,
              copiedNodes: [nodeToCopy]
            };
          }
          return prev;
          
        case 'NODE_PASTE':
          if (prev.copiedNodes.length > 0) {
            const pastedNodes = prev.copiedNodes.map(node => ({
              ...node,
              id: generateNodeId(),
              position: {
                x: event.position.x + 20,
                y: event.position.y + 20
              }
            }));
            newNodes.push(...pastedNodes);
          }
          break;
          
        case 'CONNECTION_CREATE':
          // 检查连接是否已存在
          const exists = newConnections.some(conn => 
            conn.sourceNodeId === event.connection.sourceNodeId &&
            conn.targetNodeId === event.connection.targetNodeId
          );
          if (!exists) {
            newConnections.push(event.connection);
          }
          break;
          
        case 'CONNECTION_DELETE':
          newConnections = newConnections.filter(conn => conn.id !== event.connectionId);
          break;
          
        case 'CANVAS_CLICK':
          return { ...prev, selectedNodes: [] };
          
        case 'VIEWPORT_CHANGE':
          return { ...prev, viewport: event.viewport };
          
        default:
          return prev;
      }
      
      // 更新历史记录并返回新状态
      const newState = {
        ...prev,
        nodes: newNodes,
        connections: newConnections,
        selectedNodes: newSelectedNodes
      };
      
      // 异步更新历史记录
      setTimeout(() => addToHistory(newNodes, newConnections), 0);
      
      return newState;
    });
  }, [addToHistory]);

  // 撤销
  const undo = useCallback(() => {
    setDesignerState(prev => {
      if (prev.history.past.length === 0) return prev;
      
      const previous = prev.history.past[prev.history.past.length - 1];
      const newPast = prev.history.past.slice(0, -1);
      
      return {
        ...prev,
        nodes: previous.nodes,
        connections: previous.connections,
        selectedNodes: [],
        history: {
          past: newPast,
          present: previous,
          future: [prev.history.present, ...prev.history.future]
        }
      };
    });
    setIsModified(true);
  }, []);

  // 重做
  const redo = useCallback(() => {
    setDesignerState(prev => {
      if (prev.history.future.length === 0) return prev;
      
      const next = prev.history.future[0];
      const newFuture = prev.history.future.slice(1);
      
      return {
        ...prev,
        nodes: next.nodes,
        connections: next.connections,
        selectedNodes: [],
        history: {
          past: [...prev.history.past, prev.history.present],
          present: next,
          future: newFuture
        }
      };
    });
    setIsModified(true);
  }, []);

  return {
    // 状态
    workflow,
    designerState,
    selectedNodes,
    isModified,
    isValid,
    validationErrors: validationResult.errors,
    validationWarnings: validationResult.warnings,
    canUndo,
    canRedo,
    
    // 操作方法
    loadWorkflow,
    createNewWorkflow,
    saveWorkflow,
    executeWorkflow,
    exportWorkflow,
    importWorkflow,
    handleDesignerEvent,
    undo,
    redo,
    
    // 工具方法
    validateCurrentWorkflow
  };
};

// 辅助函数：将步骤转换为设计器节点
function convertStepsToNodes(steps: any[]): DesignerNode[] {
  return steps.map((step, index) => ({
    id: step.id || `node_${index}`,
    type: step.type,
    position: step.position || { x: 100 + index * 200, y: 100 },
    size: { width: 200, height: 80 },
    step,
    data: {
      step,
      isSelected: false,
      isHighlighted: false,
      validation: { isValid: true, errors: [] }
    },
    ports: {
      input: [{ id: 'input', label: '输入', type: 'default' }],
      output: [
        { id: 'output', label: '输出', type: 'default' },
        { id: 'error', label: '错误', type: 'error' }
      ]
    }
  }));
}

// 辅助函数：从步骤中提取连接关系
function extractConnectionsFromSteps(steps: any[]): Connection[] {
  const connections: Connection[] = [];
  
  steps.forEach((step, index) => {
    if (step.onSuccess?.nextStep !== undefined) {
      connections.push({
        id: `conn_${step.id}_success`,
        sourceNodeId: step.id || `node_${index}`,
        targetNodeId: steps[step.onSuccess.nextStep]?.id || `node_${step.onSuccess.nextStep}`,
        sourcePort: 'output',
        targetPort: 'input',
        label: '成功'
      });
    }
    
    if (step.onFailure?.nextStep !== undefined) {
      connections.push({
        id: `conn_${step.id}_error`,
        sourceNodeId: step.id || `node_${index}`,
        targetNodeId: steps[step.onFailure.nextStep]?.id || `node_${step.onFailure.nextStep}`,
        sourcePort: 'error',
        targetPort: 'input',
        label: '失败'
      });
    }
  });
  
  return connections;
}