import { useState, useEffect, useCallback, useRef } from "react";
import { get } from "@/utils/request";

interface FileLoaderState {
  blobUrl: string | null;
  loading: boolean;
  error: string | null;
  progress: number;
}

interface UseFileLoaderOptions {
  enabled?: boolean; // 是否自动加载
  cacheable?: boolean; // 是否缓存
  onProgress?: (progress: number) => void;
  onError?: (error: string) => void;
  onSuccess?: (blobUrl: string) => void;
}

// 全局文件缓存
const fileCache = new Map<string, string>();
const loadingPromises = new Map<string, Promise<string>>();

// 清理blob URL的函数
const cleanupBlobUrl = (url: string) => {
  if (url && url.startsWith("blob:")) {
    URL.revokeObjectURL(url);
  }
};

// 获取认证头的函数
const getAuthHeaders = (): Record<string, string> => {
  const token = localStorage.getItem("token");
  return token ? { Authorization: `Bearer ${token}` } : {};
};

export const useFileLoader = (attachmentId: string | null, options: UseFileLoaderOptions = {}) => {
  const { enabled = true, cacheable = true, onProgress, onError, onSuccess } = options;

  const [state, setState] = useState<FileLoaderState>({
    blobUrl: null,
    loading: false,
    error: null,
    progress: 0,
  });

  const abortControllerRef = useRef<AbortController | null>(null);
  const mountedRef = useRef(true);

  // 加载文件的核心函数
  const loadFile = useCallback(
    async (id: string): Promise<string> => {
      // 如果正在加载，返回现有的Promise
      if (loadingPromises.has(id)) {
        return loadingPromises.get(id)!;
      }

      // 检查缓存
      if (cacheable && fileCache.has(id)) {
        const cachedUrl = fileCache.get(id)!;
        // 验证blob URL是否仍然有效
        try {
          const response = await fetch(cachedUrl, { method: "HEAD" });
          if (response.ok) {
            return cachedUrl;
          } else {
            // 缓存的blob URL已失效，清理并重新加载
            fileCache.delete(id);
            cleanupBlobUrl(cachedUrl);
          }
        } catch {
          // 缓存的URL无效，继续重新加载
          fileCache.delete(id);
          cleanupBlobUrl(cachedUrl);
        }
      }

      // 创建加载Promise
      const loadPromise = (async () => {
        try {
          // 创建AbortController用于取消请求
          const controller = new AbortController();
          abortControllerRef.current = controller;

          // 发起请求获取文件
          const response = await fetch(`/api/v1/upload/download/${id}`, {
            method: "GET",
            headers: {
              ...getAuthHeaders(),
            },
            signal: controller.signal,
          });

          if (!response.ok) {
            throw new Error(`文件加载失败: ${response.status} ${response.statusText}`);
          }

          // 检查内容类型
          const contentType = response.headers.get("content-type") || "";

          // 获取文件大小用于进度计算
          const contentLength = response.headers.get("content-length");
          const totalSize = contentLength ? parseInt(contentLength, 10) : 0;

          if (!response.body) {
            throw new Error("响应体为空");
          }

          // 使用ReadableStream读取响应，支持进度回调
          const reader = response.body.getReader();
          const chunks: Uint8Array[] = [];
          let receivedLength = 0;

          while (true) {
            const { done, value } = await reader.read();

            if (done) break;

            chunks.push(value);
            receivedLength += value.length;

            // 更新进度
            if (totalSize > 0 && mountedRef.current) {
              const progress = (receivedLength / totalSize) * 100;
              setState(prev => ({ ...prev, progress }));
              onProgress?.(progress);
            }
          }

          // 合并所有chunks创建blob
          const blob = new Blob(chunks, { type: contentType });
          const blobUrl = URL.createObjectURL(blob);

          // 缓存结果
          if (cacheable) {
            fileCache.set(id, blobUrl);
          }

          return blobUrl;
        } catch (error: any) {
          if (error.name === "AbortError") {
            throw new Error("文件加载已取消");
          }
          throw error;
        } finally {
          // 清理loading promise
          loadingPromises.delete(id);
          abortControllerRef.current = null;
        }
      })();

      // 缓存loading promise
      loadingPromises.set(id, loadPromise);

      return loadPromise;
    },
    [cacheable, onProgress]
  );

  // 手动触发加载
  const load = useCallback(async () => {
    if (!attachmentId || !mountedRef.current) return;

    setState(prev => ({
      ...prev,
      loading: true,
      error: null,
      progress: 0,
    }));

    try {
      const blobUrl = await loadFile(attachmentId);

      if (mountedRef.current) {
        setState(prev => ({
          ...prev,
          blobUrl,
          loading: false,
          progress: 100,
        }));
        onSuccess?.(blobUrl);
      }
    } catch (error: any) {
      if (mountedRef.current) {
        const errorMessage = error.message || "文件加载失败";
        setState(prev => ({
          ...prev,
          error: errorMessage,
          loading: false,
          progress: 0,
        }));
        onError?.(errorMessage);
      }
    }
  }, [attachmentId, loadFile, onSuccess, onError]);

  // 取消加载
  const cancel = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    setState(prev => ({
      ...prev,
      loading: false,
      progress: 0,
    }));
  }, []);

  // 清理当前blob URL
  const cleanup = useCallback(() => {
    if (state.blobUrl) {
      cleanupBlobUrl(state.blobUrl);
      setState(prev => ({
        ...prev,
        blobUrl: null,
      }));
    }
  }, [state.blobUrl]);

  // 自动加载效果
  useEffect(() => {
    if (enabled && attachmentId) {
      load();
    }

    return () => {
      // 取消正在进行的请求
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, [enabled, attachmentId, load]);

  // 组件卸载时的清理
  useEffect(() => {
    return () => {
      mountedRef.current = false;
      // 注意：这里不清理blobUrl，因为它可能被缓存用于其他组件
    };
  }, []);

  return {
    ...state,
    load,
    cancel,
    cleanup,
  };
};

// 清理全部缓存的工具函数
export const clearFileCache = () => {
  fileCache.forEach(url => {
    cleanupBlobUrl(url);
  });
  fileCache.clear();
  loadingPromises.clear();
};

// 清理特定文件的缓存
export const clearFileCacheById = (attachmentId: string) => {
  const cachedUrl = fileCache.get(attachmentId);
  if (cachedUrl) {
    cleanupBlobUrl(cachedUrl);
    fileCache.delete(attachmentId);
  }
  loadingPromises.delete(attachmentId);
};

export default useFileLoader;
