import { useQuery, UseQueryResult } from '@tanstack/react-query';
import { extractPaginatedData, extractNestedPaginatedData, PaginatedResponse } from '@/utils/data-helpers';

/**
 * 自定义Hook：处理分页数据的获取和类型安全
 */
export function usePaginatedData<T>(
  queryKey: string[],
  queryFn: () => Promise<PaginatedResponse<T>>,
  options?: {
    enabled?: boolean;
    refetchInterval?: number;
    staleTime?: number;
  }
) {
  const queryResult = useQuery({
    queryKey,
    queryFn,
    ...options,
  });

  const { data, pagination } = extractPaginatedData(queryResult.data);

  return {
    ...queryResult,
    data,
    pagination,
    total: pagination.total,
    page: pagination.page,
    limit: pagination.limit,
    totalPages: pagination.totalPages,
  };
}

/**
 * 自定义Hook：处理嵌套API响应的分页数据获取
 */
export function useNestedPaginatedData<T>(
  queryKey: string[],
  queryFn: () => Promise<{ success: boolean; data: PaginatedResponse<T> }>,
  options?: {
    enabled?: boolean;
    refetchInterval?: number;
    staleTime?: number;
  }
) {
  const queryResult = useQuery({
    queryKey,
    queryFn,
    ...options,
  });

  const { data, pagination } = extractNestedPaginatedData(queryResult.data);

  return {
    ...queryResult,
    data,
    pagination,
    total: pagination.total,
    page: pagination.page,
    limit: pagination.limit,
    totalPages: pagination.totalPages,
  };
}

/**
 * 自定义Hook：处理简单数据的获取和类型安全
 */
export function useData<T>(
  queryKey: string[],
  queryFn: () => Promise<{ success: boolean; data: T; message?: string }>,
  options?: {
    enabled?: boolean;
    refetchInterval?: number;
    staleTime?: number;
  }
) {
  const queryResult = useQuery({
    queryKey,
    queryFn,
    ...options,
  });

  return {
    ...queryResult,
    data: queryResult.data?.success ? queryResult.data.data : null,
  };
}
