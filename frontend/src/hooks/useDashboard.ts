import { useQuery, useQueries, useQueryClient } from "@tanstack/react-query";
import { dashboardService, type DashboardStats, type TrendData } from "@/services/dashboard";
import { useMemo } from "react";

// Dashboard查询键
export const DASHBOARD_QUERY_KEYS = {
  stats: ["dashboard", "stats"] as const,
  quickStats: ["dashboard", "quick-stats"] as const,
  trends: (days: number) => ["dashboard", "trends", days] as const,
  systemHealth: ["dashboard", "system-health"] as const,
};

// Dashboard主要数据Hook
export const useDashboardStats = (options = {}) => {
  return useQuery({
    queryKey: DASHBOARD_QUERY_KEYS.stats,
    queryFn: async () => {
      try {
        return await dashboardService.getDashboardStats();
      } catch (error) {
        console.error("Dashboard stats error:", error);
        throw error;
      }
    },
    staleTime: 300000, // 5分钟
    enabled: true,
    select: data => data?.data, // 安全提取data字段
    retry: 2, // 重试次数
    retryDelay: 1000, // 重试延迟
    ...options, // 允许外部覆盖选项
  });
};

// 快速统计Hook - 用于头部状态栏等
export const useQuickStats = () => {
  return useQuery({
    queryKey: DASHBOARD_QUERY_KEYS.quickStats,
    queryFn: async () => {
      try {
        const result = await dashboardService.getQuickStats();
        return result;
      } catch (error) {
        console.error("Quick stats API error:", error);
        return {
          success: false,
          data: { services: 0, customers: 0, projects: 0, systemHealth: "critical" },
          message: "Failed to load quick stats",
        };
      }
    },
    staleTime: 120000, // 2分钟
    enabled: true,
    select: data => data?.data, // 安全提取data字段
    retry: 2, // 重试次数
    retryDelay: 1000, // 重试延迟
  });
};

// 趋势数据Hook
export const useTrendData = (days = 7) => {
  return useQuery({
    queryKey: DASHBOARD_QUERY_KEYS.trends(days),
    queryFn: async () => {
      try {
        return await dashboardService.getTrendData(days);
      } catch (error) {
        console.error("Trend data error:", error);
        return {
          success: false,
          data: [],
          message: "Failed to load trend data",
        };
      }
    },
    staleTime: 600000, // 10分钟
    enabled: true,
    select: data => data?.data, // 安全提取data字段
    retry: 2, // 重试次数
    retryDelay: 1000, // 重试延迟
  });
};

// 简化的Dashboard数据Hook
export const useDashboardData = () => {
  const statsQuery = useDashboardStats();
  const trendsQuery = useTrendData(7);

  return useMemo(() => {
    return {
      stats: statsQuery.data, // 现在data已经是提取后的数据
      trends: trendsQuery.data, // 现在data已经是提取后的数据
      isLoading: statsQuery.isLoading || trendsQuery.isLoading,
      isError: statsQuery.isError || trendsQuery.isError,
      errors: [statsQuery.error, trendsQuery.error].filter(Boolean),
      statsLoading: statsQuery.isLoading,
      trendsLoading: trendsQuery.isLoading,
      refetchStats: statsQuery.refetch,
      refetchTrends: trendsQuery.refetch,
      refetchAll: () => Promise.all([statsQuery.refetch(), trendsQuery.refetch()]),
    };
  }, [statsQuery, trendsQuery]);
};

// 系统健康状态Hook - 独立缓存和更新
export const useSystemHealth = () => {
  const { data: stats, isLoading, error, refetch } = useDashboardStats();

  // 计算健康评分 - 移到顶层避免嵌套Hook
  const healthScore = useMemo(() => {
    const systemResources = stats?.systemResources;
    const databaseStatus = stats?.databaseStatus;
    const redisStatus = stats?.redisStatus;

    if (!systemResources) return 0;

    const cpuScore = Math.max(0, 100 - systemResources.cpu.usage);
    const memoryScore = Math.max(0, 100 - systemResources.memory.usage);
    const diskScore = Math.max(0, 100 - systemResources.disk.usage);
    const dbScore = databaseStatus?.isConnected ? 100 : 0;
    const redisScore = redisStatus?.isConnected ? 100 : 0;

    return Math.round((cpuScore + memoryScore + diskScore + dbScore + redisScore) / 5);
  }, [stats?.systemResources, stats?.databaseStatus, stats?.redisStatus]);

  return useMemo(() => {
    const systemHealth = stats?.systemHealth || "critical";
    const systemResources = stats?.systemResources;
    const databaseStatus = stats?.databaseStatus;
    const redisStatus = stats?.redisStatus;

    return {
      systemHealth,
      healthScore,
      systemResources,
      databaseStatus,
      redisStatus,
      isLoading,
      error,
      refetch,
    };
  }, [stats, healthScore, isLoading, error, refetch]);
};

// Dashboard实时数据Hook - 用于实时监控
export const useDashboardRealtime = (enabled: boolean = true) => {
  return useDashboardStats({
    refetchInterval: enabled ? 5 * 1000 : undefined, // 5秒实时更新
    staleTime: 0, // 实时数据不缓存
  });
};

// 性能优化：预加载Dashboard数据
export const usePrefetchDashboard = () => {
  const queryClient = useQueryClient();

  return useMemo(
    () => ({
      prefetchStats: () => {
        queryClient.prefetchQuery({
          queryKey: DASHBOARD_QUERY_KEYS.stats,
          queryFn: () => dashboardService.getDashboardStats(),
          staleTime: 5 * 60 * 1000,
        });
      },

      prefetchTrends: (days: number = 7) => {
        queryClient.prefetchQuery({
          queryKey: DASHBOARD_QUERY_KEYS.trends(days),
          queryFn: () => dashboardService.getTrendData(days),
          staleTime: 10 * 60 * 1000,
        });
      },
    }),
    [queryClient]
  );
};

// Dashboard数据无效化工具
export const useInvalidateDashboard = () => {
  const queryClient = useQueryClient();

  return useMemo(
    () => ({
      invalidateStats: () => {
        queryClient.invalidateQueries({ queryKey: DASHBOARD_QUERY_KEYS.stats });
      },

      invalidateQuickStats: () => {
        queryClient.invalidateQueries({ queryKey: DASHBOARD_QUERY_KEYS.quickStats });
      },

      invalidateAll: () => {
        queryClient.invalidateQueries({ queryKey: ["dashboard"] });
      },

      // 强制刷新所有Dashboard数据
      forceRefresh: () => {
        queryClient.refetchQueries({ queryKey: ["dashboard"] });
      },
    }),
    [queryClient]
  );
};
