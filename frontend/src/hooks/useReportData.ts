import { useQuery, useQueryClient } from '@tanstack/react-query'
import { reportService, type OperationEfficiencyMetrics, type CustomerSatisfactionMetrics, type SLAComplianceReport, type ServiceTrendAnalysis, type DashboardData, type QuickStats } from '@/services/report.service'

// ========== 查询键工厂 ==========

const reportKeys = {
  all: ['reports'] as const,
  efficiency: (params?: any) => [...reportKeys.all, 'efficiency', params] as const,
  satisfaction: (params?: any) => [...reportKeys.all, 'satisfaction', params] as const,
  slaCompliance: (params?: any) => [...reportKeys.all, 'sla-compliance', params] as const,
  serviceTrends: (params?: any) => [...reportKeys.all, 'service-trends', params] as const,
  dashboard: (params?: any) => [...reportKeys.all, 'dashboard', params] as const,
  quickStats: () => [...reportKeys.all, 'quick-stats'] as const,
  templates: () => [...reportKeys.all, 'templates'] as const
}

// ========== 自定义Hook ==========

/**
 * 获取运维效率分析数据
 */
export const useOperationEfficiency = (params?: {
  start?: string
  end?: string
  days?: number
  enabled?: boolean
}) => {
  return useQuery({
    queryKey: reportKeys.efficiency(params),
    queryFn: () => reportService.getOperationEfficiency(params),
    enabled: params?.enabled !== false,
    staleTime: 5 * 60 * 1000, // 5分钟内认为数据新鲜
    gcTime: 30 * 60 * 1000, // 30分钟后清理缓存
    retry: 2,
    refetchOnWindowFocus: false
  })
}

/**
 * 获取客户满意度分析数据
 */
export const useCustomerSatisfaction = (params?: {
  start?: string
  end?: string
  days?: number
  enabled?: boolean
}) => {
  return useQuery({
    queryKey: reportKeys.satisfaction(params),
    queryFn: () => reportService.getCustomerSatisfaction(params),
    enabled: params?.enabled !== false,
    staleTime: 5 * 60 * 1000,
    gcTime: 30 * 60 * 1000,
    retry: 2,
    refetchOnWindowFocus: false
  })
}

/**
 * 获取SLA合规性报告
 */
export const useSLACompliance = (params?: {
  start?: string
  end?: string
  days?: number
  enabled?: boolean
}) => {
  return useQuery({
    queryKey: reportKeys.slaCompliance(params),
    queryFn: () => reportService.getSLACompliance(params),
    enabled: params?.enabled !== false,
    staleTime: 5 * 60 * 1000,
    gcTime: 30 * 60 * 1000,
    retry: 2,
    refetchOnWindowFocus: false
  })
}

/**
 * 获取服务趋势分析数据
 */
export const useServiceTrends = (params?: {
  start?: string
  end?: string
  days?: number
  enabled?: boolean
}) => {
  return useQuery({
    queryKey: reportKeys.serviceTrends(params),
    queryFn: () => reportService.getServiceTrends(params),
    enabled: params?.enabled !== false,
    staleTime: 5 * 60 * 1000,
    gcTime: 30 * 60 * 1000,
    retry: 2,
    refetchOnWindowFocus: false
  })
}

/**
 * 获取综合仪表板数据
 */
export const useDashboardData = (params?: {
  start?: string
  end?: string
  days?: number
  enabled?: boolean
}) => {
  return useQuery({
    queryKey: reportKeys.dashboard(params),
    queryFn: () => reportService.getDashboardData(params),
    enabled: params?.enabled !== false,
    staleTime: 3 * 60 * 1000, // 3分钟缓存
    gcTime: 15 * 60 * 1000, // 15分钟清理
    retry: 3,
    refetchOnWindowFocus: false
  })
}

/**
 * 获取快速统计数据
 */
export const useQuickStats = (enabled: boolean = true) => {
  return useQuery({
    queryKey: reportKeys.quickStats(),
    queryFn: () => reportService.getQuickStats(),
    enabled,
    staleTime: 2 * 60 * 1000, // 2分钟缓存
    gcTime: 10 * 60 * 1000, // 10分钟清理
    retry: 3,
    refetchOnWindowFocus: true, // 快速统计允许窗口聚焦时刷新
    refetchInterval: 5 * 60 * 1000 // 每5分钟自动刷新
  })
}

/**
 * 获取报表模板
 */
export const useReportTemplates = () => {
  return useQuery({
    queryKey: reportKeys.templates(),
    queryFn: () => reportService.getReportTemplates(),
    staleTime: 30 * 60 * 1000, // 30分钟缓存
    gcTime: 60 * 60 * 1000, // 1小时清理
    retry: 2,
    refetchOnWindowFocus: false
  })
}

// ========== 缓存管理Hook ==========

/**
 * 报表数据缓存管理
 */
export const useReportCache = () => {
  const queryClient = useQueryClient()

  const invalidateAllReports = () => {
    queryClient.invalidateQueries({ queryKey: reportKeys.all })
  }

  const invalidateEfficiency = () => {
    queryClient.invalidateQueries({ queryKey: reportKeys.all })
  }

  const invalidateSatisfaction = () => {
    queryClient.invalidateQueries({ queryKey: [...reportKeys.all, 'satisfaction'] })
  }

  const invalidateSLA = () => {
    queryClient.invalidateQueries({ queryKey: [...reportKeys.all, 'sla-compliance'] })
  }

  const invalidateTrends = () => {
    queryClient.invalidateQueries({ queryKey: [...reportKeys.all, 'service-trends'] })
  }

  const prefetchDashboard = (params?: any) => {
    queryClient.prefetchQuery({
      queryKey: reportKeys.dashboard(params),
      queryFn: () => reportService.getDashboardData(params),
      staleTime: 3 * 60 * 1000
    })
  }

  return {
    invalidateAllReports,
    invalidateEfficiency,
    invalidateSatisfaction,
    invalidateSLA,
    invalidateTrends,
    prefetchDashboard
  }
}

// ========== 导出功能Hook ==========

/**
 * 数据导出功能
 */
export const useReportExport = () => {
  const downloadExcel = async (data: any, fileName: string) => {
    try {
      const blob = await reportService.exportExcel(data, fileName)
      reportService.downloadFile(blob, fileName, 'excel')
    } catch (error) {
      console.error('导出Excel失败:', error)
      throw error
    }
  }

  const downloadCSV = async (data: any[], fileName: string) => {
    try {
      const blob = await reportService.exportCSV(data, fileName)
      reportService.downloadFile(blob, fileName, 'csv')
    } catch (error) {
      console.error('导出CSV失败:', error)
      throw error
    }
  }

  const downloadPDF = async (serviceId: string, fileName?: string) => {
    try {
      const blob = await reportService.exportPDF(serviceId)
      reportService.downloadFile(blob, fileName || `service-report-${serviceId}`, 'pdf')
    } catch (error) {
      console.error('导出PDF失败:', error)
      throw error
    }
  }

  return {
    downloadExcel,
    downloadCSV,
    downloadPDF
  }
}