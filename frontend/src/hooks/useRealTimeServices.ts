import { useCallback, useEffect, useState, useRef } from "react";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { serviceService } from "@/services/service";
import { Service, ServiceSearchParams } from "shared/types";
import { Toast } from "@douyinfe/semi-ui";

interface UseRealTimeServicesOptions {
  searchParams: ServiceSearchParams & { page?: number; limit?: number };
  enabled?: boolean;
  refreshInterval?: number;
  backgroundRefreshInterval?: number;
}

interface RealTimeServiceData {
  data:
    | {
        items: Service[];
        pagination: {
          page: number;
          limit: number;
          total: number;
          pages: number;
        };
      }
    | undefined;
  isLoading: boolean;
  isError: boolean;
  error: Error | null;
  refetch: () => void;
  newTicketsCount: number;
  hasDataChanged: boolean;
  clearNewTickets: () => void;
  pauseRealTime: () => void;
  resumeRealTime: () => void;
  isRealTimePaused: boolean;
}

export function useRealTimeServices({
  searchParams,
  enabled = true,
  refreshInterval = 15000, // 15秒检测新工单
  backgroundRefreshInterval = 60000, // 页面在后台时60秒刷新
}: UseRealTimeServicesOptions): RealTimeServiceData {
  const queryClient = useQueryClient();
  const [newTicketsCount, setNewTicketsCount] = useState(0);
  const [hasDataChanged, setHasDataChanged] = useState(false);
  const [isRealTimePaused, setIsRealTimePaused] = useState(false);
  const [isPageVisible, setIsPageVisible] = useState(true);

  // 存储上一次的数据用于比较
  const previousDataRef = useRef<Service[]>([]);
  const previousTotalRef = useRef<number>(0);

  // 监听页面可见性
  useEffect(() => {
    const handleVisibilityChange = () => {
      setIsPageVisible(!document.hidden);
    };

    document.addEventListener("visibilitychange", handleVisibilityChange);
    return () => {
      document.removeEventListener("visibilitychange", handleVisibilityChange);
    };
  }, []);

  // 动态计算刷新间隔
  const currentRefreshInterval = isPageVisible ? refreshInterval : backgroundRefreshInterval;

  // 主要的数据查询
  const { data, isLoading, isError, error, refetch } = useQuery({
    queryKey: ["services", searchParams],
    queryFn: () => {
      const { status, priority, category, ...rest } = searchParams;
      const finalParams = {
        ...rest,
        ...(status && { status }),
        ...(priority && { priority }),
        ...(category && { category }),
      };
      return serviceService.getServices(finalParams);
    },
    select: response => response.data,
    enabled: enabled && !isRealTimePaused,
    refetchInterval: currentRefreshInterval,
    refetchIntervalInBackground: true,
    // 启用后台刷新
    refetchOnWindowFocus: true,
    // 当页面重新获得焦点时刷新
    staleTime: 10000, // 10秒内的数据被认为是新鲜的
  });

  // 检测数据变化
  useEffect(() => {
    if (!data?.items || !Array.isArray(data.items)) return;

    const currentItems = data.items;
    const currentTotal = data.pagination?.total || 0;
    const previousItems = previousDataRef.current;
    const previousTotal = previousTotalRef.current;

    // 首次加载，不显示变更提示
    if (previousItems.length === 0) {
      previousDataRef.current = currentItems;
      previousTotalRef.current = currentTotal;
      return;
    }

    // 检测新工单
    if (currentTotal > previousTotal) {
      const newCount = currentTotal - previousTotal;
      setNewTicketsCount(prev => prev + newCount);
      setHasDataChanged(true);

      // 显示新工单提示
      Toast.success({
        content: `发现 ${newCount} 个新工单`,
        duration: 3,
        icon: "🎫",
      });
    }

    // 检测工单状态变化
    const changedTickets = currentItems.filter(currentItem => {
      const previousItem = previousItems.find(prev => prev.id === currentItem.id);
      return (
        previousItem &&
        (previousItem.status !== currentItem.status ||
          previousItem.priority !== currentItem.priority ||
          previousItem.assignedTo !== currentItem.assignedTo)
      );
    });

    if (changedTickets.length > 0) {
      setHasDataChanged(true);

      // 显示状态变更提示
      if (isPageVisible) {
        Toast.info({
          content: `${changedTickets.length} 个工单状态已更新`,
          duration: 3,
        });
      }
    }

    // 更新引用
    previousDataRef.current = currentItems;
    previousTotalRef.current = currentTotal;
  }, [data, isPageVisible]);

  // 清除新工单计数
  const clearNewTickets = useCallback(() => {
    setNewTicketsCount(0);
    setHasDataChanged(false);
  }, []);

  // 暂停实时更新
  const pauseRealTime = useCallback(() => {
    setIsRealTimePaused(true);
    Toast.info("已暂停自动刷新");
  }, []);

  // 恢复实时更新
  const resumeRealTime = useCallback(() => {
    setIsRealTimePaused(false);
    Toast.success("已恢复自动刷新");
    // 立即刷新一次
    refetch();
  }, [refetch]);

  // 手动刷新
  const handleRefresh = useCallback(() => {
    refetch();
    Toast.success("数据已刷新");
    setHasDataChanged(false);
  }, [refetch]);

  return {
    data: data as any,
    isLoading,
    isError,
    error,
    refetch: handleRefresh,
    newTicketsCount,
    hasDataChanged,
    clearNewTickets,
    pauseRealTime,
    resumeRealTime,
    isRealTimePaused,
  };
}
