import { create } from "zustand";
import { devtools, persist } from "zustand/middleware";
import { immer } from "zustand/middleware/immer";
import type { BreadcrumbItem, Theme } from "@/types";

interface UIState {
  breadcrumbs: BreadcrumbItem[];
  sidebarCollapsed: boolean;
  theme: Theme;
}

interface UIActions {
  // Breadcrumbs
  setBreadcrumbs: (items: BreadcrumbItem[]) => void;
  pushBreadcrumb: (item: BreadcrumbItem) => void;
  popBreadcrumb: () => void;
  clearBreadcrumbs: () => void;

  // Sidebar
  toggleSidebar: () => void;
  setSidebarCollapsed: (collapsed: boolean) => void;

  // Theme
  setTheme: (theme: Theme) => void;
  initTheme: () => void;
}

export const useUIStore = create<UIState & UIActions>()(
  devtools(
    persist(
      immer((set, get) => ({
        // State
        breadcrumbs: [],
        sidebarCollapsed: false,
        theme: "light" as Theme,

        // Breadcrumb actions
        setBreadcrumbs: (items: BreadcrumbItem[]) => {
          set(state => {
            state.breadcrumbs = items;
          });
        },

        pushBreadcrumb: (item: BreadcrumbItem) => {
          set(state => {
            state.breadcrumbs.push(item);
          });
        },

        popBreadcrumb: () => {
          set(state => {
            state.breadcrumbs.pop();
          });
        },

        clearBreadcrumbs: () => {
          set(state => {
            state.breadcrumbs = [];
          });
        },

        // Sidebar actions
        toggleSidebar: () => {
          set(state => {
            state.sidebarCollapsed = !state.sidebarCollapsed;
          });
        },

        setSidebarCollapsed: (collapsed: boolean) => {
          set(state => {
            state.sidebarCollapsed = collapsed;
          });
        },

        // Theme actions
        setTheme: (theme: Theme) => {
          set(state => {
            state.theme = theme;
          });

          // Apply theme to DOM
          if (typeof window !== "undefined") {
            const root = window.document.documentElement;
            root.classList.remove("light", "dark");

            if (theme === "auto") {
              const prefersDark = window.matchMedia("(prefers-color-scheme: dark)").matches;
              root.classList.add(prefersDark ? "dark" : "light");
            } else {
              root.classList.add(theme);
            }
          }
        },

        initTheme: () => {
          const { theme, setTheme } = get();
          setTheme(theme); // Apply current theme to DOM
        },
      })),
      {
        name: "ui-storage",
        partialize: state => ({
          sidebarCollapsed: state.sidebarCollapsed,
          theme: state.theme,
        }),
      }
    ),
    { name: "UIStore" }
  )
);
