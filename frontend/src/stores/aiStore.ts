/**
 * AI 配置和状态管理 Store
 */

import { create } from "zustand";
import { persist, createJSONStorage } from "zustand/middleware";
import { aiAnalysisService } from "@/services/aiAnalysis";
import { getCurrentAIConfiguration } from "@/services/aiManagement";
import type { AIConfiguration, BackendAIConfiguration, AIMode, AISuggestion, AIAnalysisResponse } from "@/types/ai";
import { DEFAULT_AI_CONFIG } from "@/types/ai";

interface AIStore {
  // AI 配置
  config: AIConfiguration;
  loading: boolean;
  error: string | null;

  // AI 分析状态
  analyzing: boolean;
  lastAnalysis: AIAnalysisResponse | null;
  suggestions: AISuggestion[];
  analysisError: string | null;

  // AI 建议历史
  suggestionHistory: Array<{
    timestamp: string;
    request: string;
    suggestions: AISuggestion[];
    adopted: Record<string, boolean>;
  }>;

  // Actions - 配置管理
  loadConfiguration: (userId?: string) => Promise<void>;
  updateConfiguration: (config: Partial<AIConfiguration>, userId?: string) => Promise<void>;
  switchMode: (mode: AIMode, userId?: string) => Promise<void>;

  // Actions - AI 分析
  analyzeContent: (description: string, contextData?: any) => Promise<void>;
  adoptSuggestion: (field: string, value: string) => void;
  adoptAllSuggestions: () => Record<string, string>;
  clearSuggestions: () => void;

  // Actions - 历史管理
  addToHistory: (request: string, suggestions: AISuggestion[]) => void;
  clearHistory: () => void;

  // Actions - 错误处理
  setError: (error: string | null) => void;
  clearError: () => void;
}

export const useAIStore = create<AIStore>()(
  persist(
    (set, get) => ({
      // 初始状态
      config: DEFAULT_AI_CONFIG,
      loading: false,
      error: null,

      analyzing: false,
      lastAnalysis: null,
      suggestions: [],
      analysisError: null,

      suggestionHistory: [],

      // 加载AI配置
      loadConfiguration: async (userId?: string) => {
        set({ loading: true, error: null });
        try {
          // 从后端获取当前有效的AI配置
          const response = await getCurrentAIConfiguration();

          if (response.success && response.data) {
            const { data: backendConfig } = response;
            console.log(backendConfig);
            // 转换后端配置为前端配置格式
            const frontendConfig: AIConfiguration = {
              mode: ((backendConfig as any).mode === "USER_TRIGGERED"
                ? "user-triggered"
                : (backendConfig as any).mode === "AGGRESSIVE"
                  ? "aggressive"
                  : "disabled") as AIMode,
              enabledFields: (backendConfig as any).enabledFields || {
                title: true,
                category: true,
                priority: true,
                slaTemplate: true,
              },
              autoFillThreshold: ((backendConfig as any).autoFillThreshold || 80) / 100, // 后端是0-100，前端是0-1
              userPreferences: {
                showReasoningTooltips: true,
                enableSuggestionHistory: true,
                showConfidenceScores: true,
              },
            };

            set({ config: frontendConfig, loading: false });
          } else {
            // 如果没有找到后端配置，使用默认配置
            console.warn('未找到有效的AI配置，使用默认配置');
            set({ config: DEFAULT_AI_CONFIG, loading: false });
          }
        } catch (error: any) {
          console.error('加载AI配置失败:', error);
          // 出错时使用默认配置
          set({
            config: DEFAULT_AI_CONFIG,
            loading: false,
            error: `加载AI配置失败: ${error.message}`
          });
        }
      },

      // 更新AI配置 - 现在只更新本地状态
      updateConfiguration: async (configUpdate: Partial<AIConfiguration>, userId?: string) => {
        set({ loading: true, error: null });
        try {
          const currentConfig = get().config;
          const newConfig = { ...currentConfig, ...configUpdate };
          set({ config: newConfig, loading: false });
        } catch (error: any) {
          set({ error: error.message, loading: false });
        }
      },

      // 切换AI模式
      switchMode: async (mode: AIMode, userId?: string) => {
        const currentConfig = get().config;
        await get().updateConfiguration({ mode }, userId);
      },

      // AI内容分析
      analyzeContent: async (description: string, contextData?: any) => {
        if (!description.trim()) {
          set({ analysisError: "描述内容不能为空" });
          return;
        }

        set({ analyzing: true, analysisError: null, suggestions: [] });

        try {
          const response = await aiAnalysisService.analyzeTicketContent({
            description,
            contextData,
          });

          if (response.success && response.data) {
            set({
              lastAnalysis: response.data,
              suggestions: response.data.suggestions,
              analyzing: false,
            });

            // 添加到历史记录
            get().addToHistory(description, response.data.suggestions);
          } else {
            set({
              analysisError: response.message || "AI 分析失败",
              analyzing: false,
            });
          }
        } catch (error: any) {
          set({
            analysisError: error.message || "网络错误，请重试",
            analyzing: false,
          });
        }
      },

      // 采用单个建议
      adoptSuggestion: (field: string, value: string) => {
        const history = get().suggestionHistory;
        if (history.length > 0) {
          const lastEntry = history[history.length - 1];
          lastEntry.adopted[field] = true;
        }
      },

      // 采用所有建议
      adoptAllSuggestions: () => {
        const suggestions = get().suggestions;
        const adoptedValues: Record<string, string> = {};

        suggestions.forEach(suggestion => {
          const { config } = get();

          // 检查字段是否启用
          if (config.enabledFields[suggestion.field as keyof typeof config.enabledFields]) {
            // 检查置信度是否达到阈值
            if (suggestion.confidence >= config.autoFillThreshold) {
              adoptedValues[suggestion.field] = suggestion.suggested;
              get().adoptSuggestion(suggestion.field, suggestion.suggested);
            }
          }
        });

        return adoptedValues;
      },

      // 清空建议
      clearSuggestions: () => {
        set({
          suggestions: [],
          lastAnalysis: null,
          analysisError: null,
        });
      },

      // 添加到历史记录
      addToHistory: (request: string, suggestions: AISuggestion[]) => {
        const { config, suggestionHistory } = get();

        if (!config.userPreferences.enableSuggestionHistory) {
          return;
        }

        const newEntry = {
          timestamp: new Date().toISOString(),
          request: request.substring(0, 200), // 限制长度
          suggestions,
          adopted: {} as Record<string, boolean>,
        };

        // 限制历史记录数量（最多保存20条）
        const updatedHistory = [newEntry, ...suggestionHistory].slice(0, 20);

        set({ suggestionHistory: updatedHistory });
      },

      // 清空历史记录
      clearHistory: () => {
        set({ suggestionHistory: [] });
      },

      // 设置错误
      setError: (error: string | null) => {
        set({ error });
      },

      // 清空错误
      clearError: () => {
        set({ error: null, analysisError: null });
      },
    }),
    {
      name: "ai-store",
      storage: createJSONStorage(() => localStorage),
      // 只持久化配置和历史记录，不持久化临时状态
      partialize: state => ({
        config: state.config,
        suggestionHistory: state.suggestionHistory,
      }),
    }
  )
);

// 选择器函数
export const aiStoreSelectors = {
  // 获取当前AI模式
  getCurrentMode: (state: AIStore) => state.config.mode,

  // 检查是否启用激进模式
  isAggressiveMode: (state: AIStore) => state.config.mode === "aggressive",

  // 检查是否启用用户触发模式
  isUserTriggeredMode: (state: AIStore) => state.config.mode === "user-triggered",

  // 检查AI是否被禁用
  isAIDisabled: (state: AIStore) => state.config.mode === "disabled",

  // 获取可用的建议
  getAvailableSuggestions: (state: AIStore) => {
    return state.suggestions.filter(
      s => state.config.enabledFields[s.field as keyof typeof state.config.enabledFields]
    );
  },

  // 获取高置信度建议
  getHighConfidenceSuggestions: (state: AIStore) => {
    return state.suggestions.filter(
      s =>
        s.confidence >= state.config.autoFillThreshold &&
        state.config.enabledFields[s.field as keyof typeof state.config.enabledFields]
    );
  },

  // 检查是否正在加载
  isLoading: (state: AIStore) => state.loading || state.analyzing,

  // 检查是否有错误
  hasError: (state: AIStore) => !!(state.error || state.analysisError),

  // 获取错误信息
  getError: (state: AIStore) => state.error || state.analysisError,
};

export default useAIStore;
