import { create } from "zustand";
import { devtools, persist } from "zustand/middleware";
import { immer } from "zustand/middleware/immer";
import { enableMapSet } from "immer";
import { authService } from "@/services";
import { ApiError } from "@/utils/request";
import type { User, LoginCredentials, LoginResult } from "@/types";

enableMapSet();

interface AuthState {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  loading: boolean;
  permissionCache: Map<string, boolean>;
  roleCache: Map<string, boolean>;
}

interface AuthActions {
  login: (credentials: LoginCredentials) => Promise<LoginResult>;
  logout: () => Promise<void>;
  checkAuth: () => Promise<boolean>;
  updateToken: (newToken: string) => Promise<void>;
  clearAuthAndRedirect: () => void;
  hasPermission: (permission: string) => boolean;
  hasRole: (roleName: string) => boolean;
  hasAnyPermission: (permissions: string[]) => boolean;
  hasAllPermissions: (permissions: string[]) => boolean;
  hasAnyRole: (roles: string[]) => boolean;
  hasAllRoles: (roles: string[]) => boolean;
}

export const useAuthStore = create<AuthState & AuthActions>()(
  devtools(
    persist(
      immer((set, get) => ({
        user: null,
        token: null,
        isAuthenticated: false,
        loading: false,
        permissionCache: new Map(),
        roleCache: new Map(),

        clearAuthAndRedirect: () => {
          set(state => {
            state.user = null;
            state.token = null;
            state.isAuthenticated = false;
            state.loading = false;
            state.permissionCache.clear();
            state.roleCache.clear();
          });

          // 延迟执行重定向，确保状态更新完成
          setTimeout(() => {
            if (typeof window !== "undefined" && window.location.pathname !== "/login") {
              const currentPath = window.location.pathname + window.location.search;
              const redirectUrl = `/login?redirect=${encodeURIComponent(currentPath)}`;
              console.log("认证失败，重定向到登录页面:", redirectUrl);
              window.location.href = redirectUrl;
            }
          }, 100);
        },

        login: async credentials => {
          set({ loading: true });
          try {
            const response = await authService.login(credentials);
            if (response.success) {
              set({
                user: response.data.user,
                token: response.data.token,
                isAuthenticated: true,
                loading: false,
              });
              return { success: true, message: "登录成功" };
            }
            set({ loading: false });
            return { success: false, message: response.message || "登录失败" };
          } catch (error) {
            set({ loading: false });
            return { success: false, message: (error as ApiError).message || "登录异常" };
          }
        },

        logout: async () => {
          try {
            await authService.logout();
          } finally {
            get().clearAuthAndRedirect();
          }
        },

        updateToken: async (newToken: string) => {
          set(state => {
            state.token = newToken;
          });
        },

        checkAuth: async () => {
          const { token } = get();
          if (!token) return false;

          try {
            const response = await authService.me();
            if (response.success) {
              set({ user: response.data, isAuthenticated: true });
              return true;
            }
            return false;
          } catch (error) {
            if (error instanceof ApiError && error.status === 401) {
              // 401错误由后端自动刷新机制处理，如果到达这里说明refresh token也过期了
              get().clearAuthAndRedirect();
              return false;
            }
            // 其他错误也清除认证状态
            get().clearAuthAndRedirect();
            return false;
          }
        },

        hasPermission: permission => {
          const { user, permissionCache } = get();
          if (!user) return false;
          if (permissionCache.has(permission)) return permissionCache.get(permission)!;

          const hasPerm =
            user.role.permissions.includes("admin:all") ||
            user.role.permissions.includes(permission);
          set(state => {
            state.permissionCache.set(permission, hasPerm);
          });
          return hasPerm;
        },

        hasRole: roleName => {
          const { user, roleCache } = get();
          if (!user) return false;
          if (roleCache.has(roleName)) return roleCache.get(roleName)!;

          const has = user.role.name === roleName;
          set(state => {
            state.roleCache.set(roleName, has);
          });
          return has;
        },

        hasAnyPermission: permissions => permissions.some(p => get().hasPermission(p)),
        hasAllPermissions: permissions => permissions.every(p => get().hasPermission(p)),
        hasAnyRole: roles => roles.some(r => get().hasRole(r)),
        hasAllRoles: roles => roles.every(r => get().hasRole(r)),
      })),
      {
        name: "auth-storage",
        partialize: state => ({
          user: state.user,
          token: state.token,
          isAuthenticated: state.isAuthenticated,
        }),
      }
    ),
    { name: "AuthStore" }
  )
);

if (typeof window !== "undefined") {
  (window as any).__AUTH_STORE__ = useAuthStore;
}
