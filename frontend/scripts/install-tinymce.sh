#!/bin/bash

# TinyMCE 安装脚本
# 下载并解压 TinyMCE 社区版到 public/tinymce 目录

set -e

FRONTEND_DIR="$(cd "$(dirname "$0")/.." && pwd)"
PUBLIC_DIR="$FRONTEND_DIR/public"
TINYMCE_DIR="$PUBLIC_DIR/tinymce"
TINYMCE_VERSION="7.6.0"
TINYMCE_URL="https://download.tiny.cloud/tinymce/community/tinymce_${TINYMCE_VERSION}.zip"
TEMP_FILE="$PUBLIC_DIR/tinymce.zip"

echo "开始安装 TinyMCE 社区版 v${TINYMCE_VERSION}..."

# 创建 public 目录（如果不存在）
mkdir -p "$PUBLIC_DIR"

# 检查是否已经安装
if [ -d "$TINYMCE_DIR" ]; then
    echo "TinyMCE 已经安装在 $TINYMCE_DIR"
    echo "如需重新安装，请先删除该目录：rm -rf $TINYMCE_DIR"
    exit 0
fi

# 下载 TinyMCE
echo "正在下载 TinyMCE 社区版..."
curl -L -o "$TEMP_FILE" "$TINYMCE_URL"

# 解压
echo "正在解压 TinyMCE..."
cd "$PUBLIC_DIR"
unzip -q "$TEMP_FILE"

# 清理临时文件
rm "$TEMP_FILE"

echo "TinyMCE 安装完成！"
echo "安装路径：$TINYMCE_DIR"
echo "主文件：$TINYMCE_DIR/js/tinymce/tinymce.min.js"
