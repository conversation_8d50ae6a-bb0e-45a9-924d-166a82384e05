{"$schema": "http://json.schemastore.org/tsconfig", "compilerOptions": {"allowJs": false, "allowSyntheticDefaultImports": true, "alwaysStrict": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "isolatedModules": true, "jsx": "react-jsx", "lib": ["dom", "dom.iterable", "esnext"], "module": "esnext", "moduleResolution": "bundler", "noEmit": true, "noFallthroughCasesInSwitch": true, "noImplicitReturns": false, "resolveJsonModule": true, "skipLibCheck": true, "strict": true, "target": "es2020", "useDefineForClassFields": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"], "~/*": ["./src/*"], "shared/*": ["../shared/*"]}}, "include": ["src/**/*.ts", "src/**/*.tsx", "src/**/*.d.ts"], "exclude": ["node_modules", "dist", "**/__tests__/**", "**/__coverage__/**", "**/__mocks__/**"]}