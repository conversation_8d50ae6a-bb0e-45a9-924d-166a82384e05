# 运维服务管理系统 - 前端

基于 React + Rsbuild 构建的现代化运维服务管理系统前端应用。

## 技术栈

- **框架**: React 18 + TypeScript
- **构建工具**: Rsbuild (基于 Rspack)
- **路由**: React Router v6
- **状态管理**: Zustand + Immer
- **样式**: Tailwind CSS
- **开发工具**: ESLint + Prettier
- **测试**: Vitest

## 项目结构

```
src/
├── components/           # 组件
│   ├── common/          # 通用组件
│   ├── layout/          # 布局组件
│   └── guards/          # 路由守卫
├── pages/               # 页面组件
│   ├── auth/           # 认证页面
│   ├── customers/      # 客户管理
│   ├── projects/       # 项目档案
│   ├── services/       # 服务工单
│   └── admin/          # 系统管理
├── stores/              # 状态管理
├── routes/              # 路由配置
├── hooks/               # 自定义 Hooks
├── utils/               # 工具函数
├── types/               # 类型定义
└── assets/              # 静态资源
    ├── styles/         # 样式文件
    └── images/         # 图片资源
```

## 开发指南

### 安装依赖

```bash
npm install
```

### 开发模式

```bash
npm run dev
```

### 构建项目

```bash
npm run build
```

### 代码检查

```bash
npm run lint
npm run lint:fix
npm run type-check
```

### 代码格式化

```bash
npm run format
npm run format:check
```

### 测试

```bash
npm run test
```

## 核心功能

### 1. 认证系统

- JWT Token 认证
- 用户登录/登出
- 路由守卫

### 2. 状态管理

- Zustand 轻量级状态管理
- Immer 不可变更新
- 持久化存储

### 3. UI 组件

- 响应式布局
- 深色/浅色主题切换
- 通知系统
- 模态框管理

### 4. 路由系统

- 嵌套路由
- 懒加载组件
- 面包屑导航

## 开发规范

### 组件命名

- 组件文件使用 PascalCase
- 组件目录使用 kebab-case
- Hook 函数以 `use` 开头

### 类型定义

- 接口使用 PascalCase
- 类型别名使用 PascalCase
- 枚举使用 UPPER_CASE

### 样式规范

- 使用 Tailwind CSS 原子类
- 自定义样式放在 `@layer components`
- 响应式设计遵循移动优先原则

## 部署

### 生产构建

```bash
npm run build
```

构建产物位于 `dist/` 目录，可以部署到任何静态文件服务器。

### 环境变量

项目支持以下环境变量：

- `NODE_ENV`: 环境类型 (development/production)
- `BUNDLE_ANALYZE`: 启用构建分析

## 浏览器支持

- Chrome >= 87
- Firefox >= 78
- Safari >= 14
- Edge >= 88

## License

MIT
