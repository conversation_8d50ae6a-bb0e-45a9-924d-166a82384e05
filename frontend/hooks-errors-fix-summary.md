# React Hooks 错误修复总结

## 修复的错误

### 1. "Rendered fewer hooks than expected" 错误

**原因**: DashboardPage.tsx中`getStatusName`和`getStatusColor`函数在`useMemo` Hook之后才声明，造成JavaScript临时死区错误。

**解决方案**:

- 将函数声明移到所有Hook调用之前
- 删除重复的函数声明

**修复位置**: `/frontend/src/pages/DashboardPage.tsx`

### 2. "Cannot read properties of undefined (reading 'length')" 错误

**原因**: useQuery Hook的依赖数组和数据访问存在问题。

**解决方案**:

- 使用预定义的查询键常量 `DASHBOARD_QUERY_KEYS`
- 安全的数据访问：`data?.data`
- 添加重试机制和错误处理

**修复位置**: `/frontend/src/hooks/useDashboard.ts`

### 3. Hook调用顺序问题

**原因**: SystemHealthCard中存在嵌套的useMemo Hook调用。

**解决方案**:

- 将所有Hook调用移到组件顶层
- 避免在其他Hook内部调用Hook

**修复位置**: `/frontend/src/hooks/useDashboard.ts` - `useSystemHealth`

### 4. 早期return语句问题

**原因**: SystemHealthCard组件在Hook调用之前有条件return语句。

**解决方案**:

- 将所有Hook调用移到组件最顶部
- 将条件渲染逻辑移到Hook调用之后

**修复位置**: `/frontend/src/components/dashboard/SystemHealthCard.tsx`

## 新增的改进

### 1. 错误边界组件

- 创建通用的ErrorBoundary组件
- 添加useErrorHandler Hook用于函数组件错误处理
- 为Header组件添加错误边界保护

### 2. 更好的错误处理

- 添加重试机制 (`retry: 2, retryDelay: 1000`)
- 改善错误状态UI显示
- 更完善的数据验证

## React Hooks 规则总结

### 必须遵守的规则:

1. **Hook调用顺序**: 所有Hook调用必须在组件顶层，不能在条件语句、循环或嵌套函数中调用
2. **变量声明顺序**: 函数必须在使用之前声明，避免临时死区错误
3. **早期return**: 任何条件性的return语句必须在所有Hook调用之后
4. **依赖数组**: useEffect、useMemo等Hook的依赖数组必须包含所有用到的变量

### 正确的组件结构:

```tsx
function MyComponent() {
  // 1. 所有Hook调用在最顶部
  const someData = useCustomHook();
  const memoizedValue = useMemo(() => {
    /* ... */
  }, [dependency]);

  // 2. 普通函数声明
  const helperFunction = param => {
    // 处理逻辑
  };

  // 3. 条件检查和早期return
  if (error) {
    return <ErrorComponent />;
  }

  // 4. 正常渲染
  return <div>...</div>;
}
```

## 验证步骤

1. 检查控制台是否还有React Hook相关错误
2. 验证Header组件正常显示系统状态
3. 确认Dashboard页面数据加载正常
4. 测试错误边界是否正确处理组件错误

## 相关文件

- `/frontend/src/hooks/useDashboard.ts` - Dashboard数据管理Hook
- `/frontend/src/components/layout/Header.tsx` - 头部组件
- `/frontend/src/pages/DashboardPage.tsx` - Dashboard页面
- `/frontend/src/components/dashboard/SystemHealthCard.tsx` - 系统健康状态卡片
- `/frontend/src/components/common/ErrorBoundary.tsx` - 错误边界组件

所有修复都已通过ESLint检查，没有linting错误。
