# 富文本内容安全显示方案

## 安全问题

之前使用 `dangerouslySetInnerHTML` 直接渲染 HTML 内容存在 XSS（跨站脚本攻击）安全漏洞：

```tsx
// ❌ 不安全的方式
<div dangerouslySetInnerHTML={{ __html: content }} />
```

恶意用户可能注入如下内容：

```html
<script>
  alert("XSS攻击!");
</script>
<img src="x" onerror="alert('XSS攻击!')" />
<a href="javascript:alert('XSS攻击!')">点击这里</a>
```

## 解决方案

我们提供了两种安全的富文本内容显示方案：

### 1. TinyMCE 预览模式（推荐）

**组件**: `RichTextDisplay`

**特点**:

- ✅ 使用 TinyMCE 的安全渲染引擎
- ✅ 完全禁用编辑功能
- ✅ 自动过滤危险内容
- ✅ 保持富文本格式完整
- ✅ 与编辑器使用相同的渲染逻辑
- ⚠️ 需要加载 TinyMCE 库

**使用方式**:

```tsx
import { RichTextDisplay } from "@/components/common";

<RichTextDisplay content={htmlContent} placeholder="暂无内容" />;
```

### 2. 白名单过滤（轻量级）

**组件**: `SafeHTMLDisplay`

**特点**:

- ✅ 轻量级，无需额外依赖
- ✅ 使用白名单过滤危险标签和属性
- ✅ 移除所有事件处理器
- ✅ 过滤 javascript: 协议
- ⚠️ 可能过滤掉一些合法的复杂格式

**使用方式**:

```tsx
import { SafeHTMLDisplay } from "@/components/common";

<SafeHTMLDisplay content={htmlContent} placeholder="暂无内容" />;
```

## 安全措施对比

| 安全措施       | TinyMCE 预览模式 | 白名单过滤 |
| -------------- | ---------------- | ---------- |
| XSS 防护       | ✅ 完全          | ✅ 基本    |
| 脚本注入防护   | ✅ 完全          | ✅ 完全    |
| 事件处理器过滤 | ✅ 完全          | ✅ 完全    |
| 危险标签过滤   | ✅ 完全          | ✅ 完全    |
| 格式保真度     | ✅ 高            | ⚠️ 中等    |
| 性能影响       | ⚠️ 中等          | ✅ 最小    |

## 使用建议

### 何时使用 TinyMCE 预览模式

- 内容较为复杂，包含多种格式
- 需要完美的渲染效果
- 安全要求极高
- 页面上富文本显示组件数量较少

### 何时使用白名单过滤

- 内容相对简单
- 对性能要求较高
- 页面上有大量富文本显示组件
- 需要快速渲染

## 当前项目使用情况

目前项目中已将所有富文本显示组件替换为 `RichTextDisplay`（TinyMCE 预览模式）：

- ✅ 工单详情页面的描述、解决方案、客户反馈
- ✅ 评论内容显示
- ✅ 工作日志内容显示

## 迁移指南

如需从不安全的 `dangerouslySetInnerHTML` 迁移：

```tsx
// ❌ 替换前
<div dangerouslySetInnerHTML={{ __html: content }} />

// ✅ 替换后（推荐）
<RichTextDisplay content={content} />

// ✅ 或者（轻量级）
<SafeHTMLDisplay content={content} />
```

## 测试验证

您可以尝试输入以下测试内容来验证安全性：

```html
<!-- 测试脚本注入 -->
<script>
  alert("测试");
</script>

<!-- 测试事件处理器 -->
<img src="x" onerror="alert('测试')" />

<!-- 测试JavaScript协议 -->
<a href="javascript:alert('测试')">点击测试</a>

<!-- 测试危险标签 -->
<iframe src="http://malicious-site.com"></iframe>
```

安全的显示组件应该能够过滤掉这些危险内容，只显示安全的部分。
