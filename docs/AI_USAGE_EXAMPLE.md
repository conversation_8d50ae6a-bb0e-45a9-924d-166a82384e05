# AI功能使用示例

本文档展示如何使用运维服务管理系统的AI功能来自动分析工单、提供智能建议。

## 功能概述

AI集成管理系统提供以下核心功能：
1. **智能工单分析** - 自动分析工单内容，提供分类和优先级建议
2. **AI配置管理** - 管理多个AI提供商的配置
3. **提示词模板** - 自定义AI分析的提示词模板
4. **分析历史** - 查看所有AI分析记录
5. **使用统计** - 监控AI功能的使用情况和效果

## 使用示例

### 1. 在线演示

访问AI功能演示页面体验完整功能：
```bash
http://localhost:3001/demo/ai-function
```

演示页面包含：
- 4个预设场景（紧急故障、咨询服务、维护服务、Bug修复）
- 实时AI分析功能
- 智能建议展示
- 一键采用建议功能

### 2. 配置AI服务

首先需要在AI管理页面配置AI提供商：

```bash
# 访问AI管理页面
http://localhost:3001/admin/ai-management

# 在"AI配置"标签页中添加配置：
- 提供商: OpenAI
- 模型: gpt-4
- API密钥: sk-your-openai-api-key
- 温度: 0.3
- 最大Token: 4000
```

### 3. 创建工单并使用AI分析

#### 步骤1: 创建工单
```bash
# 访问工单管理页面
http://localhost:3001/tickets

# 点击"创建工单"，填写基本信息：
标题: 系统登录缓慢问题
描述: 用户反馈系统登录缓慢，页面加载时间超过10秒，影响正常使用
客户: 选择一个客户
```

#### 步骤2: 使用AI分析
```bash
# 在工单详情页面，点击"AI分析"按钮
# 系统会自动：
1. 调用配置的AI服务
2. 使用预设的提示词模板
3. 分析工单内容
4. 返回智能建议
```

#### 预期AI分析结果：
```json
{
  "category": "技术支持",
  "priority": "HIGH", 
  "estimatedTime": "4小时",
  "suggestedActions": [
    "检查数据库连接池配置",
    "优化查询性能",
    "检查服务器负载"
  ],
  "requiredSkills": ["数据库优化", "性能调优"],
  "confidence": 0.92
}
```

### 3. API调用示例

#### 直接调用AI分析API：

```bash
curl -X POST "http://localhost:3001/api/v1/ai/analyze" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "description": "用户反馈系统登录缓慢，页面加载时间超过10秒",
    "contextData": {
      "customerId": "customer_id",
      "customerType": "VIP",
      "industry": "technology"
    }
  }'
```

#### 响应示例：
```json
{
  "success": true,
  "data": {
    "requestId": "req_1704902400000_1",
    "analysis": {
      "category": "技术支持",
      "priority": "HIGH",
      "estimatedTime": "4小时",
      "suggestedActions": [
        "检查数据库连接池",
        "优化查询性能", 
        "检查服务器负载"
      ],
      "requiredSkills": ["数据库优化", "性能调优"],
      "confidence": 0.92
    },
    "suggestions": {
      "title": "系统登录性能问题",
      "category": "技术支持",
      "priority": "HIGH",
      "slaTemplate": "VIP客户4小时响应"
    },
    "processingTime": 1250
  }
}
```

### 4. 管理提示词模板

#### 创建自定义模板：
```bash
# 在AI管理页面的"提示词模板"标签页
# 点击"添加模板"

模板名称: 安全问题分析模板
模板类型: 安全分析
AI提供商: OpenAI
模板内容:
```

```text
请分析以下安全相关工单：

工单描述：{{description}}
报告用户：{{reporterInfo}}
系统环境：{{systemInfo}}

请重点关注：
1. 安全风险等级评估
2. 潜在影响范围
3. 紧急处理建议
4. 预防措施建议

返回JSON格式：
{
  "riskLevel": "CRITICAL/HIGH/MEDIUM/LOW",
  "impactScope": "影响范围描述",
  "immediateActions": ["立即行动1", "立即行动2"],
  "preventiveMeasures": ["预防措施1", "预防措施2"],
  "escalationRequired": true/false,
  "confidence": 0.95
}
```

### 5. 监控AI使用情况

#### 查看统计报告：
```bash
# 访问AI管理页面的"统计报告"标签页
# 可以看到：

总体统计:
- 总请求数: 1,250
- 成功请求: 1,180  
- 失败请求: 70
- 平均响应时间: 1,150ms
- 成功率: 94.4%

AI提供商使用分布:
- OpenAI: 750次 (60%)
- Anthropic: 350次 (28%) 
- Gemini: 150次 (12%)

最近7天趋势:
- 每日请求量和成功率趋势图
```

### 6. 实际业务场景示例

#### 场景1: 技术支持工单
```text
工单描述: "数据库连接超时，用户无法登录系统"
AI分析结果:
- 分类: 技术支持
- 优先级: HIGH
- 建议: 检查数据库连接池、重启数据库服务
- 预估时间: 2小时
```

#### 场景2: 客户服务工单  
```text
工单描述: "客户要求退款，对产品功能不满意"
AI分析结果:
- 分类: 客户服务
- 优先级: MEDIUM
- 建议: 联系客户了解具体问题、评估退款政策
- 预估时间: 4小时
```

#### 场景3: 产品改进建议
```text
工单描述: "希望增加批量导出功能，提高工作效率"
AI分析结果:
- 分类: 产品问题
- 优先级: LOW
- 建议: 记录功能需求、评估开发优先级
- 预估时间: 评估阶段1小时
```

## 最佳实践

### 1. 提示词模板设计
- 使用清晰的指令和示例
- 定义明确的输出格式
- 包含必要的上下文变量
- 定期根据效果优化模板

### 2. AI配置优化
- 根据不同场景选择合适的模型
- 调整温度参数平衡创造性和准确性
- 设置合理的Token限制
- 监控API使用成本

### 3. 质量控制
- 定期审查AI分析结果的准确性
- 收集用户反馈改进模板
- 监控成功率和响应时间
- 建立人工审核机制

### 4. 安全考虑
- 加密存储API密钥
- 限制AI功能的访问权限
- 不在提示词中包含敏感信息
- 定期轮换API密钥

## 故障排除

### 常见问题：

1. **AI分析失败**
   - 检查API密钥是否有效
   - 确认网络连接正常
   - 查看错误日志获取详细信息

2. **分析结果不准确**
   - 优化提示词模板
   - 调整模型参数
   - 增加更多上下文信息

3. **响应时间过长**
   - 减少Token限制
   - 优化提示词长度
   - 考虑使用更快的模型

通过以上示例和最佳实践，您可以充分利用AI功能提升运维服务管理的效率和质量。
