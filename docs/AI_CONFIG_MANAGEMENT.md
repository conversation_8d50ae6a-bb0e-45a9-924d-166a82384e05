# AI配置管理完整指南

## 概述

运维服务管理系统的AI功能现在完全支持从后端获取配置，实现了前后端配置的统一管理。

## 架构设计

### 后端配置管理
- **数据库存储**: AI配置存储在`AIConfiguration`表中
- **配置加密**: API密钥使用AES加密存储
- **多配置支持**: 支持多个AI提供商配置，自动选择活跃配置
- **权限控制**: 配置管理需要管理员权限

### 前端配置加载
- **自动加载**: 前端启动时自动从后端获取配置
- **类型转换**: 后端配置自动转换为前端格式
- **缓存机制**: 配置缓存在Zustand Store中
- **错误处理**: 配置加载失败时使用默认配置

## API接口

### 获取当前有效配置
```bash
GET /api/v1/ai/configuration/current
Authorization: Bearer <token>
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "id": "ai-config-1",
    "provider": "openai",
    "model": "gpt-4",
    "temperature": 0.3,
    "maxTokens": 4000,
    "timeout": 30000,
    "mode": "USER_TRIGGERED",
    "enabledFields": {
      "title": true,
      "category": true,
      "priority": true,
      "slaTemplate": true
    },
    "autoFillThreshold": 80,
    "isActive": true,
    "createdAt": "2025-08-10T10:04:09.689Z",
    "updatedAt": "2025-08-10T10:04:09.689Z"
  }
}
```

## 配置字段说明

### 后端配置字段
- `provider`: AI提供商 (openai, anthropic, gemini)
- `model`: AI模型名称
- `temperature`: 生成温度 (0.0-1.0)
- `maxTokens`: 最大Token数量
- `timeout`: 请求超时时间(毫秒)
- `mode`: AI模式 (USER_TRIGGERED, AGGRESSIVE, DISABLED)
- `enabledFields`: 启用的分析字段
- `autoFillThreshold`: 自动填充阈值 (0-100)
- `isActive`: 是否启用

### 前端配置转换
```typescript
// 后端配置 -> 前端配置
const frontendConfig: AIConfiguration = {
  mode: backendConfig.mode === "USER_TRIGGERED" ? "user-triggered" : 
        backendConfig.mode === "AGGRESSIVE" ? "aggressive" : "disabled",
  enabledFields: backendConfig.enabledFields,
  autoFillThreshold: backendConfig.autoFillThreshold / 100, // 转换为0-1
  userPreferences: {
    showReasoningTooltips: true,
    enableSuggestionHistory: true,
    showConfidenceScores: true,
  }
}
```

## 使用方式

### 1. 在组件中使用AI配置
```typescript
import { useAI } from "@/hooks/useAI";

function MyComponent() {
  const ai = useAI({
    userId: "current-user",
    autoLoadConfig: true, // 自动加载配置
  });

  // 配置加载状态
  if (ai.loading) {
    return <div>加载AI配置中...</div>;
  }

  // 配置加载错误
  if (ai.error) {
    return <div>配置加载失败: {ai.error}</div>;
  }

  // 使用AI功能
  const handleAnalyze = async () => {
    await ai.analyzeContent("工单描述内容");
  };

  return (
    <div>
      <p>AI模式: {ai.mode}</p>
      <p>自动填充阈值: {(ai.config.autoFillThreshold * 100).toFixed(0)}%</p>
      <button onClick={handleAnalyze} disabled={ai.isAIDisabled}>
        AI分析
      </button>
    </div>
  );
}
```

### 2. 直接调用配置API
```typescript
import { getCurrentAIConfiguration } from "@/services/aiManagement";

async function loadConfig() {
  try {
    const response = await getCurrentAIConfiguration();
    if (response.success) {
      console.log("当前AI配置:", response.data);
    }
  } catch (error) {
    console.error("获取配置失败:", error);
  }
}
```

## 配置管理流程

### 1. 管理员配置AI服务
1. 访问 `/admin/ai-management`
2. 在"AI配置"标签页添加配置
3. 设置提供商、模型、API密钥等参数
4. 启用配置

### 2. 前端自动加载配置
1. 用户访问系统时，AI Hook自动加载配置
2. 配置转换为前端格式并缓存
3. 如果加载失败，使用默认配置

### 3. AI功能使用配置
1. AI分析时使用当前配置的模式和参数
2. 根据`enabledFields`决定分析哪些字段
3. 根据`autoFillThreshold`决定是否自动填充

## 测试和调试

### 配置测试页面
访问 `/demo/ai-config-test` 查看：
- 后端API响应
- 前端配置转换结果
- AI Hook状态
- 配置对比

### 功能演示页面
访问 `/demo/ai-function` 体验：
- 完整的AI分析流程
- 不同场景的智能建议
- 配置效果展示

## 故障排除

### 常见问题

1. **配置加载失败**
   - 检查后端是否有有效的AI配置
   - 确认用户有足够的权限
   - 查看网络连接是否正常

2. **AI分析不工作**
   - 检查AI配置是否启用 (`isActive: true`)
   - 确认AI模式不是 `DISABLED`
   - 验证API密钥是否正确

3. **自动填充不生效**
   - 检查 `enabledFields` 配置
   - 确认置信度是否达到 `autoFillThreshold`
   - 验证AI模式是否为 `AGGRESSIVE`

### 调试方法

1. **查看控制台日志**
   ```javascript
   // 在浏览器控制台中查看AI Store状态
   console.log(useAIStore.getState());
   ```

2. **测试后端API**
   ```bash
   curl -X GET "http://localhost:3001/api/v1/ai/configuration/current" \
     -H "Authorization: Bearer <token>"
   ```

3. **检查配置转换**
   - 访问 `/demo/ai-config-test` 页面
   - 对比后端配置和前端配置
   - 确认转换逻辑正确

## 最佳实践

1. **配置管理**
   - 定期备份AI配置
   - 使用强密码保护API密钥
   - 监控AI服务使用情况

2. **性能优化**
   - 合理设置超时时间
   - 控制最大Token数量
   - 使用缓存减少重复请求

3. **用户体验**
   - 提供配置加载状态提示
   - 优雅处理配置加载失败
   - 支持配置热更新

## 总结

AI配置管理系统现在提供了完整的前后端集成方案，支持：
- ✅ 后端统一配置管理
- ✅ 前端自动配置加载
- ✅ 类型安全的配置转换
- ✅ 完善的错误处理机制
- ✅ 实时配置状态监控
- ✅ 多种测试和调试工具

这确保了AI功能的稳定性和可维护性，为用户提供了一致的智能化体验。
