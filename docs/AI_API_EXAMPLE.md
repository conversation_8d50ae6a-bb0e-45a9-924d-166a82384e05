# AI功能API调用示例

本文档展示如何通过API调用运维服务管理系统的AI功能。

## 1. AI工单分析接口

### 请求示例

```bash
curl -X POST "http://localhost:3001/api/v1/ai/analyze" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "description": "用户反馈系统登录缓慢，页面加载时间超过10秒，影响正常使用",
    "contextData": {
      "customerId": "customer_123",
      "customerType": "VIP",
      "industry": "technology",
      "historyPattern": "该客户之前有过性能相关问题"
    }
  }'
```

### 响应示例

```json
{
  "success": true,
  "data": {
    "requestId": "req_1754820400000_1",
    "analysis": {
      "title": {
        "suggested": "系统登录性能问题",
        "confidence": 90
      },
      "category": {
        "suggested": "SUPPORT",
        "confidence": 95,
        "reasoning": "用户反馈系统性能问题，需要技术支持解决"
      },
      "priority": {
        "suggested": "HIGH",
        "confidence": 85,
        "reasoning": "VIP客户反馈，影响正常使用，需要优先处理"
      },
      "slaTemplate": {
        "suggested": "VIP客户4小时响应",
        "confidence": 90,
        "reasoning": "VIP客户的高优先级技术问题"
      }
    },
    "processingTime": 1250,
    "provider": "openai",
    "model": "gpt-4"
  }
}
```

## 2. 获取AI配置列表

### 请求示例

```bash
curl -X GET "http://localhost:3001/api/v1/ai/configurations?page=1&limit=10" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### 响应示例

```json
{
  "success": true,
  "data": {
    "data": [
      {
        "id": "ai-config-1",
        "provider": "openai",
        "model": "gpt-4",
        "temperature": 0.3,
        "maxTokens": 4000,
        "timeout": 30000,
        "mode": "USER_TRIGGERED",
        "enabledFields": {
          "title": true,
          "category": true,
          "priority": true,
          "slaTemplate": true
        },
        "autoFillThreshold": 0.8,
        "isActive": true,
        "createdAt": "2025-08-10T10:04:09.689Z",
        "updatedAt": "2025-08-10T10:04:09.689Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 4,
      "totalPages": 1
    }
  }
}
```

## 3. 获取提示词模板

### 请求示例

```bash
curl -X GET "http://localhost:3001/api/v1/ai/prompt-templates?category=ticket_analysis" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### 响应示例

```json
{
  "success": true,
  "data": {
    "data": [
      {
        "id": "template_1",
        "name": "工单分析模板",
        "category": "ticket_analysis",
        "template": "请分析以下工单内容：\n\n工单描述：{{description}}...",
        "variables": ["description", "customerInfo", "historyPattern"],
        "provider": "openai",
        "version": "1.0",
        "isActive": true,
        "createdAt": "2025-08-10T10:04:09.763Z",
        "updatedAt": "2025-08-10T10:04:09.763Z",
        "creator": {
          "id": "user_123",
          "username": "admin"
        }
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 6,
      "totalPages": 1
    }
  }
}
```

## 4. 获取分析历史

### 请求示例

```bash
curl -X GET "http://localhost:3001/api/v1/ai/analysis-requests?page=1&limit=10&status=COMPLETED" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### 响应示例

```json
{
  "success": true,
  "data": {
    "data": [
      {
        "id": "req_1754820249777_1",
        "provider": "openai",
        "model": "gpt-4",
        "inputData": "{\"description\":\"用户反馈系统登录缓慢，页面加载时间超过10秒，影响正常使用\",\"contextData\":{\"customerTier\":\"VIP\"}}",
        "outputData": "{\"category\":\"技术支持\",\"priority\":\"HIGH\",\"confidence\":0.92}",
        "status": "COMPLETED",
        "processingTime": 1250,
        "errorMessage": null,
        "createdAt": "2025-08-10T10:04:09.781Z",
        "updatedAt": "2025-08-10T10:04:09.781Z",
        "user": {
          "id": "user_123",
          "username": "admin"
        }
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 3,
      "totalPages": 1
    }
  }
}
```

## 5. 获取AI使用统计

### 请求示例

```bash
curl -X GET "http://localhost:3001/api/v1/ai/statistics?startDate=2025-08-01&endDate=2025-08-10" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### 响应示例

```json
{
  "success": true,
  "data": {
    "totalRequests": 3,
    "successfulRequests": 2,
    "failedRequests": 1,
    "averageResponseTime": 1115,
    "successRate": 66.7,
    "providerUsage": {
      "anthropic": {
        "count": 1,
        "percentage": 33.3
      },
      "openai": {
        "count": 2,
        "percentage": 66.7
      }
    },
    "templateUsage": {},
    "dailyStats": [
      {
        "date": "2025-08-10",
        "requests": 3,
        "successRate": 66.7
      }
    ]
  }
}
```

## 6. 创建AI配置

### 请求示例

```bash
curl -X POST "http://localhost:3001/api/v1/ai/configurations" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "provider": "openai",
    "model": "gpt-4o-mini",
    "apiKey": "sk-your-openai-api-key",
    "temperature": 0.3,
    "maxTokens": 2000,
    "timeout": 30000,
    "mode": "USER_TRIGGERED",
    "enabledFields": {
      "title": true,
      "category": true,
      "priority": true,
      "slaTemplate": false
    },
    "autoFillThreshold": 0.8
  }'
```

### 响应示例

```json
{
  "success": true,
  "data": {
    "id": "ai-config-new",
    "provider": "openai",
    "model": "gpt-4o-mini",
    "temperature": 0.3,
    "maxTokens": 2000,
    "timeout": 30000,
    "mode": "USER_TRIGGERED",
    "enabledFields": {
      "title": true,
      "category": true,
      "priority": true,
      "slaTemplate": false
    },
    "autoFillThreshold": 0.8,
    "isActive": true,
    "createdAt": "2025-08-10T10:30:00.000Z",
    "updatedAt": "2025-08-10T10:30:00.000Z"
  },
  "message": "AI配置创建成功"
}
```

## 7. 创建提示词模板

### 请求示例

```bash
curl -X POST "http://localhost:3001/api/v1/ai/prompt-templates" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "name": "安全问题分析模板",
    "category": "security_analysis",
    "template": "请分析以下安全相关工单：\n\n工单描述：{{description}}\n报告用户：{{reporterInfo}}\n\n请评估：\n1. 安全风险等级\n2. 影响范围\n3. 处理建议\n\n返回JSON格式结果。",
    "variables": ["description", "reporterInfo"],
    "provider": "openai",
    "version": "1.0"
  }'
```

### 响应示例

```json
{
  "success": true,
  "data": {
    "id": "template_new",
    "name": "安全问题分析模板",
    "category": "security_analysis",
    "template": "请分析以下安全相关工单：...",
    "variables": ["description", "reporterInfo"],
    "provider": "openai",
    "version": "1.0",
    "isActive": true,
    "createdAt": "2025-08-10T10:30:00.000Z",
    "updatedAt": "2025-08-10T10:30:00.000Z",
    "createdBy": "user_123"
  },
  "message": "提示词模板创建成功"
}
```

## 错误处理

### 常见错误响应

```json
{
  "success": false,
  "message": "AI配置不存在或未启用",
  "code": "AI_CONFIG_NOT_FOUND"
}
```

```json
{
  "success": false,
  "message": "API调用失败",
  "error": "网络超时",
  "code": "AI_API_ERROR"
}
```

```json
{
  "success": false,
  "message": "请求参数验证失败",
  "errors": [
    {
      "field": "description",
      "message": "描述内容不能为空"
    }
  ],
  "code": "VALIDATION_ERROR"
}
```

## 认证说明

所有API请求都需要在Header中包含有效的JWT Token：

```bash
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

获取Token的方法：

```bash
curl -X POST "http://localhost:3001/api/v1/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin",
    "password": "your_password"
  }'
```

## 权限要求

使用AI功能需要以下权限：
- `config:read` - 查看AI配置
- `config:write` - 管理AI配置
- AI分析功能通常需要工单相关权限

通过以上API示例，您可以完整地集成和使用运维服务管理系统的AI功能。
