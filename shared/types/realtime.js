"use strict";
// 实时推送相关类型定义
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConnectionStatus = exports.RealtimePriority = exports.RealtimeChannelType = exports.RealtimeEventType = void 0;
var RealtimeEventType;
(function (RealtimeEventType) {
    // 系统监控相关
    RealtimeEventType["SYSTEM_METRICS_UPDATE"] = "system_metrics_update";
    RealtimeEventType["SYSTEM_STATUS_CHANGE"] = "system_status_change";
    RealtimeEventType["SYSTEM_ALERT_NEW"] = "system_alert_new";
    RealtimeEventType["SYSTEM_ALERT_UPDATE"] = "system_alert_update";
    // 用户活动相关
    RealtimeEventType["USER_ONLINE"] = "user_online";
    RealtimeEventType["USER_OFFLINE"] = "user_offline";
    RealtimeEventType["USER_ACTIVITY"] = "user_activity";
    RealtimeEventType["ONLINE_USERS_UPDATE"] = "online_users_update";
    // 工单相关
    RealtimeEventType["SERVICE_CREATED"] = "service_created";
    RealtimeEventType["SERVICE_UPDATED"] = "service_updated";
    RealtimeEventType["SERVICE_STATUS_CHANGED"] = "service_status_changed";
    RealtimeEventType["SERVICE_ASSIGNED"] = "service_assigned";
    RealtimeEventType["SERVICE_COMMENT_ADDED"] = "service_comment_added";
    // 审计日志相关
    RealtimeEventType["AUDIT_LOG_NEW"] = "audit_log_new";
    RealtimeEventType["SECURITY_EVENT"] = "security_event";
    // 配置变更
    RealtimeEventType["CONFIG_CHANGED"] = "config_changed";
    RealtimeEventType["ROLE_PERMISSION_CHANGED"] = "role_permission_changed";
    // 通知相关
    RealtimeEventType["NOTIFICATION_NEW"] = "notification_new";
    RealtimeEventType["NOTIFICATION_UPDATE"] = "notification_update";
    // 系统维护
    RealtimeEventType["MAINTENANCE_START"] = "maintenance_start";
    RealtimeEventType["MAINTENANCE_END"] = "maintenance_end";
    // 连接管理
    RealtimeEventType["CONNECTION_ESTABLISHED"] = "connection_established";
    RealtimeEventType["CONNECTION_ERROR"] = "connection_error";
    RealtimeEventType["CONNECT"] = "connect";
    RealtimeEventType["SUBSCRIBE"] = "subscribe";
    RealtimeEventType["UNSUBSCRIBE"] = "unsubscribe";
    RealtimeEventType["HEARTBEAT"] = "heartbeat";
})(RealtimeEventType || (exports.RealtimeEventType = RealtimeEventType = {}));
var RealtimeChannelType;
(function (RealtimeChannelType) {
    // 全局频道
    RealtimeChannelType["GLOBAL"] = "global";
    // 用户个人频道
    RealtimeChannelType["USER"] = "user";
    // 角色频道
    RealtimeChannelType["ROLE"] = "role";
    // 部门频道
    RealtimeChannelType["DEPARTMENT"] = "department";
    // 项目频道
    RealtimeChannelType["PROJECT"] = "project";
    // 系统监控频道
    RealtimeChannelType["SYSTEM_MONITOR"] = "system_monitor";
    // 系统告警频道
    RealtimeChannelType["SYSTEM_ALERT"] = "system_alert";
    // 通知频道
    RealtimeChannelType["NOTIFICATION"] = "notification";
    // 审计日志频道
    RealtimeChannelType["AUDIT"] = "audit";
    // 用户活动频道
    RealtimeChannelType["USER_ACTIVITY"] = "user_activity";
    // 管理员频道
    RealtimeChannelType["ADMIN"] = "admin";
})(RealtimeChannelType || (exports.RealtimeChannelType = RealtimeChannelType = {}));
var RealtimePriority;
(function (RealtimePriority) {
    RealtimePriority["LOW"] = "low";
    RealtimePriority["NORMAL"] = "normal";
    RealtimePriority["HIGH"] = "high";
    RealtimePriority["CRITICAL"] = "critical";
})(RealtimePriority || (exports.RealtimePriority = RealtimePriority = {}));
// 连接状态
var ConnectionStatus;
(function (ConnectionStatus) {
    ConnectionStatus["CONNECTING"] = "connecting";
    ConnectionStatus["CONNECTED"] = "connected";
    ConnectionStatus["RECONNECTING"] = "reconnecting";
    ConnectionStatus["DISCONNECTED"] = "disconnected";
    ConnectionStatus["ERROR"] = "error";
})(ConnectionStatus || (exports.ConnectionStatus = ConnectionStatus = {}));
//# sourceMappingURL=realtime.js.map