export declare enum RealtimeEventType {
    SYSTEM_METRICS_UPDATE = "system_metrics_update",
    SYSTEM_STATUS_CHANGE = "system_status_change",
    SYSTEM_ALERT_NEW = "system_alert_new",
    SYSTEM_ALERT_UPDATE = "system_alert_update",
    USER_ONLINE = "user_online",
    USER_OFFLINE = "user_offline",
    USER_ACTIVITY = "user_activity",
    ONLINE_USERS_UPDATE = "online_users_update",
    SERVICE_CREATED = "service_created",
    SERVICE_UPDATED = "service_updated",
    SERVICE_STATUS_CHANGED = "service_status_changed",
    SERVICE_ASSIGNED = "service_assigned",
    SERVICE_COMMENT_ADDED = "service_comment_added",
    AUDIT_LOG_NEW = "audit_log_new",
    SECURITY_EVENT = "security_event",
    CONFIG_CHANGED = "config_changed",
    ROLE_PERMISSION_CHANGED = "role_permission_changed",
    NOTIFICATION_NEW = "notification_new",
    NOTIFICATION_UPDATE = "notification_update",
    MAINTENANCE_START = "maintenance_start",
    MAINTENANCE_END = "maintenance_end",
    CONNECTION_ESTABLISHED = "connection_established",
    CONNECTION_ERROR = "connection_error",
    CONNECT = "connect",
    SUBSCRIBE = "subscribe",
    UNSUBSCRIBE = "unsubscribe",
    HEARTBEAT = "heartbeat"
}
export declare enum RealtimeChannelType {
    GLOBAL = "global",
    USER = "user",
    ROLE = "role",
    DEPARTMENT = "department",
    PROJECT = "project",
    SYSTEM_MONITOR = "system_monitor",
    SYSTEM_ALERT = "system_alert",
    NOTIFICATION = "notification",
    AUDIT = "audit",
    USER_ACTIVITY = "user_activity",
    ADMIN = "admin"
}
export declare enum RealtimePriority {
    LOW = "low",
    NORMAL = "normal",
    HIGH = "high",
    CRITICAL = "critical"
}
export interface RealtimeMessage {
    id: string;
    type: RealtimeEventType;
    channel: RealtimeChannelType;
    priority: RealtimePriority;
    timestamp: string;
    data: any;
    metadata?: {
        userId?: string;
        sessionId?: string;
        source?: string;
        version?: string;
        ttl?: number;
    };
}
export interface WebSocketClient {
    id: string;
    userId: string;
    username: string;
    roles: string[];
    permissions: string[];
    sessionId: string;
    ip: string;
    userAgent: string;
    connectedAt: string;
    lastActivity: string;
    subscriptions: RealtimeChannelType[];
}
export declare enum ConnectionStatus {
    CONNECTING = "connecting",
    CONNECTED = "connected",
    RECONNECTING = "reconnecting",
    DISCONNECTED = "disconnected",
    ERROR = "error"
}
export interface SystemMetricsMessage extends RealtimeMessage {
    type: RealtimeEventType.SYSTEM_METRICS_UPDATE;
    data: {
        cpu: number;
        memory: number;
        disk: number;
        network: number;
        timestamp: string;
    };
}
export interface SystemStatusMessage extends RealtimeMessage {
    type: RealtimeEventType.SYSTEM_STATUS_CHANGE;
    data: {
        overall: 'healthy' | 'warning' | 'critical';
        services: Array<{
            name: string;
            status: 'healthy' | 'warning' | 'critical' | 'down';
            responseTime: number;
            message?: string;
        }>;
        timestamp: string;
    };
}
export interface SystemAlertMessage extends RealtimeMessage {
    type: RealtimeEventType.SYSTEM_ALERT_NEW | RealtimeEventType.SYSTEM_ALERT_UPDATE;
    data: {
        id: string;
        title: string;
        message: string;
        severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
        component: string;
        status: 'PENDING' | 'ACKNOWLEDGED' | 'RESOLVED';
        timestamp: string;
        acknowledgedBy?: string;
        resolvedBy?: string;
    };
}
export interface UserActivityMessage extends RealtimeMessage {
    type: RealtimeEventType.USER_ONLINE | RealtimeEventType.USER_OFFLINE | RealtimeEventType.USER_ACTIVITY;
    data: {
        userId: string;
        username: string;
        fullName: string;
        action: string;
        timestamp: string;
        metadata?: any;
    };
}
export interface OnlineUsersMessage extends RealtimeMessage {
    type: RealtimeEventType.ONLINE_USERS_UPDATE;
    data: {
        count: number;
        users: Array<{
            userId: string;
            username: string;
            fullName: string;
            lastActivity: string;
            status: 'active' | 'idle' | 'away';
        }>;
    };
}
export interface ServiceMessage extends RealtimeMessage {
    type: RealtimeEventType.SERVICE_CREATED | RealtimeEventType.SERVICE_UPDATED | RealtimeEventType.SERVICE_STATUS_CHANGED | RealtimeEventType.SERVICE_ASSIGNED;
    data: {
        id: string;
        ticketNumber: string;
        title: string;
        status: string;
        priority: string;
        assignedTo?: string;
        customerName: string;
        updatedBy: string;
        timestamp: string;
        changes?: Record<string, any>;
    };
}
export interface AuditLogMessage extends RealtimeMessage {
    type: RealtimeEventType.AUDIT_LOG_NEW;
    data: {
        id: string;
        userId: string;
        username: string;
        fullName: string;
        action: string;
        resource: string;
        resourceId?: string;
        ip: string;
        timestamp: string;
        details?: any;
    };
}
export interface ConfigChangeMessage extends RealtimeMessage {
    type: RealtimeEventType.CONFIG_CHANGED;
    data: {
        category: string;
        key: string;
        oldValue?: any;
        newValue: any;
        changedBy: string;
        changeReason?: string;
        timestamp: string;
    };
}
export interface NotificationMessage extends RealtimeMessage {
    type: RealtimeEventType.NOTIFICATION_NEW | RealtimeEventType.NOTIFICATION_UPDATE;
    data: {
        id: string;
        type: 'info' | 'warning' | 'error' | 'success';
        title: string;
        message: string;
        targetUserId?: string;
        actionUrl?: string;
        persistent: boolean;
        timestamp: string;
    };
}
export interface PushStrategy {
    channels: RealtimeChannelType[];
    priority: RealtimePriority;
    throttle?: {
        enabled: boolean;
        interval: number;
        maxPerInterval: number;
    };
    filter?: {
        userRoles?: string[];
        permissions?: string[];
        conditions?: Record<string, any>;
    };
    delivery?: {
        persistent: boolean;
        retry: boolean;
        maxRetries: number;
        retryDelay: number;
    };
}
export interface WebSocketConfig {
    port: number;
    path: string;
    heartbeatInterval: number;
    connectionTimeout: number;
    maxConnections: number;
    compression: boolean;
    cors: {
        origin: string | string[];
        credentials: boolean;
    };
    auth: {
        required: boolean;
        tokenKey: string;
        refreshEnabled: boolean;
    };
    rateLimiting: {
        enabled: boolean;
        maxRequests: number;
        windowMs: number;
    };
    monitoring: {
        enabled: boolean;
        metricsInterval: number;
    };
}
export interface RealtimeStats {
    connections: {
        total: number;
        active: number;
        idle: number;
        byRole: Record<string, number>;
    };
    messages: {
        sent: number;
        received: number;
        failed: number;
        byType: Record<RealtimeEventType, number>;
    };
    performance: {
        averageLatency: number;
        throughput: number;
        errorRate: number;
    };
    uptime: number;
}
export interface SubscriptionConfig {
    channels: RealtimeChannelType[];
    eventTypes?: RealtimeEventType[];
    priority?: RealtimePriority[];
    autoReconnect: boolean;
    reconnectDelay: number;
    maxReconnectAttempts: number;
    heartbeatEnabled: boolean;
    compression: boolean;
}
export interface MessageQueueConfig {
    redis: {
        enabled: boolean;
        keyPrefix: string;
        maxLength: number;
        ttl: number;
    };
    inMemory: {
        enabled: boolean;
        maxSize: number;
        cleanupInterval: number;
    };
    persistence: {
        enabled: boolean;
        batchSize: number;
        flushInterval: number;
    };
}
//# sourceMappingURL=realtime.d.ts.map