/**
 * 权限模板相关类型定义
 */

import type { ID, Timestamp, JSONValue, PaginatedResponse, ApiResponse, UserBasicInfo } from './common'

// ==================== 权限模板基础类型 ====================

/**
 * 权限模板分类
 */
export type PermissionTemplateCategory = 
  | 'SYSTEM'    // 系统管理类
  | 'BUSINESS'  // 业务管理类
  | 'SERVICE'   // 服务运维类
  | 'READONLY'  // 只读类
  | 'CUSTOM'    // 自定义类

/**
 * 权限模板操作类型
 */
export type PermissionTemplateAction = 
  | 'CREATE'    // 创建
  | 'UPDATE'    // 更新
  | 'DELETE'    // 删除
  | 'APPLY'     // 应用到角色
  | 'EXPORT'    // 导出
  | 'IMPORT'    // 导入
  | 'COPY'      // 复制

/**
 * 权限模板使用状态
 */
export type PermissionTemplateUsageStatus = 
  | 'ACTIVE'      // 生效中
  | 'INACTIVE'    // 未生效
  | 'SUPERSEDED'  // 已被替换

// ==================== 权限模板核心类型 ====================

/**
 * 权限项详情
 */
export interface PermissionDetail {
  key: string           // 权限标识
  name: string          // 权限名称
  description?: string  // 权限描述
  group?: string        // 权限分组
}

/**
 * 权限模板基础信息
 */
export interface PermissionTemplate {
  id: ID
  name: string                              // 模板名称
  description?: string                      // 模板描述
  permissions: string[]                     // 权限列表
  permissionCount: number                   // 权限数量
  category: PermissionTemplateCategory      // 模板分类
  isDefault: boolean                        // 是否为默认模板
  isSystem: boolean                         // 是否为系统模板
  version: string                           // 模板版本
  metadata?: Record<string, JSONValue>      // 元数据信息
  usageCount: number                        // 使用次数
  createdBy: UserBasicInfo                  // 创建人
  updatedBy?: UserBasicInfo                 // 更新人
  createdAt: Timestamp                      // 创建时间
  updatedAt: Timestamp                      // 更新时间
}

/**
 * 权限模板详细信息
 */
export interface PermissionTemplateDetail extends PermissionTemplate {
  permissionDetails: PermissionDetail[]     // 权限详情列表
  usedByRoles: RoleUsageInfo[]             // 使用该模板的角色
  usageHistory: PermissionTemplateUsageRecord[]  // 使用历史
  changeHistory: PermissionTemplateHistoryRecord[]  // 变更历史
}

/**
 * 角色使用信息
 */
export interface RoleUsageInfo {
  roleId: ID
  roleName: string
  roleDescription?: string
  appliedAt: Timestamp
}

/**
 * 权限模板使用记录
 */
export interface PermissionTemplateUsageRecord {
  id: ID
  roleId: ID
  roleName: string
  roleDescription?: string
  appliedBy: UserBasicInfo
  appliedAt: Timestamp
  status: PermissionTemplateUsageStatus
  note?: string
}

/**
 * 权限模板历史记录
 */
export interface PermissionTemplateHistoryRecord {
  id: ID
  action: PermissionTemplateAction
  oldData?: Record<string, JSONValue>
  newData?: Record<string, JSONValue>
  changeReason?: string
  changedBy: UserBasicInfo
  createdAt: Timestamp
}

// ==================== 权限模板操作类型 ====================

/**
 * 创建权限模板请求
 */
export interface CreatePermissionTemplateRequest {
  name: string
  description?: string
  permissions: string[]
  category?: PermissionTemplateCategory
  version?: string
  metadata?: Record<string, JSONValue>
}

/**
 * 更新权限模板请求
 */
export interface UpdatePermissionTemplateRequest {
  name?: string
  description?: string
  permissions?: string[]
  category?: PermissionTemplateCategory
  version?: string
  metadata?: Record<string, JSONValue>
}

/**
 * 应用模板到角色请求
 */
export interface ApplyTemplateToRolesRequest {
  roleIds: ID[]
  note?: string
}

/**
 * 批量应用模板请求
 */
export interface BatchApplyTemplateRequest {
  templateId: ID
  applications: {
    roleId: ID
    note?: string
  }[]
}

/**
 * 应用结果
 */
export interface TemplateApplicationResult {
  roleId: ID
  roleName: string
  success: boolean
  error?: string
}

/**
 * 复制模板请求
 */
export interface CopyPermissionTemplateRequest {
  name?: string
  description?: string
}

// ==================== 权限模板比较和分析类型 ====================

/**
 * 模板比较请求
 */
export interface ComparePermissionTemplatesRequest {
  templateIds: ID[]  // 2-5个模板ID
}

/**
 * 权限比较矩阵行
 */
export interface PermissionComparisonRow {
  permission: string
  description: string
  [templateId: string]: boolean | string  // 每个模板对应的权限拥有情况
}

/**
 * 模板相似度信息
 */
export interface TemplateSimilarity {
  template1: { id: ID; name: string }
  template2: { id: ID; name: string }
  similarity: number          // 相似度百分比
  commonPermissions: number   // 共同权限数量
  totalPermissions: number    // 总权限数量
  onlyInTemplate1: number     // 仅在模板1中的权限数量
  onlyInTemplate2: number     // 仅在模板2中的权限数量
}

/**
 * 权限统计信息
 */
export interface PermissionStats {
  templateId: ID
  templateName: string
  category: PermissionTemplateCategory
  totalPermissions: number
  uniquePermissions: number   // 唯一权限（其他模板没有的）
}

/**
 * 模板比较结果
 */
export interface TemplateComparisonResult {
  templates: {
    id: ID
    name: string
    description?: string
    category: PermissionTemplateCategory
    version: string
    permissionCount: number
  }[]
  comparisonMatrix: PermissionComparisonRow[]
  similarities: TemplateSimilarity[]
  permissionStats: PermissionStats[]
  summary: {
    totalTemplates: number
    totalUniquePermissions: number
    averagePermissionsPerTemplate: number
  }
}

// ==================== 权限模板导入导出类型 ====================

/**
 * 导出权限模板请求
 */
export interface ExportPermissionTemplatesRequest {
  templateIds: ID[]
  format: 'json' | 'csv'
}

/**
 * 导入权限模板数据
 */
export interface PermissionTemplateImportData {
  name: string
  description?: string
  permissions: string[]
  category?: PermissionTemplateCategory
  version?: string
  metadata?: Record<string, JSONValue>
}

/**
 * 导入权限模板请求
 */
export interface ImportPermissionTemplatesRequest {
  templates: PermissionTemplateImportData[]
  overwrite?: boolean  // 是否覆盖同名模板
}

/**
 * 导入结果项
 */
export interface TemplateImportResult {
  name: string
  success: boolean
  action?: 'created' | 'updated'
  templateId?: ID
  error?: string
  details?: {
    invalidPermissions?: string[]
    unknownPermissions?: string[]
  }
}

/**
 * 导出模板数据格式
 */
export interface ExportedTemplateData {
  version: string
  exportedAt: Timestamp
  totalTemplates: number
  templates: {
    name: string
    description?: string
    permissions: string[]
    category: PermissionTemplateCategory
    version: string
    isDefault: boolean
    metadata?: Record<string, JSONValue>
    createdBy: string
    createdAt: Timestamp
    exportedAt: Timestamp
  }[]
}

// ==================== 权限模板统计类型 ====================

/**
 * 权限模板概览统计
 */
export interface PermissionTemplateOverview {
  total: number       // 总模板数
  default: number     // 默认模板数
  system: number      // 系统模板数
  custom: number      // 自定义模板数
  active: number      // 生效中的模板数
}

/**
 * 分类分布统计
 */
export interface CategoryDistribution {
  category: PermissionTemplateCategory
  count: number
}

/**
 * 使用状态分布统计
 */
export interface UsageDistribution {
  status: PermissionTemplateUsageStatus
  count: number
}

/**
 * 热门模板信息
 */
export interface PopularTemplate {
  id: ID
  name: string
  category: PermissionTemplateCategory
  usageCount: number
}

/**
 * 最近活动信息
 */
export interface RecentActivity {
  id: ID
  action: PermissionTemplateAction
  templateName: string
  changedBy?: string
  changeReason?: string
  createdAt: Timestamp
}

/**
 * 权限模板统计数据
 */
export interface PermissionTemplateStats {
  overview: PermissionTemplateOverview
  categoryDistribution: CategoryDistribution[]
  usageDistribution: UsageDistribution[]
  popularTemplates: PopularTemplate[]
  recentActivity: RecentActivity[]
}

// ==================== 权限模板查询类型 ====================

/**
 * 权限模板查询参数
 */
export interface PermissionTemplateQuery {
  page?: number
  pageSize?: number
  category?: PermissionTemplateCategory
  search?: string      // 搜索关键词（名称或描述）
  isDefault?: boolean  // 是否为默认模板
  isSystem?: boolean   // 是否为系统模板
  createdBy?: ID       // 创建人
  sortBy?: 'name' | 'createdAt' | 'updatedAt' | 'usageCount'
  sortOrder?: 'asc' | 'desc'
}

// ==================== API 响应类型 ====================

/**
 * 权限模板列表响应
 */
export interface PermissionTemplateListResponse extends PaginatedResponse<PermissionTemplate> {}

/**
 * 权限模板详情响应
 */
export interface PermissionTemplateDetailResponse extends ApiResponse<PermissionTemplateDetail> {}

/**
 * 权限模板创建响应
 */
export interface CreatePermissionTemplateResponse extends ApiResponse<{
  id: ID
  name: string
  description?: string
  permissions: string[]
  permissionCount: number
  category: PermissionTemplateCategory
  version: string
  createdBy: UserBasicInfo
  createdAt: Timestamp
}> {}

/**
 * 权限模板更新响应
 */
export interface UpdatePermissionTemplateResponse extends ApiResponse<{
  id: ID
  name: string
  description?: string
  permissions: string[]
  category: PermissionTemplateCategory
  version: string
  updatedBy: UserBasicInfo
  updatedAt: Timestamp
}> {}

/**
 * 应用模板响应
 */
export interface ApplyTemplateResponse extends ApiResponse<{
  templateId: ID
  templateName: string
  results: TemplateApplicationResult[]
}> {}

/**
 * 批量应用模板响应
 */
export interface BatchApplyTemplateResponse extends ApiResponse<{
  templateId: ID
  templateName: string
  total: number
  success: number
  failure: number
  results: TemplateApplicationResult[]
}> {}

/**
 * 模板比较响应
 */
export interface CompareTemplatesResponse extends ApiResponse<TemplateComparisonResult> {}

/**
 * 导入模板响应
 */
export interface ImportTemplatesResponse extends ApiResponse<{
  total: number
  success: number
  failure: number
  results: TemplateImportResult[]
}> {}

/**
 * 复制模板响应
 */
export interface CopyTemplateResponse extends ApiResponse<{
  id: ID
  name: string
  description?: string
  category: PermissionTemplateCategory
  permissions: string[]
  createdBy: UserBasicInfo
  createdAt: Timestamp
}> {}

/**
 * 权限模板统计响应
 */
export interface PermissionTemplateStatsResponse extends ApiResponse<PermissionTemplateStats> {}

/**
 * 创建默认模板响应
 */
export interface CreateDefaultTemplatesResponse extends ApiResponse<{
  total: number
  success: number
  failure: number
  results: {
    roleName: string
    templateName: string
    templateId?: ID
    success: boolean
    permissionCount?: number
    message?: string
    error?: string
  }[]
}> {}

// ==================== 权限分组和权限项类型 ====================

/**
 * 权限分组信息
 */
export interface PermissionGroup {
  key: string
  name: string
  label: string
  description: string
  permissionCount: number
  permissions: PermissionDetail[]
}

/**
 * 获取所有权限响应
 */
export interface AllPermissionsResponse extends ApiResponse<{
  total: number
  permissions: PermissionGroup[]
  flatPermissions: PermissionDetail[]
}> {}

/**
 * 权限分组列表响应
 */
export interface PermissionGroupsResponse extends ApiResponse<PermissionGroup[]> {}

// ==================== 表单和UI相关类型 ====================

/**
 * 权限选择器选项
 */
export interface PermissionOption {
  label: string
  value: string
  key: string
  children?: PermissionOption[]
  description?: string
  disabled?: boolean
}

/**
 * 权限模板表单数据
 */
export interface PermissionTemplateFormData {
  name: string
  description?: string
  permissions: string[]
  category: PermissionTemplateCategory
  version: string
  metadata?: Record<string, JSONValue>
}

/**
 * 权限模板筛选选项
 */
export interface PermissionTemplateFilterOptions {
  categories: { key: PermissionTemplateCategory; label: string; count: number }[]
  creators: { id: ID; name: string; count: number }[]
  versions: { version: string; count: number }[]
}

/**
 * 权限模板操作配置
 */
export interface PermissionTemplateActionConfig {
  canView: boolean
  canCreate: boolean
  canEdit: boolean
  canDelete: boolean
  canCopy: boolean
  canApply: boolean
  canExport: boolean
  canImport: boolean
  canCompare: boolean
  canCreateDefaults: boolean
}

// ==================== 默认值和常量 ====================

/**
 * 默认权限模板查询参数
 */
export const DEFAULT_PERMISSION_TEMPLATE_QUERY: Required<PermissionTemplateQuery> = {
  page: 1,
  pageSize: 20,
  category: 'CUSTOM' as PermissionTemplateCategory,
  search: '',
  isDefault: false,
  isSystem: false,
  createdBy: '',
  sortBy: 'updatedAt' as const,
  sortOrder: 'desc' as const
}

/**
 * 权限模板分类选项
 */
export const PERMISSION_TEMPLATE_CATEGORY_OPTIONS = [
  { key: 'SYSTEM' as const, label: '系统管理', icon: '🛡️', color: 'red' },
  { key: 'BUSINESS' as const, label: '业务管理', icon: '💼', color: 'blue' },
  { key: 'SERVICE' as const, label: '服务运维', icon: '🔧', color: 'green' },
  { key: 'READONLY' as const, label: '只读权限', icon: '👁️', color: 'gray' },
  { key: 'CUSTOM' as const, label: '自定义', icon: '⚙️', color: 'purple' }
]

/**
 * 权限模板操作类型选项
 */
export const PERMISSION_TEMPLATE_ACTION_OPTIONS = [
  { key: 'CREATE' as const, label: '创建', icon: '➕', color: 'green' },
  { key: 'UPDATE' as const, label: '更新', icon: '✏️', color: 'blue' },
  { key: 'DELETE' as const, label: '删除', icon: '🗑️', color: 'red' },
  { key: 'APPLY' as const, label: '应用', icon: '📥', color: 'purple' },
  { key: 'EXPORT' as const, label: '导出', icon: '📤', color: 'orange' },
  { key: 'IMPORT' as const, label: '导入', icon: '📥', color: 'teal' },
  { key: 'COPY' as const, label: '复制', icon: '📋', color: 'cyan' }
]

/**
 * 使用状态选项
 */
export const PERMISSION_TEMPLATE_USAGE_STATUS_OPTIONS = [
  { key: 'ACTIVE' as const, label: '生效中', color: 'green' },
  { key: 'INACTIVE' as const, label: '未生效', color: 'gray' },
  { key: 'SUPERSEDED' as const, label: '已替换', color: 'orange' }
]