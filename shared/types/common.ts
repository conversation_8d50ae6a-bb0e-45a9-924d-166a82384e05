// 基础类型定义
export type ID = string
export type Timestamp = string
export type JSONValue = string | number | boolean | null | JSONObject | JSONArray

export interface JSONObject {
  [key: string]: JSONValue
}

export interface JSONArray extends Array<JSONValue> {}

// 用户基础信息
export interface UserBasicInfo {
  id: string
  username: string
  fullName: string
  email: string
  avatar?: string
}

// 通用API响应类型
export interface ApiResponse<T = any> {
  success: boolean
  message: string
  data?: T
  code?: string
}

// 分页请求参数
export interface PaginationParams {
  page?: number
  limit?: number
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

// 分页响应数据
export interface PaginatedData<T> {
  items: T[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
    hasNext: boolean
    hasPrev: boolean
  }
}

// 分页响应
export interface PaginatedResponse<T> extends ApiResponse<PaginatedData<T>> {}

// 筛选参数
export interface FilterParams {
  search?: string
  status?: string
  startDate?: string
  endDate?: string
  [key: string]: any
}

// 用户状态枚举
export enum UserStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  BANNED = 'BANNED'
}

// 客户等级枚举
export enum CustomerLevel {
  BASIC = 'BASIC',
  STANDARD = 'STANDARD',
  PREMIUM = 'PREMIUM',
  ENTERPRISE = 'ENTERPRISE'
}

// 档案状态枚举
export enum ArchiveStatus {
  ACTIVE = 'ACTIVE',
  MAINTENANCE = 'MAINTENANCE',
  DEPRECATED = 'DEPRECATED',
  ARCHIVED = 'ARCHIVED'
}

// 服务类别枚举
export enum ServiceCategory {
  MAINTENANCE = 'MAINTENANCE',
  SUPPORT = 'SUPPORT',
  UPGRADE = 'UPGRADE',
  BUGFIX = 'BUGFIX',
  CONSULTING = 'CONSULTING',
  MONITORING = 'MONITORING'
}

// 工作类别枚举
export enum WorkCategory {
  ANALYSIS = 'ANALYSIS',
  IMPLEMENTATION = 'IMPLEMENTATION',
  TESTING = 'TESTING',
  DOCUMENTATION = 'DOCUMENTATION',
  COMMUNICATION = 'COMMUNICATION',
  MAINTENANCE = 'MAINTENANCE',
  SUPPORT = 'SUPPORT',
  OTHER = 'OTHER'
}

// 优先级枚举
export enum Priority {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  URGENT = 'URGENT'
}

// 配置类型枚举
export enum ConfigType {
  SERVER = 'SERVER',
  DATABASE = 'DATABASE',
  VPN = 'VPN',
  ACCOUNT = 'ACCOUNT',
  ENVIRONMENT = 'ENVIRONMENT',
  OTHER = 'OTHER'
}

// 服务状态枚举
export enum ServiceStatus {
  PENDING = 'PENDING',
  IN_PROGRESS = 'IN_PROGRESS',
  WAITING_CUSTOMER = 'WAITING_CUSTOMER',
  RESOLVED = 'RESOLVED',
  CLOSED = 'CLOSED'
}

// 通知类型枚举
export enum NotificationType {
  EMAIL = 'EMAIL',
  SMS = 'SMS',
  SYSTEM = 'SYSTEM'
}

// 通知状态枚举
export enum NotificationStatus {
  PENDING = 'PENDING',
  SENT = 'SENT',
  FAILED = 'FAILED',
  CANCELLED = 'CANCELLED'
}

// 权限相关
export interface Permission {
  id: string
  name: string
  description?: string
  resource: string
  action: string
}

export interface Role {
  id: string
  name: string
  description?: string
  permissions: string[]
}

// 错误类型
export interface ApiError {
  success: false
  message: string
  code: string
  details?: any
}

// 文件上传相关
export interface FileUpload {
  filename: string
  originalName: string
  mimeType: string
  size: number
  url: string
}

// 统计数据类型
export interface StatisticsData {
  label: string
  value: number
  change?: number
  changeType?: 'increase' | 'decrease' | 'neutral'
}

// 图表数据类型
export interface ChartData {
  labels: string[]
  datasets: {
    label: string
    data: number[]
    backgroundColor?: string | string[]
    borderColor?: string | string[]
    [key: string]: any
  }[]
}

// 操作日志类型
export interface OperationLog {
  id: string
  userId: string
  action: string
  resource: string
  resourceId?: string
  details?: any
  ipAddress?: string
  userAgent?: string
  createdAt: string
}

// 基础系统配置类型
export interface BasicSystemConfig {
  key: string
  value: any
  description?: string
  category: string
  isPublic: boolean
}

// 导出类型
export interface ExportOptions {
  format: 'xlsx' | 'csv' | 'pdf'
  fields?: string[]
  filters?: FilterParams
  filename?: string
}