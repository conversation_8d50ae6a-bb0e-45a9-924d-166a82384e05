// 系统配置类型定义

export interface SystemConfig {
  id: string
  category: SystemConfigCategory
  key: string
  value: any
  description?: string
  dataType: SystemConfigDataType
  isEncrypted: boolean
  isSystem: boolean
  isPublic: boolean
  validationRule?: any
  defaultValue?: any
  displayOrder?: number
  updatedBy?: string
  createdAt: string
  updatedAt: string
  updatedByUser?: {
    username: string
    fullName?: string
  }
}

export interface SystemConfigHistory {
  id: string
  configId: string
  oldValue?: any
  newValue: any
  changeReason?: string
  changedBy: string
  createdAt: string
  changedByUser: {
    username: string
    fullName?: string
  }
}

export enum SystemConfigCategory {
  GENERAL = 'GENERAL',          // 基本配置
  SECURITY = 'SECURITY',        // 安全配置
  EMAIL = 'EMAIL',              // 邮件配置
  SMS = 'SMS',                  // 短信配置
  NOTIFICATION = 'NOTIFICATION',// 通知配置
  STORAGE = 'STORAGE',          // 存储配置
  BACKUP = 'BACKUP',            // 备份配置
  SYSTEM = 'SYSTEM',            // 系统配置
  INTEGRATION = 'INTEGRATION',  // 集成配置
  CUSTOM = 'CUSTOM'             // 自定义配置
}

export enum SystemConfigDataType {
  STRING = 'STRING',
  NUMBER = 'NUMBER',
  BOOLEAN = 'BOOLEAN',
  JSON = 'JSON',
  EMAIL = 'EMAIL',
  URL = 'URL',
  PASSWORD = 'PASSWORD',
  TEXTAREA = 'TEXTAREA',
  SELECT = 'SELECT',
  MULTI_SELECT = 'MULTI_SELECT'
}

// 系统配置输入类型
export interface CreateSystemConfigInput {
  category: SystemConfigCategory
  key: string
  value: any
  description?: string
  dataType?: SystemConfigDataType
  isEncrypted?: boolean
  isSystem?: boolean
  isPublic?: boolean
  validationRule?: any
  defaultValue?: any
  displayOrder?: number
}

export interface UpdateSystemConfigInput {
  value?: any
  description?: string
  isPublic?: boolean
  validationRule?: any
  displayOrder?: number
  changeReason?: string
}

export interface SystemConfigFilter {
  category?: SystemConfigCategory
  isPublic?: boolean
  isSystem?: boolean
  search?: string
}

// 系统配置验证测试结果
export interface ConfigTestResult {
  success: boolean
  message: string
  details?: any
  timestamp: string
}

// 邮件配置测试
export interface EmailTestConfig {
  recipient: string
  subject?: string
  content?: string
}

// 短信配置测试
export interface SmsTestConfig {
  phone: string
  message?: string
}

// 配置导出/导入
export interface ConfigExport {
  version: string
  timestamp: string
  configs: Omit<SystemConfig, 'id' | 'createdAt' | 'updatedAt'>[]
}

export interface ConfigImportOptions {
  overwriteExisting?: boolean
  skipEncrypted?: boolean
  categories?: SystemConfigCategory[]
}

export interface ConfigImportResult {
  success: boolean
  message: string
  imported: number
  skipped: number
  errors: string[]
}

// 预定义的系统配置项
export interface SystemConfigDefaults {
  [SystemConfigCategory.GENERAL]: {
    SITE_NAME: string
    SITE_DESCRIPTION: string
    SITE_LOGO: string
    SITE_FAVICON: string
    DEFAULT_TIMEZONE: string
    DEFAULT_LANGUAGE: string
    COMPANY_NAME: string
    COMPANY_ADDRESS: string
    COMPANY_PHONE: string
    COMPANY_EMAIL: string
  }
  
  [SystemConfigCategory.SECURITY]: {
    PASSWORD_MIN_LENGTH: number
    PASSWORD_REQUIRE_UPPERCASE: boolean
    PASSWORD_REQUIRE_LOWERCASE: boolean
    PASSWORD_REQUIRE_NUMBER: boolean
    PASSWORD_REQUIRE_SPECIAL: boolean
    PASSWORD_EXPIRY_DAYS: number
    MAX_LOGIN_ATTEMPTS: number
    LOCKOUT_DURATION: number
    SESSION_TIMEOUT: number
    ENABLE_2FA: boolean
    JWT_EXPIRY: number
    REFRESH_TOKEN_EXPIRY: number
  }
  
  [SystemConfigCategory.EMAIL]: {
    SMTP_HOST: string
    SMTP_PORT: number
    SMTP_SECURE: boolean
    SMTP_USER: string
    SMTP_PASS: string
    SENDER_NAME: string
    SENDER_EMAIL: string
    EMAIL_ENABLED: boolean
  }
  
  [SystemConfigCategory.SMS]: {
    ALI_SMS_ACCESS_KEY_ID: string
    ALI_SMS_ACCESS_KEY_SECRET: string
    ALI_SMS_REGION: string
    ALI_SMS_SIGN_NAME: string
    SMS_ENABLED: boolean
  }
  
  [SystemConfigCategory.NOTIFICATION]: {
    DEFAULT_EMAIL_TEMPLATE: string
    DEFAULT_SMS_TEMPLATE: string
    NOTIFICATION_RETRY_TIMES: number
    NOTIFICATION_RETRY_DELAY: number
    AUTO_NOTIFICATION_ENABLED: boolean
    NOTIFICATION_QUEUE_SIZE: number
  }
  
  [SystemConfigCategory.STORAGE]: {
    UPLOAD_MAX_SIZE: number
    UPLOAD_ALLOWED_TYPES: string[]
    STORAGE_PATH: string
    ENABLE_CDN: boolean
    CDN_DOMAIN: string
    FILE_RETENTION_DAYS: number
  }
  
  [SystemConfigCategory.BACKUP]: {
    AUTO_BACKUP_ENABLED: boolean
    BACKUP_SCHEDULE: string
    BACKUP_RETENTION_DAYS: number
    BACKUP_STORAGE_PATH: string
    BACKUP_COMPRESSION: boolean
    BACKUP_ENCRYPTION: boolean
  }
  
  [SystemConfigCategory.SYSTEM]: {
    SYSTEM_MAINTENANCE_MODE: boolean
    MAINTENANCE_MESSAGE: string
    DEBUG_MODE: boolean
    LOG_LEVEL: string
    LOG_RETENTION_DAYS: number
    CACHE_TTL: number
    API_RATE_LIMIT: number
    HEALTH_CHECK_INTERVAL: number
  }
}

// 配置项元数据（用于前端渲染）
export interface ConfigFieldMeta {
  label: string
  description?: string
  placeholder?: string
  required?: boolean
  validation?: {
    min?: number
    max?: number
    pattern?: string
    options?: Array<{ label: string; value: any }>
  }
  displayProps?: {
    rows?: number
    cols?: number
    multiple?: boolean
  }
}

export interface ConfigCategoryMeta {
  label: string
  description?: string
  icon?: string
  order?: number
  fields: Record<string, ConfigFieldMeta>
}