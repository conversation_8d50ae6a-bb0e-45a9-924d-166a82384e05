// 在浏览器控制台中运行此代码来调试认证问题
(function() {
    console.log('🔍 开始检查认证状态...');
    
    // 1. 检查localStorage
    console.log('📦 检查localStorage中的认证数据:');
    const authStorage = localStorage.getItem('auth-storage');
    if (authStorage) {
        try {
            const parsed = JSON.parse(authStorage);
            console.log('✅ 找到auth-storage:', parsed);
            
            const token = parsed.state?.token;
            if (token) {
                console.log('🔑 找到token:', token.substring(0, 20) + '...');
                
                // 解析JWT payload
                try {
                    const payload = JSON.parse(atob(token.split('.')[1]));
                    console.log('📊 Token payload:', payload);
                    
                    // 检查过期时间
                    const now = Math.floor(Date.now() / 1000);
                    if (payload.exp < now) {
                        console.error('⚠️ Token已过期！过期时间:', new Date(payload.exp * 1000));
                    } else {
                        const remaining = payload.exp - now;
                        console.log('✅ Token有效，剩余时间:', Math.floor(remaining / 60), '分钟');
                    }
                } catch (e) {
                    console.error('❌ 无法解析Token payload:', e);
                }
            } else {
                console.error('❌ auth-storage中没有token');
            }
        } catch (e) {
            console.error('❌ 无法解析auth-storage:', e);
        }
    } else {
        console.error('❌ 没有找到auth-storage');
    }
    
    // 2. 检查内存AuthStore
    console.log('💾 检查内存中的AuthStore:');
    if (window.__AUTH_STORE__) {
        const state = window.__AUTH_STORE__.getState();
        console.log('✅ 内存AuthStore状态:', state);
    } else {
        console.warn('⚠️ 没有找到内存AuthStore');
    }
    
    // 3. 测试API请求
    console.log('🌐 测试API请求...');
    
    async function testAuth() {
        try {
            const authStorage = localStorage.getItem('auth-storage');
            let token = null;
            
            if (authStorage) {
                const parsed = JSON.parse(authStorage);
                token = parsed.state?.token;
            }
            
            if (!token) {
                console.error('❌ 没有token，无法测试API请求');
                console.log('💡 建议：请先登录或重新登录');
                return;
            }
            
            const response = await fetch('/api/v1/auth/me', {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${token}`
                }
            });
            
            const data = await response.json();
            
            if (response.ok) {
                console.log('✅ API请求成功，当前用户:', data);
            } else {
                console.error('❌ API请求失败:', response.status, data);
                
                if (response.status === 401) {
                    console.log('💡 建议：Token无效或过期，请重新登录');
                }
            }
            
        } catch (error) {
            console.error('❌ 测试API请求时出错:', error);
        }
    }
    
    testAuth();
    
    // 4. 提供解决方案
    console.log('\n🔧 解决方案:');
    console.log('1. 如果没有token或token过期: 请重新登录');
    console.log('2. 清除存储并重新登录: localStorage.clear(); location.reload();');
    console.log('3. 手动跳转登录页: location.href = "/login";');
    
})();