# 运维服务管理系统

一个专业的软件项目运维服务管理系统，基于 Nuxt.js 3 和 Node.js 构建，专注于项目开发完成后的运维服务管理，提供完整的工单跟踪、SLA 管理和客户服务功能。

## 🚀 功能特性

### 核心功能
- **客户管理** - 客户信息管理、联系人管理、客户等级分类
- **项目档案管理** - 项目基础信息录入、技术栈记录、版本管理
- **运维配置管理** - 服务器、数据库、VPN、账号密码等敏感信息加密存储
- **服务工单管理** - 工单创建、分配、跟踪，支持多种服务类别
- **SLA 服务管理** - SLA 模板、响应时间跟踪、服务质量监控
- **工作日志记录** - 详细的工作时间、工作内容记录和统计
- **通知服务** - 短信、邮件自动通知，支持模板化消息
- **用户权限管理** - 多用户、角色权限、操作日志
- **运维统计分析** - 服务响应时间、客户满意度、工作量统计

### 技术特性
- **SSR 渲染** - 基于 Nuxt.js 3 的服务端渲染，SEO 友好
- **类型安全** - 全栈 TypeScript 支持
- **数据安全** - 敏感数据加密存储，JWT 认证
- **高性能缓存** - Redis 缓存策略优化
- **API 文档** - 自动生成 Swagger API 文档
- **容器化部署** - Docker 支持，便于部署和扩展

## 🛠️ 技术栈

### 核心框架
- **前端框架**: React 18 + TypeScript
- **构建工具**: Rsbuild (基于 Rspack)
- **路由**: React Router v6
- **包管理器**: npm

### UI 框架 - **重要约束**
- **UI 组件库**: @douyinfe/semi-ui (Semi Design)
- **图标库**: @douyinfe/semi-icons
- **样式方案**: Semi Design + Tailwind CSS
- **禁止使用**: Ant Design、Element UI、其他组件库

### 后端
- **框架**: Node.js + Express + TypeScript
- **数据库**: MySQL + Prisma ORM
- **缓存**: Redis
- **认证**: JWT + HttpOnly Cookies
- **文档**: Swagger/OpenAPI
- **加密**: bcrypt + crypto

### 基础设施
- **容器化**: Docker + Docker Compose
- **反向代理**: Nginx
- **进程管理**: PM2
- **监控**: 日志收集和性能监控

## 📁 项目结构

```
ops-management-system/
├── frontend/                     # Nuxt.js SSR 前端应用
│   ├── assets/                   # 静态资源
│   ├── components/               #  组件
│   │   ├── UI/                   # 通用UI组件
│   │   ├── forms/                # 表单组件
│   │   └── charts/               # 图表组件
│   ├── composables/              # 组合式函数
│   ├── layouts/                  # 布局组件
│   ├── pages/                    # 页面路由 (file-based routing)
│   │   ├── customers/            # 客户管理页面
│   │   ├── archives/             # 项目档案页面
│   │   ├── services/             # 服务工单页面
│   │   └── admin/                # 系统管理页面
│   ├── stores/                   # Pinia 状态管理
│   └── types/                    # 前端类型定义
├── backend/                      # Node.js API 后端
│   ├── src/
│   │   ├── controllers/          # API 控制器
│   │   ├── services/             # 业务逻辑服务
│   │   ├── middleware/           # 中间件
│   │   ├── utils/                # 工具函数
│   │   └── config/               # 配置文件
│   ├── prisma/                   # Prisma 数据库
│   │   ├── schema.prisma         # 数据库模型
│   │   └── migrations/           # 数据库迁移
│   └── tests/                    # 测试文件
├── shared/                       # 前后端共享代码
│   └── types/                    # 共享类型定义
├── deployment/                   # 部署配置
│   ├── docker/                   # Docker 配置
│   ├── nginx/                    # Nginx 配置
│   └── pm2/                      # PM2 配置
└── docs/                         # 项目文档
```

## 🚦 快速开始

### 环境要求
- Node.js >= 18.0.0
- MySQL >= 8.0
- Redis >= 6.0
- npm 或 yarn

### 安装依赖

```bash
# 安装前端依赖
cd frontend
npm install

# 安装后端依赖
cd ../backend
npm install
```

### 数据库配置

1. 创建 MySQL 数据库
```sql
CREATE DATABASE ops_management CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

2. 配置环境变量
```bash
# 复制配置文件
cp backend/.env.example backend/.env

# 编辑配置文件，设置数据库连接等信息
vim backend/.env
```

3. 初始化数据库
```bash
cd backend
npm run db:generate
npm run db:push
npm run db:seed
```

### 启动开发服务器

```bash
# 启动后端服务 (端口 3001)
cd backend
npm run dev

# 启动前端服务 (端口 3000)
cd frontend
npm run dev
```

访问 http://localhost:3000 查看应用。

### API 文档

启动后端服务后，访问 http://localhost:3001/api-docs 查看 Swagger API 文档。

## 🏗️ 部署指南

### Docker 部署 (推荐)

```bash
# 构建和启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

### 传统部署

```bash
# 构建前端
cd frontend
npm run build

# 构建后端
cd ../backend
npm run build

# 启动服务
npm start
```

详细部署文档请参考 [deployment.md](docs/deployment.md)。

## 📖 开发文档

- [开发指南](docs/development.md)
- [API 文档](docs/api.md)
- [架构设计](docs/architecture.md)
- [部署指南](docs/deployment.md)

## 🔐 默认账号

系统初始化后会创建默认管理员账号：
- 用户名: `admin`
- 密码: `admin123`

**⚠️ 生产环境请立即修改默认密码！**

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 技术支持

如有问题请提交 [Issue](../../issues) 或联系开发团队。

---

🌟 如果这个项目对您有帮助，请给它一个 Star！