# 运维管理系统权限体系设计文档

## 概述

本文档详细介绍了运维管理系统的权限体系设计，包括权限常量定义、权限验证中间件、权限装饰器使用方法等。

## 权限体系架构

### 1. 权限常量体系

#### 权限命名规则
```
模块名:操作类型[:子操作]
```

- **模块名**: 业务模块的简称 (如: user, customer, service)
- **操作类型**: read, write, delete, manage, export, import
- **子操作**: 特定的业务操作 (如: reset_password, view_sensitive)

#### 权限层级
1. **超级管理员权限**: `admin:all` - 拥有所有权限
2. **模块级权限**: 如 `user:*`, `customer:*`
3. **功能级权限**: 如 `user:read`, `user:write`
4. **操作级权限**: 如 `user:export`, `user:reset_password`

### 2. 权限分组

#### 系统管理权限
```typescript
USER_PERMISSIONS = {
  READ: 'user:read',              // 查看用户
  WRITE: 'user:write',            // 创建/编辑用户
  DELETE: 'user:delete',          // 删除用户
  MANAGE: 'user:manage',          // 用户管理（批量操作）
  EXPORT: 'user:export',          // 导出用户数据
  IMPORT: 'user:import',          // 导入用户数据
  RESET_PASSWORD: 'user:reset_password',     // 重置密码
  VIEW_SENSITIVE: 'user:view_sensitive',     // 查看敏感信息
}
```

#### 业务模块权限
```typescript
SERVICE_PERMISSIONS = {
  READ: 'service:read',           // 查看服务工单
  WRITE: 'service:write',         // 创建/编辑工单
  DELETE: 'service:delete',       // 删除工单
  ASSIGN: 'service:assign',       // 分配工单
  TRANSFER: 'service:transfer',   // 转交工单
  CLOSE: 'service:close',         // 关闭工单
  REOPEN: 'service:reopen',       // 重新打开工单
  COMMENT: 'service:comment',     // 添加评论
  COMMENT_INTERNAL: 'service:comment_internal', // 内部评论
  VIEW_ALL: 'service:view_all',   // 查看所有工单
}
```

### 3. 默认角色权限配置

#### 运维工程师
```typescript
engineer: [
  CUSTOMER_PERMISSIONS.READ,
  ARCHIVE_PERMISSIONS.READ,
  ARCHIVE_PERMISSIONS.WRITE,
  ARCHIVE_PERMISSIONS.CONFIG_READ,
  ARCHIVE_PERMISSIONS.CONFIG_WRITE,
  SERVICE_PERMISSIONS.READ,
  SERVICE_PERMISSIONS.WRITE,
  SERVICE_PERMISSIONS.COMMENT,
  SERVICE_PERMISSIONS.WORK_LOG,
  // ...
]
```

#### 客户服务
```typescript
customer_service: [
  CUSTOMER_PERMISSIONS.READ,
  CUSTOMER_PERMISSIONS.WRITE,
  SERVICE_PERMISSIONS.READ,
  SERVICE_PERMISSIONS.WRITE,
  SERVICE_PERMISSIONS.COMMENT,
  // 注意：不包含内部评论权限
  // ...
]
```

## 权限验证方式

### 1. 中间件方式

#### 基础权限中间件
```typescript
import { requirePermissions, requireAnyPermission } from '@/middleware/auth.middleware'

// 需要特定权限
router.get('/users', 
  authenticateToken, 
  requirePermissions('user:read'), 
  getUsers
)

// 需要任一权限
router.get('/dashboard', 
  authenticateToken, 
  requireAnyPermission('user:read', 'customer:read'), 
  getDashboard
)
```

#### 增强权限中间件
```typescript
import { PermissionPolicies } from '@/middleware/enhanced-auth.middleware'

// 严格权限策略 - 必须拥有所有权限
router.post('/admin/users',
  authenticateToken,
  PermissionPolicies.strict(['user:write', 'user:manage']),
  createUser
)

// 灵活权限策略 - 拥有任一权限即可
router.get('/reports',
  authenticateToken,
  PermissionPolicies.flexible(['service:stats', 'customer:export']),
  getReports
)

// 所有权策略 - 资源所有者或管理员
router.get('/profile/:userId',
  authenticateToken,
  PermissionPolicies.ownership(['user:read'], ['admin:all', 'user:manage']),
  getUserProfile
)
```

### 2. 装饰器方式

#### 基础装饰器
```typescript
import { RequirePermissions, RequireAnyPermission } from '@/utils/decorators.util'

export class UserController {
  @RequirePermissions('user:read')
  @LogApiAccess('查看用户列表')
  async getUsers(req: Request, res: Response) {
    // 控制器逻辑
  }

  @RequireAnyPermission('user:write', 'user:manage')
  @RateLimit(60000, 10) // 每分钟最多10次
  async createUser(req: Request, res: Response) {
    // 控制器逻辑
  }
}
```

#### 高级装饰器
```typescript
import { 
  RequireSuperAdmin, 
  RequireOwnership,
  RequireConditionalPermission 
} from '@/utils/decorators.util'

export class SystemConfigController {
  @RequireSuperAdmin()
  @LogApiAccess('删除系统配置')
  async deleteConfig(req: Request, res: Response) {
    // 只有超级管理员可以删除配置
  }

  @RequireOwnership(
    (req) => req.params.userId, // 获取资源所有者ID
    ['admin:all', 'user:manage'] // 管理员权限
  )
  async getUserSettings(req: Request, res: Response) {
    // 只能查看自己的设置或管理员可以查看所有
  }

  @RequireConditionalPermission(
    async (req) => {
      const config = await getConfig(req.params.key)
      return !config.isPublic // 非公开配置需要权限检查
    },
    ['system:security']
  )
  async getSystemConfig(req: Request, res: Response) {
    // 根据配置的公开性动态检查权限
  }
}
```

### 3. 程序化权限检查

#### 在控制器中手动检查
```typescript
import { hasPermission, checkOwnershipOrAdmin } from '@/utils/permission.util'

export const getServiceDetail = async (req: AuthenticatedRequest, res: Response) => {
  const userPermissions = req.user.permissions
  const serviceId = req.params.id

  // 基础权限检查
  if (!hasPermission(userPermissions, 'service:read')) {
    return res.status(403).json({
      success: false,
      message: '无权限查看服务工单'
    })
  }

  const service = await getService(serviceId)
  
  // 资源级权限检查
  if (!checkOwnershipOrAdmin(req, service.assignedTo, ['service:view_all'])) {
    return res.status(403).json({
      success: false,
      message: '只能查看自己分配的工单'
    })
  }

  // 返回数据
  res.json({ success: true, data: service })
}
```

## 权限管理接口

### 1. 权限查询接口

```typescript
// 获取所有权限列表
GET /api/v1/permissions

// 获取权限分组
GET /api/v1/permissions/groups

// 获取角色权限映射
GET /api/v1/permissions/role-mapping

// 权限统计
GET /api/v1/permissions/stats
```

### 2. 权限验证接口

```typescript
// 验证权限格式
POST /api/v1/permissions/validate
{
  "permissions": ["user:read", "invalid_permission"]
}

// 检查用户权限
POST /api/v1/permissions/check
{
  "userId": "user123",
  "permissions": ["user:read", "user:write"]
}
```

### 3. 角色权限管理

```typescript
// 更新角色权限
PUT /api/v1/permissions/roles/:roleId
{
  "permissions": ["user:read", "user:write"]
}

// 批量更新角色权限
POST /api/v1/permissions/roles/batch-update
{
  "roleUpdates": [
    {
      "roleId": "role1",
      "permissions": ["user:read"]
    }
  ]
}

// 重置角色权限为默认值
POST /api/v1/permissions/roles/:roleId/reset
```

## 权限继承与扩展

### 1. 权限隐含关系

```typescript
// 写权限隐含读权限
'user:write' → 'user:read'

// 管理权限隐含所有操作权限
'user:manage' → ['user:read', 'user:write', 'user:delete', ...]

// 超级管理员权限隐含所有权限
'admin:all' → [所有权限]
```

### 2. 权限展开示例

```typescript
import { expandPermissions } from '@/utils/permission.util'

const userPermissions = ['user:write', 'service:manage']
const expandedPermissions = expandPermissions(userPermissions)
// 结果: ['user:write', 'user:read', 'service:manage', 'service:read', 'service:write', ...]
```

## 安全注意事项

### 1. 敏感权限保护

- `admin:all`: 超级管理员权限，需要严格控制
- `user:view_sensitive`: 查看敏感用户信息
- `system:security`: 系统安全配置管理
- `config:sensitive`: 敏感配置管理

### 2. 权限检查最佳实践

1. **最小权限原则**: 只授予必要的权限
2. **权限分离**: 读写权限分离，管理权限分离
3. **资源隔离**: 用户只能访问自己的资源
4. **审计日志**: 记录所有权限相关操作
5. **定期检查**: 定期检查权限分配的合理性

### 3. 常见安全问题

```typescript
// ❌ 错误：没有权限检查
export const deleteUser = async (req: Request, res: Response) => {
  await userService.delete(req.params.id)
  res.json({ success: true })
}

// ✅ 正确：有权限检查
@RequirePermissions('user:delete')
@LogApiAccess('删除用户')
export const deleteUser = async (req: Request, res: Response) => {
  await userService.delete(req.params.id)
  res.json({ success: true })
}
```

## 权限测试

### 1. 单元测试示例

```typescript
import { hasPermission } from '@/utils/permission.util'

describe('权限检查测试', () => {
  test('超级管理员应该拥有所有权限', () => {
    const adminPermissions = ['admin:all']
    expect(hasPermission(adminPermissions, 'user:read')).toBe(true)
    expect(hasPermission(adminPermissions, 'service:write')).toBe(true)
  })

  test('普通用户只能访问授权的权限', () => {
    const userPermissions = ['user:read', 'customer:read']
    expect(hasPermission(userPermissions, 'user:read')).toBe(true)
    expect(hasPermission(userPermissions, 'user:write')).toBe(false)
  })
})
```

### 2. 集成测试示例

```typescript
describe('权限中间件测试', () => {
  test('有权限的用户应该可以访问', async () => {
    const token = await createUserWithPermissions(['user:read'])
    
    const response = await request(app)
      .get('/api/v1/users')
      .set('Authorization', `Bearer ${token}`)
      .expect(200)
    
    expect(response.body.success).toBe(true)
  })

  test('无权限的用户应该被拒绝', async () => {
    const token = await createUserWithPermissions(['customer:read'])
    
    await request(app)
      .get('/api/v1/users')
      .set('Authorization', `Bearer ${token}`)
      .expect(403)
  })
})
```

## 权限系统扩展

### 1. 自定义权限策略

```typescript
import { createPermissionPolicy } from '@/middleware/enhanced-auth.middleware'

// 自定义时间段权限检查
const workingHoursPolicy = createPermissionPolicy({
  strategy: PermissionStrategy.CONDITIONAL,
  permissions: ['service:write'],
  options: {
    condition: (req) => {
      const hour = new Date().getHours()
      return hour >= 9 && hour <= 18 // 只在工作时间允许
    }
  }
})
```

### 2. 动态权限规则

```typescript
import { createDynamicPermissionCheck } from '@/middleware/enhanced-auth.middleware'

// 根据工单状态动态检查权限
const dynamicServicePermission = createDynamicPermissionCheck(async (req) => {
  const service = await getService(req.params.id)
  
  if (service.status === 'CLOSED') {
    return ['service:reopen'] // 已关闭的工单需要重开权限
  } else {
    return ['service:write'] // 正常工单需要写权限
  }
})
```

## 总结

本权限系统提供了完整的权限管理解决方案，包括：

1. **完整的权限常量体系** - 覆盖所有业务模块
2. **灵活的权限验证方式** - 中间件、装饰器、程序化检查
3. **强大的权限管理接口** - 权限查询、验证、管理
4. **安全的权限继承机制** - 权限隐含和扩展
5. **丰富的权限策略支持** - 严格、灵活、所有权、条件、层级

通过合理使用这些功能，可以构建安全、灵活、易维护的权限控制系统。