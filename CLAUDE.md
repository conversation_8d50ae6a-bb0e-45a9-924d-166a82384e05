# 运维服务管理系统 - 代码库文档

## 系统概述

这是一个专业的运维服务管理系统，基于  React + TypeScript 和 Node.js 构建，专注于软件项目开发完成后的运维服务管理。系统提供完整的工单跟踪、SLA 管理和客户服务功能。

## 技术架构

### 核心框架

- **前端框架**: React 18 + TypeScript
- **构建工具**: Rsbuild (基于 Rspack)
- **路由**: React Router v6
- **包管理器**: npm
- **格式化**: Prettier
- **代码检查**: ESLint
- **类型检查**: TypeScript

### UI 框架 - **重要约束**

- **UI 组件库**: @douyinfe/semi-ui (Semi Design)
- **图标库**: @douyinfe/semi-icons
- **样式方案**: Semi Design + Tailwind CSS
- **禁止使用**: Ant Design、Element UI、其他组件库

### 后端技术栈

- **框架**: Node.js + Express + TypeScript
- **数据库**: MySQL + Prisma ORM
- **缓存**: Redis
- **认证**: JWT + HttpOnly Cookies
- **加密**: bcrypt + crypto (敏感配置加密)
- **文档**: Swagger/OpenAPI
- **文件上传**: multer

## 项目结构

```
ops-management-system/
├── frontend/                     # Nuxt.js SSR 前端
│   ├── assets/css/              # 全局样式
│   ├── components/              #  组件
│   │   ├── UI/                  # 通用UI组件
│   │   ├── forms/               # 表单组件
│   │   └── charts/              # 图表组件
│   ├── composables/             # 组合式函数
│   ├── layouts/                 # 布局组件
│   ├── middleware/              # 路由中间件
│   ├── pages/                   # 页面路由
│   │   ├── customers/           # 客户管理
│   │   ├── archives/            # 项目档案
│   │   ├── services/            # 服务工单
│   │   └── admin/               # 系统管理
│   ├── plugins/                 # Nuxt插件
│   ├── stores/                  # Pinia状态管理
│   ├── types/                   # 前端类型定义
│   ├── utils/                   # 工具函数
│   ├── app.tsx                  # 根组件
│   └── package.json
├── backend/                     # Node.js API后端
│   ├── src/
│   │   ├── controllers/         # API控制器
│   │   ├── services/            # 业务逻辑服务
│   │   ├── middleware/          # Express中间件
│   │   ├── routes/              # API路由
│   │   ├── utils/               # 工具函数
│   │   ├── config/              # 配置文件
│   │   ├── types/               # 后端类型定义
│   │   └── index.ts             # 应用入口
│   ├── prisma/                  # Prisma数据库
│   │   ├── schema.prisma        # 数据库模型
│   │   ├── migrations/          # 数据库迁移
│   │   └── seeds/               # 数据库种子
│   ├── tests/                   # 测试文件
│   ├── .env.example             # 环境变量示例
│   ├── package.json
│   └── tsconfig.json
├── shared/                      # 前后端共享
│   ├── types/                   # 共享类型定义
│   │   ├── common.ts            # 通用类型
│   │   ├── user.ts              # 用户相关
│   │   ├── customer.ts          # 客户相关
│   │   ├── project.ts           # 项目档案相关
│   │   ├── service.ts           # 服务相关
│   │   ├── notification.ts      # 通知相关
│   │   └── index.ts             # 类型导出
│   └── constants/               # 常量定义
├── deployment/                  # 部署配置
│   ├── docker/                  # Docker配置
│   ├── nginx/                   # Nginx配置
│   └── pm2/                     # PM2配置
├── docs/                        # 项目文档
├── scripts/                     # 构建脚本
├── .env.example                 # 环境变量示例
├── .gitignore
├── README.md
└── CLAUDE.md                    # 本文档
```

## 核心功能模块

### 1. 客户管理 (Customer Management)

- **位置**: `frontend/pages/customers/`, `backend/src/controllers/customer.controller.ts`
- **功能**: 客户信息管理、联系人管理、客户等级分类
- **数据表**: `customers`, `customer_contacts`

### 2. 项目档案管理 (Project Archive)
- **位置**: `frontend/pages/archives/`, `backend/src/controllers/archive.controller.ts`
- **功能**: 项目基础信息录入、技术栈记录、版本管理
- **数据表**: `project_archives`
- **特点**: 简化的项目信息，主要用于运维服务对象

### 3. 运维配置管理 (Configuration Management)
- **位置**: `backend/src/controllers/configuration.controller.ts`
- **功能**: 服务器、数据库、VPN、账号密码等配置信息管理
- **数据表**: `project_configurations`
- **安全**: 敏感字段加密存储 (`backend/src/utils/crypto.util.ts`)

### 4. 服务工单管理 (Service Ticket Management) - 核心功能
- **位置**: `frontend/pages/services/`, `backend/src/controllers/service.controller.ts`
- **功能**: 
  - 工单创建、分配、跟踪
  - 服务类别管理 (维护、支持、升级、Bug修复等)
  - 优先级和状态管理
  - 工单号自动生成
  - 客户联系人关联
- **数据表**: `services`, `service_work_logs`, `service_attachments`, `service_comments`

### 5. SLA管理 (SLA Management)
- **位置**: `backend/src/controllers/sla.controller.ts`
- **功能**: SLA模板管理、响应时间跟踪、服务质量监控
- **数据表**: `sla_templates`

### 6. 工作跟踪 (Work Tracking)
- **功能**:
  - 工作日志记录 (`service_work_logs`)
  - 文件附件管理 (`service_attachments`)
  - 内部讨论和客户沟通 (`service_comments`)
  - 工时统计和分析

### 7. 通知服务 (Notification Service)
- **位置**: `backend/src/utils/notification.util.ts`
- **功能**: 短信、邮件自动通知，支持模板化消息
- **数据表**: `notification_templates`, `notifications`

### 8. 用户权限管理 (User & Permission Management)
- **位置**: `backend/src/middleware/auth.middleware.ts`
- **功能**: 多用户、角色权限、操作日志
- **数据表**: `users`, `roles`, `user_roles`, `operation_logs`

## 数据库设计

### 核心数据表关系
```
customers (客户)
    ↓ 1:N
project_archives (项目档案)
    ↓ 1:N
services (服务工单) ← 核心表
    ↓ 1:N
service_work_logs (工作日志)
service_attachments (附件)
service_comments (评论)
```

### 重要枚举类型
- `ArchiveStatus`: ACTIVE, MAINTENANCE, DEPRECATED, ARCHIVED
- `ServiceCategory`: MAINTENANCE, SUPPORT, UPGRADE, BUGFIX, CONSULTING, MONITORING  
- `ServiceStatus`: PENDING, IN_PROGRESS, WAITING_CUSTOMER, RESOLVED, CLOSED
- `Priority`: LOW, MEDIUM, HIGH, URGENT
- `WorkCategory`: ANALYSIS, IMPLEMENTATION, TESTING, DOCUMENTATION, etc.

## 关键配置文件

### 前端配置
- `frontend/nuxt.config.ts` - Nuxt.js 主配置
- `frontend/assets/css/main.css` - 全局样式和Tailwind配置

### 后端配置
- `backend/src/config/database.config.ts` - Prisma数据库配置
- `backend/src/config/redis.config.ts` - Redis缓存配置
- `backend/src/config/swagger.config.ts` - API文档配置
- `backend/prisma/schema.prisma` - 数据库Schema定义

### 安全配置
- JWT认证: `backend/src/utils/jwt.util.ts`
- 数据加密: `backend/src/utils/crypto.util.ts`
- 请求验证: `backend/src/middleware/auth.middleware.ts`

## 开发命令

### 前端开发
```bash
cd frontend
npm install
npm run dev          # 启动开发服务器 (http://localhost:3000)
npm run build        # 构建生产版本
npm run generate     # 生成静态文件
npm run lint         # 代码检查
npm run type-check   # 类型检查
```

### 后端开发
```bash
cd backend
npm install
npm run dev          # 启动开发服务器 (http://localhost:3001)
npm run build        # 构建TypeScript
npm start           # 启动生产服务器
npm run test        # 运行测试
npm run lint        # 代码检查

# 数据库相关
npm run db:generate  # 生成Prisma客户端
npm run db:push     # 推送Schema到数据库
npm run db:migrate  # 运行数据库迁移
npm run db:seed     # 运行数据库种子
npm run db:studio   # 打开Prisma Studio
```

## 环境变量配置

关键环境变量 (参考 `.env.example`):

```bash
# 数据库
DATABASE_URL="mysql://username:password@localhost:3306/ops_management"

# Redis
REDIS_URL="redis://localhost:6379"

# JWT认证
JWT_SECRET="your-jwt-secret-key"
REFRESH_TOKEN_SECRET="your-refresh-token-secret"

# 数据加密
CRYPTO_SECRET="your-crypto-secret-key-32-chars-long"

# 邮件服务
SMTP_HOST="smtp.gmail.com"
SMTP_USER="<EMAIL>"
SMTP_PASS="your-app-password"

# 短信服务 (阿里云)
ALI_SMS_ACCESS_KEY_ID="your-access-key"
ALI_SMS_ACCESS_KEY_SECRET="your-secret-key"
```

## API文档

启动后端服务后访问: http://localhost:3001/api-docs

主要API端点:
- `/api/v1/auth/*` - 认证相关
- `/api/v1/customers/*` - 客户管理
- `/api/v1/archives/*` - 项目档案
- `/api/v1/services/*` - 服务工单
- `/api/v1/configurations/*` - 配置管理
- `/api/v1/users/*` - 用户管理

## 业务流程

### 典型运维服务流程
1. **项目交付** → 录入项目档案 (`project_archives`)
2. **配置录入** → 添加运维配置信息 (`project_configurations`)
3. **服务请求** → 客户提交服务需求，创建工单 (`services`)
4. **工单分配** → 分配给相应工程师
5. **服务执行** → 记录工作日志 (`service_work_logs`)
6. **客户沟通** → 添加评论和附件 (`service_comments`, `service_attachments`)
7. **服务完成** → 更新工单状态，收集客户反馈
8. **工单关闭** → 完成服务并归档

## 部署说明

### Docker部署 (推荐)
```bash
docker-compose up -d
```

### 传统部署
1. 安装依赖: Node.js 18+, MySQL 8+, Redis 6+
2. 配置环境变量
3. 初始化数据库
4. 构建和启动服务

详见 `README.md` 和 `docs/deployment.md`

## 注意事项

### 安全注意事项
- 所有敏感配置信息在数据库中加密存储
- JWT token使用HttpOnly Cookie存储
- API接口有完整的权限验证
- 生产环境必须修改默认密码和密钥

### 性能优化
- Redis缓存用户信息和API响应
- 数据库查询优化和索引
- 前端SSR提升首屏加载速度
- 图片和静态资源CDN加速

### 开发规范
- 全栈TypeScript，类型安全
- ESLint代码检查
- Prettier代码格式化
- Git提交规范
- API文档自动生成

## 默认账号

系统初始化后的默认管理员账号:
- 用户名: `admin`
- 密码: `admin123`

**⚠️ 生产环境请立即修改默认密码！**